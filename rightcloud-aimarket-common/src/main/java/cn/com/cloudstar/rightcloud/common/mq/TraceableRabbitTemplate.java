/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.mq;

import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

/**
 * <AUTHOR>
 */
public class TraceableRabbitTemplate extends RabbitTemplate {

    public TraceableRabbitTemplate(ConnectionFactory connectionFactory) {
        this();
        this.setConnectionFactory(connectionFactory);
    }

    public TraceableRabbitTemplate() {
        super();
        // this.setAfterReceivePostProcessors(new TraceableReceivedMsgProcessor());
        // this.setBeforePublishPostProcessors(new TraceablePublishMsgProcessor());
    }

}
