package cn.com.cloudstar.rightcloud.remote.api.resource.service.server;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResBms;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResBmsNodeInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11
 */
public interface ResBmsRemoteService {

    ResBms selectByPrimaryKey(String id);

    List<ResBmsNodeInfo> getNodeInfoListByClusterId(Long clusterId);

    ResBmsNodeInfo getNodeInfoByInstanceId(String instanceId);

    Integer updateByPrimaryKeySelective(ResBms resBms);

}
