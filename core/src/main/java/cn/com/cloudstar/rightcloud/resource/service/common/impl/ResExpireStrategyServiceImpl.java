/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.common.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.PostConstruct;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.CacheConstants;
import cn.com.cloudstar.rightcloud.common.expire.ResExpireStrategy;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.resource.dao.common.ResExpireStrategyMapper;
import cn.com.cloudstar.rightcloud.resource.service.common.ResExpireStrategyService;

@Service
@Slf4j
public class ResExpireStrategyServiceImpl implements ResExpireStrategyService {

    private static final String EXPIRE_TASK_CRON = "0 0 h 1/d * ?";

    @Autowired
    private ResExpireStrategyMapper resExpireStrategyMapper;

    @PostConstruct
    public void init() {
        //初始化策略缓存
        List<ResExpireStrategy> strategies = selectByParams(new Criteria("status", 1));
        strategies.forEach(strategy -> JedisUtil.instance()
                                                .set(CacheConstants.RESOURCE_NOTICE_KEY + strategy.getResourceType(),
                                                     Convert.toStr(strategy.getNoticeTime())));
    }

    @Override
    public int countByParams(Criteria example) {
        return resExpireStrategyMapper.countByParams(example);
    }

    @Override
    public ResExpireStrategy selectById(Long id) {
        return resExpireStrategyMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ResExpireStrategy> selectByParams(Criteria example) {
        return resExpireStrategyMapper.selectByParams(example);
    }

    @Override
    public int deleteById(Long id) {
        return resExpireStrategyMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateById(ResExpireStrategy record) {
        int n = resExpireStrategyMapper.updateByPrimaryKeySelective(record);
        if (n > 0 && record.getStatus()) {
            // 更新缓存，用于资源计算到期信息
            JedisUtil.instance()
                     .set(CacheConstants.RESOURCE_NOTICE_KEY + record.getResourceType(),
                          Convert.toStr(record.getNoticeTime()));
        }
        return n;
    }

    @Override
    public int deleteByParams(Criteria example) {
        return resExpireStrategyMapper.deleteByParams(example);
    }

    @Override
    public boolean createExpireStrategy(ResExpireStrategy record) {
        return resExpireStrategyMapper.insertSelective(record) > 0;
    }

    @Override
    public boolean configNoticeCron(Integer day, Integer hour) {
        // 更新策略
        ResExpireStrategy strategy = new ResExpireStrategy();
        //每几天几点
        strategy.setNoticeFrequency(hour);
        strategy.setNoticeCount(day);
        // 更新所有资源对应策略
        int n = resExpireStrategyMapper.configCron(strategy);
        // 更新任务
        updateNoticeTask(computeCron(day, hour));
        return n > 0;
    }

    @Override
    public void updateNoticeTask(String cron) {
        ScheduleHelper.updateResourceExpireNoticeTask(cron);
    }

    private String computeCron(Integer day, Integer hour) {
        return EXPIRE_TASK_CRON.replace("h", hour.toString()).replace("d", day.toString());
    }
}
