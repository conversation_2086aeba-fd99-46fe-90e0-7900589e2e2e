/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.event;

import cn.com.cloudstar.rightcloud.common.event.BaseEvent;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;

public class SfsDepoyEvent extends BaseEvent<ResVm> {

    private String shareId;
    private String sharePath;
    private String localPath;
    /**
     * NFS客户端重传请求前的等待时间(单位为0.1秒)。默认：600
     */
    private Long timeo;

    /**
     * lock/nolock
     */
    private String nlmLock;
    private String taskId;
    private String hostId;
    /**
     * 挂载卸载
     */
    private String type;

    private String shareProto;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getSharePath() {
        return sharePath;
    }

    public void setSharePath(String sharePath) {
        this.sharePath = sharePath;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public Long getTimeo() {
        return timeo;
    }

    public void setTimeo(Long timeo) {
        this.timeo = timeo;
    }

    public String getNlmLock() {
        return nlmLock;
    }

    public void setNlmLock(String nlmLock) {
        this.nlmLock = nlmLock;
    }

    public SfsDepoyEvent(ResVm source) {
        super(source);
    }

    public String getShareProto() {
        return shareProto;
    }

    public void setShareProto(String shareProto) {
        this.shareProto = shareProto;
    }
}
