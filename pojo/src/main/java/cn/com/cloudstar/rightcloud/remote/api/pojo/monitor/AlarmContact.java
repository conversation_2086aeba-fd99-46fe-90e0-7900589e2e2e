/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.monitor;

import java.io.Serializable;
import java.util.Date;

public class AlarmContact implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private String mobile;

    /**
     * active, inactive
     */
    private String mobileStatus;

    private String mobileActiveCode;

    private String email;

    /**
     * active, inactive
     */
    private String emailStatus;

    private String emailActiveCode;

    /**
     * 联系组ID(数组)
     */
    private String groups;

    private Long levelId;
    private Long groupId;
    private String smsNotice;
    private String emailNotice;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

    private Long ownerId;

    private Long orgSid;

    private String orgName;

    public Long getOrgSid() {
        return orgSid;
    }

    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * @return active, inactive
     */
    public String getMobileStatus() {
        return mobileStatus;
    }

    /**
     * @param mobileStatus active, inactive
     */
    public void setMobileStatus(String mobileStatus) {
        this.mobileStatus = mobileStatus;
    }

    public String getMobileActiveCode() {
        return mobileActiveCode;
    }

    public void setMobileActiveCode(String mobileActiveCode) {
        this.mobileActiveCode = mobileActiveCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return active, inactive
     */
    public String getEmailStatus() {
        return emailStatus;
    }

    /**
     * @param emailStatus active, inactive
     */
    public void setEmailStatus(String emailStatus) {
        this.emailStatus = emailStatus;
    }

    public String getEmailActiveCode() {
        return emailActiveCode;
    }

    public void setEmailActiveCode(String emailActiveCode) {
        this.emailActiveCode = emailActiveCode;
    }

    /**
     * @return 联系组ID(数组)
     */
    public String getGroups() {
        return groups;
    }

    /**
     * @param groups 联系组ID(数组)
     */
    public void setGroups(String groups) {
        this.groups = groups;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getLevelId() {
        return levelId;
    }

    public void setLevelId(Long levelId) {
        this.levelId = levelId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getSmsNotice() {
        return smsNotice;
    }

    public void setSmsNotice(String smsNotice) {
        this.smsNotice = smsNotice;
    }

    public String getEmailNotice() {
        return emailNotice;
    }

    public void setEmailNotice(String emailNotice) {
        this.emailNotice = emailNotice;
    }
}
