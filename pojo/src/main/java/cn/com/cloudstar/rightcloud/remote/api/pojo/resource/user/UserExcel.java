/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user;

import java.io.Serializable;

;

/**
 * 导出所需要的用户信息
 * Created by durantJiang on 2017/10/11.
 */
public class UserExcel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 账号
     */

    private String account;

    /**
     * 真实名称
     */

    private String realName;

    /**
     * 项目名称
     */

    private String projectName;

    /**
     * 电子邮件地址
     */

    private String email;

    /**
     * 手机
     */

    private String mobile;

    /**
     * 用户类型
     */

    private String authType;
    /**
     * 创建时间
     */

    private String createdDt;
    /**
     * 开始时间
     */

    private String startTime;
    /**
     * 结束时间
     */

    private String endTime;
    /**
     * 用户状态
     */

    private String status;
    /**
     * 企业名称
     */

    private String companyNames;


    private String role;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(String createdDt) {
        this.createdDt = createdDt;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCompanyNames() {
        return companyNames;
    }

    public void setCompanyNames(String companyNames) {
        this.companyNames = companyNames;
    }
}
