/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.fortress.response;

import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.AccountDto;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.DepartmentDto;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.OwnerDto;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.ResGroupDto;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.ServiceDto;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.SysTypeDto;

/**
 * DESC:
 *
 * <AUTHOR>
 * @date 2020/08/14 16:59
 */
@Data
public class FortressQueryDevResponse {

    private AccountDto accounts;
    private String charset;
    private boolean deleted;
    private DepartmentDto department;
    private int id;
    private String ip;
    private String name;
    private ServiceDto services;
    private int state;
    private SysTypeDto sysType;
    private int type;
    private List<ResGroupDto> resGroups;
    private OwnerDto owner;

}
