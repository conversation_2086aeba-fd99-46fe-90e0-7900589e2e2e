package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.product;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-04-11 13:42
 * @Desc
 */
@Data
public class SfProductTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private String status;

    /**
     * 组织id
     */
    private Long orgSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 乐观锁
     */
    private Long version;

    /**
     * 运营实体id
     */
    private Long entityId;

}
