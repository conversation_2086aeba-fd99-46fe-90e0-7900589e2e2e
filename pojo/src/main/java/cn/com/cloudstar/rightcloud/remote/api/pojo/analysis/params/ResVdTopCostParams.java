/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.analysis.params;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.remote.api.pojo.request.common.BasicRequest;

/**
 * <AUTHOR>
 * @date 2020-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResVdTopCostParams extends BasicRequest {
    private Boolean apportionment;
    private Long orgId;
    private List<Long> orgIds;
    private String orNullOrgId;
    private String isNullOrg;
    private Long envId;
    private String accountId;
    private String df;
    private String productCode;
    private Long envIds;
    private List<String> billingCycles;
    private String startTime;
    private Date endTime;
    private String billingCycle;
    private List<String> productCodeInList;
    private List<String> accountIds;
    private List<String> tagIds;
    private int limit;
}
