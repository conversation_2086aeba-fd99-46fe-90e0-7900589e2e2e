/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.fortress.response;

import lombok.Data;

import cn.com.cloudstar.rightcloud.core.pojo.fortress.dto.DepartmentDto;

@Data
public class FortressAddUserResponse {

    private Long id;
    private String loginName;
    private String userName;
    private String email;
    private DepartmentDto department;
    private int state;
    private long lastLoginTime;
    private int pwdValidType;
    private long joinTime;
    private long updateTime;
    private int joinUser;
    private boolean deleted;
    private int userType;
    private boolean enabled;
}
