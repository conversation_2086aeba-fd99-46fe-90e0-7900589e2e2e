package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

import lombok.Data;



/**
 * createTime:2022-08-24 19:57
 *
 * <AUTHOR>
 * @version 1.0
 * @since JDK1.8
 */
@Data
public class StorageInfo implements Serializable {

    @J<PERSON><PERSON>ield(name = "ShareID")
    private String shareID;
    @J<PERSON>NField(name = "ShareType")
    private String shareType;
    @JSONField(name = "FloatManagerIP")
    private String floatManagerIP;
    @JSONField(name = "MasterManagerIP")
    private String masterManagerIP;
    @JSONField(name = "ShareInfo")
    private List<ShareInfo> shareInfos;


}
