/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.remote.api.pojo.request.common.BasicRequest;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryResVdByParamsRequest extends BasicRequest implements Serializable {

    /**
     * 资源组id
     */
    private String cloudDeploymentId;

    /**
     * 名称模糊匹配
     */
    private String nameLike;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态不等于
     */
    private String statusNotEquals;

    /**
     * 类型
     */
    private String instanceType;

    /**
     * 存储类型
     */
    private String volumeTypeId;

    /**
     * 云环境id
     */
    private String cloudEnvId;

    /**
     * 组织id
     */
    private String orgSid;

    /**
     * 存储大小符号, gte:大于等于 lte:小于等于 默认为等于
     */
    private String allocateDiskSizeSign;

    /**
     * 存储大小
     */
    private String allocateDiskSize;

    /**
     * 是否已挂载。 empty： 未挂载， not_empty：已挂载
     */
    private String resVmIdFlag;

    /**
     * 排序字段
     */
    @JsonIgnore
    private String orderClause;

    private List<String> statusNotIn;
}
