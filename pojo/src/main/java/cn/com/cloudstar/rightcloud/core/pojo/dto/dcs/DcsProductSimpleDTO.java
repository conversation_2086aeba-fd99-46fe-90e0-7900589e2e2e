/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.dcs;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * Dcs产品简单信息
 **/
@Data
public class DcsProductSimpleDTO implements Serializable {

    /**
     * 缓存引擎类型
     **/
    private String engine;
    /**
     * 支持的引擎版本号，以分号分割
     **/
    private String engineVersion;

    /**
     * 缓存实例的内存规格
     **/
    private String capacity;

    /**
     * 容量单位
     **/
    private String unit;

    /**
     * 可用区ID
     **/
    private List<String> availableZones;

    /**
     * 产品id
     **/
    private String productId;
    /**
     * DCS的产品规格编码
     **/
    private String specCode;
    /**
     * 缓存实例类型
     **/
    private String cacheMode;
    /**
     * 存实例的产品类型
     **/
    private String productType;
    /**
     * CPU架构类型
     **/
    private String cpuType;
    /**
     * 存储类型
     **/
    private String storageType;

    /**
     * 计费类型
     **/
    private String chargingType;
    /**
     * 产品类型
     **/
    private String prodType;
    /**
     * 云服务类型编码
     **/
    private String cloudServiceTypeCode;
    /**
     * 云资源类型编码
     **/
    private String cloudResourceTypeCode;

    /**
     * 最大连接数
     */
    private String maxConnections;
}
