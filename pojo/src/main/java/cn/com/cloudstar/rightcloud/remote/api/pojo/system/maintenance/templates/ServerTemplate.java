/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.templates;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class ServerTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private String description;

    private String href;

    private String publisher;

    /**
     * 1.publish  2.unpublish
     */
    private String publishStatus;


    private Long clonedId;

    private String clonedName;

    private Date clonedDt;

    private Long orgSid;

    private String orgName;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

    private String templateClass;

    private Long publishedId;

    private String envConfig;

    private ServerTemplateRevision revision;

    private String cloudEnvNames;

    private String cloudEnvTypes;

    private List<Map<String, Object>> versions;

    /**
     * 是否固定云环境
     */
    private Boolean fixed;

    private String type;

}
