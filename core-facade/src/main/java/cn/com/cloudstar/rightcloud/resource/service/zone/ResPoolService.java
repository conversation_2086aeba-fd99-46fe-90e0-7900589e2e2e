/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.zone;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResPool;

/**
 * The interface Res pool service.
 */
public interface ResPoolService {

    /**
     * Count by params int.
     *
     * @param example the example
     * @return the int
     */
    int countByParams(Criteria example);

    /**
     * Select by primary key res pool.
     *
     * @param id the id
     * @return the res pool
     */
    ResPool selectByPrimaryKey(Long id);

    List<ResPool> selectByExample(Criteria example);

    /**
     * Select by params list.
     *
     * @param example the example
     * @return the list
     */
    List<ResPool> selectAllPools(Criteria example);

    /**
     * Select by params list.
     *
     * @param example the example
     * @return the list
     */
    List<ResPool> selectByParams(Criteria example);

    List<ResPool> selectBaseByParams(Criteria example);

    /**
     * Delete by primary key int.
     *
     * @param id the id
     * @return the int
     */
    int deleteByPrimaryKey(Long id);

    int deleteAvailablePartition(Long cloudEnvId);

    /**
     * Delete available partition int.
     *
     * @param id         the id
     * @param cloudEnvId the cloud env id
     * @return the int
     */
    int deleteAvailablePartition(Long id, Long cloudEnvId);

    /**
     * Update by primary key selective int.
     *
     * @param record the record
     * @return the int
     */
    int updateByPrimaryKeySelective(ResPool record);

    /**
     * Update by primary key int.
     *
     * @param record the record
     * @return the int
     */
    int updateByPrimaryKey(ResPool record);

    /**
     * Delete by params int.
     *
     * @param example the example
     * @return the int
     */
    int deleteByParams(Criteria example);

    /**
     * Update by params selective int.
     *
     * @param record  the record
     * @param example the example
     * @return the int
     */
    int updateByParamsSelective(ResPool record, Criteria example);

    /**
     * Update by params int.
     *
     * @param record  the record
     * @param example the example
     * @return the int
     */
    int updateByParams(ResPool record, Criteria example);

    /**
     * Insert int.
     *
     * @param record the record
     * @return the int
     */
    int insert(ResPool record);

    /**
     * Insert selective int.
     *
     * @param record the record
     * @return the int
     */
    int insertSelective(ResPool record);

    /**
     * Create available partition int.
     *
     * @param resPool the resPool
     * @return the int
     */
    int createAvailablePartition(ResPool resPool);

    /**
     * Update available partition int.
     *
     * @param resPool the res pool
     * @return the int
     */
    @Transactional(rollbackFor = Exception.class)
    int updateAvailablePartition(ResPool resPool);

    /**
     * 查询分享的分区列表
     */
    List<ResPool> selectResPoolsOfShare(Criteria criteria);

    List<ResPool> selectResPoolAlloc2Project(Criteria criteria);

    List<ResPool> queryAvailablePartition(Criteria criteria);

    /**
     * 查询云环境独享的orgSid
     *
     * @param resPoolId 分区ID
     * @return orgSid
     */
    Long sharedAsExclusiveOrgSid(Long resPoolId);

    /**
     * 根据名称查询分区
     *
     * @param zone zoneName
     * @param cloudEnvId 云环境ID
     * @return 分区
     */
    ResPool selectByName(String zone, Long cloudEnvId);

    /**
     * 查询可分配到项目删改的分区
     *
     * @param criteria 查询条件
     * @return 分区列表
     */
    List<ResPool> selectCanAllocPool(Criteria criteria);
}
