#!/bin/sh

#
# Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
#
umask 0027
EXEC_PARAM_COMMAND=" -XX:TieredStopAtLevel=1 -noverify -Dspring.jmx.enabled=false -Dloader.path=lib/ -Djava.security.egd=file:/dev/./urandom "
EXEC_END_COMMAND="-jar app.jar"

if [ $BSS_SERVER_PORT_NUM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.port=$BSS_SERVER_PORT_NUM "
fi

if [ $BSS_SERVER_MGR_PORT_NUM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.mgr.port=$BSS_SERVER_MGR_PORT_NUM "
fi


# mysql
if [ $BSS_DB_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.address=$BSS_DB_HOST "
elif [ $DB_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.address=$DB_HOST "
fi

if [ $BSS_DB_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.port=$BSS_DB_PORT "
elif [ $DB_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.port=$DB_PORT "
fi

if [ $BSS_DB_NAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.name=$BSS_DB_NAME "
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.dbname=$BSS_DB_NAME "
elif [ $DB_NAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.name=$DB_NAME "
fi

if [ $BSS_DB_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.username=$BSS_DB_USERNAME "
elif [ $DB_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.username=$DB_USERNAME "
fi

if [ $BSS_DB_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.bss.db.password=$BSS_DB_PASSWORD "
fi

# redis
if [ $REDIS_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.host=$REDIS_HOST "
fi

if [ $REDIS_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.port=$REDIS_PORT "
fi

if [ $REDIS_IS_SENTINEL ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.is_sentinel=$REDIS_IS_SENTINEL "
fi

if [ $REDIS_PASSWORD ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.password=$REDIS_PASSWORD "
fi



if [ $REDIS_MASTER_NAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.master=$REDIS_MASTER_NAME "
fi

if [ $REDIS_SENTINELS ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.sentinels=$REDIS_SENTINELS "
fi


# mq
if [ $MQ_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.host=$MQ_HOST "
fi

if [ $MQ_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.port=$MQ_PORT "
fi

if [ $MQ_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.username=$MQ_USERNAME "
fi

if [ $MQ_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.password=$MQ_PASSWORD "
fi

if [ MQ_KEY_STORE_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.key.store.password=$MQ_KEY_STORE_PASSWORD "
fi

if [ MQ_TRUST_STORE_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.trust.store.password=$MQ_TRUST_STORE_PASSWORD "
fi



# fluentd
if [ $FLUENTD_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.host=$FLUENTD_HOST "
fi

if [ $FLUENTD_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.port=$FLUENTD_PORT "
fi

if [ $FLUENTD_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.enable=$FLUENTD_ENABLE "
fi

# remote request
if [ $GATEWAY_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.gateway.host=$GATEWAY_HOST "
fi

if [ $OSS_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.oss.path=$OSS_PATH "
fi
if [ $MONGODB_HOST ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.host=$MONGODB_HOST "
fi

if [ $MONGODB_PORT ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.port=$MONGODB_PORT "
fi

if [ $MONGODB_USERNAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.username=$MONGODB_USERNAME "
fi

if [ $MONGODB_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.password=$MONGODB_PASSWORD "
fi

if [ $MONGODB_CLIENT_KEYSTORE_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.client.keystore.password=$MONGODB_CLIENT_KEYSTORE_PASSWORD "
fi

if [ $MONGODB_CLIENT_P12_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.client.p12.password=$MONGODB_CLIENT_P12_PASSWORD "
fi



if [ $MONGO_REPLICA_SET ];then
  PROFILES_ACTIVE=${PROFILES_ACTIVE}",mongo-ha"
  EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.replicaset=$MONGO_REPLICA_SET "
fi

if [ $NACOS_ADDR ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.addr=$NACOS_ADDR "
fi

if [ $NACOS_EPHEMERAL ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.discovery.ephemeral=$NACOS_EPHEMERAL "
fi

if [ $NACOS_HEARTBEAT_INTERVAL ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.discovery.heartbeat.interval=$NACOS_HEARTBEAT_INTERVAL "
fi

if [ $NACOS_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.username=$NACOS_USERNAME "
fi

if [ $NACOS_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.password=$NACOS_PASSWORD "
fi


if [ $MQ_P12_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.keyStore=$MQ_P12_FILE "
fi

if [ $MQ_JKS_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.trustStore=$MQ_JKS_FILE "
fi



if [ $MQ_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.enabled=$MQ_SSL_ENABLE "
fi

if [ $MQ_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.algorithm=$MQ_ALGORITHM "
fi

if [ $CLOUDSTAR_KEYSTORE_TYPE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.type=$CLOUDSTAR_KEYSTORE_TYPE "
fi




if [ $MONGODB_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.ssl.algorithm=$MONGODB_ALGORITHM "
fi

if [ $MONGODB_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.ssl.enabled=$MONGODB_SSL_ENABLE "
fi

if [ $MONGODB_CLIENT_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.mongodb.filepath=$MONGODB_CLIENT_KEYSTORE_PATH "
fi



if [ $MONGODB_CLIENT_P12_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.p12.mongodb.filepath=$MONGODB_CLIENT_P12_PATH "
fi


if [ $MINIO_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.minio.ssl.algorithm=$MINIO_ALGORITHM "
fi

if [ $MINIO_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.minio.ssl.enabled=$MINIO_SSL_ENABLE "
fi

if [ $MINIO_CLIENT_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.minio.filepath=$MINIO_CLIENT_KEYSTORE_PATH "
fi


if [ $MINIO_CLIENT_P12_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.p12.minio.filepath=$MINIO_CLIENT_P12_PATH "
fi



if [ $BSS_SERVER_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore=$BSS_SERVER_KEYSTORE_PATH "
fi

if [ $BSS_SERVER_KEYSTORE_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.bss.customize.keystore.password=$BSS_SERVER_KEYSTORE_PASSWORD "
fi


if [ $CMP_BSS_DUBBO_KEY ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.dubbo.key=$CMP_BSS_DUBBO_KEY "
fi



if [ $CMP_BSS_DUBBO_CERT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.dubbo.cert=$CMP_BSS_DUBBO_CERT "
fi

if [ $CMP_BSS_DUBBO_CERT_COLLECTION ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.dubbo.cert.collection=$CMP_BSS_DUBBO_CERT_COLLECTION "
fi

if [ $FILE_STORAGE_ACTIVE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.file.storage.active=$FILE_STORAGE_ACTIVE"
fi

if [ $FILE_STORAGE_MINIO_INITIALIZE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.file.storage.minio.initialize=$FILE_STORAGE_MINIO_INITIALIZE"
fi

if [ $FILE_STORAGE_MINIO_BUCKET_NAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.file.storage.minio.bucket.name=$FILE_STORAGE_MINIO_BUCKET_NAME"
fi

if [ $FILE_STORAGE_MINIO_INTERCEPT_URL ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.file.storage.minio.intercept.url=$FILE_STORAGE_MINIO_INTERCEPT_URL"
fi

if [ $LOCAL_POD_IP ]; then
   EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.local.podip=$LOCAL_POD_IP "
fi


if [ $REDIS_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.ssl=$REDIS_SSL_ENABLE "
fi

if [ $REDIS_SSL_JKS_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.trustKeyStore=$REDIS_SSL_JKS_FILE "
fi

if [ $CRYPTO_KEY_PATH ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.key.path=$CRYPTO_KEY_PATH"
fi

# deal openrasp
tar zxvf /rasp-java.tar.gz -C /tmp/ && rm -f  /tmp/rasp-2022-03-11/RaspInstall.jar \
&& chmod -R 700 /tmp/rasp-2022-03-11 && chmod 400 /tmp/rasp-2022-03-11/rasp/conf/openrasp.yml \
&& chmod 400 /tmp/rasp-2022-03-11/rasp/*.jar && chmod 400 /tmp/rasp-2022-03-11/rasp/plugins/official.js

EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dspring.profiles.active=$PROFILES_ACTIVE "
if [ $SHOW_PARAM ];then
    echo "EXEC COMMAND:java" $JVM_OPTS ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
fi
exec tini -- java $JVM_OPTS ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
