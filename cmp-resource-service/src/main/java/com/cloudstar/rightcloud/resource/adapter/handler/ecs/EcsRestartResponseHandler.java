package com.cloudstar.rightcloud.resource.adapter.handler.ecs;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cloudstar.rightcloud.resource.adapter.handler.AbstractCallbackHandler;
import com.cloudstar.rightcloud.resource.common.adapter.annotation.HandlerTarget;
import com.cloudstar.rightcloud.resource.common.constant.type.SpecTypeSpecCode;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionResultEnum;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionTemplateEnum;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionTypeEnum;
import com.cloudstar.rightcloud.resource.common.em.ResourceChangeEnum;
import com.cloudstar.rightcloud.resource.data.envs.dto.ResCloudEnv;
import com.cloudstar.rightcloud.resource.data.org.mapper.OrgMapper;
import com.cloudstar.rightcloud.resource.data.spectype.dao.ResSpecTypeDao;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeListDto;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeListResultDto;
import com.cloudstar.rightcloud.resource.data.subnetport.dao.ResSubnetPortDao;
import com.cloudstar.rightcloud.resource.data.subnetport.dto.ResSubnetPort;
import com.cloudstar.rightcloud.resource.data.user.dto.SysUser;
import com.cloudstar.rightcloud.resource.data.user.mapper.UserMapper;
import com.cloudstar.rightcloud.resource.data.vd.dao.ResVdDao;
import com.cloudstar.rightcloud.resource.data.vd.dto.ResVd;
import com.cloudstar.rightcloud.resource.data.vm.dao.ResVmDao;
import com.cloudstar.rightcloud.resource.data.vm.dto.ResVm;
import com.cloudstar.rightcloud.resource.data.vm.dto.ResVmSpecAttr;
import com.cloudstar.rightcloud.resource.data.vm.mapper.ResVmMapper;
import com.cloudstar.rightcloud.resource.data.vmgroup.dao.ResVmGroupDao;
import com.cloudstar.rightcloud.resource.data.vmgroup.dto.ResVmGroup;
import com.cloudstar.rightcloud.resource.data.vmgroup.dto.ResVmGroupDto;
import com.cloudstar.rightcloud.resource.data.vpc.dao.ResVpcDao;
import com.cloudstar.rightcloud.resource.data.vpc.dto.ResVpc;
import com.cloudstar.rightcloud.sdk.common.ResourceTypeCode;
import com.cloudstar.rightcloud.sdk.common.em.ResourceDataSyncEventEnum;
import com.cloudstar.rightcloud.sdk.common.status.ResourceStatus;
import com.cloudstar.rightcloud.sdk.resource.common.em.StoragePurposeEnum;
import com.cloudstar.rightcloud.sdk.resource.common.model.ResourceActionResult;
import com.cloudstar.rightcloud.sdk.resource.ecs.response.EcsDataSyncModel;
import com.cloudstar.rightcloud.sdk.resource.ecs.response.EcsRestartResponse;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 云主机重启响应回调处理
 *
 * @author: chengpeng
 * @date: 2023/5/9 9:22
 */
@Slf4j
@Service
@HandlerTarget(target = ResourceTypeCode.ECS + "restart")
public class EcsRestartResponseHandler extends AbstractCallbackHandler {

    @Resource
    private ResVmDao resVmDao;
    @Resource
    private ResVdDao resVdDao;
    @Resource
    private ResSpecTypeDao resSpecTypeDao;
    @Resource
    private ResVpcDao vpcDao;
    @Resource
    private ResSubnetPortDao subnetPortDao;
    @Resource
    private ResVmMapper vmMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private ResVmGroupDao resVmGroupDao;

    @Override
    public void handler(ResourceActionResult result, boolean relaMaintain) {
        EcsRestartResponse response = JSONUtil.toBean(JSONUtil.toJsonStr(result.getData()), EcsRestartResponse.class);
        // 云环境
        ResCloudEnv cloudEnv = checkCloudEnvNormal(result.getCloudEnvId(), true);
        ResVm resVm = resVmDao.getByUuidAndCloudEnvId(response.getUuid(), result.getCloudEnvId());
        // 发消息
        try {
            sendMessage(result, resVm);
        } catch (Exception e) {
            log.error("Message Send Failed! errorMessage: {}", e.getMessage());
        }

        if (result.getSuccess()) {
            resVm.setStatus(ResourceStatus.ACTIVE.getCode());
            resVm.setStatusInfo("");
        } else {
            resVm.setStatus(resVm.getOriginalStatus());
        }
        prepareUpdateCommonParams(resVm, cloudEnv);
        resVmDao.updateById(resVm);
        pushResource(cloudEnv, ResourceTypeCode.ECS, Collections.singletonList(resVm), ResourceChangeEnum.UPDATE);

//        Object originalData = result.getOriginalData();
//        if (originalData != null) {
//            Long orgId = resVm.getOrgId();
//            List<ResVm> resList = Collections.singletonList(resVm);
//            List<EcsDataSyncModel> ecsDataSyncModel = new ArrayList<>();
//            List<Map> ecsOriginalData = JSONUtil.toList(JSONUtil.toJsonStr(originalData), Map.class);
//            List<ResVpc> vpcs = vpcDao.selectByCloudEnvId(result.getCloudEnvId());
//            List<ResSubnetPort> subnetPorts = subnetPortDao.selectByCloudEnvId(result.getCloudEnvId());
//            List<ResVmSpecAttr> attrs = vmMapper.selectResVmSpecAttr(cloudEnv.getCloudEnvAccountId());
//            String orgName = orgMapper.selectNameById(orgId);
//            List<SysUser> sysUser = userMapper.selectBatchIds(Collections.singleton(resVm.getOwner()));
//            ResVmGroupDto vmGroupDto = new ResVmGroupDto();
//            vmGroupDto.setCloudEnvId(result.getCloudEnvId());
//            List<ResVmGroup> resVmGroups = resVmGroupDao.selectList(vmGroupDto);
//            EcsResDataSyncHandler.resDataConv(resList, ecsOriginalData, ecsDataSyncModel, vpcs, subnetPorts, attrs,
//                    orgName, cloudEnv.getRegion(), sysUser, resVmGroups);
//            CompletableFuture.runAsync(() -> {
//                resServerDataSyncService.dataSync(ResourceTypeCode.ECS, result.getUserSid(), orgId,
//                        ResourceDataSyncEventEnum.UPDATE, JSON.toJSONString(ecsDataSyncModel, SerializerFeature.WriteMapNullValue), cloudEnv.getId());
//            });
//        }
    }

    private void sendMessage(ResourceActionResult result, ResVm resVm) {
        final String templateId = NoticeActionTemplateEnum.getTemplateId(ResourceChangeEnum.UPDATE.name());
        Map<String, Object> noticeParam = Maps.newHashMap();
        noticeParam.put("operator", result.getOperator());
        noticeParam.put("createTime", ObjectUtil.isNotNull(resVm.getUpdatedDt())
                ? DateUtil.formatDateTime(resVm.getUpdatedDt()) : DateUtil.formatDateTime(new Date()));
        noticeParam.put("resourceType", ResourceTypeCode.ECS);
        noticeParam.put("actionType", NoticeActionTypeEnum.RESTART.getActionType());
        noticeParam.put("actionResult", NoticeActionResultEnum.valueOf(String.valueOf(result.getSuccess()).toUpperCase()).getActionResult());
        noticeParam.put("resourceId", resVm.getId());
        noticeParam.put("resourceName", resVm.getName());

        StringBuilder specs = StrUtil.builder("CPU : " + resVm.getCpu() + "\n"
                + "\t\t\t\t内存 : " + resVm.getMemory() + "\n"
                + "\t\t\t\tIP地址 : " + resVm.getPublicIp() + "\n"
                + "\t\t\t\t操作系统 : " + resVm.getOsType() + "\n");
        // 设置系统盘, 数据盘
        setResVmVdInfo(resVm.getId(), result.getCloudEnvId(), specs);
        noticeParam.put("specs", specs);

        // 发送消息
        doNotice(result, templateId, noticeParam);

    }

    private void setResVmVdInfo(Long vmId, Long cloudEnvId, StringBuilder specs) {

        // 查询主机关联的硬盘
        Map<Long, List<ResVd>> vmId2Vds = resVdDao.selectByVmIds(
                Collections.singletonList(vmId)).stream().collect(Collectors.groupingBy(ResVd::getVmId));
        // 查询硬盘资源规格
        ResSpecTypeListDto resSpecTypeListDto = new ResSpecTypeListDto()
                .setSpecCode(SpecTypeSpecCode.VD_TYPE_ID).setCloudEnvIds(Collections.singletonList(cloudEnvId));
        Map<Long, ResSpecTypeListResultDto> specTypeId2Obj = resSpecTypeDao.selectList(
                resSpecTypeListDto).stream().collect(Collectors.toMap(ResSpecTypeListResultDto::getId, o -> o, (o1, o2) -> o1));
        // 按存储用途分类 主机关联的硬盘列表
        Map<String, List<ResVd>> vdTypeId2Vds = vmId2Vds.getOrDefault(vmId, new ArrayList<>()).stream()
                .filter(e -> StringUtils.isNotBlank(e.getStoragePurpose()))
                .collect(Collectors.groupingBy(ResVd::getStoragePurpose));
        vdTypeId2Vds.forEach((k, v) -> {
            String diskInfo = v.stream().map(resVd -> "(" + specTypeId2Obj.getOrDefault(resVd.getVdTypeId(),
                    new ResSpecTypeListResultDto()).getName() + ")"
                    + resVd.getSize() + "GB").collect(Collectors.joining(","));
            // 系统盘
            if (StringUtils.equalsIgnoreCase(k, StoragePurposeEnum.SYS_DISK.getCode())) {
                specs.append("\t\t\t\t系统盘 : ").append(diskInfo).append("\n");
            }
            // 数据盘
            if (StringUtils.equalsIgnoreCase(k, StoragePurposeEnum.DATA_DISK.getCode())) {
                specs.append("\t\t\t\t数据盘 : ").append(diskInfo).append("\n");
            }
        });
    }

}
