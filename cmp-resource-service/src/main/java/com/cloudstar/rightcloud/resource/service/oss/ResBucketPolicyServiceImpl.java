package com.cloudstar.rightcloud.resource.service.oss;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cloudstar.rightcloud.api.cmdb.resinst.CmdbResInstClient;
import com.cloudstar.rightcloud.api.cmdb.resinst.form.CmdbResInstPageFeignForm;
import com.cloudstar.rightcloud.api.cmdb.resinst.result.CmdbResInstPageFeignResult;
import com.cloudstar.rightcloud.api.cmdb.resinst.result.CmdbResInstPropValFeignResult;
import com.cloudstar.rightcloud.api.system.config.SysConfigClient;
import com.cloudstar.rightcloud.api.system.config.result.SysConfigFeignResult;
import com.cloudstar.rightcloud.common.constant.base.CommonFieldName;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.enums.MessageSendTypeEnum;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.driver.mq.MessageQueueHelper;
import com.cloudstar.rightcloud.driver.mq.exception.MessageQueueException;
import com.cloudstar.rightcloud.notice.pojo.NoticeBaseParam;
import com.cloudstar.rightcloud.notice.util.NoticeUtil;
import com.cloudstar.rightcloud.resource.adapter.handler.ResourceActionHelper;
import com.cloudstar.rightcloud.resource.client.common.param.ActionParam;
import com.cloudstar.rightcloud.resource.client.oss.param.ResBucketPolicyPageParam;
import com.cloudstar.rightcloud.resource.client.oss.ressult.ResBucketPolicyPageResult;
import com.cloudstar.rightcloud.resource.client.oss.service.ResBucketPolicyService;
import com.cloudstar.rightcloud.resource.common.constant.config.SystemConfigKeyConstant;
import com.cloudstar.rightcloud.resource.common.constant.config.SystemConfigTypeConstant;
import com.cloudstar.rightcloud.resource.common.constant.msg.field.ResFieldKeyConstant;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionResultEnum;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionTemplateEnum;
import com.cloudstar.rightcloud.resource.common.em.NoticeActionTypeEnum;
import com.cloudstar.rightcloud.resource.common.em.NoticeSwitchEnum;
import com.cloudstar.rightcloud.resource.common.em.ResourceChangeEnum;
import com.cloudstar.rightcloud.resource.data.envs.dao.ResCloudEnvDao;
import com.cloudstar.rightcloud.resource.data.envs.dto.ResCloudEnv;
import com.cloudstar.rightcloud.resource.data.oss.dao.ResBucketDao;
import com.cloudstar.rightcloud.resource.data.oss.dao.ResBucketPolicyDao;
import com.cloudstar.rightcloud.resource.data.oss.dto.ResBucket;
import com.cloudstar.rightcloud.resource.data.oss.dto.ResBucketPolicy;
import com.cloudstar.rightcloud.resource.data.oss.dto.ResBucketPolicyListDto;
import com.cloudstar.rightcloud.resource.data.oss.dto.ResBucketPolicyPageDto;
import com.cloudstar.rightcloud.resource.data.oss.dto.ResBucketPolicyPageResultDto;
import com.cloudstar.rightcloud.sdk.common.ResourceTypeCode;
import com.cloudstar.rightcloud.sdk.resource.common.model.ResourceAction;
import com.cloudstar.rightcloud.sdk.resource.common.model.ResourceActionResult;
import com.cloudstar.rightcloud.sdk.resource.oss.BucketPolicyModel;
import com.cloudstar.rightcloud.sdk.resource.oss.BucketPolicyRuleModel;
import com.cloudstar.rightcloud.sdk.resource.oss.request.BucketPolicyUpdateRequest;
import com.cloudstar.rightcloud.utils.AuthUserInfoUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 桶策略Service实现
 *
 * <AUTHOR>
 * @since 2023-06-08 22:24
 */
@Slf4j
@Service
public class ResBucketPolicyServiceImpl implements ResBucketPolicyService {

    @Resource
    private ResBucketDao resBucketDao;
    @Resource
    private ResBucketPolicyDao resBucketPolicyDao;
    @Resource
    private ResCloudEnvDao resCloudEnvDao;
    @Resource
    private CmdbResInstClient cmdbResInstClient;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private MessageQueueHelper messageQueueHelper;
    @Resource
    private ResourceActionHelper resourceActionHelper;

    @Override
    public RightCloudResult<PageResult<ResBucketPolicyPageResult>> getBucketPolicyPage(ResBucketPolicyPageParam param) {
        ResBucketPolicyPageDto bucketPolicyPageDto = BeanUtil.copyProperties(param, ResBucketPolicyPageDto.class);
        PageResult<ResBucketPolicyPageResultDto> pageResult = resBucketPolicyDao.page(bucketPolicyPageDto);
        return RightCloudResult.success(PageHelperUtil.of(pageResult, ResBucketPolicyPageResult.class));
    }

    @Override
    @Transactional
    public RightCloudResult<Long> createBucketPolicy(ActionParam param) {
        BucketPolicyModel bean = BeanUtil.toBean(param.getData(), BucketPolicyModel.class);
        // 校验桶是否存在
        ResBucket resBucket = checkBucketExist(bean);
        ResBucketPolicy resBucketPolicy = BeanUtil.copyProperties(bean, ResBucketPolicy.class);
        resBucketPolicy.setUuid(UUID.randomUUID().toString().replace("-", ""));
        // 查询桶现有策略
        ResBucketPolicyListDto bucketPolicyListDto = new ResBucketPolicyListDto();
        bucketPolicyListDto.setBucketId(resBucket.getId());
        List<ResBucketPolicy> resBucketPolicies = resBucketPolicyDao.selectList(bucketPolicyListDto);
        List<ResBucketPolicy> oldResBucketPolicies = new ArrayList<>(resBucketPolicies);
        // 将新增策略添加到新策略集合中
        resBucketPolicies.add(resBucketPolicy);
        // 下发请求到Driver
        sendToDriver(param, resBucket, resBucketPolicy, oldResBucketPolicies, resBucketPolicies, bean.getExtra());
        resBucketPolicy.setCloudEnvId(param.getCloudEnvId());
        resBucketPolicy.setOrgId(AuthUserInfoUtil.getCurrentOrgId());
        CrudHelpUtil.prepareInsertParams(resBucketPolicy);
        resBucketPolicyDao.insert(resBucketPolicy);
        return RightCloudResult.success(resBucketPolicy.getId());
    }


    @Override
    @Transactional
    public RightCloudResult<Void> deleteBucketPolicy(ActionParam param) {
        BucketPolicyModel bean = BeanUtil.toBean(param.getData(), BucketPolicyModel.class);
        // 校验桶是否存在
        ResBucket resBucket = checkBucketExist(bean);
        ResBucketPolicy resBucketPolicy = resBucketPolicyDao.selectById(bean.getId());
        handlePolicyAndSend(resBucket, resBucketPolicy.getId(), param, resBucketPolicy, bean.getExtra(), false);
        CrudHelpUtil.prepareInsertParams(resBucketPolicy);
        resBucketPolicyDao.deleteBatchIds(Collections.singletonList(resBucketPolicy.getId()));
        return RightCloudResult.success();
    }

    /**
     * 处理处理并发送请求到Driver
     *
     * @param resBucket 桶信息
     * @param resBucketPolicyId 桶策略ID
     * @param param 操作参数
     * @param resBucketPolicy 桶策略
     * @param extra 高级属性
     */
    private void handlePolicyAndSend(ResBucket resBucket, Long resBucketPolicyId, ActionParam param,
                                     ResBucketPolicy resBucketPolicy, String extra, Boolean update) {
        // 查询桶现有策略
        ResBucketPolicyListDto bucketPolicyListDto = new ResBucketPolicyListDto();
        bucketPolicyListDto.setBucketId(resBucket.getId());
        List<ResBucketPolicy> oldResBucketPolicies = resBucketPolicyDao.selectList(bucketPolicyListDto);
        // 筛选待删除或更新的策略
        List<ResBucketPolicy> selectedPolicies = oldResBucketPolicies.stream()
                                                                     .filter(policy -> Objects.equals(
                                                                             resBucketPolicyId, policy.getId()))
                                                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selectedPolicies)) {
            BizAssertUtils.fail(CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, ResFieldKeyConstant.OSS_BUCKET_POLICY);
        }
        List<ResBucketPolicy> resBucketPolicies = oldResBucketPolicies.stream()
                                                                      .filter(policy -> !Objects.equals(resBucketPolicyId, policy.getId()))
                                                                      .collect(Collectors.toList());
        if (update) {
            resBucketPolicy.setUuid(UUID.randomUUID().toString().replace("-", ""));
            resBucketPolicies.add(resBucketPolicy);
        }
        // 下发请求到Driver
        sendToDriver(param, resBucket, resBucketPolicy, oldResBucketPolicies, resBucketPolicies, extra);
    }

    @Override
    @Transactional
    public RightCloudResult<Void> updateBucketPolicy(ActionParam param) {
        BucketPolicyModel bean = BeanUtil.toBean(param.getData(), BucketPolicyModel.class);
        // 校验桶是否存在
        ResBucket resBucket = checkBucketExist(bean);
        ResBucketPolicy resBucketPolicy = BeanUtil.copyProperties(bean, ResBucketPolicy.class);
        resBucketPolicy.setBucketName(resBucket.getName());
        resBucketPolicy.setCloudEnvId(resBucket.getCloudEnvId());
        // 查询桶现有策略
        handlePolicyAndSend(resBucket, bean.getId(), param, resBucketPolicy, bean.getExtra(), true);
        resBucketPolicyDao.updateById(resBucketPolicy);
        return RightCloudResult.success();
    }

    /**
     * 下发请求到Driver
     *
     * @param param 操作参数
     * @param resBucket 桶信息
     * @param resBucketPolicy 桶策略
     * @param oldResBucketPolicies 原桶策略信息集合
     * @param resBucketPolicies 新桶策略信息集合
     * @param extra 高级属性
     */
    private void sendToDriver(ActionParam param, ResBucket resBucket, ResBucketPolicy resBucketPolicy,
                              List<ResBucketPolicy> oldResBucketPolicies, List<ResBucketPolicy> resBucketPolicies, String extra) {
        // 构建桶策略更新请求
        BucketPolicyUpdateRequest request = buildBucketPolicyUpdateRequest(resBucketPolicies, oldResBucketPolicies, resBucket);
        request.setBucketName(resBucket.getName());
        request.setExtra(extra);
        ResourceAction action = resourceActionHelper.init(param);
        ResourceActionResult result = null;
        try {
            action.setData(request);
            // 下发driver处理请求
            result = messageQueueHelper.rpc(action);
            resBucketPolicy.setBucketName(resBucket.getName());
        } catch (MessageQueueException e) {
            BizAssertUtils.fail(CommonMsgConstant.COMMON_ERROR_HANDLE_FAIL);
        }

        try {
            ResCloudEnv cloudEnv = null;
            if (ObjectUtil.isNotNull(param.getCloudEnvId())) {
                cloudEnv = resCloudEnvDao.getById(param.getCloudEnvId());
            }
            sendMessage(result, resBucket, cloudEnv, param.getAction().toUpperCase());
        } catch (Exception e) {
            log.error("Message send failed! errorMessage: {}", e.getMessage());
        }

        if (!result.getSuccess()) {
            log.error(result.getErrMsg());
            throw new BizException(result.getErrMsg());
        }
    }

    private void sendMessage(ResourceActionResult result, ResBucket resBucket, ResCloudEnv cloudEnv, String actionType) {
        // 1.查看是否开启资源通知
        SysConfigFeignResult sysNoticeSwitchConfig = sysConfigClient.findConfigByTypeAndKey(
                SystemConfigTypeConstant.RESOURCE_NOTICE_TYPE_CONFIG, SystemConfigKeyConstant.RESOURCE_ACTION_NOTICE_SWITCH).getData();
        if (ObjectUtil.isNull(sysNoticeSwitchConfig)) {
            log.error("system config doesn't contains key: {}", SystemConfigKeyConstant.RESOURCE_ACTION_NOTICE_SWITCH);
            return;
        }
        String noticeSwitch = sysNoticeSwitchConfig.getConfigValue();
        // 未开启资源通知, 不通知直接返回
        if (NoticeSwitchEnum.DISABLE.getValue().equals(noticeSwitch)) {
            return;
        }
        // 2.查看通知方式
        SysConfigFeignResult sysNoticeTypeConfig = sysConfigClient.findConfigByTypeAndKey(
                SystemConfigTypeConstant.RESOURCE_NOTICE_TYPE_CONFIG, SystemConfigKeyConstant.RESOURCE_ACTION_NOTICE_TYPE).getData();
        if (ObjectUtil.isNull(sysNoticeTypeConfig)) {
            log.error("system config doesn't contains key: {}", SystemConfigKeyConstant.RESOURCE_ACTION_NOTICE_TYPE);
            return;
        }
        // 2.1通知方式
        String noticeType = sysNoticeTypeConfig.getConfigValue();
        List<String> types = JSON.parseArray(noticeType, String.class);
        if (CollUtil.isEmpty(types)) {
            log.info("Notice switch is enable, but the notice types are empty!");
            return;
        }
        final String templateId = NoticeActionTemplateEnum.getTemplateId(ResourceChangeEnum.valueOf(actionType).name());
        Map<String, Object> noticeParam = Maps.newHashMap();
        noticeParam.put("operator", result.getOperator());
        noticeParam.put("createTime", ObjectUtil.isNotNull(resBucket.getCreatedDt())
                ? DateUtil.formatDateTime(resBucket.getCreatedDt()) : DateUtil.formatDateTime(new Date()));

        noticeParam.put("resourceType", ResourceTypeCode.OSS_STRATEGY);
        noticeParam.put("actionType", NoticeActionTypeEnum.valueOf(actionType).getActionType());
        noticeParam.put("actionResult", NoticeActionResultEnum.valueOf(String.valueOf(result.getSuccess()).toUpperCase()).getActionResult());
        noticeParam.put("resourceId", resBucket.getId());
        noticeParam.put("resourceName", resBucket.getName());
        String cloudEnvName = "";
        if (ObjectUtil.isNotNull(cloudEnv)) {
            cloudEnvName = cloudEnv.getCloudEnvName();
        } else {
            cloudEnvName = getCloudEnvName(String.valueOf(resBucket.getId()));
        }
        noticeParam.put("cloudEnvName", cloudEnvName);
        String specs = "对象数量 : " + resBucket.getObjectNum() + "\n"
                + "\t\t\t\t使用容量 : " + resBucket.getUsedSize() + "GB" + "\n"
                + "\t\t\t\t桶容量 : " + resBucket.getTotalSize() + "GB" + "\n";
        noticeParam.put("specs", specs);

        for (String type : types) {
            NoticeBaseParam noticeBaseParam = new NoticeBaseParam();
            noticeBaseParam.setNoticeEnum(MessageSendTypeEnum.valueOf(type.toUpperCase()));
            noticeBaseParam.setTemplateId(templateId);
            noticeBaseParam.setReceiver(result.getOperator());
            noticeBaseParam.setTemplateParam(noticeParam);
            NoticeUtil.notice(noticeBaseParam);
        }

    }

    /**
     * 根据资源id, 查询云环境名称(发消息专用)
     *
     * @param resourceId 资源id
     * @return 云环境名称
     */
    private String getCloudEnvName(String resourceId) {
        CmdbResInstPageFeignForm feignForm = new CmdbResInstPageFeignForm();
        if (ObjectUtil.isNull(resourceId)) {
            return "";
        }
        feignForm.setInstanceIds(Collections.singletonList(resourceId));
        final PageResult<CmdbResInstPageFeignResult> data = cmdbResInstClient.page(feignForm).getData();
        List<CmdbResInstPageFeignResult> cmdbResInstList = data.getList();
        if (CollUtil.isNotEmpty(cmdbResInstList)) {
            String cloudEnvName = null;
            for (CmdbResInstPropValFeignResult property : cmdbResInstList.get(0).getResInstPropValList()) {
                if (CommonFieldName.CLOUD_ENV_NAME.equalsIgnoreCase(property.getPropCode()) && StrUtil.isNotBlank(property.getPropValue())) {
                    cloudEnvName = property.getPropValue();
                    break;
                }
                if (CommonFieldName.CLOUD_ENV_ID.equalsIgnoreCase(property.getPropCode()) && StrUtil.isNotBlank(property.getPropValue())) {
                    ResCloudEnv cloudEnv = resCloudEnvDao.getById(Long.valueOf(property.getPropValue()));
                    cloudEnvName = Optional.ofNullable(cloudEnv).map(ResCloudEnv::getCloudEnvName).orElse(null);
                }
            }
            return Optional.ofNullable(cloudEnvName).orElse("");
        }
        return "";
    }

    /**
     * 校验桶是否操作
     *
     * @param bucketPolicyModel 桶策略信息
     *
     * @return 桶信息
     */
    private ResBucket checkBucketExist(BucketPolicyModel bucketPolicyModel) {
        ResBucket resBucket = resBucketDao.selectById(bucketPolicyModel.getBucketId());
        if (resBucket == null) {
            BizAssertUtils.fail(CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS, ResFieldKeyConstant.OSS_BUCKET);
        }
        return resBucket;
    }

    /**
     * 构建桶策略更新请求
     *
     * @param resBucketPolicies 待更新桶策略
     * @param oldResBucketPolicies 原桶策略
     * @param resBucket 桶信息
     *
     * @return 桶策略更新请求
     */
    private BucketPolicyUpdateRequest buildBucketPolicyUpdateRequest(List<ResBucketPolicy> resBucketPolicies,
                                                                     List<ResBucketPolicy> oldResBucketPolicies,
                                                                     ResBucket resBucket) {
        BucketPolicyUpdateRequest bucketPolicyUpdateRequest = new BucketPolicyUpdateRequest();
        // 构建新策略规则
        List<BucketPolicyRuleModel> policyRules = Optional.ofNullable(resBucketPolicies)
                                                          .orElse(Collections.emptyList())
                                                          .stream()
                                                          .map(this::toPolicyRule)
                                                          .collect(Collectors.toList());
        // 构建原策略规则
        List<BucketPolicyRuleModel> oldPolicyRules = Optional.ofNullable(oldResBucketPolicies)
                                                             .orElse(Collections.emptyList())
                                                             .stream()
                                                             .map(this::toPolicyRule)
                                                             .collect(Collectors.toList());
        // 构建桶策略更新请求
        bucketPolicyUpdateRequest.setBucketName(resBucket.getName());
        bucketPolicyUpdateRequest.setPolicyRules(policyRules);
        bucketPolicyUpdateRequest.setOldPolicyRules(oldPolicyRules);
        return bucketPolicyUpdateRequest;
    }

    /**
     * 转换桶策略实体
     *
     * @param policy 桶策略
     *
     * @return 桶策略规则
     */
    private BucketPolicyRuleModel toPolicyRule(ResBucketPolicy policy) {
        BucketPolicyRuleModel rule = new BucketPolicyRuleModel();
        rule.setUuid(policy.getUuid());
        rule.setEffect(policy.getEffect());
        rule.setAction(policy.getAction());
        rule.setActionAuthType(policy.getActionAuthType());
        rule.setPrincipalAuthType(policy.getPrincipalAuthType());
        rule.setPrincipal(policy.getPrincipal());
        rule.setResourceAuthType(policy.getResourceAuthType());
        rule.setResource(policy.getResource());
        return rule;
    }
}
