package com.cloudstar.rightcloud.resource.service.resourceSync.strategy;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudstar.rightcloud.sdk.common.ResourceTypeCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024-01-17 15:17
 * @Desc 云硬盘
 */
@Slf4j
@Component
public class DdsResSyncStrategy extends ResSyncAbstractStrategy {

    @Override
    public String getStrategy() {
        return ResourceTypeCode.DDS;
    }

    /**
     *  原始数据
     *  [{"actions":[],"backupStrategy":{"keepDays":0,"startTime":"00:00-01:00"},"created":"2024-01-03T10:36:50","datastore":{"patchAvailable":true,"type":"DDS-Community","version":"4.2"},"dbUserName":"rwuser","engine":"rocksDB","enterpriseProjectId":"0","groups":[{"nodes":[{"availabilityZone":"cn-east-3a","id":"01fd309ee1104e3c8d268aaa004d7e92no02","name":"dds-d8b3_replica_node_2","privateIp":"*************","publicIp":"","role":"","specCode":"dds.mongodb.s6.medium.4.repset","status":"createfail"},{"availabilityZone":"cn-east-3a","id":"75c1973142644aaaa02d5df6939cbea5no02","name":"dds-d8b3_replica_node_3","privateIp":"*************","publicIp":"","role":"","specCode":"dds.mongodb.s6.medium.4.repset","status":"createfail"},{"availabilityZone":"cn-east-3a","id":"959ed62066a04e2fb32d75dce3dc81d6no02","name":"dds-d8b3_replica_node_1","privateIp":"*************","publicIp":"","role":"","specCode":"dds.mongodb.s6.medium.4.repset","status":"createfail"}],"type":"replica","volume":{"size":"10","used":"0"}}],"id":"20aceafb57e54903ba818f9b656f971ein02","maintenanceWindow":"14:00-18:00","mode":"ReplicaSet","name":"dds-d8b3","payMode":"0","port":"8635","region":"cn-east-3","securityGroupId":"761a8d51-14fd-4ac0-8f48-f6c171fd833a","ssl":0,"status":"createfail","subnetId":"bf023037-aa58-45cd-81d0-8243142ff31e","tags":[],"timeZone":"","updated":"2024-01-28T14:11:24","vpcId":"de629336-90a1-4e4c-8dc9-10784c943a45"}]
     */
    @Override
    public String handleServiceDetail(JSONObject object) {
        object.remove("tags");
        object.putOpt("createdAt", object.getStr("created"));
        return JSONUtil.toJsonStr(object);
    }

}
