package com.cloudstar.rightcloud.resource.service.resourceSync.strategy;

import com.cloudstar.rightcloud.resource.data.oss.dto.ObsIamUser;
import com.cloudstar.rightcloud.resource.data.oss.mapper.ObsIamUserMapper;
import com.cloudstar.rightcloud.sdk.common.ResourceTypeCode;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * <p>
 * obs授权信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Component
public class ObsPolicySyncStrategy extends ResSyncAbstractStrategy {

    @Resource
    private ObsIamUserMapper obsIamUserMapper;

    @Override
    public String getStrategy() {
        return ResourceTypeCode.OBS_POLICY;
    }

    @Override
    public String handleServiceDetail(JSONObject object) {
        Map<String, Object> result = new HashMap<>();

        result.put("bucketId", object.getStr("bucketName"));
        result.put("bucketName", object.getStr("bucketName"));

        result.put("authUsers", object.get("authUsers"));
        result.put("createdBy", object.getStr("createdBy"));
        result.put("createdTime", object.getStr("createdDt"));
        result.put("authority", "allow");

        String authorityOperation;
        if (CollectionUtils.isNotEmpty(object.getBeanList("action", String.class))) {
            List<String> action = object.getBeanList("action", String.class);
            authorityOperation = StrUtil.join(",", action);
        } else if("allFunction".equalsIgnoreCase(object.getStr("policyType"))){
            authorityOperation = "Get*,List*,GetObject,PutObject,DeleteObject,GetObjectAcl,PutObjectAcl,GetObjectVersion,DeleteObjectVersion,GetObjectVersionAcl,PutObjectVersionAcl,AbortMultipartUpload,ListMultipartUploadParts,ModifyObjectMetaData";
        }else if("readWrite".equalsIgnoreCase(object.getStr("policyType"))){
            authorityOperation = "GetObject,PutObject,GetObjectAcl,PutObjectAcl,GetObjectVersion,GetObjectVersionAcl,PutObjectVersionAcl,AbortMultipartUpload,ListMultipartUploadParts,ModifyObjectMetaData,Get*,List*";
        }else{
            authorityOperation = "GetObject,GetObjectAcl,GetObjectVersion,GetObjectVersionAcl,ListMultipartUploadParts,Get*,List*";
        }

        result.put("authorityOperation", authorityOperation);
        result.put("authorityResource", object.getStr("policyMethod"));
        result.put("policyPre", object.getStr("policyPre"));
        result.put("authorityRole", object.getStr("policyType"));
        result.put("bucketAclId", object.getStr("policyUuid"));
        return JSONUtil.toJsonStr(result, JSONConfig.create().setIgnoreNullValue(false));
    }
}
