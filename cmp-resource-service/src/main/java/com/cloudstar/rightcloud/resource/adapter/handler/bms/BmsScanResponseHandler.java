package com.cloudstar.rightcloud.resource.adapter.handler.bms;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.img.ColorUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cloudstar.rightcloud.api.order.sync.BssServiceClient;
import com.cloudstar.rightcloud.api.order.sync.result.DescribeProductSyncChargeTypeResponse;
import com.cloudstar.rightcloud.api.system.tag.SysTagClient;
import com.cloudstar.rightcloud.api.system.tag.form.SysTagUnbindObjsFeignForm;
import com.cloudstar.rightcloud.api.system.tag.form.TagBindObjsFeignForm;
import com.cloudstar.rightcloud.common.constant.tag.TagCategoryConstant;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.component.tag.client.param.TagBindObjs;
import com.cloudstar.rightcloud.component.tag.client.param.TagBindParam;
import com.cloudstar.rightcloud.component.tag.utils.TagConvertUtil;
import com.cloudstar.rightcloud.data.util.IdWorker;
import com.cloudstar.rightcloud.resource.adapter.handler.AbstractCallbackHandler;
import com.cloudstar.rightcloud.resource.adapter.handler.ebs.ScanEbsResponseHandler;
import com.cloudstar.rightcloud.resource.client.bms.service.ResBmsService;
import com.cloudstar.rightcloud.resource.client.envs.service.ResCloudEnvService;
import com.cloudstar.rightcloud.resource.client.quotacontrol.param.CalculateQuotaParam;
import com.cloudstar.rightcloud.resource.client.quotacontrol.service.QuotaService;
import com.cloudstar.rightcloud.resource.common.adapter.annotation.HandlerTarget;
import com.cloudstar.rightcloud.resource.common.constant.quota.QuotaObjectType;
import com.cloudstar.rightcloud.resource.common.constant.vm.ServerType;
import com.cloudstar.rightcloud.resource.common.em.PlatformFlagEnum;
import com.cloudstar.rightcloud.resource.common.em.ResourceChangeEnum;
import com.cloudstar.rightcloud.resource.common.util.ChargeTimeUtil;
import com.cloudstar.rightcloud.resource.common.util.ip.IpUtil;
import com.cloudstar.rightcloud.resource.data.bms.dao.ResBmsDao;
import com.cloudstar.rightcloud.resource.data.bms.dto.ResBms;
import com.cloudstar.rightcloud.resource.data.bms.dto.ResBmsListDto;
import com.cloudstar.rightcloud.resource.data.envs.dto.ResCloudEnv;
import com.cloudstar.rightcloud.resource.data.host.dao.ResHostDao;
import com.cloudstar.rightcloud.resource.data.host.dto.ResHost;
import com.cloudstar.rightcloud.resource.data.host.dto.ResHostListDto;
import com.cloudstar.rightcloud.resource.data.image.dao.ResImageDao;
import com.cloudstar.rightcloud.resource.data.image.dto.ResImage;
import com.cloudstar.rightcloud.resource.data.keypair.dao.ResKeypairDao;
import com.cloudstar.rightcloud.resource.data.keypair.dto.ResKeypair;
import com.cloudstar.rightcloud.resource.data.keypair.dto.ResKeypairListQueryDto;
import com.cloudstar.rightcloud.resource.data.org.mapper.OrgMapper;
import com.cloudstar.rightcloud.resource.data.region.dao.ResRegionDao;
import com.cloudstar.rightcloud.resource.data.region.dto.ResRegion;
import com.cloudstar.rightcloud.resource.data.region.dto.ResRegionListDto;
import com.cloudstar.rightcloud.resource.data.securitygroup.dao.ResSecurityGroupDao;
import com.cloudstar.rightcloud.resource.data.securitygroup.dto.ResSecurityGroup;
import com.cloudstar.rightcloud.resource.data.securitygroup.dto.ResSecurityGroupListDto;
import com.cloudstar.rightcloud.resource.data.spectype.dao.ResSpecTypeAttrDao;
import com.cloudstar.rightcloud.resource.data.spectype.dao.ResSpecTypeDao;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeAttr;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeAttrListDto;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeListDto;
import com.cloudstar.rightcloud.resource.data.spectype.dto.ResSpecTypeListResultDto;
import com.cloudstar.rightcloud.resource.data.vd.dao.ResVdDao;
import com.cloudstar.rightcloud.resource.data.vd.dto.ResVd;
import com.cloudstar.rightcloud.resource.data.virtualcluster.dao.ResVirtualClusterDao;
import com.cloudstar.rightcloud.resource.data.virtualcluster.dto.ResVirtualCluster;
import com.cloudstar.rightcloud.resource.data.virtualcluster.dto.ResVirtualClusterListDto;
import com.cloudstar.rightcloud.resource.data.vmext.dao.ResVmExtDao;
import com.cloudstar.rightcloud.resource.data.zone.dao.ResZoneDao;
import com.cloudstar.rightcloud.resource.data.zone.dto.ResZone;
import com.cloudstar.rightcloud.resource.data.zone.dto.ResZoneDto;
import com.cloudstar.rightcloud.sdk.common.ResourceTypeCode;
import com.cloudstar.rightcloud.sdk.common.em.ChargeTypeEnum;
import com.cloudstar.rightcloud.sdk.common.em.ResourceDataSyncEventEnum;
import com.cloudstar.rightcloud.sdk.common.status.ResourceStatus;
import com.cloudstar.rightcloud.sdk.resource.bms.BmsDataSyncModel;
import com.cloudstar.rightcloud.sdk.resource.bms.BmsModel;
import com.cloudstar.rightcloud.sdk.resource.bms.BmsScanResponse;
import com.cloudstar.rightcloud.sdk.resource.common.model.ResourceActionResult;
import com.cloudstar.rightcloud.sdk.resource.ebs.EbsModel;
import com.cloudstar.rightcloud.sdk.resource.ebs.response.ScanEbsResponse;
import com.cloudstar.rightcloud.sdk.resource.securitygroup.SecurityGroupModel;
import com.cloudstar.rightcloud.sdk.resource.spectype.SpecTypeModel;
import com.cloudstar.rightcloud.sdk.resource.virtualnetwork.VirtualNetworkModel;
import com.cloudstar.rightcloud.utils.tasklog.TaskHelper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 裸金属同步
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Slf4j
@Service
@HandlerTarget(target = ResourceTypeCode.BMS + "scan")
public class BmsScanResponseHandler extends AbstractCallbackHandler {

    @Resource
    private ResSecurityGroupDao resSecurityGroupDao;
    @Resource
    private ResRegionDao resRegionDao;
    @Resource
    private ResZoneDao resZoneDao;
    @Resource
    private ResImageDao resImageDao;
    @Resource
    private ResHostDao resHostDao;
    @Resource
    private ResSpecTypeDao resSpecTypeDao;
    @Resource
    private ResSpecTypeAttrDao resSpecTypeAttrDao;
    @Resource
    private ResKeypairDao resKeypairDao;
    @Resource
    private ResVirtualClusterDao resVirtualClusterDao;
    @Resource
    private ResBmsDao resBmsDao;
    @Resource
    private ResVmExtDao resVmExtDao;
    @Resource
    private ResVdDao resVdDao;
    @Resource
    private SysTagClient sysTagClient;
    @Resource
    private ScanEbsResponseHandler scanEbsResponseHandler;
    @Resource
    private ResBmsService resBmsService;
    @Resource
    private ResCloudEnvService resCloudEnvService;
    @Resource
    private QuotaService quotaService;
    @Resource
    private OrgMapper orgMapper;

    @Override
    public void handler(ResourceActionResult result, boolean relaMaintain) {
        log.info("BmsScanResponseHandler-handler-envId:{}", result.getCloudEnvId());
        if (!result.getSuccess()) {
            errorHandler(result, relaMaintain);
            return;
        }
        ResCloudEnv cloudEnv = checkCloudEnvNormal(result.getCloudEnvId(), true);
        List<ResCloudEnv> bossManagedCloudEnv = getBossManagedCloudEnv(cloudEnv.getId());
        ResSecurityGroupListDto securityGroupListDto = new ResSecurityGroupListDto();
        securityGroupListDto.setCloudEnvIdIn(bossManagedCloudEnv.stream().map(ResCloudEnv::getId).collect(Collectors.toList()));
        Map<String, Long> sgUuid2Id = resSecurityGroupDao.selectList(securityGroupListDto).stream()
                                                         .collect(Collectors.toMap(ResSecurityGroup::getUuid, ResSecurityGroup::getId));
        // TODO 获取当前云环境是否是Admin类型,Admin类型更新数据时,不修改租户环境资源的云环境ID
        BmsScanResponse response = JSONUtil.toBean(JSONUtil.toJsonStr(result.getData()), BmsScanResponse.class);
        Set<String> uuids = response.getUuids();
        List<BmsModel> bmsModels = response.getBms();
        Map<String, BmsModel> uuid2BmsModel = bmsModels.stream().collect(Collectors.toMap(BmsModel::getUuid, o -> o));
        List<ResBms> resList = transformBmsModel(bmsModels, result);
        // 数据库数据与底层同步数据差异判断条件
        BiPredicate<ResBms, ResBms> matchPredicate = (res1, res2) -> Objects.equals(res1.getUuid(), res2.getUuid());
        BiPredicate<ResBms, ResBms> matchNamePredicate = (res1, res2) -> StrUtil.isBlank(res2.getUuid())
                && Objects.equals(res1.getName(), res2.getName());
        // 查询数据库中已纳管虚拟机
        List<ResBms> managedResList = getAdminAndTenantBms(result.getCloudEnvId());
        // 若当前云环境是租户云环境,将Admin同步入库的数据进行转移到当前环境
        if (!PlatformFlagEnum.isSupportAdmin(cloudEnv.getPlatformFlag())) {
            managedResList.forEach(resVm -> {
                if (uuids.contains(resVm.getUuid()) && !Objects.equals(resVm.getCloudEnvId(), result.getCloudEnvId())) {
                    resVm.setCloudEnvId(result.getCloudEnvId());
                }
            });
        }
        // 移除非当前云环境的数据,admin环境直接全部返回
        managedResList = managedResList.stream().filter(resVm -> PlatformFlagEnum.isSupportAdmin(cloudEnv.getPlatformFlag())
                || Objects.isNull(resVm.getUuid())
                || Objects.equals(resVm.getCloudEnvId(), result.getCloudEnvId())).collect(Collectors.toList());
        // 需要更新的数据
        List<ResBms> requireUpdateList = intersectionDbByPredicate(managedResList, resList, matchPredicate);
        // 需要插入的数据
        List<ResBms> requireInsertList = differenceByPredicate(resList, managedResList, matchPredicate.or(matchNamePredicate));
        // 需要删除的数据
        List<ResBms> requireDeleteList = differenceByPredicate(managedResList, resList, matchPredicate.or(matchNamePredicate));
        requireUpdateList = requireUpdateList.stream().filter(distinctByKey(ResBms::getUuid)).collect(Collectors.toList());
        log.info("All instances in the hypervisor[{}], total :[{}], added :[{}], updated :[{}], deleted :[{}]", result.getCloudEnvId(),
                 resList.size(), requireInsertList.size(), requireUpdateList.size(), requireDeleteList.size());
        // 实际更新到数据库的数据
        List<ResBms> updateResList = new ArrayList<>();
        //需要更新的uuid
        List<String> updateUuids = syscDate(result, requireUpdateList, requireInsertList, requireDeleteList, cloudEnv);
        if(Objects.nonNull(updateUuids) && updateUuids.size()>0){
            //只保留需要更新的数据
            requireUpdateList=requireUpdateList.stream().filter(resVm -> updateUuids.contains(resVm.getUuid())).collect(Collectors.toList());
            // 更新操作
            if (!CollectionUtil.isEmpty(requireUpdateList)) {
                List<ResBms> finalRequireDeleteList = requireDeleteList;
                requireUpdateList.stream()
                                 // 如果处于配置中，不做更新
                                 .filter(res -> !ResourceStatus.RESIZE.getCode().equals(res.getStatus())).forEach(managedRes -> {
                    // 避免重复数据问题
                    ResBmsListDto bmsListDto = new ResBmsListDto();
                    bmsListDto.setIdNotIn(Collections.singletonList(managedRes.getId()));
                    bmsListDto.setUuid(managedRes.getUuid());
                    List<ResBms> resBms = resBmsDao.list(bmsListDto);
                    finalRequireDeleteList.addAll(resBms);

                    ResBms updateRes = resList.stream()
                                              .filter(res -> matchPredicate.test(managedRes, res))
                                              .findFirst()
                                              .get();
                    updateRes.setId(managedRes.getId());

                    BeanUtil.copyProperties(updateRes, managedRes, CopyOptions.create().setIgnoreNullValue(true));
                    BmsModel bmsModel = uuid2BmsModel.get(updateRes.getUuid());
                    // 标签处理
                    buildTag(cloudEnv, managedRes, bmsModel);
                    updateDbBms(managedRes, cloudEnv);
                    pushResource(cloudEnv, ResourceTypeCode.ECS, Collections.singletonList(managedRes), ResourceChangeEnum.UPDATE);

                    // 更新网卡
                    List<SecurityGroupModel> securityGroups = bmsModel.getSecurityGroups();
                    List<Long> sgIds = new ArrayList<>();
                    if (CollUtil.isNotEmpty(securityGroups)) {
                        sgIds.addAll(securityGroups.stream()
                                                   .filter(sg -> sgUuid2Id.containsKey(sg.getUuid()))
                                                   .map(sg -> sgUuid2Id.get(sg.getUuid())).collect(Collectors.toList()));
                    }
                    dealVmSubnetPortRela(managedRes.getId(), cloudEnv.getId(), result.getOrgId(), bmsModel.getPorts(), sgIds);
                    dealBmsVmExtInfo(cloudEnv, updateRes, bmsModel);
                    if (Objects.nonNull(bmsModel.getVds())) {
                        // 更新磁盘 ScanVdResponseHandler.handler()
                        ResourceActionResult vdResult = BeanUtil.copyProperties(result, ResourceActionResult.class);
                        ScanEbsResponse scanVdResponse = new ScanEbsResponse();
                        scanVdResponse.setEbsModels(bmsModel.getVds());
                        scanVdResponse.setUuids(bmsModel.getVds().stream().map(EbsModel::getUuid).collect(Collectors.toSet()));
                        scanEbsResponseHandler.handler(vdResult, true);
                    }

                    updateResList.add(updateRes);
                });

                requireDeleteList.addAll(finalRequireDeleteList);
            }
        }


        // 删除操作
        if (!CollectionUtil.isEmpty(requireDeleteList)) {
            // 不是 admin 时，在支持 boss 关联的云环境中能够查询不属于自己租户的资源，删除操作需要剔除
            requireDeleteList = requireDeleteList.stream()
                                                 .filter(res -> result.getCloudEnvId().equals(res.getCloudEnvId())).distinct()
                                                 .collect(Collectors.toList());
            requireDeleteList.stream()
                             .filter(res -> !(Strings.isNullOrEmpty(res.getUuid()) && ResourceStatus.CREATING.getCode().equals(
                                     res.getStatus())))
                             .forEach(deleteRes -> {
                                 resBmsDao.deleteBatchIds(Collections.singletonList(deleteRes.getId()));
                                 resVmExtDao.deleteByResourceIds(Collections.singletonList(deleteRes.getId()), ResourceTypeCode.BMS);
                                 List<ResVd> resVds = resVdDao.selectByVmIds(Collections.singletonList(deleteRes.getId()));
                                 for (ResVd resVd : resVds) {
                                     LambdaUpdateWrapper<ResVd> updateWrapper = new LambdaUpdateWrapper<>();
                                     updateWrapper.eq(ResVd::getId, resVd.getId())
                                                  .set(ResVd::getVmId, null);
                                     resVdDao.update(resVd, updateWrapper);
                                 }
//                                 this.resBmsService.deleteBms(deleteRes.getId(), false,
//                                                            false, "", false, null);
                                 resCloudEnvService.pushDeleteResource(Collections.singletonList(deleteRes.getId()), cloudEnv.getId());
                             });

            if (CollUtil.isNotEmpty(requireDeleteList)) {
                List<String> resourceIds = requireDeleteList.stream()
                                                            .map(vm -> String.valueOf(vm.getId()))
                                                            .distinct()
                                                            .collect(Collectors.toList());
                SysTagUnbindObjsFeignForm tagUnbindObjsFeignForm = new SysTagUnbindObjsFeignForm();
                tagUnbindObjsFeignForm.setObjIds(resourceIds);
                sysTagClient.unbindTag(tagUnbindObjsFeignForm);
            }
        }
        if (!CollectionUtil.isEmpty(requireInsertList)) {
            int requireCount = requireInsertList.size();
            List<ResBms> allowInsertResVms = requireInsertList;
            allowInsertResVms
                    .forEach(insertRes -> {
                        insertRes.setId(IdWorker.generateId());
                        BmsModel bmsModel = uuid2BmsModel.get(insertRes.getUuid());
                        buildTag(cloudEnv, insertRes, bmsModel);
                        insertRes.setSyncStatus(insertRes.getStatus());
                        insertRes.setStatus(ResourceStatus.CREATING.getCode());
                        insertRes.setDataSource("2");
                        insertDbBms(insertRes, result, cloudEnv);
                        pushResource(cloudEnv, ResourceTypeCode.ECS, Collections.singletonList(insertRes), ResourceChangeEnum.CREATE);
                        dealBmsVmExtInfo(cloudEnv, insertRes, bmsModel);
                        List<Long> sgIds = new ArrayList<>();
                        if (CollUtil.isNotEmpty(bmsModel.getSecurityGroups())) {
                            // 更新网卡
                            sgIds.addAll(bmsModel.getSecurityGroups().stream()
                                                 .filter(sg -> sgUuid2Id.containsKey(sg.getUuid()))
                                                 .map(sg -> sgUuid2Id.get(sg.getUuid())).collect(Collectors.toList()));
                        }
                        dealVmSubnetPortRela(insertRes.getId(), cloudEnv.getId(), result.getOrgId(), bmsModel.getPorts(), sgIds);
                        if (Objects.nonNull(bmsModel.getVds())) {
                            ScanEbsResponse scanVdResponse = new ScanEbsResponse();
                            scanVdResponse.setUuids(bmsModel.getVds().stream().map(EbsModel::getUuid).collect(Collectors.toSet()));
                        }
                    });
        }

        // 同步配额
        if (Objects.nonNull(result.getOrgId())) {
            CalculateQuotaParam calculateQuotaParam = new CalculateQuotaParam();
            List<Long> orgIds = orgMapper.selectAllByOrgSid(result.getOrgId());
            ResBmsListDto resBmsListDto = new ResBmsListDto();
            resBmsListDto.setOrgIds(orgIds);
            resBmsListDto.setStatusNotIn(Arrays.asList(ResourceStatus.DELETED.getCode(), ResourceStatus.ERROR.getCode(),
                                                      ResourceStatus.CREATE_FAILURE.getCode()));
            calculateQuotaParam.setResource(resBmsDao.list(resBmsListDto));
            calculateQuotaParam.setResTypeCode(ResourceTypeCode.BMS);
            calculateQuotaParam.setObjectId(result.getOrgId());
            calculateQuotaParam.setObjectType(QuotaObjectType.ORG);
            quotaService.scanQuota(calculateQuotaParam);
        }



        // 任务认证
        TaskHelper.logInfo(result.getResTypeCode(), String.format("Cloud env (%s) Synchronous:"
                                                                          + " total:[%d], add:[%d], update:[%d], delete:[%d]",
                                                                  cloudEnv.getCloudEnvName(), resList.size(),
                                                                  requireInsertList.size(), requireUpdateList.size(), requireDeleteList.size()));
    }

    private List<ResBms> transformBmsModel(List<BmsModel> bmsModels, ResourceActionResult result) {
        Long cloudEnvId = result.getCloudEnvId();
        ResRegionListDto regionListDto = new ResRegionListDto();
        regionListDto.setCloudEnvId(cloudEnvId);
        final Map<String, Long> regionUuid2Id = resRegionDao.selectList(regionListDto).stream()
                                                            .collect(Collectors.toMap(ResRegion::getUuid, ResRegion::getId));
        ResZoneDto zoneListDto = new ResZoneDto();
        zoneListDto.setCloudEnvId(cloudEnvId);
        final Map<String, Long> zoneUuid2Id = resZoneDao.getZone(zoneListDto).stream()
                                                        .collect(Collectors.toMap(ResZone::getUuid, ResZone::getId, (k1, k2) -> k1));
        final Map<String, ResImage> imageUuid2ResImage = resImageDao.selectByCloudEnvId(cloudEnvId).stream()
                                                                    .filter(distinctByKey(ResImage::getUuid))
                                                                    .collect(Collectors.toMap(ResImage::getUuid, o -> o));
        ResHostListDto hostListDto = new ResHostListDto();
        final Map<String, Long> hostUuid2Id = resHostDao.selectList(hostListDto).stream()
                                                        .collect(Collectors.toMap(ResHost::getUuid, ResHost::getId, (k1, k2) -> k1));
        ResSpecTypeListDto vmTypeListDto = new ResSpecTypeListDto();
        vmTypeListDto.setCloudEnvId(cloudEnvId);
        final Map<String, Long> id2vmType = resSpecTypeDao.selectList(vmTypeListDto).stream()
                                                          .collect(Collectors.toMap(ResSpecTypeListResultDto::getCode, ResSpecTypeListResultDto::getId, (k1, k2) -> k1));
        Map<Long, List<ResSpecTypeAttr>> specTypeId2Attrs = resSpecTypeAttrDao.selectList(new ResSpecTypeAttrListDto()
                                                                                                  .setSpecTypeIds(new ArrayList<>(id2vmType.values()))).stream().collect(Collectors.groupingBy(ResSpecTypeAttr::getSpecTypeId));
        ResKeypairListQueryDto keypairListDto = new ResKeypairListQueryDto();
        keypairListDto.setCloudEnvId(cloudEnvId);
        Map<String, Long> keypairUuid2Id = resKeypairDao.selectList(keypairListDto).stream()
                                                        .collect(Collectors.toMap(ResKeypair::getUuid, ResKeypair::getId, (o1, o2) -> o1));
        ResVirtualClusterListDto virtualClusterListDto = new ResVirtualClusterListDto();
        virtualClusterListDto.setCloudEnvId(cloudEnvId);
        Map<String, Long> vcUuid2Id = resVirtualClusterDao.selectList(virtualClusterListDto).stream()
                                                          .collect(Collectors.toMap(ResVirtualCluster::getUuid, ResVirtualCluster::getId, (k1, k2) -> k1));
        return bmsModels.stream().map(bmsModel -> {
            ResBms resBms = BeanUtil.copyProperties(bmsModel, ResBms.class);
            if (!CollectionUtils.isEmpty(bmsModel.getPorts())) {
                List<String> ipList = bmsModel.getPorts()
                                              .stream()
                                              .map(VirtualNetworkModel::getIp)
                                              .filter(ip -> !Strings.isNullOrEmpty(ip))
                                              .collect(Collectors.toList());
                List<String> innerIpList = new ArrayList<>();
                for (String ip : ipList) {
                    Collections.addAll(innerIpList, ip.split(","));
                }
                // 处理ipv6
                List<String> ipv6List = bmsModel.getPorts()
                                                .stream()
                                                .map(VirtualNetworkModel::getIpv6)
                                                .filter(ipv6 -> !Strings.isNullOrEmpty(ipv6))
                                                .collect(Collectors.toList());
                for (String ipv6 : ipv6List) {
                    if (IpUtil.isIPv6Cidr(ipv6)) {
                        innerIpList.add(ipv6);
                    }
                }
                if (!innerIpList.isEmpty()) {
                    resBms.setInnerIp(Joiner.on(",").join(innerIpList));
                }
            }
            if (!Strings.isNullOrEmpty(bmsModel.getInnerIp())) {
                resBms.setInnerIp(bmsModel.getInnerIp());
            }
            if (Objects.nonNull(bmsModel.getElasticIp())) {
                resBms.setPublicIp(bmsModel.getElasticIp().getIp());
            }
            //======关联资源填充======//
            // 宿主机
            if (Objects.nonNull(bmsModel.getHost()) && StrUtil.isNotBlank(bmsModel.getHost().getUuid())) {
                resBms.setHostId(hostUuid2Id.get(bmsModel.getHost().getUuid()));
            }
            // 区域
            if (Objects.nonNull(bmsModel.getRegion()) && StrUtil.isNotBlank(bmsModel.getRegion().getUuid())) {
                resBms.setRegionId(regionUuid2Id.get(bmsModel.getRegion().getUuid()));
            }
            // 可用区
            if (Objects.nonNull(bmsModel.getZone()) && StrUtil.isNotBlank(bmsModel.getZone().getUuid())) {
                resBms.setZoneId(zoneUuid2Id.get(bmsModel.getZone().getUuid()));
            }
            // 密钥
            if (Objects.nonNull(bmsModel.getKeypair()) && StrUtil.isNotBlank(bmsModel.getKeypair().getUuid())) {
                resBms.setCredentialId(keypairUuid2Id.get(bmsModel.getKeypair().getUuid()));
            }
            // 集群
            if (Objects.nonNull(bmsModel.getCluster()) && StrUtil.isNotBlank(bmsModel.getCluster().getUuid())) {
                resBms.setVirtualClusterId(vcUuid2Id.get(bmsModel.getCluster().getUuid()));
            }
            // 镜像
            if (Objects.nonNull(bmsModel.getImage()) && StrUtil.isNotBlank(bmsModel.getImage().getUuid())) {
                ResImage resImage = imageUuid2ResImage.get(bmsModel.getImage().getUuid());
                if (Objects.nonNull(resImage)) {
                    resBms.setImageId(resImage.getId());
                    resBms.setOsType(resImage.getOsType());
                    resBms.setOsPlatform(resImage.getOsPlatform());
                    resBms.setOsVersion(resImage.getOsVersion());
                }
            }
            // 规格
            if (Objects.nonNull(bmsModel.getVmType()) && StrUtil.isNotBlank(bmsModel.getVmType().getCode())) {
                String key = bmsModel.getVmType().getCode();
                Long resSpecTypeId = id2vmType.get(key);
                resBms.setVmTypeId(resSpecTypeId);
                resBms.setVmTypeUuid(key);
                if (Objects.nonNull(resSpecTypeId)) {
                    Map<String, ResSpecTypeAttr> code2Obj = specTypeId2Attrs.get(resSpecTypeId).stream()
                                                                            .collect(Collectors.toMap(ResSpecTypeAttr::getAttrCode, o -> o));
                    if (CollUtil.isNotEmpty(code2Obj)) {
                        ResSpecTypeAttr cpu = code2Obj.get(SpecTypeModel.ECS_FLAVOR_CPU);
                        if (Objects.nonNull(cpu)) {
                            resBms.setCpu(Integer.parseInt(cpu.getAttrValue()));
                        }
                        ResSpecTypeAttr memory = code2Obj.get(SpecTypeModel.ECS_FLAVOR_MEMORY);
                        if (Objects.nonNull(memory)) {
                            resBms.setMemory(Integer.parseInt(memory.getAttrValue()));
                        }
                    }
                }
            }
            return resBms;
        }).collect(Collectors.toList());
    }

    private void buildTag(ResCloudEnv cloudEnv, ResBms insertRes, BmsModel bmsModel) {
        List<TagBindParam> bindTags = new ArrayList<>();
        if (CollUtil.isNotEmpty(bmsModel.getBindTags())) {
            // 标签处理
            bindTags = bmsModel.getBindTags().stream().map(tag -> {
                TagBindParam tagBindParam = BeanUtil.copyProperties(tag, TagBindParam.class);
                tagBindParam.setRgb(ColorUtil.toHex(ColorUtil.randomColor()));
                tagBindParam.setCategory(TagCategoryConstant.THIRD);
                return tagBindParam;
            }).collect(Collectors.toList());
        }
        List<TagBindObjs> tagBindObjs = TagConvertUtil.mergedTagList(cloudEnv.getId(), insertRes.getOrgId(),
                                                                     insertRes.getId().toString(), ResourceTypeCode.BMS, insertRes.getTag(), bindTags);
        List<TagBindObjsFeignForm> tagBindObjsFeignForms = BeanUtil.copyToList(tagBindObjs, TagBindObjsFeignForm.class);
        sysTagClient.bindTag(tagBindObjsFeignForms.toArray(new TagBindObjsFeignForm[0]));
        insertRes.setTag(TagConvertUtil.convertToString(BeanUtil.copyToList(tagBindObjs, TagBindParam.class)));
    }

    private void updateDbBms(ResBms managedRes, ResCloudEnv cloudEnv) {
        // 更新虚拟机数据
        managedRes.setId(managedRes.getId());
        if (Objects.isNull(managedRes.getStartTime())) {
            managedRes.setStartTime(Calendar.getInstance().getTime());
        }
        prepareUpdateCommonParams(managedRes, cloudEnv);
        // 计算资源创建时间，资源到期时间
        ChargeTimeUtil.prepareChargeTimeParams(managedRes, null);
        resBmsDao.updateById(managedRes);
    }

    private void insertDbBms(ResBms insertRes, ResourceActionResult result, ResCloudEnv cloudEnv) {
        insertRes.setCloudEnvId(result.getCloudEnvId());
        insertRes.setOrgId(result.getOrgId());
        if (Objects.isNull(insertRes.getStartTime())) {
            insertRes.setStartTime(Calendar.getInstance().getTime());
        }
        if (StrUtil.isBlank(insertRes.getServerType())) {
            insertRes.setServerType(ServerType.INSTANCE);
        }
        prepareInsertCommonParams(insertRes, cloudEnv);
        insertRes.setChargeType(Optional.ofNullable(queryChargeType("RS-BMS")).orElse(ChargeTypeEnum.POST_PAID.getCode()));
        insertRes.setOwnerAccount(cloudEnv.getCreatedBy());
        // 计算资源创建时间，资源到期时间
        ChargeTimeUtil.prepareChargeTimeParams(insertRes, null);
        resBmsDao.insert(insertRes);
    }


    /**
     * 反向纳管迁移
     * @param result
     * @param requireUpdateList
     * @param requireInsertList
     * @param requireDeleteList
     * @param cloudEnv
     * @return Boolean
     */
    public List<String> syscDate(ResourceActionResult result, List<ResBms> requireUpdateList,
                                 List<ResBms> requireInsertList, List<ResBms> requireDeleteList
            ,ResCloudEnv cloudEnv){
        Object originalData = result.getOriginalData();
        List<String> uuids=new ArrayList<>();
        if (originalData != null || CollectionUtil.isNotEmpty(requireDeleteList)) {
            List<ResBms> finalRequireUpdateList = requireUpdateList;
            List<ResBms> finalRequireDeleteList1 = requireDeleteList;
            List<Map> ecsOriginalData = JSONUtil.toList(JSONUtil.toJsonStr(originalData), Map.class);
            CompletableFuture.runAsync(() -> {
                if (!CollectionUtil.isEmpty(finalRequireUpdateList)) {
                    List<String> resUuid = finalRequireUpdateList.stream().filter(e -> e.getOrgId() - result.getOrgId() == 0)
                                                                 .map(ResBms::getUuid).collect(Collectors.toList());
                    List<Map> syncImageModel = ecsOriginalData.stream().filter(e -> resUuid.contains(e.get("id").toString())).collect(Collectors.toList());
                    List<BmsDataSyncModel> bmsDataSyncModels = new ArrayList<>();
                    BmsResDataSyncHandler.resDataConv(finalRequireUpdateList, syncImageModel, bmsDataSyncModels);
                    uuids.addAll(resServerDataSyncService.dataSync(ResourceTypeCode.BMS, result.getUserSid(), result.getOrgId(),
                                                      ResourceDataSyncEventEnum.UPDATE, JSON.toJSONString(bmsDataSyncModels, SerializerFeature.WriteMapNullValue),
                                                                   cloudEnv.getId()));
                }

                if (!CollectionUtil.isEmpty(finalRequireDeleteList1)) {
                    List<String> resUuid = finalRequireDeleteList1.stream().map(ResBms::getUuid).collect(Collectors.toList());
                    resServerDataSyncService.deleteDataSync(ResourceTypeCode.BMS, result.getUserSid(), result.getOrgId(),
                            resUuid, cloudEnv.getId());
                }

                if (!CollectionUtil.isEmpty(requireInsertList)) {
                    List<BmsDataSyncModel> bmsDataSyncModels = new ArrayList<>();
                    List<String> resUuid = requireInsertList.stream().map(ResBms::getUuid).collect(Collectors.toList());
                    List<Map> syncImageModel = ecsOriginalData.stream().filter(e -> resUuid.contains(e.get("id").toString())).collect(Collectors.toList());
                    BmsResDataSyncHandler.resDataConv(requireInsertList, syncImageModel, bmsDataSyncModels);
                    resServerDataSyncService.dataSync(ResourceTypeCode.BMS, result.getUserSid(), result.getOrgId(),
                                                      ResourceDataSyncEventEnum.CREATE, JSON.toJSONString(bmsDataSyncModels, SerializerFeature.WriteMapNullValue),
                                                      cloudEnv.getId());
                }
            });
        }
        return uuids;
    }

}
