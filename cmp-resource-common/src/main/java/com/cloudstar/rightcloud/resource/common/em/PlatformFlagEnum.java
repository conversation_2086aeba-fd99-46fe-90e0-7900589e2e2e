package com.cloudstar.rightcloud.resource.common.em;

import lombok.Getter;

/**
 * 管理侧/租户侧标记
 * admin/tenant
 *
 * <AUTHOR>
 * @since 2023/6/6 9:48
 */
@Getter
public enum PlatformFlagEnum {

    /**
     * 管理侧
     */
    ADMIN("admin", "管理侧"),

    /**
     * 租户侧
     */
    TENANT("tenant", "租户侧");

    private final String code;
    private final String name;

    PlatformFlagEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 判断是不是管理侧
     *
     * @param platformFlag 管理侧租户侧标记
     * @return boolean
     */
    public static boolean isSupportAdmin(String platformFlag) {
        return ADMIN.code.equals(platformFlag);
    }

}
