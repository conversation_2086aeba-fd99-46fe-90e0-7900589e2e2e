package cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MinioParam {

    /**
     * 桶名
     */
    String bucketName;

    /**
     * 只写前缀
     */
    List<String> writeOnly;

    /**
     * 只读前缀
     */
    List<String> readOnly;


    /**
     * 可读可写前缀
     */
    List<String> readAndWrite;

}

