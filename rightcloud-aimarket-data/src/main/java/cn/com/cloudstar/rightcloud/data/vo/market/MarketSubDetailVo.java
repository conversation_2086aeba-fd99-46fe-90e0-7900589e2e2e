package cn.com.cloudstar.rightcloud.data.vo.market;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订阅详情签证官
 *
 * <AUTHOR>
 * @date 2023/08/18
 */
@Data
public class MarketSubDetailVo {

    /**
     * 订阅id
     */
    private String subscribeId;

    /**
     * 商品id
     */
    private String shopId;

    /**
     * 商品标题
     */
    private String shopTitle;

    /**
     * 商品类型
     */
    private String shopType;

    /**
     * 供应商名称
     */
    private String orgName;

    /**
     * 购买方名称
     */
    private String custOrgName;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 资金监管状态
     */
    private String superviseStatus;

    /**
     * 结算状态
     */
    private String settlementStatus;

    /**
     * 规格名称
     */
    private String unit;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 完成时间
     */
    private String completeDt;

    /**
     * 商品分类
     */
    private String categoryName;

    /**
     * 订阅人
     */
    private Long ownerId;

    /**
     * 商品所有人
     */
    private Long shopOwnerId;

}
