# AiModel产品定时计费出账实现

## 概述

本实现为AiModel产品增加了定时计费出账功能，从MongoDB中获取话单数据，通过对数据的统计进行计费扣费，出账时按Token套餐包>余额的顺序进行费用抵扣，对余额不足的情况标记欠费。

## 实现架构

### 1. 数据模型

#### AiModelCollector (话单数据实体)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/bss/module/bill/pojo/entity/AiModelCollector.java`
- **MongoDB集合**: `hws_sdr_aimodel_data`
- **主要字段**:
  - `tokenUsage`: Token消耗量
  - `modelName`: 模型名称
  - `modelVersion`: 模型版本
  - `requestType`: 请求类型
  - `inputTokens`: 输入Token数量
  - `outputTokens`: 输出Token数量

#### AiModelCollectorArchived (话单归档实体)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/bss/module/bill/pojo/entity/AiModelCollectorArchived.java`
- **MongoDB集合**: `hws_sdr_aimodel_data_archived`
- **用途**: 存储已处理的话单数据

### 2. 服务层

#### IAiModelGaapCostService (计费服务接口)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/bss/module/bill/service/IAiModelGaapCostService.java`
- **主要方法**: `handleAiModelBill()` - 处理AiModel话单计费

#### AiModelGaapCostServiceImpl (计费服务实现)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/bss/module/bill/service/impl/AiModelGaapCostServiceImpl.java`
- **核心功能**:
  - Token费用计算
  - 创建账单记录
  - 费用扣减处理（Token套餐包 > 余额 > 欠费标记）

### 3. 定时任务

#### CalculateAiModelBillsTask (计费任务执行器)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/bss/module/bill/task/CalculateAiModelBillsTask.java`
- **功能**:
  - 从MongoDB查询AiModel话单
  - 调用计费服务处理账单
  - 保存收支明细和账单记录
  - 归档已处理话单

#### AiModelBillsGenerateTask (Quartz调度器)
- **路径**: `src/main/java/cn/com/cloudstar/rightcloud/schedule/system/task/analysis/AiModelBillsGenerateTask.java`
- **调度频率**: 每15分钟执行一次 (`0 0/15 * * * ?`)

### 4. 配置更新

#### 枚举类型扩展
- **SdrColectorTypeEnum**: 添加了 `AIMODEL(5, "AiModel", "AI大模型服务")`
- **CalculateSdrBillsTask**: 更新支持类型5（AiModel）

#### 数据库配置
- **路径**: `src/main/resources/db/migrations/bss/V1_1_1636__aimodel_billing_task.sql`
- **内容**: 添加AiModel计费定时任务配置

## 计费流程

### 1. 数据采集
- AiModel话单数据存储在MongoDB的 `hws_sdr_aimodel_data` 集合中
- 包含Token使用量、模型信息等关键计费数据

### 2. 定时计费
- 每15分钟执行一次定时任务
- 查询未处理的AiModel话单数据
- 按话单逐条进行计费处理

### 3. 费用计算
- 根据Token使用量计算费用
- 当前实现：每个Token 0.00001元（可配置）

### 4. 费用扣减顺序
1. **Token套餐包**: 优先使用用户的Token套餐包余量
2. **账户余额**: 套餐包不足时使用账户余额
3. **欠费标记**: 余额不足时标记为欠费状态

### 5. 数据归档
- 已处理的话单移动到归档集合 `hws_sdr_aimodel_data_archived`
- 删除原始话单数据，避免重复处理

## TODO项目

### 1. Token套餐包扣费逻辑
```java
// 位置: AiModelGaapCostServiceImpl.deductFromTokenPackage()
// 需要实现:
// - 查询用户可用的Token套餐包
// - 按优先级扣减Token套餐包余量
// - 更新套餐包使用记录
```

### 2. 欠费标记逻辑
```java
// 位置: AiModelGaapCostServiceImpl.markAsArrears()
// 需要实现:
// - 更新用户欠费状态
// - 发送欠费通知
// - 记录欠费历史
```

### 3. SdrTimeRecord扩展
```java
// 需要在SdrTimeRecord类中添加:
// private Long aiModelRecord;
// 用于记录AiModel话单的处理时间戳
```

### 4. 增量查询优化
```java
// 位置: CalculateAiModelBillsTask.getAiModelCollectorOnMongo()
// 需要实现:
// - 基于时间戳的增量查询
// - 避免重复处理相同话单
```

### 5. 费用计算规则配置化
- 将Token单价配置化，支持不同模型不同价格
- 支持阶梯计费、批量折扣等复杂计费规则

## 部署说明

### 1. 数据库迁移
执行SQL脚本添加定时任务配置：
```sql
-- 文件: V1_1_1636__aimodel_billing_task.sql
```

### 2. 应用部署
- 确保所有新增的Java类已编译打包
- 重启应用服务器以加载新的定时任务

### 3. 监控验证
- 检查定时任务是否正常调度
- 监控MongoDB中话单数据的处理情况
- 验证账单生成和费用扣减是否正确

## 扩展性考虑

### 1. 多模型支持
- 当前架构支持扩展到不同的AI模型
- 可通过模型名称和版本区分不同的计费规则

### 2. 计费规则灵活性
- 支持按输入/输出Token分别计费
- 支持按请求类型差异化计费

### 3. 性能优化
- 支持批量处理话单数据
- 支持分布式处理大量话单

## 注意事项

1. **事务处理**: 计费过程使用事务确保数据一致性
2. **并发控制**: 使用Redis锁防止重复计费
3. **错误处理**: 单条话单处理失败不影响其他话单
4. **日志记录**: 详细记录计费过程便于问题排查
5. **数据备份**: 话单归档前确保数据完整性
