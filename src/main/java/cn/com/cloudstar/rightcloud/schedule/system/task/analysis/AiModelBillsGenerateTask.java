/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.schedule.system.task.analysis;

import cn.com.cloudstar.rightcloud.bss.module.bill.task.CalculateAiModelBillsTask;
import cn.com.cloudstar.rightcloud.schedule.system.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName: AiModelBillsGenerateTask.java
 * @Description： AiModel计费账单定时任务调度器
 * @Author: system
 * @Date: 2025/01/21
 * @Version: 1.0.0
 **/
@Component
@Slf4j
public class AiModelBillsGenerateTask extends BaseTask {
    
    @Autowired
    private CalculateAiModelBillsTask calculateAiModelBillsTask;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("AiModel计费账单定时任务开始执行");
        try {
            calculateAiModelBillsTask.calculateAiModelBills();
            log.info("AiModel计费账单定时任务执行完成");
        } catch (Exception e) {
            log.error("AiModel计费账单定时任务执行失败", e);
            throw new JobExecutionException("AiModel计费账单定时任务执行失败", e);
        }
    }
}
