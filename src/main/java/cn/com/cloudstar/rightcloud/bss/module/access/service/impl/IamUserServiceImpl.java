package cn.com.cloudstar.rightcloud.bss.module.access.service.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamSubUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUsersListResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IamUserService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.CloudClientFactory;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResUser;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.user.ResUserCredentialRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.user.ResUserRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-09-21 18:29
 * @Desc iam 用户相关操作
 */
@Service
@Slf4j
public class IamUserServiceImpl implements IamUserService {

    /**
     * 华为侧IAM子用户名
     */
    private static final String IAM_SUBUSER_NAME = "bss_";

    /**
     * 华为侧IAM子用户名
     */
    private static final String IAM_USER_NAME = "Modelarts_user";

    /**
     * 华为侧IAM用户组名
     */
    private static final String IAM_GROUP_NAME = "Bss_For_Modelarts";

    private static final String TENANT_IDP = "TenantIdp";

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @DubboReference
    private ResUserRemoteService resUserRemoteService;

    @DubboReference
    private ResUserCredentialRemoteService resUserCredentialRemoteService;

    @DubboReference
    private IamRemoteService iamRemoteService;

    @Autowired
    private UserMapper userMapper;


    @Override
    public boolean resUserCreate(Long userSid, String account, Long orgSid) {
        // 判断是否达到上限
        int subUserLimit = this.checkCreateIamSubUserLimit();
        if (subUserLimit > 0) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria resUserCri = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
            resUserCri.put("orgSidAll", orgSid);
            int count = resUserRemoteService.countByParams(resUserCri);
            // 加一个HCSO账户
            count = count + 1;
            if (count >= subUserLimit) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_239284543));
            }
        }
        Long envId = cloudEnvId();
        try {
            // 创建IAM子用户
            IamSubUserResult subUserResult = createSubUser(envId, userSid, account);
            if (!subUserResult.isSuccess()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_743862339));
            }
            String domainId = subUserResult.getDomainId();
            String subUserId = subUserResult.getId();

            // 检查用户组是否存在
            IamUserGroupResult userGroup = getIamUserGroup(envId, IAM_GROUP_NAME, domainId);
            if (!userGroup.isSuccess() || !userGroup.isFlg()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_368204001));
            }

            // 配置子用户到用户组
            BaseResult subUserToGroup = addSubUserToGroup(envId, subUserId, userGroup.getId());
            if (!subUserToGroup.isSuccess()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1668342964));
            }

            // 导入资源用户表
            ResUser resUser = new ResUser();
            WebUserUtil.prepareInsertParams(resUser);
            resUser.setUuid(subUserId);
            resUser.setName(subUserResult.getName());
            resUser.setStatus(true);
            resUser.setDomainId(domainId);
            resUser.setDescription("Modelarts子用户");
            resUser.setCloudEnvId(envId);
            resUser.setOrgSid(orgSid);
            resUser.setUserSid(userSid);
            if (resUserRemoteService.insertSelective(resUser) < 1) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1390099129));
            }

        } catch (Exception e) {
            boolean deleteSucces = deleteSubUser(envId, account,userSid);
            if (deleteSucces) {
                log.info("创建子用户-映射IAM用户[{}]-华为IAM子用户回滚成功！！",account);
            }
            throw e;
        }
        return true;
    }

    public List<IamUserResult> getIamUserInfo(Long envId, String name) {
        List<IamUserResult> result = Lists.newArrayList();
        IamUsersListGet usersListGet = CloudClientFactory.buildMQBean(envId, IamUsersListGet.class);
        if (StringUtils.isNotEmpty(name)) {
            usersListGet.setName(name);
        }
        try {
            IamUsersListResult usersListResult = (IamUsersListResult) MQHelper.rpc(usersListGet);
            if (Objects.nonNull(usersListResult) && Objects.nonNull(usersListResult.getUsers())) {
                // 默认第一个用户是管理员用户
                result = BeanConvertUtil.convert(usersListResult.getUsers(), IamUserResult.class);
            }
        } catch (MQException e) {
            log.error("华为IAM项目服务中的用户列表查询失败[{}]", e.getMessage());
        }
        return result;
    }

    /**
     * 环境ID取得
     */
    private Long cloudEnvId() {
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        return Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
    }

    /**
     * IAM管理员创建IAM子用户
     */
    @Override
    public IamSubUserResult createSubUser(Long envId, Long userSid, String name) {
        if (envId == null) {
            envId = cloudEnvId();
        }

        IamSubUserResult result = new IamSubUserResult();
        IamSubUserCreate param = CloudClientFactory.buildMQBean(envId, IamSubUserCreate.class);
        param.setName(getIamUserName(name));
        param.setDomainId(param.getDomain());
        if (checkCreateIamSubUser()) {
            String keycloakUserId = iamRemoteService.findKeycloakUserId(userSid);
            param.setXuserId(keycloakUserId);
            //xuser_type取值当前仅支持TenantIdp。见华为云API文档
            param.setXuserType(TENANT_IDP);
        }
        param.setDescription("ModelArts子用户");

        try {
            result = (IamSubUserResult) MQHelper.rpc(param);
            log.info("IamUserServiceImpl.createSubUser IAM管理员创建IAM子用户返回参数: {}", JSONUtil.toJsonStr(result));
        } catch (MQException e) {
            log.error("IamUserServiceImpl.createSubUser 创建IAM子用户[{}]", e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }


    @Override
    public IamUserGroupResult getIamUserGroup(Long envId, String groupName, String domainId) {
        if (envId == null) {
            envId = cloudEnvId();
        }

        IamUserGroupResult iamUserGroupResult = new IamUserGroupResult();
        IamUserGroupGet iamUserGroupGet = CloudClientFactory.buildMQBean(envId, IamUserGroupGet.class);
        iamUserGroupGet.setName(groupName);
        iamUserGroupGet.setDomainId(domainId);

        try {
            log.info("IamUserServiceImpl.getIamUserGroup 查询华为IAM用户组详情开始");
            iamUserGroupResult = (IamUserGroupResult) MQHelper.rpc(iamUserGroupGet);
            log.info("IamUserServiceImpl.getIamUserGroup 查询华为IAM用户组详情结束 result: {}", JSONUtil.toJsonStr(iamUserGroupResult));
        } catch (MQException e) {
            log.error("IamUserServiceImpl.getIamUserGroup查询华为IAM用户组详情失败[{}]", e.getMessage());
            iamUserGroupResult.setSuccess(false);
        }
        log.info("IamUserServiceImpl.getIamUserGroup 查询华为IAM用户组详情成功！");
        return iamUserGroupResult;
    }

    @Override
    public BaseResult addSubUserToGroup(Long envId, String userId, String groupId) {
        if (envId == null) {
            envId = cloudEnvId();
        }

        BaseResult result = new BaseResult();
        IamSubUserAddToGroup param = CloudClientFactory.buildMQBean(envId, IamSubUserAddToGroup.class);
        param.setUserId(userId);
        param.setGroupId(groupId);

        try {
            result = (BaseResult) MQHelper.rpc(param);
            log.info("IamUserServiceImpl.getIamUserGroup 添加IAM用户到用户组结束 result: {}", JSONUtil.toJsonStr(result));
        } catch (MQException e) {
            log.error("IamUserServiceImpl.addSubUserToGroup 添加IAM用户到用户组[{}]", e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }


    /**
     * 获取IAM子用户用户名字
     * @return
     */
    private String getIamUserName(String account) {
        return checkCreateIamSubUser() ? IAM_SUBUSER_NAME + account : IAM_USER_NAME;
    }

    /**
     * 是否创建IAM子用户
     * @return
     */
    @Override
    public boolean checkCreateIamSubUser() {
        String createIamUserFlag = PropertiesUtil.getProperty(SysConfigConstants.IAM_CREATE_USER_FLAG);
        log.info("IamUserServiceImpl.checkCreateIamSubUser createIamUserFlag:【{}】", createIamUserFlag);

        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        if (CollectionUtil.isEmpty(cloudEnvs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.CLOUD_ENV_NOT_EXIST));
        }

        return BooleanUtil.toBoolean(createIamUserFlag) && CloudEnvType.HCSO.equals(cloudEnvs.get(0).getCloudEnvType());
    }

    /**
     * IAM子用户配额 0表示不限制
     * @return 数量
     */
    private int checkCreateIamSubUserLimit() {
        int num = 0;
        String createIamUserLimit= PropertiesUtil.getProperty(SysConfigConstants.IAM_CREATE_USER_LIMIT);
        if (StringUtils.isNotBlank(createIamUserLimit)) {
            num = Integer.parseInt(createIamUserLimit);
        }

        log.info("IamUserServiceImpl.checkCreateIamSubUser createIamUserLimit:【{}】", createIamUserLimit);
        return num;
    }

    @Override
    public List<IamUserResult> getIamUserInfo(Long envId) {
        if (envId == null) {
            envId = cloudEnvId();
        }

        List<IamUserResult> result = Lists.newArrayList();
        IamUsersListGet usersListGet = CloudClientFactory.buildMQBean(envId, IamUsersListGet.class);
        try {
            IamUsersListResult usersListResult = (IamUsersListResult) MQHelper.rpc(usersListGet);
            if (Objects.nonNull(usersListResult) && Objects.nonNull(usersListResult.getUsers())) {
                // 默认第一个用户是管理员用户
                result = BeanConvertUtil.convert(usersListResult.getUsers(), IamUserResult.class);
            }
        } catch (MQException e) {
            log.error("IamUserServiceImpl.checkCreateIamSubUser getIamUserInfo: 华为IAM项目服务中的用户列表查询失败[{}]", e.getMessage());
        }
        return result;
    }

    @Override
    public boolean deleteSubUser(Long envId, String account, Long userSid) {
        log.info("IamUserServiceImpl-deleteSubUser account: {}", account);
        if (envId == null) {
            envId = cloudEnvId();
        }

        try {
            // 删除 res_user
            cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
            criteria.put("userSid", userSid);
            resUserRemoteService.deleteByParams(criteria);

            // 删除访问密钥
            resUserCredentialRemoteService.deleteByParams(criteria);
        } catch (Exception e) {
            log.error("IamUserServiceImpl.deleteSubUser: 删除子用户res_user失败 error[{}]", e.getMessage());
        }

        boolean flag = false;
        List<IamUserResult> userList =  getIamUserInfo(envId);
        if (CollectionUtil.isEmpty(userList)) {
            log.error("IamUserServiceImpl.deleteSubUser-{}： 没有可删除的iam子用户", account);
           return false;
        }
        String iamUserName = getIamUserName(account);
        for (IamUserResult userInfo : userList) {
            if (Objects.equals(userInfo.getName(), iamUserName)) {
                BaseResult result = new BaseResult();
                IamSubUserDelete param = CloudClientFactory.buildMQBean(envId, IamSubUserDelete.class);
                String iamUserId = userInfo.getId();
                param.setUserId(iamUserId);

                int count = 3;
                while (count > 0) {
                    count--;
                    try {
                        result = (BaseResult) MQHelper.rpc(param);
                        flag = result.isSuccess();
                        if (flag) {
                            break;
                        }
                    } catch (MQException e) {
                        log.error("IamUserServiceImpl.deleteSubUser-"+account+"： 删除IAM子用户error[{}]", e.getMessage());
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1583267028));
                    }
                }
            }
        }

        return flag;
    }

    @Override
    public boolean updateSubUserEnabled(Long userId, Long orgSid, boolean enabled) {
        // 获取IAM子账号ID
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria.put("userSid", userId);
        List<ResUser> resUsers = resUserRemoteService.selectByParams(criteria);
        if (CollectionUtil.isEmpty(resUsers) || Objects.isNull(resUsers.get(0))) {
            criteria.clear();
            criteria.put("orgSid", orgSid);
            resUsers = resUserRemoteService.selectByParams(criteria);
        }

        if (CollectionUtil.isEmpty(resUsers) || Objects.isNull(resUsers.get(0))) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_242166146));
        }

        IamSubUserUpdate userUpdate = CloudClientFactory.buildMQBean(cloudEnvId(), IamSubUserUpdate.class);
        userUpdate.setUserId(resUsers.get(0).getUuid());
        userUpdate.setEnabled(enabled);

        BaseResult result = new BaseResult();
        try {
            result = (BaseResult)MQHelper.rpc(userUpdate);
        } catch (MQException e) {
            log.error("IamUserServiceImpl.updateSubUserEnabled： 修改IAM子用户信息失败 error[{}]", e.getMessage());
            result.setSuccess(false);
        }

        return result.isSuccess();
    }
}
