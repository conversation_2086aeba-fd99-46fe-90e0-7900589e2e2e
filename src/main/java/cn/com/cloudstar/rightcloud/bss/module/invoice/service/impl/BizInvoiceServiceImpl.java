/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceMethodEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PayTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PriceTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.SysBssEntityMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCostSummary;
import cn.com.cloudstar.rightcloud.bss.module.invoice.common.InvoiceUtil;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceDepositeMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceSettingMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeDepositWithResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.message.service.ISysMsgService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * <p>
 * 运营_发票表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */

@Service
@Slf4j
public class BizInvoiceServiceImpl extends ServiceImpl<BizInvoiceMapper, BizInvoice> implements IBizInvoiceService {
    private static final String ACCOUNT_ID = "ACCOUNT_ID";
    private static final String INVOICE_ID = "INVOICE_SID";
    private static final String DEPOSITE_SID = "DEPOSITE_SID";
    private static final String PERSONAL_INVOICE_TYPE = "0";
    private static final String  BILL_ING_CYCLE= "billingCycle";



    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private BizInvoiceDepositeMapper bizInvoiceDepositeMapper;
    @Autowired
    private BizInvoiceSettingMapper bizInvoiceSettingMapper;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private SysBssEntityMapper sysBssEntityMapper;

    @Autowired
    private ISysMsgService iSysMsgService;
    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private OrgService orgService;


    @Override
    public List<InstanceGaapCostVO> selectDepositWithResource(DescribeDepositWithResourceRequest request) {
        ArrayList<InstanceGaapCostVO> instances = new ArrayList<>();
        Criteria criteria = new Criteria();
        Query query = new Query();

        Sort commonSort = this.getCommonSort(criteria);
        query.with(commonSort);
        org.springframework.data.mongodb.core.query.Criteria c1 =
                org.springframework.data.mongodb.core.query.Criteria.where("ownerId").is(Long.parseLong(request.getAccountId().toString()));
        org.springframework.data.mongodb.core.query.Criteria c2 =
                org.springframework.data.mongodb.core.query.Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.PENDING.getCode());
        org.springframework.data.mongodb.core.query.Criteria c3 =
                org.springframework.data.mongodb.core.query.Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.DONE.getCode());
        query.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(c1,c2,c3));
        query.with(Sort.by(Sort.Direction.ASC, "payTime"));
        List<BillBillingCycleCost> billBillingCycleCosts = mongoTemplate.find(query, BillBillingCycleCost.class,"biz_bill_billing_cycle");

        if (CollectionUtils.isEmpty(billBillingCycleCosts)) {
            return instances;
        }

        List<String> billBillingCycleIdList = billBillingCycleCosts.stream().map(BillBillingCycleCost::getId).map(String::valueOf).collect(Collectors.toList());
        Query instanceQuery = new Query();
        org.springframework.data.mongodb.core.query.Criteria costQuery = org.springframework.data.mongodb.core.query.Criteria.where("billBillingCycleId").in(billBillingCycleIdList);
        instanceQuery.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(costQuery));
        List<InstanceGaapCost> costs = mongoTemplate.find(instanceQuery, InstanceGaapCost.class, "biz_bill_usage_item");
        Map<String, List<InstanceGaapCost>> cycleIdAndOrderSourceSnMap = costs.stream().collect(Collectors.groupingBy(InstanceGaapCost::getBillBillingCycleId));
        billBillingCycleCosts.forEach(e -> {
            List<InstanceGaapCost> instanceGaapCosts = cycleIdAndOrderSourceSnMap.get(e.getId().toString());
            if (CollectionUtils.isEmpty(instanceGaapCosts)) {
                return;
            }
            BigDecimal rechargeCreditAmount = instanceGaapCosts.stream().map(InstanceGaapCost::getRechargeCreditAmount).filter(rechargeCredit -> !Objects.isNull(rechargeCredit)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal couponAmount = instanceGaapCosts.stream().map(InstanceGaapCost::getCouponAmount).filter(coupon -> !Objects.isNull(coupon)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            e.setOrderSourceSn(instanceGaapCosts.get(0).getOrderSourceSn());
            e.setCouponAmount(couponAmount);
            BigDecimal creditAmount = e.getCreditAmount();
            if (Objects.isNull(creditAmount)) {
                creditAmount = BigDecimal.ZERO;
            }
            e.setCreditAmount(creditAmount.add(rechargeCreditAmount));
            e.setInvoiceAmount(e.getInvoiceAmount().subtract(rechargeCreditAmount));
            e.setInstanceName(instanceGaapCosts.get(0).getInstanceName());
            e.setPriceTypeName(PriceTypeEnum.codeFromName(e.getPriceType()));
        });

        Map<String, List<BillBillingCycleCost>> instanceCostsGroup = billBillingCycleCosts.stream().filter(c-> c.getOrderSourceSn() != null).collect(Collectors.groupingBy(BillBillingCycleCost::getOrderSourceSn));

        for (Entry<String, List<BillBillingCycleCost>> cycleCosts : instanceCostsGroup.entrySet()) {
            List<BillBillingCycleCost> cycleCost = cycleCosts.getValue();
            InstanceGaapCostVO instance = BeanConvertUtil.convert(cycleCost.get(0), InstanceGaapCostVO.class);
            Date startTime = cycleCost.stream().map(BillBillingCycleCost::getBillStartTime).min(Comparator.comparing(Date::getTime)).get();
            Date endTime = cycleCost.stream().map(BillBillingCycleCost::getBillEndTime).max(Comparator.comparing(Date::getTime)).get();

            BigDecimal cashAmount = cycleCost.stream().map(BillBillingCycleCost::getCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal creditAmount = cycleCost.stream().map(BillBillingCycleCost::getCreditAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal deductCouponDiscount = cycleCost.stream().map(BillBillingCycleCost::getDeductCouponDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal couponAmount = cycleCost.stream().map(BillBillingCycleCost::getCouponAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal invoiceAmount = cycleCost.stream().map(BillBillingCycleCost::getInvoiceAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<String> cycleIds = cycleCost.stream().map(e -> e.getId().toString()).collect(Collectors.toList());

            instance.setBillStartTime(startTime);
            instance.setBillEndTime(endTime);
            instance.setCashAmount(cashAmount);
            instance.setCreditAmount(creditAmount);
            instance.setDeductCouponDiscount(deductCouponDiscount);
            instance.setCouponAmount(couponAmount);
            instance.setInvoiceAmount(invoiceAmount);
            instance.setCycleIds(cycleIds);
            instances.add(instance);
        }
        return instances;
    }

    /**
     *  计算退款金额
     * @param cost 周期账单数据
     * @param resourceUseAmount 资源使用金额（不包含HPC扩容，因为里面的数据是通过资源查询的，HPC扩容数据中无资源名称）
     * @return 退款金额
     */
    private BigDecimal getReleaseAmount(BillBillingCycleCost cost, Map<String, BigDecimal> resourceUseAmount, Map<String, String> cycIdToApplyOrderId) {
        // 获取使用金额
        BigDecimal releaseAmount = BigDecimal.ZERO;
        String instanceName = cost.getInstanceName();
        BigDecimal amount = cost.getAmount();
        String productCode = cost.getProductCode();
        String applyTime = cycIdToApplyOrderId.get(cost.getId().toString());
        String key = instanceName + "_" + productCode + "_" + applyTime;
        BigDecimal useAmount = resourceUseAmount.get(key);
        if (useAmount == null) {

            return releaseAmount;
        }

        if (useAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return amount.negate();
        }

        BigDecimal diffAmount = useAmount.subtract(amount);
        if (diffAmount.compareTo(BigDecimal.ZERO) >= 0) {
            resourceUseAmount.put(key, diffAmount);
        } else {
            diffAmount = amount.subtract(useAmount);
            releaseAmount = diffAmount.negate();
            resourceUseAmount.put(key, BigDecimal.ZERO);
        }

        return releaseAmount;
    }



    private List<BillBillingCycleCost> getBillBillingCycleCosts(List<BillBillingCycleCost> billBillingCycleCosts,boolean checkInvoicable) {

        List<String> cycleIds = new ArrayList<>();
        billBillingCycleCosts.forEach(cost ->{
            cycleIds.add(cost.getId().toString());
        });

        List<InstanceGaapCost> list = null;
        if(!CollectionUtils.isEmpty(cycleIds) && cycleIds.size()>0){
            Query lineQuery = new Query();
            org.springframework.data.mongodb.core.query.Criteria cl =
                    org.springframework.data.mongodb.core.query.Criteria.where("billBillingCycleId").in(cycleIds);
            lineQuery.addCriteria(cl);
            list = mongoTemplate.find(lineQuery, InstanceGaapCost.class);
        }
        Set<String> orderIds = new HashSet<>();
        Set<String> orderReneIds = new HashSet<>();
        if(!CollectionUtils.isEmpty(list) ){
            Iterator<BillBillingCycleCost> it = billBillingCycleCosts.iterator();
            while(it.hasNext()){
                BillBillingCycleCost next = it.next();
                Set<String> cycleIdSet = list.stream().filter(cos -> !"release".equalsIgnoreCase(cos.getType())).map(InstanceGaapCost::getBillBillingCycleId).collect(Collectors.toSet());

                Map<String, List<InstanceGaapCost>> collect = list.stream().collect(Collectors.groupingBy(InstanceGaapCost::getBillBillingCycleId));

                List<InstanceGaapCost> costlist = collect.get(next.getId().toString());
                if(!CollectionUtil.isEmpty(costlist)){
                    for (InstanceGaapCost cos : costlist) {
                        if (checkInvoicable && cos.getInvoicable() != null ) {
                            if (BooleanEnum.NO.getCode().equals(cos.getInvoicable())) {
                                it.remove();
                                break;
                            };
                        }
                        if(Objects.nonNull(cos.getType()) &&
                                OrderType.RELEASE.equals(cos.getType()) && cycleIdSet.contains(cos.getBillBillingCycleId())){
                            BigDecimal rechargeCreditAmount = cos.getRechargeCreditAmount();
                            if (rechargeCreditAmount == null) {
                                rechargeCreditAmount = BigDecimal.ZERO;
                            }
                            BigDecimal usableAmount = next.getInvoiceAmount().add((cos.getCashAmount()))
                                    .subtract(rechargeCreditAmount);
                            int i = (next.getInvoiceAmount().add(cos.getCashAmount())).compareTo(BigDecimal.ZERO);
                            if(Objects.nonNull(cos.getOrderStatus())
                                    && OrderStatus.RELEASE_SUCCESS.equals(cos.getOrderStatus())){
                                if(0 == i){
                                    try {
                                        it.remove();
                                        break;
                                    } catch (Exception e) {
                                        log.error(e.getMessage());

                                    }
                                }else{
                                    next.setInvoiceAmount(BigDecimalUtil.scaleAndRoundHalfUp(usableAmount));
                                    next.setCashAmount(BigDecimalUtil.scaleAndRoundHalfUp(usableAmount));
                                }

                            }
                        }else if(Objects.nonNull(cos.getType()) && (OrderType.APPLY.equals(cos.getType()))){
                            orderIds.add(cos.getOrderId());
                        }else if(Objects.nonNull(cos.getType()) && (OrderType.RENEW.equals(cos.getType()))
                                && cycleIdSet.contains(cos.getBillBillingCycleId())){
                            if(Objects.isNull(cos.getInstanceName())){
                                continue;
                            }
                            //查询退订的订单
                            List<ServiceOrderVo> releaseServiceOrderS = serviceOrderMapper.selectOrderByName(
                                    cos.getInstanceName());
                            //存在退订的订单，退订周期与续订周期进行比较
                            if(Objects.nonNull(releaseServiceOrderS) && releaseServiceOrderS.size()>0){
                                //过滤掉同名其他订单的退订数据
                                ServiceOrderVo serviceOrderVo = releaseServiceOrderS.get(0);
                                List<ServiceOrderDetail> serviceOrderDetailList = serviceOrderDetailMapper.selectList(
                                        Wrappers.<ServiceOrderDetail>lambdaQuery()
                                                .eq(ServiceOrderDetail::getOrderId,serviceOrderVo.getId()));
                                if(Objects.nonNull(serviceOrderDetailList)){
                                    ServiceOrderDetail serviceOrderDetail = serviceOrderDetailList.get(0);
                                    //退订的周期在续订的周期之后，需要开票
                                    if(serviceOrderDetail.getEndTime().after(cos.getUsageEndDate())){
                                        orderIds.add(cos.getOrderId());
                                    }else{
                                        orderReneIds.add(cos.getOrderId());
                                    }
                                }
                            }
                        }
                    }
                }
                if (Objects.nonNull(next.getReleaseAmount())) {
                    next.setInvoiceAmount(BigDecimalUtil.scaleAndRoundHalfUp(next.getInvoiceAmount()
                                                                                 .subtract(
                                                                                         next.getReleaseAmount()
                                                                                 )));
                    next.setCashAmount(BigDecimalUtil.scaleAndRoundHalfUp(next.getCashAmount()
                                                                              .subtract(
                                                                                      next.getReleaseAmount()
                                                                              )));
                }

            }
        }
        //过滤退订中的单据
        if((CollectionUtil.isNotEmpty(orderIds) && orderIds.size()>0) || orderReneIds.size()>0){
            List<String> releaseId = new ArrayList<>();
            //过滤续订周期的订单
            if(orderReneIds.size()>0){

                for(String ordrId:orderReneIds){
                    releaseId.add(ordrId);
                }
            }

            List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectReleaseIngDetail(new ArrayList<>(orderIds));
            if(CollectionUtil.isNotEmpty(serviceOrderVos) && serviceOrderVos.size()>0){
                for (ServiceOrderVo serviceOrderVo : serviceOrderVos) {
                    releaseId.add(serviceOrderVo.getOrderId());
                }
            }

            List<InstanceGaapCost> newIngs = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(releaseId) && releaseId.size() > 0 && !CollectionUtils.isEmpty(list)) {
                Iterator<InstanceGaapCost> iterator = list.iterator();
                while (iterator.hasNext()){
                    InstanceGaapCost next = iterator.next();
                    for (String id : releaseId) {
                        if(next.getOrderId().equals(id)){
                            newIngs.add(next);
                        }
                    }
                }
            }

            if(CollectionUtil.isNotEmpty(newIngs) && newIngs.size()>0){
                Map<String, List<InstanceGaapCost>> collect = newIngs.stream().collect(Collectors.groupingBy
                        (InstanceGaapCost::getBillBillingCycleId));
                Iterator<BillBillingCycleCost> it = billBillingCycleCosts.iterator();
                while(it.hasNext()){
                    BillBillingCycleCost next = it.next();
                    for (String s : collect.keySet()) {
                        if(next.getId().toString().equals(s)){
                            it.remove();
                        }
                    }
                }
            }
        }
        return billBillingCycleCosts;
    }

    @Override
    public InvoiceDTO selectDetailInvoice(Long invoiceSid) {
        QueryWrapper<BizInvoice> wrapper = new QueryWrapper<BizInvoice>().eq(INVOICE_ID, invoiceSid);

        InvoiceDTO convert
                = BeanConvertUtil.convert(bizInvoiceMapper.selectOne(wrapper), InvoiceDTO.class);
        if (Objects.isNull(convert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1690538322));
        }
        convert.setBankAccount(CrytoUtilSimple.decrypt(convert.getBankAccount()));
        convert.setRegisterPhone(CrytoUtilSimple.decrypt(convert.getRegisterPhone()));
        convert.setRegisterAddress(CrytoUtilSimple.decrypt(convert.getRegisterAddress()));

        if (InvoiceMethodEnum.BILLING.getCode().equals(convert.getInvoiceMethod())) {
        // 查询该发票下的充值记录

        Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("invoiceIds")
                .regex("^(\\d*,)*"+invoiceSid+"(,\\d+)*$"));
        List<BillBillingCycleCost> costs = mongoTemplate.find(query, BillBillingCycleCost.class,"biz_bill_billing_cycle");
        if(CollectionUtils.isEmpty(costs)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1761956622));
        }

        Map<String,String> orderIdMap = new HashMap<>(16);
        Map<String,String> instanceNameMap = new HashMap<>(16);
        Map<String, BigDecimal> cashAmountMap = Maps.newHashMap();
        List<String> billBillingCycleIdList = costs.stream().map(BillBillingCycleCost::getId).map(String::valueOf).collect(Collectors.toList());
        Query instanceQuery = new Query();
        org.springframework.data.mongodb.core.query.Criteria instanceC = org.springframework.data.mongodb.core.query.Criteria.where("billBillingCycleId").in(billBillingCycleIdList);
        instanceQuery.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(instanceC));
        List<BillBillingCycleCost> itemInfo = mongoTemplate.find(instanceQuery, BillBillingCycleCost.class, "biz_bill_usage_item");
        List<String> orderIds = itemInfo.stream().map(BillBillingCycleCost::getOrderId).collect(Collectors.toList());
        //orderIds判空校验
        if(CollectionUtil.isNotEmpty(orderIds)){
            List<ServiceOrderVo> orderVo = serviceOrderMapper.selectServiceOrderAndDetail(new Criteria("orderIdIn", orderIds));
            orderVo.forEach(order -> {
                orderIdMap.put(order.getId().toString(),order.getName());
            });
        }
        itemInfo.forEach(info -> {
            if (Objects.nonNull(cashAmountMap.get(info.getBillBillingCycleId()))) {
                cashAmountMap.put(info.getBillBillingCycleId(), NumberUtil.add(cashAmountMap.get(info.getBillBillingCycleId()), info.getCashAmount()));
            } else {
                cashAmountMap.put(info.getBillBillingCycleId(), info.getCashAmount());
            }
            instanceNameMap.put(info.getBillBillingCycleId(),orderIdMap.get(info.getOrderId()));
        });

        List<BillBillingCycleCostVo> voList = new ArrayList<>();
        costs.forEach(detail->{
            detail.setPriceTypeName(PriceTypeEnum.codeFromName(detail.getPriceType()));
            detail.setPayTypeName(PayTypeEnum.codeFromName(detail.getBillType()));
            detail.setInstanceName(instanceNameMap.get(detail.getId().toString()));
            BillBillingCycleCostVo vo = new BillBillingCycleCostVo();
            BeanUtils.copyProperties(detail,vo);
            vo.setCashAmount(cashAmountMap.get(detail.getId() + ""));
            vo.setId(detail.getId().toString());
            voList.add(vo);
        });
        //发票的现金额应该减掉退订订单的金额

        convert.setCostList(voList);
        }

        return convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInvoiceStatus(BizInvoice bizInvoice) {
        AuthUser userInfo = RequestContextUtil.getAuthUserInfo();
        BizInvoice bizInvoice1 = bizInvoiceMapper.selectById(bizInvoice.getInvoiceSid());
        if(bizInvoice1 == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_3951031));
        }

        if(!userInfo.getEntityId().equals(bizInvoice1.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        if (!InvoiceStatusEnum.PENDING.getCode().equals(bizInvoice1.getInvoiceStatus())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1833656946));
        }
        //更新账单周期表开票状态
        Map<String,Object> updateFieldMap = new HashMap<>();
        Map<String,Object> upItemMap = new HashMap<>();
        //approveFlag标识，值为done 则标识审核通过，值为pending则审核驳回
        Date current = new Date();
        bizInvoice.setInvoiceDt(current);
        upItemMap.put("invoiceDt",current);
        String msgId;
        String msgId1;
        if(InvoiceStatusEnum.DONE.getCode().equals(bizInvoice.getInvoiceStatus())){
            updateFieldMap.put("invoiceStatus",InvoiceStatusEnum.DONE.getCode());
            upItemMap.put("invoiceStatus",InvoiceStatusEnum.DONE.getCode());
            bizInvoice.setInvoiceStatus(InvoiceStatusEnum.DONE.getCode());
            msgId = NotificationConsts.ConsoleMsg.FinanceMsg.TENANT_INVOICE_ACCESS;
            msgId1 = NotificationConsts.PlatformMsg.FinanceMsg.BSSMGT_INVOICE_ACCESS;
        }else if(InvoiceStatusEnum.REJECTED.getCode().equals(bizInvoice.getInvoiceStatus())){
            updateFieldMap.put("invoiceStatus",InvoiceStatusEnum.REJECTED.getCode());
            upItemMap.put("invoiceStatus",InvoiceStatusEnum.REJECTED.getCode());
            bizInvoice.setInvoiceStatus(InvoiceStatusEnum.REJECTED.getCode());
            msgId = NotificationConsts.ConsoleMsg.FinanceMsg.TENANT_INVOICE_REJECT;
            msgId1 = NotificationConsts.PlatformMsg.FinanceMsg.BSSMGT_INVOICE_REJECT;

            //账户回滚已开票金额
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(bizInvoice1.getAccountId());
            bizBillingAccount.setInvoicedAmount(bizBillingAccount.getInvoicedAmount().subtract(bizInvoice1.getDepositeAmount()));
            bizBillingAccountMapper.updateById(bizBillingAccount);
        }else{
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_447200890));
        }

        QueryWrapper<BizInvoice> wrapper
                = new QueryWrapper<BizInvoice>().eq(INVOICE_ID, bizInvoice.getInvoiceSid());
        if (bizInvoiceMapper.update(bizInvoice, wrapper) <= 0){
            return false;
        }

        //更新账单明细表开票状态
        Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("invoiceId")
                .is(bizInvoice.getInvoiceSid()));
        List<BillBillingCycleCost> billBillingCycleCosts = mongoTemplate.find(query, BillBillingCycleCost.class,"biz_bill_billing_cycle");
        List<String> ids = new ArrayList<>();
        if(!CollectionUtils.isEmpty(billBillingCycleCosts)){
            for (BillBillingCycleCost billBillingCycleCost : billBillingCycleCosts) {

                ids.add(String.valueOf(billBillingCycleCost.getId()));
            }
        }

        if(!CollectionUtils.isEmpty(ids)){
            boolean bool = this.updateMongoDbMethod(ids, updateFieldMap, "biz_bill_billing_cycle");
            if(!bool){
                log.error("BizInvoiceServiceImpl_updateInvoiceStatus_failed_{}",bool);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_238996974));
            }
            this.updateDetailApprove(ids,upItemMap);
        }

        if (!ObjectUtils.isEmpty(userInfo)
                && !ObjectUtils.isEmpty(userInfo.getUserType())
                && UserType.DISTRIBUTOR_USER.equals(userInfo.getUserType())
                && !UserTypeConstants.CONSOLE.equals(userInfo.getRemark())) {
            List<Long> orgSids = orgService.list(new QueryWrapper<Org>().eq("parent_id", userInfo.getOrgSid()))
                                           .stream().map(Org::getOrgSid).collect(Collectors.toList());
            List<BizBillingAccount> accounts =
                    bizBillingAccountMapper.selectList(new QueryWrapper<BizBillingAccount>().in("org_sid", orgSids));
            if (accounts.stream().noneMatch(account -> bizInvoice1.getAccountId().equals(account.getId().toString()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1849032376));
            }
        }

        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(bizInvoice1.getAccountId());
        //获取申请用户
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        Map<String, String> messageContent = MapBuilder.<String, String>create()
                                                   .put("userAccount", user.getAccount())
                                                   .put("invoiceNo", bizInvoice1.getInvoiceNo())
                                                   .put("amount", NumberUtil.roundStr(bizInvoice1.getDepositeAmount().toString(),2))
                                                   .build();
        messageContent.put("comments", StringUtils.isNotBlank(bizInvoice.getRemark()) ? bizInvoice.getRemark() : "--");
        if (ObjectUtil.isNotEmpty(user)) {
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
            baseNotificationMqBean.setMsgId(msgId);
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setEntityId(bizBillingAccount.getEntityId());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT, baseNotificationMqBean);
            List<User> users = userMapper.findAdminstratorsByEntityId(bizBillingAccount.getEntityId());
            if (CollectionUtil.isNotEmpty(users)) {
                BaseNotificationMqBean baseNotificationMqBean1 = new BaseNotificationMqBean();
                baseNotificationMqBean1.getImsgUserIds().addAll(users.stream().map(User::getUserSid).collect(Collectors.toSet()));
                baseNotificationMqBean1.setMsgId(msgId1);
                baseNotificationMqBean1.setMap(messageContent);
                baseNotificationMqBean1.setEntityId(bizBillingAccount.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT, baseNotificationMqBean1);
            }

        }
        return true;
    }

    private void assembleBaseMessageContent(Map<String, String> messageContent) {
        String companyName = PropertiesUtil.getProperty("company.name");
        String consoleUrl = PropertiesUtil.getProperty("rightcloud.console.url");
        String portalUrl = PropertiesUtil.getProperty("rightcloud.portal.url");
        messageContent.put("consoleUrl", consoleUrl);
        messageContent.put("portalUrl", portalUrl);
        messageContent.put("portalUrl", portalUrl);
        messageContent.put("companyName", companyName);
        messageContent.put("logoUrl", consoleUrl + PropertiesUtil.getProperty("platform.large.logo"));
        messageContent.put("systemName", PropertiesUtil.getProperty("system.name"));
        messageContent.put("companyPhone", PropertiesUtil.getProperty("system.contact.number"));
    }
    private void updateDetailApprove(List<String> ids, Map<String, Object> upItemMap) {
        ids.forEach(id->{
            Query query = new Query();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("billBillingCycleId").is(id));
            Update update = new Update();
            upItemMap.forEach((k,v)->{
                update.set(k,v);
            });

            UpdateResult result = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");

            List<InstanceGaapCost> costs = mongoTemplate.find(query, InstanceGaapCost.class, "biz_bill_usage_item");
            //汇总开票金额
            this.invoiceSummary(costs);


        });
    }

    private void invoiceSummary(List<InstanceGaapCost> costs) {
        Query query;
        List<Long> orgSids = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (InstanceGaapCost cost : costs) {
            orgSids.add(cost.getOrgSid());
        }
        Query querySummary = new Query();
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria.where("orgSid").in(orgSids);
        querySummary.addCriteria(criteria);
        List<InstanceGaapCostSummary> summaryJobs =
                mongoTemplate.find(querySummary, InstanceGaapCostSummary.class, "biz_bill_usage_item_summary");

        if(CollectionUtils.isEmpty(summaryJobs) || orgSids.size()!=summaryJobs.size()){

            Set<Long> summaryOrgSidSet = summaryJobs.stream().map(InstanceGaapCostSummary::getOrgSid).collect(Collectors.toSet());

            for (Long orgSid : orgSids) {
                if(summaryOrgSidSet.contains(orgSid)){
                    continue;
                }
                InstanceGaapCostSummary costSummary = new InstanceGaapCostSummary();
                costSummary.setTotalBillAmount(BigDecimal.ZERO);
                costSummary.setCashAmount(BigDecimal.ZERO);
                costSummary.setCouponAmount(BigDecimal.ZERO);
                costSummary.setCouponDiscount(BigDecimal.ZERO);
                costSummary.setCreditAmount(BigDecimal.ZERO);
                costSummary.setDiscountAmount(BigDecimal.ZERO);
                costSummary.setGaapPretaxAmount(BigDecimal.ZERO);
                costSummary.setInvoiceAmount(BigDecimal.ZERO);
                costSummary.setRechargeCreditAmount(BigDecimal.ZERO);
                costSummary.setMonthGaapPretaxAmount(BigDecimal.ZERO);
                costSummary.setOfficialAmount(BigDecimal.ZERO);
                costSummary.setOrgDiscount(BigDecimal.ZERO);
                costSummary.setPretaxAmount(BigDecimal.ZERO);
                costSummary.setPretaxGrossAmount(BigDecimal.ZERO);
                costSummary.setPricingDiscount(BigDecimal.ZERO);
                costSummary.setCrateDt(new Date());
                costSummary.setOrgSid(orgSid);
                costSummary.setId(null);
                InstanceGaapCostSummary insert = mongoTemplate.insert(costSummary);
                summaryJobs.add(insert);
            }
        }

        if(!CollectionUtils.isEmpty(summaryJobs) ){
            for (InstanceGaapCostSummary summaryJob : summaryJobs) {
                for (InstanceGaapCost cost : costs) {
                    if(!"done".equalsIgnoreCase(cost.getInvoiceStatus())){
                        continue;
                    }
                    BigDecimal rechargeCreditAmount = Optional.ofNullable(cost.getRechargeCreditAmount()).orElse(BigDecimal.ZERO);


                    ids.add(cost.getId());
                    if(summaryJob.getOrgSid().equals(cost.getOrgSid())){
                        summaryJob.setInvoiceAmount((summaryJob.getInvoiceAmount()==null?BigDecimal.ZERO:summaryJob.getInvoiceAmount())
                                .add(Optional.ofNullable(cost.getCashAmount()).orElse(BigDecimal.ZERO).subtract(rechargeCreditAmount)));
                    }
                }
                querySummary = new Query();
                Update update = new Update();
                querySummary.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(summaryJob.getId()));
                update.set("invoiceAmount",summaryJob.getInvoiceAmount());
                UpdateResult updateResult = mongoTemplate.updateFirst(querySummary, update, "biz_bill_usage_item_summary");
                if(updateResult.getMatchedCount()>0){
                    log.info("BillBillingCycleJobServiceImpl------invoiceSummary-----------success");
                    query = new Query();
                    update = new Update();
                    query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").in(ids));
                    update.set("summaryInvoiceFlag","Y");
                    UpdateResult updateResultItem = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");
                    if(updateResultItem.getMatchedCount()>0){
                        log.info("BizInvoiceServiceImpl.invoiceSummary-----------success");
                    }else{

                    }
                }else{

                }
            }

        }
    }


    /**
     *
     * 创建开票记录
     * 账单开票逻辑不变
     * 新增充值金额开票
     *
     * @param bizInvoice bizInvoice
     * @param ids ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyInvoice(BizInvoice bizInvoice, String ids) {
        bizInvoice.setInvoiceNo(InvoiceUtil.getInvoiceNo());
        // 开票时插入运营实体信息
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(bizInvoice.getAccountId());
        bizInvoice.setEntityId(bizBillingAccount.getEntityId());
        bizInvoice.setRegisterPhone(CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone()));
        Optional.ofNullable(sysBssEntityMapper.selectById(bizInvoice.getEntityId()))
                .ifPresent(entity -> bizInvoice.setEntityName(entity.getName()));
        //更新已开票金额
        bizBillingAccount.setInvoicedAmount(bizBillingAccount.getInvoicedAmount().add(bizInvoice.getDepositeAmount()));
        bizBillingAccountMapper.updateById(bizBillingAccount);

        List<String> headerIds = new ArrayList<>();
        Map<String, String> hisInvoiceIdsMap = new HashMap<>();
        if (InvoiceMethodEnum.BILLING.getCode().equals(bizInvoice.getInvoiceMethod())) {
            // 校核充值记录是否有效
            if (Strings.isNullOrEmpty(ids)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            List<String> depositIds = JSONArray.parseArray(ids, String.class);
            List<BillBillingCycleCost> costs = new ArrayList<>();
            for (String id : depositIds) {
                log.info("BizInvoiceServiceImpl_createInvoice_[{}]",id);
                Query query = new Query();
                org.springframework.data.mongodb.core.query.Criteria c1 = org.springframework.data.mongodb.core.query.Criteria
                        .where("id").is(new ObjectId(id))
                        .and("ownerId").is(bizBillingAccount.getId());
                org.springframework.data.mongodb.core.query.Criteria c2 = org.springframework.data.mongodb.core.query.Criteria
                        .where("invoiceStatus").ne(InvoiceStatusEnum.PENDING.getCode()).and("ownerId").is(bizBillingAccount.getId());
                org.springframework.data.mongodb.core.query.Criteria c3 = org.springframework.data.mongodb.core.query.Criteria
                        .where("invoiceStatus").ne(InvoiceStatusEnum.DONE.getCode()).and("ownerId").is(bizBillingAccount.getId());
                query.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(c1,c2,c3));
                query.addCriteria(c1);
                BillBillingCycleCost one = mongoTemplate.findOne(query, BillBillingCycleCost.class,"biz_bill_billing_cycle");
                if (Objects.isNull(one)) {
                    Query query1=new Query();
                    org.springframework.data.mongodb.core.query.Criteria c4= org.springframework.data.mongodb.core.query.Criteria
                            .where("_id").is(new ObjectId(id)).and("ownerId").is(bizBillingAccount.getId());
                    query1.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(c4));
                    BillBillingCycleCost one1 = mongoTemplate.findOne(query1, BillBillingCycleCost.class,
                                                                      "biz_bill_billing_cycle");
                    if (!Objects.isNull(one1)&&InvoiceStatusEnum.PENDING.getCode().equals(one1.getInvoiceStatus())){
                        throw new BizException(WebUtil.getMessage(MsgCd.INVOICE_AMOUNT_PENDING));
                    }
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if (ProductCodeEnum.AI_MARKET.getProductType().equals(one.getProductCode()) &&
                        (Objects.isNull(one.getMarketShopState()) || Boolean.FALSE.equals(one.getMarketShopState()))) {
                    throw new BizException(WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CURRENT_ORDER_NOT_COMPLETED));
                }
                hisInvoiceIdsMap.put(one.getId().toString(), one.getInvoiceIds() == null ? "" : one.getInvoiceIds());
                headerIds.add(one.getId().toString());
                costs.add(one);
            }

            // 校核充值记录和发票金额是否一致
            BigDecimal totalMoney = BigDecimal.ZERO;
            for (BillBillingCycleCost cost : costs) {
                BigDecimal rechargeCreditAmount = cost.getRechargeCreditAmount();
                if (Objects.isNull(rechargeCreditAmount)) {
                    rechargeCreditAmount = BigDecimal.ZERO;
                }
                totalMoney = totalMoney.add(cost.getInvoiceAmount().subtract(rechargeCreditAmount));
            }
            if (totalMoney.setScale(3,BigDecimal.ROUND_HALF_UP).compareTo(bizInvoice.getDepositeAmount().setScale(3,BigDecimal.ROUND_HALF_UP)) != 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.INVOICE_AMOUNT_EXCEPTION));
            }
        }

        if (PERSONAL_INVOICE_TYPE.equals(bizInvoice.getInvoiceType())) {
            bizInvoice.setBankAccount(null);
            bizInvoice.setDepositBank(null);
            bizInvoice.setRegisterAddress(null);
            bizInvoice.setRegisterPhone(null);
        } else {
            // 银行账号加密存储
            if(!CCSPCacheUtil.ccspServiceOpen()){
                bizInvoice.setBankAccount(CrytoUtilSimple.encrypt(bizInvoice.getBankAccount()));
            }
            if(!CCSPCacheUtil.ccspServiceOpen()){
                bizInvoice.setRegisterPhone(CrytoUtilSimple.encrypt(bizInvoice.getRegisterPhone()));
            }
            if(!CCSPCacheUtil.ccspServiceOpen()){
                bizInvoice.setRegisterAddress(CrytoUtilSimple.encrypt(bizInvoice.getRegisterAddress()));
            }
        }
        bizInvoiceMapper.insertInvoice(bizInvoice);
        if (InvoiceMethodEnum.BILLING.getCode().equals(bizInvoice.getInvoiceMethod())) {
            this.updateCycle(headerIds, InvoiceStatusEnum.PENDING.getCode(), new BigDecimal(0),bizInvoice.getInvoiceSid(), hisInvoiceIdsMap);
            this.updateDetail(headerIds);
        }
        // 给管理员发送消息通知
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        Map<String, String> messageContent = MapBuilder.<String, String>create()
                                                       .put("userAccount", user.getAccount())
                                                       .put("invoiceNo", bizInvoice.getInvoiceNo())
                                                       .put("amount", NumberUtil.roundStr(bizInvoice.getDepositeAmount().toString(), 2))
                                                       .build();
        BaseNotificationMqBean notificationMqBean = new BaseNotificationMqBean();
        notificationMqBean.setMsgId(NotificationConsts.PlatformMsg.FinanceMsg.BSSMGT_INVOICE_APPROVAL);
        notificationMqBean.setMap(messageContent);
        notificationMqBean.setEntityId(bizBillingAccount.getEntityId());
        notificationMqBean.getImsgUserIds().add(1L);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT, notificationMqBean);
        return true;
    }

    private void updateDetail(List<String> ids) {
        ids.forEach(id->{
            Query query = new Query();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("billBillingCycleId").is(id));
            Update update = Update.update("invoiceStatus", InvoiceStatusEnum.PENDING.getCode());
            UpdateResult result = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");

        });

    }

    /**
     * 新增InvoiceDeposit
     * @param depositSid
     * @param invoiceSid
     * @return
     */
    private boolean insertInvoiceDeposit(Long depositSid, Long invoiceSid){
        BizInvoiceDeposite bd = new BizInvoiceDeposite()
                                .setDepositeSid(depositSid)
                                .setInvoiceSid(invoiceSid);
        WebUserUtil.prepareInsertParams(bd);
        if (bizInvoiceDepositeMapper.insert(bd) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 检查用户是否可以开发票
     * @param accountId
     * @return
     */
    @Override
    public boolean checkInvoicePermission(String accountId) {
        // 检查用户的发票信息和邮寄地址等信息是否完整
        QueryWrapper<BizInvoiceSetting> wrapper =
                new QueryWrapper<BizInvoiceSetting>().eq(ACCOUNT_ID, accountId);
        BizInvoiceSetting bizInvoiceSetting = bizInvoiceSettingMapper.selectOne(wrapper);

        if (Objects.isNull(bizInvoiceSetting)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_566660286));
        }
        // 校核发票类型是否为空
        if (Strings.isNullOrEmpty(bizInvoiceSetting.getInvoiceType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_845801186));
        }
        if (!PERSONAL_INVOICE_TYPE.equals(bizInvoiceSetting.getInvoiceType())) {
            CheckInvoiceSettingDTO convert =
                    BeanConvertUtil.convert(bizInvoiceSetting, CheckInvoiceSettingDTO.class);
            for (Field f : ClassUtils.getUserClass(convert).getDeclaredFields()) {
                ReflectionUtils.makeAccessible(f);
                try {
                    if (Strings.isNullOrEmpty((String) f.get(convert))){
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_845801186));
                    };
                } catch (Exception e) {
                    log.info("开票信息不完整");
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_845801186));
                }
            }
        }

        if (Strings.isNullOrEmpty(bizInvoiceSetting.getAddress())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_131217663));
        }
        if (Strings.isNullOrEmpty(bizInvoiceSetting.getPhone())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1733784797));
        }
        if (Strings.isNullOrEmpty(bizInvoiceSetting.getReceiver())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_263692571));
        }

        // 校核是否有金额可以开发票
        Query query = new Query();
        org.springframework.data.mongodb.core.query.Criteria c1 = org.springframework.data.mongodb.core.query.Criteria.where("ownerId").is(Long.parseLong(accountId));
        org.springframework.data.mongodb.core.query.Criteria c2 = org.springframework.data.mongodb.core.query.Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.DONE.getCode());
        org.springframework.data.mongodb.core.query.Criteria c3 = org.springframework.data.mongodb.core.query.Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.PENDING.getCode());
        query.addCriteria(new org.springframework.data.mongodb.core.query.Criteria().andOperator(c1,c2,c3));
        List<BillBillingCycleCost> billBillingCycleCosts = mongoTemplate.find(query, BillBillingCycleCost.class);
        if(CollectionUtils.isEmpty(billBillingCycleCosts)){
            log.info("BizInvoiceServiceImpl checkInvoicePermission is empty");
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746776280));
        }
        BigDecimal totalMoney = new BigDecimal(0);
        for (BillBillingCycleCost cost : billBillingCycleCosts) {
            totalMoney = totalMoney.add(cost.getCashAmount()).setScale(3, BigDecimal.ROUND_HALF_UP);
        }
        if (totalMoney.equals(BigDecimal.ZERO)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1129399905));
        }
        if (BigDecimal.ZERO.compareTo(totalMoney) != -1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746776280));
        }
        return true;
    }

    @Override
    public List<InvoiceDTO> exportInvoice(Criteria criteria) {
        return bizInvoiceMapper.selectByParam(criteria);
    }


    @Override
    public IPage<InvoiceDTO> selectByParams(IPage<InvoiceDTO> page, Criteria criteria ){
        IPage<InvoiceDTO> invoiceDTOIPage = bizInvoiceMapper.selectByParams(page, criteria);
        // 32947 查询周期记录

        return invoiceDTOIPage;
    }
    /**
     * 获取通用排序Sort
     * @param example
     * @return
     */
    private Sort getCommonSort(Criteria example){
        //设置排序
        Sort sorts = Sort.by(Sort.Direction.DESC, BILL_ING_CYCLE);
        if(!StringUtil.isNullOrEmpty(example.getOrderByClause())){
            String[] orderBys = example.getOrderByClause().split(",");
            List<String> orders = Arrays.asList(orderBys);
            if(!StringUtil.isNullOrEmpty(orders) && !orders.isEmpty()){
                for (String order:orders){
                    String[] realOrder = order.split(" ");
                    String fieldName = realOrder[0];
                    String direction = realOrder[1];
                    Sort itemSort = Sort.by("desc".equals(direction) ? Sort.Direction.DESC: Sort.Direction.ASC, fieldName.toLowerCase());
                    sorts = Sort.by(Sort.Direction.DESC, BILL_ING_CYCLE).and(itemSort);
                }
            }
        }
        return sorts;
    }

    /**
     * 更新账单周期和
     * @param headerIds
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCycle(List<String> headerIds, String status, BigDecimal amount, Long invoiceId, String invoiceIds) {
        Map<String, Object> updateMap;
        Map<String, Object> queryFieldMap;
        //更新账单周期表开票金额为0 状态为PENDING
        queryFieldMap = new HashMap<>();
        updateMap = new HashMap<>();
        updateMap.put("invoiceStatus",status);
        updateMap.put("invoiceId",invoiceId);
        // 32947 针对同一周期二次开票导致发票详情开票数据查询不到
        updateMap.put("invoiceIds",invoiceIds);
        boolean b = this.updateMongoDbMethod(headerIds, updateMap, "biz_bill_billing_cycle");
        if(!b){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1879541542));
        }
    }

    /**
     * 更新账单周期和
     * @param headerIds
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCycle(List<String> headerIds, String status, BigDecimal amount, Long invoiceId, Map<String, String> hisInvoiceIdsMap) {
        for (String headerId : headerIds) {
            List<String> ids = new ArrayList<>();
            ids.add(headerId);
            Map<String, Object> updateMap;
            //更新账单周期表开票金额为0 状态为PENDING
            updateMap = new HashMap<>();
            updateMap.put("invoiceStatus", status);
            updateMap.put("invoiceId", invoiceId);
            // 32947 针对同一周期二次开票导致发票详情开票数据查询不到
            updateMap.put("invoiceIds",hisInvoiceIdsMap.get(headerId) + "," + invoiceId);
            boolean b = this.updateMongoDbMethod(ids, updateMap, "biz_bill_billing_cycle");
            if(!b){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1879541542));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateMongoDbMethod(List<String> headerIds, Map<String, Object> updateFieldMap,String updateTableName){
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        boolean bool = Boolean.TRUE;
       if(headerIds.size()>0){
           for (String headerId : headerIds) {
               if (updateFieldMap != null && !updateFieldMap.isEmpty()) {
                   Update update = new Update();
                   Query query = new Query();
                   query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(new ObjectId(headerId)));
                   updateFieldMap.forEach(update::set);
                   UpdateResult updateResult = mongoTemplate.updateFirst(query, update, updateTableName);
                   if(updateResult.getMatchedCount()>0){
                       log.info("BizInvoiceServiceImpl------update-----------success");
                   }else{

                       log.info("创建发票异常：更新账单周期数据失败,发票Id:[{}]",headerId);
                       throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1069443934));
                   }
               }
           }
       }
        return bool;
    }

    @Override
    public List<BillBillingCycleCost> checkServiceRelease(List<BillBillingCycleCost> costs) {
        return this.getBillBillingCycleCosts(costs,false);
    }

    @Override
    public InvoiceDTO selectInvoice(Long accountId) {
        return bizInvoiceMapper.selectInvoice(accountId);
    }

    @Override
    public List<InvoiceDTO> selectRechargeByOrgSid(List<Long> orgSids, Long entityId) {
        return bizInvoiceMapper.selectRechargeByOrgSid(orgSids, entityId);
    }
}
