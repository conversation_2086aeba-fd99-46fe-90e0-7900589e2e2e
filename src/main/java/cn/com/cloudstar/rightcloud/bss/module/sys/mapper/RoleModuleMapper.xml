<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleModuleMapper" >
  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.user.RoleModule" >
    <id column="module_sid" property="moduleSid" jdbcType="VARCHAR" />
    <id column="role_sid" property="roleSid" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <trim prefix="where" prefixOverrides="and|or" >
      <if test="condition.roleSid != null">
        and role_sid = #{condition.roleSid}
      </if>
      <if test="condition.roleSids != null">
        and role_sid in
        <foreach item="item" index="index" collection="condition.roleSids"
            open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="condition.moduleSid != null">
        and module_sid = #{condition.moduleSid}
      </if>

      <if test="condition.moduleSids != null">
        AND module_sid IN
        <foreach item="item" index="index" collection="condition.moduleSids"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </trim>
  </sql>
  <sql id="Base_Column_List" >
    module_sid, role_sid
  </sql>

  <select id="selectRoleModuleByUserSid" resultMap="BaseResultMap" parameterType="_long">
      SELECT role_sid,
             module_sid
      FROM sys_m_role_module A
      WHERE A.role_sid in (
          SELECT role_sid
          FROM sys_m_user_role
          WHERE user_sid = #{userSid})
        and A.module_sid in (SELECT module_sid
                             FROM sys_m_module
                             WHERE display_flag = 1)

  </select>

    <select id="countRoleModuleByUserSid" resultType="java.lang.Integer">
        SELECT count(0) FROM sys_m_role_module A WHERE A.role_sid in ( SELECT role_sid FROM sys_m_user_role WHERE user_sid = #{userSid})
    </select>

    <select id="selectCommonModuleSidByConsole"  resultType="java.lang.String" parameterType="java.lang.Long">
        SELECT module_sid
        FROM
            sys_m_module
        WHERE
            module_sid = 'C1' or parent_sid = 'C1'
    </select>
</mapper>
