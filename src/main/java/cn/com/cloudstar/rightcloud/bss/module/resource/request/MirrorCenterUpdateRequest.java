package cn.com.cloudstar.rightcloud.bss.module.resource.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("镜像中心更新")
@Data
public class MirrorCenterUpdateRequest {

    private Long id;
    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer pagesize;

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer pagenum;

    /**
     * 组织
     */
    //@NotEmpty
    @ApiModelProperty(value = "组织", required = true)
    private String namespace;

    /**
     * 仓库
     */
    @ApiModelProperty(value = "仓库")
    private String repository;

    /**
     * 是否公开
     */
    @ApiModelProperty(value = "是否公开")
    private Boolean is_public;


    /**
     *
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     *
     */
    @ApiModelProperty(value = "tag")
    private String tag;

    /**
     *
     */
    private String projectname;

    /**
     * 镜像名称
     */
    private String name;


    /**
     * 镜像个数
     */
    @ApiModelProperty(value = "镜像个数")
    private String numImages;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private Long warehouseId;

    /**
     * 详情
     */
    @ApiModelProperty(value = "详情")
    private String descriptionDetail;

}
