/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.operation.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Period;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Period.PeriodType;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponAccountService;
import cn.com.cloudstar.rightcloud.bss.module.message.pojo.entity.AccountVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderNumAnalysisVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderTypeAnalysisVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderTypePeriodVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderTypePeriodVO.OrderType;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderUserAnalysisVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.request.DescribeOrderInfoRequest;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.request.HeaderRequest;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.response.*;
import cn.com.cloudstar.rightcloud.bss.module.operation.service.IOperationStatisticsService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BA;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BT;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运营统计
 *
 * <AUTHOR>
 * @date 2019/10/17.
 */
@Api(tags = "运营统计")
@RestController
@RequestMapping("/operation/statistics")
@Validated
public class OperationStatisticsController {

    @Autowired
    private IOperationStatisticsService operationStatisticsService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private IBizCouponAccountService bizCouponAccountService;

    /**
     * 获得统计数据
     *
     * @param orgId 组织id
     * @return {@code DescribeStatisticsResponse}
     */
    @AuthorizeBss(action = BA.BA)
    @ApiOperation(httpMethod = "GET", value = "运营统计-基本信息")
    @GetMapping()
    public DescribeStatisticsResponse getStatistics(@ApiParam("组织ID")
                                                    @RequestParam(value = "orgId", required = false) Long orgId) {
        return BeanConvertUtil.convert(operationStatisticsService.getStatistics(orgId), DescribeStatisticsResponse.class);
    }

    /**
     * 运营统计-用户Top数据
     *
     * @param num   Top数量
     * @param orgId 组织id
     * @return {@code DescribeTopTenResponse}
     */
    @ApiOperation(httpMethod = "GET", value = "运营统计-用户Top数据")
    @AuthorizeBss(action = BA.USER_TOP)
    @GetMapping("/top")
    public DescribeTopTenResponse getTopInfo(@ApiParam("Top数量") @RequestParam(value = "num", required = false, defaultValue = "10") Integer num,
                                             @ApiParam("组织ID") @RequestParam(value = "orgId", required = false) Long orgId) {
        DescribeTopTenResponse convert = BeanConvertUtil.convert(operationStatisticsService.getTopInfo(orgId, num), DescribeTopTenResponse.class);
        if (convert != null) {
            if (convert.getBalance() != null) {
                DesensitizationUtil.desensitization(convert.getBalance());
            }

        }
        return convert;
    }

    /**
     * 运营统计-消费趋势
     *
     * @param period 周期
     * @param orgId  组织id
     * @return {@code List<DescribeConsumeTrendResponse>}
     */
    @AuthorizeBss(action = AuthModule.BA.BA)
    @ApiOperation(httpMethod = "GET", value = "运营统计-消费趋势")
    @GetMapping("/consume_trend")
    public List<DescribeConsumeTrendResponse> getConsumeTrend(@ApiParam("周期") @RequestParam("period") String period,
                                                              @ApiParam("组织ID") @RequestParam(value = "orgId", required = false) Long orgId) {
        return BeanConvertUtil.convert(operationStatisticsService.getConsumeTrend(period, orgId),
                DescribeConsumeTrendResponse.class);
    }

    /**
     * 运营分析-订单数
     *
     * @param request 订单分析请求体
     * @return {@code List<DescribeOrderNumResponse>}
     */
    @ApiOperation(httpMethod = "GET", value = "运营分析-订单数")
    @AuthorizeBss(action = BT.BT01)
    @GetMapping("/order_num")
    public List<DescribeOrderNumResponse> getOrderNumAnalysis(@Valid DescribeOrderInfoRequest request) {
        LocalDateTime startDate = LocalDateTime.parse(request.getStartTime(), DateTimeFormatter.ofPattern(DateUtil.COMMON_DATE_PATTERN));
        LocalDateTime endDate = LocalDateTime.parse(request.getEndTime(), DateTimeFormatter.ofPattern(DateUtil.COMMON_DATE_PATTERN));

        Criteria criteria = new Criteria();
        criteria.put("startTime", startDate);
        criteria.put("endTime", endDate);
        List<OrderNumAnalysisVO> numAnalysisList = operationStatisticsService.getOrderNumAnalysis(criteria);
        Map<String, Integer> numMap = numAnalysisList.stream().collect(
                Collectors.toMap(OrderNumAnalysisVO::getPeriod, OrderNumAnalysisVO::getCount, (k1, k2) -> k1));

        List<OrderNumAnalysisVO> result = Lists.newArrayList();
        List<Period> periods = DateUtil.getPeriod(startDate.toLocalDate(), endDate.toLocalDate(), PeriodType.Daily);
        for (Period period : periods) {
            OrderNumAnalysisVO numAnalysis = new OrderNumAnalysisVO();
            numAnalysis.setPeriod(period.getPeriodName());
            numAnalysis.setCount(numMap.getOrDefault(period.getPeriodName(), 0));
            result.add(numAnalysis);
        }
        return BeanConvertUtil.convert(result,
                DescribeOrderNumResponse.class);
    }

    /**
     * 运营分析-订单服务类型
     *
     * @param request 订单分析请求体
     * @return {@code DescribeOrderTypeResponse}
     */
    @ApiOperation(httpMethod = "GET", value = "运营分析-订单服务类型")
    @GetMapping("/order_type")
    @AuthorizeBss(action = AuthModule.BT.BT01)
    public DescribeOrderTypeResponse getOrderTypeAnalysis(@Valid DescribeOrderInfoRequest request) {
        LocalDateTime startDate = LocalDateTime.parse(request.getStartTime(), DateTimeFormatter.ofPattern(DateUtil.COMMON_DATE_PATTERN));
        LocalDateTime endDate = LocalDateTime.parse(request.getEndTime(), DateTimeFormatter.ofPattern(DateUtil.COMMON_DATE_PATTERN));

        Criteria criteria = new Criteria();
        criteria.put("startTime", startDate);
        criteria.put("endTime", endDate);
        if (StrUtil.isNotEmpty(request.getServiceType())) {
            criteria.put("serviceType", request.getServiceType());
        }
        List<OrderTypeAnalysisVO> orderTypeAnalysis = operationStatisticsService.getOrderTypeAnalysis(criteria);
        Map<String, Object> result = Maps.newHashMap();
        BigDecimal totalAmount = orderTypeAnalysis.stream().map(orderTypeAnalysisVO -> orderTypeAnalysisVO.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //金额为负显示0
        result.put("totalAmount", NumberUtil.isLessOrEqual(totalAmount, BigDecimal.ZERO)
                ? 0 : totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));

        // 设置各服务类型订单金额占比
        Map<String, List<OrderTypeAnalysisVO>> serviceTypeMap = orderTypeAnalysis.stream()
                .filter(analysis -> StrUtil.isNotBlank(analysis.getServiceType()))
                .collect(Collectors.groupingBy(OrderTypeAnalysisVO::getServiceType));

        List<OrderTypeAnalysisVO> orderTypeResult = Lists.newArrayList();

        serviceTypeMap.forEach((serviceType, orderAmounts) -> {
            OrderTypeAnalysisVO orderType = new OrderTypeAnalysisVO();
            orderType.setServiceType(serviceType);
            orderType.setServiceTypeName(ProductCodeEnum.toDesc(serviceType));
            orderType.setAmount(orderAmounts.stream().map(orderTypeAnalysisVO -> orderTypeAnalysisVO.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
            orderTypeResult.add(orderType);
        });

        //计算百分比，金额为负不参与计算百分比
        BigDecimal greatZeroAmount = orderTypeResult.stream()
                .filter(orderAmount -> NumberUtil.isGreater(orderAmount.getAmount(), BigDecimal.ZERO))
                .map(OrderTypeAnalysisVO::getAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        orderTypeResult.forEach(orderType -> {
            if (NumberUtil.isLessOrEqual(orderType.getAmount(), BigDecimal.ZERO)) {
                orderType.setRatio(BigDecimal.ZERO);
            } else {
                orderType.setRatio(NumberUtil.mul(NumberUtil.div(orderType.getAmount(), greatZeroAmount), BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP));
            }
        });

        result.put("orderTypeRatio", orderTypeResult);
        // 设置周期内各服务类型订单金额

        Map<String, Map<String, List<OrderTypeAnalysisVO>>> serviceTypePeriodMap = orderTypeAnalysis.stream()
                .collect(
                        Collectors.groupingBy(OrderTypeAnalysisVO::getPeriod, Collectors.groupingBy(OrderTypeAnalysisVO::getServiceType)));
        List<OrderTypePeriodVO> periodOrders = Lists.newArrayList();
        List<Period> periods = DateUtil.getPeriod(startDate.toLocalDate(), endDate.toLocalDate(), PeriodType.Monthly);
        for (Period period : periods) {
            OrderTypePeriodVO typePeriodVO = new OrderTypePeriodVO();
            String periodMonth = period.getPeriodStart().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            typePeriodVO.setPeriod(periodMonth);
            Map<String, List<OrderTypeAnalysisVO>> details = serviceTypePeriodMap.get(periodMonth);
            if (CollectionUtil.isEmpty(details)) {
                continue;
            }
            details.forEach((type, analysis) -> {
                if (StrUtil.isEmpty(type)) {
                    return;
                }
                OrderType orderType = new OrderType();
                orderType.setServiceType(type);
                orderType.setServiceName(ProductCodeEnum.toDesc(type));
                orderType.setAmount(details.get(type).get(0).getAmount()
                        .setScale(2, BigDecimal.ROUND_HALF_UP));
                typePeriodVO.getItems().add(orderType);
            });
            periodOrders.add(typePeriodVO);
        }
        result.put("periodOrders", periodOrders);

        return BeanConvertUtil.convert(result,
                DescribeOrderTypeResponse.class);
    }

    /**
     * 运营分析-客户订单数
     *
     * @param request 订单分析请求体
     * @return {@code IPage<DescribeOrderAccountResponse>}
     */
    @ApiOperation(httpMethod = "GET", value = "运营分析-客户订单数")
    @AuthorizeBss(action = BT.BT01)
    @GetMapping("/order_account")
    public IPage<DescribeOrderAccountResponse> getUserOrderAnalysis(@Valid DescribeOrderInfoRequest request) {
        Criteria criteria = new Criteria();
        criteria.put("startTime", request.getStartTime());
        criteria.put("endTime", LocalDateTime.parse(request.getEndTime(),
                DateTimeFormatter.ofPattern(DateUtil.COMMON_DATE_PATTERN)).toString());
        IPage<OrderUserAnalysisVO> page = PageUtil.preparePageParams(request);
        IPage<OrderUserAnalysisVO> orderUserAnalysis = operationStatisticsService.getOrderUserAnalysis(page, criteria);
        orderUserAnalysis.getRecords().forEach(e -> {
            e.setAmount(e.getAmount().setScale(3, BigDecimal.ROUND_DOWN));
        });
        return BeanConvertUtil.convertPage(orderUserAnalysis,
                DescribeOrderAccountResponse.class);
    }


    /**
     * 获取头信息
     *
     * @param request 账户头部信息请求体
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "获取头信息")
    @GetMapping("/header/info")
    public RestResult<HeaderResponse> header(HeaderRequest request) {
        // 数据越权判断
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtil.isEmpty(authUserInfo)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(cn.com.cloudstar.rightcloud.oss.common.constants.RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        BizBillingAccount account = bizBillingAccountService.getById(request.getAccountId());
        if (ObjectUtil.isNotEmpty(account) && !account.getOrgSid().equals(authUserInfo.getOrgSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        if (Objects.nonNull(request.getFlag()) && 1 == request.getFlag()){
            HeaderResponse headerResponse = bizCouponAccountService.findSumCoupon(request);
            return new RestResult(headerResponse);
        }
        AccountVO accountVO = null;
        if (Objects.nonNull(account)) {
            accountVO = operationStatisticsService.getAccountVO(account.getId());
        }
        HeaderResponse headerResponse = new HeaderResponse();
        headerResponse.setAccountVO(accountVO);
        return new RestResult(headerResponse);
    }
}
