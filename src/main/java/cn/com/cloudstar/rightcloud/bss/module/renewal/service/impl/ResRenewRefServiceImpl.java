/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResContainerCluster;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResDcs;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RenewTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.OrgConfig;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductOperation;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductService;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IUserGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.LdapUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.InquiryRenewPriceRequestDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractDetail;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.EmailVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.UpdateResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.mapper.ResRenewRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.ResRenewRef;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.CloudEnvVO;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.ProductVO;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.RenewalDTO;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.DescribeRenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.ListProductAndCloudEnvRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.response.ListProductAndCloudEnvResponse;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.ResRenewRefService;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.ResourceConfigService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.CloudAndProductDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResAllParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cce.CceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.dcs.ResDcsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;


@Service
@Slf4j
public class ResRenewRefServiceImpl implements ResRenewRefService {

    private static final String SALE_TYPE = "02";
    private static final String NORMAL_TYPE = "01";

    private static final String LOGIN_NODE_INFO = "loginNodeInfo";

    private static final String COMPUTE_NODE_INFO = "computeNodeInfo";

    @Autowired
    private IBizDistributorProductService distributorProductService;

    @Autowired
    private ResRenewRefMapper resRenewRefMapper;

    @Autowired
    @Lazy
    private IUserGroupService iUserGroupService;

    @Autowired
    private OrgConfigMapper orgConfigMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private BizContractDetailMapper  contractDetailMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private ResourceConfigService resourceConfigService;

    @Autowired
    private BizInquiryPriceService inquiryPriceService;

    @Autowired
    private IServiceOrderPriceDetailService iServiceOrderPriceDetailService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @DubboReference
    private ResAllRemoteService resAllRemoteService;

    @DubboReference
    private ResVmRemoteService resVmRemoteService;
    @DubboReference
    private MaRemoteService maRemoteService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private HpcClusterService hpcClusterService;

    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;
    @Autowired
    private IServiceOrderService serviceOrderService;
    @Autowired
    private BizInquiryPriceService bizInquiryPriceService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private LdapUserService ldapUserService;
    @Autowired
    private BizContractService bizContractService;
    @DubboReference
    private CceRemoteService cceRemoteService;
    @DubboReference
    private ResFloatingIpRemoteService eipRemoteService;
    @DubboReference
    private ResVdRemoteService ebsRemoteService;
    @DubboReference
    private ResDcsRemoteService dcsRemoteService;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;
    @Autowired
    private BizCouponMapper bizCouponMapper;


    public static final List<String> normalList = Lists.newArrayList("running", "stopped",
            "normal", "ACTIVE", "UNUSED", "02");
    private static final Logger logger = LoggerFactory.getLogger(ResRenewRefServiceImpl.class);


    @Override
    public int countByParams(Criteria example) {
        int count = this.resRenewRefMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }

    @Override
    public ResRenewRef selectByPrimaryKey(Long renewSid) {
        return this.resRenewRefMapper.selectByPrimaryKey(renewSid);
    }

    @Override
    public List<ResRenewRef> selectByParams(Criteria example) {
        return this.resRenewRefMapper.selectByParams(example);
    }

    @Override
    public int deleteByPrimaryKey(Long renewSid) {
        return this.resRenewRefMapper.deleteByPrimaryKey(renewSid);
    }

    @Override
    public int updateByPrimaryKeySelective(ResRenewRef record) {
        return this.resRenewRefMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ResRenewRef record) {
        return this.resRenewRefMapper.updateByPrimaryKey(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.resRenewRefMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(ResRenewRef record, Criteria example) {
        return this.resRenewRefMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(ResRenewRef record, Criteria example) {
        return this.resRenewRefMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(ResRenewRef record) {
        return this.resRenewRefMapper.insert(record);
    }

    @Override
    public int insertSelective(ResRenewRef record) {
        return this.resRenewRefMapper.insertSelective(record);
    }

    @Override
    public ListProductAndCloudEnvResponse listProductAndCloud(ListProductAndCloudEnvRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("chargeType", "PrePaid");
        User authUser = AuthUtil.getAuthUser();
        this.selectByProject(criteria, request.getProject(), authUser);
        List<CloudAndProductDTO> simple = resAllRemoteService.selectSimpleByParams(criteria.toRemoteRequest(ResAllParams.class));
        Set<CloudEnvVO> cloudEnvVOSet = new HashSet<CloudEnvVO>();
        HashSet<ProductVO> productVOS = new HashSet<>();
        if (CollectionUtil.isNotEmpty(simple)) {
            simple.forEach(s -> {
                cloudEnvVOSet.add(BeanConvertUtil.convert(s, CloudEnvVO.class));
                ProductVO convert = BeanConvertUtil.convert(s, ProductVO.class);
                convert.setProductName(ProductCodeEnum.keyFromDesc(s.getProductType()));
                productVOS.add(convert);
            });
        }
        ListProductAndCloudEnvResponse response = new ListProductAndCloudEnvResponse();
        response.setCloud(Lists.newArrayList(cloudEnvVOSet));
        response.setProducts(Lists.newArrayList(productVOS));
        return response;
    }

    @Override
    public List<RenewalDTO> listRenewal(DescribeRenewRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("chargeType", "PrePaid");
        criteria.put("statusIn", normalList);
        User authUser = AuthUtil.getAuthUser();
        this.selectByProject(criteria, request.getProject(), authUser);
        cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil.preparePageParams(request, criteria, "countdown asc");
        return resRenewRefMapper.selectRenew(criteria);
    }


    @Override
    public Map<String, Object> renewDetail(String id) {
        return resourceConfigService.selectResource(id);
    }

    @Override
    public Integer selectUnifyDate(Long orgSid) {
        QueryWrapper<OrgConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_type", "UNIFY_DATE");
        queryWrapper.eq("org_sid", orgSid);
        OrgConfig orgConfig = orgConfigMapper.selectOne(queryWrapper);
        if (Objects.isNull(orgConfig)) {
            return 1;
        }
        return Integer.valueOf(orgConfig.getConfigValue());
    }

    @Override
    public void modifyUnifyDate(Long orgSid, Integer unifyDate) {
        QueryWrapper<OrgConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_type", "UNIFY_DATE");
        queryWrapper.eq("org_sid", orgSid);
        OrgConfig orgConfig = orgConfigMapper.selectOne(queryWrapper);
        if (Objects.isNull(orgConfig)) {
            OrgConfig newConfig = new OrgConfig();
            newConfig.setConfigType("UNIFY_DATE");
            newConfig.setConfigValue(unifyDate.toString());
            newConfig.setOrgSid(orgSid);
            newConfig.setConfigName("统一到期时间");
            newConfig.setConfigKey("unify_date");
            WebUserUtil.prepareInsertParams(newConfig);
            orgConfigMapper.insert(newConfig);
        } else {
            orgConfig.setConfigValue(unifyDate.toString());
            orgConfigMapper.updateById(orgConfig);
        }
    }

    @Override
    public RestResult  renewResource(ProductOperation productOperation) {
        productOperation.setOrderType("Renew");
        List<ProductInfo> productInfoList = productOperation.getProductInfo();
        if (CollectionUtil.isEmpty(productInfoList)) {
            logger.info("参数productInfo");
            return new RestResult();
        }
        ProductInfo productInfo = productInfoList.get(0);
        //产品资源id
        String productResId= productInfo.getId();
        //续订时长
        BigDecimal period = productInfo.getPeriod();
        //统一结束时间
        Integer unifyDate = productInfo.getUnifyDate();
        //产品名称
        String name = productInfo.getName();
        //产品编码
        String mainProductCode = productInfo.getProductCode();
        //判断是不是HPC专属，是HPC专属则从表里获取,否则新增产品资源对象
        SfProductResource sfProductResource =
            ProductCodeEnum.HPC_DRP.getProductType().equals(mainProductCode)
                ? sfProductResourceMapper.selectById(Long.valueOf(productResId))
                : new SfProductResource();
        if (ObjectUtils.isEmpty(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        //初始化状态
        String status = sfProductResource.getStatus();
        sfProductResource.setPreStatus(status);


        Date now = new Date();
        if(ProductCodeEnum.HPC_DRP.getProductType().equals(mainProductCode)){

            //查询集群扩容相关的合同
            List<HpcBizContractDTO> hpcBizContractDTOS = serviceOrderService.getHpcBizContractDTOList(sfProductResource.getClusterId());

            this.validFrozenSfs(sfProductResource.getClusterId());

            //获取资源对应的最新的订单详情
            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + productResId + "\"");
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew"));
            criteria.put("orderStatus", "completed");
            criteria.put("productCodeIn", Lists.newArrayList(ProductCodeEnum.HPC_DRP.getProductType(),
                    ProductCodeEnum.ECS.getProductType(),ProductCodeEnum.federationProducts(),
                    ProductCodeEnum.BMS.getProductType(),ProductCodeEnum.SFS2.getProductType(),
                    ProductCodeEnum.FLOATING_IP.getProductType()));
            criteria.put("isValid", true);
            List<ServiceOrderDetail> serviceOrderDetails = iServiceOrderPriceDetailService.selectOrderDetailByCriteria(criteria);
            if (CollectionUtil.isEmpty(serviceOrderDetails)) {
                return new RestResult();
            }
            ServiceOrderDetail lastOrderDetail = CollectionUtil.getFirst(serviceOrderDetails);
            Long orderId = lastOrderDetail.getOrderId();
            List<ServiceOrderDetail> lastOrderDetailList = serviceOrderDetails.stream().filter(od -> orderId.equals(od.getOrderId())).collect(Collectors.toList());

            //判断收费规则是否改变
            RestResult checkNonBillingProductResult = checkNonBillingProductHPCDrp(orderId,productInfoList,lastOrderDetailList,productOperation);
            if (!checkNonBillingProductResult.getStatus()){
                return checkNonBillingProductResult;
            }

            if(CollectionUtil.isNotEmpty(lastOrderDetailList)){
                for (ServiceOrderDetail serviceOrderDetail : lastOrderDetailList) {
                    String serviceType = serviceOrderDetail.getServiceType();
                    iServiceOrderPriceDetailService.setProductConfigDescNodeInfo(serviceOrderDetail);
                    ProductInfo detailProductInfo = this.buildProductInfo(serviceOrderDetail);
                    detailProductInfo.setMakeUpOrderDetailId(serviceOrderDetail.getId());
                    if(!ProductCodeEnum.HPC_DRP.getProductType().equals(serviceType)){
                        productInfoList.add(detailProductInfo);
                        detailProductInfo.setPeriod(period);
                        detailProductInfo.setUnifyDate(unifyDate);
                        detailProductInfo.setResourceId(productResId);
                        setProductInfoFromSfProductRes(detailProductInfo, sfProductResource,now);

                        //修改节点数
                        changeNodeNum(sfProductResource,serviceOrderDetail,detailProductInfo, false, hpcBizContractDTOS);
                    }else{
                        BeanUtils.copyProperties(detailProductInfo,productInfo);
                        productInfo.setPrincipal(true);
                        productInfo.setId(productResId);
                        productInfo.setResourceId(productResId);
                        productInfo.setPeriod(period);
                        productInfo.setUnifyDate(unifyDate);
                        productInfo.setName(name);
                        setProductInfoFromSfProductRes(productInfo, sfProductResource,now);
                        productInfo.setClusterId(sfProductResource.getClusterId());

                        // 续订HPC专属，在productConfigDesc记录正确的购买时长
                        JSONObject productConfigDesc = JSONUtil.parseObj(productInfo.getProductConfigDesc());
                        JSONArray jsonArray = new JSONArray();
                        for (Object jsonNode : productConfigDesc.getJSONArray("currentDesc")) {
                            JSONObject item = (JSONObject) jsonNode;
                            if ("time".equals(item.get("attrKey"))) {
                                Integer oldValue = Integer.parseInt(item.getStr("value"));
                                BigDecimal newValue = NumberUtil.add(oldValue, period);
                                item.put("value", newValue);
                                JSONObject desc = item.getJSONObject("desc");
                                desc.put("value", newValue + "个月");
                            }
                            jsonArray.add(item);
                        }
                        productConfigDesc.put("currentDesc", JSONUtil.toJsonStr(jsonArray));
                        productInfo.setProductConfigDesc(JSONUtil.toJsonStr(productConfigDesc));
                    }
                }
            }

        }
        //不是hpc专属默认设置为主资源
        if (!ProductCodeEnum.HPC_DRP.getProductType().equals(mainProductCode)) {
            //判断收费规则是否改变
            RestResult checkNonBillingProductResult = checkNonBillingProduct(productInfoList,productOperation,mainProductCode);
            if (!checkNonBillingProductResult.getStatus()){
                return checkNonBillingProductResult;
            }
            productInfoList.get(0).setPrincipal(true);
        }
        boolean multiProduct = 1 < productInfoList.size();
        HashMap<String, InquiryPriceResponse> priceInfos = new HashMap<>();
        Boolean isRds = productOperation.getProductInfo().get(0).getProductCode().equals(ProductCodeEnum.RDS.getProductType());
        productInfoList.forEach(p -> {
            //修改资源续费类型,不续费改成手动续费
            changeRenewType(p.getId());
            if(!ProductCodeEnum.HPC_DRP.getProductType().equals(mainProductCode)) {
                //完善产品信息，包括补扣天数计算和订单询价json
                this.completeProductInfo(p);
            }
            //询价请求参数
            InquiryRenewPriceRequestDTO request = new InquiryRenewPriceRequestDTO();
            request.setId(p.getId());
            request.setPeriod(p.getPeriod());
            request.setUnifyDate(p.getUnifyDate());
            request.setAccountId(productOperation.getAccountId());
            String productCode = p.getProductCode();
            ServiceOrderPriceDetail serviceOrderPriceDetail
                    = iServiceOrderPriceDetailService.selectServiceId(request.getId(),productCode);
            String serviceId = "";
            Long projectId = 0L;
            if (ProductCodeEnum.isCmpApiProduct().contains(mainProductCode)) {
                SfProductResource sfProductResource2 = sfProductResourceMapper.selectById(Long.valueOf(productResId));
                com.alibaba.fastjson.JSONObject object = com.alibaba.fastjson.JSONObject.parseObject(p.getProductConfigDesc());
                com.alibaba.fastjson.JSONArray array = object.getJSONArray("productConfigDesc");
                if (ProductCodeEnum.CCE.getProductType().equals(mainProductCode)) {
                    ResContainerCluster containerCluster = cceRemoteService.selectByPrimaryKey(sfProductResource2.getClusterId());
                    for (int i = 0; i < array.size(); i++) {
                        com.alibaba.fastjson.JSONObject item = array.getJSONObject(i);
                        String label = item.getString("label");
                        if ("集群规模".equals(label)) {
                            item.put("value", containerCluster.getSize() + "节点");
                        }
                    }
                    p.setProductConfigDesc(JSONUtil.toJsonStr(object));
                }
                if (ProductCodeEnum.FLOATING_IP.getProductType().equals(mainProductCode)) {
                    ResFloatingIp resFloatingIp = eipRemoteService.selectByPrimaryKey(sfProductResource2.getClusterId());
                    for (int i = 0; i < array.size(); i++) {
                        com.alibaba.fastjson.JSONObject item = array.getJSONObject(i);
                        String label = item.getString("label");
                        if ("带宽大小".equals(label)) {
                            item.put("value", resFloatingIp.getBandWidth());
                        }
                    }
                    p.setProductConfigDesc(JSONUtil.toJsonStr(object));
                }
                if (ProductCodeEnum.DISK.getProductType().equals(mainProductCode)) {
                    ResVd resVd = ebsRemoteService.selectByPrimaryKey(String.valueOf(sfProductResource2.getClusterId()));
                    for (int i = 0; i < array.size(); i++) {
                        com.alibaba.fastjson.JSONObject item = array.getJSONObject(i);
                        String label = item.getString("label");
                        if ("存储大小(GB)".equals(label)) {
                            item.put("value", resVd.getSize());
                        }
                    }
                    p.setProductConfigDesc(JSONUtil.toJsonStr(object));
                }
                if (ProductCodeEnum.DCS.getProductType().equals(mainProductCode)) {
                    ResDcs resDcs = dcsRemoteService.selectByPrimaryKey(sfProductResource2.getClusterId());
                    for (int i = 0; i < array.size(); i++) {
                        com.alibaba.fastjson.JSONObject item = array.getJSONObject(i);
                        String label = item.getString("label");
                        if ("存储大小(GB)".equals(label)) {
                            item.put("value", resDcs.getCapacity());
                        }
                    }
                    p.setProductConfigDesc(JSONUtil.toJsonStr(object));
                    //request.setQuantity(p.getQuantity());
                }
            }



            if (!ProductCodeEnum.SFS2.getProductType().equals(productCode)
                && Objects.nonNull(serviceOrderPriceDetail)) {
                serviceId = serviceOrderPriceDetail.getServiceId();
                projectId = serviceOrderPriceDetail.getOrgSid();
            }
            if (!Strings.isNullOrEmpty(serviceId)) {
                request.setServiceId(serviceId);
            }
            if (StrUtil.isEmpty(request.getServiceId()) && StrUtil.isNotEmpty(p.getServiceId())) {
                request.setServiceId(p.getServiceId());
            }
            request.setProductCode(productCode);
            if (!multiProduct) {
                // 优惠卷
                request.setCouponId(productOperation.getCouponSid());
            }
            // 合同价
            request.setContractId(p.getContractId());
            request.setContractPrice(p.getContractPrice());
            InquiryPriceResponse inquiryPriceResponse = null;
            BigDecimal expiredUsedAmount=new BigDecimal(0);
            SfProductResource sf = sfProductResourceMapper.selectById(p.getId());
            if(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(mainProductCode)){
                p.setClusterId(sf.getClusterId());
                if(ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sf.getMaVersion()))){
                  p.setApplyType(ApplyTypeEnum.DEPTRAIN.getType());
                    ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(sf.getClusterId());
                    if(Objects.nonNull(resMaPool)){
                        p.setQuantity(resMaPool.getMaxCount());
                    }
                }else{
                    p.setApplyType(ApplyTypeEnum.DEPONLINE.getType());
                }
            } else if (ProductCodeEnum.SFS2.getProductType().equals(mainProductCode)) {
                p.setClusterId(Long.valueOf(productResId));
            }
            if(!ProductCodeEnum.HPC_DRP.getProductType().equals(mainProductCode)){
                inquiryPriceResponse = inquiryPriceService.inquiryRenewPrice(request);
            }else{
                inquiryPriceResponse = inquiryPriceService.getInquiryPriceResponse(request,p);
            }
            priceInfos.put(p.getId(), inquiryPriceResponse);
            List<BizBillingPriceVO> billingPrices = inquiryPriceResponse.getBillingPrices();

            p.setId(productResId);
            if (ProductCodeEnum.ECS.getProductType().equals(mainProductCode)
                    || ProductCodeEnum.RS_BMS.getProductType().equals(mainProductCode)) {
                p.setId(p.getResourceId());
            }
            p.setServices(BeanConvertUtil.convert(
                    billingPrices, ProductService.class));
            //折扣金额,包括平台折扣和客户折扣
            p.setPlatformDiscount(inquiryPriceResponse.getPlatformDiscount());
            //平台折扣率
            p.setFloatingRatio(inquiryPriceResponse.getFloatingRatio());
            //客户折扣率
            p.setDiscountRatio(null);
            //资源单价
            p.setPrice(inquiryPriceResponse.getResourceAmount());
            //支付金额
            p.setFinalCost(inquiryPriceResponse.getResourceAmount());
            //原始金额
            p.setServiceAmount(inquiryPriceResponse.getServiceAmount());
            p.setNow(inquiryPriceResponse.getNow());
            //过期补扣金额
            p.setExpiredUsedAmount(inquiryPriceResponse.getExpiredUsedAmount());
            p.setDiscount(inquiryPriceResponse.getDiscountPrice());
            p.setOriginalPrice(inquiryPriceResponse.getOriginalPrice());
            if (StrUtil.isNotEmpty(serviceId)) {
                p.setServiceId(serviceId);
            }
            if (Objects.isNull(p.getProjectId())) {
                p.setProjectId(projectId);
            }
            productOperation.setPlatformDiscount(inquiryPriceResponse.getPlatformDiscount());
            sfProductResource.setId(Convert.toLong(p.getId()));
            //HPC专属资源池直接出账，修改sfProductResource在oss处理
            if(!ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(mainProductCode)){
                if(Objects.nonNull(sf)){
                    sfProductResource.setPreStatus(sf.getStatus());
                }
                sfProductResource.setStatus(SfProductEnum.RENEWING.getStatus());
            }
        });
        if (multiProduct && Objects.nonNull(productOperation.getCouponSid())) {
            List<InquiryPriceResponse> result = Lists.newArrayList();
            for (Entry<String, InquiryPriceResponse> entry : priceInfos.entrySet()) {
                result.add(entry.getValue());
            }
            inquiryPriceService.multipleProductCouponPrice(result, bizCouponMapper.selectById(productOperation.getCouponSid()).getDiscountAmount());
        }
        if(!isRds){
            productInfoList.forEach(p -> {
                InquiryPriceResponse inquiryPriceResponse = priceInfos.get(p.getId());
                p.setServices(BeanConvertUtil.convert(inquiryPriceResponse.getBillingPrices(), ProductService.class));
                //折扣金额,包括平台折扣和客户折扣
                p.setPlatformDiscount(inquiryPriceResponse.getPlatformDiscount());
                //平台折扣率
                p.setFloatingRatio(inquiryPriceResponse.getFloatingRatio());
                //客户折扣率
                p.setDiscountRatio(null);
                //资源单价
                p.setPrice(inquiryPriceResponse.getResourceAmount());
                //支付金额
                p.setFinalCost(inquiryPriceResponse.getResourceAmount());
                if (ProductCodeEnum.ECS.getProductType().equals(mainProductCode)
                        || ProductCodeEnum.RS_BMS.getProductType().equals(mainProductCode)) {
                    p.setFinalCost(inquiryPriceResponse.getTradePrice());
                }
                //原始金额
                p.setServiceAmount(inquiryPriceResponse.getServiceAmount());
                p.setNow(inquiryPriceResponse.getNow());
                //过期补扣金额
                p.setExpiredUsedAmount(inquiryPriceResponse.getExpiredUsedAmount());
                p.setDiscount(NumberUtil.sub(inquiryPriceResponse.getDiscountPrice(), inquiryPriceResponse.getCouponAmount()));
                p.setOriginalPrice(inquiryPriceResponse.getOriginalPrice());
            });
        }


        // HPC专属资源池优惠卷单独处理
        RestResult restResult = orderService.renewResource(productOperation);
        if (restResult.getStatus()) {
            //HPC专属资源池直接出账，修改sfProductResource在oss处理
            if (!ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(mainProductCode)) {
                if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(mainProductCode)
                        || ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(mainProductCode)) {
                    productInfoList.forEach(e -> {
                        SfProductResource sfProductResource1 = sfProductResourceMapper.selectById(Long.valueOf(e.getId()));
                        sfProductResource1.setPreStatus(sfProductResource1.getStatus());
                        sfProductResource1.setStatus(SfProductEnum.RENEWING.getStatus());
                        sfProductResourceMapper.updateById(sfProductResource1);
                    });
                }
                else if (!ProductCodeEnum.SFS.getProductType().equalsIgnoreCase(mainProductCode)
                        && !ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(mainProductCode)) {
                sfProductResourceMapper.updateById(sfProductResource);
            }

                if (ProductCodeEnum.SFS.getProductType().equalsIgnoreCase(mainProductCode)
                        || ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(mainProductCode)) {
                    handleHpcClusterResource(sfProductResource);
                }
            }
        }
        return restResult;
    }

    /**
     * 续订SFS的同时，解冻其挂载的且未过期的hpc资源
     * @param sfProductResource 产品对应资源信息
     */
    private void handleHpcClusterResource(SfProductResource sfProductResource) {
        RestResult hpcClusterResult = hpcClusterService.getResHpcClusterByShareId(
                sfProductResource.getId());
        if (Objects.nonNull(hpcClusterResult)) {
            Object resultData = hpcClusterResult.getData();
            List list = BeanConvertUtil.convert(resultData, List.class);
            List<ResHpcClusterRemoteModule> hpcClusterRemoteModules = BeanConvertUtil.convert(list, ResHpcClusterRemoteModule.class);
            hpcClusterRemoteModules.stream()
                    .filter(hpcClusterRemoteModule -> hpcClusterRemoteModule.getEndTime().after(new Date()))
                    .forEach(hpcClusterRemoteModule -> {
                        log.info("更新HPC资源池状态:{}", hpcClusterRemoteModule.getId());
                        UpdateResHpcClusterRequest updateResHpcClusterRequest = new UpdateResHpcClusterRequest();
                        updateResHpcClusterRequest.setClusterId(hpcClusterRemoteModule.getId());
                        updateResHpcClusterRequest.setStatus("available");
                        RestResult restResult = hpcClusterService.updateHpcCluster(updateResHpcClusterRequest);

                        Long orgSid = hpcClusterRemoteModule.getOrgSid();
                        // 更新ldap状态
                        log.info("同步资源到ldap:{}", orgSid);
                        cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest ldapSyncRequest = new cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest();
                        ldapSyncRequest.setOrgId(orgSid);
                        ldapUserService.synHPCTOLdap(ldapSyncRequest);
                    });
        }
    }

    /**
     * HPC下有冻结的文件系统时，不能续订HPC
     * @param clusterId
     */
    private void validFrozenSfs(Long clusterId) {
        RestResult restResult = hpcClusterService.validFrozenSfs(clusterId);
        Boolean haveFrozenSfs = (Boolean) restResult.getData();
        if (haveFrozenSfs) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_RENEW_HPC_DRP_ON_FROZEN_SFS));
        }
    }

    private void checkBalance(ProductOperation productOperation, InquiryPriceResponse inquiryPriceResponse) {
        List<ServiceCategory> categories = serviceCategoryMapper.getServiceCategoryByServiceType(inquiryPriceResponse.getProductCode());
        if (categories == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2081550435));
        }

        BizBillingAccount billingAccount = bizBillingAccountMapper
                .getBillingAccountByUserIdAndEntitytId(productOperation.getConsumer().getUserSid(), categories.get(0).getEntityId());
        BigDecimal balance = billingAccount.getBalance();
        BigDecimal balanceCash = billingAccount.getBalanceCash();
        BigDecimal creditLine = billingAccount.getCreditLine();
        BigDecimal totalAccount = BigDecimal.ZERO;
        if (Objects.nonNull(balance)) {
            totalAccount = totalAccount.add(balance);
        }
        if (Objects.nonNull(balanceCash)) {
            totalAccount = totalAccount.add(balanceCash);
        }
        if (Objects.nonNull(creditLine)) {
            totalAccount = totalAccount.add(creditLine);
        }

        BigDecimal tradePrice = inquiryPriceResponse.getTradePrice();
        if (Objects.nonNull(tradePrice) && totalAccount.compareTo(tradePrice) < 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_13080022));
        }
    }

    private void setProductInfoFromSfProductRes(ProductInfo productInfo, SfProductResource sfProductResource,Date now) {
        productInfo.setEndTime(sfProductResource.getEndTime());

        productInfo.setStatus(sfProductResource.getStatus());
        productInfo.setFrozenTime(sfProductResource.getFrozenTime());
    }


    @Override
    public void changeNodeNum(SfProductResource sfProductResource, ServiceOrderDetail serviceOrderDetail,
                              ProductInfo productInfo, boolean compareFlage, List<HpcBizContractDTO> hpcBizContractDTOS) {
        //查询当前节点数，nodeInfo中的节点规格，同一种类型的节点规格一致。
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        if (productInfo.getProductCode().equals(ProductCodeEnum.BMS.getProductType())) {
            nodeInfoList.addAll(resBmsRemoteService.getNodeInfoListByClusterId(sfProductResource.getClusterId()));
            hpcBizContractDTOS = hpcBizContractDTOS.stream().filter(dto -> !dto.isZeroNode()).collect(Collectors.toList());
            int allContractAgentNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 0);
            //当合同中剩余节点数大于0时减去对应的节点数，
            if(allContractAgentNodeNum >0){
                nodeInfoList = nodeInfoList.stream().limit(nodeInfoList.size()-allContractAgentNodeNum).collect(Collectors.toList());
            }
            changeQuantity(serviceOrderDetail, productInfo, nodeInfoList, compareFlage);
        }
        if (productInfo.getProductCode().equals(ProductCodeEnum.ECS.getProductType())) {
            nodeInfoList.addAll(resVmRemoteService.getNodeInfoListByClusterId(sfProductResource.getClusterId()));
            int allContractCliNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 1);
            if (allContractCliNodeNum > 0) {
                List<NodeInfo> cliNodeInfoList = nodeInfoList.stream().filter(hpcNode -> HpcPointType.CCS_CLI.equals(hpcNode.getHpcPointType())).collect(Collectors.toList());
                int remainNodeNum = cliNodeInfoList.size() - allContractCliNodeNum;
                //清空登陆节点
                nodeInfoList.removeIf(nodeInfo -> HpcPointType.CCS_CLI.equals(nodeInfo.getHpcPointType()));
                if (remainNodeNum > 0) {
                    cliNodeInfoList = cliNodeInfoList.stream().limit(remainNodeNum).collect(Collectors.toList());
                    nodeInfoList.addAll(cliNodeInfoList);
                }
            }
            int allContractVNCNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 2);
            if (allContractVNCNodeNum > 0) {
                List<NodeInfo> vncNodeInfoList = nodeInfoList.stream().filter(hpcNode -> HpcPointType.VNC.equals(hpcNode.getHpcPointType())).collect(Collectors.toList());
                int remainNodeNum = vncNodeInfoList.size() - allContractVNCNodeNum;
                nodeInfoList.removeIf(nodeInfo -> HpcPointType.VNC.equals(nodeInfo.getHpcPointType()) );
                //清空VNC节点
                if (remainNodeNum > 0) {
                    vncNodeInfoList = vncNodeInfoList.stream().limit(remainNodeNum).collect(Collectors.toList());
                    nodeInfoList.addAll(vncNodeInfoList);
                }
            }
            changeQuantity(serviceOrderDetail, productInfo, nodeInfoList, compareFlage);
        }
    }

    /**
     * hpc专属资源池修改节点数量
     * @param serviceOrderDetail
     * @param productInfo
     * @param nodeInfoList
     */
    private void changeQuantity(ServiceOrderDetail serviceOrderDetail, ProductInfo productInfo,
                                List<NodeInfo> nodeInfoList, boolean compareFlage) {
        String serviceConfig = serviceOrderDetail.getServiceConfig();
        if (StringUtil.isNotEmpty(serviceConfig)) {
            ProductInfoVO productInfoVO = com.alibaba.fastjson.JSONObject.parseObject(serviceConfig, ProductInfoVO.class);
            long count = nodeInfoList.stream().filter(node -> StringUtil.equals(node.getTypeName(), productInfoVO.getProductCategory())).count();
            //当前节点数小于最后续订的节点数，更新节点数
            boolean flag = productInfo.getQuantity() > count;
            if (compareFlage) {
                flag = productInfo.getQuantity() != count;
            }
            if(flag){
                productInfo.setQuantity((int)count);
                String config = productInfo.getConfig();
                if (StringUtil.isNotEmpty(config)) {
                    com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(config);
                    jsonObject.put("amount",count);
                    productInfo.setConfig(jsonObject.toJSONString());
                }
            }
        }
    }

    //根据订单详情够成ProductInfoVO
    @Override
    public ProductInfo buildProductInfo(ServiceOrderDetail serviceOrderDetail) {

        if (ObjectUtils.isEmpty(serviceOrderDetail)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
        }

        cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(serviceOrderDetail.getServiceConfig());

        Map<String, Object> detail = Maps.newHashMap();
        detail.put("chargeType", serviceOrderDetail.getChargeType());
        detail.put("cloudEnvId", serviceConfig.getStr("cloudEnvId"));
        detail.put("serviceId", serviceOrderDetail.getServiceId().toString());
        detail.put("productCode", serviceOrderDetail.getServiceType());

        detail.put("inquiryJson", serviceConfig.toString());
        detail.put("endTime", cn.hutool.core.date.DateUtil.format(serviceOrderDetail.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        detail.put("startTime", cn.hutool.core.date.DateUtil.format(serviceOrderDetail.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        Date now = new Date();
        if (serviceOrderDetail.getEndTime().before(now)) {

            DateTime date = DateUtil.dateNew(now);
            DateTime endTime = DateUtil.date(serviceOrderDetail.getEndTime());
            date.setField(DateField.HOUR_OF_DAY, endTime.getField(DateField.HOUR_OF_DAY));
            date.setField(DateField.MINUTE, endTime.getField(DateField.MINUTE));
            date.setField(DateField.SECOND, endTime.getField(DateField.SECOND));
            date.setField(DateField.MILLISECOND, endTime.getField(DateField.MILLISECOND));
            while (date.before(now)) {
                date = DateUtil.offsetDay(date, 1);
            }
            detail.put("now", DateUtil.format(date, "yyyy年MM月dd日 HH:mm:ss"));
        }
        detail.put("currentDate", DateUtil.format(now, "yyyy年MM月dd日 HH:mm:ss"));
        Object byPath = serviceConfig.getByPath("productConfigDesc.currentConfigDesc");

        detail.put("config", byPath !=null?byPath:serviceOrderDetail.getProductConfigDesc());
        detail.put("period", serviceConfig.getStr("period"));
        detail.put("projectId", serviceOrderDetail.getOrgSid());
        detail.put("quantity", serviceOrderDetail.getQuantity());
        detail.put("orgSid", serviceOrderDetail.getOrgSid());

        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductCode(serviceOrderDetail.getServiceType());
        this.mapToProductInfo(detail,productInfo);

        return productInfo;
    }


    @Override
    public void completeProductInfo(ProductInfo productInfo) {
        if (Strings.isNullOrEmpty(productInfo.getId())) {
            ServiceOrderDetail orderDetail = serviceOrderDetailService.getById(
                productInfo.getOrderDetailId());
            if (ProductCodeEnum.ECS.getProductType().equals(productInfo.getProductCode())) {
                ServiceOrder serviceOrder = serviceOrderService.getById(orderDetail.getOrderId());
                orderDetail.setClusterId(serviceOrder.getClusterId());
                iServiceOrderPriceDetailService.setProductConfigDescNodeInfo(orderDetail);
                QueryWrapper<SfProductResource> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("service_order_id", orderDetail.getOrderId());

                SfProductResource sfProductResource = sfProductResourceMapper.selectOne(queryWrapper);

                //查询集群扩容相关的合同
                List<HpcBizContractDTO> hpcBizContractDTOS = serviceOrderService.getHpcBizContractDTOList(sfProductResource.getClusterId());
                changeNodeNum(sfProductResource, orderDetail, productInfo, true, hpcBizContractDTOS);
            }
            if (Objects.nonNull(orderDetail)) {
                BeanUtil.copyProperties(this.buildProductInfo(orderDetail), productInfo, "id",
                    "price", "finalCost", "expiredUsedAmount", "orderDetailId", "serviceAmount");
            }
            return;
        }
        Map<String, Object> detail=null;
        //RDS 特殊处理
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(productInfo.getId());
        String productCode = productInfo.getProductCode();
        if(sfProductResource.getProductType().equals(ProductCodeEnum.RDS.getProductType())){
            detail=this.renewInnerProductDetail(productInfo.getId(), true,productCode);
        }else {
             detail = ProductCodeEnum.isInnerProduct(productInfo.getProductCode())
                    || ProductCodeEnum.HPC_DRP.getProductType().equals(productInfo.getProductCode())
                    || ProductCodeEnum.isCmpApiProduct().contains(productInfo.getProductCode())
                    || ProductCodeEnum.federationProducts().contains(productInfo.getProductCode())
                    ? this.renewInnerProductDetail(productInfo.getId(), true)
                    : this.renewDetail(productInfo.getId());
        }
        //detail 映射到productInfo
        mapToProductInfo(detail,productInfo);
        if(sfProductResource.getProductType().equals(ProductCodeEnum.RDS.getProductType())){
            productInfo.setProductCode(productCode);
        }

    }




    @Override
    public void mapToProductInfo(Map<String, Object> detail,ProductInfo productInfo) {
        if (CollectionUtils.isEmpty(detail)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_537059183));
        }
        if (Objects.isNull(detail.get("chargeType"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_693266500));
        }
        productInfo.setPublic(
                Boolean.valueOf(BeanConvertUtil.getValue(detail, "isPublic", "false").toString()));
        productInfo.setChargeType(BeanConvertUtil.getValue(detail, "chargeType", "").toString());
        //云环境id
        productInfo.setCloudEnvId(detail.get("cloudEnvId").toString());
        //资源id
        productInfo.setResourceId(BeanConvertUtil.getValue(detail, "resourceId", "").toString());
        //服务id
        productInfo.setServiceId(Objects.nonNull(detail.get("serviceId")) ? detail.get("serviceId").toString() : null);
        //产品编码
        productInfo.setProductCode(detail.get("productCode").toString());
        productInfo.setCurrentType(BeanConvertUtil.getValue(detail, "instanceType", "").toString());
        //订单询价json
        productInfo.setConfig(detail.get("inquiryJson").toString());
        //时长
        if (Objects.isNull(productInfo.getPeriod())) {
            productInfo.setPeriod(new BigDecimal(BeanConvertUtil.getValue(detail, "period", "1").toString()));
        }
        if (Objects.nonNull(detail.get("size"))) {
            productInfo.setCurrentSize(Integer.valueOf(detail.get("size").toString()));
        }
        //组织id
        if (Objects.nonNull(detail.get("projectId"))) {
            productInfo.setProjectId(Convert.toLong(detail.get("projectId")));
        }
        if (Objects.nonNull(detail.get("orgSid"))) {
            productInfo.setOrgSid(Convert.toLong(detail.get("orgSid")));
        }
        if (Objects.nonNull(detail.get("serviceId"))) {
            productInfo.setServiceId(Convert.toStr(detail.get("serviceId")));
        }
        if (Objects.nonNull(detail.get("quantity"))) {
            productInfo.setQuantity(Convert.toInt(detail.get("quantity")));
            if (ProductCodeEnum.isCmpApiProduct().contains(productInfo.getProductCode())/* && !ProductCodeEnum.DCS.getProductType().equals(productInfo.getProductCode())*/) {
                productInfo.setQuantity(1);
            }
        }
        if (Objects.nonNull(detail.get("name"))) {
            productInfo.setName(Convert.toStr(detail.get("name")));
        }
        if (Objects.nonNull(detail.get("status"))) {
            productInfo.setStatus(Convert.toStr(detail.get("status")));
        }
        //开始时间
        if (Objects.nonNull(detail.get("startTime"))) {
            DateTime time = DateTime.of(detail.get("startTime").toString(), "yyyy年MM月dd日 HH:mm:ss");
            productInfo.setStartTime(time.toJdkDate());
        }
        if (Objects.nonNull(detail.get("now"))) {
            DateTime time = DateTime.of(detail.get("now").toString(), "yyyy年MM月dd日 HH:mm:ss");
            productInfo.setNow(time.toJdkDate());
        }
        if (Objects.nonNull(detail.get("currentDate"))) {
            DateTime time = DateTime.of(detail.get("currentDate").toString(), "yyyy年MM月dd日 HH:mm:ss");
            productInfo.setCurrentDate(time.toJdkDate());
        }
        if (Objects.nonNull(detail.get("frozenTime"))) {
            DateTime time = DateTime.of(detail.get("frozenTime").toString(), "yyyy年MM月dd日 HH:mm:ss");
            productInfo.setFrozenTime(time.toJdkDate());
        }
        if (Strings.isNullOrEmpty(productInfo.getProductConfigDesc())) {
            JSONObject jsonObject = new JSONObject();
            if (ProductCodeEnum.isCmpApiProduct().contains(productInfo.getProductCode())
                    ||ProductCodeEnum.federationProducts().contains(productInfo.getProductCode())) {
                // 变更后当前配置改变
                String config = detail.get("config").toString();
                if (Objects.nonNull(detail.get("clusterId"))) {
                    config = newConfigurationReplacement(Long.valueOf(detail.get("clusterId").toString()),
                                                productInfo.getProductCode(), config);
                }
                productInfo.setProductConfigDesc(config);
            } else {
                jsonObject.put("currentDesc", JSONUtil.toList((String) detail.get("config"), Object.class));
                if (!Strings.isNullOrEmpty(productInfo.getTargetType())
                        || Objects.nonNull(productInfo.getTargetSize())) { // dcs
                    jsonObject.put("changeDesc", selectChangeDesc(productInfo, detail));
                }
                productInfo.setProductConfigDesc(JSONUtil.toJsonStr(jsonObject));
            }
        }

        //modelarts变更配置，记录变更的数量
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productInfo.getTargetType())
                && Objects.nonNull(productInfo.getTargetSize())){
            JSONObject serviceConfig = JSONUtil.parseObj(detail.get("inquiryJson").toString());
            serviceConfig.put("amount",productInfo.getTargetSize());

            if (!ApplyTypeEnum.DEPTRAIN.getType().equals(serviceConfig.get("applyType"))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2087343772));
            }
            JSONObject productConfigDesc = serviceConfig.getJSONObject("productConfigDesc");
            JSONArray currentConfigDesc = productConfigDesc.getJSONArray("currentConfigDesc");

            JSONObject attrKey = (JSONObject)currentConfigDesc.stream().filter(currentConfig -> {
                JSONObject jsonObject = (JSONObject) currentConfig;
                return "count".equals(jsonObject.getStr("attrKey"));
            }).collect(Collectors.toList()).get(0);

            String attrKeyStr = attrKey.getStr("value");
            Integer value;
            if (attrKeyStr.contains("/")){
                String[] split = attrKeyStr.split("/");
                value = Integer.parseInt(split[0]);
            }else {
                value = Integer.parseInt(attrKeyStr);
            }
            if (productInfo.getTargetSize().compareTo(value) > 0){
                attrKey.put("value", value + "/" + productInfo.getTargetSize());
            }
            productConfigDesc.put("currentConfigDesc",JSONUtil.toJsonStr(currentConfigDesc));
            serviceConfig.put("productConfigDesc",productConfigDesc);

            productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));

            int changeNum = Math.abs(productInfo.getTargetSize() - value);
            productInfo.setQuantity(changeNum);

            if (value.compareTo(productInfo.getTargetSize()) > 0) {
                productInfo.setPeriod(productInfo.getPeriod().subtract(BigDecimal.ONE));
            }
        }
        else if (ProductCodeEnum.isCmpApiProduct().contains(productInfo.getProductCode())){
            productInfo.setConfig(resourceConfigService.updateCurrentSpec(detail.get("inquiryJson").toString(), productInfo.getProductCode(), Long.valueOf(productInfo.getId())));
        }
        String endTime = "";
        if (Objects.nonNull(detail.get("endTime"))) {
            endTime = detail.get("endTime").toString();
        }
        if (Strings.isNullOrEmpty(endTime)) {
            return;
        }
        DateTime time = DateTime.of(endTime, "yyyy年MM月dd日 HH:mm:ss");
        //结束时间
        productInfo.setEndTime(time.toJdkDate());
    }

    /**
     * 获取变配的新配置替换
     */
    @Override
    public String newConfigurationReplacement(Long clusterId, String productCode, String config) {
        if (Objects.nonNull(clusterId)) {
            List<ServiceOrderVo> serviceOrderVos = serviceOrderService.selectOrderByCluster(clusterId, productCode);
            List<ServiceOrderVo> modifys = serviceOrderVos.stream()
                                                          .filter(t -> "modify".equals(t.getType())
                                                                  && "completed".equals(t.getStatus()))
                                                          .sorted((e1, e2) -> e2.getCreatedDt()
                                                                                .compareTo(e1.getCreatedDt()))
                                                          .collect(Collectors.toList());
            if (modifys.size() > 0) {
                ServiceOrderVo serviceOrderVo = modifys.get(0);
                ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectOne(
                        new QueryWrapper<ServiceOrderDetail>().lambda()
                                                              .eq(ServiceOrderDetail::getOrderId,
                                                                  serviceOrderVo.getId()));
                if (Objects.nonNull(serviceOrderDetail)) {
                    com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(
                            serviceOrderDetail.getProductConfigDesc());
                    String modifyConfigDesc1 = jsonObject1.get("modifyConfigDesc").toString();
                    com.alibaba.fastjson.JSONObject modifyConfigDesc = com.alibaba.fastjson.JSONObject.parseObject(
                            modifyConfigDesc1);
                    String newDesc = modifyConfigDesc.get("newDesc").toString();
                    com.alibaba.fastjson.JSONArray newDescArray = com.alibaba.fastjson.JSONObject.parseArray(
                            newDesc);
                    com.alibaba.fastjson.JSONObject configs = com.alibaba.fastjson.JSONObject.parseObject(config);
                    com.alibaba.fastjson.JSONArray productConfigDesc = configs.getJSONArray(
                            "productConfigDesc");
                    for (int i = 0; i < productConfigDesc.size(); i++) {
                        Object oldObject = productConfigDesc.get(i);
                        com.alibaba.fastjson.JSONObject oldConfig = com.alibaba.fastjson.JSONObject.parseObject(
                                oldObject.toString());
                        // 用变配中的新配置替换
                        for (Object newObject : newDescArray) {
                            com.alibaba.fastjson.JSONObject newConfig = com.alibaba.fastjson.JSONObject.parseObject(
                                    newObject.toString());
                            if (!oldConfig.containsKey("label")) {
                                continue;
                            }
                            if (oldConfig.get("label")
                                         .toString()
                                         .equalsIgnoreCase(newConfig.get("label").toString())) {
                                productConfigDesc.remove(i);
                                productConfigDesc.add(i, newObject);
                            }
                        }
                    }
                    configs.put("productConfigDesc", productConfigDesc);
                    return configs.toJSONString();
                }
            }
        }
        return config;
    }

    @Override
    public Map<String, Object> renewInnerProductDetail(String id, Boolean dealType) {
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(id);
        if (Objects.isNull(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        User authUser = AuthUtil.getAuthUser();
        //运营管理员不检查权限
        if(!(UserType.PLATFORM_USER.equals(authUser.getUserType()) && Objects.isNull(authUser.getOrgSid())) && !authUser.getAccount().equals(sfProductResource.getCreatedBy())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        ServiceOrderDetail applyDetail = null;
        if (dealType && ProductCodeEnum.HPC_DRP.getProductType().equals(sfProductResource.getProductType())) {
            //获取资源对应的最新的订单详情
            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + sfProductResource.getId() + "\"");
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", OrderType.UPGRADE, OrderType.DEGRADE));
            criteria.put("orderStatus", "completed");
            criteria.put("productCodeIn", Lists.newArrayList(ProductCodeEnum.HPC_DRP.getProductType()));
            criteria.put("isValid", true);
            List<ServiceOrderDetail> serviceOrderDetails = iServiceOrderPriceDetailService.selectOrderDetailByCriteria(criteria);
            if (ObjectUtils.isEmpty(serviceOrderDetails)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
            }
            applyDetail = CollectionUtil.getFirst(serviceOrderDetails);
            if (ObjectUtils.isEmpty(applyDetail)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
            }
            iServiceOrderPriceDetailService.setProductConfigDescNodeInfo(applyDetail);
        } else {
            if (ObjectUtils.isEmpty(sfProductResource)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_424216285));
            }
            LambdaQueryWrapper<ServiceOrderResourceRef> queryWrapper = Wrappers
                    .<ServiceOrderResourceRef>lambdaQuery().eq(ServiceOrderResourceRef::getResourceId, id)
                    .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType());
            List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper
                    .selectList(queryWrapper);
            if (ObjectUtils.isEmpty(serviceOrderResourceRefs)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
            }
            applyDetail = serviceOrderDetailService
                    .getById(CollectionUtil.getFirst(serviceOrderResourceRefs).getOrderDetailId());
            if (ObjectUtils.isEmpty(applyDetail)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
            }
        }
        return productDetail(sfProductResource, applyDetail);
    }

    @Override
    public Map<String, Object> renewInnerProductDetail(String id, Boolean dealType,String productCode) {
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(id);
        if (Objects.isNull(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        User authUser = AuthUtil.getAuthUser();
        //运营管理员不检查权限
        if(!(UserType.PLATFORM_USER.equals(authUser.getUserType()) && Objects.isNull(authUser.getOrgSid())) && !authUser.getAccount().equals(sfProductResource.getCreatedBy())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        ServiceOrderDetail applyDetail = null;
        if (ObjectUtils.isEmpty(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_424216285));
        }
        LambdaQueryWrapper<ServiceOrderResourceRef> queryWrapper = Wrappers
                .<ServiceOrderResourceRef>lambdaQuery().eq(ServiceOrderResourceRef::getResourceId, id)
                .eq(ServiceOrderResourceRef::getType, productCode);
        List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper
                .selectList(queryWrapper);
        if (ObjectUtils.isEmpty(serviceOrderResourceRefs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
        }
        applyDetail = serviceOrderDetailService
                .getById(CollectionUtil.getFirst(serviceOrderResourceRefs).getOrderDetailId());
        if (ObjectUtils.isEmpty(applyDetail)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
        }
        return productDetail(sfProductResource, applyDetail);
    }

    private Map<String, Object> productDetail(SfProductResource sfProductResource, ServiceOrderDetail applyDetail) {
        Map<String, Object> detail = Maps.newHashMap();
        detail.put("chargeType", applyDetail.getChargeType());
        detail.put("cloudEnvId", sfProductResource.getCloudEnvId());
        detail.put("resourceId", sfProductResource.getId());
        detail.put("serviceId", applyDetail.getServiceId().toString());
        detail.put("productCode", sfProductResource.getProductType());
        JSONObject serviceConfig = JSONUtil.parseObj(applyDetail.getServiceConfig());
        detail.put("inquiryJson", serviceConfig.toString());
        detail.put("endTime", ChargeTypeEnum.PREPAID.getValue().equals(applyDetail.getChargeType()) ? DateUtil.format(sfProductResource.getEndTime(), "yyyy年MM月dd日 HH:mm:ss") : null);
        detail.put("startTime", DateUtil.format(sfProductResource.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        detail.put("status",sfProductResource.getStatus());
        Date now = new Date();
        detail.put("period", ChargeTypeEnum.PREPAID.getValue().equals(applyDetail.getChargeType()) ? DateUtil.betweenMonth(now, sfProductResource.getEndTime(), false) + 1 : null);

        if ("frozen".equalsIgnoreCase(sfProductResource.getStatus())) {
            detail.put("frozenTime", DateUtil.format(sfProductResource.getFrozenTime(), "yyyy年MM月dd日 HH:mm:ss"));
            Integer offDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.calculateOffDay(sfProductResource.getEndTime(),
                    sfProductResource.getFrozenTime());
            detail.put("makeUpDays", offDay+"天");
        }else{
            if (Objects.nonNull(sfProductResource.getEndTime()) && sfProductResource.getEndTime().before(now)) {
                Integer offDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.calculateOffDay(sfProductResource.getEndTime(), now);
                detail.put("makeUpDays", offDay+"天");
            }
        }

        // 现在modelarts专属也有冻结状态
        if (Objects.nonNull(sfProductResource.getEndTime()) && sfProductResource.getEndTime().before(now)) {
            detail.put("now", DateUtil.format(now, "yyyy年MM月dd日 HH:mm:ss"));
        }

        detail.put("clusterId", sfProductResource.getClusterId());
        detail.put("config", this.getConfig(sfProductResource, serviceConfig.getByPath("productConfigDesc.currentConfigDesc")));
        detail.put("projectId", sfProductResource.getOrgSid());
        detail.put("quantity", applyDetail.getQuantity());

        ServiceOrder serviceOrder = serviceOrderService.getById(applyDetail.getOrderId());
        detail.put("name", serviceOrder.getName());
        return detail;
    }

    private String getConfig(SfProductResource sfProductResource, Object currentConfigDesc) {
        if (currentConfigDesc == null) {
            return "";
        }
        Long clusterId = sfProductResource.getClusterId();
        String config = JSONUtil.toJsonStr(currentConfigDesc);
        List<BizContractDetail> contractDetails = contractDetailMapper.selectBizContractDetailByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(contractDetails)) {
            int cliNum = 0, agentNum = 0;
            for (BizContractDetail contractDetail : contractDetails) {
                cliNum += Optional.ofNullable(contractDetail.getCliNumberOpen()).orElse(0)  -  Optional.ofNullable(contractDetail.getCliNumberRenew()).orElse(0);
                agentNum += Optional.ofNullable(contractDetail.getAgentNumberOpen()).orElse(0)  -  Optional.ofNullable(contractDetail.getAgentNumberRenew()).orElse(0);
            }

            JSONArray array = JSONUtil.parseArray(config);
            if (cliNum > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    if (object.getStr("attrKey").equals(LOGIN_NODE_INFO)) {
                        JSONArray chidrenDesc = object.getJSONArray("chidrenDesc");
                        if (Objects.nonNull(chidrenDesc)) {
                            for (Object data : (JSONArray) chidrenDesc.get(0)) {
                                JSONObject label = (JSONObject) data;
                                if ("节点数目".equals(label.get("label"))) {
                                    Integer value = label.getInt("value");
                                    label.put("value", value - cliNum);
                                    logger.info("ResRenewRefServiceImpl.getConfig 登录节点去掉 【{}】 个", cliNum);
                                }
                            }
                        }
                        break;
                    }
                }
            }

            if (agentNum > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    if (object.getStr("attrKey").equals(COMPUTE_NODE_INFO)) {
                        JSONArray chidrenDesc = object.getJSONArray("chidrenDesc");
                        if (Objects.nonNull(chidrenDesc)) {
                            for (Object data : (JSONArray) chidrenDesc.get(0)) {
                                JSONObject label = (JSONObject) data;
                                if ("节点数目".equals(label.get("label"))) {
                                    Integer value = label.getInt("value");
                                    label.put("value", value - agentNum);
                                    logger.info("ResRenewRefServiceImpl.getConfig 计算节点去掉 【{}】 个", agentNum);
                                }
                            }
                        }
                        break;
                    }
                }
            }

            config = JSONUtil.toJsonStr(array);
        }

        if (ProductCodeEnum.RS_BMS.getProductType().equals(sfProductResource.getProductType())
                || ProductCodeEnum.ECS.getProductType().equals(sfProductResource.getProductType())) {
            JSONObject object = JSONUtil.parseObj(config);
            JSONArray jsonArray = object.getJSONArray("productConfigDesc");
            jsonArray.removeIf(o -> {
                JSONObject jsonObject = (JSONObject) o;
                if ("申请数量".equals(jsonObject.getStr("label"))) {
                    return true;
                }else {
                    return false;
                }
            });
            config = JSONUtil.toJsonStr(object);
        }
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRenewRef(ResRenewRef resRenewRef) {
        if (Strings.isNullOrEmpty(RenewTypeEnum.typeForDecs(resRenewRef.getRenewType()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1760477614));
        }
        if (RenewTypeEnum.AUTO.getType().equals(resRenewRef.getRenewType())
                && Objects.isNull(resRenewRef.getRenewCycle())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_884141291));
        }

        List<ResRenewRef> renewRefs = this.selectByParams(new Criteria("objSid", resRenewRef.getObjSid()));
        if (CollectionUtil.isEmpty(renewRefs)) {
            WebUserUtil.prepareInsertParams(resRenewRef);
            this.insertSelective(resRenewRef);
        } else {
            ResRenewRef resRenewRefOld = renewRefs.get(0);
            resRenewRefOld.setRenewCycle(resRenewRef.getRenewCycle());
            resRenewRefOld.setRenewType(resRenewRef.getRenewType());
            this.updateByPrimaryKeySelective(resRenewRefOld);
        }
        if (RenewTypeEnum.AUTO.getType().equals(resRenewRef.getRenewType())) {
            this.aotuRenew();
        }
    }

    @Override
    public List<RenewalDTO> exportRenew(DescribeRenewRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("chargeType", "PrePaid");
        criteria.put("statusIn", normalList);
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1619437642));
        }
        this.selectByProject(criteria, request.getProject(), authUser);
        return resRenewRefMapper.selectRenew(criteria);
    }

    @Override
    public RestResult aotuRenew() {
        Criteria criteria = new Criteria();
        criteria.put("countdown", 7);
        criteria.put("renewType", RenewTypeEnum.AUTO.getType());
        List<RenewalDTO> renewalDTOS = resRenewRefMapper.selectRenew(criteria);
        User authUser = AuthUtil.getAuthUser();
        if (CollectionUtil.isNotEmpty(renewalDTOS)) {
            renewalDTOS.forEach(r -> {
                Org org = orgService.selectRootOrg(r.getOrgSid());
                BizBillingAccount account = bizBillingAccountMapper.selectByOrgIdAndEntityId(org.getOrgSid(),authUser.getEntityId());
                cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserMapper.selectByPrimaryKey(account.getAdminSid());
                if (!Strings.isNullOrEmpty(user.getEmail())) {
                    try {
                        ProductOperation renewRequest = new ProductOperation();
                        ProductInfo productInfo = new ProductInfo();
                        productInfo.setId(r.getId());
                        productInfo.setProductCode(r.getProductType());
                        productInfo.setPeriod(BigDecimal.valueOf(r.getRenewCycle()));
                        renewRequest.setProductInfo(Lists.newArrayList(productInfo));
                        if (Objects.isNull(user.getUserSid()) || Objects.isNull(user.getOrgSid())) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597597653));
                        }
                        renewRequest.setConsumer(user);
                        renewRequest.setAccountId(account.getId());
                        RestResult result = this.renewResource(renewRequest);
                        if (RestConst.BssError.contains((int) result.get("code"))) {
                            // 发送邮件
                            EmailVO emailVO = new EmailVO();
                            emailVO.setEmail(user.getEmail());
                            Map<String, String> map = new HashMap<>();
                            map.put("resourceId", r.getResourceId());
                            map.put("name", r.getName());
                            map.put("owner", user.getAccount());
                            emailVO.setContent(map);
                            orderService.sendEmail(emailVO);
                        }
                        _addActionCashLog(r,Boolean.TRUE);
                    } catch (Exception e) {
                        logger.error("自动续费失败", e.getMessage());
                        _addActionCashLog(r,Boolean.FALSE);
                    }
                }
            });
        }
        return new RestResult();
    }

    private void _addActionCashLog(RenewalDTO dto,boolean success) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
        mongoTemplate.insert(ActionLog.builder()
                        .account(authUserInfo.getAccount())
                        .actionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request))
                        .actionTime(new Date())
                        .httpMethod(request.getMethod())
                        .lbIp(cn.com.cloudstar.rightcloud.oss.common.util.IPAddressUtil.getRemoteAddr(request))
                        .resource(OperationResourceEnum.AUTO_RENEW.getDesc())
                        .tagName(dto.getProductName()+"，资源ID："+dto.getId())
                        .roleName("system")
                        .success(Boolean.TRUE.equals(success) ? OperationResourceEnum.SUCCESS.getDesc() : OperationResourceEnum.FAILURE.getDesc())
                        .build()
                , "action_log");
    }

    private Criteria selectByProject(Criteria criteria, Long project, User authUser) {
        if (Objects.isNull(project)) {
            // 查询全部资源
            // 判断是否为租户管理员
            if (validTenantAdmin(authUser.getUserSid())) {
                criteria.put("orgSid", authUser.getOrgSid());
            } else {
                criteria.put("userSid", authUser.getUserSid());
            }
        } else {
            criteria.put("projectId", project);
        }
        return criteria;
    }

    private boolean validTenantAdmin(Long userSid) {
        if (Objects.isNull(userSid)) {
            return false;
        }
        QueryWrapper<UserGroup> wrapper = new QueryWrapper<>();
        wrapper.eq("group_sid", 1)
                .eq("user_sid", userSid);
        List<UserGroup> list = iUserGroupService.list(wrapper);
        return list.size() > 0;
    }

    private JSONArray selectChangeDesc(ProductInfo productInfo, Map<String, Object> detail) {
        String productCode = detail.get("productCode").toString();
        if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(productCode)
                && !Strings.isNullOrEmpty(productInfo.getTargetType())) {
            return resourceConfigService.selectVmChange(productInfo.getId(), productInfo.getTargetType());
        }
        if (ProductCodeEnum.FLOATING_IP.getProductType().equalsIgnoreCase(productCode)) {
            return resourceConfigService.selectEIPChange(productInfo.getId(), productInfo);
        }
        if (ProductCodeEnum.DISK.getProductType().equalsIgnoreCase(productCode)) {
            return resourceConfigService.selectEbsChange(productInfo.getId(), productInfo);
        }
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(productCode)) {
            return resourceConfigService.selectSfsChange(productInfo.getId(), productInfo);
        }
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productCode)){
            return resourceConfigService.selectDrpChange(productInfo);
        }
//        if (ProductCodeEnum.DCS.getProductType().equalsIgnoreCase(productCode)
//                && !Strings.isNullOrEmpty(productInfo.getTargetType())) {
//            return resourceConfigService.selectDcsChange(productInfo.getId(), productInfo.getTargetType());
//        }
        return new JSONArray();
    }

    /**
     * 修改资源续费类型，把不进行续费 改成手动续费类型
     * @param id
     */
    private void changeRenewType(String id) {
        List<ResRenewRef> resources = this.selectByParams(new Criteria("objSid", id));
        if (CollectionUtil.isNotEmpty(resources)) {
            resources.stream().filter(r -> RenewTypeEnum.NONE.getType().equals(r.getRenewType())).forEach(r -> {
                r.setRenewType(RenewTypeEnum.MANUAL.getType());
                this.updateByPrimaryKeySelective(r);
            });
        }
    }

    private RestResult checkNonBillingProductHPCDrp(Long orderId,List<ProductInfo> productInfoList,List<ServiceOrderDetail> orderDetails,ProductOperation productOperation) {
        //查询是否关联不计费产品
        IsNonBillProductRequest request = new IsNonBillProductRequest();
        List<ServiceOrderDetail> hpcDRPOrderDetails = orderDetails.stream().filter(orderDetail -> ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(orderDetail.getServiceType())).collect(Collectors.toList());

        request.setSfServiceId(hpcDRPOrderDetails.get(0).getServiceId());
        request.setChargeType(productInfoList.get(0).getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        if (isNonBillProduct){
            productOperation.setChargingType(SALE_TYPE);
        }else {
            productOperation.setChargingType(NORMAL_TYPE);
        }
        ServiceOrder serviceOrder = serviceOrderService.getById(orderId);
        String chargingType = serviceOrder.getChargingType();
        //销售计费两种情况,1、不计费产品中包含该产品的场合,正常续订；2、不计费产品中不包含该产品的场合，不能续订产品
        //正常计费两种情况,1、不计费产品中包含该产品的场合,不能续订产品；2、不计费产品中不包含该产品的场合，正常续订
        if (Objects.nonNull(chargingType) && SALE_TYPE.equals(chargingType)) {
            if (isNonBillProduct) {
                return new RestResult(true);
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            }
        } else {
            if (isNonBillProduct) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            } else {
                return new RestResult(true);
            }
        }
    }

    private RestResult checkNonBillingProduct(List<ProductInfo> productInfoList,ProductOperation productOperation,String mainProductCode) {
        //根据资源id查询申请时订单
        ServiceOrder serviceOrder = serviceOrderService.selectOrderDetailByResourceId(productInfoList.get(0).getId(),mainProductCode);

        //查询是否关联不计费产品
        IsNonBillProductRequest request = new IsNonBillProductRequest();

        request.setSfServiceId(Long.valueOf(serviceOrder.getServiceId()));
        request.setChargeType(productInfoList.get(0).getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        if (isNonBillProduct){
            productOperation.setChargingType(SALE_TYPE);
        }else {
            productOperation.setChargingType(NORMAL_TYPE);
        }
        String chargingType = serviceOrder.getChargingType();
        //销售计费两种情况,1、不计费产品中包含该产品的场合,正常续订；2、不计费产品中不包含该产品的场合，不能续订产品
        //正常计费两种情况,1、不计费产品中包含该产品的场合,不能续订产品；2、不计费产品中不包含该产品的场合，正常续订
        if (Objects.nonNull(chargingType) && SALE_TYPE.equals(chargingType)) {
            if (isNonBillProduct) {
                return new RestResult(true);
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            }
        } else {
            if (isNonBillProduct) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            } else {
                return new RestResult(true);
            }
        }
    }

    @Data
    private static class ConfigDesc {
        private String label;

        private String value;
    }

}
