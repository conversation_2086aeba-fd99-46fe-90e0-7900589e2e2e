/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询RDS实例列表 入参
 *
 * <AUTHOR>
 */
@ApiModel(description = "查询RDS实例规格")
@Setter
@Getter
public class DescribeRdsInstanceEngineRequest extends BaseParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境ID")
    @NotNull
    private Long cloudEnvId;

    /**
     * 计费类型
     */
    @ApiModelProperty("计费类型")
    private String instanceChargeType;

    /**
     * 可用区
     */
    @ApiModelProperty("可用区")
    private String zoneId;

}
