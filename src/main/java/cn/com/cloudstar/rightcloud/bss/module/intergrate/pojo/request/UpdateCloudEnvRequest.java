/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * The type UpdateCloudEnvRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/5
 */
@Getter
@Setter
@ApiModel(description = "更新云环境")
public class UpdateCloudEnvRequest implements Serializable {

    /**
     * 平台云环境ID
     */
    @NotNull
    @ApiModelProperty(value = "平台云环境ID", name = "id", example = "1234", required = true)
    private Long id;

    /**
     * 云环境平台的账户ID
     */
    @NotNull
    @ApiModelProperty(value = "云环境平台的账户ID", name = "cloudEnvAccountId", example = "1234", required = true)
    private Long cloudEnvAccountId;

    /**
     * 云环境名称
     */
    @NotBlank
    @ApiModelProperty(value = "云环境名称", name = "cloudEnvName", example = "aliyun10", required = true)
    @SafeHtml
    private String cloudEnvName;

    /**
     * 云环境详细信息。传入校验信息，json格式。根据[/config/{envType}]接口返回的结果（取属性valueRule包含值为`require`的对象）传入参数。
     */
    @ApiModelProperty(name = "attrData", value = "云环境详细信息。传入校验信息，json格式。根据[/config/{envType}]接口返回的结果（取属性valueRule包含值为`require`的对象）传入参数。", example = "阿里云传入（apiKey，secureToken）。{\"apiKey\": \"{...}\",\"secureToken\": \"{...}\"}", required = true)
    private String attrData;

    /**
     * 定时同步时间, 单位分钟
     */
    @Max(1440)
    @Min(60)
    @NotNull
    @ApiModelProperty(value = "定时同步时间, 单位分钟", name = "cycleTime", example = "60", required = true)
    private Integer cycleTime;

    /**
     * 云环境类型
     */
    @NotBlank
    @ApiModelProperty(value = "云环境类型", name = "cloudEnvType", example = "VMware", required = true)
    private String cloudEnvType;

    /**
     * 监听采集频率
     */
    @Max(60)
    @Min(5)
    @ApiModelProperty(value = "基础监控采集频率, 单位分钟", name = "monitorCollectFrequency", example = "10", required = true)
    private Integer monitorCollectFrequency;

    /**
     * 告警采集频率
     */
    @Max(60)
    @Min(5)
    @ApiModelProperty(value = "基础监控告警采集频率, 单位分钟", name = "alarmCollectFrequency", example = "10", required = true)
    private Integer alarmCollectFrequency;

    /**
     * 监控采集开关
     */
    @ApiModelProperty(value = "监控采集开关", name = "monitorEnable", example = "true", required = true)
    private Boolean monitorEnable;

    @ApiModelProperty(value = "堡垒机id")
    private Long cloudFortressId;

    @ApiModelProperty(value = "VMWare NSX Env ID, 为空将删除云环境的关联")
    private Long nsxEnvId;

    @ApiModelProperty(value = "验证码", required = true)
    @org.hibernate.validator.constraints.NotBlank
    private String smscode;
}
