/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/4/23.
 */
@FeignClient(value = "https://cmp-oss:8080", configuration = FeignConfig.class, path = "/api/v1/oss")
public interface MyMirrorService {

    /**
     * 我的镜像列表
     */
    @GetMapping("/myMirror/listImages/fegin")
    RestResult listImages(@SpringQueryMap MirrorCenterRequest request);



    /**
     * 镜像同步
     */
    @PutMapping("/myMirror/syncImages/fegin")
    RestResult syncImages();

    /**
     * 镜像组织同步
     */
    @PutMapping("/myMirror/syncNameSpace/fegin")
    RestResult syncNameSpace();

    @GetMapping("/myMirror/getNameSpace/fegin")
    RestResult getNameSpace(@SpringQueryMap SwrNameSpaceRequest request);

    @PostMapping("/myMirror/nameSpace/fegin")
    RestResult createNameSpace(@RequestBody SwrNameSpaceCreateRequest request);

    @DeleteMapping("/myMirror/nameSpace/fegin")
    RestResult deleteNameSpace(@RequestBody SwrNameSpaceDeleteRequest request);
    /**
     * 版本列表
     */
    @GetMapping("/myMirror/listImageVersions/fegin")
    RestResult listImageVersions(@SpringQueryMap MirrorCenterRequest request);


    /**
     * 删除版本
     */
    @DeleteMapping("/myMirror/imageTagDelete/fegin")
    RestResult imageTagDelete(@SpringQueryMap MirrorCenterRequest request);

    @PostMapping("/mirrorCenter/imageUpdate/fegin")
    RestResult imageUpdateFeign(MirrorCenterUpdateRequest request);

    @DeleteMapping("/mirrorCenter/imageTagDelete/feign")
    RestResult imageTagDeleteFeign(MirrorCenterDeleteRequest request);
}
