package cn.com.cloudstar.rightcloud.bss.module.resource.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("镜像中心查询")
@Data
public class MirrorCenterDeleteRequest {

    /**
     * ID
     */
    private Long id;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    private String namespace;

    /**
     * 镜像名称
     */
    @ApiModelProperty(value = "镜像名称")
    private String name;


    /**
     * tag
     */
    @ApiModelProperty(value = "镜像tag")
    private String tag;

    /**
     * cloudEnvId
     */
    @ApiModelProperty(value = "cloudEnvId")
    private Long cloudEnvId;


}
