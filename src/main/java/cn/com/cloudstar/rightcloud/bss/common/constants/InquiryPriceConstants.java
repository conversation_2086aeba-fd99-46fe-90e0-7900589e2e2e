/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constants;

import com.google.common.collect.ImmutableMap;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2019/11/5
 */
public interface InquiryPriceConstants {

    /**
     * 基础价格变量
     */
    String X = "X";
    /**
     * 实际用量或带宽
     */
    String N = "N";

    /**
     * 基数
     */
    String Y = "Y";

    double GB_TO_MB_SIZE = 1024d;

    String SINGLE_SPEC_ITEM = "0";

    int KEEP_FOUR_DECIMALS = 10000;

    // BUG:29903 金额精确到小数点后5位数
    int DECIMAL_FIX = 5;

    String PUBLIC_ENV = "Public";
    String PRIVATE_ENV = "Private";

    /**
     * 无资源计费
     */
    String CHARGE_NONE = "none";

    /**
     * 按小时计费
     */
    String HOUR = "hour";
    /**
     * 全区域
     */
    String ALL_REGION = "all";

    /**
     * 缺省
     */
    List<String> DEFAULT = Arrays.asList("default", "*");

    /**
     * Openstack系列网络IP池
     */
    String PUBLIC = "public";

    String ECS = "ECS";

    String EIP = "EIP";

    String EBS = "EBS";

    String CHARGE_ITEM_CATEGORY = "chargeItemCategory";

    Map<String, String> META_MAP = ImmutableMap.of("instance", "ECS", "dataDisks", "EBS",
        "systemDisk", "EBS",
        "network", "EIP");

}
