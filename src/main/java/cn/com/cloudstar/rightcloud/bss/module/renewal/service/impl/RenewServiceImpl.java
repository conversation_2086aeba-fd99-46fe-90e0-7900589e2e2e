/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.OrgType;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductOperation;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.service.impl.ServiceOrderDetailServiceImpl;
import cn.com.cloudstar.rightcloud.bss.module.order.service.impl.ServiceOrderResourceRefServiceImpl;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.RenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.RenewService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.ResRenewRefService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizAccountProductQuota;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizProductQuota;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizProductQuotaWhiteList;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizAccountProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaWhiteListService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants.Sfs2Message;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2021/07
 */
@Service
public class RenewServiceImpl implements RenewService {

    @Autowired
    private ResRenewRefService resRenewRefService;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private ServiceOrderResourceRefServiceImpl serviceOrderResourceRefService;

    @Autowired
    private ServiceOrderDetailServiceImpl serviceOrderDetailService;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    @Autowired
    private BizAccountProductQuotaService bizAccountProductQuotaService;

    @Autowired
    private BizProductQuotaService bizProductQuotaService;

    @Autowired
    private BizProductQuotaWhiteListService bizProductQuotaWhiteListService;


    @Override
    public RestResult renew(RenewRequest request) {
        //ProductOperation convert = BeanConvertUtil.convert(request, ProductOperation.class);
        String jsonStr = JSONUtil.toJsonStr(request);
        ProductOperation convert = JSONUtil.toBean(jsonStr, ProductOperation.class);
        User authUser = AuthUtil.getAuthUser();
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user
                = sysUserService.selectByPrimaryKey(authUser.getUserSid());
        List<Org> orgList= orgService.selectOrgByUserSidAndType(user.getOrgSid(), OrgType.COMPANY);

        if(!"authSucceed".equalsIgnoreCase(user.getCertificationStatus())){
            if(orgList.size()>0){
                List<String> certificationStatusList = orgList.stream().map(Org::getCertificationStatus).collect(
                        Collectors.toList());
                if(certificationStatusList.size()>0){
                    if(!"authSucceed".equals(certificationStatusList.get(0))){
                        throw new BizException(WebUtil.getMessage(Sfs2Message.USER_AUTH));                    }
                }
            }
        }
        if (Objects.isNull(user.getUserSid()) || Objects.isNull(user.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597597653));
        }
        //设置客户信息
        convert.setConsumer(user);
        // 产品限额
        List<Long> serviceIdList = new ArrayList<>();
        request.getProductInfo().forEach(t -> {
            ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(
                    Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                            .eq(ServiceOrderResourceRef::getResourceId,
                                t.getId())
                            .eq(ServiceOrderResourceRef::getType,
                                t.getProductCode()));
            if (Objects.nonNull(serviceOrderResourceRef)) {
                ServiceOrderDetail serviceOrderDetail = serviceOrderDetailService.getById(
                        serviceOrderResourceRef.getOrderDetailId());
                if (Objects.nonNull(serviceOrderDetail)) {
                    serviceIdList.add(serviceOrderDetail.getServiceId());
                }
            }
        });
        String minAmount = productLimit(authUser, serviceIdList);
        if (Strings.isNotEmpty(minAmount)) {
            return new RestResult(Status.FAILURE,
                                  cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.LIMIT_MIN_AMOUNT,
                                                                                                 new Object[]{
                                                                                                         minAmount}));
        }
        return resRenewRefService.renewResource(convert);
    }

    /**
     * 查询该用户的产品受限
     *
     * @param user 用户
     * @param serviceIdList 服务id
     *
     * @return 最小受限金额
     */
    public String productLimit(User user, List<Long> serviceIdList) {
        // 找到服务ID对应的运营实体ID
        // 默认看作所有产品属于统一运营实体
        Long entityId = serviceCategoryMapper.selectById(serviceIdList.get(0)).getEntityId();
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entityId, user.getUserSid());
        for (Long serviceId : serviceIdList) {
            // 客户限额
            QueryWrapper<BizAccountProductQuota> queryAccount = new QueryWrapper<>();
            queryAccount.eq("user_sid", user.getUserSid())
                        .in("service_id", Lists.newArrayList(0L, serviceId))
                        .eq("status", "1")
                        .eq("entity_id", entityId);
            BizAccountProductQuota accountProductQuota = bizAccountProductQuotaService.getOne(queryAccount);
            if (Objects.nonNull(accountProductQuota)) {
                if (account.getBalance().compareTo(accountProductQuota.getMinAmount()) < 0) {
                    return String.valueOf(accountProductQuota.getMinAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    return null;
                }
            }
            // 产品限额
            QueryWrapper<BizProductQuota> queryProduct = new QueryWrapper<>();
            queryProduct.eq("service_id", serviceId).eq("status", "1").eq("entity_id", entityId);
            BizProductQuota productQuota = bizProductQuotaService.getOne(queryProduct);
            if (Objects.nonNull(productQuota)) {
                if (account.getBalance().compareTo(productQuota.getMinAmount()) < 0) {
                    return String.valueOf(productQuota.getMinAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    return null;
                }
            }
            // 平台限额
            QueryWrapper<BizProductQuotaWhiteList> queryWhiteList = new QueryWrapper<>();
            BizProductQuotaWhiteList productQuotaWhiteList = bizProductQuotaWhiteListService.getOne(
                    queryWhiteList.eq("user_sid", user.getUserSid()).eq("entity_id", entityId));
            if ("true".equals(PropertiesUtil.getProperty(SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT_OPEN + entityId))
                    && Objects.isNull(
                    productQuotaWhiteList)) {
                if (account.getBalance().compareTo(new BigDecimal(
                        PropertiesUtil.getProperty(SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + entityId))) < 0) {
                    return PropertiesUtil.getProperty(SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + entityId);
                } else {
                    return null;
                }

            }
        }
        return null;
    }
}
