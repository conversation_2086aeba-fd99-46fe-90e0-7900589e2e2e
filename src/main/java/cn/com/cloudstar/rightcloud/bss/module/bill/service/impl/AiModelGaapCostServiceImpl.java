/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.enums.DealType;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.TradeType;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.HcsoUserRefService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagUser;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.IBizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.AiModelCollector;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.Collector;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IAiModelGaapCostService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.IHcsoUserRefService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.NumberUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.MsgCd;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName: AiModelGaapCostServiceImpl.java
 * @Description： AiModel计费服务实现类
 **/
@Service
@Slf4j
public class AiModelGaapCostServiceImpl implements IAiModelGaapCostService {
    
    private static final String BILL_NO_PREFIX = "AI";
    private static final String POST_PAID = "PostPaid";
    private static final String MONTH_PATTERN = "yyyy-MM";
    private static final String AIMODEL_PRODUCT_TYPE = "AIMODEL";
    
    @Autowired
    private HcsoUserRefService hcsoUserRefService;
    
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    
    @Autowired
    private BizBagUserService bizBagUserService;
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void handleAiModelBill(List<Collector> archRecords, AiModelCollector detail, String chargeType, 
                                 List<InstanceGaapCost> costs, List<BizAccountDeal> deals) {
        
        log.info("开始处理AiModel话单计费，资源ID: {}, Token使用量: {}", detail.getResource_id(), detail.getTokenUsage());
        
        // 根据话单用户ID查询运营平台租户ID
        Long refUserId = hcsoUserRefService.getByAccountId(detail.getUser_id());
        
        // 查询账户余额等明细
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getOne(
            new QueryWrapper<BizBillingAccount>().eq("admin_sid", refUserId));
        
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1594992141));
        }
        
        // 计算Token费用
        BigDecimal tokenCost = calculateTokenCost(detail);
        
        if (NumberUtil.isGreater(tokenCost, BigDecimal.ZERO)) {
            // 创建账单记录
            InstanceGaapCost cost = createInstanceGaapCost(detail, bizBillingAccount, tokenCost);
            costs.add(cost);
            
            // 处理费用扣减 - 按Token套餐包>余额的顺序进行费用抵扣
            handlePayment(detail, bizBillingAccount, tokenCost, cost, deals);
            
            // 添加到归档记录
            archRecords.add(detail);
            
            log.info("AiModel话单处理完成，资源ID: {}, 费用: {}", detail.getResource_id(), tokenCost);
        }
    }
    
    /**
     * 计算Token费用
     */
    private BigDecimal calculateTokenCost(AiModelCollector detail) {
        // TODO: 实现Token费用计算逻辑
        // 这里需要根据模型类型、Token数量等计算实际费用
        // 暂时使用简单的计算方式
        Long tokenUsage = detail.getTokenUsage();
        if (tokenUsage == null || tokenUsage <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 假设每1000个Token收费0.01元
        BigDecimal tokenPrice = new BigDecimal("0.00001"); // 每个Token 0.00001元
        return NumberUtil.mul(new BigDecimal(tokenUsage), tokenPrice);
    }
    
    /**
     * 创建账单记录
     */
    private InstanceGaapCost createInstanceGaapCost(AiModelCollector detail, BizBillingAccount account, BigDecimal cost) {
        InstanceGaapCost gaapCost = new InstanceGaapCost();
        
        gaapCost.setBillNo(NoUtil.generateNo(BILL_NO_PREFIX));
        gaapCost.setBillingCycle(DateUtil.format(new Date(), MONTH_PATTERN));
        gaapCost.setBillSource("platform");
        gaapCost.setOwnerId(account.getAdminSid().toString());
        gaapCost.setUserAccountId(account.getId());
        gaapCost.setUserAccountName(account.getCreatedBy());
        gaapCost.setResourceId(detail.getResource_id());
        gaapCost.setInstanceName(detail.getModelName());
        gaapCost.setProductType(AIMODEL_PRODUCT_TYPE);
        gaapCost.setProductName("AI模型服务");
        gaapCost.setCurrency("CNY");
        gaapCost.setPriceType("token");
        gaapCost.setUsageCount(detail.getTokenUsage().toString());
        gaapCost.setUsageStartDate(DateUtil.parseDateTime(detail.getBegin_time()));
        gaapCost.setUsageEndDate(DateUtil.parseDateTime(detail.getEnd_time()));
        gaapCost.setPayTime(new Date());
        gaapCost.setPretaxAmount(cost);
        gaapCost.setTotalCost(cost);
        gaapCost.setOrgSid(account.getOrgSid());
        gaapCost.setOrgName(account.getOrgName());
        
        return gaapCost;
    }
    
    /**
     * 处理费用扣减 - 按Token套餐包>余额的顺序进行费用抵扣
     */
    private void handlePayment(AiModelCollector detail, BizBillingAccount account, BigDecimal totalCost, 
                              InstanceGaapCost cost, List<BizAccountDeal> deals) {
        
        BigDecimal remainingCost = totalCost;
        
        // 1. 优先使用Token套餐包
        remainingCost = deductFromTokenPackage(detail, account, remainingCost, cost, deals);
        
        // 2. 使用余额
        if (NumberUtil.isGreater(remainingCost, BigDecimal.ZERO)) {
            remainingCost = deductFromBalance(account, remainingCost, cost, deals);
        }
        
        // 3. 对余额不足的情况，标记欠费
        if (NumberUtil.isGreater(remainingCost, BigDecimal.ZERO)) {
            markAsArrears(account, remainingCost, cost, deals);
        }
        
        // 更新账户余额
        bizBillingAccountService.updateById(account);
    }
    
    /**
     * 从Token套餐包扣费
     */
    private BigDecimal deductFromTokenPackage(AiModelCollector detail, BizBillingAccount account, 
                                            BigDecimal cost, InstanceGaapCost gaapCost, List<BizAccountDeal> deals) {
        // TODO: 实现Token套餐包扣费逻辑
        // 查询用户可用的Token套餐包
        // 按优先级扣减Token套餐包余量
        
        log.info("TODO: 实现Token套餐包扣费逻辑，当前费用: {}", cost);
        
        // 暂时返回原费用，表示没有套餐包可用
        return cost;
    }
    
    /**
     * 从余额扣费
     */
    private BigDecimal deductFromBalance(BizBillingAccount account, BigDecimal cost, 
                                       InstanceGaapCost gaapCost, List<BizAccountDeal> deals) {
        
        BigDecimal balance = account.getBalance();
        
        if (NumberUtil.isGreaterOrEqual(balance, cost)) {
            // 余额充足，全额扣减
            BizAccountDeal accountDeal = createAccountDeal(account, cost, RechargeTypeEnum.ACC_TCASH);
            accountDeal.setBalance(NumberUtil.sub(balance, cost));
            accountDeal.setBalanceCredit(account.getCreditLine());
            deals.add(accountDeal);
            
            gaapCost.setCashAmount(cost);
            account.setBalance(NumberUtil.sub(balance, cost));
            
            return BigDecimal.ZERO;
        } else if (NumberUtil.isGreater(balance, BigDecimal.ZERO)) {
            // 余额不足，部分扣减
            BizAccountDeal accountDeal = createAccountDeal(account, balance, RechargeTypeEnum.ACC_TCASH);
            accountDeal.setBalance(BigDecimal.ZERO);
            accountDeal.setBalanceCredit(account.getCreditLine());
            deals.add(accountDeal);
            
            gaapCost.setCashAmount(balance);
            account.setBalance(BigDecimal.ZERO);
            
            return NumberUtil.sub(cost, balance);
        }
        
        return cost;
    }
    
    /**
     * 标记欠费
     */
    private void markAsArrears(BizBillingAccount account, BigDecimal arrearsCost, 
                              InstanceGaapCost gaapCost, List<BizAccountDeal> deals) {
        
        // TODO: 实现欠费标记逻辑
        log.info("TODO: 标记欠费，用户: {}, 欠费金额: {}", account.getAdminSid(), arrearsCost);
        
        // 创建欠费记录
        BizAccountDeal accountDeal = createAccountDeal(account, arrearsCost, RechargeTypeEnum.ACC_CREDIT);
        accountDeal.setBalance(account.getBalance());
        accountDeal.setBalanceCredit(account.getCreditLine());
        deals.add(accountDeal);
        
        gaapCost.setCreditAmount(arrearsCost);
    }
    
    /**
     * 创建账户交易记录
     */
    private BizAccountDeal createAccountDeal(BizBillingAccount account, BigDecimal amount, RechargeTypeEnum tradeChannel) {
        BizAccountDeal accountDeal = new BizAccountDeal();
        accountDeal.setDealSid(IdUtil.getSnowflakeNextId());
        accountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
        accountDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
        accountDeal.setType(DealType.OUT);
        accountDeal.setTradeType(TradeType.CONSUME);
        accountDeal.setTradeChannel(tradeChannel.getCode());
        accountDeal.setAmount(amount);
        accountDeal.setAccountId(account.getId());
        accountDeal.setOrgSid(account.getOrgSid());
        accountDeal.setCreatedDt(new Date());
        accountDeal.setUpdatedDt(new Date());
        
        return accountDeal;
    }
}
