/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.service.impl;


import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.basic.data.service.config.BasicSysConfigService;
import cn.com.cloudstar.rightcloud.bss.common.constants.*;
import cn.com.cloudstar.rightcloud.bss.common.enums.*;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RightCloudTrunc;
import cn.com.cloudstar.rightcloud.bss.common.util.*;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserDto;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserOrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.enums.CloudEnvType;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.InstanceGaapCostMapper;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.OrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.OrderSourceSnVO;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.OrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.ServiceInstance;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.Target;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeBizBagOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest.DimesionEnum;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.GaapCostDetailRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeGaapCostResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IInstanceGaapCostService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BillingConfigCategory;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponResourceService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.CustomerTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SubUserTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostSplitItem;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.enums.RequirePermissionEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Role;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.Resource;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.UserSyncRemoteService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.BasicDBObject;
import com.mongodb.client.ListIndexesIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.result.UpdateResult;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation.AddFieldsOperationBuilder;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators.Round;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.schema.JsonSchemaObject.Type;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 月费用分摊服务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Service
@Slf4j
public class InstanceGaapCostServiceImpl extends
        ServiceImpl<InstanceGaapCostMapper, InstanceGaapCost> implements IInstanceGaapCostService {

    private static final int MEMORY_STEP = 512;
    private static final double MEMORY_SIZE = 0.5;
    private static final int UNIT_COUNT = 1024;

    private static final String DETAIL_KEY = "bill:task:detail:{}";
    /**
     * 按量付费
     */
    private static final String BILL_NO_PREFIX = "ZD";
    private static final String PRODUCT = "product";
    /**
     * 后付账单
     */
    private static final String PAY_AS_YOU_GO_BILL = "PayAsYouGoBill";
    private static final String SUBSCRIPTION_ORDER = "SubscriptionOrder";
    private static final String NEW = "New";
    private static final List<String> ESC_CATEGORIES = Lists
        .newArrayList(BillingConfigCategory.COMPUTER, BillingConfigCategory.BLOCK_STORAGE,
            BillingConfigCategory.NETWORK);
    private static final String POST_PAID = "PostPaid";
    private static final String PRE_PAID = "PrePaid";

    private static final List<String> CREATING = Lists.newArrayList("CREATING", "creating");
    private static final List<String> CREATE_FAILURE = Lists
        .newArrayList("create_failure", "CREATE_FAILURE", "error", "failure");
    /**
     * 标识 Y
     */
    public static final String Y = "Y";

    private static String MONTH_PATTERN = "yyyy-MM";

    private static final String HCSO = "HCSO";

    private static final String UPGRADE_OF_BILLS_1 = "UPDATE service_order SET order_source_sn = order_sn WHERE type = 'apply' and order_source_sn is NULL";
    private static final String UPGRADE_OF_BILLS_2 = "SELECT b.order_sn, (SELECT a.order_sn FROM service_order a WHERE a.type = 'apply' and a.cluster_id = b.cluster_id and a.name = b.name) apply_order_sn  FROM service_order b WHERE b.type != 'apply' and b.order_source_sn is NULL and b.cluster_id is not NULL";

    private static final Integer SHEET_LIMIT = 200000;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private IBizCouponAccountService bizCouponAccountService;

    @Autowired
    private IBizCouponResourceService bizCouponResourceService;

    @Autowired
    private SysUserOrgMapper sysUserOrgMapper;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private OrgService orgService;

    @DubboReference
    private ResAllRemoteService resAllRemoteService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @Autowired
    private IBizDistributorService bizDistributorService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private IBizInvoiceService bizInvoiceService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    @Lazy
    private ExportService exportService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;
    @Autowired
    private IBizBillingAccountService iBizBillingAccountService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private BasicSysConfigService sysConfigService;

    @DubboReference
    private UserSyncRemoteService userSyncRemoteService;

    @Override
    public IPage<DescribeGaapCostResponse> listBills(DescribeGaapCostRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        CompletableFuture.runAsync(() -> {
            //判断索引是否存在
            List<String> indexList = Arrays.asList("payTime","summaryFlag", "ownerId", "entityId", "usageEndDate", "usageStartDate",
                "billNo","billType", "productCode", "orderSourceSn", "priceType",
                "orderSn","userAccountName","productName","billingCycle","type",
                "distributorName","bagDiscountInstId","billSource","cloudEnvName","cloudEnvId","invoiceStatus");
            createItemIndex(indexList);
        });

        //根据当前用户的角色和数据权限配置获取该用户能够查看账单数据的最大账户数
        Set<Long> accountIdsSet = findBillingAccountByCurrentUserRole(authUser);
        request.setAccountIds(accountIdsSet);
        Criteria criteria = getCriteria(request, null);
        if (Objects.equals(authUser.getUserType(), UserType.DISTRIBUTOR_USER) && !criteria.getCriteriaObject()
                                                                                          .containsKey("orgSid")) {
            //如果当前登录用户是分销商管理员
            List<Long> orgSids = orgService.selectCustomerOrgSids(authUser.getOrgSid());
            criteria.and("orgSid").in(orgSids);
        }
        Query queryCri = new Query(criteria);
        IPage<DescribeGaapCostResponse> responsePage = PageUtil.emptyPage();
        //按账期统计，是否分组统计
        long count = 0L;
        String groupByPeriod = request.getGroupByPeriod();
        queryCri.with(Sort.by(Sort.Direction.DESC, "payTime", "usageStartDate"));
        if (Objects.nonNull(request.getPagenum()) && Objects.nonNull(request.getPagesize())) {
            int pageNum = request.getPagenum().intValue();
            int pageSize = request.getPagesize().intValue();
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
            queryCri.with(pageable);
        }

        long startTime = System.currentTimeMillis();

        List<InstanceGaapCost> instanceGaapCosts = new ArrayList<>();
        //按账期统计，是否分组统计
        if (StringUtils.isEmpty(groupByPeriod)) {
            instanceGaapCosts = getInstanceGaapCosts(queryCri, request.getPageFlag(), false);
        } else {
            instanceGaapCosts = getInstanceGaapCostsGroupBy(criteria, request, false);
        }

        long endTime = System.currentTimeMillis();
        log.info("InstanceGaapCostServiceImpl.listBills 获取账单明细：耗时：【{}】ms： ", endTime -startTime);

        List<DescribeGaapCostResponse> data = new ArrayList<>();
        Set<Long> userAccountIds = instanceGaapCosts.stream()
                .filter(e -> StringUtils.isBlank(e.getDistributorName()))
                .map(InstanceGaapCost::getUserAccountId)
                .collect(Collectors.toSet());
        List<BizBillingAccount> accounts = null;
        if (userAccountIds.size() > 0) {
            accounts = bizBillingAccountMapper.selectAccountByIds(userAccountIds);
        }

        // 查询CUE是否显示
        String cueShowOrHide = ConfigConstants.CUE_SHOW;
        SysConfig sysConfig = sysConfigService.findByConfigTypeAndKey("other_config", "cue.show.or.hide");
        if (sysConfig != null) {
            cueShowOrHide = sysConfig.getConfigValue();
        }

        List<BizBillingAccount> finalAccounts = accounts;
        String finalCueShowOrHide = cueShowOrHide;
        instanceGaapCosts.forEach(instanceGaapCost -> {
            DescribeGaapCostResponse response = BeanConvertUtil.convert(instanceGaapCost,
                                                                        DescribeGaapCostResponse.class);
            if (ObjectUtils.isEmpty(response)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
            }
            response.setBillNo(instanceGaapCost.getBillNo());
            response.setCueShowOrHide(finalCueShowOrHide);
            response.setPaymentCurrency(instanceGaapCost.getCurrency());
            BigDecimal deductCouponDiscount = instanceGaapCost.getDeductCouponDiscount() == null ? BigDecimal.ZERO
                    : instanceGaapCost.getDeductCouponDiscount();
            response.setDeductCouponDiscount(deductCouponDiscount);
            response.setProductName(instanceGaapCost.getProductName());
            String instanceName = instanceGaapCost.getInstanceName();
            if ("EVS".equals(instanceGaapCost.getProductCode()) && Objects.nonNull(instanceName)){
                if (instanceName.startsWith("::")){
                    instanceName = instanceName.substring(2);
                }
                if (instanceName.endsWith(":: ")){
                    instanceName = instanceName.substring(0,instanceName.length()-3);
                }
            }
            response.setInstanceName(
                    StringUtils.isBlank(instanceName) ? instanceGaapCost.getProductName()
                            : instanceName);
            response.setSubscriptionType(instanceGaapCost.getBillType());
            response.setComputingPower(instanceGaapCost.getComputingPower());
            String instanceId = instanceGaapCost.getResourceId();
            if (StrUtil.isNotBlank(instanceId)) {
                String[] split = instanceId.split(",");
                if (split.length > 10) {
                    split = Arrays.copyOfRange(split, 0, 10);
                    instanceId = Arrays.stream(split).collect(Collectors.joining(",")) + "...";
                }
            }
            response.setInstanceId(instanceId);
            response.setOrderId(instanceGaapCost.getOrderSn());
            String cueValue = instanceGaapCost.getCueValue() == null ? null : instanceGaapCost.getCueValue();
            response.setCueValue(cueValue);
            if (instanceGaapCost.getInvoicable() == null) {
                response.setInvoicable(BooleanEnum.YES.getCode());
            }
            if (instanceGaapCost.getRechargeCreditAmount() == null) {
                response.setRechargeCreditAmount(BigDecimal.ZERO);
            }
            if (instanceGaapCost.getBilledAndFree() == null) {
                response.setBilledAndFree("--");
            }
            if (StringUtils.isBlank(instanceGaapCost.getEntityName()) && Objects.nonNull(finalAccounts)) {
                for (BizBillingAccount account : finalAccounts) {
                    if (Objects.equals(response.getUserAccountId(), account.getId().toString())) {
                        response.setEntityName(account.getEntityName());
                        return;
                    }
                }
            }
            if (StringUtils.isBlank(response.getDistributorName()) && finalAccounts != null
                    && finalAccounts.size() > 0) {
                Optional<BizBillingAccount> account = finalAccounts.stream()
                                                                   .filter(e -> response.getUserAccountId().equals(e.getId() == null?null:e.getId().toString()))
                                                                   .findFirst();
                account.ifPresent(
                        bizBillingAccount -> response.setDistributorName(bizBillingAccount.getDistributorName()));
            }
            BigDecimal statisticHours = response.getStatisticHours();
            if(statisticHours !=null){
                response.setStatisticHours(statisticHours.setScale(2,BigDecimal.ROUND_HALF_UP));
                }
            BigDecimal statisticDays = response.getStatisticDays();
            if(statisticDays !=null){
                response.setStatisticDays(statisticDays.setScale(0,BigDecimal.ROUND_HALF_UP));
            }
            data.add(response);
        });

        responsePage.setRecords(data);
        responsePage.setTotal(data.size());
        return responsePage;
    }

    private void createItemIndex(List<String> indexList) {
        log.info("创建索引-InstanceGaapCostServiceImpl.createItemIndex.createIndex-开始");
        long startTime = System.currentTimeMillis();
        //创建索引
        try {
            Map<String,Boolean> indexExsitMap = new HashMap<>();
            for (String index : indexList) {
                indexExsitMap.put(index,Boolean.FALSE);
            }
            ListIndexesIterable<Document> itemIndexIter = mongoTemplate.getCollection("biz_bill_usage_item").listIndexes();
            for (Document document : itemIndexIter) {
                if (document.get("key") != null) {
                    Document keyDoc = (Document) document.get("key");
                    if (keyDoc.size() < 2) {
                        for (Entry<String, Boolean> entry : indexExsitMap.entrySet()) {
                            Object o = keyDoc.get(entry.getKey());
                            if (o != null) {
                                entry.setValue(Boolean.TRUE);
                            }
                        }
                    }
                }
            }
            log.info("创建索引-InstanceGaapCostServiceImpl.createItemIndex-[{}]", JSONObject.toJSONString(indexExsitMap));
            if (indexExsitMap.values().stream().anyMatch(v ->!Boolean.TRUE.equals(v))) {
                // 创建索引
                List<IndexModel> ims = new ArrayList<>();
                for (Entry<String, Boolean> entry : indexExsitMap.entrySet()) {
                    if (!Boolean.TRUE.equals(entry.getValue())) {
                        BasicDBObject indexModel = new BasicDBObject();
                        indexModel.put(entry.getKey(),-1);
                        ims.add(new IndexModel(indexModel));
                    }
                }

                mongoTemplate.getCollection("biz_bill_usage_item").createIndexes(ims);
            }

        } catch (Exception e) {
            log.error("创建索引异常-InstanceGaapCostServiceImpl.createItemIndex-异常[{}]", e.getMessage());
        }
        log.info("创建索引-InstanceGaapCostServiceImpl.createItemIndex-执行时间[{}]s", (System.currentTimeMillis() - startTime) / 1000);
    }
    private long getAggregationGroupCount(Criteria criteria, DescribeGaapCostRequest request) {
        {
            String[] fileds = new String[]{"orderId","orderSn","productCode","userAccountId","userAccountName","productName","pretaxAmount","pretaxGrossAmount","orgSid", "cashAmount", "creditAmount", "couponAmount", "entityId", "ownerId", "billingCycle",
                    "billType", "priceType", "payTime", "entityName","instanceName","resourceId"};

            //内存不足使用本地存储
            AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project(fileds),
                    // 排序
                    Aggregation.sort(Direction.DESC, "payTime")).withOptions(aggregationOptions);

            AggregationPipeline pipeline = aggregation.getPipeline();

            String groupByDimension = request.getGroupByDimension();
            if (DimesionEnum.ORDER.getCode().equalsIgnoreCase(groupByDimension)) {
                pipeline.add(Aggregation.group("orderId").min("payTime").as("payTime"));
            } else if (DimesionEnum.PRODUCT_CODE.getCode().equalsIgnoreCase(groupByDimension)) {
                pipeline.add(Aggregation.group("productCode", "orgSid").min("payTime").as("payTime"));
            } else if ((DimesionEnum.RESNAME.getCode().equalsIgnoreCase(groupByDimension))) {
                pipeline.add(Aggregation.group("instanceName","resourceId").min("payTime").as("payTime"));
        }
            pipeline.add(Aggregation.count().as("count"));
            AggregationResults<Document> groupCountResult =
                    mongoTemplate.aggregate(aggregation, "biz_bill_usage_item", Document.class);

            List<Document> mappedResults = groupCountResult.getMappedResults();
            if (CollectionUtil.isNotEmpty(mappedResults)) {
                return CollectionUtil.getFirst(mappedResults).getInteger("count").intValue();
        }
            return 0L;
        }

    }

    private Set<Long> findAccountIdsByOrgSid(List<Long> orgSids) {
        Set<Long> accountIds = Sets.newHashSet();
        List<BizBillingAccount> accounts = bizBillingAccountMapper.getByOrgSids(orgSids);
        if (CollectionUtil.isNotEmpty(accounts)) {
            accountIds = accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toSet());
        }

        return accountIds;
    }

    @Override
    public Set<Long> findAccountIdsByCurrentLoginUser(AuthUser authUserInfo) {
        Set<Long> accountIds = Sets.newHashSet();
        List<BizBillingAccount> accounts = bizBillingAccountMapper.getByAdminSid(authUserInfo.getUserSid());
        if (CollectionUtil.isNotEmpty(accounts)) {
            accountIds = accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toSet());
        }

        return accountIds;
    }

    @Override
    public List<InstanceGaapCost> listExportBills(DescribeGaapCostRequest request, String id, Boolean excelFlg, AuthUser authUserInfo) {
        Query query = getQuery(request, id);
        String pageFlag = request.getPageFlag();
        //设置分页
        Long pagenum = request.getPagenum();
        Long pagesize = request.getPagesize();
        if (Y.equals(pageFlag) && pagenum !=null && pagesize != null) {
            query.skip((pagenum - 1) * pagesize);
            query.limit(pagesize.intValue());
        }
        return getInstanceGaapCosts(query, pageFlag, excelFlg);
    }

    @Override
    public List<InstanceGaapCost> exportBills(DescribeGaapCostRequest request) {
        List<InstanceGaapCost> costs= new ArrayList<>();
        if (StringUtils.isEmpty(request.getGroupByPeriod())) {
            costs = exportBillsInstanceGaapCosts(request);
        }else{
            Criteria criteria = getCriteria(request, null);
            costs = getInstanceGaapCostsGroupBy(criteria, request, false);
        }
        return costs;
    }

    @NotNull
    private List<InstanceGaapCost> exportBillsInstanceGaapCosts(DescribeGaapCostRequest request) {
        Query query = getQuery(request, null);
        Long skip = (request.getPagenum() - 1) * request.getPagesize();
        int size = request.getPagesize().intValue();
        query.with(Sort.by(Direction.DESC, "payTime"));

        List<InstanceGaapCost> costs = new ArrayList<>();

        // 查询所有的org表
        List<Org> list = orgService.list();
        Map<Long, Org> orgMap = list.stream().collect(Collectors.toMap(Org::getOrgSid, org -> org, (k1, k2) -> k1));
        Map<Long, Org> collect = new HashMap<>();
        orgMap.forEach((key,value) -> {
            collect.put(key, getParentOrg(value,orgMap));
        });

        // 查询所有用户表
        List<UserDto> userDtos = userMapper.selectAllUserAccount();
        Map<Long, String> userMag = userDtos.stream().collect(Collectors.toMap(UserDto::getUserSid, UserDto::getAccount));

        MongoCursor<Document> cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                                                            find(query.getQueryObject())
                                                    .noCursorTimeout(true)
                                                    .skip(skip.intValue())
                                                    .limit(size)
                                                    .sort(query.getSortObject())
                                                    .cursor();

        Document doc = null;
        InstanceGaapCost instanceGaapCost = null;
        while (cursor.hasNext()) {
            try {
                doc = cursor.next();
                instanceGaapCost = BeanUtil.toBean(doc, InstanceGaapCost.class);
                Object id = doc.get("_id");
                if (id != null) {
                    instanceGaapCost.setId(String.valueOf(id));
                }

                if (StringUtil.isBlank(instanceGaapCost.getUserAccountName())) {
                    Long userSId = Long.parseLong(instanceGaapCost.getOwnerId());
                    String account = userMag.get(userSId);
                    if (StringUtils.isNotEmpty(account)) {
                        instanceGaapCost.setUserAccountName(account);
                    }
                }
                Object orgSidObj = doc.get("orgSid");
                if (orgSidObj != null) {
                    Long orgSid = Long.valueOf(orgSidObj.toString());
                    Org org = collect.get(orgSid);
                    if (org != null) {
                        String orgName = org.getOrgName();
                        instanceGaapCost.setOrgName(orgName);
                    }

                }

                if (StringUtils.isBlank(instanceGaapCost.getBillType())) {
                    String subscriptionType = instanceGaapCost.getSubscriptionType();
                    if (StringUtils.isNotBlank(subscriptionType)) {
                        if (subscriptionType.startsWith("PayAsYouGo")) {
                            instanceGaapCost.setBillType("PayAsYouGoBill");
                        }
                        else if (subscriptionType.startsWith("Subscription")) {
                            instanceGaapCost.setBillType("SubscriptionOrder");
                        }
                    }
                }

            } catch (Exception e) {
                log.error("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument.赋值账单信息异常 eroor: {}", e.getMessage());
            }
            if (instanceGaapCost != null) {
                costs.add(instanceGaapCost);
            } else {
                log.info("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument instanceGaapCost is null ");
            }
        }
        cursor.close();
        return costs;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void handleOrderBill(OrderDetail detail, String chargeType, List<InstanceGaapCost> costs,
                                List<BizAccountDeal> deals) {
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getById(detail.getAccountId());
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_994932438), detail.getAccountId()));
        }
        List<ServiceInstance> instances = detail.getInstances();
        if (CollectionUtil.isEmpty(instances)) {
            return;
        }
        DateTime now = DateUtil.date();
        BigDecimal totalTradePrice = BigDecimal.ZERO;
        for (ServiceInstance instance : instances) {
            Target target = orderMapper
                    .getServiceInstanceTarget(instance.getServiceInstanceId());
            if (Objects.isNull(target)) {
                continue;
            }

            Resource resource = resAllRemoteService.getResource(target.getResourceId(), target.getResourceType());
            resource.setAccountId(bizBillingAccount.getId());
            if (Objects.isNull(resource) || Objects.isNull(resource.getResourceStatus())) {
                continue;
            }
            if (CREATING.contains(resource.getResourceStatus())) {
                continue;
            }
            BigDecimal eachDiscount = NumberUtil
                    .div(Convert.toBigDecimal(detail.getDiscount(), BigDecimal.ZERO), detail.getQuantity());
            DurationBill durationBill = DurationBill.builder()
                                                    .start(instance.getServiceStartTime())
                                                    .now(now)
                                                    .costs(costs)
                                                    .detail(detail)
                                                    .instance(instance)
                                                    .resource(resource)
                                                    .chargeType(chargeType)
                                                    .eachDisCount(eachDiscount)
                                                    .deals(deals)
                                                    .build();
            if (CREATE_FAILURE.contains(resource.getResourceStatus())) {
                orderMapper.updateInstanceLastBillTime(instance.getServiceInstanceId(), now);
                Integer failureCount = Convert.toInt(
                        JedisUtil.INSTANCE.get(StrUtil.format(DETAIL_KEY, detail.getDetailId())),
                        0);
                JedisUtil.INSTANCE.set(StrUtil.format(DETAIL_KEY, detail.getDetailId()),
                                       Convert.toStr(failureCount + 1), 60 * 60 * 24);
                setProductPrice(durationBill);
                updateBalance(bizBillingAccount,
                              durationBill.getTradePrice().subtract(durationBill.getEachDisCount()),
                              true);
                continue;
            }
            orderMapper.updateInstanceLastBillTime(instance.getServiceInstanceId(), now);

            //产品入账
            calculateProduct(durationBill, detail, bizBillingAccount);
            // 资源入账
            calculateResource(durationBill, detail, bizBillingAccount);

            totalTradePrice = totalTradePrice
                    .add(durationBill.getTradePrice()).subtract(durationBill.getEachDisCount());
        }
        // 扣减账户余额
        if (POST_PAID.equals(chargeType)) {
            updateBalance(bizBillingAccount, totalTradePrice, false);
        } else if (PRE_PAID.equals(chargeType)) {
            Long failureCount = Convert.toLong(
                    JedisUtil.INSTANCE.get(StrUtil.format(DETAIL_KEY, detail.getDetailId())), 0L);
            if (Objects.equals(failureCount, detail.getQuantity()) && Objects
                    .nonNull(detail.getCouponSid())) {
                BizCouponAccount one = bizCouponAccountService.getOne(
                        Wrappers.<BizCouponAccount>lambdaQuery()
                                .eq(BizCouponAccount::getCouponSid, detail.getCouponSid()));
                if (Objects.nonNull(one)) {
                    one.setCouponStatus(CouponStatusEnum.UNUSED.getCode());
                    bizCouponAccountService.updateById(one);
                    bizCouponResourceService.remove(Wrappers.<BizCouponResource>lambdaQuery()
                                                            .eq(BizCouponResource::getCouponSid, detail.getCouponSid())
                                                            .eq(BizCouponResource::getOrderId, detail.getOrderId()));
                }
            }
        }
    }

    /**
     * 收支明细
     *
     * @param durationBill 账单明细
     * @param detail 订单详情
     * @param cost 账单
     * @param account 账户
     * @param tradePrice 交易金额
     */
    private void makeAccountDeal(DurationBill durationBill, OrderDetail detail, InstanceGaapCost cost,
                                 BizBillingAccount account, BigDecimal tradePrice) {
        String remark;
        if (cost.getProductCode().equals(PRODUCT)) {
            remark = ProductComponentEnum.keyFromDesc(detail.getServiceType());
        } else {
            remark = ProductComponentEnum.keyFromDesc(cost.getProductCode());
        }
        BizAccountDeal accountDeal = new BizAccountDeal();
        accountDeal.setFlowNo(NoUtil.generateNo("SZ"));
        accountDeal.setType(DealType.OUT);
        accountDeal.setTradeType(TradeType.PAY);
        accountDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
        accountDeal.setFlowNo(NoUtil.generateNo("SZ"));
        accountDeal.setEnvType(cost.getCloudEnvType());
        accountDeal.setEnvName(StrUtil.isNotEmpty(cost.getCloudEnvType()) ?
                                       CloudEnvType.from(cost.getCloudEnvType()).getDesc() : "");
        accountDeal.setOrderNo(detail.getOrderSn());
        accountDeal.setBillNo(cost.getBillNo());
        accountDeal.setRemark(StrUtil.concat(true, remark, "[", accountDeal.getEnvName(), "]"));
        accountDeal.setBillingCycle(LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
        accountDeal.setAccountSid(account.getId());
        accountDeal.setAccountName(account.getAccountName());
        accountDeal.setOrgSid(account.getOrgSid());
        accountDeal.setUserSid(account.getId());
        accountDeal.setDealTime(System.currentTimeMillis());
        tradePrice = durationBill.isFirstTime ? NumberUtil.add(cost.getPretaxAmount(), detail.getCurrAmount())
                : cost.getPretaxAmount();

        BigDecimal balance = account.getBalance();
        if (NumberUtil.isGreaterOrEqual(balance, tradePrice)) {
            //采用余额
            accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
            accountDeal.setBalance(NumberUtil.sub(balance, tradePrice));
            accountDeal.setAmount(tradePrice);
            //信用额度
            accountDeal.setBalanceCredit(account.getCreditLine());
            durationBill.getDeals().add(accountDeal);

            cost.setCashAmount(accountDeal.getAmount());
        } else if (NumberUtil.isGreater(balance, BigDecimal.ZERO)
                && NumberUtil.isLess(balance, tradePrice)
                && NumberUtil.isLess(tradePrice, account.getCreditLine())) {
            // 余额、信用额度
            accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
            accountDeal.setBalance(BigDecimal.ZERO);
            accountDeal.setAmount(balance);

            cost.setCashAmount(accountDeal.getAmount());

            accountDeal.setBalanceCredit(account.getCreditLine());
            durationBill.getDeals().add(accountDeal);
            BizAccountDeal creditDeal = BeanConvertUtil.convert(accountDeal, BizAccountDeal.class);
            creditDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
            creditDeal.setAmount(NumberUtil.sub(tradePrice, balance));
            creditDeal.setBalanceCredit(NumberUtil.sub(account.getCreditLine(), accountDeal.getAmount()));
            creditDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
            creditDeal.setFlowNo(NoUtil.generateNo("SZ"));
            cost.setCreditAmount(creditDeal.getAmount());

            durationBill.getDeals().add(creditDeal);
        } else if (NumberUtil.isLessOrEqual(balance, BigDecimal.ZERO)
                && NumberUtil.isLess(tradePrice, account.getCreditLine())) {
            // 信用额度
            accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
            accountDeal.setBalance(NumberUtil.sub(balance, tradePrice));
            accountDeal.setAmount(tradePrice);
            accountDeal.setBalanceCredit(NumberUtil.sub(account.getCreditLine(), accountDeal.getAmount()));

            cost.setCreditAmount(tradePrice);

            durationBill.getDeals().add(accountDeal);
        } else {
            accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
            accountDeal.setBalance(NumberUtil.sub(balance, tradePrice));
            accountDeal.setAmount(tradePrice);
            accountDeal.setBalanceCash(BigDecimal.ZERO);
            accountDeal.setBalanceCredit(BigDecimal.ZERO);

            cost.setCreditAmount(tradePrice);

            durationBill.getDeals().add(accountDeal);
        }
        account.setBalance(accountDeal.getBalance());
        WebUserUtil.prepareInsertParams(accountDeal, detail.getCreatedBy());
        cost.setOfficialAmount(detail.getOriginalCost());
        cost.setDiscountAmount(NumberUtil.sub(detail.getOriginalCost(), tradePrice));
        cost.setCouponAmount(cost.getPricingDiscount());
    }

    /**
     * 获取账单明显
     *
     * @param query 查询条件
     * @param pageFlag 是否分页
     */
    private List<InstanceGaapCost> getInstanceGaapCosts(Query query, String pageFlag,Boolean excelFlg) {
        if (excelFlg) {
            query.with(Sort.by(Sort.Direction.DESC, "_id"));
        } else {
            query.with(Sort.by(Sort.Direction.DESC, "payTime"));
        }

        List<InstanceGaapCost> instanceGaapCosts = this.getInstanceGaapCostByDocument(query, pageFlag,excelFlg);

        for (InstanceGaapCost instanceGaapCost : instanceGaapCosts) {
            if (StringUtils.isBlank(instanceGaapCost.getBillType())) {
                String subscriptionType = instanceGaapCost.getSubscriptionType();
                if (StringUtils.isNotBlank(subscriptionType)) {
                    if (subscriptionType.startsWith("PayAsYouGo")) {
                        instanceGaapCost.setBillType("PayAsYouGoBill");
                    }
                    else if (subscriptionType.startsWith("Subscription")) {
                        instanceGaapCost.setBillType("SubscriptionOrder");
                    }
                }
            }
    }

        return instanceGaapCosts;
    }

    /**
     * 获取账单明显
     *
     * @param criteria    查询条件
     * @param costRequest 请求
     */
    private List<InstanceGaapCost> getInstanceGaapCostsGroupBy(Criteria criteria, DescribeGaapCostRequest costRequest, Boolean excelFlg) {
        return this.costGroupBy(criteria, costRequest);
    }


    /**
     * 组装查询条件类
     *
     * @param request
     */
    private Query getQuery(DescribeGaapCostRequest request, String id) {
        Criteria criteria = getCriteria(request, id);
        return new Query(criteria);
    }

    @NotNull
    public Criteria getCriteria(DescribeGaapCostRequest request, String id) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(id)) {
            ObjectId objectId = new ObjectId(id);
            criteria.and("_id").lt(objectId);
        }
        Set<Long> orgIds = new HashSet<>();
        if (StringUtil.isEmpty(request.getExportType()) || (StringUtil.isNotEmpty(request.getExportType())
                && !"all".equals(request.getExportType()))) {

            if ("current".equals(request.getExportType())) {
                //LocalDate localDate = LocalDate.now().plusMonths(-1);
                LocalDate localDate = LocalDate.now();
                int year = localDate.getYear();
                int month = localDate.getMonthValue();
                criteria.and("billingCycle")
                        .gte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthFirstDay(year, month),
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN))
                        .lte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthLastDay(year, month),
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN));
            } else if (StringUtil.isNotEmpty(request.getStartTime()) && StringUtil.isNotEmpty(request.getEndTime())
                    && !"undefined".equals(request.getStartTime()) && !"undefined".equals(request.getEndTime())) {
                String startTime = request.getStartTime();
                Pattern compile = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}");
                if (compile.matcher(startTime).matches()) {
                    startTime = startTime + " 00:00:00";
                }
                Date currentStart = DateUtil.parseDateTime(startTime);
                Date currentStop = DateUtil.parseDateTime(request.getEndTime());
                criteria.and("payTime").gte(currentStart).lte(currentStop);
            } else if ("month".equals(request.getExportType())) {
                //特定的月份导出
                String specifiMonth = request.getSpecifiMonth();
                Date date = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(specifiMonth,
                                                                                           cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN);
                int year = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getYear(date);
                int month = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getMonth(date);
                criteria.and("billingCycle")
                        .gte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthFirstDay(year, month),
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN))
                        .lte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthLastDay(year, month),
                                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN));
            }

            if (request.getBillNoLike() != null) {
                criteria.and("billNo").regex(Pattern.compile("^.*" + request.getBillNoLike() + ".*$"));
            }
            if (request.getOrderNoLike() != null) {
                criteria.and("orderSn").regex(Pattern.compile("^.*" + request.getOrderNoLike() + ".*$"));
            }
            if (request.getBagInstUuid() != null) {
                criteria.and("bagDiscountInstId").is(request.getBagInstUuid());
            }
            if (request.getPriceType() != null) {
                criteria.and("priceType").regex(Pattern.compile("^.*" + request.getPriceType() + ".*$"));
            }
            if (request.getEnvId() != null) {
                criteria.and("cloudEnvId").is(request.getEnvId());
            }
            if (CollectionUtil.isNotEmpty(request.getProductCodes())) {
                criteria.and("productCode").in(request.getProductCodes());
                if (request.getProductCode().equalsIgnoreCase(cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.HPC.getProductCode())) {
                    criteria.and("productName").regex(Pattern.compile("^.*" + cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.HPC.getProductCode() + ".*$"));
                } else if (request.getProductCode().equalsIgnoreCase(cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.MODEL_ARTS.getProductCode())) {
                    criteria.and("productName").regex(Pattern.compile("^.*" + cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.MODEL_ARTS.getProductName() + ".*$"));
                }
            } else {
                if (request.getProductCode() != null) {
                    criteria.and("productCode").is(request.getProductCode());
                }
            }
            if (request.getSubscriptionType() != null) {
                criteria.and("billType").regex(Pattern.compile("^.*" + request.getSubscriptionType() + ".*$"));
            }

            if (StringUtil.isNotBlank(request.getUserAccountName())) {
                criteria.and("userAccountName").is(request.getUserAccountName());
            }

            String cloudEnvName = request.getCloudEnvName();
            if (StringUtil.isNotEmpty(cloudEnvName)) {
                if (!"其他".contains(cloudEnvName)) {
                    criteria.and("cloudEnvName").regex(Pattern.compile("^.*" + cloudEnvName + ".*$"));
                } else {
                    criteria.and("cloudEnvName").is(null);
                }
            }

            if (request.getInvoiceStatus() != null) {
                criteria.and("invoiceStatus").is(request.getInvoiceStatus());
            }

            if (request.getInvoiceAmountFlag() != null && Y.equals(request.getInvoiceAmountFlag())) {
                criteria.and("cashAmount").gt(BigDecimal.ZERO);
            }
            if (!StringUtil.isNullOrEmpty(request.getInstanceName())) {
                Criteria criteria1 = new Criteria();
                criteria1.and("instanceName").regex(Pattern.compile("^.*" + request.getInstanceName().trim() + ".*$"));
                Criteria criteria2 = new Criteria();
                criteria2.and("productName").regex(Pattern.compile("^.*" + request.getInstanceName().trim() + ".*$"));
                criteria2.andOperator(Criteria.where("instanceName").exists(false),
                                      Criteria.where("instanceName").is(""));
                criteria.orOperator(criteria1, criteria2);
            }
            if (!StringUtil.isNullOrEmpty(request.getInstanceId())) {
                criteria.and("resourceId").regex(Pattern.compile("^.*" + request.getInstanceId().trim() + ".*$"));
            }
            if (Objects.nonNull(request.getBillingCycle())) {
                criteria.and("billingCycle").is(request.getBillingCycle());
            }
            if (Objects.nonNull(request.getBillBillingCycleId())) {
                criteria.and("billBillingCycleId").regex(request.getBillBillingCycleId());
            }
            if (!StringUtil.isNullOrEmpty(request.getBillSource())) {
                criteria.and("billSource").is(request.getBillSource());
            }
            if (request.getAccountIds() != null && request.getAccountIds().size() > 0 && AuthUtil.getAuthUser() != null
                    && ModuleTypeConstants.FROM_BSS.equals(AuthUtil.getAuthUser().getRemark())) {
                criteria.and("userAccountId").in(request.getAccountIds());
            }
            if (request.getProjectNameLike() != null) {
                List<Long> orgSidList = orgService.selectOrgIdListByOrgNameLike(request.getProjectNameLike());
                List<Long> allOrgSidList = new ArrayList<>();
                for (Long orgSid : orgSidList) {
                    List<Long> children = orgService.selectCustomerOrgSids(orgSid);
                    allOrgSidList.addAll(children);
                }
                orgIds.addAll(allOrgSidList);
            }
            if (Objects.nonNull(request.getType())) {
                criteria.and("type").is(request.getType());
            }
            if (Objects.nonNull(request.getOrderSourceSn())) {
                criteria.and("orderSourceSn").is(request.getOrderSourceSn());
            }
        } else {
            if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())
                    && !"undefined".equals(request.getStartTime()) && !"undefined".equals(request.getEndTime())) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
                criteria.andOperator(Criteria.where("billingCycle").gte(request.getStartTime())
                        , Criteria.where("billingCycle").lte(request.getEndTime()));

            }
        }
        String groupByPeriod = request.getGroupByPeriod();
        if (StringUtil.isNotEmpty(groupByPeriod)) {
            assembleCriteriaInGroupByPeriod(criteria, groupByPeriod);
        }
        //当前组织及以下数据
        if (Objects.nonNull(request.getOrgSid())) {
            List<Long> orgSids = orgService.selectCustomerOrgSids(request.getOrgSid());
            orgIds.addAll(orgSids);
        }
        //当前组织及以下数据
        else if (!request.isOrderSourceSnIsNull() && Objects.isNull(request.getOrgSid()) && Objects.nonNull(
                AuthUtil.getAuthUser()) && Objects.nonNull(
                AuthUtil.getAuthUser().getOrgSid())) {
            if (!Objects.equals(AuthUtil.getAuthUser().getUserType(), UserType.DISTRIBUTOR_USER)) {
                List<Long> orgSids = orgService.selectCustomerOrgSids(AuthUtil.getAuthUser().getOrgSid());
                orgIds.addAll(orgSids);
            }
        }

        if (StringUtil.isNotBlank(request.getDistributorName())) {
            if (BizConstants.DISTRIBUTOR_NAME.equals(request.getDistributorName())) {
                criteria.and("distributorName").is(null);
            } else {
                Pattern pattern = Pattern.compile("^.*" + request.getDistributorName() + ".*$");
                criteria.and("distributorName").regex(pattern);
            }
        }

        if (request.isOrderSourceSnIsNull()) {
            criteria.and("orderSourceSn").isNull();
        }
        if (AuthUtil.getAuthUser() != null && ModuleTypeConstants.FROM_BSS.equals(AuthUtil.getAuthUser().getRemark())) {
            criteria.and("entityId").is(AuthUtil.getAuthUser().getEntityId());
        }
        if (!CollectionUtils.isEmpty(orgIds)) {
            criteria.and("orgSid").in(orgIds);
        }
        return criteria;
    }

    /**
     * 设置时间查询条件，满足当前账期
     * @param criteria
     * @param groupByPeriod
     */
    private void assembleCriteriaInGroupByPeriod(Criteria criteria, String groupByPeriod) {
        Date firstDay = getFirstDay(groupByPeriod);
        Date firstDayOfNextMonth = DateUtils.addMonths(firstDay, 1);
        criteria.and("usageEndDate").gt(firstDay);
        criteria.and("usageStartDate").lt(firstDayOfNextMonth);
    }

    /**
     * 月份第一天
     * @param groupByPeriod
     * @return
     */
    private Date getFirstDay(String groupByPeriod) {
        Date firstDay =new Date();
        String firstDayOfMonthStr = "-01 00:00:00";
        try {
            firstDay = DateUtils.parseDate(groupByPeriod + firstDayOfMonthStr, cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.COMMON_DATE_PATTERN);
        } catch (Exception e) {
            log.error("账单明细账单拆分-InstanceGaapCostServiceImpl.splitPrePaidCost-账期解析异常[{}]", e.getMessage());
            String firstDayStr = DateFormatUtils.format(firstDay,cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN+firstDayOfMonthStr);
            try {
                firstDay = DateUtils.parseDate(firstDayStr, cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.COMMON_DATE_PATTERN);
            } catch (ParseException ex) {
                log.error("账单明细账单拆分-InstanceGaapCostServiceImpl.splitPrePaidCost-账期解析异常[{}]",ex.getMessage());
                throw new RuntimeException(ex);
            }
        }
        return firstDay;
    }


    private void updateBalance(BizBillingAccount bizBillingAccount, BigDecimal tradePrice, boolean isAdd) {
        if (NumberUtil.isGreater(tradePrice, BigDecimal.ZERO)) {
            BigDecimal updatePrice =
                    isAdd ? NumberUtil.add(bizBillingAccount.getBalance(), tradePrice)
                            : NumberUtil.sub(bizBillingAccount.getBalance(), tradePrice);
            bizBillingAccount.setBalance(updatePrice);
            bizBillingAccountService.updateById(bizBillingAccount);
            //修改业务标识
            cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(
                    bizBillingAccount.getAdminSid());
            updateBusinessArrearageTag(bizBillingAccount,user);
        }
    }

    private void updateBusinessArrearageTag(BizBillingAccount bizBillingAccount, cn.com.cloudstar.rightcloud.bss.common.pojo.User user) {
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                            String[] split = s.replaceAll(
                                                      BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.add(
                            BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
            } else {
                user.setBusinessTag(
                        BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userMapper.updateByPrimaryKeySelective(user);
            userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        } else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                    tagList =  tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                            String[] split = s.replaceAll(
                                                      BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (org.springframework.util.CollectionUtils.isEmpty(accountIdList)) {
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
                    userMapper.updateByPrimaryKeySelective(user);
                    userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));
                }
            }
        }
    }

    private DurationBill calculateProduct(DurationBill durationBill, OrderDetail orderDetail,
                                          BizBillingAccount account) {
        // 计算每次任务时间的价格
        setProductPrice(durationBill);
        BigDecimal tradePrice = durationBill.getTradePrice();

        // 金额大于0
        BigDecimal originalPrice = durationBill.getOriginalPrice();
        Date now = durationBill.getNow();
        Date useStartTime = Convert.toDate(durationBill.getUseStartTime(), now);
        Date useEndTime = Convert.toDate(durationBill.getUseEndTime(), now);
        OrderDetail detail = durationBill.getDetail();
        ServiceInstance instance = durationBill.getInstance();

        InstanceGaapCost productCost = new InstanceGaapCost();
        productCost.setOrderId(Convert.toStr(detail.getOrderId()));
        productCost.setOrgSid(detail.getOrgSid());
        productCost.setOwnerId(Convert.toStr(detail.getOwnerId()));
        productCost.setResourceId(Convert.toStr(detail.getServiceId()));

        productCost.setProductCode(PRODUCT);
        productCost.setBillNo(NoUtil.generateNo(BILL_NO_PREFIX));
        productCost.setOrderType(NEW);
        productCost.setBillType(
                POST_PAID.equals(durationBill.getChargeType()) ? PAY_AS_YOU_GO_BILL
                        : SUBSCRIPTION_ORDER);
        productCost.setBillingCycle(DateUtil.format(now, "YYYY-MM"));
        productCost.setInstanceName(instance.getServiceInstanceName());
        productCost.setCurrency("CNY");

        Integer failureCount = Convert.toInt(
                JedisUtil.INSTANCE.get(StrUtil.format(DETAIL_KEY, detail.getDetailId())),
                0);
        Long quantity = orderDetail.getQuantity() - failureCount.longValue();
        // 原始金额
        productCost.setPretaxGrossAmount(originalPrice.multiply(Convert.toBigDecimal(quantity, BigDecimal.ZERO)));
        // 询价优惠
        productCost.setPricingDiscount(durationBill.isFirstTime ? orderDetail.getCoupon() : BigDecimal.ZERO);

        //抹零金额
        productCost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(
                NumberUtil.sub(productCost.getPretaxGrossAmount(), orderDetail.getDiscount())));
        // 优惠后金额
        productCost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(
                NumberUtil.sub(productCost.getPretaxGrossAmount(), orderDetail.getDiscount())));
        productCost.setUsageStartDate(useStartTime);
        productCost.setUsageEndDate(useEndTime);
        productCost.setPayTime(now);
        durationBill.getCosts().add(productCost);
        log.info("产品id=[{}], 名称=[{}], 入账=[{}], 使用开始时间=[{}], 结束时间=[{}], 时长=[{}]",
                 productCost.getResourceId(), productCost.getInstanceName(), tradePrice,
                 DateUtil.formatDateTime(useStartTime), DateUtil.formatDateTime(useEndTime),
                 durationBill.getDuration());
        makeAccountDeal(durationBill, orderDetail, productCost, account, productCost.getPretaxAmount());
        return durationBill;
    }

    private void calculateResource(DurationBill durationBill, OrderDetail orderDetail, BizBillingAccount account) {
        String chargeType = durationBill.getChargeType();
        Resource resource = durationBill.getResource();
        if (CloudEnvType.from(resource.getCloudEnvType()).isPublic() && !HCSO.equals(resource.getCloudEnvType())) {
            return;
        }

        List<BizBillingPriceVO> billingPrices = durationBill.getDetailPrices();
        Boolean isFirstTime = durationBill.isFirstTime;

        if ("ecs".equals(resource.getProductCode())) {
            BigDecimal tradePrice;
            // ( oncePrice? + price/unit ) * discount
            if (POST_PAID.equals(chargeType)) {
                tradePrice = billingPrices.stream()
                    .filter(vo -> ESC_CATEGORIES.contains(vo.getCategory())).map(vo -> {
                        if (isFirstTime) {
                            return NumberUtil.mul(NumberUtil
                                .add(NumberUtil.mul(vo.getHourPrice(), durationBill.getDuration()),
                                    vo.getOncePrice()), vo.getPlatformDiscount());
                        }
                        return NumberUtil
                            .mul(NumberUtil.mul(vo.getHourPrice(), durationBill.getDuration()),
                                vo.getPlatformDiscount());
                    }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            } else {
                tradePrice = billingPrices.stream()
                    .filter(vo -> ESC_CATEGORIES.contains(vo.getCategory())).map(vo -> {
                        if (isFirstTime) {
                            return NumberUtil
                                .mul(NumberUtil.add(vo.getMonthPrice(), vo.getOncePrice()),
                                    vo.getPlatformDiscount());
                        }
                        return NumberUtil.mul(vo.getMonthPrice(), vo.getPlatformDiscount());
                    }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            addResourceCost(durationBill, tradePrice, orderDetail, account);
        } else {
            String category =
                "eip".equals(resource.getProductCode()) ? BillingConfigCategory.NETWORK
                    : BillingConfigCategory.BLOCK_STORAGE;
            BizBillingPriceVO one = CollectionUtil
                .findOne(billingPrices, vo -> Objects.equals(vo.getCategory(), category));
            if (Objects.isNull(one)) {
                return;
            }
            BigDecimal tradePrice;
            if (POST_PAID.equals(chargeType)) {
                tradePrice = isFirstTime ? NumberUtil.mul(NumberUtil
                    .add(NumberUtil.mul(one.getHourPrice(), durationBill.getDuration()),
                        one.getOncePrice()), one.getPlatformDiscount()) : NumberUtil
                    .mul(NumberUtil.mul(one.getHourPrice(), durationBill.getDuration()),
                        one.getPlatformDiscount());
            } else {
                tradePrice = isFirstTime ? NumberUtil
                    .mul(NumberUtil.add(one.getMonthPrice(), one.getOncePrice()),
                        one.getPlatformDiscount())
                    : NumberUtil.mul(one.getMonthPrice(), one.getPlatformDiscount());
            }

            addResourceCost(durationBill, tradePrice, orderDetail, account);
        }
    }

    private void addResourceCost(DurationBill durationBill, BigDecimal tradePrice, OrderDetail orderDetail, BizBillingAccount account) {
        if (NumberUtil.isGreater(tradePrice, BigDecimal.ZERO)) {
            Date now = durationBill.getNow();
            Date useStartTime = Convert.toDate(durationBill.getUseStartTime(), now);
            Date useEndTime = Convert.toDate(durationBill.getUseEndTime(), now);
            String chargeType = durationBill.getChargeType();
            OrderDetail detail = durationBill.getDetail();
            Resource resource = durationBill.getResource();

            InstanceGaapCost cost = new InstanceGaapCost();
            cost.setOrgSid(detail.getOrgSid());
            cost.setBillNo(NoUtil.generateNo(BILL_NO_PREFIX));
            cost.setBillingCycle(DateUtil.format(now, "YYYY-MM"));
            cost.setInstanceName(resource.getInstanceName());
            cost.setConfiguration(resource.getConfiguration());
            cost.setRegion(resource.getZone());
            cost.setCloudEnvId(resource.getCloudEnvId());
            cost.setCloudEnvName(resource.getCloudEnvName());
            cost.setCloudEnvType(resource.getCloudEnvType());
            cost.setCloudEnvAccountId(resource.getCloudEnvAccountId());
            cost.setCloudEnvAccountName(resource.getCloudEnvAccountName());
            cost.setProductCode(resource.getProductCode());
            cost.setBillType(
                POST_PAID.equals(chargeType) ? PAY_AS_YOU_GO_BILL : SUBSCRIPTION_ORDER);
            cost.setPayTime(now);
            cost.setOrderId(Convert.toStr(detail.getOrderId()));
            // 原始金额
            cost.setPretaxGrossAmount(tradePrice);

            // 优惠金额
            cost.setPricingDiscount(durationBill.isFirstTime ? detail.getCoupon() : BigDecimal.ZERO);
            //抹零金额
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(tradePrice));
            // 优惠后金额
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(tradePrice));

            cost.setUsageStartDate(useStartTime);
            cost.setUsageEndDate(useEndTime);
            cost.setCurrency("CNY");
            cost.setResourceId(resource.getResourceId());
            log.info("资源id=[{}],名称=[{}], 入账=[{}], 使用开始时间=[{}], 结束时间=[{}], 时长=[{}]",
                cost.getResourceId(), cost.getInstanceName(), tradePrice,
                DateUtil.formatDateTime(cost.getUsageStartDate()),
                DateUtil.formatDateTime(cost.getUsageEndDate()), durationBill.getDuration());
            durationBill.getCosts().add(cost);
        }
    }

    private void setProductPrice(DurationBill durationBill) {
        OrderDetail detail = durationBill.getDetail();
        Date start = durationBill.getStart();
        Date now = durationBill.getNow();
        ServiceInstance instance = durationBill.getInstance();

        boolean isFirstTime = Objects.isNull(instance.getServiceLastBillTime());
        durationBill.setIsFirstTime(isFirstTime);
        if (POST_PAID.equals(durationBill.getChargeType())) {
            Date lastCalculateTime = isFirstTime ? start : instance.getServiceLastBillTime();
            long seconds = DateUtil.between(lastCalculateTime, now, DateUnit.SECOND);
            double hours = NumberUtil.div(seconds, 60 * 60, 2);
            durationBill.setDuration(Convert.toBigDecimal(hours));
            durationBill.setUseStartTime(lastCalculateTime);
            durationBill.setUseEndTime(now);
        } else {
            durationBill.setUseStartTime(start);
            durationBill.setUseEndTime(instance.getServiceEndTime());
        }
        List<BizBillingPriceVO> priceVOS = JSON.parseArray(detail.getPriceDetail(), BizBillingPriceVO.class);
        if (CollectionUtil.isEmpty(priceVOS)) {
            return;
        }
        durationBill.setDetailPrices(priceVOS);
        // 额外单次配置费用
        BigDecimal tradeOncePrice = NumberUtil.sub(NumberUtil
            .add(detail.getDiscount(), detail.getAmountReceived()), detail.getOriginalCost());

        // 单个资源原价
        BigDecimal originalPrice = NumberUtil.div(detail.getOriginalCost(), detail.getQuantity());
        // 单个最终交易价
        BigDecimal tradePrice = NumberUtil.div(NumberUtil.sub(detail.getAmountReceived(), tradeOncePrice), detail.getQuantity());

        if (POST_PAID.equals(durationBill.getChargeType())) {
            BigDecimal hours = durationBill.getDuration();
            BigDecimal durationOriginalPrice = NumberUtil.mul(originalPrice, hours);

            durationBill.setOriginalPrice(isFirstTime ? originalPrice
                : durationOriginalPrice);

            BigDecimal durationTradeHourPrice = NumberUtil.mul(tradePrice, hours);
            durationBill.setTradePrice(isFirstTime ? tradePrice
                : durationTradeHourPrice);

        } else if (PRE_PAID.equals(durationBill.getChargeType())) {
            durationBill.setOriginalPrice(originalPrice);
            durationBill.setTradePrice(isFirstTime ? NumberUtil.add(tradePrice, tradeOncePrice)
                : tradePrice);
        }
    }

    @Override
    public IPage<BillBillingCycleCostVo> listCycleBills(DescribeGaapCostRequest request) {
        /* 获取当前登录用户信息*/
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        //分页查询所有账单，主要获取分页后的账单的总条数
        Aggregation aggregation = this.getCycleCriteria(request, authUser);
        AggregationPipeline pipeline = aggregation.getPipeline();
        String countFiledName = "totalCount";
        pipeline.add(Aggregation.count().as(countFiledName));
        log.info("账单周期条数mongodb查询条件:{}", JSON.toJSONString(aggregation));
        IPage<BillBillingCycleCostVo> responsePage = PageUtil.emptyPage();
        /* 按聚合条件从mongo中查询结果*/
        String collectionName = "biz_bill_billing_cycle";
        if (Boolean.TRUE.equals(request.getSplitByPeriod())) {
            collectionName = "biz_bill_billing_cycle_split_item";
        }
        AggregationResults<Document> countResult = mongoTemplate.aggregate(aggregation,
                                                                           collectionName,Document.class);
        long count = 0;
        if (Objects.isNull(countResult) || Objects.isNull(countResult.getMappedResults())) {
            return responsePage;
        } else {
            List<Document> mappedResults = countResult.getMappedResults();
            if (CollectionUtil.isNotEmpty(mappedResults)) {
                count = CollectionUtil.getFirst(mappedResults).getInteger(countFiledName).intValue();
            }
            if (count <= 0) {
                return responsePage;
            }
        }
        //传入分页的参数，对账单做分页查询，显示的时候分页显示
        request.setPageFlag(Y);
        aggregation = this.getCycleCriteria(request, authUser);
        log.info("账单周期分页mongodb查询条件:{}", JSON.toJSONString(aggregation));

        List<BillBillingCycleCostVo> billBillingCycleCost = this.getBillBillingCycleCost(aggregation,
                                                                                         request.getSplitByPeriod(), Y);
        responsePage.setRecords(billBillingCycleCost);
        responsePage.setTotal(count);
        return responsePage;
    }





    private List<BillBillingCycleCostVo> getBillBillingCycleCost(Aggregation aggregation,Boolean splitByPeriod,String detailFlag) {
        List<BillBillingCycleCostVo> response = new ArrayList<>();
        String collecctionName = "biz_bill_billing_cycle";
        if (Boolean.TRUE.equals(splitByPeriod)) {
            collecctionName = "biz_bill_billing_cycle_split_item";
        }
        AggregationResults<BillBillingCycleCostSplitItem> costResult = mongoTemplate.aggregate(aggregation, collecctionName, BillBillingCycleCostSplitItem.class);
        if (Objects.nonNull(costResult)) {
            List<BillBillingCycleCostSplitItem> billingCycleCosts = costResult.getMappedResults();
            List<BillBillingCycleCostVo> vos = new ArrayList<>();
            //账单周期账单不为空
            if (CollectionUtil.isNotEmpty(billingCycleCosts)) {
                //循环赋值 按照周期，运营实体分组的周期数据
                for (BillBillingCycleCostSplitItem billBillingCycleCost : billingCycleCosts) {
                    BillBillingCycleCostVo vo = BeanConvertUtil.convert(billBillingCycleCost, BillBillingCycleCostVo.class);
                    //根据下面的ids获取汇总成这些ids的账单明细
                    if (Boolean.TRUE.equals(splitByPeriod)) {
                        vo.setIds(billBillingCycleCost.getCycleIds());
                        dealSplitByPeriod(vo);
                        vos.add(vo);
                    } else {
                        List<String> cycleIds = billBillingCycleCost.getIds();
                        if (CollectionUtil.isNotEmpty(cycleIds)) {
                            //查询所有的账单周期,获取所有的账单周期的数据
                            Query queryDetail = new Query(Criteria.where("id").in(cycleIds));
                            //账单周期列表
                            List<BillBillingCycleCost> monthCycleCost = mongoTemplate.find(queryDetail,
                                    BillBillingCycleCost.class);

                            //原始金额
                            BigDecimal pretaxGrossAmount = BigDecimal.ZERO;
                            BigDecimal eraseZeroAmount = BigDecimal.ZERO;
                            BigDecimal discountAmount = BigDecimal.ZERO;
                            //现金支付金额
                            BigDecimal cashAmount = BigDecimal.ZERO;
                            //信用额度支付金额
                            BigDecimal creditAmount = BigDecimal.ZERO;
                            //充值现金券支付金额
                            BigDecimal voucherAmount = BigDecimal.ZERO;
                            BigDecimal invoiceAmount = BigDecimal.ZERO;
                            BigDecimal couponDiscount = BigDecimal.ZERO;
                            //抵扣现金券支付金额
                            BigDecimal deductCouponDiscount = BigDecimal.ZERO;
                            //折扣优惠金额
                            BigDecimal pricingDiscount = BigDecimal.ZERO;
                            //套餐包优惠金额
                            BigDecimal bizBagDiscount = BigDecimal.ZERO;

                            //初始化账单周期列表
                            List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> newMonthCycleCosts = new ArrayList<>();


                            // 原始金额
                            vo.setIds(cycleIds);
                            if (CollectionUtil.isNotEmpty(monthCycleCost)) {
                                for (BillBillingCycleCost cycleCost : monthCycleCost) {
                                    //初始化一个新的对象
                                    cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost tempCost = new cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost();
                                    //退订的账期为空，就走账期表的数据
                                    //优惠券优惠金额
                                    BigDecimal applyCouponDiscount =
                                            cycleCost.getCouponDiscount() == null ? BigDecimal.ZERO
                                                    : cycleCost.getCouponDiscount();
                                    //套餐包优惠金额
                                    BigDecimal applyBizBagDiscount =
                                            cycleCost.getBagDiscountAmount() == null ? BigDecimal.ZERO
                                                    : cycleCost.getBagDiscountAmount();
                                    //抵扣现金券支付金额
                                    BigDecimal applyDeductCouponDiscount =
                                            cycleCost.getDeductCouponDiscount() == null ? BigDecimal.ZERO
                                                    : cycleCost.getDeductCouponDiscount();
                                    //优惠后金额
                                    BigDecimal applyPretaxAmount = cycleCost.getAmount() == null ? BigDecimal.ZERO : cycleCost.getAmount();
                                    //已分摊优惠后金额
                                    BigDecimal applyGaapPretaxAmount = cycleCost.getDiscountAmount() == null ? BigDecimal.ZERO : cycleCost.getDiscountAmount();
                                    //现金支付金额
                                    BigDecimal applyCashAmount = cycleCost.getCashAmount() == null ? BigDecimal.ZERO : cycleCost.getCashAmount();
                                    //信用额度支付金额
                                    BigDecimal applyCreditAmount = cycleCost.getCreditAmount() == null ? BigDecimal.ZERO : cycleCost.getCreditAmount();
                                    //充值现金券支付金额
                                    BigDecimal applyCouponAmount = cycleCost.getVoucherAmount() == null ? BigDecimal.ZERO : cycleCost.getVoucherAmount();
                                    //抹零金额
                                    BigDecimal applyEraseZeroAmount =
                                            cycleCost.getEraseZeroAmount() == null ? BigDecimal.ZERO
                                                    : cycleCost.getEraseZeroAmount();
                                    //原始金额
                                    BigDecimal applyPretaxGrossAmount = cycleCost.getOfficialAmount() == null ? cycleCost.getAmount() : cycleCost.getOfficialAmount();
                                    //预付费得折扣优惠金额 = 原始金额- 优惠券优惠金额-所有支付金额,后付费得直接取表里面汇总得折扣优惠金额
                                    BigDecimal applyPricingDiscount = BigDecimal.ZERO;
                                    if (PayTypeEnum.SUBSCRIPTION.getCode().equalsIgnoreCase(cycleCost.getBillType())) {
                                        applyPricingDiscount = applyPretaxGrossAmount.subtract(applyCouponDiscount).subtract(applyCashAmount)
                                                .subtract(applyCreditAmount).subtract(applyCouponAmount).subtract(applyDeductCouponDiscount)
                                                .subtract(applyBizBagDiscount).subtract(applyEraseZeroAmount);
                                    } else {
                                        applyPricingDiscount = cycleCost.getPricingDiscount() == null ? BigDecimal.ZERO : cycleCost.getPricingDiscount();
                                    }

                                    applyPretaxGrossAmount = applyPretaxGrossAmount == null ? BigDecimal.ZERO : applyPretaxGrossAmount;
                                    //原始金额
                                    tempCost.setPretaxGrossAmount(applyPretaxGrossAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
                                    //优惠后金额(出账金额)
                                    tempCost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(applyPretaxAmount));
                                    //已分摊优惠后金额
                                    tempCost.setGaapPretaxAmount(applyGaapPretaxAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
                                    //现金支付金额
                                    tempCost.setCashAmount(applyCashAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    //信用额度支付金额
                                    tempCost.setCreditAmount(applyCreditAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    //充值现金券支付金额
                                    tempCost.setCouponAmount(applyCouponAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    //优惠券优惠金额
                                    tempCost.setCouponDiscount(applyCouponDiscount.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    //折扣优惠金额
                                    tempCost.setPricingDiscount(applyPricingDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                                    //抵扣券现金支付金额
                                    tempCost.setDeductCouponDiscount(
                                            applyDeductCouponDiscount.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    //套餐包折扣金额
                                    tempCost.setBagDiscountAmount(
                                            applyBizBagDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                                    //抹零金额
                                    tempCost.setEraseZeroAmount(applyEraseZeroAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
                                    newMonthCycleCosts.add(tempCost);

                                }
                            }

                            //对月周期进行汇总
                            for (cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost newMonthCycleCost : newMonthCycleCosts) {
                                //折扣后支付金额
                                discountAmount = discountAmount.add(newMonthCycleCost.getDiscountAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getDiscountAmount());
                                //现金支付金额
                                cashAmount = cashAmount.add(newMonthCycleCost.getCashAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getCashAmount());
                                //信用额度支付金额
                                creditAmount = creditAmount.add(newMonthCycleCost.getCreditAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getCreditAmount());
                                //充值现金券支付金额
                                voucherAmount = voucherAmount.add(newMonthCycleCost.getCouponAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getCouponAmount());
                                invoiceAmount = invoiceAmount.add(newMonthCycleCost.getCashAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getCashAmount());
                                //优惠券优惠金额
                                couponDiscount = couponDiscount.add(newMonthCycleCost.getCouponDiscount() == null ? BigDecimal.ZERO : newMonthCycleCost.getCouponDiscount());
                                //抵扣现金券支付金额
                                deductCouponDiscount = deductCouponDiscount.add(
                                        newMonthCycleCost.getDeductCouponDiscount() == null ? BigDecimal.ZERO
                                                : newMonthCycleCost.getDeductCouponDiscount());
                                //套餐包优惠金额
                                bizBagDiscount = bizBagDiscount.add(
                                        newMonthCycleCost.getBagDiscountAmount() == null ? BigDecimal.ZERO
                                                : newMonthCycleCost.getBagDiscountAmount());
                                //折扣优惠金额
                                pricingDiscount = pricingDiscount.add(
                                        newMonthCycleCost.getPricingDiscount() == null ? BigDecimal.ZERO
                                                : newMonthCycleCost.getPricingDiscount());
                                // 原始金额
                                pretaxGrossAmount = pretaxGrossAmount.add(newMonthCycleCost.getPretaxGrossAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getPretaxGrossAmount());

                                //抹零金额
                                eraseZeroAmount = eraseZeroAmount.add(newMonthCycleCost.getEraseZeroAmount() == null ? BigDecimal.ZERO : newMonthCycleCost.getEraseZeroAmount());
                            }


                            //原始金额
                            vo.setPretaxGrossAmount(pretaxGrossAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
                            //抹零金额
                            vo.setEraseZeroAmount(eraseZeroAmount);
                            //实际支付金额= 现金支付金额+
                            vo.setAmount(cashAmount.add(creditAmount)
                                    .add(voucherAmount)
                                    .add(deductCouponDiscount)
                                    .setScale(5, BigDecimal.ROUND_HALF_UP));
                            //折扣后支付金额
                            vo.setDiscountAmount(discountAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                            //现金支付金额
                            vo.setCashAmount(cashAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                            //信用额度支付金额
                            vo.setCreditAmount(creditAmount.setScale(2, BigDecimal.ROUND_UP));
                            //充值现金券支付金额
                            vo.setVoucherAmount(voucherAmount.setScale(2, BigDecimal.ROUND_UP));
                            vo.setInvoiceAmount(invoiceAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                            //优惠券优惠金额
                            vo.setCouponDiscount(couponDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                            //抵扣现金券支付金额
                            vo.setDeductCouponDiscount(deductCouponDiscount.setScale(2, BigDecimal.ROUND_HALF_UP));
                            //套餐包优惠金额
                            vo.setBizBagDiscount(bizBagDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                            // 询价优惠金额
                            vo.setPricingDiscount( pricingDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                            //询价折扣金额

                            //优惠金额=套餐包优惠+优惠卷折扣+充值现金券支付金额+抵扣现金券支付金额
                            vo.setDiscountTotalAmount(
                                    pricingDiscount.add(deductCouponDiscount)
                                            .add(bizBagDiscount)
                                            .add(voucherAmount)
                                            .add(couponDiscount)
                                            .setScale(5, BigDecimal.ROUND_HALF_UP));
                            // 原始金额
                            vo.setPretaxGrossAmount(pretaxGrossAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
                            vos.add(vo);
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(vos) && vos.size() > 0) {
                    Set<Long> owenIds = new HashSet<>();
                    Set<Long> orgIds = new HashSet<>();
                    for (BillBillingCycleCostVo groupBy : vos) {
                        if (Objects.nonNull(groupBy.getPayTime())) {
                            groupBy.setPayTimeStr(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(groupBy.getPayTime()));
                        }
                        owenIds.add(groupBy.getOwnerId());
                        orgIds.add(groupBy.getOrgId());
                    }
                    if (CollectionUtil.isNotEmpty(owenIds) && owenIds.size() > 0) {
                        QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper();
                        queryWrapper.in("id", owenIds);
                        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(queryWrapper);
                        if (CollectionUtil.isNotEmpty(bizBillingAccounts) && bizBillingAccounts.size() > 0) {
                            for (BillBillingCycleCostVo groupBy : vos) {
                                for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
                                    if (groupBy.getOwnerId().equals(bizBillingAccount.getId())) {
                                        groupBy.setAccountName(bizBillingAccount.getAccountName());
                                    }
                                }
                            }
                        }
                    }

                    if (!orgIds.isEmpty() && orgIds.size() > 0) {
                        Map param = new HashMap(1);
                        param.put("orgIds", orgIds);
                        List<Org> orgInfo = orgService.getOrgInfo(param);
                        if (CollectionUtil.isNotEmpty(orgInfo) && orgInfo.size() > 0) {
                            for (BillBillingCycleCostVo instance : vos) {
                                for (Org org : orgInfo) {
                                    if (instance.getOrgId().equals(org.getOrgSid())) {
                                        instance.setOrgName(org.getOrgName());
                                        instance.setOrgName(org.getOrgName());
                                        instance.setDistributorName(org.getDistributorName());
                                    }
                                }
                            }
                        }
                    }
                }
                response.addAll(vos);
            }
        }
            return response;
    }

    /**
     * 处理账期拆分数据
     * @param vo BillBillingCycleCostVo
     */
    private void dealSplitByPeriod(BillBillingCycleCostVo vo) {
        GaapCostDetailRequest gaapCostDetailRequest = new GaapCostDetailRequest();
        gaapCostDetailRequest.setIds(vo.getIds());
        gaapCostDetailRequest.setGroupByPeriod(vo.getBillingCycle());
        IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> instanceGaapCostIPage = listBillDetails(
                gaapCostDetailRequest);
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> records = instanceGaapCostIPage.getRecords();

        vo.setPretaxGrossAmount(records.stream().map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getPretaxGrossAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setAmount(records.stream().map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getPretaxAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setEraseZeroAmount(records.stream()
                                                .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getEraseZeroAmount)
                                                .filter(Objects::nonNull)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setCashAmount(
                records.stream().map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getCashAmount)
                       .filter(Objects::nonNull)
                       .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setCouponAmount(records.stream()
            .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getCouponAmount)
                                             .filter(Objects::nonNull)
                                             .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setDeductCouponDiscount(records.stream()
            .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getDeductCouponDiscount)
                                                     .filter(Objects::nonNull)
                                                     .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setBizBagDiscount(records.stream()
                                               .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getBagDiscountAmount)
                                               .filter(Objects::nonNull)
                                               .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setVoucherAmount(records.stream()
            .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getCouponAmount)
                                              .filter(Objects::nonNull)
                                              .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setCouponDiscount(records.stream()
                                               .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getCouponDiscount)
                                               .filter(Objects::nonNull)
                                               .reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setPricingDiscount(records.stream()
                                                .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getPricingDiscount)
                                                .filter(Objects::nonNull)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add));

        vo.setDiscountTotalAmount(
                vo.getPricingDiscount().add(vo.getDeductCouponDiscount())
                  .add(vo.getBizBagDiscount())
                  .add(vo.getVoucherAmount())
                  .add(vo.getCouponDiscount())
                  .setScale(5, BigDecimal.ROUND_HALF_UP));
    }

    /**
         * 组装账期查询条件类
         *
         * @param request
         * @return
         */
        private Aggregation getCycleCriteria (DescribeGaapCostRequest request, AuthUser authUserInfo){
            // 分页参数为null时默认赋值
            if (ObjectUtil.isEmpty(request.getPagenum())) {
                request.setPagenum(0L);
            }
            if (ObjectUtil.isEmpty(request.getPagesize())) {
                request.setPagesize(10L);
            }

            Criteria criteria = new Criteria();
            Aggregation aggregation = null;
            AuthUser authUser;
            if (Objects.nonNull(authUserInfo)) {
                authUser = authUserInfo;
            } else {
                authUser = RequestContextUtil.getAuthUserInfo();
            }

            //平台分为运营管理员，分销商，租户
            // 运营管理员和租户 userType 都为 03，但是运营管理员没有组织id
            // 分销上管理员和租户都有组织id, 就根据userType区分，userType位03是租户，userType是04为分销商管理员
            //获取所需要查询的组织id
            List<Long> orgSidList = new ArrayList<>();
            if (Objects.nonNull(authUser) && Objects.nonNull(
                    authUser.getOrgSid())) {
                if (Objects.equals(authUser.getUserType(), UserType.DISTRIBUTOR_USER)) {
                    List<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> roleList = roleMapper.findRolesByUserSid(
                            authUserInfo.getUserSid());
                    Optional<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> first = roleList.stream()
                            .min(Comparator.comparing(
                                    cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role::getDataScope));
                    if (first.isPresent()) {
                        cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role role = first.get();
                        if (Objects.equals(DataScopeEnum.DATA_SCOPE_COMPANY.getScope(), role.getDataScope())) {
                            // 销售
                            List<BizBillingAccount> selfAndSelfCustomer = bizBillingAccountMapper.findSelfAndSelfCustomer(
                                    authUser.getUserSid(), authUser.getEntityId());
                            Set<Long> orgSids = selfAndSelfCustomer.stream()
                                    .map(BizBillingAccount::getOrgSid)
                                    .collect(Collectors.toSet());
                            for (Long orgSid : orgSids) {
                                orgSids.addAll(orgService.selectChildrenOrgIds(orgSid));
                            }
                            criteria.and("orgId").in(orgSids);
                            orgSidList.addAll(orgSids);
                        } else {
                            //如果当前登录用户是分销商管理员
                            List<Long> orgSids = orgService.selectCustomerOrgSids(authUser.getOrgSid());
                            criteria.and("orgId").in(orgSids);
                            orgSidList.addAll(orgSids);
                        }
                    }
                } else if (Objects.equals(authUser.getUserType(), UserType.PLATFORM_USER)) {
                    //如果当期登录人是租户管理员
                    List<Long> orgSids = new ArrayList<>();
                    orgSids.add(authUser.getOrgSid());
                    orgSidList.addAll(orgSids);
                }
            }

            if (StringUtil.isEmpty(request.getExportType()) || (StringUtil.isNotEmpty(request.getExportType())
                    && !"all".equals(request.getExportType()))) {

                if ("current".equals(request.getExportType())) {
                    //导出当前月份
                    criteria.and("billingCycle")
                            .gte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                    cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getFirstDay()
                                    , cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN))
                            .lte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                    cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getLastDay(),
                                    cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN));
                } else if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())
                        && !"undefined".equals(request.getStartTime()) && !"undefined".equals(request.getEndTime())) {
                    //开始时间，结束时间导出
                    criteria.andOperator(Criteria.where("billingCycle").gte(request.getStartTime()),
                            Criteria.where("billingCycle").lte(request.getEndTime()));
                } else if ("month".equals(request.getExportType())) {
                    //特定的月份导出
                    String specifiMonth = request.getSpecifiMonth();
                    Date date = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(specifiMonth, cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN);
                    int year = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getYear(date);
                    int month = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getMonth(date);
                    criteria.and("billingCycle")
                            .gte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                    cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthFirstDay(year, month), cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN))
                            .lte(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDateFormat(
                                    cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getSpeMonthLastDay(year, month), cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN));
                }
                if (request.getBillNoLike() != null && !"".equals(request.getBillNoLike())) {
                    criteria.and("billNo").regex(Pattern.compile("^.*" + request.getBillNoLike() + ".*$"));
                }
                if (request.getPriceType() != null && !"".equals(request.getPriceType())) {
                    criteria.and("priceType").regex(Pattern.compile("^.*" + request.getPriceType() + ".*$"));
                }
                if (request.getProductCode() != null && !"".equals(request.getProductCode())) {
                    criteria.and("productCode").regex(Pattern.compile("^.*" + request.getProductCode() + ".*$"));
                }
                if (request.getSubscriptionType() != null && !"".equals(request.getSubscriptionType())) {
                    criteria.and("billType").regex(Pattern.compile("^.*" + request.getSubscriptionType() + ".*$"));
                }
                if (request.getInvoiceStatus() != null && !"".equals(request.getInvoiceStatus())) {
                    criteria.and("invoiceStatus").is(request.getInvoiceStatus());
                }
                if (request.getInvoiceAmountFlag() != null && Y.equals(request.getInvoiceAmountFlag())) {
                    criteria.and("invoiceAmount").gt(BigDecimal.ZERO);
                }
                if (Objects.nonNull(request.getBillingCycle())) {
                    criteria.and("billingCycle").is(request.getBillingCycle());
                }
                //用户控制台,根据组织id,查询账户id过滤数据
                if (CollectionUtil.isNotEmpty(orgSidList) && !"bss".equals(authUserInfo.getRemark())) {
                    Set<Long> accountIdsSet = this.findAccountIdsByOrgSid(orgSidList);
                    if (CollectionUtil.isNotEmpty(accountIdsSet)) {
                        criteria.and("ownerId").in(accountIdsSet);
                    } else {
                        criteria.and("ownerId").in(-1L);
                    }
                }
            } else {
                if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())
                        && !"undefined".equals(request.getStartTime()) && !"undefined".equals(request.getEndTime())) {
                    // 按照开始时间,结束时间导出
                    criteria.andOperator(Criteria.where("billingCycle").gte(request.getStartTime())
                            , Criteria.where("billingCycle").lte(request.getEndTime()));
                }
                //查询组织下id的账单
                if (CollectionUtil.isNotEmpty(orgSidList)) {
                    Set<Long> accountIdsSet = this.findAccountIdsByOrgSid(orgSidList);
                    if (CollectionUtil.isNotEmpty(accountIdsSet)) {
                        criteria.and("ownerId").type(Type.INT_64).in(accountIdsSet);
                    } else {
                        criteria.and("ownerId").type(Type.INT_64).in(-1L);
                    }
                }
            }

            //按照客户名称筛选
            if (request.getCustomerName() != null && !"".equals(request.getCustomerName())) {
                //查询accountId
                Set<Long> accountIds = new HashSet<>();
                List<BizBillingAccount> bizBillingAccounts = bizBillingAccountService.selectAccountInfoByCustomerName(request);
                if (CollectionUtil.isNotEmpty(bizBillingAccounts) && bizBillingAccounts.size() > 0) {
                    for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
                        accountIds.add(bizBillingAccount.getId());
                    }
                }
                criteria.and("ownerId").in(accountIds);
            }

            //按照分销商名称筛选
            if (request.getDistributorName() != null && !"".equals(request.getDistributorName())) {
                List<Long> accountIds = new ArrayList<>();
                BizDistributor distributor = new BizDistributor();
                distributor.setName(request.getDistributorName());
                List<BizDistributor> distributors = bizDistributorService.selectDistributorInfoByRequest(distributor);
                if (CollectionUtil.isNotEmpty(distributors) && distributors.size() > 0) {
                    List<Long> disIds = new ArrayList<>();
                    for (BizDistributor bizDistributor : distributors) {
                        Long id = bizDistributor.getId();
                        disIds.add(id);
                    }
                    if (CollectionUtil.isNotEmpty(disIds) && disIds.size() > 0) {
                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.in("distributor_id", disIds);
                        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountService.getBaseMapper().selectList(queryWrapper);

                        if (CollectionUtil.isNotEmpty(bizBillingAccounts) && bizBillingAccounts.size() > 0) {
                            for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
                                accountIds.add(bizBillingAccount.getId());
                            }
                        }
                    }
                }
                criteria.and("ownerId").in(accountIds);

            }
            //排除退订中的订单
            if (CollectionUtil.isNotEmpty(orgSidList) && orgSidList.size() > 0) {
                if (CollectionUtil.isNotEmpty(orgSidList) && orgSidList.size() > 0) {
                    List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectReleaseByOrgSid(orgSidList);
                    if (CollectionUtil.isNotEmpty(serviceOrderVos) && serviceOrderVos.size() > 0) {
                        List<String> orderIds = new ArrayList<>();
                        for (ServiceOrderVo serviceOrderVo : serviceOrderVos) {
                            orderIds.add(serviceOrderVo.getOrderId());
                        }
                        if (CollectionUtil.isNotEmpty(orderIds) && orderIds.size() > 0) {
                            Query queryDetail = new Query(Criteria.where("billBillingCycleId").exists(true).and("orderId").in(orderIds));
                            List<InstanceGaapCost> costs = mongoTemplate.find(queryDetail, InstanceGaapCost.class);
                            if (CollectionUtil.isNotEmpty(costs) && costs.size() > 0) {
                                Set<Object> cycleIds = new HashSet<>();
                                for (InstanceGaapCost cost : costs) {
                                    cycleIds.add(new ObjectId(cost.getBillBillingCycleId()));
                                }
                                criteria.and("id").nin(cycleIds);
                            }
                        }
                    }
                }
            }

            //根据运营实体过滤
            if (authUser != null && ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUser.getRemark())) {
                criteria.and("entityId").in(Optional.ofNullable(RequestContextUtil.getEntityId()).orElse(1L).toString(), Optional.ofNullable(RequestContextUtil.getEntityId()).orElse(1L));
            }
            String[] fields = new String[]{"entityId", "entityName", "billingCycle", "billType", "priceType", "payTime", "ids", "cycleIds",
                    "amount", "officialAmount", "discountAmount", "cashAmount", "creditAmount", "voucherAmount", "invoiceAmount"};
            if (Y.equals(request.getPageFlag())) {
                if ("customer".equals(request.getGroupByFlag())) {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "ownerId", "billType", "priceType", "entityName")
                                    .first("entityId").as("entityId").first("ownerId")
                                    .as("ownerId")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids"),
                            Aggregation.project("entityId", "ownerId", "billingCycle", "billType", "priceType", "payTime", "ids", "entityName")
                                    .and("id")
                                    .previousOperation(),
                            // 排序
                            Aggregation.sort(Sort.Direction.DESC, "payTime")
                            // 分页：页码
                            , Aggregation.skip((request.getPagenum() - 1) * request.getPagesize())
                            // 分页：条数
                            , Aggregation.limit((long) request.getPagesize())

                    );
                } else if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUser.getRemark())) {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "entityName", "billingCycle", "billType", "priceType")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids")
                                    .addToSet("billBillingCycleId")
                                    .as("cycleIds")
                                    .sum(ConvertOperators.valueOf("amount").convertToDecimal()).as("amount")
                                    .sum(Round.roundValueOf(ConvertOperators.valueOf("officialAmount").convertToDecimal()).place(5)).as("officialAmount")
                                    .sum(ConvertOperators.valueOf("discountAmount").convertToDecimal()).as("discountAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("creditAmount").convertToDecimal(),2))).as("creditAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("voucherAmount").convertToDecimal(),2))).as("voucherAmount")
                                    .sum(ConvertOperators.valueOf("invoiceAmount").convertToDecimal()).as("invoiceAmount"),
                            Aggregation.project(fields)
                                    .and("id")
                                    .previousOperation(),
                            // 排序
                            Aggregation.sort(Sort.Direction.DESC, "entityId", "billingCycle", "payTime")
                            // 分页：页码
                            , Aggregation.skip((request.getPagenum() - 1) * request.getPagesize())
                            // 分页：条数
                            , Aggregation.limit((long) request.getPagesize())

                    );
                } else {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "entityName", "billingCycle", "billType", "priceType")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids")
                                    .addToSet("billBillingCycleId")
                                    .as("cycleIds")
                                    .sum(ConvertOperators.valueOf("amount").convertToDecimal()).as("amount")
                                    .sum(Round.roundValueOf(ConvertOperators.valueOf("officialAmount").convertToDecimal()).place(5)).as("officialAmount")
                                    .sum(ConvertOperators.valueOf("discountAmount").convertToDecimal()).as("discountAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("creditAmount").convertToDecimal(),2))).as("creditAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("voucherAmount").convertToDecimal(),2))).as("voucherAmount")
                                    .sum(ConvertOperators.valueOf("invoiceAmount").convertToDecimal()).as("invoiceAmount"),
                            Aggregation.project(fields)
                                    .and("id")
                                    .previousOperation(),
                            // 排序
                            Aggregation.sort(Sort.Direction.DESC, "entityId", "billingCycle", "payTime")
                            // 分页：页码
                            , Aggregation.skip((Long.valueOf(request.getPagenum()) - 1) * request.getPagesize())
                            // 分页：条数
                            , Aggregation.limit((long) request.getPagesize()));
                }

            } else {
                if ("customer".equals(request.getGroupByFlag())) {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "ownerId", "billType", "priceType")
                                    .first("ownerId")
                                    .as("ownerId")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids"),
                            Aggregation.project("entityId", "ownerId", "billingCycle", "billType", "priceType", "payTime", "ids")
                                    .and("id")
                                    .previousOperation()
                    );
                } else if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUser.getRemark())) {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "entityName", "billingCycle", "billType", "priceType")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids")
                                    .addToSet("billBillingCycleId")
                                    .as("cycleIds")
                                    .sum(ConvertOperators.valueOf("amount").convertToDecimal()).as("amount")
                                    .sum(Round.roundValueOf(ConvertOperators.valueOf("officialAmount").convertToDecimal()).place(5)).as("officialAmount")
                                    .sum(ConvertOperators.valueOf("discountAmount").convertToDecimal()).as("discountAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("creditAmount").convertToDecimal(),2))).as("creditAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("voucherAmount").convertToDecimal(),2))).as("voucherAmount")
                                    .sum(ConvertOperators.valueOf("invoiceAmount").convertToDecimal()).as("invoiceAmount"),
                            Aggregation.project(fields)
                                    .and("id")
                                    .previousOperation(),
                            // 排序
                            Aggregation.sort(Sort.Direction.DESC, "entityId", "billingCycle", "payTime")
                    );

                } else {
                    aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),
                            Aggregation.group("entityId", "entityName", "billingCycle", "billType", "priceType")
                                    .first("billingCycle")
                                    .as("billingCycle")
                                    .first("billType")
                                    .as("billType")
                                    .first("priceType")
                                    .as("priceType")
                                    .last("payTime")
                                    .as("payTime")
                                    .addToSet("_id")
                                    .as("ids").addToSet("billBillingCycleId")
                                    .as("cycleIds")
                                    .sum(ConvertOperators.valueOf("amount").convertToDecimal()).as("amount")
                                    .sum(Round.roundValueOf(ConvertOperators.valueOf("officialAmount").convertToDecimal()).place(5)).as("officialAmount")
                                    .sum(ConvertOperators.valueOf("discountAmount").convertToDecimal()).as("discountAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("creditAmount").convertToDecimal(),2))).as("creditAmount")
                                    .sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("voucherAmount").convertToDecimal(),2))).as("voucherAmount")
                                    .sum(ConvertOperators.valueOf("invoiceAmount").convertToDecimal()).as("invoiceAmount"),
                            Aggregation.project(fields)
                                    .and("id")
                                    .previousOperation(),
                            // 排序
                            Aggregation.sort(Sort.Direction.DESC, "entityId", "billingCycle", "payTime")
                    );
                }
            }

            return aggregation;
        }

        @Data
        @Builder
        private static class DurationBill {

            private String chargeType;
            private List<InstanceGaapCost> costs;

            private Date start;
            private Date end;
            private Date now;

            private OrderDetail detail;
            private ServiceInstance instance;
            private Resource resource;

            private Boolean isFirstTime;

            private Date useStartTime;
            private Date useEndTime;
            private BigDecimal duration = BigDecimal.ZERO;

            private BigDecimal eachDisCount = BigDecimal.ZERO;
            private BigDecimal originalPrice = BigDecimal.ZERO;
            private BigDecimal tradePrice = BigDecimal.ZERO;
            private List<BizBillingPriceVO> detailPrices = Lists.newArrayList();
            private List<BizAccountDeal> deals;
        }

        private void filterUnsubscribe
        (List < cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost > totalCosts) {
            //过滤已经退订的单据金额
            Iterator<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> it = totalCosts.iterator();
            while (it.hasNext()) {
                cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost next = it.next();
                BigDecimal nextCashAmount = next.getCashAmount() != null ? next.getCashAmount().abs() : BigDecimal.ZERO;
                BigDecimal nextCreditAmount = next.getCreditAmount() != null ? next.getCreditAmount().abs() : BigDecimal.ZERO;
                BigDecimal nextCouponAmount = next.getCouponAmount() != null ? next.getCouponAmount().abs() : BigDecimal.ZERO;
                BigDecimal nextAmount = nextCashAmount.add(nextCreditAmount).add(nextCouponAmount).setScale(5, BigDecimal.ROUND_HALF_UP);
                if (Objects.nonNull(next.getType()) && OrderType.APPLY.equals(next.getType())) {
                    if (Objects.nonNull(next.getOrderStatus()) && !"".equals(next.getOrderStatus())) {
                        if (OrderStatus.RELEASE_SUCCESS.equals(next.getOrderStatus())) {
                            Query queryRelease = new Query();
                            Criteria cRelease =
                                    Criteria.where("type").
                                            is(OrderType.RELEASE).and("billBillingCycleId").is(next.getBillBillingCycleId());

                            queryRelease.addCriteria(cRelease);
                            List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> costList = mongoTemplate.find(queryRelease,
                                    cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class, "biz_bill_usage_item");
                            BigDecimal useAmount = BigDecimal.ZERO;
                            BigDecimal cashAmount = BigDecimal.ZERO;
                            BigDecimal creditAmount = BigDecimal.ZERO;
                            BigDecimal couponAmount = BigDecimal.ZERO;
                            if (CollectionUtil.isNotEmpty(costList)) {
                                for (cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost instanceGaapCost : costList) {
                                    cashAmount = instanceGaapCost.getCashAmount() != null ? instanceGaapCost.getCashAmount().abs() : BigDecimal.ZERO;
                                    creditAmount = instanceGaapCost.getCreditAmount() != null ? instanceGaapCost.getCreditAmount().abs() : BigDecimal.ZERO;
                                    couponAmount = instanceGaapCost.getCouponAmount() != null ? instanceGaapCost.getCouponAmount().abs() : BigDecimal.ZERO;
                                    useAmount = useAmount.add(cashAmount).add(creditAmount).add(couponAmount).setScale(5, BigDecimal.ROUND_HALF_UP);
                                }
                            }

                            next.setCashAmount(BigDecimalUtil.remainTwoPointAmount(nextCashAmount.subtract(cashAmount)));
                            next.setCreditAmount(BigDecimalUtil.remainTwoPointAmount(nextCreditAmount.subtract(creditAmount)));
                            next.setCouponAmount(BigDecimalUtil.remainTwoPointAmount(nextCouponAmount.subtract(couponAmount)));
                            next.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(useAmount));

                        }
                    }
                }

            }

        }

    /**
     * 查询所有的org表
     * <br>
     * 获取组织map
     */
    public Map<Long, Org> getOrgMap() {
        List<Org> list = orgService.list();
        Map<Long, Org> collect = list.stream()
                .filter(org -> !StringUtil.equals("distributor", org.getOrgType()))
                .collect(Collectors.toMap(Org::getOrgSid, org -> org, (k1, k2) -> k1));
        collect.forEach((key, value) -> {
            collect.put(key, getOrg(value, list));
        });
        return collect;
    }

        private List<BillBillingCycleCostVo> doGroupByInfo
        (Map < Object, List < BillBillingCycleCost >> collect, List < cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost > gaapCosts, String
        detailFlag){
            List<BillBillingCycleCostVo> response = new ArrayList<>();
            for (Map.Entry<Object, List<BillBillingCycleCost>> objectListEntry : collect.entrySet()) {
                List<BillBillingCycleCost> value1 = objectListEntry.getValue();
                BillBillingCycleCostVo bill = BeanConvertUtil.convert(value1.get(0), BillBillingCycleCostVo.class);
                bill.setId("");
                //计算汇总金额
                //实际支付金额
                BigDecimal amount = BigDecimal.ZERO;
                //现金支付金额
                BigDecimal cashAmount = BigDecimal.ZERO;
                //额度支付金额
                BigDecimal creditAmount = BigDecimal.ZERO;
                //现金卷支付金额
                BigDecimal voucherAmount = BigDecimal.ZERO;
                //优惠卷优惠金额
                BigDecimal couponDiscount = BigDecimal.ZERO;
                List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> costs = new ArrayList<>();
                for (BillBillingCycleCost cycleCost : value1) {
                    BigDecimal a = cycleCost.getAmount() == null ? BigDecimal.ZERO : cycleCost.getAmount();
                    BigDecimal cash = cycleCost.getCashAmount() == null ? BigDecimal.ZERO : cycleCost.getCashAmount();
                    BigDecimal credit = cycleCost.getCreditAmount() == null ? BigDecimal.ZERO : cycleCost.getCreditAmount();
                    BigDecimal voucher = cycleCost.getVoucherAmount() == null ? BigDecimal.ZERO : cycleCost.getVoucherAmount();
                    BigDecimal coupon = cycleCost.getCouponDiscount() == null ? BigDecimal.ZERO : cycleCost.getCouponDiscount();
                    amount = amount.add(a);
                    cashAmount = cashAmount.add(cash);
                    creditAmount = creditAmount.add(credit);
                    voucherAmount = voucherAmount.add(voucher);
                    couponDiscount = couponDiscount.add(coupon);
                    if (Y.equals(detailFlag)) {
                        for (cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost cost : gaapCosts) {
                            BigDecimal cashAmount1 = cost.getCashAmount() == null ? BigDecimal.ZERO : cost.getCashAmount();
                            BigDecimal couponAmount1 = cost.getCouponAmount() == null ? BigDecimal.ZERO : cost.getCouponAmount();
                            BigDecimal creditAmount1 = cost.getCreditAmount() == null ? BigDecimal.ZERO : cost.getCreditAmount();
                            BigDecimal discountAmount1 = cost.getDiscountAmount() == null ? BigDecimal.ZERO : cost.getDiscountAmount();
                            BigDecimal costAmount = cashAmount1
                                    .add(couponAmount1)
                                    .add(creditAmount1)
                                    .add(discountAmount1);
                            if (Objects.nonNull(cost.getBillBillingCycleId())
                                    && String.valueOf(cycleCost.getId()).equals(cost.getBillBillingCycleId())
                                    && costAmount.compareTo(BigDecimal.ZERO) > 0) {
                                costs.add(cost);
                            }
                        }
                    }
                }
                bill.setAmount(amount.setScale(5, BigDecimal.ROUND_HALF_UP));
                bill.setCashAmount(BigDecimalUtil.remainTwoPointAmount(cashAmount));
                bill.setCreditAmount(BigDecimalUtil.remainTwoPointAmount(creditAmount));
                bill.setVoucherAmount(BigDecimalUtil.remainTwoPointAmount(voucherAmount));
                bill.setCouponDiscount(couponDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                bill.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
                bill.setCosts(costs);
                response.add(bill);
            }
            return response;
        }
        private List<BillBillingCycleCostVo> listSplit (List < BillBillingCycleCostVo > list,int pagenum, int pagesize){
            List<BillBillingCycleCostVo> newList = null;
            int total = list.size();
            newList = list.subList(pagesize * (pagenum - 1), ((pagesize * pagenum) > total ? total : (pagesize * pagenum)));
            return newList;
        }

        @Override
        public RestResult asynExportBillDetails (DescribeGaapCostRequest request, String moduleType){

            if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
                Date startDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getStartTime(), "yyyy-MM");
                Date endDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getEndTime(), "yyyy-MM");
                if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1261968050));
                }
                if (DateUtils.addMonths(startDate, 3).before(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_497753068));
                }
            }

            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (Objects.isNull(request.getOrgSid())) {
                request.setOrgSid(authUserInfo.getOrgSid());
            }
            BizDownload download = new BizDownload();
            download.setOperationType(ExportTypeEnum.BILLDETAIL.getCode());
            //添加下载任务数据
            download = getBizDownload(download, request, moduleType, authUserInfo);
            if (Objects.isNull(download.getDownloadId())) {
                new RestResult(RestResult.Status.FAILURE, "账单明细数据下载异常，请稍后重试!");
            }
            new ExportThreadUtil(exportService,
                    request,
                    moduleType,
                    ExportTypeEnum.BILLDETAIL.getCode(),
                    download.getDownloadId(),
                    authUserInfo
            ).submit();
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_662058302));
        }

        /**
         * 客户管理账单明细导出
         * @param request
         * @param moduleType
         * @return RestResult
         */
        @Override
        public RestResult asynExportFeginBillDetails (DescribeGaapCostRequest request, String moduleType){
            if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
                Date startDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getStartTime(), "yyyy-MM");
                Date endDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getEndTime(), "yyyy-MM");
                if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1261968050));
                }
                if (DateUtils.addMonths(startDate, 5).before(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_497753068));
                }
            }

            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (Objects.nonNull(request.getOrgSid())) {
                if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
                    List<Long> orgSids = orgService.selectCustomerOrgSids(authUserInfo.getOrgSid());
                    if (!orgSids.contains(request.getOrgSid())) {
                        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                    }
                }
                authUserInfo.setOrgSid(request.getOrgSid());
            }
            BizDownload download = new BizDownload();
            download.setOperationType(ExportTypeEnum.BILLDETAIL.getCode());
            //添加下载任务数据
            download = getBizDownload(download, request, moduleType, authUserInfo);
            if (Objects.isNull(download.getDownloadId())) {
                new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.CURRENT_STATUS_NOT_DOWN));
            }
            new ExportThreadUtil(exportService,
                    request,
                    moduleType,
                    ExportTypeEnum.BILLDETAIL.getCode(),
                    download.getDownloadId(),
                    authUserInfo
            ).submit();
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_662058302));
        }

        @Override
        public void upgradeOfBills () {
            // 处理订单-订单来源
            // 1、处理开通类型订单
            DBUtils.INSTANCE.update(UPGRADE_OF_BILLS_1);
            // 2、处理非开通类型订单
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(
                    new QueryWrapper<ServiceOrder>().lambda().isNull(ServiceOrder::getOrderSourceSn));
            log.info("提供给upgrade模块处理历史数据-处理订单(订单来源)-总共待处理数据：[{}]", serviceOrders.size());
            if (serviceOrders.size() > 0) {
                List<OrderSourceSnVO> orderSourceSnVOS = DBUtils.INSTANCE.queryBeanList(UPGRADE_OF_BILLS_2,
                        OrderSourceSnVO.class);
                Map<String, String> orderSourceSnMaps = orderSourceSnVOS.stream()
                        .filter(t -> Objects.nonNull(t.getApplyOrderSn()))
                        .collect(Collectors.toMap(
                                OrderSourceSnVO::getOrderSn,
                                OrderSourceSnVO::getApplyOrderSn,
                                (key1, key2) -> key1));
                List<ServiceOrder> orderSourceClusterIdNotNull = serviceOrders.stream()
                        .filter(t -> Objects.nonNull(
                                t.getClusterId()))
                        .collect(Collectors.toList());
                for (ServiceOrder serviceOrder : orderSourceClusterIdNotNull) {
                    serviceOrder.setOrderSourceSn(orderSourceSnMaps.get(serviceOrder.getOrderSn()));
                    serviceOrderMapper.updateById(serviceOrder);
                }
                log.info("提供给upgrade模块处理历史数据-处理订单(订单来源)-orderSourceClusterIdNotNull-待处理数据：[{}]，已处理数据：[{}]，未处理数据：[{}]",
                        orderSourceClusterIdNotNull.size(),
                        orderSourceClusterIdNotNull.stream().filter(t -> Objects.nonNull(t.getOrderSourceSn())).count(),
                        JSON.toJSONString(orderSourceClusterIdNotNull.stream()
                                .filter(t -> Objects.isNull(t.getOrderSourceSn()))
                                .map(ServiceOrder::getOrderSn)
                                .collect(
                                        Collectors.toList())));
                List<ServiceOrder> orderSourceClusterIdIsNull = serviceOrders.stream()
                        .filter(t -> Objects.isNull(t.getClusterId()))
                        .collect(Collectors.toList());
                for (ServiceOrder serviceOrder : orderSourceClusterIdIsNull) {
                    // 弹性文件
                    if (serviceOrder.getProductName().contains(ProductCodeEnum.SFS.getProductType())) {
                        List<ServiceOrder> serviceOrdersSFS = serviceOrderMapper.selectList(
                                new QueryWrapper<ServiceOrder>().lambda()
                                        .eq(ServiceOrder::getName,
                                                serviceOrder.getName())
                                        .eq(ServiceOrder::getOwnerId,
                                                serviceOrder.getOwnerId())
                                        .eq(ServiceOrder::getType,
                                                OrderType.APPLY));
                        if (serviceOrdersSFS.size() > 0) {
                            serviceOrder.setOrderSourceSn(serviceOrdersSFS.get(0).getOrderSn());
                            serviceOrderMapper.updateById(serviceOrder);
                        }
                    }
                    // MA共享
                    if (serviceOrder.getProductName().equals(ProductCodeEnum.MODELARTS.getProductName())) {
                        List<ServiceOrder> serviceOrdersModelArts = serviceOrderMapper.selectList(
                                new QueryWrapper<ServiceOrder>().lambda()
                                        .eq(ServiceOrder::getName,
                                                serviceOrder.getName())
                                        .eq(ServiceOrder::getOwnerId,
                                                serviceOrder.getOwnerId())
                                        .eq(ServiceOrder::getType,
                                                OrderType.APPLY)
                                        .lt(ServiceOrder::getCreatedDt,
                                                serviceOrder.getCreatedDt())
                                        .orderByDesc(ServiceOrder::getCreatedDt));
                        if (serviceOrdersModelArts.size() > 0) {
                            serviceOrder.setOrderSourceSn(serviceOrdersModelArts.get(0).getOrderSn());
                            serviceOrderMapper.updateById(serviceOrder);
                        }
                    }
                    // 套餐包
                    if (serviceOrder.getProductName().equals(ProductCodeEnum.BIZ_BAG.getProductName())) {
                        List<ServiceOrder> serviceOrdersBizBag = serviceOrderMapper.selectList(
                                new QueryWrapper<ServiceOrder>().lambda()
                                        .eq(ServiceOrder::getName,
                                                serviceOrder.getName())
                                        .eq(ServiceOrder::getBizBillingAccountId,
                                                serviceOrder.getBizBillingAccountId())
                                        .eq(ServiceOrder::getType,
                                                OrderType.APPLY)
                                        .lt(ServiceOrder::getCreatedDt,
                                                serviceOrder.getCreatedDt())
                                        .orderByDesc(ServiceOrder::getCreatedDt));
                        if (serviceOrdersBizBag.size() > 0) {
                            serviceOrder.setOrderSourceSn(serviceOrdersBizBag.get(0).getOrderSn());
                            serviceOrderMapper.updateById(serviceOrder);
                        }
                    }
                }
                log.info("提供给upgrade模块处理历史数据-处理订单(订单来源)-orderSourceClusterIdIsNull-待处理数据：[{}]，已处理数据：[{}]，未处理数据：[{}]",
                        orderSourceClusterIdIsNull.size(),
                        orderSourceClusterIdIsNull.stream().filter(t -> Objects.nonNull(t.getOrderSourceSn())).count(),
                        JSON.toJSONString(orderSourceClusterIdIsNull.stream()
                                .filter(t -> Objects.isNull(t.getOrderSourceSn()))
                                .map(ServiceOrder::getOrderSn)
                                .collect(
                                        Collectors.toList())));
            }
            // 处理账单明细-订单来源/订单类型
            org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                    .where("orderSourceSn").isNull();

            long count = mongoTemplate.count(Query.query(criteria), InstanceGaapCost.class);
            List<InstanceGaapCost> invoicedCostList;
            DescribeGaapCostRequest request = new DescribeGaapCostRequest();
            request.setOrderSourceSnIsNull(true);
            long num = 0L;
            request.setPagesize(50000L);
            long pageNum = 0L;
            do {
                request.setPagenum(pageNum);
                invoicedCostList = exportBills(request);
                if (CollectionUtil.isNotEmpty(invoicedCostList)) {
                    pageNum++;
                    log.info("提供给upgrade模块处理历史数据-处理账单明细(订单来源/订单类型)-本次处理数：【{}】 已处理数: {}  总数： {}",
                            invoicedCostList.size(), (invoicedCostList.size() + num), count);
                    log.info("提供给upgrade模块处理历史数据-处理账单明细(订单来源/订单类型)-总共待处理数据：[{}]", invoicedCostList.size());
                    if (invoicedCostList.size() > 0) {
                        List<ServiceOrder> serviceOrderAll = serviceOrderMapper.selectList(new QueryWrapper<>());
                        Map<String, String> orderSourceSnAllMaps = serviceOrderAll.stream()
                                .filter(t -> Objects.nonNull(
                                        t.getOrderSourceSn()))
                                .collect(Collectors.toMap(
                                        ServiceOrder::getOrderSn,
                                        ServiceOrder::getOrderSourceSn,
                                        (key1, key2) -> key1));
                        Map<String, String> typeAllMaps = serviceOrderAll.stream().filter(t -> Objects.nonNull(t.getType()))
                                .collect(Collectors.toMap(
                                        ServiceOrder::getOrderSn,
                                        ServiceOrder::getType,
                                        (key1, key2) -> key1));
                        for (InstanceGaapCost instanceGaapCost : invoicedCostList) {
                            Query query = new Query();
                            Update update = new Update();
                            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("billNo")
                                    .is(instanceGaapCost.getBillNo()));
                            update.set("orderSourceSn", orderSourceSnAllMaps.get(instanceGaapCost.getOrderSn()));
                            if (ResourceOperateEnum.SYNC.getOperate().equals(instanceGaapCost.getBillSource())) {
                                update.set("type", ResourceOperateEnum.SYNC.getOperate());
                            } else {
                                update.set("type", typeAllMaps.get(instanceGaapCost.getOrderSn()));
                            }
                            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");
                            if (updateResult.getMatchedCount() > 0) {
                                instanceGaapCost.setOrderSourceSn(orderSourceSnAllMaps.get(instanceGaapCost.getOrderSn()));
                                instanceGaapCost.setType(typeAllMaps.get(instanceGaapCost.getOrderSn()));
                            } else {
                                log.error("提供给upgrade模块处理历史数据-[{}]处理账单明细(订单来源/订单类型)-ERROR：[{}]",
                                        instanceGaapCost.getBillNo(), JSON.toJSONString(updateResult));
                            }
                        }
                    }
                    log.info("提供给upgrade模块处理历史数据-处理账单明细(订单来源/订单类型)-已处理数据：[{}]，未处理数据：[{}]",
                            invoicedCostList.stream()
                                    .filter(t -> (Objects.nonNull(t.getOrderSourceSn()) && Objects.nonNull(
                                            t.getType())))
                                    .count(),
                            JSON.toJSONString(invoicedCostList.stream()
                                    .filter(t -> (Objects.isNull(t.getOrderSourceSn()) || Objects
                                            .isNull(
                                                    t.getType())))
                                    .map(InstanceGaapCost::getBillNo)
                                    .collect(
                                            Collectors.toList())));
                }
            } while (CollectionUtil.isNotEmpty(invoicedCostList));

        }

    @Override
    public IPage listBillsCount(DescribeGaapCostRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        //根据当前用户的角色和数据权限配置获取该用户能够查看账单数据的最大账户数
        Set<Long> accountIdsSet = findBillingAccountByCurrentUserRole(authUser);
        request.setAccountIds(accountIdsSet);
        Criteria criteria = getCriteria(request, null);
        if (Objects.equals(authUser.getUserType(), UserType.DISTRIBUTOR_USER) && !criteria.getCriteriaObject()
            .containsKey("orgSid")) {
            //如果当前登录用户是分销商管理员
            List<Long> orgSids = orgService.selectCustomerOrgSids(authUser.getOrgSid());
            criteria.and("orgSid").in(orgSids);
        }
        Query queryCri = new Query(criteria);
        IPage<DescribeGaapCostResponse> responsePage = PageUtil.emptyPage();
        //按账期统计，是否分组统计
        long count = 0L;
        String groupByPeriod = request.getGroupByPeriod();
        if (StringUtils.isEmpty(groupByPeriod)) {
            count = mongoTemplate.count(queryCri, InstanceGaapCost.class);
        }else{
            count = getAggregationGroupCount(criteria,request);
        }
        if (count <= 0) {
            return responsePage;
        }
        responsePage.setTotal(count);
        return responsePage;
    }

    @Override
        public RestResult asynExportBillCycles (DescribeGaapCostRequest request, String moduleType){
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (Objects.equals(request.getExportType(), "all")) {
                Date startDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getStartTime(), "yyyy-MM");
                Date endDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getEndTime(), "yyyy-MM");
                if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1261968050));
                }
                if (DateUtils.addMonths(startDate, 5).before(endDate)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_329069122));
                }
            }
            BizDownload download = new BizDownload();
            download.setOperationType(ExportTypeEnum.BILLCYCLE.getCode());
            if ("bss".equals(moduleType)) {
                // 运营管理员周期导出分客户和账期
                download.setOperationType(ExportTypeEnum.BILLCYCLE_CYCLE.getCode());
                String groupByFlag = request.getGroupByFlag();
                if (StringUtils.isNotBlank(groupByFlag)) {
                    download.setOperationType(groupByFlag);
                }
            }
            download.setEntityId(authUserInfo.getEntityId());
            //添加下载任务数据
            download = getBizDownload(download, request, moduleType, authUserInfo);
            if (Objects.isNull(download.getDownloadId())) {
                new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2038660916));
            }
            new ExportThreadUtil(exportService,
                    request,
                    moduleType,
                    ExportTypeEnum.BILLCYCLE.getCode(),
                    download.getDownloadId(),
                    authUserInfo
            ).submit();
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1486919323));
        }

        @Override
        public List<BillBillingCycleCostVo> listExportCycle (DescribeGaapCostRequest request, AuthUser authUserInfo){
            Aggregation aggregation = this.getCycleCriteria(request, authUserInfo);
            List<BillBillingCycleCostVo> billBillingCycleCost = this.getBillBillingCycleCost(aggregation, request.getSplitByPeriod(), "N");
            return billBillingCycleCost;
        }

        private BizDownload getBizDownload (BizDownload download, DescribeGaapCostRequest request, String
        moduleType, AuthUser authUserInfo){
            // 租户在下载任务中存入accountId
            download.setAccountId(authUserInfo.getUserSid());
            download.setOrgSid(authUserInfo.getOrgSid());
            download.setDownloadNum(NoUtil.generateNo("DT"));
            download.setParam(JSON.toJSONString(request));
            download.setStatus(2);
            download.setCreatedBy(authUserInfo.getAccount());
            download.setCreatedDt(new Date());
            download.setVersion(1);
            download.setEntityId(authUserInfo.getEntityId());
            int insert = bizDownloadMapper.insert(download);
            return download;
    }

    @Override
    public RestResult asynExportBizBagBillDetails(DescribeGaapCostRequest request, String moduleType) {
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            Date startDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getStartTime(),
                                                                                            "yyyy-MM");
            Date endDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getEndTime(),
                                                                                          "yyyy-MM");
            if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1261968050));
            }
            if (DateUtils.addMonths(startDate, 3).before(endDate)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_216452256));
            }
        }

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(request.getOrgSid())) {
            request.setOrgSid(authUserInfo.getOrgSid());
        }
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.BIZBAG_BILLDETAIL.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, moduleType, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, "套餐包使用明细数据下载异常，请稍后重试!");
        }
        new ExportThreadUtil(exportService, request, moduleType,
                                                       ExportTypeEnum.BIZBAG_BILLDETAIL.getCode(),
                             download.getDownloadId(),
                             authUserInfo
        ).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1338214492));
    }


    @Override
    public RestResult asynExportBizBagOrder(DescribeBizBagOrderRequest request, String moduleType) {
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            Date startDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getStartTime(),
                                                                                            "yyyy-MM");
            Date endDate = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.parseDate(request.getEndTime(),
                                                                                          "yyyy-MM");
            if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1261968050));
            }
            if (DateUtils.addMonths(startDate, 5).before(endDate)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_409914578));
            }
        }

        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.BIZBAG_BILL_ORDER.getCode());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam("request【" + JSON.toJSONString(request) + "】---moduleType【" + moduleType + "】");
        download.setStatus(2);
        download.setCreatedBy(AuthUtil.getAuthUser().getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        download.setAccountId(authUserInfo.getUserSid());
        download.setCreatedBy(authUserInfo.getAccount());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, "套餐包订单数据下载异常，请稍后重试!");
        }
        new ExportThreadUtil(exportService, request, moduleType,
                             ExportTypeEnum.BIZBAG_BILL_ORDER.getCode(),
                             download.getDownloadId(), authUserInfo)
                .submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_737296150));
    }

    @Override
    public RestResult asynExportCustomer(ImportCustomerRequest request) {
        List<CustomerTemplate> customers = request.getCustomers();
        List<SubUserTemplate> subUsers = request.getSubUsers();
        BizDownload download =   bizDownloadMapper.selectOne(new QueryWrapper<BizDownload>().eq("download_num", request.getDownloadNum()));
        this.setCustomerBizDownload(download, customers, subUsers);
        bizDownloadMapper.updateById(download);

        new ExportThreadUtil(exportService,
                             request.getType(),
                             download.getDownloadId(),
                             customers,
                             subUsers,
                             RequestContextUtil.getAuthUserInfo()
        ).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1601343667), download.getDownloadId());
    }

    private void setCustomerBizDownload(BizDownload download, List<CustomerTemplate> customers, List<SubUserTemplate> subUsers) {
        long successNum = 0, totalNum = 0;
        if (CollectionUtil.isNotEmpty(customers)) {
            totalNum += customers.size();
            long num = customers.stream().filter(e -> StringUtils.isNotBlank(e.getResult()) && "success".equals(e.getResult())).count();
            successNum += num;
        }
        if (CollectionUtil.isNotEmpty(subUsers)) {
            totalNum += subUsers.size();
            long num = subUsers.stream().filter(e -> StringUtils.isNotBlank(e.getResult()) && "success".equals(e.getResult())).count();
            successNum += num;
        }

        Map<String, Long> params = new HashMap<>();
        params.put("successNum", successNum);
        params.put("errorNum", totalNum - successNum);
        download.setParam(JSON.toJSONString(params));
    }

    @Override
    public Long getCountExportBills(DescribeGaapCostRequest request, String id, AuthUser authUserInfo) {
        long count = 0L;
        if (StringUtils.isEmpty(request.getGroupByPeriod())) {
            Query criteria = getQuery(request, id);
            count = mongoTemplate.count(criteria, InstanceGaapCost.class);
        } else {
            Criteria criteria = getCriteria(request, id);
            count = getAggregationGroupCount(criteria,request);
        }
        return count;
    }

    @Override
    public IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> listBillDetails(
            GaapCostDetailRequest request) {
        IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> response = PageUtil.emptyPage();
        if (CollUtil.isEmpty(request.getIds())) {
            log.info("InstanceGaapCostServiceImpl listBillDetails ids isEmpty");
            return response;
        }
        String groupByPeriod = request.getGroupByPeriod();

        List<String> cycleIds = request.getIds();
        Criteria criteria = Criteria.where("billBillingCycleId").in(cycleIds);

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        if(authUserInfo != null && ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUserInfo.getRemark())){
            criteria.and("entityId").is(RequestContextUtil.getEntityId());
        }
        long count = 0L;
        if (StringUtils.isEmpty(groupByPeriod)) {
            Query queryCount = new Query(criteria);
            count = mongoTemplate.count(queryCount,cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class);
        }else{
            assembleCriteriaInGroupByPeriod(criteria,groupByPeriod);
            DescribeGaapCostRequest gaapCostRequest = new DescribeGaapCostRequest();
            count = getAggregationGroupCount(criteria, gaapCostRequest);
        }

        if (count <= 0) {
            log.info("InstanceGaapCostServiceImpl listBillDetails count lt 0");
            return response;
        }


        //因为返回数据是原始数结果
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> costs = new ArrayList<>();
        if (StringUtils.isEmpty(groupByPeriod)) {
            Query queryDetail = new Query(criteria);
            if(authUserInfo != null && ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUserInfo.getRemark())){
                queryDetail.addCriteria(Criteria.where("entityId").is(RequestContextUtil.getEntityId()));
            }
        if (Objects.nonNull(request.getPagenum()) && Objects.nonNull(request.getPagesize())) {
            int pageNum = request.getPagenum().intValue();
            int pageSize = request.getPagesize().intValue();
            Pageable pageable = PageRequest.of(pageNum-1, pageSize);
            queryDetail.with(pageable);
        }

            queryDetail.with(Sort.by(Sort.Direction.DESC, "payTime"));

            costs = mongoTemplate.find(
                    queryDetail, cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class);
        }else{
            DescribeGaapCostRequest gaapCostRequest = new DescribeGaapCostRequest();
            gaapCostRequest.setGroupByPeriod(groupByPeriod);
            if(request.getPagenum() !=null && request.getPagesize() !=null){
                gaapCostRequest.setPagenum(request.getPagenum()-1);
                gaapCostRequest.setPagesize(request.getPagesize());
            }
            costs = BeanConvertUtil.convert(this.costGroupBy(criteria, gaapCostRequest),cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class) ;
        }

        long size = count;
        if (UserTypeConstants.CONSOLE.equals(authUserInfo.getRemark()) ) {
            List<Long> orgs = orgService.selectChildrenOrgIds(authUserInfo.getOrgSid());
            orgs.add(authUserInfo.getOrgSid());
            if (costs.stream().anyMatch(e -> !orgs.contains(e.getOrgSid()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }
        response.setTotal(size);
        response.setRecords(costs);
        if (StringUtils.isEmpty(groupByPeriod)) {
            // 折扣优惠不包含抹零金额
            response.getRecords().forEach(costResponse -> {
                if (PayTypeEnum.SUBSCRIPTION.getCode().equalsIgnoreCase(costResponse.getBillType())) {
                    BigDecimal pretaxGrossAmount = Objects.isNull(costResponse.getPretaxGrossAmount()) ? BigDecimal.ZERO
                        : costResponse.getPretaxGrossAmount();
                BigDecimal cashAmount = Objects.isNull(costResponse.getCashAmount()) ? BigDecimal.ZERO
                        : costResponse.getCashAmount();
                BigDecimal couponDiscount = Objects.isNull(costResponse.getCouponDiscount()) ? BigDecimal.ZERO
                        : costResponse.getCouponDiscount();
                BigDecimal couponAmount = Objects.isNull(costResponse.getCouponAmount()) ? BigDecimal.ZERO
                        : costResponse.getCouponAmount();
                BigDecimal creditAmount = Objects.isNull(costResponse.getCreditAmount()) ? BigDecimal.ZERO
                        : costResponse.getCreditAmount();
                BigDecimal deductCouponDiscount =
                        Objects.isNull(costResponse.getDeductCouponDiscount()) ? BigDecimal.ZERO
                                : costResponse.getDeductCouponDiscount();
                BigDecimal bagDiscountAmount = Objects.isNull(costResponse.getBagDiscountAmount()) ? BigDecimal.ZERO
                        : costResponse.getBagDiscountAmount();
                BigDecimal eraseZeroAmount = Objects.isNull(costResponse.getEraseZeroAmount()) ? BigDecimal.ZERO
                        : costResponse.getEraseZeroAmount();
                    BigDecimal pricingDiscount = pretaxGrossAmount.subtract(cashAmount)
                        .subtract(couponDiscount)
                        .subtract(couponAmount)
                        .subtract(creditAmount)
                        .subtract(deductCouponDiscount)
                        .subtract(bagDiscountAmount)
                        .subtract(eraseZeroAmount);
                    costResponse.setPricingDiscount(pricingDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
                } else {
                    costResponse.setPricingDiscount(costResponse.getPricingDiscount());
                }
                costResponse.setPretaxGrossAmount(preventNPE(costResponse.getPretaxGrossAmount()));
                costResponse.setEraseZeroAmount(preventNPE(costResponse.getEraseZeroAmount()));
                costResponse.setPretaxAmount(preventNPE(costResponse.getPretaxAmount()));
                costResponse.setCashAmount(preventNPE(costResponse.getCashAmount()).setScale(2, BigDecimal.ROUND_DOWN));
                costResponse.setCouponAmount(preventNPE(costResponse.getCouponAmount()));
            });
        } else {
            // 折扣优惠不包含抹零金额
            response.getRecords().forEach(costResponse -> {

                BigDecimal eraseZeroAmount = BigDecimal.ZERO;


                BigDecimal pretaxGrossAmount = preventNPE(costResponse.getPretaxGrossAmount());
                BigDecimal cashAmount = Objects.isNull(costResponse.getCashAmount()) ? BigDecimal.ZERO
                    : preventNPEScaleTwo(costResponse.getCashAmount());
                eraseZeroAmount = eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getCashAmount(), cashAmount));

                BigDecimal couponDiscount = Objects.isNull(costResponse.getCouponDiscount()) ? BigDecimal.ZERO
                    : preventNPEScaleTwo(costResponse.getCouponDiscount());
                eraseZeroAmount = eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getCouponDiscount(), couponDiscount));

                BigDecimal couponAmount = Objects.isNull(costResponse.getCouponAmount()) ? BigDecimal.ZERO
                    : preventNPEScaleTwo(costResponse.getCouponAmount());
                eraseZeroAmount = eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getCouponAmount(), couponAmount));

                BigDecimal creditAmount = Objects.isNull(costResponse.getCreditAmount()) ? BigDecimal.ZERO
                    : preventNPEScaleTwo(costResponse.getCreditAmount());
                eraseZeroAmount = eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getCreditAmount(), creditAmount));

                BigDecimal deductCouponDiscount =
                    Objects.isNull(costResponse.getDeductCouponDiscount()) ? BigDecimal.ZERO
                        : preventNPEScaleTwo(costResponse.getDeductCouponDiscount());
                eraseZeroAmount = eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getDeductCouponDiscount(), deductCouponDiscount));

                BigDecimal bagDiscountAmount = Objects.isNull(costResponse.getBagDiscountAmount()) ? BigDecimal.ZERO
                    : preventNPEScaleTwo(costResponse.getBagDiscountAmount());
                eraseZeroAmount = preventNPE(eraseZeroAmount.add(calcEraseZeroAmount(costResponse.getBagDiscountAmount(), bagDiscountAmount)));

                BigDecimal pricingDiscount = pretaxGrossAmount.subtract(cashAmount)
                    .subtract(couponDiscount)
                    .subtract(couponAmount)
                    .subtract(creditAmount)
                    .subtract(deductCouponDiscount)
                    .subtract(bagDiscountAmount)
                    .subtract(eraseZeroAmount);


                costResponse.setPricingDiscount(pricingDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));

                costResponse.setPretaxGrossAmount(preventNPE(costResponse.getPretaxGrossAmount()));
                costResponse.setEraseZeroAmount(preventNPE(costResponse.getEraseZeroAmount()));
                costResponse.setPretaxAmount(cashAmount.add(creditAmount).add(couponAmount));
                costResponse.setCashAmount(cashAmount);
                costResponse.setCouponDiscount(couponDiscount);
                costResponse.setCouponAmount(couponAmount);
                costResponse.setCreditAmount(creditAmount);
                costResponse.setDeductCouponDiscount(deductCouponDiscount);
                costResponse.setBagDiscountAmount(bagDiscountAmount);
                costResponse.setEraseZeroAmount(eraseZeroAmount);

            });
        }

        return response;
    }

    private static BigDecimal calcEraseZeroAmount(BigDecimal costResponseAmount, BigDecimal newAmount) {
        if (!NumberUtil.equals(newAmount, BigDecimal.ZERO)) {
            if (costResponseAmount == null) {
                costResponseAmount = BigDecimal.ZERO;
            }
            return costResponseAmount.subtract(newAmount);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal preventNPE(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        } else {
            return bigDecimal.setScale(5, BigDecimal.ROUND_HALF_UP);
        }
    }


    private BigDecimal preventNPEScaleTwo(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        } else {
            return bigDecimal.setScale(2, BigDecimal.ROUND_DOWN);
        }
    }

    private List<InstanceGaapCost> getInstanceGaapCostByDocument(Query query, String pageFlag,Boolean excelFlg) {
        List<InstanceGaapCost> costs = new ArrayList<>();
        Document doc = null;
        InstanceGaapCost instanceGaapCost = null;

        // 查询所有的org表
        List<Org> list = orgService.selectAllOrgSimple();
        Map<Long, Org> orgMap = list.stream().collect(Collectors.toMap(Org::getOrgSid, org -> org, (k1, k2) -> k1));
        Map<Long, Org> collect = new HashMap<>();
        orgMap.forEach((key,value) -> {
            collect.put(key, getParentOrg(value,orgMap));
        });

        // 查询所有用户表
        List<UserDto> userDtos = userMapper.selectAllUserAccount();
        Map<Long, String> userMag = userDtos.stream().collect(Collectors.toMap(UserDto::getUserSid, UserDto::getAccount));

        MongoCursor<Document> cursor = null;
        try {
            if (Objects.nonNull(pageFlag) && Y.equals(pageFlag)) {
                // 创建索引
                List<IndexModel> ims = new ArrayList<>();
                BasicDBObject index1 = new BasicDBObject();
                index1.put("payTime", -1);
                index1.put("usageStartDate", -1);
                ims.add(new IndexModel(index1));
                try {
                    mongoTemplate.getCollection("biz_bill_usage_item").createIndexes(ims);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("创建索引异常：[{}]", e.getMessage());
                }
                cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                    find(query.getQueryObject())
                    .noCursorTimeout(true)
                    .batchSize(1000)
                    .sort(query.getSortObject())
                    .skip((int) query.getSkip())
                    .limit(query.getLimit())
                    .cursor();
            } else {
                if (excelFlg) {
                    cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                                                  find(query.getQueryObject())
                                          .noCursorTimeout(true)
                                          .limit(50000)
                                          .sort(query.getSortObject()).cursor();
                }
                else {
                    if (excelFlg){
                        cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                                                      find(query.getQueryObject())
                                              .noCursorTimeout(true)
                                              .limit(50000)
                                              .sort(query.getSortObject())
                                              .cursor();
            } else {
                        cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                                                      find(query.getQueryObject())
                                              .noCursorTimeout(true)
                                              .batchSize(10000)
                                              .sort(query.getSortObject())
                                              .cursor();
                    }}

            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        if (cursor == null) {
            log.info("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument  cursor is null");
            return costs;
        }

        // 防止赋值异常没有数据
        while (cursor.hasNext()) {
            try {
                doc = cursor.next();
                instanceGaapCost = BeanUtil.toBean(doc, InstanceGaapCost.class);
                Object id = doc.get("_id");
                if (id != null) {
                    instanceGaapCost.setId(String.valueOf(id));
                }

                if (StringUtil.isBlank(instanceGaapCost.getUserAccountName())) {
                    Long userSId = Long.parseLong(instanceGaapCost.getOwnerId());
                    String userAccount = userMag.get(userSId);
                    if (StringUtils.isNotEmpty(userAccount)) {
                        instanceGaapCost.setUserAccountName(userAccount);
                    }
                }
                Object orgSidObj = doc.get("orgSid");
                if (orgSidObj != null) {
                    Long orgSid = Long.valueOf(orgSidObj.toString());
                    Org org = collect.get(orgSid);
                    if (org != null) {
                        String orgName = org.getOrgName();
                        instanceGaapCost.setOrgName(orgName);
                    }

            }
        } catch (Exception e) {
                log.error("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument.赋值账单信息异常 eroor: {}", e.getMessage());
            }
            if (instanceGaapCost != null) {
                costs.add(instanceGaapCost);
            } else {
                log.info("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument instanceGaapCost is null ");
        }
        }
        cursor.close();

        log.info("costs==============================:  " + costs.size());
        return costs;
    }

    private List<InstanceGaapCost> costGroupBy(Criteria criteria, DescribeGaapCostRequest costRequest) {

        List<InstanceGaapCost> costs = new ArrayList<>();
        //拼接聚合条件
        Aggregation aggregation = getAggregation(criteria, costRequest);

        // 查询所有的org表
        List<Org> list = orgService.list();
        Map<Long, Org> allOrgMap = list.stream().collect(Collectors.toMap(Org::getOrgSid, org -> org, (k1, k2) -> k1));
        Map<Long, Org> orgMap = new HashMap<>();
        allOrgMap.forEach((key, value) -> {
            orgMap.put(key, getParentOrg(value, allOrgMap));
        });

        // 查询所有用户表
        List<UserDto> userDtos = userMapper.selectAllUserAccount();
        Map<Long, String> userMag = userDtos.stream().collect(Collectors.toMap(UserDto::getUserSid, UserDto::getAccount));

        AggregationResults<InstanceGaapCost> billUsageItemList =
                mongoTemplate.aggregate(aggregation, "biz_bill_usage_item", InstanceGaapCost.class);
        List<InstanceGaapCost> mappedResults = billUsageItemList.getMappedResults();

        if (CollectionUtil.isEmpty(mappedResults)) {
            return costs;
        }
        costs = mappedResults;

        // 防止赋值异常没有数据
        for (InstanceGaapCost gaapCost : mappedResults) {
            //赋值
            assembleData(orgMap, userMag, gaapCost);
            gaapCost.setBillingCycle(costRequest.getGroupByPeriod());
        }
        log.info("分组统计账单-instanceGaapCostServiceImpl.costGroupBy-账单总条数[{}]", costs.size());
        return costs;
    }

    @NotNull
    private Aggregation getAggregation(Criteria criteria, DescribeGaapCostRequest costRequest) {
        int oneHour = 1000*60*60;
        String[] fileds = new String[]{"_id","orderId","orderSn","productCode","userAccountId","instanceName","resourceId","userAccountName","productName","pretaxAmount","pretaxGrossAmount","eraseZeroAmount","orgSid", "cashAmount", "creditAmount", "couponAmount", "entityId", "ownerId", "billingCycle",
                "billType","currency","billSource", "priceType", "payTime", "entityName","usageStartDate","usageEndDate","pricingDiscount","bagDiscountAmount","deductCouponDiscount","billNo","couponDiscount"};
        String groupByPeriod = costRequest.getGroupByPeriod();
        Date firstDay = getFirstDay(groupByPeriod);
        Date firstDayOfNextMonth = DateUtils.addMonths(firstDay,1);
        //判断开始时间、结束时间
        String newUsageStartDateExpression = "{$cond:{if:{$gt:{'$usageStartDate', [0]}},then:'$usageStartDate',else: [1]}}";
        String newUsageEndDateExpression = "{$cond:{if:{$gt:{'$usageEndDate',[0]}},then:[1],else: '$usageEndDate'}}";
        //账单统计开始时间和结束时间之间是否小于1小时
        String allHoursLtOneHourExpression = "{$lt: {{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},1}}";
        //如果账单统计开始时间和结束时间之间小于1小时，四舍五入保留两位小数
        String allHoursExpression = "{$cond:{ 'if':{'$eq':{'$allHoursLtOneHour','true'}},'then':{$round: {{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},2}},'else':{$round: {{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},0}}}}";
        //如果账单统计开始时间和结束时间之间小于1小时，四舍五入保留两位小数是否小于0
        String allHoursEqZeroExpression = "{$eq:{'$allhours',0}}";

        //支付时间转换
        String payTimeExpression = "{ '$dateToString' : {'format' : '%Y%m%d%H%M%S', date: '$payTime'}}";

        //内存不足使用本地存储
        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.project(fileds)
                           .andExpression(allHoursLtOneHourExpression, oneHour).as("allHoursLtOneHour")
//                           .andExpression(allHoursExpression, oneHour).as("allhours")
                           .andExpression(payTimeExpression).as("payTimeStr")
                           .andExpression(newUsageStartDateExpression,firstDay,firstDay).as("newUsageStartDate")
                           .andExpression(newUsageEndDateExpression,firstDayOfNextMonth,firstDayOfNextMonth).as("newUsageEndDate")).withOptions(aggregationOptions);



        //计算统计小时和计算统计天数
        AggregationPipeline pipeline = aggregation.getPipeline();

        AddFieldsOperationBuilder allhoursFieldsOperationBuilder = Aggregation.addFields()
                                                                              .addField("allhours").withValueOfExpression(allHoursExpression,oneHour);

        pipeline.add(allhoursFieldsOperationBuilder.build());


        String[] productArr = new String[] {ProductCodeEnum.HPC_DRP.getProductType()
                , ProductCodeEnum.MODELARTS.getProductType()
                , ProductCodeEnum.HPC.getProductType()
                , ProductCodeEnum.BMS.getProductType()
                , ProductCodeEnum.ECS.getProductType()
                , ProductCodeEnum.SFS.getProductType()
                , ProductCodeEnum.SFS2.getProductType()
                , ProductCodeEnum.OBS.getProductType()
                , ProductCodeEnum.EVS.getProductType()

        };
        AddFieldsOperationBuilder sortFieldsOperationBuilder = Aggregation.addFields()
                                                                          .addField("sortNo").withValueOfExpression("{'$indexOfArray':{{[0],[1],[2],[3],[4],[5],[6],[7],[8]}, '$productCode' }}",productArr);
        pipeline.add(sortFieldsOperationBuilder.build());
        pipeline.add(Aggregation.sort(Direction.DESC, "payTimeStr"));
        pipeline.add(Aggregation.sort(Direction.ASC, "sortNo"));
        //当前账期的小时数，当账单总的时间间隔小于1小时时，当前拆分的时间四舍五入保留两位小数。
        String hourExpression = "{$cond:{'if':{'$eq':{'$allHoursLtOneHour','true'}},'then':{$round:{{$divide: {{$subtract: {'$newUsageEndDate', '$newUsageStartDate'}},[0]}},2}},'else':{$round:{{$divide: {{$subtract: {'$newUsageEndDate', '$newUsageStartDate'}},[0]}},0}}}}";
        AddFieldsOperationBuilder firstFieldsOperationBuilder = Aggregation.addFields()
                                                                           .addField("hour").withValueOfExpression(hourExpression, oneHour)
                                                                           .addField("days").withValueOfExpression(
                        "{$round:{{$divide: {{$subtract: {'$newUsageEndDate', '$newUsageStartDate'}},[0]}},0}}", oneHour * 24);
        pipeline.add(firstFieldsOperationBuilder.build());
        //拆分金额,后付费的，账单统计开始时间和结束时间间隔小于0的不拆分
        String withValueOfExpression = "{$cond:{'if':{'$or':{{$eq:{'$billType','PayAsYouGoBill'}},{$eq:{'$allhours',0}}}},'then':%s,'else': {$multiply: {{$divide: {{$convert: {input: %s,to: 'decimal',onNull: 0}},'$allhours'}},'$hour'}}}}";


        AddFieldsOperationBuilder secFieldsOperationBuilder = Aggregation.addFields()
                                                                         .addField("pretaxGrossAmount").withValueOfExpression(String.format(withValueOfExpression,"'$pretaxGrossAmount'","'$pretaxGrossAmount'"))
                                                                         .addField("pretaxAmount").withValueOfExpression(String.format(withValueOfExpression,"'$pretaxAmount'","'$pretaxAmount'"))
                                                                         .addField("cashAmount").withValueOfExpression(String.format(withValueOfExpression,"'$cashAmount'","'$cashAmount'"))
                                                                         .addField("creditAmount").withValueOfExpression(String.format(withValueOfExpression,"'$creditAmount'","'$creditAmount'"))
                                                                         .addField("couponAmount").withValueOfExpression(String.format(withValueOfExpression,"'$couponAmount'","'$couponAmount'"))
                                                                         .addField("eraseZeroAmount").withValueOfExpression(String.format(withValueOfExpression,"'$eraseZeroAmount'","'$eraseZeroAmount'"))
                                                                         .addField("pricingDiscount").withValueOfExpression(String.format(withValueOfExpression,"'$pricingDiscount'","'$pricingDiscount'"))
                                                                         .addField("deductCouponDiscount").withValueOfExpression(String.format(withValueOfExpression,"'$deductCouponDiscount'","'$deductCouponDiscount'"))
                                                                         .addField("couponDiscount").withValueOfExpression(String.format(withValueOfExpression,"'$couponDiscount'","'$couponDiscount'"))
                                                                         .addField("bagDiscountAmount").withValueOfExpression(String.format(withValueOfExpression,"'$bagDiscountAmount'","'$bagDiscountAmount'"));

        pipeline.add(secFieldsOperationBuilder.build());



        // 1000*60*60 1小时毫秒数,最终时间是否小于1小时
        String statisticHoursLtOneHourExpression = "{$lt: {{$subtract: {'$usageEndDate', '$usageStartDate'}},3600000}}";
        String statisticHoursExpression = "{$cond:{'if':{'$eq':{'$statisticHoursLtOneHour','true'}},'then':{$round:{{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},2}},'else':{$round:{{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},0}}}}";
        String statisticDaysExpression = "{$round:{{$divide: {{$subtract: {'$usageEndDate', '$usageStartDate'}},[0]}},0}}";

        //按维度做分组统计
        String groupByDimension = costRequest.getGroupByDimension();
        if (DimesionEnum.ORDER.getCode().equalsIgnoreCase(groupByDimension)) {
            pipeline.add(Aggregation.group("orderId")
                                    .push("productCode").as("productCodeList")
                                    .push("productName").as("productNameList")
                                    .first("orgSid").as("orgSid")
                                    .first("ownerId").as("ownerId")
                                    .first("orderId").as("orderId")
                                    .first("orderSn").as("orderSn")
                                    .first("billType").as("billType")
                                    .first("instanceName").as("instanceName")  //bug58648修改
                                    .first("resourceId").as("resourceId")
                                    .first("priceType").as("priceType")
                                    .first("entityId").as("entityId")
                                    .first("entityName").as("entityName")
                                    .first("userAccountId").as("userAccountId")
                                    .first("userAccountName").as("userAccountName")
                                    .first("billSource").as("billSource")
                                    .first("currency").as("currency")
                                    .first("billNo").as("billNo")
                                    .sum("hour").as("statisticHours")
                                    .sum("days").as("statisticDays")
                                    .sum(ConvertOperators.valueOf("pretaxGrossAmount").convertToDecimal()).as("pretaxGrossAmount")
                                    .sum(ConvertOperators.valueOf("pretaxAmount").convertToDecimal()).as("pretaxAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(ConvertOperators.valueOf("creditAmount").convertToDecimal()).as("creditAmount")
                                    .sum(ConvertOperators.valueOf("couponAmount").convertToDecimal()).as("couponAmount")
                                    .sum(ConvertOperators.valueOf("eraseZeroAmount").convertToDecimal()).as("eraseZeroAmount")
                                    .sum(ConvertOperators.valueOf("pricingDiscount").convertToDecimal()).as("pricingDiscount")
                                    .sum(ConvertOperators.valueOf("deductCouponDiscount").convertToDecimal()).as("deductCouponDiscount")
                                    .sum(ConvertOperators.valueOf("couponDiscount").convertToDecimal()).as("couponDiscount")
                                    .sum(ConvertOperators.valueOf("bagDiscountAmount").convertToDecimal()).as("bagDiscountAmount")
                                    .min("newUsageStartDate").as("usageStartDate")
                                    .max("newUsageEndDate").as("usageEndDate")
                                    .min("payTime").as("payTime"));
            //循环去重排序
            pipeline.add(Aggregation.addFields().addField("productCode")
                                    .withValueOfExpression("{$reduce: {input: '$productCodeList',initialValue: '',in: {'$cond':{'if':{$gt:{{'$indexOfCP':{'$$value','$$this'}},-1}},'then':'$$value','else':{'$concat': {'$$value', ' ', '$$this'}}}}}}")
                                    .addField("statisticHoursLtOneHour")
                                    .withValueOfExpression(statisticHoursLtOneHourExpression)
                                    .addField("statisticHours")
                                    .withValueOfExpression(statisticHoursExpression,oneHour)
                                    .addField("statisticDays")
                                    .withValueOfExpression(statisticDaysExpression,oneHour*24)
                                    .addField("productName")
                                    .withValueOfExpression("{$reduce: {input: '$productNameList',initialValue: '',in: {'$cond':{'if':{$gt:{{'$indexOfCP':{'$$value','$$this'}},-1}},'then':'$$value','else':{'$concat': {'$$value', ' ', '$$this'}}}}}}")
                                    .build());

        } else if (DimesionEnum.PRODUCT_CODE.getCode().equalsIgnoreCase(groupByDimension)) {
            pipeline.add(Aggregation.group("productCode", "orgSid")
                                    .first("productCode").as("productCode")
                                    .first("productName").as("productName")
                                    .first("orgSid").as("orgSid")
                                    .first("ownerId").as("ownerId")
                                    .first("userAccountId").as("userAccountId")
                                    .first("userAccountName").as("userAccountName")
                                    .first("billType").as("billType")
                                    .first("priceType").as("priceType")
                                    .first("entityId").as("entityId")
                                    .first("entityName").as("entityName")
                                    .first("billSource").as("billSource")
                                    .first("currency").as("currency")
                                    .first("billNo").as("billNo")
                                    .sum("hour").as("statisticHours")
                                    .sum("days").as("statisticDays")
                                    .sum(ConvertOperators.valueOf("pretaxGrossAmount").convertToDecimal()).as("pretaxGrossAmount")
                                    .sum(ConvertOperators.valueOf("pretaxAmount").convertToDecimal()).as("pretaxAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(ConvertOperators.valueOf("creditAmount").convertToDecimal()).as("creditAmount")
                                    .sum(ConvertOperators.valueOf("couponAmount").convertToDecimal()).as("couponAmount")
                                    .sum(ConvertOperators.valueOf("eraseZeroAmount").convertToDecimal()).as("eraseZeroAmount")
                                    .sum(ConvertOperators.valueOf("pricingDiscount").convertToDecimal()).as("pricingDiscount")
                                    .sum(ConvertOperators.valueOf("deductCouponDiscount").convertToDecimal()).as("deductCouponDiscount")
                                    .sum(ConvertOperators.valueOf("couponDiscount").convertToDecimal()).as("couponDiscount")
                                    .sum(ConvertOperators.valueOf("bagDiscountAmount").convertToDecimal()).as("bagDiscountAmount")
                                    .min("newUsageStartDate").as("usageStartDate")
                                    .max("newUsageEndDate").as("usageEndDate")
                                    .min("payTime").as("payTime"));
        } else if ((DimesionEnum.RESNAME.getCode().equalsIgnoreCase(groupByDimension))) {
            pipeline.add(Aggregation.group("instanceName","resourceId")
                                    .first("productCode").as("productCode")
                                    .first("instanceName").as("instanceName")
                                    .first("resourceId").as("resourceId")
                                    .first("productName").as("productName")
                                    .first("orderId").as("orderId")
                                    .first("orderSn").as("orderSn")
                                    .first("userAccountId").as("userAccountId")
                                    .first("userAccountName").as("userAccountName")
                                    .first("billType").as("billType")
                                    .first("priceType").as("priceType")
                                    .first("entityId").as("entityId")
                                    .first("entityName").as("entityName")
                                    .first("orgSid").as("orgSid")
                                    .first("ownerId").as("ownerId")
                                    .first("billSource").as("billSource")
                                    .first("currency").as("currency")
                                    .first("billNo").as("billNo")
                                    .sum("hour").as("statisticHours")
                                    .sum("days").as("statisticDays")
                                    .sum(ConvertOperators.valueOf("pretaxGrossAmount").convertToDecimal()).as("pretaxGrossAmount")
                                    .sum(ConvertOperators.valueOf("pretaxAmount").convertToDecimal()).as("pretaxAmount")
                                    .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount")
                                    .sum(ConvertOperators.valueOf("creditAmount").convertToDecimal()).as("creditAmount")
                                    .sum(ConvertOperators.valueOf("couponAmount").convertToDecimal()).as("couponAmount")
                                    .sum(ConvertOperators.valueOf("eraseZeroAmount").convertToDecimal()).as("eraseZeroAmount")
                                    .sum(ConvertOperators.valueOf("pricingDiscount").convertToDecimal()).as("pricingDiscount")
                                    .sum(ConvertOperators.valueOf("deductCouponDiscount").convertToDecimal()).as("deductCouponDiscount")
                                    .sum(ConvertOperators.valueOf("couponDiscount").convertToDecimal()).as("couponDiscount")
                                    .sum(ConvertOperators.valueOf("bagDiscountAmount").convertToDecimal()).as("bagDiscountAmount")
                                    .min("newUsageStartDate").as("usageStartDate")
                                    .max("newUsageEndDate").as("usageEndDate")
                                    .min("payTime").as("payTime"));
        }else {
            pipeline.add(Aggregation.addFields().addField("usageStartDate")
                                    .withValueOfExpression("newUsageStartDate")
                                    .addField("usageEndDate")
                                    .withValueOfExpression("newUsageEndDate").build());
        }
        // 排序
        pipeline.add(Aggregation.sort(Direction.DESC, "payTime","usageStartDate"));
        //分页
        if (Objects.nonNull(costRequest.getPagenum()) && Objects.nonNull(costRequest.getPagesize())) {
            pipeline.add(Aggregation.skip((Long.valueOf(costRequest.getPagenum()) - 1) * costRequest.getPagesize()));
            pipeline.add(Aggregation.limit(costRequest.getPagesize()));
        }
        return aggregation;
    }

    private void assembleData(Map<Long, Org> orgMap, Map<Long, String> userMag, InstanceGaapCost gaapCost) {
        try {
            if (StringUtil.isBlank(gaapCost.getUserAccountName())) {
                Long userSId = Long.parseLong(gaapCost.getOwnerId());
                String userAccunt = userMag.get(userSId);
                if (StringUtils.isNotEmpty(userAccunt)) {
                    gaapCost.setUserAccountName(userAccunt);
                }
            }
            Long orgSid = gaapCost.getOrgSid();
            Org org = orgMap.get(orgSid);
            if (org != null) {
                String orgName = org.getOrgName();
                gaapCost.setOrgName(orgName);
            }
            String subscriptionType = gaapCost.getSubscriptionType();
            if (StringUtils.isNotBlank(subscriptionType)) {
                if (subscriptionType.startsWith("PayAsYouGo")) {
                    gaapCost.setBillType("PayAsYouGoBill");
                } else if (subscriptionType.startsWith("Subscription")) {
                    gaapCost.setBillType("SubscriptionOrder");
                }
    }
        } catch (Exception e) {
            log.error("InstanceGaapCostServiceImpl.getInstanceGaapCostByDocument.赋值账单信息异常 eroor:", e);
        }
    }


    private Org getOrg(Org org, List<Org> list) {
        if ("company".equals(org.getOrgType()) || Objects.isNull(org.getParentId())) {
            return org;
        }
        Optional<Org> optional = list.stream().filter(
            x -> Objects.equals(x.getOrgSid(), org.getParentId())
        ).findFirst();
        boolean isPresent = optional.isPresent();
        if (!isPresent) {
            return org;
        }
        Org l = optional.get();
        return getOrg(l, list);
    }

    private Org getParentOrg(Org org, Map<Long,Org> orgMap) {
        if ("company".equals(org.getOrgType()) || Objects.isNull(org.getParentId())) {
            return org;
        }
        Org parentOrg = orgMap.get(org.getParentId());
        if (parentOrg == null) {
            return org;
        }
        return getParentOrg(parentOrg,orgMap);
    }





    @Override
    public Set<Long> findBillingAccountByCurrentUserRole(AuthUser authUser){
        Set<Long> accountIds = Sets.newHashSet();
        List<Role> roleList = Lists.newArrayList();
        if (Objects.isNull(authUser)) {
            authUser = RequestContextUtil.getAuthUserInfo();
        }

        String dataScope = "";
        // 如果是超级管理员，数据权限范围为全部
        if (authUser.getAdminFlag()) {
            dataScope = RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope();
        }

        if(null == authUser.getOrgSid()){
            roleList = userMapper.findRolesByCurrentUser(authUser.getUserSid());
        }else{
            roleList = userMapper.findRolesByCurrentUserAndOrgSid(authUser.getUserSid(),authUser.getOrgSid());
        }

        // 获取到最大的数据权限范围
        int dataScopeInteger = 8;
        for (Role r : roleList) {
            int ds = Integer.valueOf(r.getDataScope());
            if (ds == 9) {
                dataScopeInteger = ds;
                break;
            } else if (ds < dataScopeInteger) {
                dataScopeInteger = ds;
            }
        }
        //如果是最大权限查询当权运营实体下的账户ID
        dataScope = String.valueOf(dataScopeInteger);
        List<BizBillingAccount> accounts = Lists.newArrayList();
        if(RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope().equalsIgnoreCase(dataScope)){
            //根据当前运营实体账户
            accounts = bizBillingAccountMapper.getAccountIdByEntityId(authUser.getEntityId());
        }else if(RequirePermissionEnum.DATA_SCOPE_ENTITY_AND_CHILD.getScope().equalsIgnoreCase(dataScope)){
            //查询当前组织及当前组织下的是所有客户账户
            accountIds = bizBillingAccountMapper.getBillingAccountByEntityIdAndOrgSid(authUser.getEntityId(),authUser.getOrgSid());
        }else if(RequirePermissionEnum.DATA_SCOPE_OWNER_AND_CUSTOM.getScope().equalsIgnoreCase(dataScope)){
            //查询本人及本人关联客户账户
            accounts = bizBillingAccountMapper.findSelfAndSelfCustomer(authUser.getUserSid(),authUser.getEntityId());
        }
        if(CollectionUtil.isNotEmpty(accounts)){
            accountIds = accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toSet());
        }

        return accountIds;
    }

    @PostConstruct
    public void createIndex(){
        List<IndexInfo> billUsageItemList = mongoTemplate.indexOps("biz_bill_usage_item").getIndexInfo();
        List<String> collect = billUsageItemList.stream().map(IndexInfo::getName).collect(Collectors.toList());
        if(collect.contains("billBillingCycleId -1")){
            log.info("index已经存在了::billBillingCycleId -1");
        }else{
            Index index=new Index();
            index.named("billBillingCycleId -1");
            index.on("billBillingCycleId", Direction.DESC);
            index.on("type", Direction.DESC);
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }
        if (!collect.contains("userAccountId_1")) {
            //添加账户的索引
            Index index = new Index("userAccountId", Direction.ASC).named("userAccountId_1");
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }
        if (!collect.contains("billingCycle_1")) {
            //添加周期索引
            Index index = new Index("billingCycle", Direction.ASC).named("billingCycle_1");
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }
        if (!collect.contains("orderId_1")) {
            //添加订单索引
            Index index = new Index("orderId", Direction.ASC).named("orderId_1");
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }
        if(!collect.contains("orgSid_1")){
            //添加销售组织索引索引
            Index index = new Index("orgSid", Direction.ASC).named("orgSid_1");
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }

        if(!collect.contains("payTime_usageStartDate_1")){
            Index index=new Index();
            index.named("payTime_usageStartDate_1");
            index.on("payTime", Direction.DESC);
            index.on("usageStartDate", Direction.DESC);
            mongoTemplate.indexOps("biz_bill_usage_item").ensureIndex(index);
        }
    }

    @Override
    public IPage<DescribeGaapCostResponse> listCalculateBills(DescribeGaapCostRequest request) {
        Query criteria = getQuery(request,null);
        criteria.with(Sort.by(Sort.Direction.DESC, "payTime", "usageStartDate"));
        IPage<DescribeGaapCostResponse> responsePage = PageUtil.emptyPage();
        long count = mongoTemplate.count(criteria, InstanceGaapCost.class);
        if (count <= 0) {
            return responsePage;
        }
        log.info("获取账单明细：开始时间： " + cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date()));
        Date start = new Date();
        List<InstanceGaapCost> instanceGaapCosts = this.getInstanceGaapCosts2(criteria);
        log.info("获取账单明细：结束时间： " + cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date()));
        Date end = new Date();
        log.info("listBills_耗时 = " + (end.getTime() - start.getTime()));
        List<DescribeGaapCostResponse> data = new ArrayList<>();

        log.info("获取账户信息：开始时间： " + cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date()));
        instanceGaapCosts.forEach(instanceGaapCost -> {
            DescribeGaapCostResponse response = BeanConvertUtil.convert(instanceGaapCost,
                                                                        DescribeGaapCostResponse.class);
            if (ObjectUtils.isEmpty(response)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
            }
            response.setPaymentCurrency(instanceGaapCost.getCurrency());
            BigDecimal deductCouponDiscount = instanceGaapCost.getDeductCouponDiscount() == null ? BigDecimal.ZERO
                    : instanceGaapCost.getDeductCouponDiscount();
            response.setDeductCouponDiscount(deductCouponDiscount);
            response.setProductName(instanceGaapCost.getProductName());
            response.setInstanceName(
                    StringUtils.isBlank(instanceGaapCost.getInstanceName()) ? instanceGaapCost.getProductName()
                            : instanceGaapCost.getInstanceName());
            response.setSubscriptionType(instanceGaapCost.getBillType());
            String instanceId = instanceGaapCost.getResourceId();
            if (StrUtil.isNotBlank(instanceId)) {
                String[] split = instanceId.split(",");
                if (split.length > 10) {
                    split = Arrays.copyOfRange(split, 0, 10);
                    instanceId = Arrays.stream(split).collect(Collectors.joining(",")) + "...";
                }
            }
            response.setInstanceId(instanceId);
            response.setOrderId(instanceGaapCost.getOrderSn());
            String cueValue = instanceGaapCost.getCueValue() == null ? null : instanceGaapCost.getCueValue();
            response.setCueValue(cueValue);
            if (instanceGaapCost.getInvoicable() == null) {
                response.setInvoicable(BooleanEnum.YES.getCode());
            }
            if (instanceGaapCost.getRechargeCreditAmount() == null) {
                response.setRechargeCreditAmount(BigDecimal.ZERO);
            }
            data.add(response);
        });
        log.info("获取账户信息：结束时间： " + cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date()));
        responsePage.setRecords(data);
        responsePage.setTotal(count);
        return responsePage;
    }

    /**
     * 获取账单明细优化版本
     *
     * @param query 查询条件
     */
    private List<InstanceGaapCost> getInstanceGaapCosts2(Query query) {
        query.with(Sort.by(Sort.Direction.DESC, "payTime"));
        Date start = new Date();


        List<InstanceGaapCost> instanceGaapCosts = new ArrayList<>();
        Document doc = null;
        int total = 0;
        try {
            MongoCursor<Document> cursor;
            cursor = mongoTemplate.getCollection("biz_bill_usage_item").
                                  find(query.getQueryObject())
                                  .noCursorTimeout(true)
                                  .batchSize(10000)
                                  .sort(query.getSortObject())
                                  .cursor();
            //user映射map
            HashMap<Long, User> userMap = new HashMap<>(50);
            List<User> userList = sysUserService.list();
            userList.forEach(user -> {
                userMap.put(user.getUserSid(), user);
            });
            //org映射map
            Map<Long, Org> orgMap = new HashMap<>(50);
            List<Org> orgList = orgService.list();
            orgList.forEach(org -> {
                orgMap.put(org.getOrgSid(), org);
            });
            while (cursor.hasNext()) {
                doc = cursor.next();
                InstanceGaapCost instanceGaapCost = BeanUtil.toBean(doc, InstanceGaapCost.class);

                try {
                    instanceGaapCost.setId(doc.getObjectId("_id").toString());
                } catch (ClassCastException e) {
                    log.error("instanceGaapCost账单id异常，错误次数{}，错误信息：{}", ++total, e.getMessage());
                    continue;
                }
                if (StringUtil.isBlank(instanceGaapCost.getUserAccountName())) {
                    try {
                        User user = userMap.get(Long.parseLong(instanceGaapCost.getOwnerId()));
                        instanceGaapCost.setUserAccountName(user.getAccount());
                    } catch (NumberFormatException e) {
                        log.error("userSid转换失败，请检查mogodb中ownerId的值");
                        log.error("exception message:", e);
                    }
                }
                Long orgSid = doc.getLong("orgSid");
                Org org = this.selectRootOrg(Long.parseLong(String.valueOf(orgSid)), orgMap);
                instanceGaapCost.setOrgName(org.getOrgName());

                if (StringUtils.isBlank(instanceGaapCost.getBillType())) {
                    if (StringUtils.isNotBlank(instanceGaapCost.getSubscriptionType())) {
                        if (instanceGaapCost.getSubscriptionType().startsWith("PayAsYouGo")) {
                            instanceGaapCost.setBillType("PayAsYouGoBill");
                        }
                        if (instanceGaapCost.getSubscriptionType().startsWith("Subscription")) {
                            instanceGaapCost.setBillType("SubscriptionOrder");
                        }
                    }
                }
                instanceGaapCosts.add(instanceGaapCost);
            }
            cursor.close();
        } catch (Exception e) {
            log.error("exception message:", e);
        }
        log.info("costs==============================:  " + instanceGaapCosts.size());
        Date end = new Date();
        log.info("instanceGaapCosts==============================:  " + (end.getTime() - start.getTime()));
        return instanceGaapCosts;
    }

    private Org selectRootOrg(Long orgSid, Map<Long, Org> orgMap) {
        if (Objects.isNull(orgSid)) {
            return null;
        }
        Org org = orgMap.get(orgSid);
        if (Objects.isNull(org)) {
            return null;
        }
        if ("company".equals(org.getOrgType()) || Objects.isNull(org.getParentId())) {
            return org;
        } else {
            return selectRootOrg(org.getParentId(), orgMap);
        }
    }
}

