/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.CashCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CancelCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DeductCashCouponRecordRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.*;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.CashCouponService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BL.BL02;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 现金券管理
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
@Api(value = "/cashCoupon", tags = {"现金券管理"})
@RestController
@RequestMapping("/cashCoupon")
public class CashCouponController {

    @Resource
    private CashCouponService cashCouponService;

    /**
     * [INNER API] 创建现金券
     *
     * @param createCashCouponRequest 创建现金优惠券请求体
     * @return {@code RestResult}
     * @since 2.4.1
     */
    @RejectCall
    @ApiOperation(value = "创建现金券", httpMethod = "POST")
    @PostMapping
    public RestResult createCashCoupons(@RequestBody @Valid CreateCashCouponRequest createCashCouponRequest) {
        cashCouponService.createCashCoupon(createCashCouponRequest);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_120701479));
    }

    /**
     * [INNER API] 发放现金券
     *
     * @param distributeCashCouponRequest 分发现金券请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(value = "分发现金券", httpMethod = "PUT")
    @PutMapping("/distribute")
    public RestResult distributeCashCoupon(@RequestBody DistributeCashCouponRequest distributeCashCouponRequest) {
        LambdaQueryWrapper<CashCoupon> qw = new LambdaQueryWrapper<>();
        qw.in(CashCoupon::getCouponNo, distributeCashCouponRequest.getCouponNo());
        qw.eq(CashCoupon::getType, distributeCashCouponRequest.getType());
        if (cashCouponService.count(qw) == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        cashCouponService.distributeCashCoupon(distributeCashCouponRequest);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1526112421));
    }

    /**
     * 校验现金券
     *
     * @param cardNO    现金券号
     * @param accountId 账户id
     * @return {@code RestResult<BigDecimal>}
     */
    @AuthorizeBss(action = AuthModule.CA.CA01 + AuthModule.COMMA + AuthModule.CC.CC0201)
    @ApiOperation(value = "校验现金券", httpMethod = "GET")
    @GetMapping("/check/{cardNO}/{accountId}")
    public RestResult<BigDecimal> check(@PathVariable("cardNO") String cardNO, @PathVariable("accountId") String accountId) {
        return cashCouponService.check(cardNO, accountId);
    }

    /**
     * [INNER API] 作废现金券
     *
     * @param cancelCashCouponRequest 取消现金优惠券请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(value = "作废现金券", httpMethod = "PUT")
    @PutMapping("/cancel")
    public RestResult cancelCashCoupon(@RequestBody @Valid CancelCashCouponRequest cancelCashCouponRequest) {
        LambdaQueryWrapper<CashCoupon> qw = new LambdaQueryWrapper<>();
        qw.in(CashCoupon::getCouponNo, cancelCashCouponRequest.getCouponNo());
        qw.eq(CashCoupon::getType, cancelCashCouponRequest.getType());
        if (cashCouponService.count(qw) == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        cashCouponService.cancelCashCoupon(cancelCashCouponRequest.getCouponNo(), cancelCashCouponRequest.getRemark());
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_23766069) + cancelCashCouponRequest.getCouponNo().size() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_755109655));
    }

    /**
     * 分页查询现金券
     *
     * @param pagenum       页数
     * @param pagesize      页大小
     * @param type          类型
     * @param couponNo      优惠券号
     * @param status        状态
     * @param sortdatafield 排序字段
     * @param sortorder     排序方式
     * @param couponName    优惠券名称
     * @return {@code IPage<CashCouponGroupNameResponse>}
     */
    @AuthorizeBss(action = BL02.BL02)
    @ApiOperation(value = "分页查询现金券", httpMethod = "GET")
    @GetMapping
    public IPage<CashCouponPageResponse> list(@RequestParam(value = "pagenum",required = true) Integer pagenum,
                                              @RequestParam(value = "pagesize",required = true) Integer pagesize,
                                              @RequestParam(value = "type", defaultValue = "deposit") String type,
                                              @RequestParam(value = "accountNameLike",required = false) String accountNameLike,
                                              String couponNo, String status, String sortdatafield,
                                              String sortorder, String couponName) {
        IPage<CashCouponGroupNameResponse> cashCouponGroupNameResponseIPage = cashCouponService.pageCashCoupon(pagenum, pagesize, type, couponNo, status, sortdatafield, sortorder,
                couponName,accountNameLike);
        return BeanConvertUtil.convertPage(cashCouponGroupNameResponseIPage, CashCouponPageResponse.class);

    }

    /**
     * 分页查询现金券详情列表
     *
     * @param pagenum       页数
     * @param pagesize      页大小
     * @param type          类型
     * @param couponName    优惠券名称
     * @param couponNo      优惠券不
     * @param status        状态
     * @param sortdatafield 排序字段
     * @param sortorder     排序方式
     * @return {@code IPage<CashCoupon>}
     */
    @AuthorizeBss(action = BL02.BL02)
    @ApiOperation(value = "分页查询现金券详情列表", httpMethod = "GET")
    @GetMapping("/info")
    public IPage<CashCouponResponse> listInfo(@RequestParam(value = "pagenum", required = true) Integer pagenum,
                                              @RequestParam(value = "pagesize", required = true) Integer pagesize,
                                              @RequestParam(value = "type", required = true) String type,
                                              @RequestParam(value = "accountNameLike",required = false) String accountNameLike,
                                              String couponName, String couponNo, String status, String sortdatafield,
                                              String sortorder) {
        IPage<CashCoupon> cashCouponIPage = cashCouponService.pageCashListInfo(pagenum, pagesize, type, couponNo, couponName, status, sortdatafield,
                sortorder,accountNameLike);
        return BeanConvertUtil.convertPage(cashCouponIPage, CashCouponResponse.class);
    }

    /**
     * 分发现金券时查询账户
     *
     * @param pagenum     页数
     * @param pagesize    页大小
     * @param accountName 帐户名称
     * @param orgName     组织名字
     * @param mobile      移动
     * @param account     账户
     * @return {@code IPage<BizBillingAccount>}
     */
    @AuthorizeBss(action = BL02.BL02)
    @ApiOperation(value = "分发现金券时查询账户", httpMethod = "GET")
    @GetMapping("/billingAccount")
    public IPage<BizBillingAccountResponse> findBillingAccount(
            @RequestParam(value = "pagenum",required = true) Integer pagenum,
            @RequestParam(value = "pagesize",required = true) Integer pagesize, String accountName, String orgName,
            String mobile, String account) {
        IPage<BizBillingAccount> billingAccount = cashCouponService.findBillingAccount(pagenum, pagesize, accountName, orgName, mobile, account);
        IPage<BizBillingAccountResponse> page = BeanConvertUtil.convertPage(billingAccount, BizBillingAccountResponse.class);
        //对数据做脱敏
        DesensitizationUtil.desensitization(page.getRecords());
        return page;
    }

    /**
     * 账户分页查询现金券
     *
     * @param pagenum       页数
     * @param pagesize      页大小
     * @param type          类型
     * @param couponNo      优惠券不
     * @param status        状态
     * @param accountId     账户id
     * @param sortdatafield 排序字段
     * @param sortorder     排序方式
     * @return {@code IPage<CashCouponAccountResponse>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC02)
    @ApiOperation(value = "账户分页查询现金券", httpMethod = "GET")
    @GetMapping("/account")
    @ListenExpireBack
    public IPage<CashCouponAccountResponse> accountList(
            @RequestParam(value = "pagenum", defaultValue = "0") Integer pagenum,
            @RequestParam(value = "pagesize", defaultValue = "10") Integer pagesize,
            @RequestParam(value = "type", defaultValue = "deposit") String type,
            @RequestParam(required = false, value ="status") String couponStatus,
            String couponNo,
            Long accountId, String sortdatafield, String sortorder) {

        return cashCouponService.accountListCashCoupon(pagenum, pagesize, type, couponNo, couponStatus, accountId,
                sortdatafield, sortorder, true);
    }

    /**
     * [INNER API] 账户分页查询现金券
     *
     * @param pagenum       页数
     * @param pagesize      页大小
     * @param type          类型
     * @param couponNo      优惠券不
     * @param status        状态
     * @param accountId     账户id
     * @param sortdatafield 排序字段
     * @param sortorder     排序方式
     * @return {@code IPage<CashCouponAccountResponse>}
     */
    @RejectCall
    @ApiOperation(value = "账户分页查询现金券", httpMethod = "GET")
    @GetMapping("/account/feign")
    public IPage<CashCouponAccountResponse> accountListByFeign(
            @RequestParam(value = "pagenum", defaultValue = "0") Integer pagenum,
            @RequestParam(value = "pagesize", defaultValue = "10") Integer pagesize,
            @RequestParam(value = "type", defaultValue = "deposit") String type, String couponNo, String status,
            Long accountId, String sortdatafield, String sortorder) {
        IPage<CashCouponAccountResponse> cashCouponAccountResponseIPage = cashCouponService.accountListCashCoupon(pagenum, pagesize, type, couponNo, status, accountId,
                                                                                                                  sortdatafield, sortorder, false);
        return BeanConvertUtil.convertPage(cashCouponAccountResponseIPage, CashCouponAccountResponse.class);
    }

    /**
     * 账户统计现金券数量
     *
     * @return {@code CashCouponAccountSumResponse}
     */
    @AuthorizeBss(action = AuthModule.CC.CC02)
    @ApiOperation(value = "账户统计现金券数量", httpMethod = "GET")
    @GetMapping("/account/sum")
    public CashCouponAccountSumResponse accountCashSum() {
        return cashCouponService.accountCashSum();
    }

    /**
     * 账户统计现金券数量
     *
     * @return {@code CashCouponAccountSumResponse}
     */
    @AuthorizeBss(action = AuthModule.CA.CA)
    @ApiOperation(value = "账户统计现金券数量", httpMethod = "GET")
    @GetMapping("/account/sum/overview")
    public CashCouponAccountSumResponse accountCashSumOverview() {
        return cashCouponService.accountCashSum();
    }

    /**
     * 账户统计抵扣现金券数量
     *
     * @return {@code CashCouponAccountSumResponse}
     */
    @AuthorizeBss(action = AuthModule.CC.CC0204)
    @ApiOperation(value = "账户统计抵扣现金券数量", httpMethod = "GET")
    @GetMapping("/account/deductSum")
    public CashCouponAccountSumResponse accountDeductCashSum() {
        return cashCouponService.accountDeductCashSum();
    }


    /**
     * 分页查询抵扣现金券抵扣记录
     *
     * @param request 查询抵扣现金券抵扣记录请求体
     * @return {@code IPage<DeductCashCouponRecordResponse>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC0204)
    @ApiOperation(value = "分页查询抵扣现金券抵扣记录", httpMethod = "GET")
    @GetMapping("/deductRecord")
    public IPage<DeductCashCouponRecordResponse> deductRecordList(@Valid DeductCashCouponRecordRequest request) {
        return cashCouponService.deductRecordList(request);
    }

    /**
     * [INNER API] 分页查询抵扣现金券抵扣记录
     *
     * @param request 查询抵扣现金券抵扣记录请求体
     * @return {@code IPage<DeductCashCouponRecordResponse>}
     */
    @RejectCall
    @ApiOperation(value = "分页查询抵扣现金券抵扣记录", httpMethod = "GET")
    @GetMapping("/deductRecord/feign")
    public IPage<DeductCashCouponRecordResponse> deductRecordListByFeign(@Valid DeductCashCouponRecordRequest request) {
        return cashCouponService.deductRecordList(request);
    }

}
