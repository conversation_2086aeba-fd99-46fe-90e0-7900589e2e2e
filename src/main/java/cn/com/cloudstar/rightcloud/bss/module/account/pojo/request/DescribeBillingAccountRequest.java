/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;


/**
 * 账户列表查询 入参
 *
 * <AUTHOR>
 */
@ApiModel(description = "账户列表查询")
@Data
public class DescribeBillingAccountRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    @ApiModelProperty("账户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @SafeHtml
    private String id;

    private Long userSid;

    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称")
    @SafeHtml
    private String accountName;


    /**
     * 客户账号
     */
    @ApiModelProperty("客户账号")
    private String account;

    /**
     * 组织OU
     */
    @ApiModelProperty("组织OU")
    @SafeHtml
    private String ldapOu;

    /**
     * 账户名称模糊
     */
    @ApiModelProperty("账户名称模糊")
    @SafeHtml
    private String accountNameLike;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @SafeHtml
    private String email;

    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    @SafeHtml
    private String mobile;

    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private Long orgSid;

    /**
     * 分销商ID
     */
    @ApiModelProperty("分销商ID")
    private Long distributorId;

    /**
     * 销售ID
     */
    @ApiModelProperty("销售ID")
    private Long salesmenId;

    /**
     * 分销商
     */
    @ApiModelProperty("分销商")
    @SafeHtml
    private String distributorName;

    /**
     * 销售
     */
    @ApiModelProperty("销售")
    @SafeHtml
    private String salesmen;

    /**
     * 直营
     */
    @ApiModelProperty("直营")
    @SafeHtml
    private String direct;

    /**
     * 信用额度
     */
    @ApiModelProperty("信用额度")
    @SafeHtml
    private String creditLine;

    /**
     * 资源冻结状态
     */
    @ApiModelProperty("资源冻结状态(0:禁用，1：启用)")
    @SafeHtml
    private String freezeStatus;

    /**
     * 所属行业
     */
    @ApiModelProperty("所属行业")
    @SafeHtml
    private String industry;

    /**
     * 应用场景
     */
    @ApiModelProperty("应用场景")
    @SafeHtml
    private String applicationScenario;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @SafeHtml
    private String status;

    /**
     * 业务标识tag
     */
    @ApiModelProperty("业务标识tag，拓展中：expansion，已备案：recorded，试算中：trial，已签单：signed，商用中：commercial，欠费中：arrearage，已注销：cancelled")
    @SafeHtml
    private String businessTag;

    /**
     * 运营实体ID
     */
    @ApiModelProperty("运营实体ID")
    private Long entityId;

    /**
     * 运营实体名称
     */
    @ApiModelProperty("运营实体名称")
    @SafeHtml
    private String entityName;

    /**
     * 平台限额标识
     */
    @ApiModelProperty("平台限额标识")
    @SafeHtml
    private String quotaIndex;

    /**
     * 排除未审核账户
     */
    @ApiModelProperty("排除未审核账户")
    @SafeHtml
    private Boolean excludeUnapproved = false;

    /**
     * 是否含子用户
     */
    @ApiModelProperty("是否含子用户")
    private Boolean subUser = false;

    @ApiModelProperty("自定义信息过滤字段")
    private String customizationInfo;

    /**
     * 关键词搜索
     */
    @ApiModelProperty("关键词搜索")
    private String keyword;

    /**
     * 开通产品信息
     */
    @ApiModelProperty("开通产品信息")
    private List<String> openProductInformation;

    private String refOrgId;
    private String companyName;
    private String orgName;
    private Long neqOrgId;

}
