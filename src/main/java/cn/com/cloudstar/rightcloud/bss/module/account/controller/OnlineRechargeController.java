/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.PayCreateVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.PaySwitchVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.OnlineRechargeRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IOnlineRechargeService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 在线充值
 *
 * <AUTHOR>
 * @since 2021/3/2
 */
@Api(tags = "在线充值")
@RestController
@RequestMapping("/online_recharge")
public class OnlineRechargeController {

    @Autowired
    private IOnlineRechargeService onlineRechargeService;

    /**
     * 充值下单
     *
     * @param request 在线充值请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CA.CA01 + AuthModule.COMMA + CC.CC0201)
    @ApiOperation(httpMethod = "POST", value = "充值下单")
    @PostMapping("/create")
    //@SmsValidation
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'充值下单'", resource = OperationResourceEnum.RECHARGE_CREATE_ORDER,bizId = "#request.cashCouponNo", tagNameUs ="'Recharge and place an order'")
    public RestResult<PayCreateVO> recharge(@RequestBody @Valid OnlineRechargeRequest request) {
        return onlineRechargeService.rechargeCreteOrder(request);
    }

    /**
     * 手动输入券号充值
     *
     * @param cashNo 优惠卷编号
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "手动输入券号充值")
    @PostMapping("/balanceCash/{cashNo}/{accountId}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'充值'", resource = OperationResourceEnum.ONLINERECHARGE,bizId = "#cashNo", tagNameUs ="'Recharge'")
    @Idempotent
    //@SmsValidation
    @AuthorizeBss(action = AuthModule.CC.CC0203)
    public RestResult<PayCreateVO> recharge(@PathVariable("cashNo") String cashNo,@PathVariable("accountId") Long accountId) {
        return onlineRechargeService.balanceCashRecharge(cashNo,accountId);
    }

    @ApiOperation(httpMethod = "POST", value = "第三方支付异步通知")
    @PostMapping("/notify")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'在线支付异步通知'", param = "#notifyData", resource = OperationResourceEnum.ONLINE_PAY_ASYNC_NOTIFY, tagNameUs ="'Online payment asynchronous notification'")
    public void notify(@RequestBody String notifyData,
                       HttpServletResponse httpServletResponse,
                       HttpServletRequest httpServletRequest) {
        onlineRechargeService.payNotify(notifyData,httpServletResponse,httpServletRequest);
    }

    @AuthorizeBss(action = AuthModule.CA.CA01)
    @ApiOperation(httpMethod = "GET", value = "批量查询订单")
    @GetMapping("/batchQuery")
    public void batchQueryPayStatus() {
        // todo 后期调整银联验签，验签通过可通过异步通知的方法进行更新订单
        // 批量查询订单并更新
        onlineRechargeService.batchQueryPayStatus();
    }


    /**
     * 查询当前用户可用现金券
     * @since 2.4.1
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CA.CA01 + AuthModule.COMMA + CC.CC0201)
    @ApiOperation(httpMethod = "GET", value = "查询当前用户可用现金券")
    @GetMapping("/cashCoupons")
    public RestResult queryCashCoupons(Long accountId) {
        if(accountId != null){
            return onlineRechargeService.queryUnusedCashCoupon(accountId);
        }else{
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_197031015));
        }
    }

    /**
     * 查询当前可使用的支付渠道
     *
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CA.CA01 + AuthModule.COMMA + CC.CC0201)
    @ApiOperation(httpMethod = "GET", value = "查询当前可使用的支付渠道")
    @GetMapping("/paySwitch")
    public RestResult<PaySwitchVO> queryPaySwitch() {
        return onlineRechargeService.queryPaySwitch();
    }

}
