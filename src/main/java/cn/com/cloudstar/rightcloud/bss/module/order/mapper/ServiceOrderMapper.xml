<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper">
    <resultMap id="BaseResultMap"
               type="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_sn" property="orderSn" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT" />
        <result column="service_id" property="serviceId" jdbcType="VARCHAR" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT"/>
        <result column="extra_attr" property="extraAttr" jdbcType="LONGVARCHAR" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
        <result column="original_cost" property="originalCost" jdbcType="DECIMAL" />
        <result column="org_discount" property="orgDiscount" jdbcType="DECIMAL" />
        <result column="coupon_discount" property="couponDiscount" jdbcType="DECIMAL" />
        <result column="final_cost" property="finalCost" jdbcType="DECIMAL" />
        <result column="process_code" property="processCode" jdbcType="VARCHAR" />
        <result column="biz_billing_account_id" property="bizBillingAccountId" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="process_flag" property="processFlag" jdbcType="VARCHAR" />
        <result column="step_name" property="stepName" jdbcType="VARCHAR" />
        <result column="curr_amount" property="currAmount" jdbcType="DECIMAL" />
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="service_type" property="serviceType" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="VARCHAR"/>
        <result column="contract_title" property="contractTitle" jdbcType="VARCHAR"/>
        <result column="contract_id" property="contractId" jdbcType="VARCHAR"/>
        <result column="settlement_type" property="settlementType" jdbcType="VARCHAR"/>
        <result column="behalf_user_sid" property="behalfUserSid" jdbcType="VARCHAR"/>
        <result column="contract_file" property="contractFile" jdbcType="VARCHAR"/>
        <result column="contract_file_name" property="contractFileName" jdbcType="VARCHAR"/>
        <result column="charging_type" property="chargingType" jdbcType="VARCHAR"/>
        <result column="cluster_id" property="clusterId" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="ResultMapCloudMarketEnvCatalog" type="cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO" >
        <result column="id" property="catalogId"/>
        <result column="category_name" property="catalogName"/>
        <result column="icon" property="icon"/>
        <result column="sort_number" property="sortNumber"/>
        <collection property="services" column="id" select="selectAvailableServiceByCatalogId" />
    </resultMap>

    <sql id="Base_Column_List">
        a.id, a.order_sn, a.name, a.type, a.org_sid, a.service_id, a.owner_id, a.extra_attr, a.total_amount, a.pay_time,
        a.process_code, a.biz_billing_account_id,
        a.created_by, a.created_dt, a.updated_by, a.updated_dt, a.version, a.process_flag, a.step_name,
        a.product_name, a.settlement_type, a.behalf_user_sid, a.behalf_user_sid,a.charging_type,a.entity_id,a.entity_name,a.cluster_id,
        a.STATUS,
        a.curr_amount,
        a.final_cost,
        a.original_cost,
        a.org_discount,
        a.coupon_discount,
        a.ccsp_mac
    </sql>
    <select id="selectCloudProductInfo"
            resultMap="BaseResultMap"
            parameterType="Long">
        select
        count(a.quantity) quantity,
        a.category_name ,
        a.product_name service_name,
        a.product_type service_type,
        a.service_id service_id
        from
        (
        select
        spr.id, sod.quantity, spcc.category_name, spr.product_name , spr.product_type, ifnull(so.service_id,sod.service_id) as service_id
        from
        sf_product_resource spr
        join service_order_resource_ref sorr on
        spr.id = sorr.resource_id
        and sorr.`type` = spr.product_type
        join service_order_detail sod on
        sod.id = sorr.order_detail_id
        join service_order so on
        so.id = sod.order_id
        join sf_service_catalog_relation sscr on
        sscr.service_id = sod .service_id
        join sf_product_category_catalog spcc on
        spcc.id = sscr.catalog_id
        where
        spr.status not in ('deleted','pending','unsubscribed','expired') -- 没有完全退订的资源都做统计,不统计申请中
        <if test="accountIds != null and accountIds.size() > 0">
            and biz_billing_account_id in
            <foreach collection="accountIds" item="accountId" close=")" open="(" separator=",">
                #{accountId}
            </foreach>
        </if>
        group by
        spr.id )a
        group by
        a.service_id
    </select>

    <select id="selectCloudProductTotal"
            resultMap="BaseResultMap">
        select
            spca.category_name,ssc.service_name,ssc.service_type
        from
            sf_product_category_catalog spca
                join sf_service_catalog_relation sscr on
                spca.id = sscr.catalog_id
                join sf_service_category ssc on
                ssc.id = sscr.service_id
        where
            ssc.publish_status = 'succeed'
          and ssc.status = 'using'
        group by spca.category_name ,ssc.service_name
    </select>

    <select id="selectProductCatalogWithService"  resultMap="ResultMapCloudMarketEnvCatalog" >
        SELECT A.id, A.category_name, A.icon, A.sort_number
        FROM sf_product_category_catalog A
        ORDER BY A.sort_number ASC, A.id ASC
    </select>

    <select id="selectAvailableServiceByCatalogId" parameterType="java.lang.Long" resultType="cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO" >
        SELECT A.id, A.service_name, A.product_name, A.show_type, A.service_form serviceForm,A.service_type serviceType,0 quantity
        FROM sf_service_category A
                 LEFT JOIN sf_service_catalog_relation B ON B.service_id = A.id
        WHERE B.catalog_id = #{catalogId} AND A.publish_status = 'succeed' AND A.status = 'using'
        ORDER BY A.sort_number ASC, A.id ASC
    </select>
    <select id="selectReleaseIngDetail" resultMap="BaseResultMap" parameterType="list">
        select
        t.id , t.order_id , t.charge_type , t.service_price , t.service_type
        from
        (
        select
        a.id , a.order_id , a.charge_type , a.service_price , a.service_type
        from
        service_order_detail a , service_order_resource_ref b , sf_product_resource c
        where
        a.id = b.order_detail_id
        and b.resource_id = c.id
        and b.`type` = c.product_type
        and c.status = 'unsubscribing'
        <if test="orderIds != null">
            and a.order_id in
            <foreach item="item" index="index" collection="orderIds" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        group by
        a.order_id
        union all
        select
        sod2.id , sod2.order_id , sod2.charge_type , sod2.service_price , sod2.service_type
        from
        service_order_detail sod2
        join (
        select
        *
        from
        service_order_price_detail w
        where
        w.ref_instance_id in(
        select
        concat('["', spr.id, '"]')
        from
        sf_product_resource spr
        where
        spr.status = 'unsubscribing'
        and spr.id in(
        select
        SUBSTRING_INDEX(SUBSTRING_INDEX(p.ref_instance_id, '["',-1), '"]', 1)
        from
        service_order_price_detail p
        where
        p.ref_instance_id is not null
        and p.`type` = 'renew'
        and p.order_detail_id in(
        select
        sod.id
        from
        service_order_detail sod
        where 1=1
        <if test="orderIds != null">
            and sod.order_id in
            <foreach item="item" index="index" collection="orderIds" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        )group by
        p.ref_instance_id ) )
         ) a on
        sod2.id = a.order_detail_id) t
    </select>


    <resultMap id="orderAndDetailResultMap" type="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo" extends="BaseResultMap">
        <collection property="orderDetails" ofType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail">
            <result column="detail_id" property="id"/>
            <result column="service_id" property="serviceId"/>
            <result column="service_config" property="serviceConfig"/>
            <result column="charge_type" property="chargeType"/>
            <result column="quantity" property="quantity"/>
            <result column="service_type" property="serviceType"/>
            <result column="start_time" property="startTime"/>
            <result column="end_time" property="endTime"/>
        </collection>
    </resultMap>

    <select id="selectServiceOrderAndDetail"
            resultMap="orderAndDetailResultMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select so.id,
        so.org_sid org_sid,
        so.owner_id owner_id,
        so.type,
        so.biz_billing_account_id,
        so.cluster_id,
        ifnull(so.coupon_discount, 0.00) coupon_discount ,
        so.coupon_discount coupon,
        so.org_discount account_discount ,
        so.final_cost,
        so.original_cost,
        sod.id detail_id,
        so.extra_attr extra_attr,
        sod.service_id service_id,
        sod.service_config service_config,
        sod.quantity quantity,
        sod.service_type service_type,
        sod.id order_detail_id,
        so.curr_amount,
        so.created_by,
        sod.charge_type,
        sod.start_time,
        sod.end_time,
        so.order_sn,
        so.status,
        so.product_name,
        so.contract_id,
        so.name
        from service_order so
        left join service_order_detail sod on so.id = sod.order_id
        <if test="condition.obsInModalArts">
            left join service_order_resource_ref sorr on sorr.order_detail_id = sod.id
        </if>
        <where>
            <if test="condition.status !=null">
                and so.status = #{condition.status}
            </if>
            <if test="condition.orderId !=null">
                and so.id = #{condition.orderId}
            </if>
            <if test="condition.type !=null">
                and so.type = #{condition.type}
            </if>
            <if test="condition.orgId !=null">
                and so.org_id = #{condition.orgId}
            </if>
            <if test="condition.ownerId !=null">
                and so.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.name !=null">
                and so.name = #{condition.name}
            </if>
            <if test="condition.clusterId !=null">
                and so.cluster_id = #{condition.clusterId}
            </if>
            <if test="condition.entityId !=null">
                and so.entity_id = #{condition.entityId}
            </if>
            <if test="condition.orderIdIn != null">
                and so.id in
                <foreach collection="condition.orderIdIn" separator="," item="id" open="(" close=")" >
                    #{id}
                </foreach>
            </if>
            <if test="condition.orderDetialId !=null">
                and sod.id = #{condition.orderDetialId}
            </if>
            <if test="condition.orderDetailIdIn !=null">
                and sod.id in
                <foreach collection="condition.orderDetailIdIn" separator="," item="type" open="(" close=")" >
                    #{type}
                </foreach>
            </if>
            <if test="condition.serviceTypeIn !=null">
                and sod.service_type in
                <foreach collection="condition.serviceTypeIn.split(',')" separator="," item="type" open="(" close=")" >
                    #{type}
                </foreach>
            </if>
            <if test="condition.gtStartTime !=null">
                and   #{condition.gtStartTime} > sod.start_time

            </if>
            <if test="condition.obsInModalArts == 'YES'">
                and   sorr.resource_id = -1 and sorr.type = "OBS"
            </if>


            <choose>
                <when test="condition.chargeType == 'PostPaid' ">
                    and (sod.charge_type is null or sod.charge_type = 'PostPaid')
                </when>
                <when test="condition.chargeType == 'PrePaid' ">
                    and sod.charge_type = 'PrePaid'
                </when>
            </choose>
        </where>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectProductResourceIdListByChargeType"
            resultType="java.lang.Long" parameterType="java.lang.String">
        select spr.id from sf_product_resource as spr
        left join service_order_resource_ref as sorr on spr.id=sorr.resource_id and spr.product_type=sorr.type
        left join service_order_detail as sod on sod.id=sorr.order_detail_id
        where sod.charge_type=#{chargeType}
        order by spr.id asc
    </select>

    <select id="selectProductResourceIdListByOrderSn"
            resultType="java.lang.Long" parameterType="java.lang.String">
        select spr.id from sf_product_resource as spr
        left join service_order_resource_ref as sorr on spr.id=sorr.resource_id and spr.product_type=sorr.type
        left join service_order_detail as sod on sod.id=sorr.order_detail_id
        left join service_order as so on sod.order_id=so.id
        where  so.order_sn like concat('%',#{orderSn},'%')
        order by spr.id asc
    </select>

    <select id="selectProductResourceIdListByOwnerId"
            resultType="java.lang.Long" parameterType="java.lang.String">
        select spr.id from sf_product_resource as spr
        left join service_order_resource_ref as sorr on spr.id=sorr.resource_id and spr.product_type=sorr.type
        left join service_order_detail as sod on sod.id=sorr.order_detail_id
        left join service_order as so on sod.order_id=so.id
        where so.owner_id in
        <foreach collection="ownerIdList" item="item"  index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by spr.id asc
    </select>

    <select id="selectReleaseByOrgSid" resultMap="BaseResultMap" parameterType="list">
        select
        t.id , t.order_id , t.charge_type , t.service_price , t.service_type
        from
        (
        select
        a.id , a.order_id , a.charge_type , a.service_price , a.service_type
        from
        service_order_detail a , service_order_resource_ref b , sf_product_resource c
        where
        a.id = b.order_detail_id
        and b.resource_id = c.id
        and b.`type` = c.product_type
        and c.status = 'unsubscribing'
        <if test="orgSidList != null">
            and a.org_sid in
            <foreach item="item" index="index" collection="orgSidList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        group by
        a.order_id
        union all
        select
        sod2.id , sod2.order_id , sod2.charge_type , sod2.service_price , sod2.service_type
        from
        service_order_detail sod2
        join (
        select
        *
        from
        service_order_price_detail w
        where
        w.ref_instance_id in(
        select
        concat('["', spr.id, '"]')
        from
        sf_product_resource spr
        where
        spr.status = 'unsubscribing'
        and spr.id in(
        select
        SUBSTRING_INDEX(SUBSTRING_INDEX(p.ref_instance_id, '["',-1), '"]', 1)
        from
        service_order_price_detail p
        where
        p.ref_instance_id is not null
        and p.`type` = 'renew'
        and p.order_detail_id in(
        select
        sod.id
        from
        service_order_detail sod
        where 1=1
        <if test="orgSidList != null">
            and sod.org_sid in
            <foreach item="item" index="index" collection="orgSidList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        )group by
        p.ref_instance_id ) )
        group by w.ref_instance_id ) a on
        sod2.id = a.order_detail_id) t
    </select>

    <select id="selectOrderDetailById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
            <include refid="Base_Column_List"/>,
        b.contract_title, b.contract_id, b.contract_file_name, b.contract_file
        from service_order a left join biz_contract b on a.contract_id = b.contract_id
        where a.id = #{orderId}
    </select>

    <select id="selectOrderByName" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from service_order a where a.name like concat('%',#{name},'%') and type='release' and status='completed'
        order by a.id desc
    </select>

    <select id="selectOrderByCluster" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from service_order a
        left join service_order_detail sod on a.id = sod.order_id
        where a.cluster_id=#{clusterId}
        <if test="productCode != null">
            and sod.service_type =#{productCode}
        </if>
         and a.`status`='completed'
    </select>

    <select id="selectDetailByResId"
            resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail">

        select sod.* from service_order_detail sod
        left join service_order so on sod.order_id = so.id
        left join sf_product_resource spr on so.id = spr.service_order_id
        where spr.id = #{id}
    </select>

    <select id="selectProductResourceIdListBypProductNameLike"
        resultType="java.lang.Long" parameterType="java.lang.String">
        select spr.id
        from sf_product_resource as spr
                 left join service_order_resource_ref as sorr
                           on spr.id = sorr.resource_id and spr.product_type = sorr.type
                 left join service_order_detail as sod on sod.id = sorr.order_detail_id
                 left join service_order as so on sod.order_id = so.id
        where so.name like concat('%', #{productNameLike}, '%')
        order by spr.id asc
    </select>

    <select id="selectOrderSnByEntityId" resultType="java.lang.String">
        select id from service_order
        where entity_id = #{entityId}
    </select>

    <select id="selectOrderDetailByClusterId"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail" parameterType="map">

        select sod.* from service_order so,service_order_detail sod

        where so.id=sod.order_id and so.type='apply' and so.cluster_id=#{clusterId}

        and sod.service_type=#{productType}
    </select>
    <select id="selectByInstanceId"
            resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM service_order a
        LEFT JOIN service_order_price_detail b ON a.order_sn = b.order_sn
        WHERE b.ref_instance_id = #{refInstanceId}
        and a.type = 'apply'
        ORDER BY id desc
    </select>
    <select id="selectOrderDetailByResourceId"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder">
        SELECT <include refid="Base_Column_List"/>,
        b.service_id as serviceId
        from service_order a
        LEFT JOIN service_order_detail b on a.id = b.order_id
        LEFT JOIN service_order_resource_ref c on b.id = c.order_detail_id
        where c.resource_id = #{id}
        and c.type = #{mainProductCode}
        order by a.id desc limit 1
    </select>
    <select id="findAccountIdByOrderSn" resultType="java.lang.Long">
        SELECT
            biz_billing_account_id
        FROM
            service_order
        WHERE
            order_sn = #{orderSn}
    </select>

    <select id="selectProductOrderByWeek"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail" parameterType="map">
        select sod.* from service_order so left join service_order_detail sod
                                                     ON so.id=sod.order_id
                                           left join service_order_price_detail sopd
                                                     on sod.id=sopd.order_detail_id
        where  sod.service_type=#{productCode}
          and so.type='modify'
          and sopd.ref_instance_id like concat('%',#{resourceId},'%')
          and DATE_SUB(CURDATE(), INTERVAL 7 DAY)&lt;=sod.start_time
    </select>


    <select id="selectForColleter" resultType="java.lang.Long">
        SELECT id FROM service_order WHERE cluster_id = #{id}  AND type = 'apply'
    </select>

    <select id="selectOrderByAdminSid"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder">
        SELECT distinct so.id,
                        so.order_sn,
                        so.name,
                        so.type,
                        so.org_sid,
                        so.owner_id,
                        so.extra_attr,
                        so.STATUS,
                        so.curr_amount,
                        so.final_cost,
                        so.original_cost,
                        so.org_discount,
                        so.coupon_discount,
                        so.ccsp_mac,
                        so.total_amount,
                        so.created_by,
                        so.created_dt,
                        so.updated_by,
                        so.updated_dt,
                        so.process_flag,
                        so.charging_type
        FROM service_order so
                 LEFT JOIN service_order_detail sod ON so.id = sod.order_id
                 LEFT JOIN sf_service_category a on sod.service_id = a.id
                 LEFT JOIN sf_product_template_relation b ON a.id = b.product_id
                 LEFT JOIN sf_product_template c ON b.template_id = c.id
                 LEFT JOIN sf_product_resource d ON a.service_type = d.product_type
                 LEFT JOIN biz_billing_account e ON d.org_sid = e.org_sid
        WHERE a.publish_status = 'succeed'
          AND a.editable = '1'
          AND a.publish_dt IS NOT NULL
          AND (
                c.template_content LIKE concat('%', 'PostPaid', '%')
                OR c.template_content LIKE concat('%', '公共资源池', '%'))
          AND d.STATUS != "unsubscribed"
		AND e.admin_sid = #{adminSid}
          AND so.owner_id = #{adminSid}
          AND so.`status` = 'completed'
          AND so.type = 'apply'
          AND e.admin_sid IS NOT NULL
          AND so.entity_id = #{entityId}
        ORDER BY so.id desc
    </select>

    <select id="selectByInstanceId"
        resultMap="BaseResultMap">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM service_order a
        LEFT JOIN service_order_price_detail b ON a.order_sn = b.order_sn
        WHERE b.ref_instance_id = #{refInstanceId}
        and a.type = 'apply'
        ORDER BY id desc
    </select>

    <select id="selectOrderDetailByClusterId"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail" parameterType="map">

        select sod.* from service_order so,service_order_detail sod

        where so.id=sod.order_id and so.type='apply'

        <if test="clusterId != null">
            and so.cluster_id=#{clusterId}
        </if>

        and sod.service_type=#{productType}

        order by sod.id desc
    </select>
    <select id="selectPendingOrder" resultType="java.lang.Integer">
        SELECT count(*) FROM service_order a
                                 LEFT JOIN service_order_price_detail pd
                                           ON a.order_sn = pd.order_sn
        WHERE a.`status` = 'pending'
          AND pd.product_code = #{productCode}
          AND REPLACE(REPLACE(pd.ref_instance_id, '["', ''),'"]','') = #{id}
    </select>

    <resultMap id="DrpListResultMap"
        type="cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DrpProductResponse">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="useDays" property="useDays" jdbcType="BIGINT"/>
        <result column="remainingDays" property="remainingDays" jdbcType="BIGINT"/>
    </resultMap>

    <select id="selectDrpList"
        resultMap="DrpListResultMap" parameterType="java.lang.Long">
        select spr.id ,so.`name`,DATEDIFF(NOW(),spr.start_time) +1 useDays ,DATEDIFF(spr.end_time,NOW())-1 remainingDays
        from sf_product_resource as spr
          left join service_order_resource_ref as sorr on spr.id=sorr.resource_id and spr.product_type=sorr.type
          left join service_order_detail as sod on sod.id=sorr.order_detail_id
          left join service_order as so on sod.order_id=so.id
        where so.owner_id = #{userSid}  AND spr.product_type = 'DRP'  AND spr.`status` not in ('deleted')  AND spr.end_time>=now();
    </select>


    <select id="selectConfig" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT config_value FROM sys_m_config WHERE config_key = #{key};
    </select>
</mapper>
