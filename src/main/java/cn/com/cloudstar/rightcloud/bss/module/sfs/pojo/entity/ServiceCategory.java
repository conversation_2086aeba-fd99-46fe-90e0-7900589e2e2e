/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@TableName("sf_service_category")
@CCSPProcessVerifyClass
public class ServiceCategory extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 服务类别
     */
    private String serviceForm;

    /**
     * 服务类型：group（多组合）  unit（单一）
     */
    private String serviceType;

    /**
     * 服务类别名称
     */
    @TableField(exist = false)
    private String serviceFormName;

    /**
     * 1.public  2.private
     */
    private String serviceClass;

    /**
     * 服务类别名称
     */
    @TableField(exist = false)
    private String serviceCategoryName;

    /**
     * 服务所有者id
     */
    private Long serviceOwnerId;

    /**
     * 服务所有者名字
     */
    private String serviceOwnerName;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务
     */
    private String serviceDesc;

    /**
     * 服务图标路径
     */
    private String serviceIconPath;

    /**
     * 服务配置
     */
    private String serviceConfig;

    /**
     * 云环境配置
     */
    @TableField(exist = false)
    private String envConfig;

    /**
     * 服务工作流id
     */
    private Long serviceWorkflowId;

    /**
     * 服务工作流名称
     */
    @TableField(exist = false)
    private String serviceWorkflowName;

    /**
     * 组织sid
     */
    private Long orgSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本
     */
    private Long version;

    /**
     * 状态
     */
    @CCSPIntegralityHashAndVerify
    private String status;

    /**
     * 自服务基础计价
     */
    private BigDecimal price;

    /**
     * res国旗
     */
    private String resFlag;

    /**
     * 服务细节
     */
    private String serviceDetails;

    /**
     * 开放式
     */
    private String openType;

    /**
     * 标识
     */
    @TableField(exist = false)
    private Integer flag;

    /**
     * 云环境类型列表
     */
    @TableField(exist = false)
    private Set<String> cloudEnvTypes;

    /**
     * 云环境id列表
     */
    @TableField(exist = false)
    private List<Long> cloudEnvIds;

    /**
     * 部署状态
     */
    @TableField(exist = false)
    private Boolean deployStatus;

    /**
     * 服务组件
     */
    private String serviceComponent;

    /**
     * 是iaas
     */
    @TableField(exist = false)
    private Boolean isIaaS;

    /**
     * 是paas
     */
    @TableField(exist = false)
    private Boolean isPaaS;

    /**
     * 组织名字
     */
    @TableField(exist = false)
    private String orgName;

    /**
     * 组名称
     */
    @TableField(exist = false)
    private String groupName;

    /**
     * 云环境类型
     */
    @Transient
    @TableField(exist = false)
    private String cloudEnvType;

    /**
     * 云环境id
     */
    @Transient
    @TableField(exist = false)
    private String cloudEnvId;

    /**
     * 服务类别列表
     */
    @Transient
    @TableField(exist = false)
    private List<ServiceCategoryAdditional> serviceCategoryAdditionals;

    /**
     * 服务目录列表
     */
    @TableField(exist = false)
    private List<ServiceCategoryCatalog> serviceCatalogs;

    /**
     * 相关组织名字列表
     */
    @TableField(exist = false)
    private Set<String> relatedOrgsName;

    /**
     * 发布状态
     */
    private String publishStatus;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品
     */
    private String productDesc;
    /**
     * 显示类型
     */
    private String showType;
    /**
     * 显示类型id
     */
    private String showTypeId;
    /**
     * 代表状态
     */
    private String behalfStatus;

    /**
     * 发布时间
     */
    private Date publishDt;

    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 可编辑
     */
    private Integer editable;

    /**
     * 运营实体id
     */
    private Long entityId;
    /**
     * 运营实体名称
     */
    private String entityName;

    /**
     * 模板id
     */
    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;


    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getBehalfStatus() {
        return behalfStatus;
    }

    public void setBehalfStatus(String behalfStatus) {
        this.behalfStatus = behalfStatus;
    }

    public String getServiceComponent() {
        return serviceComponent;
    }

    public void setServiceComponent(String serviceComponent) {
        this.serviceComponent = serviceComponent;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getCloudEnvType() {
        return cloudEnvType;
    }

    public void setCloudEnvType(String cloudEnvType) {
        this.cloudEnvType = cloudEnvType;
    }

    public List<ServiceCategoryAdditional> getServiceCategoryAdditionals() {
        return serviceCategoryAdditionals;
    }

    public void setServiceCategoryAdditionals(
        List<ServiceCategoryAdditional> serviceCategoryAdditionals) {
        this.serviceCategoryAdditionals = serviceCategoryAdditionals;
    }

    public String getOpenType() {
        return openType;
    }

    public void setOpenType(String openType) {
        this.openType = openType;
    }

    public Long getOrgSid() {
        return orgSid;
    }

    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceForm() {
        return serviceForm;
    }

    public void setServiceForm(String serviceForm) {
        this.serviceForm = serviceForm;
    }

    public String getServiceFormName() {
        return serviceFormName;
    }

    public void setServiceFormName(String serviceFormName) {
        this.serviceFormName = serviceFormName;
    }

    /**
     * @return 服务类型：group（多组合）  unit（单一）
     */
    public String getServiceType() {
        return serviceType;
    }

    /**
     * @param serviceType 服务类型：group（多组合）  unit（单一）
     */
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    /**
     * @return 1.public  2.private
     */
    public String getServiceClass() {
        return serviceClass;
    }

    /**
     * @param serviceClass 1.public  2.private
     */
    public void setServiceClass(String serviceClass) {
        this.serviceClass = serviceClass;
    }

    public Long getServiceOwnerId() {
        return serviceOwnerId;
    }

    public void setServiceOwnerId(Long serviceOwnerId) {
        this.serviceOwnerId = serviceOwnerId;
    }

    public String getServiceOwnerName() {
        return serviceOwnerName;
    }

    public void setServiceOwnerName(String serviceOwnerName) {
        this.serviceOwnerName = serviceOwnerName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceDesc() {
        return serviceDesc;
    }

    public void setServiceDesc(String serviceDesc) {
        this.serviceDesc = serviceDesc;
    }

    public String getServiceIconPath() {
        return serviceIconPath;
    }

    public void setServiceIconPath(String serviceIconPath) {
        this.serviceIconPath = serviceIconPath;
    }

    public String getServiceConfig() {
        return serviceConfig;
    }

    public void setServiceConfig(String serviceConfig) {
        this.serviceConfig = serviceConfig;
    }

    public Long getServiceWorkflowId() {
        return serviceWorkflowId;
    }

    public void setServiceWorkflowId(Long serviceWorkflowId) {
        this.serviceWorkflowId = serviceWorkflowId;
    }

    public String getServiceWorkflowName() {
        return serviceWorkflowName;
    }

    public void setServiceWorkflowName(String serviceWorkflowName) {
        this.serviceWorkflowName = serviceWorkflowName;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getResFlag() {
        return resFlag;
    }

    public void setResFlag(String resFlag) {
        this.resFlag = resFlag;
    }

    public String getServiceDetails() {
        return serviceDetails;
    }

    public void setServiceDetails(String serviceDetails) {
        this.serviceDetails = serviceDetails;
    }

    public String getServiceCategoryName() {
        return serviceCategoryName;
    }

    public void setServiceCategoryName(String serviceCategoryName) {
        this.serviceCategoryName = serviceCategoryName;
    }

    public List<ServiceCategoryCatalog> getServiceCatalogs() {
        return serviceCatalogs;
    }

    public void setServiceCatalogs(List<ServiceCategoryCatalog> serviceCatalogs) {
        this.serviceCatalogs = serviceCatalogs;
    }

    public Set<String> getCloudEnvTypes() {
        return cloudEnvTypes;
    }

    public void setCloudEnvTypes(Set<String> cloudEnvTypes) {
        this.cloudEnvTypes = cloudEnvTypes;
    }

    public List<Long> getCloudEnvIds() {
        return cloudEnvIds;
    }

    public void setCloudEnvIds(List<Long> cloudEnvIds) {
        this.cloudEnvIds = cloudEnvIds;
    }

    public Boolean getDeployStatus() {
        return deployStatus;
    }

    public void setDeployStatus(Boolean deployStatus) {
        this.deployStatus = deployStatus;
    }

    public Set<String> getRelatedOrgsName() {
        return relatedOrgsName;
    }

    public void setRelatedOrgsName(Set<String> relatedOrgsName) {
        this.relatedOrgsName = relatedOrgsName;
    }

    public Boolean isIaaS() {
        return isIaaS;
    }

    public void setIaaS(Boolean iaaS) {
        isIaaS = iaaS;
    }

    public Boolean isPaaS() {
        return isPaaS;
    }

    public void setPaaS(Boolean paaS) {
        isPaaS = paaS;
    }

    public String getCloudEnvId() {
        return cloudEnvId;
    }

    public void setCloudEnvId(String cloudEnvId) {
        this.cloudEnvId = cloudEnvId;
    }

    public String getEnvConfig() {
        return envConfig;
    }

    public void setEnvConfig(String envConfig) {
        this.envConfig = envConfig;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(String publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductDesc() {
        return productDesc;
    }

    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public String getShowType() {
        return showType;
    }

    public void setShowType(String showType) {
        this.showType = showType;
    }

    public String getShowTypeId() {
        return showTypeId;
    }

    public void setShowTypeId(String showTypeId) {
        this.showTypeId = showTypeId;
    }

}
