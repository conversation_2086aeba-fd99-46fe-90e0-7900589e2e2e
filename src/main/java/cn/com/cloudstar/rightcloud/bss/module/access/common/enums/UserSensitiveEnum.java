
package cn.com.cloudstar.rightcloud.bss.module.access.common.enums;

import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2022/12/22 14:30
 */
public enum UserSensitiveEnum {

    USER("user", "用户"),

    ACCOUNT("account", "账户"),

    INVOICE_DETAIL("invoiceDetail","发票"),
    INVOICE_SETTING("invoiceSetting","发票"),

    MOBILE("mobile", "手机号"),

    REGISTER_MOBILE("register_mobile", "注册电话"),

    REGISTER_ADDRESS("register_address", "注册地址"),

    EMAIL("email", "邮箱"),

    REALNAME("realName", "真实姓名"),

    ID_CARD("idCard", "身份证"),

    ADDRESS("address", "地址"),

    ORG("org", "组织"),
    PROVIDER("provider", "供应商"),
    LEGAL_PERSON("legalPerson", "法人代表"),
    LEGAL_PERSONCARD("legalPersonCard", "法人证件"),
    ID_CARD_FRONT_PICTURE("id_card_front", "身份证前面"),

    ID_CARD_REVERSE_PICTURE("id_card_reverse", "身份证后面"),

    BANK_ACCOUNT("bank_account", "银行卡");


    private String key;


    private String desc;

    UserSensitiveEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    public boolean equalsKey(String key) {
        return this.key.equals(key);
    }

    public static UserSensitiveEnum getEnum(String key) {
        for (UserSensitiveEnum value : UserSensitiveEnum.values()) {
            if (value.key.equals(key)) {
                return value;
            }
        }
        return null;
    }
}
