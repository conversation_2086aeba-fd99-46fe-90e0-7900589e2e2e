/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;

/**
 * @ClassName: SdrColectorTypeEnum.java
 * @Description： 话单类型枚举类
 * @Author: yun
 * @Date: 2021/2/26 11:38
 * @Vsersion: 1.0.0
 **/
public enum SdrColectorTypeEnum {
    /**
     * OBS
     */
    OBS(1, "OBS", "对象存储"),

    /**
     * sfs
     */
    SFS(2, "SFS", "弹性文件存储"),

    /**
     * hpc
     */
    HPC(3, "HPC", "高性能计算"),

    /**
     * sfs
     */
    MODELARTS(4, "ModelArts", "AI开发平台共享资源池"),

    /**
     * AiModel
     */
    AIMODEL(5, "AiModel", "AI大模型服务");

    private String code;
    private String desc;
    private Integer type;

    SdrColectorTypeEnum(Integer type, String code, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String get(Integer type) {
        for (SdrColectorTypeEnum typeEnum : SdrColectorTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getCode();
            }
        }
        return null;
    }

    public static SdrColectorTypeEnum get(String code) {
        for (SdrColectorTypeEnum typeEnum : SdrColectorTypeEnum.values()) {
            if (typeEnum.getCode().equalsIgnoreCase(code)) {
                return typeEnum;
            }
        }
        return null;
    }


}
