package cn.com.cloudstar.rightcloud.bss.module.provider.controller;


import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.entity.SysMProvider;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.request.SysMProviderApplyCancelRequest;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.request.SysMProviderCreateRequest;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.response.SysMProviderDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.provider.service.ISysMProviderService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.CheckSmsCodeUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/providers")
public class SysMProviderController {

    @Autowired
    private ISysMProviderService sysMProviderService;

    /**
     * 查询供应商详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @AuthorizeBss()
    public SysMProviderDetailResponse getProviderById(@PathVariable Long id) {
        //检查请求
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061170872));
        }
        SysMProvider sysMProvider = sysMProviderService.getById(id);
        if (sysMProvider == null || !authUserInfo.getUserSid().equals(sysMProvider.getOwnerId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061170872));
        }
        SysMProviderDetailResponse convert = BeanConvertUtil.convert(sysMProvider, SysMProviderDetailResponse.class);
        return DesensitizationUtil.doDesensitization(convert);
    }

    /**
     * 申请供应商
     * @param request
     * @return
     */
    @PostMapping
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'申请供应商'", tagNameUs ="'Apply Provider'",
        resource = OperationResourceEnum.APPLY_PROVIDER, param = "#request",bizId = "#request.id")
    @AuthorizeBss()
    public RestResult createProvider(@RequestBody @Valid SysMProviderCreateRequest request) {
        //检查请求
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061170872));
        }
        //请求幂等性
        boolean result = JedisUtil.instance().setnx( "applyProvider-" + authUserInfo.getOrgSid(), "true", 5);
        if (!result) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_315137718));
        }
        SysMProvider provider =  BeanConvertUtil.convert(request, SysMProvider.class);
        provider.setCompanyId(authUserInfo.getOrgSid());
        provider.setOwnerId(authUserInfo.getUserSid());
        Long providerId = sysMProviderService.applyProvider(provider);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1308329757), providerId);
    }

    @PutMapping("/applyCancel")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'申请注销供应商'", tagNameUs ="'Cancel Provider'",bizId = "#id",param = "#request",
        resource = OperationResourceEnum.APPLY_CANCEL_PROVIDER)
    @AuthorizeBss()
    public RestResult applyCancelProvider(@RequestBody @Valid SysMProviderApplyCancelRequest request) {
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        //检查请求
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061170872));
        }
        sysMProviderService.cancelProvider(authUserInfo.getUserSid(),request.getId());
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1308329757));
    }

}
