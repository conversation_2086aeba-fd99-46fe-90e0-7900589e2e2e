/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request;

import com.fasterxml.jackson.annotation.JsonFormat;

import org.hibernate.validator.constraints.Length;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

/**
 * <AUTHOR>
 * @since 2019/10/16 16:39
 */
@Data
@ApiModel("管理员废除优惠劵列表请求参数")
public class DescribeCouponDelRequest  {

    /**
     * 状态
     */
    @ApiModelProperty("备注")
    @Length(max = 256)
    @SafeHtml
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String remark;


    /**
     * 账户ID列表
     */
    @ApiModelProperty("账户ID列表")
    private List<Long> accountIdList;


}
