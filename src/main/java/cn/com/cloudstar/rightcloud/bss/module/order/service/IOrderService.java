/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;

/**
 * <AUTHOR>
 * @date 2021/7.
 */
public interface IOrderService {
    /**
     * 执行申请服务
     * @return
     */
    String orderServiceAction(ApplyServiceVO applyServiceVO);

    /**
     * 检查申请产品,是否需要开通公共产品
     * @return
     */
    String checkProduct(ApplyServiceVO applyServiceVO, Long userSid);

    /**
     * 自动申请开通modelarts
     * @return
     */
    String modelArtsOrder(ApplyServiceVO applyServiceVO);

    /**
     * 自动审核modelarts
     * @return
     */
    String modelArtsOpen(ApplyServiceVO applyServiceVO);

    /**
     *  处理订单编码对应计费规则数据存入redis
     */
    void handleOrderToChargeingType();

    String orderServiceMarket(ApplyServiceVO applyMarketVo);
}
