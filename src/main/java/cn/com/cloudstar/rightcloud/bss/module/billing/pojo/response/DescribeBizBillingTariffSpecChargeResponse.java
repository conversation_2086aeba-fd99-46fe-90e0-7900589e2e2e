/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.SpecMonthVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 规格定价响应
 *
 * <AUTHOR> Created on 2019/10/25
 */
@ApiModel(description = "规格定价")
@Data
public class DescribeBizBillingTariffSpecChargeResponse {
    /**
     * 策略id
     */
    @ApiModelProperty("策略ID")
    private Long id;


    /**
     * 定价配置
     */
    @ApiModelProperty("定价配置")
    private String billingConfig;
    /**
     * 按量计费基础价格（元/小时）
     */
    @ApiModelProperty("按量计费基础价格（元/小时）")
    private BigDecimal fixedHourPrice;
    /**
     * 包月包年基础价格（元/月）
     */
    @ApiModelProperty("包月包年基础价格（元/月）")
    private BigDecimal fixedMonthPrice;
    /**
     * 按量计费（元/小时）
     */
    @ApiModelProperty("按量计费（元/小时）")
    private BigDecimal hourPrice;
    /**
     * 包月包年（元/月）
     */
    @ApiModelProperty("包月包年（元/月）")
    private BigDecimal monthPrice;

    /**
     * 单次增量收费
     */
    @ApiModelProperty("单次增量收费")
    private BigDecimal oncePrice;
    /**
     * 是否为计费项
     */
    @ApiModelProperty("是否为计费项")
    private Boolean isCharge;
    /**
     * 计费方式
     */
    @ApiModelProperty("计费方式")
    private String chargeStrategy;
    /**
     * 组织sid
     */
    @ApiModelProperty("组织ID")
    private Long orgSid;
    /**
     * 规格组id
     */
    @ApiModelProperty(value = "规格族ID")
    private Long specGroupId;


    /**
     * 按量付费规格单价
     */
    @ApiModelProperty(value = "按量付费规格单价")
    private BigDecimal unitHourPrice;

    /**
     * 包年包月规格单价
     */
    @ApiModelProperty(value = "包年包月规格单价")
    private BigDecimal unitMonthPrice;

    /**
     * 用量
     */
    @ApiModelProperty(value = "用量，1000次或1000M")
    private Integer usageCount;

    /**
     * 用量对应价格
     */
    @ApiModelProperty(value = "用量对应价格")
    private BigDecimal usageCountPrice;

    /**
     * 指定月数计费
     */
    @ApiModelProperty(value = "指定月数计费")
    private List<SpecMonthVO> months;

    /**
     * 计费规格项描述
     */
    @ApiModelProperty(value = "计费规格项描述")
    private String specDesc;

    /**
     * 规格类型
     */
    @ApiModelProperty(value = "规格类型")
    private String specType;

    @ApiModelProperty(value = "转化系数")
    private BigDecimal convertRatio;
}
