/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.ShareSupportClusterTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.bss.common.constants.DealType;
import cn.com.cloudstar.rightcloud.bss.common.constants.PriceType;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.ContractStatus;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.service.HPCService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IBizBillingCycleService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.UpgradeDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractDetail;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractDetailService;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeShareRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeVmTypeNodeInfoRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeShareResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateRelationMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplateRelation;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SidService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.enums.HPCPropertyKey;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.tracelog.TraceUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShare;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.DiscountScope;
import cn.com.cloudstar.rightcloud.oss.common.constants.ServiceConfigArrKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.ClusterNodeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.BillType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.TradeType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CluseterSizeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

@Service
@Slf4j
public class HPCDrpOrderServiceImpl extends AbstractOrderService {
    //serviceOrderDetail serviceConfig json对应的key
    private static final String JSON_LABEL = "label";
    private static final String JSON_ATTR_KEY = "attrKey";
    private static final String JSON_VALUE = "value";
    private static final String HPC_POINT_TYPE_CLI = "CCS_CLI";
    private static final String HPC_POINT_TYPE_VNC = "VNC";
    private static final String HPC_POINT_TYPE_CCP = "CCP_MASTER";

    private static final String COMPUTE_NODE_INFO = "computeNodeInfo";
    private static final String MANAGEMENT_NODE_INFO = "managementNodeInfo";
    private static final String LOGIN_NODE_INFO = "loginNodeInfo";
    private static final String VNC_NODE_INFO = "vncNodeInfo";
    private static final String VNC_NODE = "vncNode";
    private static final String STORAGE_INFO = "storageInfo";
    private static final String AGREE_VPC = "agreeVpc";
    private static final String TIME = "time";
    private static final String ORG = "org";
    private static final String NAME = "name";

    private static final String MONTH_PATTERN = "yyyy-MM";

    private static final IdWorker ID_WORKER = new IdWorker();
    private static final String SALE_TYPE = "02";
    private static final String NORMAL_TYPE = "01";

    @Autowired
    private OrderService orderService;

    @Autowired
    private BizInquiryPriceService bizInquiryPriceService;

    @Autowired
    private HpcClusterService hpcClusterService;

    @Autowired
    private ShareService shareService;
    @Autowired
    private BizContractService bizContractService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private BizContractDetailService bizContractDetailService;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private SidService sidService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @Autowired
    private SfProductTemplateMapper sfProductTemplateMapper;
    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;
    @DubboReference
    private ResVmRemoteService resVmRemoteService;
    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;

    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;
    @Autowired
    private IBizDistributorProductService distributorProductService;
    @Autowired
    private SfProductTemplateRelationMapper sfProductTemplateRelationMapper;
    @Autowired
    private IBizDistributorService bizDistributorService;
    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;
    @Autowired
    private ISfProductResourceService sfProductResourceService;
    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;
    @Autowired
    private BizBillingAccountMapper billingAccountMapper;

    @Autowired
    private HPCService hpcService;

    /**
     * 文件系统使用列表
     */
    public static Set<Object> toBeUsedShareIds = new HashSet<>();
    @Autowired
    private IBizBillingCycleService bizBillingCycleService;

    @Override
    public String apply(ApplyServiceVO serviceVO) {
        Set<Object> usedShareIds = new HashSet<>();
        try {
            ApplyEntity applyEntity = before();
            if (applyEntity.getAuthUser().getParentSid() != null) {
                return "只有租户管理员可以开通 HPC 服务";
            }
            //判断是否冻结
            RestResult restResult = orderService.isFreeze(applyEntity.getAuthUser().getUserSid());
            if (restResult.getStatus()&&(Boolean) restResult.getData()) {
                return "用户已被冻结";
            }

            this.checkParams(serviceVO.getProductInfo());

            //校验预部署sfs文件大小
            checkSfsConfig(serviceVO.getProductInfo());

            this.validFileSystem(serviceVO.getProductInfo());


            serviceVO.setProjectId(applyEntity.getAuthUser().getOrgSid());
            String cache = "apply:hpc:drp:" + applyEntity.getAuthUser().getUserSid();
            String s = JedisUtil.INSTANCE.get(cache);
            if (StringUtils.isNotEmpty(s)) {
                return "提交申请过快,请稍后重试";
            }
            JedisUtil.INSTANCE.set(cache, "nice", 10);
            //验证用户是否实名
            restResult = orderService.checkAuth(applyEntity.getAuthUser().getUserSid());
            if (!restResult.getStatus()) {
                return "请先实名认证";
            }

            validateSpec(serviceVO);
            log.info("开始待处理文件系统ID：[{}]", toBeUsedShareIds);
            // HPC专属资源池检查
            if(ApplyTypeEnum.HPC_DRP_CUSTOM.getType().equals(serviceVO.getProductInfo().get(0).getApplyType())){
            usedShareIds = hpcDrpValidate(serviceVO);
            }


            execute(serviceVO, applyEntity);

            String result = remoteInvoke(serviceVO, applyEntity);

            after(applyEntity);
            return result;
        } finally {
            log.info("待处理文件系统ID：[{}]", toBeUsedShareIds);
            log.info("处理文件系统ID：[{}]", usedShareIds);
            toBeUsedShareIds.removeAll(usedShareIds);
            log.info("结束待处理文件系统ID：[{}]", toBeUsedShareIds);
        }
    }

    /**
     *  参数检查
     * @param productInfo
     */
    private void checkParams(List<ProductInfoVO> productInfo) {
        ProductInfoVO productInfoVO = productInfo.get(0);
        // 验证计费类型
        ChargeTypeEnum.toKsyunApiChargeType(productInfoVO.getChargeType());

        String applyType = productInfoVO.getApplyType();
        if (!ApplyTypeEnum.HPC_DRP_CUSTOM.getType().equals(applyType) && !ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(applyType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_213209218));
        }
    }


    /**
     * 校验文件系统匹配与否
     * @param productInfo
     */
    private void validFileSystem(List<ProductInfoVO> productInfo) {
        Optional<ProductInfoVO> optional = productInfo.stream()
                .filter(infoVO -> ProductCodeEnum.HPC_DRP.getProductType().equals(infoVO.getProductCode()))
                .findFirst();
        if (!optional.isPresent()) {
            return;
        }

        ProductInfoVO productInfoVO = optional.get();

        if (!ApplyTypeEnum.HPC_DRP_CUSTOM.getType().equals(productInfoVO.getApplyType())) {
            // 只校验自定义专属资源池，防止影响标准专属资源池开通
            return;
        }
        JSONArray jsonArray = JSON.parseArray(productInfoVO.getProductConfigDesc().getCurrentConfigDesc());

        for (Object node:jsonArray) {
            com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject)node;
            if (STORAGE_INFO.equals(jsonObject.get(JSON_ATTR_KEY))) {
                String shareId = jsonObject.getJSONArray(JSON_VALUE).get(0).toString();
                RestResult shareById = shareService.getShareById(Long.parseLong(shareId));
                ResShare resShare = new ResShare();
                BeanUtil.copyProperties(shareById.getData(), resShare);
                String property = PropertiesUtil.getProperty(HPCPropertyKey.HPC_PREDEPLOY_SWITCH_KEY.getCode());

                // 表示高级自定义开关打开，但是传入的文件系统为非高级自定义文件系统
                boolean switchOn = HPCPropertyKey.SWITCH_ON.getCode().equals(property)
                        && !ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType());
                // 表示高级自定义开关关闭，但是传入的文件系统为高级自定义文件系统
                boolean switchOff = HPCPropertyKey.SWITCH_OFF.getCode().equals(property)
                        && ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType());
                if (switchOn || switchOff){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832409397));
                }
                break;
            }
        }
    }

    /**
     * 插入审批单 启动审批流程
     *
     * @param serviceVO
     * @param applyEntity
     */
    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ServiceOrder order = applyEntity.getOrder();
        try {
        orderService.startProcessByOrder(order.getId());
        }catch (Exception e) {
            try {
                log.info("申请HPC，开始回滚");
                QueryHPCShareCluster queryHPCShareCluster = new QueryHPCShareCluster();
                queryHPCShareCluster.setStatus(ResHpcClusterStatus.APPLY);
                queryHPCShareCluster.setName(order.getName());
                List<ResHpcClusterRemoteModule> resHpcClusterRemoteModules = hpcRemoteService.selectByParams(queryHPCShareCluster);
//                if (CollectionUtil.isNotEmpty(resHpcClusterRemoteModules)) {
//                    hpcRemoteService.rollBackHPC(
//                            resHpcClusterRemoteModules.get(resHpcClusterRemoteModules.size() - 1).getId());
//                }

                serviceOrderDetailMapper.delete(new QueryWrapper<ServiceOrderDetail>().eq("order_id", order.getId()));
                serviceOrderMapper.deleteById(order.getId());
                serviceOrderPriceDetailMapper.delete(new QueryWrapper<ServiceOrderPriceDetail>().eq("order_sn", order.getOrderSn()));
                SfProductResource sfProductResource = sfProductResourceService.getOne(new QueryWrapper<SfProductResource>().eq("service_order_id", order.getId()));
                List<ServiceOrderResourceRef> orderResourceRefList = serviceOrderResourceRefMapper.getOrderResourceRefByDetailId(order.getOrderDetails().get(0).getId());
                if (CollectionUtil.isNotEmpty(orderResourceRefList)) {
                    serviceOrderResourceRefMapper.deleteById(orderResourceRefList.get(0).getId());
                }
                if (Objects.nonNull(sfProductResource)) {
                    sfProductResourceService.removeById(sfProductResource.getId());
                 //   hpcRemoteService.rollBackHPC(sfProductResource.getClusterId());
                }
            }catch (Exception innerException) {
                log.error("申请HPC回退，回退错误：[{}]", innerException.getMessage());
            }
            log.error("申请HPC回退，发生异常：[{}]", e.getMessage());
            throw e;
        }
        log.info("HPC申请启动审批流程 结束");
        return null;
    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        String clusterName = serviceVO.getProductName();
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().stream()
                .filter(infoVO -> ProductCodeEnum.HPC_DRP.getProductType().equals(infoVO.getProductCode()))
                .findFirst().get();

        if (StrUtil.isBlank(clusterName) || !clusterName.matches("^[\\u4E00-\\u9FA5A-Za-z0-9]{1}[\\u4E00-\\u9FA5A-Za-z0-9-]{1,22}[\\u4E00-\\u9FA5A-Za-z0-9]{1}$")) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1915745700));
        }

        // 预部署文件系统不校验hpc-cluster-pool表重名
        String checkParam = clusterName + CommonPropertyKeyEnum.PLACEHOLDER_MARK.getCode() + productInfoVO.getApplyType();
        RestResult restResult = hpcClusterService.checkHpcClusterName(checkParam);
        if (Objects.nonNull(restResult.getData()) && (Boolean) restResult.getData()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_96609621)+clusterName+WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049732961));
        }
    }

    private Set<Object> hpcDrpValidate(ApplyServiceVO serviceVO) {
        Set<Object> usedShareIds = new HashSet<>();
        serviceVO.getProductInfo().forEach(productInfoVO -> {
            if (ProductCodeEnum.HPC_DRP.getProductType().equals(productInfoVO.getProductCode())) {
                InsertHpcDrpResource hpcDrpResource = new InsertHpcDrpResource();
                hpcDrpResource.setProductConfig(productInfoVO.getProductConfigDesc().getCurrentConfigDesc());
                hpcDrpResource.setTest(true);
                // 文件系统检查
                // 可用文件系统取得
                DescribeShareRequest shareRequest = new DescribeShareRequest();
                shareRequest.setNotIncludeUsed(true);
                shareRequest.setStatus("available");
                RestResult shareResult = shareService.getShareList(BasicInfoUtil.getMaxDataScope(), shareRequest);
                if (shareResult.getStatus()) {
                    Object resultData = shareResult.getData();
                    List<?> list;
                    if (resultData instanceof List) {
                        list = (List<?>) resultData;
                    } else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1715570267));
                    }
                    if (CollectionUtil.isEmpty(list)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1715570267));
                    }
                    List<DescribeShareResponse> shareResponses = BeanConvertUtil.convert(list, DescribeShareResponse.class);
                    List<Long> shareIds = shareResponses.stream().map(DescribeShareResponse::getId).collect(Collectors.toList());
                    toBeUsedShareIds.removeIf(toBeUsedShareId -> !shareIds.contains(toBeUsedShareId));
                    log.info("开始待处理文件系统去除已使用ID：[{}]", toBeUsedShareIds);
                    for (Object object : JSONUtil.parseArray(hpcDrpResource.getProductConfig())) {
                        JSONObject jsonObject = (JSONObject) object;
                        if (Objects.equals(jsonObject.get("attrKey"), "storageInfo")) {
                            for (Object shareId : (List<Object>) jsonObject.get("value")) {
                                if (!shareIds.contains(Convert.toLong(shareId, 0L))
                                    || toBeUsedShareIds.contains(shareId)) {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_620938601) + shareId + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_838132676));
                                }
                                toBeUsedShareIds.add(shareId);
                                usedShareIds.add(shareId);
                            }
                        }
                    }
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1715570267));
                }
                //预检是否能够插入
                hpcRemoteService.insertHpcDrpResource(hpcDrpResource);
            }
            String resourceType = StrUtil.EMPTY;
            if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(productInfoVO.getProductCode())) {
                resourceType = "管理节点";
            } else if (ProductCodeEnum.BMS.getProductType().equalsIgnoreCase(productInfoVO.getProductCode())) {
                resourceType = "计算节点";
            }
            if (StrUtil.isNotBlank(resourceType)) {
                if (StrUtil.isBlank(productInfoVO.getShareTypeId())) {
                    log.error("请确认模版中的集群类型是否正确！参数：[{}]", JSONUtil.toJsonStr(productInfoVO));
                } else {
                    DescribeVmTypeNodeInfoRequest describeVmTypeNodeInfoRequest = new DescribeVmTypeNodeInfoRequest();
                    describeVmTypeNodeInfoRequest.setResourceType(productInfoVO.getProductCode());
                    describeVmTypeNodeInfoRequest.setFlavorRef(productInfoVO.getProductCategory());
                    describeVmTypeNodeInfoRequest.setShareType(productInfoVO.getShareTypeId());
                    describeVmTypeNodeInfoRequest.setNodeCount(Convert.toLong(productInfoVO.getAmount()));
                    describeVmTypeNodeInfoRequest.setCloudEnvId(productInfoVO.getCloudEnvId());
                    RestResult result = hpcClusterService.getNodeInfo(describeVmTypeNodeInfoRequest);
                    if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                        Map<String, Object> nodeInfo = (Map<String, Object>) result.getData();
                        if (Convert.toLong(nodeInfo.get("freeCount")).compareTo(0L) < 0) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1815238196) + resourceType + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_91) + productInfoVO.getProductCategory() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1696329740));
                        }
                    }
                }
            }
        });
        return usedShareIds;
    }

    @Override
    public void inquiryPrice(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.inquiryPrice(serviceVO, applyEntity);
        List<InquiryPriceResponse>  inquiryPriceResponses = applyEntity.getPrices();
        if (inquiryPriceResponses == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
        }

        inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(ProductCodeEnum.HPC_DRP.getProductType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(inquiryPriceResponses) || CollectionUtil.isEmpty(inquiryPriceResponses.get(0).getBillingPrices())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_836856513));
        }

        inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(ProductCodeEnum.BMS.getProductType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(inquiryPriceResponses) || CollectionUtil.isEmpty(inquiryPriceResponses.get(0).getBillingPrices())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_835480726));
        }

        if (ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(serviceVO.getProductInfo().get(0).getApplyType())) {
            inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(ProductCodeEnum.SFS2.getProductType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(inquiryPriceResponses) || CollectionUtil.isEmpty(inquiryPriceResponses.get(0).getBillingPrices())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1472922318));
            }
        }

        inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(ProductCodeEnum.ECS.getProductType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(inquiryPriceResponses)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_690160127));
        } else {
            for (InquiryPriceResponse response : inquiryPriceResponses) {
                String productCategory = response.getProductCategory();
                if (CollectionUtil.isEmpty(response.getBillingPrices())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_619263840) + productCategory + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_144599566));
                }
            }
        }

        // 优惠券计算
        if (Objects.nonNull(applyEntity.getBizCoupon())) {
            bizInquiryPriceService.multipleProductCouponPrice(applyEntity.getPrices(), applyEntity.getBizCoupon().getDiscountAmount());
        }
    }


    /**
     * 升级
     * <AUTHOR>
     * @date  2022/5/1 15:05
     */
    @Override
    public String upgrade(ApplyServiceRequest serviceRequest){
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        Long contractId = serviceRequest.getContractId();
        if(contractId ==null){
            return WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR);
        }
        Long resourceId = serviceRequest.getResourceId();
        if(resourceId == null){
            return WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR);
        }
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(resourceId);
        if(sfProductResource == null){
            return "产品不存在";
        }
        try {
            long traceId = TraceUtil.tracingInstance().currentTraceContext().get().traceId();
            String cacheKey = TraceUtil.TRACE_ID_KEY + sfProductResource.getProductType() + StrUtil.COLON + sfProductResource.getClusterId();
            JedisUtil.INSTANCE.set(cacheKey,String.valueOf(traceId));
        } catch (Exception e) {
            log.info("设置当前链路traceId-HPCDrpOrderServiceImpl.upgrade-失败:[{}]", e.getMessage());
        }
        Long authUserSid = authUserInfo.getUserSid();
        boolean isOperationAdmin = sysUserService.checkOperationAdmin(authUserSid);
        AuthUser applyUser = BeanConvertUtil.convert(authUserInfo, AuthUser.class);
        //如果是运营管理员，换成租户
        if(isOperationAdmin){
            Long orgSid = sfProductResource.getOrgSid();
            User operationUser = sysUserService.lambdaQuery().eq(User::getOrgSid, orgSid).isNull(User::getParentSid).one();
            applyUser = BeanConvertUtil.convert(operationUser,AuthUser.class);
        }
        if (!applyUser.getOrgSid().equals(sfProductResource.getOrgSid())) {
            return "产品不存在";
        }

        String status = sfProductResource.getStatus();
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(sfProductResource.getClusterId());
        if(!StringUtils.equalsIgnoreCase(SfProductEnum.NORMAL.getStatus(),status)
                || !StringUtils.equalsIgnoreCase(ResHpcClusterStatus.AVALIABLE, resHpcClusterRemoteModule.getStatus())){
            return "实例状态不可用。";
        }
        HpcDrpUpgradeEntity upgradeEntity = HpcDrpUpgradeEntity.builder().authUser(authUserInfo).build();

        upgradeEntity.setResourceId(Arrays.asList(sfProductResource.getId().toString()));

        List<BizBillingAccount> accounts = bizBillingAccountService.getByOrgSid(sfProductResource.getOrgSid());
        if (accounts == null) {
            return "账户不存在";
        }
        BizContract bizContract = bizContractService.lambdaQuery().eq(BizContract::getContractId, contractId)
                .in(BizContract::getAccountId, accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toSet())).one();
        if(bizContract == null){
            return "合同不存在。";
        }
        String contractStatus = bizContract.getContractStatus();
        if (!ContractStatus.UNUSED.equals(contractStatus)) {
            return "合同不可用。";
        }
        Date sfProductResourceEndTime = sfProductResource.getEndTime();
        List<BizContractDetail> list = bizContractDetailService.lambdaQuery().eq(BizContractDetail::getContractSid, contractId).list();
        bizContract.setContractDetails(list);
        if(DiscountScope.HPC_RENEW.equals(bizContract.getDiscountScope())){
            serviceRequest.setOrderType(OrderType.UPGRADE_RENEW);

            BizContractDetail bizContractDetail = list.get(0);
            Date contractEndTime = bizContract.getEndTime();
            //合同没有结束时间设置时间
            if(contractEndTime == null){
                assembleDate(bizContract,bizContractDetail, sfProductResourceEndTime);
            }
            //超期设置结束时间为集群资源到期时间
            contractEndTime = bizContract.getEndTime();
            if(contractEndTime.after(sfProductResourceEndTime)){
                contractEndTime = sfProductResourceEndTime;
            }
            bizContract.setEndTime(contractEndTime);
            bizContractDetail.setEndTime(contractEndTime);
        }
        Date startTime = bizContract.getStartTime();
        if(startTime !=null && !startTime.before(sfProductResourceEndTime)){
            return "合同超期。";
        }
        BigDecimal contractTradePrice = BigDecimal.ZERO;
        for (BizContractDetail bizContractDetail : list) {
            contractTradePrice = bizContractDetailService.getContractDetailTotalPrice(bizContractDetail);
        }
        Long userSid = applyUser.getUserSid();
        //判断是否冻结
        RestResult restResult = orderService.isFreeze(userSid);
        if (restResult.getStatus()&&(Boolean) restResult.getData()) {
            return "用户已被冻结。";
        }
        String cache = "upgrade:hpc:drp:" + userSid;
        String s = JedisUtil.INSTANCE.get(cache);
        if (StringUtils.isNotEmpty(s)) {
            return "提交申请过快,请稍后重试。";
        }
        JedisUtil.INSTANCE.set(cache, "nice", 10);

        upgradeEntity.setContract(bizContract);
        //查看余额
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getBillingAccountDetail(bizContract.getAccountId());
        BigDecimal balance = Optional.ofNullable(bizBillingAccount.getBalance()).orElse(BigDecimal.ZERO);
        BigDecimal balanceCash = Optional.ofNullable(bizBillingAccount.getBalanceCash()).orElse(BigDecimal.ZERO);
        BigDecimal creditLine = Optional.ofNullable(bizBillingAccount.getCreditLine()).orElse(BigDecimal.ZERO);
        BigDecimal remaimAmount = NumberUtil.add(balance, balanceCash, creditLine);

        if (NumberUtil.isLess(remaimAmount,contractTradePrice)) {
            return "余额不足。";
        }
        serviceRequest.setContractPrice(contractTradePrice);
        upgradeEntity.setOriginalPrice(contractTradePrice);
        upgradeEntity.setTradePrice(contractTradePrice);
        upgradeEntity.setAccount(bizBillingAccount);
        //查询申请订单,根据申请单创建扩容申请单
        String discountScope = bizContract.getDiscountScope();
        Long serviceOrderId = sfProductResource.getServiceOrderId();
        ServiceOrder serviceOrder = serviceOrderMapper.selectById(serviceOrderId);
        //续订订单取扩容订单
        if(DiscountScope.HPC_RENEW.equals(discountScope)) {
            Long contractSid = bizContract.getContractSid();

            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(new QueryWrapper<ServiceOrder>().lambda()
                    .eq(ServiceOrder::getContractId, contractSid)
                    .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED));
            serviceOrder = CollectionUtil.getFirst(serviceOrders);
        }
        upgradeEntity.setOrder(serviceOrder);
        this.buildUpgradeOrder(serviceRequest,upgradeEntity);

        //扩容合同走开启流程
        ServiceOrder upgradeEntityOrder = upgradeEntity.getOrder();
        if(DiscountScope.HPC_OPEN.equals(discountScope)){
            orderService.startProcessByOrder(upgradeEntityOrder.getId());
        }

        if(DiscountScope.HPC_RENEW.equals(discountScope)){

            this.hpcDrpUpgradeCost(upgradeEntityOrder,bizBillingAccount,null);

            upgradeEntityOrder.setStatus(OrderStatus.COMPLETED);
            upgradeEntityOrder.setStepName("扩容续订完成");
            this.serviceOrderMapper.update(upgradeEntityOrder,new QueryWrapper<ServiceOrder>().lambda().eq(ServiceOrder::getId,upgradeEntityOrder.getId()));
            try {
                this.sendUgradeCompletedMessage(isOperationAdmin, applyUser, upgradeEntityOrder);
            } catch (Exception e) {
                log.error("HPC扩容续订发送通知-HpcDrpOrderServiceImpl-upgrade-异常:【{}】",e);
            }
        }
        //修改合同状态
        bizContract.setContractStatus(ContractStatus.AUDIT_EXECUTING);
        bizContractService.updateById(bizContract);

        return StrUtil.EMPTY;
    }

    private void sendUgradeCompletedMessage(boolean isOperationAdmin, AuthUser applyUser, ServiceOrder upgradeEntityOrder) {

        Map<String,String> messageContent = new HashMap<>();
        messageContent.put("productName", upgradeEntityOrder.getProductName());
        messageContent.put("poolName", upgradeEntityOrder.getName());
        User user = new User();
        user.setUserSid(applyUser.getUserSid());
        user.setAccount(applyUser.getAccount());
        sendMessage(user,messageContent,NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_UP_RENEW,NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_SCALE_UP_RENEW,upgradeEntityOrder.getEntityId());
    }

    /**
     * 发送信息
     * @param
     */
    private void sendMessage(User user, Map<String, String> messageContent, String msg1, String msg2 , Long entityId) {

        messageContent.put("userAccount", user.getAccount());
        NotificationUtil.assembleBaseMessageContent(messageContent);

        // 系统名称
        if (msg1!=null) {
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(msg1);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setEntityId(entityId);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }catch (Exception e){
                log.error("共享资源池发送信息异常：",e);
            }
        }
        //运营管理员发送消息

        if (msg2!=null) {
            List<cn.com.cloudstar.rightcloud.bss.common.pojo.User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            if(CollUtil.isNotEmpty(adminstrators)){

                try {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().
                            map(cn.com.cloudstar.rightcloud.bss.common.pojo.User::getUserSid).collect(Collectors.toSet()));
                    baseNotificationMqBean.setEntityId(entityId);
                    baseNotificationMqBean.setMsgId(msg2);
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
                }catch (Exception e){
                    log.error("共享资源池发送信息异常：",e);
                }
            }
        }
    }

    private void hpcDrpUpgradeCost(ServiceOrder serviceOrder, BizBillingAccount bizBillingAccount, Date startTime) {
        List<ServiceOrderDetail> details = serviceOrder.getOrderDetails();
        ServiceOrderDetail serviceOrderDetail = details.get(0);
        if (startTime == null) {
            startTime = serviceOrderDetail.getStartTime();
        }
        if (startTime == null) {
            startTime = new Date();
        }
        String orderSn = serviceOrder.getOrderSn();
        QueryWrapper<ServiceOrderPriceDetail> objectQueryWrapper = new QueryWrapper<>();
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper
                .selectList(objectQueryWrapper.lambda().eq(ServiceOrderPriceDetail::getOrderSn,orderSn));

        List<InstanceGaapCost> costs = new ArrayList<>();
        // 入账账单
        Date endTime = org.apache.commons.lang3.time.DateUtils.addMonths(startTime, serviceOrderDetail.getDuration());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectList(new QueryWrapper<SfProductResource>()
                .lambda().eq(SfProductResource::getClusterId,serviceOrder.getClusterId()).eq(SfProductResource::getProductType,ProductCodeEnum.HPC_DRP.getProductType()));
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);
        Date sfEndTime = sfProductResource.getEndTime();
        if (endTime.after(sfEndTime)) {
            endTime = sfEndTime;
        }

        Long contractId = serviceOrder.getContractId();
        BizContract bizContract = null;
        if (contractId != null) {
            bizContract = bizContractService.lambdaQuery().eq(BizContract::getContractId,contractId).one();
        }

        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            //扩容续订不计算集群的价格
            if (StringUtils.equals(priceDetail.getProductCode(), ProductCodeEnum.HPC_DRP.getProductType())) {
                continue;
            }
            BigDecimal amount = Optional.ofNullable(priceDetail.getAmount()).orElse(BigDecimal.ZERO);

            priceDetail.setStartTime(startTime);

            priceDetail.setEndTime(endTime);
            InstanceGaapCost cost = new InstanceGaapCost();

            cost.setQuantity(priceDetail.getQuantity());
            cost.setOrderId(serviceOrder.getId().toString());
            cost.setOrderSn(orderSn);
            cost.setPayTime(new Date());
            cost.setUsageStartDate(priceDetail.getStartTime());
            cost.setUsageEndDate(priceDetail.getEndTime());
            cost.setUsageCount(
                    cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(), false)
                            + "天");

            cost.setBillType(BillType.fromChargeTypeEnum(priceDetail.getChargeType()));
            cost.setBillingCycle(cn.hutool.core.date.DateUtil.format(startTime, "yyyy-MM"));
            cost.setOrgSid(serviceOrder.getOrgSid());
            cost.setOwnerId(serviceOrder.getOwnerId().toString());
            cost.setBillNo(NoUtil.generateNo("ZD"));
            cost.setCurrency("CNY");
            cost.setPriceType("resource");
            cost.setBillSource("platform");
            cost.setUserAccountId(bizBillingAccount.getId());
            Long adminSid = bizBillingAccount.getAdminSid();
            cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(adminSid);
            String accountName = bizBillingAccount.getCreatedBy();
            if (user != null) {
                accountName = user.getAccount();
            }
            cost.setUserAccountName(accountName);
            cost.setPrice(priceDetail.getPrice());
            cost.setPretaxGrossAmount(priceDetail.getOriginalCost());
            cost.setPricingDiscount(priceDetail.getDiscount());

            //抹零金额
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            //最终价格
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount)); // tradeFinalCost 最终价格

            cost.setProductCode(priceDetail.getProductCode());

            String productName = ProductCodeEnum.toDesc(priceDetail.getProductCode());
            log.info("所属产品Code：[{}]", priceDetail.getProductCode());
            if (ProductCodeEnum.HPC_DRP.getProductType().equals(priceDetail.getProductCode())) {
                productName = serviceOrder.getProductName();
                log.info("HPC_DRP 所属产品名称：[{}]", productName);
            }
            cost.setProductName(productName);

            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            cost.setType(serviceOrder.getType());
            if (Objects.nonNull(bizBillingAccount)) {
                BizDistributor distributor = bizDistributorService.getById(bizBillingAccount.getDistributorId());
                cost.setDistributorName(Objects.nonNull(distributor) ? distributor.getName() : null);
            }
            ServiceOrderDetail orderDetail = serviceOrderDetailMapper.selectById(priceDetail.getOrderDetailId());
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(orderDetail.getServiceConfig());
            cost.setResourceId(Optional.ofNullable(jsonObject.getString(ServiceConfigArrKey.NODE_INSTANCE_ID_LIST))
                    .orElse("")); // 节点uuid


            Boolean contractPayStatus = bizContract.getPayStatus();

            String bizContractPayStatus = Boolean.TRUE.equals(contractPayStatus) ? "合同已付款。" : "合同未付款。";
            cost.setConfiguration(priceDetail.getBillingSpec() + ";" + bizContractPayStatus);

            setUsedCost(bizBillingAccount, cost, amount);
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(NumberUtil
                    .sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(NumberUtil
                    .sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            cost.setBizBillingAccount(BeanUtil.toBean(bizBillingAccount, cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount.class));
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());

            costs.add(cost);
            serviceOrderPriceDetailMapper.updateById(priceDetail);
        }
        //修改订单开始结束时间
        for (ServiceOrderDetail detail : details) {
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
        }
        //修改合同时间
        if (bizContract != null && bizContract.getStartTime() == null) {
            bizContract.setStartTime(startTime);
            bizContract.setEndTime(endTime);
            bizContract.setUpdatedDt(startTime);
            bizContractService.updateById(bizContract);

            List<BizContractDetail> bizContractDetails = bizContractDetailService.getBaseMapper().selectList(new QueryWrapper<BizContractDetail>().lambda().eq(BizContractDetail::getContractSid,contractId));
            if (CollectionUtil.isNotEmpty(bizContractDetails)) {
                for (BizContractDetail bizContractDetail : bizContractDetails) {
                    bizContractDetail.setStartTime(startTime);
                    bizContractDetail.setEndTime(endTime);
                    bizContractDetail.setUpdatedDt(endTime);

                    bizContractDetailService.updateById(bizContractDetail);
                }
            }
        }

        bizBillingAccountService.updateById(bizBillingAccount);
        //新增变更记录
        ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
        resChangeRecordDTO = saveResChangeRecord(serviceOrder.getClusterId(),resChangeRecordDTO,bizContract);
        //fix(49071),专属资源池扩容续订cli节点时长超过资源池结束时间，变更记录里扩容续订时间显示不正确
        resChangeRecordDTO.setChangeStartTime(startTime);
        resChangeRecordDTO.setChangeEndTime(endTime);
        //插入
        resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
        try {
            Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costs);
            if (!CollectionUtil.isEmpty(instanceGaapCosts)) {
                List<InstanceGaapCost> costList = new ArrayList<InstanceGaapCost>(instanceGaapCosts);
                this.insertBillCycleInfo(costList);
                BigDecimal finalCost = serviceOrder.getFinalCost();
                if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
                    this.insertAccountDeal(costs, serviceOrder);
                }
            } else {
                log.error("ServiceOrderServiceImpl------processProduct-----------failed---instanceGaapCosts isEmpty");
            }
        } catch (Exception e) {
            log.error("入账失败", e);
        }
    }

    private ResChangeRecordDTO saveResChangeRecord(Long clusterId, ResChangeRecordDTO resChangeRecordDTO, BizContract bizContract) {
       // String nodeInfos = hpcRemoteService.getNodeInfo(clusterId);
    //    resChangeRecordDTO.setConfigDesc(nodeInfos);
        List<NodeInfo> nodeInfoList = getNodeInfos(clusterId);
        nodeInfoList.removeIf(nodeInfo -> ResHpcClusterNodeStatus.REMOVING.equals(nodeInfo.getStatus()));
        Date curr = new Date();
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        //插入变更记录
        resChangeRecordDTO.setCloudEnvId(resHpcCluster.getCloudEnvId());
        resChangeRecordDTO.setResourceId(resHpcCluster.getId().toString());
        resChangeRecordDTO.setInstanceId(resHpcCluster.getResourceId());
        resChangeRecordDTO.setResType("HPC-DRP");
        long nodeNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType()))
                .count();
        resChangeRecordDTO.setOriginalType(String.valueOf(nodeNum));
        resChangeRecordDTO.setCreatedDt(curr);
        resChangeRecordDTO.setCreatedBy(resHpcCluster.getCreatedBy());
        resChangeRecordDTO.setUpdatedDt(curr);
        resChangeRecordDTO.setUpdatedBy(resHpcCluster.getCreatedBy());
        resChangeRecordDTO.setOrgSid(resHpcCluster.getOrgSid());
        resChangeRecordDTO.setOwnerId(resHpcCluster.getOwnerId());
        resChangeRecordDTO.setChangeStartTime(bizContract.getStartTime());
        String oldExtrajson = getExtraJson(nodeInfoList);
        resChangeRecordDTO.setOriginalExtra(oldExtrajson);
        resChangeRecordDTO.setNewType(String.valueOf(nodeNum));
        resChangeRecordDTO.setNewExtra(oldExtrajson);
        resChangeRecordDTO.setChangeEndTime(bizContract.getEndTime());
        resChangeRecordDTO.setUpdatedDt(curr);
        resChangeRecordDTO.setChangeType(DiscountScope.HPC_RENEW);
        return resChangeRecordDTO;
    }

    private List<NodeInfo> getNodeInfos(Long clusterId) {
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
        if(CollectionUtil.isNotEmpty(bmsInfoList)){
            nodeInfoList.addAll(bmsInfoList);
        }
        List<ResVmNodeInfo> resInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resInfoList)) {
            nodeInfoList.addAll(resInfoList);
        }
        return nodeInfoList;
    }

    /**
     * 变更资源json
     *
     * @param
     */
    private String getExtraJson(List<NodeInfo> nodeInfoList) {
        long vncNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HpcPointType.VNC.equals(resource.getHpcPointType()))
                .count();
        long cliNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HpcPointType.CCS_CLI.equals(resource.getHpcPointType()))
                .count();
        long computeNum = nodeInfoList.stream()
                .filter(resource -> "compute".equals(resource.getNodeType()))
                .count();
        Map<String, Long> nodeMap = new HashMap<>();
        nodeMap.put("计算节点数", computeNum);
        nodeMap.put("登录节点数", cliNum);
        nodeMap.put("VNC节点数", vncNum);
        return JSON.toJSONString(nodeMap);
    }

    private void insertAccountDeal(List<InstanceGaapCost> costs, ServiceOrder serviceOrder) {
        List<BizAccountDeal> deals = Lists.newArrayList();
        boolean releaseBeforeEndTime = releaseBeforeEndTime(serviceOrder);
        costs.forEach(cost -> {
            BizAccountDeal accountDeal = new BizAccountDeal();
            accountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
            if (serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) >= 0) {
                accountDeal.setType(DealType.OUT);
            } else {
                accountDeal.setType(DealType.IN);
            }
            BigDecimal tradePrice = cost.getPretaxAmount();
            accountDeal.setTradeType(
                    tradePrice.compareTo(BigDecimal.ZERO) < 0 ? TradeType.REFUND : TradeType.PAY);
            accountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
            accountDeal.setEnvType(cost.getCloudEnvType());
            accountDeal.setEnvName(cost.getCloudEnvName());
            accountDeal.setOrderNo(serviceOrder.getOrderSn());
            accountDeal.setBillNo(cost.getBillNo());
            accountDeal.setRemark(cost.getDescription());
            accountDeal.setBillingCycle(
                    LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
            BizBillingAccount account = BeanConvertUtil.convert(cost.getBizBillingAccount(), BizBillingAccount.class);
            accountDeal.setAccountSid(account.getId());
            accountDeal.setAccountName(account.getAccountName());
            accountDeal.setOrgSid(account.getOrgSid());
            accountDeal.setUserSid(account.getId());
            accountDeal.setDealTime(System.currentTimeMillis());
            accountDeal.setBalance(account.getBalance());
            accountDeal.setBalanceCredit(account.getCreditLine());
            accountDeal.setBalanceCash(account.getBalanceCash());
            accountDeal.setEntityId(serviceOrder.getEntityId());
            accountDeal.setEntityName(serviceOrder.getEntityName());
            String priceType = cost.getPriceType();
            String priceTypeDes = "";
            if (PriceType.RESOURCE.equals(priceType)) {
                priceTypeDes = "-资源费用";
            } else if (PriceType.SERVICE.equals(priceType)) {
                priceTypeDes = "-服务费用";
            } else if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                priceTypeDes = "-配置费用";
            }
            if (ProductCodeEnum.HPC_DRP.getProductName().equals(serviceOrder.getProductName())) {
                priceTypeDes += "\n 个数：" + cost.getQuantity();
                priceTypeDes += "\n 集群ID：" + serviceOrder.getClusterUuid();
            }
            accountDeal.setRemark(StrUtil.concat(true,
                    EnumUtil.likeValueOf(ProductCodeEnum.class, cost.getProductCode())
                            .getProductName(), priceTypeDes));
            WebUserUtil.prepareInsertParams(accountDeal, serviceOrder.getCreatedBy());
            BigDecimal couponAmount = cost.getCouponAmount();
            //优惠券抵扣为0元
            if (cost.getCouponDiscount() != null && cost.getCouponDiscount().compareTo(BigDecimal.ZERO) > 0
                    && cost.getPretaxAmount().compareTo(BigDecimal.ZERO) >= 0) {
                // 采用余额

                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setRemark(accountDeal.getRemark() + "(优惠券抵扣" + cost.getCouponDiscount().setScale(2,
                    RoundingMode.HALF_UP)
                    + "元)");
                bizAccountDeal.setTradeChannel(RechargeTypeEnum.COUPON.getCode());
                bizAccountDeal.setCouponAmount(cost.getCouponDiscount());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setBalanceCash(NumberUtil.add(account.getBalanceCash(), cost.getCouponAmount()));
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            //实际支付金额为0 并且非优惠券抵扣 当前不可能出现0元商品 后边可能会有
            if (cost.getPretaxAmount().compareTo(BigDecimal.ZERO) == 0 && (cost.getCouponDiscount() == null ||
                    cost.getCouponDiscount().compareTo(BigDecimal.ZERO) == 0)) {
                // 采用余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }

            if (NumberUtil.isGreater(couponAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(couponAmount, BigDecimal.ZERO)) {
                // 现金券余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? couponAmount.abs() : couponAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal cashAmount = cost.getCashAmount();
            if (NumberUtil.isGreater(cashAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(cashAmount, BigDecimal.ZERO)) {
                // 采用余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? cashAmount.abs() : cashAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal creditAmount = cost.getCreditAmount();
            if (NumberUtil.isGreater(creditAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(creditAmount, BigDecimal.ZERO)) {
                // 信用额度
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? creditAmount.abs() : creditAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
        });
        if (CollectionUtil.isNotEmpty(deals)) {
            for (BizAccountDeal deal : deals) {
                bizAccountDealMapper.insert(deal);
            }
        }
    }

    private boolean releaseBeforeEndTime(ServiceOrder serviceOrder) {
        boolean isRelease = OrderType.RELEASE.equals(serviceOrder.getType());
        if (!isRelease) {
            return false;
        }
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getOrderDetails());
        if (Objects.isNull(detail)) {
            return false;
        }
        Date originalEndTime = JSONUtil.parseObj(detail.getServiceConfig())
                .getDate("originalEndTime");
        if (Objects.isNull(originalEndTime) || Objects.isNull(detail.getEndTime())) {
            return false;
        }
        return detail.getEndTime().before(originalEndTime);
    }

    /**
     * 生成账单周期表数据
     *
     * @param costs
     */
    private void insertBillCycleInfo(List<InstanceGaapCost> costs) {
        costs.forEach(cost -> {
            BillBillingCycleCost cycleCost = new BillBillingCycleCost();
            cycleCost.setBillingCycle(cost.getBillingCycle());
            cycleCost.setBillNo(NoUtil.generateNo("BP"));
            cycleCost.setCloudEnvType(cost.getCloudEnvType());
            cycleCost.setCloudEnvName(cost.getCloudEnvName());
            cycleCost.setProductCode(cost.getProductCode());
            cycleCost.setProductName(cost.getProductName());
            cycleCost.setBillStartTime(cost.getUsageStartDate());
            cycleCost.setBillEndTime(cost.getUsageEndDate());
            cycleCost.setPayTime(cost.getPayTime());
            cycleCost.setOfficialAmount(cost.getPretaxGrossAmount());
            cycleCost.setDiscountAmount(cost.getPretaxAmount());
            cycleCost.setCashAmount(cost.getCashAmount());
            cycleCost.setCreditAmount(cost.getCreditAmount());
            cycleCost.setVoucherAmount(cost.getCouponAmount());
            cycleCost.setInvoiceAmount(cost.getCashAmount());
            if (cost.getUserAccountId() != null) {
                cycleCost.setOwnerId(cost.getUserAccountId());
            }
            cycleCost.setOrgId(cost.getOrgSid());
            cycleCost.setOrgName(cost.getOrgName());
            cycleCost.setPriceType(cost.getPriceType());
            cycleCost.setBillType(cost.getBillType());
            BigDecimal cashAmount = cost.getCashAmount() != null ? cost.getCashAmount() : new BigDecimal(0);
            BigDecimal creditAmount = cost.getCreditAmount() != null ? cost.getCreditAmount() : new BigDecimal(0);
            BigDecimal voucherAmount = cost.getCouponAmount() != null ? cost.getCouponAmount() : new BigDecimal(0);
            cycleCost.setAmount(
                    (cashAmount.add(creditAmount).add(voucherAmount)).setScale(3, BigDecimal.ROUND_HALF_UP));
            cycleCost.setCouponDiscount(cost.getCouponDiscount());
            cycleCost.setCreateDt(new Date());
            BillBillingCycleCost insert = mongoTemplate.insert(cycleCost);
            //保存拆分账单周期数据
            bizBillingCycleService.splitAndSaveCycleItem(cycleCost);
            //更新账单明细
            Query query = new Query();
            Update update = new Update();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(cost.getMongoId()));
            update.set("billBillingCycleId", insert.getId().toString());
            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");
            if (updateResult.getMatchedCount() > 0) {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------success");
            } else {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------Failed-----{}",
                        JSON.toJSONString(updateResult));
            }
        });

    }

    private void setUsedCost(BizBillingAccount bizBillingAccount, InstanceGaapCost cost,
                             BigDecimal finalCost) {
        cost.setCashAmount(BigDecimal.ZERO);
        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setCouponAmount(BigDecimal.ZERO);
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(bizBillingAccount.getBalanceCash(), BigDecimal.ZERO),
                    finalCost)) {
                cost.setCouponAmount(finalCost);
            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, bizBillingAccount.getBalanceCash());
                cost.setCouponAmount(bizBillingAccount.getBalanceCash());
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(bizBillingAccount.getBalance(), BigDecimal.ZERO),
                        overCost)) {
                    cost.setCashAmount(overCost);
                } else {
                    overCost = NumberUtil.sub(overCost, bizBillingAccount.getBalance());
                    cost.setCashAmount(bizBillingAccount.getBalance());
                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(bizBillingAccount.getCreditLine(), BigDecimal.ZERO),
                            overCost)) {
                        cost.setCreditAmount(overCost);
                    } else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_643345578));
                    }
                }
            }
        }
    }





    /**
     * 扩容详情
     * @param upgradeServiceRequest
     * @return
     */
    @Override
    public UpgradeDetailVO upgradeDetail(UpgradeServiceRequest upgradeServiceRequest) {

        AuthUser authUser = AuthUserHolder.getAuthUser();
        Long orgSid = authUser.getOrgSid();

        boolean isOperationAdmin = sysUserService.checkOperationAdmin(authUser.getUserSid());


        UpgradeDetailVO upgradeDetailVO = new UpgradeDetailVO();

        Long contractId = upgradeServiceRequest.getContractId();

        Integer cliNum = 0;
        Integer agentNum = 0;
        Integer afterUgradeAgentNum = 0;
        Integer afterUgradeVncNum = 0;
        Integer vncNum = 0;
        Integer month = 0;
        Long productResourceId = upgradeServiceRequest.getProductResourceId();
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(productResourceId);
        if (sfProductResource == null) {
            log.info("sfProductResource is empty");
            return upgradeDetailVO;
        }
        if(!isOperationAdmin && !sfProductResource.getOrgSid().equals(orgSid)){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        ServiceOrder serviceOrder = serviceOrderMapper.selectOne(new QueryWrapper<ServiceOrder>().eq("id", sfProductResource.getServiceOrderId()));
        //判断是否冻结
        List<BizBillingAccount> accounts = billingAccountMapper.findSelfAndSelfCustomer(
                authUser.getUserSid(), serviceOrder.getEntityId());
        if(accounts.size()>0){
            if(SysMNotifyConfigConstant.ExpireStrategyEnum.FREEZE.getValue().equals(accounts.get(0).getStatus())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1894653058));
            }
        }

        Date sfProductResourceEndTime = sfProductResource.getEndTime();
        upgradeDetailVO.setEndTime(sfProductResourceEndTime);
        Date current = new Date();
        if(!current.before(sfProductResourceEndTime)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_94040994));
        }

        Long clusterId = sfProductResource.getClusterId();
        BizContract bizContract = null;
        if (contractId != null) {
            bizContract = bizContractService.lambdaQuery().eq(BizContract::getContractId, contractId).one();
            BizContractDetail bizContractDetail = bizContractDetailService.lambdaQuery().eq(BizContractDetail::getContractSid, contractId).one();
            if (bizContractDetail == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_704044297));
            }
            cliNum = Optional.ofNullable(bizContractDetail.getCliNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getCliNumberRenew()).orElse(0);
            agentNum = Optional.ofNullable(bizContractDetail.getAgentNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getAgentNumberRenew()).orElse(0);
            afterUgradeAgentNum=agentNum;
            vncNum = Optional.ofNullable(bizContractDetail.getVncNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getVncNumberRenew()).orElse(0);
            month = Optional.ofNullable(bizContractDetail.getContractMonth()).orElse(0);

            Date startTime = bizContractDetail.getStartTime();
            Date bizContractEndTime = bizContractDetail.getEndTime();

            if (DiscountScope.HPC_RENEW.equals(bizContract.getDiscountScope()) && bizContractEndTime ==null) {
                assembleDate(bizContract, bizContractDetail, sfProductResourceEndTime);
            }
            //设置开始结束时间
            upgradeDetailVO.setStartime(bizContractDetail.getStartTime());
            upgradeDetailVO.setEndTime(sfProductResourceEndTime);
            Date endTime = bizContractDetail.getEndTime();
            if (endTime != null) {
                if (!endTime.after(sfProductResourceEndTime)) {
                    upgradeDetailVO.setEndTime(endTime);
                }else {
                    bizContractDetail.setEndTime(sfProductResourceEndTime);
                    bizContract.setEndTime(sfProductResourceEndTime);
                }
            }
            BigDecimal detailTotalPrice = bizContractDetailService.getContractDetailTotalPrice(bizContractDetail);
            upgradeDetailVO.setTradePrice(detailTotalPrice);
            upgradeDetailVO.setOriginalPrice(detailTotalPrice);


            List<ResVmNodeInfo> vmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
            long cliCount = vmNodeInfoList.stream().filter(node -> HPC_POINT_TYPE_CLI.equals(node.getHpcPointType())).count();
            long vncCount = vmNodeInfoList.stream().filter(node -> HPC_POINT_TYPE_VNC.equals(node.getHpcPointType())).count();
            long computeCount = vmNodeInfoList.stream().filter(node -> ClusterNodeType.COMPUTE.equals(node.getNodeType())).count();


            List<ResBmsNodeInfo> bmsNodeInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
            cliCount = bmsNodeInfoList.stream().filter(node -> HPC_POINT_TYPE_CLI.equals(node.getHpcPointType())).count() + cliCount;
            vncCount = bmsNodeInfoList.stream().filter(node -> HPC_POINT_TYPE_VNC.equals(node.getHpcPointType())).count() + vncCount;
            computeCount = bmsNodeInfoList.stream().filter(node -> ClusterNodeType.COMPUTE.equals(node.getNodeType())).count() + computeCount;
            afterUgradeAgentNum +=(int)computeCount;
            //扩容合同判断
            if (DiscountScope.HPC_OPEN.equals(bizContract.getDiscountScope())) {
                String resourcePoolType = bizContract.getResourcePoolType();
                CluseterSizeTypeEnum sizeTypeEnum = CluseterSizeTypeEnum.getByCode(resourcePoolType);
                if(sizeTypeEnum !=null){
                    // if (!sizeTypeEnum.isVaildComputeSize(afterUgradeAgentNum)) {
                    //     throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079119111));
                    // }
                    if (!sizeTypeEnum.isVaildCliSize((int)cliCount + cliNum)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1893187657));
                    }
                    afterUgradeVncNum = (int)vncCount + vncNum;
                    if (!sizeTypeEnum.isVaildVncSize(afterUgradeVncNum)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_173321838));
                    }
                }
            }
        }
        List<ProductInfoVO> productInfoVOList = new ArrayList<>();
        Long serviceOrderId = sfProductResource.getServiceOrderId();
        //如果是续订合同，取扩容合同的订单信息
        if(bizContract !=null && DiscountScope.HPC_RENEW.equals(bizContract.getDiscountScope())){
            Long contractSid = bizContract.getContractSid();
            QueryWrapper<ServiceOrder> tWrapper = new QueryWrapper<>();
            tWrapper.eq("contract_id", contractSid);
            tWrapper.eq("status", "completed");
            ServiceOrder upgradeServiceOrder = serviceOrderMapper.selectOne(tWrapper);
            serviceOrderId = upgradeServiceOrder.getId();

            if(!upgradeServiceOrder.getClusterId().equals(clusterId)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_934939659));
            }
        }

        QueryWrapper<ServiceOrderDetail> tWrapper = new QueryWrapper<>();
        tWrapper.eq("order_id", serviceOrderId);
        List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectList(tWrapper);

        //检查计算节点数
        this.checkMaxAgentNodeNum(serviceOrderDetails,afterUgradeAgentNum);

        //HPC申请单无VNC节点需要创建ProductInfo
        boolean needCreateVNCProductInfo = false;
        CurrentConfigDesc vncNodeConfigDesc = null;
        //循环修改节点数和日期
        for (ServiceOrderDetail serviceOrderDetail : serviceOrderDetails) {
            String serviceConfig = serviceOrderDetail.getServiceConfig();
            if (StringUtils.isEmpty(serviceConfig) || ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(serviceOrderDetail.getServiceType())) {
                continue;
            }
            ProductInfoVO productInfoVO = com.alibaba.fastjson.JSONObject.parseObject(serviceConfig, ProductInfoVO.class);
            String productConfigDesc = serviceOrderDetail.getProductConfigDesc();
            //HPC申请单无VNC节点需要创建VNC节点
            boolean needCreateVNCNode = false;

            if (ProductCodeEnum.HPC_DRP.getProductType().equals(productInfoVO.getProductCode())) {
                if (!StringUtils.containsIgnoreCase(productConfigDesc, VNC_NODE_INFO) && vncNum > 0) {
                    needCreateVNCNode = true;
                    needCreateVNCProductInfo = true;
                    //从产品模板中，获取VNC节点信息
                    vncNodeConfigDesc = this.templateToVNCNodeConfigDesc(serviceOrderDetail.getServiceId());
                }else if (ApplyTypeEnum.HPC_DRP_STANDARD.getType().equalsIgnoreCase(serviceOrderDetail.getApplyType())) {
                    needCreateVNCProductInfo = true;
                    vncNodeConfigDesc = this.templateToVNCNodeConfigDesc(serviceOrderDetail.getServiceId());
                }
            } else if (StringUtils.containsIgnoreCase(productConfigDesc, COMPUTE_NODE_INFO) && agentNum > 0) {
                //检查计算节点配额
                Long cloudEnvId = productInfoVO.getCloudEnvId();
                String shareTypeId = productInfoVO.getShareTypeId();
                String productCategory = productInfoVO.getProductCategory();
                String productCode = productInfoVO.getProductCode();
                Map<String, Object> nodeInfoMap =
                        resVmTypeRemoteService.getNodeInfo(shareTypeId, productCategory, productCode, Long.valueOf(agentNum), cloudEnvId);
                Long freeCount = (Long) nodeInfoMap.get("freeCount");
                if (freeCount <= 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_118120701) + productCategory + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_485561678) + shareTypeId + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2929));
                }
            }

            JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
            JSONArray newJsonArray = new JSONArray();
            productInfoVO.setPeriod(BigDecimal.valueOf(month));

            if (CollectionUtil.isNotEmpty(jsonArray)) {
                int arrSize = jsonArray.size();
                boolean nodeInfo = false;
                for (Object jsonO : jsonArray) {
                    arrSize --;
                    com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                    //修改计算节点信息
                    if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), COMPUTE_NODE_INFO) && agentNum>0) {
                        productInfoVO.setAmount(agentNum);
                        setAttr(agentNum, newJsonArray, jsonObject);
                        nodeInfo = true;
                        continue;
                    }
                    //修改登录节点信息
                    if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), LOGIN_NODE_INFO) && cliNum>0) {
                        productInfoVO.setAmount(cliNum);
                        setAttr(cliNum, newJsonArray, jsonObject);
                        nodeInfo =true;
                        continue;
                    }

                    //修改时间
                    if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), TIME)) {
                        CurrentConfigDescStr currentConfigDescStr = null;
                        try {
                            currentConfigDescStr = JSON.parseObject(jsonObject.toJSONString(), CurrentConfigDescStr.class);
                            currentConfigDescStr.setValue(month.toString());
                            currentConfigDescStr.setLabel("扩容时长");
                            DescDTO desc = currentConfigDescStr.getDesc();
                            desc.setValue(month + "个月");
                            desc.setLabel("扩容时长");
                            newJsonArray.add(JSON.toJSON(currentConfigDescStr));
                        } catch (Exception e) {
                            System.out.println(jsonObject.toJSONString());
                            log.info("json解析异常");
                            newJsonArray.add(jsonO);
                        }
                        continue;
                    }
                    //修改VNC节点信息
                    if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), VNC_NODE_INFO)) {
                        //标准专属资源池重新设置
                        if(vncNum>0 && !isHpcDrpStandardEcs(serviceOrderDetail)){
                            productInfoVO.setAmount(vncNum);
                            setAttr(vncNum, newJsonArray, jsonObject);
                        }
                        continue;
                    } else if (needCreateVNCNode && (nodeInfo || arrSize == 0)) {
                        setConfigAttr(vncNum, newJsonArray, vncNodeConfigDesc);
                        needCreateVNCNode = false;
                    }
                    // 开启/关闭VNC
                    if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), VNC_NODE)) {
                        String value = "";
                        if (contractId != null) {
                            value = vncNum > 0 ? "开启" : "关闭";
                        } else {
                            List<ResVmNodeInfo> vmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(
                                    clusterId);
                            long vncCount = vmNodeInfoList.stream()
                                                          .filter(node -> HPC_POINT_TYPE_VNC.equals(
                                                                  node.getHpcPointType()))
                                                          .count();
                            List<ResBmsNodeInfo> bmsNodeInfoList = resBmsRemoteService.getNodeInfoListByClusterId(
                                    clusterId);
                            vncCount = bmsNodeInfoList.stream()
                                                      .filter(node -> HPC_POINT_TYPE_VNC.equals(node.getHpcPointType()))
                                                      .count() + vncCount;
                            value = vncCount > 0 ? "开启" : "关闭";
                        }
                        jsonObject.put("value", value);
                        com.alibaba.fastjson.JSONObject desc = jsonObject.getJSONObject("desc");
                        desc.put("value", value);
                    }
                    //排除管理节点
                    if (this.skipAdd(jsonObject)) {
                        newJsonArray.add(jsonObject);
                    }
                }

                if (ProductCodeEnum.HPC_DRP.getProductType().equals(productInfoVO.getProductCode())) {
                    productInfoVO.setAmount(0);
                }


                productInfoVO.getProductConfigDesc().setCurrentConfigDesc(
                        newJsonArray.toString()
                );
                //排除管理节点
                if (newJsonArray.size() > 0) {
                    productInfoVOList.add(productInfoVO);
                }
                serviceOrderDetail.setServiceConfig(JSON.toJSONString(productInfoVO));
                serviceOrderDetail.setProductConfigDesc(newJsonArray.toString());
            }
        }
        //创建proudctInfo
        if (needCreateVNCProductInfo) {
            ProductInfoVO vncProductInfo = createVNCProductInfo(serviceOrderDetails, vncNodeConfigDesc);
            if (vncProductInfo != null) {
                vncProductInfo.setPeriod(BigDecimal.valueOf(month));
                productInfoVOList.add(vncProductInfo);
            }
        }
        upgradeDetailVO.setProductInfoVOList(productInfoVOList);
        Optional<ServiceOrderDetail> orderDetailOptional = serviceOrderDetails.stream().findFirst();
        if (orderDetailOptional.isPresent()) {
            ServiceOrderDetail serviceOrderDetail = orderDetailOptional.get();
            upgradeDetailVO.setProductConfigDesc(serviceOrderDetail.getProductConfigDesc());
        }
        return upgradeDetailVO;
    }

    private boolean isHpcDrpStandardEcs(ServiceOrderDetail serviceOrderDetail) {
        return ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(serviceOrderDetail.getServiceType()) && ApplyTypeEnum.HPC_DRP_STANDARD.getType().equalsIgnoreCase(serviceOrderDetail.getApplyType());
    }

    /**
     * 判断计算节点是否超过配置的最大值
     * @param serviceOrderDetails
     * @param afterUgradeAgentNum
     */
    private void checkMaxAgentNodeNum(List<ServiceOrderDetail> serviceOrderDetails, Integer afterUgradeAgentNum) {
        if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {
            String productConfigDesc = serviceOrderDetails.stream().filter(orderDetail ->
                    org.apache.commons.lang3.StringUtils.equalsIgnoreCase(orderDetail.getServiceType(), cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.HPC_DRP.getProductCode()))
                    .map(ServiceOrderDetail::getProductConfigDesc)
                    .findFirst().orElse("");

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(productConfigDesc)) {
                JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
                boolean noConfigMaxComputeNodeNum = true;
                for (Object jsonO : jsonArray) {
                    com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                    if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                            ServiceConfigArrKey.MAX_COMPUTE_NODE_NUM)) {
                        Integer configMaxComputeNodeNum = jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE);
                        if(configMaxComputeNodeNum !=null && afterUgradeAgentNum !=null && configMaxComputeNodeNum<afterUgradeAgentNum){
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079119111));
                        }
                        noConfigMaxComputeNodeNum =false;
                        break;
                    }
                }
                //不存在配置 默认小规模节点数
                if (noConfigMaxComputeNodeNum) {
                    boolean isVaild = CluseterSizeTypeEnum.SMALL.isVaildCliSize(afterUgradeAgentNum);
                    if(!isVaild){
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079119111));
                    }

                }
            }
        }
    }


    private void assembleDate(BizContract bizContract, BizContractDetail bizContractDetail, Date sfProductResourceEndTime) {
        Date bizContractEndTime;
        Date startTime;
        BizContract lastRenewContract = bizContractService.getLastRenewContract(bizContract.getContractSid());

        startTime = lastRenewContract.getEndTime();
        if(!startTime.before(sfProductResourceEndTime)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_662690366));
        }
        bizContract.setStartTime(startTime);
        bizContractDetail.setStartTime(startTime);
        bizContractEndTime = DateUtils.addMonths(startTime, bizContractDetail.getContractMonth());

        bizContract.setEndTime(bizContractEndTime);
        bizContractDetail.setEndTime(bizContractEndTime);
    }

    private boolean skipAdd(com.alibaba.fastjson.JSONObject jsonObject) {
        boolean b = !Objects.equals(jsonObject.get(JSON_ATTR_KEY), MANAGEMENT_NODE_INFO) &&
                !Objects.equals(jsonObject.get(JSON_ATTR_KEY), ORG) &&
                !Objects.equals(jsonObject.get(JSON_ATTR_KEY), VNC_NODE_INFO) &&
                !Objects.equals(jsonObject.get(JSON_ATTR_KEY), COMPUTE_NODE_INFO);
        return b && !Objects.equals(jsonObject.get(JSON_ATTR_KEY), LOGIN_NODE_INFO) &&
                !Objects.equals(jsonObject.get(JSON_ATTR_KEY), STORAGE_INFO) &&
                !Objects.equals(jsonObject.get(JSON_ATTR_KEY), AGREE_VPC);
    }

    /**
     * 复制一个Detail的信息
     * @param serviceOrderDetails
     * @param vncNodeConfigDesc
     * @return
     */
    private ProductInfoVO createVNCProductInfo(List<ServiceOrderDetail> serviceOrderDetails, CurrentConfigDesc vncNodeConfigDesc) {
        if(vncNodeConfigDesc !=null ){
            List<NodeValueDTO> value = vncNodeConfigDesc.getValue();
            if (CollectionUtil.isNotEmpty(value)) {
                NodeValueDTO firstNodeValue = CollectionUtil.getFirst(value);
                Optional<ServiceOrderDetail> detailOptional = serviceOrderDetails.stream()
                        .filter(serviceOrderDetail -> serviceOrderDetail.getServiceType().equalsIgnoreCase(firstNodeValue.getResourceType()))
                        .findFirst();
                if (detailOptional.isPresent()) {
                    ServiceOrderDetail serviceOrderDetail = detailOptional.get();
                    String serviceConfig = serviceOrderDetail.getServiceConfig();
                    if (StringUtils.isNotEmpty(serviceConfig)) {
                        ProductInfoVO productInfoVO = com.alibaba.fastjson.JSONObject.parseObject(serviceConfig, ProductInfoVO.class);
                        Integer nodeNum = firstNodeValue.getNodeNum();
                        productInfoVO.setAmount(nodeNum);
                        productInfoVO.setProductCategory(firstNodeValue.getFlavorRef());
                        ProductConfigDesc productConfigDesc = new ProductConfigDesc();
                        productConfigDesc.setCurrentConfigDesc(JSON.toJSONString(Arrays.asList(vncNodeConfigDesc)));
                        productInfoVO.setProductConfigDesc(productConfigDesc);
                        return productInfoVO;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 产品模板获取VNC节点信息
     * @return
     */
    public CurrentConfigDesc templateToVNCNodeConfigDesc(Long serviceId) {
        String key_panelDataList = "panelDataList";
        String key_options = "options";
        String key_groupKey = "groupKey";
        String key_isParentNode = "isParentNode";
        String key_label = "label";
        String key_defaultValue = "defaultValue";
        String key_attrKey = "attrKey";
        CurrentConfigDesc currentConfigDesc = null;

        log.debug("HpcDrpOrderServiceImpl.templateToVNCNodeConfigDesc:serviceId={}",serviceId);
        QueryWrapper<SfProductTemplateRelation> tWrapper = new QueryWrapper<>();
        tWrapper.eq("product_id",serviceId);
        SfProductTemplateRelation sfProductTemplateRelation = sfProductTemplateRelationMapper.selectOne(tWrapper);
        if(sfProductTemplateRelation == null){
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_445890786),serviceId));
        }


        //获取产品模板
        QueryWrapper<SfProductTemplate> templateQueryWrapper = new QueryWrapper<>();
        templateQueryWrapper.eq("id",sfProductTemplateRelation.getTemplateId());
        templateQueryWrapper.like("template_content",VNC_NODE_INFO);
        SfProductTemplate productTemplate = sfProductTemplateMapper.selectOne(templateQueryWrapper);
        if(productTemplate !=null){
            currentConfigDesc = new CurrentConfigDesc();
            currentConfigDesc.setIsHidden(false);

            //页面展示部分元素
            List<List<ChidrenDescDTO>> chidrenDesc = new ArrayList<>();
            List<ChidrenDescDTO> chidrenDescDTOList = new ArrayList<>();
            chidrenDesc.add(chidrenDescDTOList);
            List<NodeValueDTO> value = new ArrayList<>();
            NodeValueDTO nodeValueDTO = new NodeValueDTO();
            value.add(nodeValueDTO);
            currentConfigDesc.setChidrenDesc(chidrenDesc);
            currentConfigDesc.setValue(value);

            String templateContent = productTemplate.getTemplateContent();
            if (StringUtils.isNotEmpty(templateContent)) {
                com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(templateContent);
                JSONArray panelDataList = jsonObject.getJSONArray(key_panelDataList);
                //过滤模板中vncNode节点信息
                List<Object> vncNodeObjList = panelDataList.stream().filter(data -> {
                    //获取VNC节点相关的配置项
                    com.alibaba.fastjson.JSONObject dataJO = (com.alibaba.fastjson.JSONObject) data;
                    if (dataJO == null) {
                        return false;
                    }
                    com.alibaba.fastjson.JSONObject optionJO = dataJO.getJSONObject(key_options);
                    if (optionJO == null) {
                        return false;
                    }

                    String groupKey = optionJO.getString(key_groupKey);
                    if (StringUtils.containsIgnoreCase(groupKey, VNC_NODE_INFO)) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                //设置节点信息
                for (Object data : vncNodeObjList) {
                    com.alibaba.fastjson.JSONObject dataJO = (com.alibaba.fastjson.JSONObject) data;
                    com.alibaba.fastjson.JSONObject optionJO = dataJO.getJSONObject(key_options);

                    Boolean isParentNode = optionJO.getBoolean(key_isParentNode);
                    if (Boolean.TRUE.equals(isParentNode)) {
                        currentConfigDesc.setLabel(optionJO.getString(key_label));
                        currentConfigDesc.setAttrKey(optionJO.getString(key_groupKey));
                    }else{
                        //设置页面展示数据
                        ChidrenDescDTO chidrenDescDTO = new ChidrenDescDTO();
                        chidrenDescDTO.setLabel(optionJO.getString(key_label));
                        chidrenDescDTO.setValue(optionJO.getString(key_defaultValue));

                        String attrKey = optionJO.getString(key_attrKey);
                        if(StringUtils.startsWith(attrKey,"flavorRef") || StringUtils.startsWith(attrKey,"nodeNum")){
                            chidrenDescDTO.setIsShow(true);
                        }else {
                            chidrenDescDTO.setIsShow(false);
                        }
                        //设置节点规格等值
                        Field[] fields = nodeValueDTO.getClass().getDeclaredFields();
                        try {
                            for (Field field : fields) {
                                ReflectionUtils.makeAccessible(field);
                                if(StringUtils.startsWithIgnoreCase(attrKey,field.getName())){
                                    field.set(nodeValueDTO,optionJO.getObject(key_defaultValue,field.getType()));
                                    break;
                                }
                            }
                        } catch (IllegalAccessException e) {
                            log.error("字段设置值异常");
                        }
                        chidrenDescDTOList.add(chidrenDescDTO);
                    }
                }
            }
        }else{
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1496829557),serviceId));
        }
        log.debug("HpcDrpOrderServiceImpl.templateToVNCNodeConfigDesc return currentConfigDesc={}",currentConfigDesc);
        return currentConfigDesc;
    }

    private void setAttr(Integer vncNum, JSONArray newJsonArray, com.alibaba.fastjson.JSONObject jsonObject) {
        CurrentConfigDesc currentConfigDesc = null;
        try {
            currentConfigDesc = JSON.parseObject(jsonObject.toJSONString(), CurrentConfigDesc.class);
            setConfigAttr(vncNum, newJsonArray, currentConfigDesc);
        } catch (Exception e) {
            System.out.println(jsonObject.toJSONString());
            log.info("json解析异常");
            newJsonArray.add(jsonObject);
        }
    }

    private void setConfigAttr(Integer vncNum, JSONArray newJsonArray, CurrentConfigDesc currentConfigDesc) {
        changeNodeNum(vncNum, currentConfigDesc);
        newJsonArray.add(JSON.toJSON(currentConfigDesc));
    }

    /**
     * 修改节点数
     * @param agentNum
     * @param currentConfigDesc
     */
    private void changeNodeNum(Integer agentNum, CurrentConfigDesc currentConfigDesc) {
        List<NodeValueDTO> nodeValueDTOList = currentConfigDesc.getValue();
        if (CollectionUtil.isNotEmpty(nodeValueDTOList)) {
            for (NodeValueDTO nodeValueDTO : nodeValueDTOList) {
                nodeValueDTO.setNodeNum(agentNum);
            }
        }
        List<List<ChidrenDescDTO>> chidrenDesc = currentConfigDesc.getChidrenDesc();
        for (List<ChidrenDescDTO> chidrenDescDTOS : chidrenDesc) {
            for (ChidrenDescDTO chidrenDescDTO : chidrenDescDTOS) {
                if("资源规格".equals(chidrenDescDTO.getLabel())){
                    String value = chidrenDescDTO.getValue();
                    QueryResVmTypeByParamsRequest resVmTypeByParamsRequest =
                            new QueryResVmTypeByParamsRequest();
                    resVmTypeByParamsRequest.setUuid(value);
                    ResVmType resVmType = CollectionUtil.getFirst(resVmTypeRemoteService.selectByParams(resVmTypeByParamsRequest));

                    if(resVmType !=null){
                        StringBuffer sb = new StringBuffer();
                        if(resVmType.getCpu() !=null){
                            sb.append(resVmType.getCpu()).append("vCPU");
                        }
                        if (resVmType.getRam() !=null) {
                            sb.append("、").append(Float.valueOf(resVmType.getRam()/1024).intValue()).append("GiB内存");
                        }
                        chidrenDescDTO.setValue(sb.toString());
                    }
                }
                boolean b = !"节点类型".equals(chidrenDescDTO.getLabel())
                        && !"登录密码".equals(chidrenDescDTO.getLabel())
                        && !"资源类型".equals(chidrenDescDTO.getLabel());
                if(b && !"磁盘类型".equals(chidrenDescDTO.getLabel()) && !"磁盘大小".equals(chidrenDescDTO.getLabel())){
                    chidrenDescDTO.setIsShow(true);
                }
                if("节点数目".equals(chidrenDescDTO.getLabel())){
                    chidrenDescDTO.setValue(agentNum.toString());
                }
            }
        }
    }

    /**
     * 创建订单、订单详情、订单价格详情
     * @param serviceRequest
     * @param modifyEntity
     */
    @Transactional
    public void buildUpgradeOrder(ApplyServiceRequest serviceRequest, HpcDrpUpgradeEntity modifyEntity) {
        //获取申请订单
        ServiceOrder applyOrder = modifyEntity.getOrder();
        BizContract bizContract = modifyEntity.getContract();
        Boolean payStatus = bizContract.getPayStatus();


        BigDecimal tradePrice = modifyEntity.getTradePrice();
        modifyEntity.setOncePrice(BigDecimal.ZERO);
        modifyEntity.setDiscountPrice(BigDecimal.ZERO);

        ServiceOrder serviceOrder = BeanConvertUtil.convert(applyOrder, ServiceOrder.class);
        IsNonBillProductRequest request = new IsNonBillProductRequest();

        String serviceId = applyOrder.getServiceId();
        if(StringUtils.isEmpty(serviceId)){
            QueryWrapper<ServiceOrderDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("order_id",applyOrder.getId());
            queryWrapper.eq("service_type",ProductCodeEnum.HPC_DRP.getProductType());
            List<ServiceOrderDetail> orderDetails = serviceOrderDetailMapper.selectList(queryWrapper);
            if(CollectionUtil.isNotEmpty(orderDetails)){
                ServiceOrderDetail serviceOrderDetail = orderDetails.get(0);
                request.setSfServiceId(serviceOrderDetail.getServiceId());
            }
        }else{
            request.setSfServiceId(Long.valueOf(serviceId));
        }
        request.setChargeType(modifyEntity.getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        if (isNonBillProduct) {
            log.info("不计费产品，实际金额赋值为0");
            serviceOrder.setFinalCost(BigDecimal.ZERO);
            serviceOrder.setCurrAmount(BigDecimal.ZERO);
            serviceOrder.setChargingType(SALE_TYPE);
            payStatus = true;
        } else {
            serviceOrder.setChargingType(NORMAL_TYPE);
        }
        // 订单、服务实例名称追加时间戳，区分重名
        serviceOrder.setId(null);
        serviceOrder.setStepName(null);
        serviceOrder.setType(serviceRequest.getOrderType());
        serviceOrder.setProcessFlag("01");
        serviceOrder.setPayTime(new Date());
        serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
        serviceOrder.setStatus(OrderStatus.PENDING);

        serviceOrder.setFinalCost(getFinalCost(payStatus, tradePrice));
        serviceOrder.setCurrAmount(getFinalCost(payStatus, tradePrice));
        serviceOrder.setOriginalCost(tradePrice);
        serviceOrder.setOrgDiscount(BigDecimal.ZERO);

        serviceOrder.setCouponDiscount(BigDecimal.ZERO);
        serviceOrder.setBizBillingAccountId(modifyEntity.getAccount().getId());

        WebUserUtil.prepareInsertParams(serviceOrder, modifyEntity.getAuthUser().getAccount());
        serviceOrder.setAccountName(modifyEntity.getAccount().getAccountName());
        // 结算类型
        if (Objects.nonNull(serviceRequest.getContractId())) {
            serviceOrder.setSettlementType("合同价");
            // 合同ID
            serviceOrder.setContractId(serviceRequest.getContractId());
        } else {
            serviceOrder.setSettlementType("标准价");
        }
        // 代客下单管理员ID
        if (Objects.nonNull(serviceRequest.getBehalfUserSid())) {
            serviceOrder.setBehalfUserSid(serviceRequest.getBehalfUserSid());
        }
        this.serviceOrderMapper.insert(serviceOrder);
        modifyEntity.setOrder(serviceOrder);

        BizContract contract = bizContract;
        //
        List<BizContractDetail> contractDetails = contract.getContractDetails();
        Optional<BizContractDetail> optional = contractDetails.stream().findFirst();
        BizContractDetail bizContractDetail = new BizContractDetail();
        if (optional.isPresent()) {
            bizContractDetail = optional.get();
        }
        List<ServiceOrderPriceDetail> orderPriceDetails = Lists.newArrayList();
        for (ProductInfoVO productInfo : serviceRequest.getProductInfo()) {
            String productCode = productInfo.getProductCode();
            //扩容内置弹性文件不需要生成订单
            if(ProductCodeEnum.SFS2.getProductType().equals(productCode)){
                continue;
            }
            ServiceOrderDetail orderDetail = new ServiceOrderDetail();
            orderDetail.setOrderId(serviceOrder.getId());
            orderDetail.setServiceId(productInfo.getServiceId());
            orderDetail.setChargeType(productInfo.getChargeType());
            orderDetail.setServiceConfig(JSON.toJSONString(productInfo));

            orderDetail.setVersion(1L);
            orderDetail.setOrgSid(serviceOrder.getOrgSid());
            orderDetail.setServiceType(productCode);
            orderDetail.setQuantity(productInfo.getAmount());
            BigDecimal period = productInfo.getPeriod();
            orderDetail.setDuration(Optional.ofNullable(period).orElse(BigDecimal.ONE).intValue());
            orderDetail.setStartTime(bizContractDetail.getStartTime());

            String currentConfigDesc = productInfo.getProductConfigDesc().getCurrentConfigDesc();
            orderDetail.setProductConfigDesc(currentConfigDesc);

            Integer contractMonth = bizContractDetail.getContractMonth();
            //设置金额
            if(StringUtils.containsIgnoreCase(productCode,ProductCodeEnum.HPC_DRP.getProductType())) {
                orderDetail.setPrice(BigDecimal.ZERO);
                orderDetail.setAmount(BigDecimal.ZERO);
                orderDetail.setQuantity(0);
                orderDetail.setOriginalCost(BigDecimal.ZERO);
            }else if(StringUtils.containsIgnoreCase(currentConfigDesc,COMPUTE_NODE_INFO)){
                Integer agentNum = Optional.ofNullable(bizContractDetail.getAgentNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getAgentNumberRenew()).orElse(0);

                BigDecimal agentPrice = Optional.ofNullable(bizContractDetail.getAgentPrice()).orElse(BigDecimal.ZERO);
                orderDetail.setPrice(agentPrice);
                BigDecimal amount = NumberUtil.mul(agentPrice, agentNum, contractMonth);
                orderDetail.setAmount(getFinalCost(payStatus, amount));
                orderDetail.setOriginalCost(amount);
                orderDetail.setQuantity(agentNum);
            }else if(StringUtils.containsIgnoreCase(currentConfigDesc,LOGIN_NODE_INFO)){
                Integer cliNum = Optional.ofNullable(bizContractDetail.getCliNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getCliNumberRenew()).orElse(0);

                BigDecimal cliPrice = Optional.ofNullable(bizContractDetail.getCliPrice()).orElse(BigDecimal.ZERO);
                orderDetail.setPrice(cliPrice);
                BigDecimal amount = NumberUtil.mul(cliPrice, cliNum, contractMonth);
                orderDetail.setAmount(getFinalCost(payStatus, amount));
                orderDetail.setOriginalCost(amount);
                orderDetail.setQuantity(cliNum);
            }else if(StringUtils.containsIgnoreCase(currentConfigDesc,VNC_NODE_INFO)){
                Integer vncNum = Optional.ofNullable(bizContractDetail.getVncNumberOpen()).orElse(0)-Optional.ofNullable(bizContractDetail.getVncNumberRenew()).orElse(0);

                BigDecimal vncPrice = Optional.ofNullable(bizContractDetail.getVncPrice()).orElse(BigDecimal.ZERO);
                orderDetail.setPrice(vncPrice);
                BigDecimal amount = NumberUtil.mul(vncPrice, vncNum, contractMonth);
                orderDetail.setAmount(getFinalCost(payStatus, amount));
                orderDetail.setOriginalCost(amount);
                orderDetail.setQuantity(vncNum);
            }else{
                orderDetail.setPrice(BigDecimal.ZERO);
                orderDetail.setAmount(BigDecimal.ZERO);
                orderDetail.setOriginalCost(BigDecimal.ZERO);
            }


            orderDetail.setVersion(1L);
            orderDetail.setOrgSid(serviceOrder.getOrgSid());

            // orderDetail.setStartTime(Calendar.getInstance().getTime());
            orderDetail.setDiscountRatio(BigDecimal.ONE);
            orderDetail.setFloatingRatio(BigDecimal.ONE);

            // Integer duration = Convert.toInt(orderDetail.getDuration(), 0);
            // if (orderDetail.getDuration() != null && duration > 0) {
            //     orderDetail.setEndTime(DateUtil.offsetMonth(orderDetail.getStartTime(), duration));
            // }
            // if (ProductCodeEnum.isInnerProduct(productInfo.getProductCode())) {
            //     orderDetail.setStartTime(null);
            orderDetail.setEndTime(bizContractDetail.getEndTime());
            // }
            //新增一个申请类型
            orderDetail.setApplyType(productInfo.getApplyType());
            serviceOrderDetailMapper.insert(orderDetail);

            serviceOrder.getOrderDetails().add(orderDetail);

            modifyEntity.setPriceDetails(orderPriceDetails);
            makeUpgradeOrderPriceDetail(serviceOrder, orderDetail, modifyEntity);

        }
        modifyEntity.setOrderDetails(serviceOrder.getOrderDetails());
        orderActionLog(serviceOrder);

    }

    private BigDecimal getFinalCost(Boolean payStatus, BigDecimal tradePrice) {
        return Boolean.FALSE.equals(payStatus) ? tradePrice : BigDecimal.ZERO;
    }

    private void makeUpgradeOrderPriceDetail(ServiceOrder serviceOrder, ServiceOrderDetail orderDetail, HpcDrpUpgradeEntity hpcDrpUpgradeEntity) {

        ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
        priceDetail.setType(serviceOrder.getType());
        priceDetail.setChargeType(orderDetail.getChargeType());
        priceDetail.setOrderSn(serviceOrder.getOrderSn());
        priceDetail.setOrgSid(serviceOrder.getOrgSid());
        priceDetail.setOrderDetailId(orderDetail.getId());
        priceDetail.setVersion(1L);

        priceDetail.setCouponAmount(hpcDrpUpgradeEntity.getBizCoupon() == null ?
                BigDecimal.ZERO : hpcDrpUpgradeEntity.getBizCoupon().getDiscountAmount());


        String serviceConfig = orderDetail.getServiceConfig();
        if (StringUtils.isNotEmpty(serviceConfig)) {
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(serviceConfig);
            if(jsonObject !=null){
                String productCategory = jsonObject.getString("productCategory");
                priceDetail.setServiceType(productCategory);
                priceDetail.setBillingSpec(productCategory);
            }
        }

        String serviceType = orderDetail.getServiceType();
        priceDetail.setProductCode(serviceType);
        priceDetail.setQuantity(orderDetail.getQuantity());
        priceDetail.setPriceType("resource");
        priceDetail.setRefInstanceId(JSONArray.toJSONString(hpcDrpUpgradeEntity.getResourceId()));

        priceDetail.setPriceDesc(ProductCodeEnum.toDesc(serviceType));

        priceDetail.setPrice(orderDetail.getPrice());
        priceDetail.setAmount(orderDetail.getAmount());
        priceDetail.setOriginalCost(orderDetail.getOriginalCost());
        priceDetail.setFixedHourPrice(BigDecimal.ZERO);
        priceDetail.setUnitHourPrice(BigDecimal.ZERO);
        priceDetail.setTradeFixedHourPrice(BigDecimal.ZERO);
        priceDetail.setTradeUnitHourPrice(BigDecimal.ZERO);
        priceDetail.setFixedMonth(BooleanEnum.NO.getCode());
        priceDetail.setPayBalance(BigDecimal.ZERO);
        priceDetail.setPayBalanceCash(BigDecimal.ZERO);
        priceDetail.setPayCreditLine(BigDecimal.ZERO);
        priceDetail.setStartTime(orderDetail.getStartTime());
        priceDetail.setEndTime(orderDetail.getEndTime());

        // 批量新增订单价格明细
        serviceOrderPriceDetailService.save(priceDetail);

        hpcDrpUpgradeEntity.getPriceDetails().add(priceDetail);

    }



    private void orderActionLog(ServiceOrder serviceOrder){
        StringBuffer actionName = new StringBuffer();
        if (OrderType.UPGRADE_RENEW.equals(serviceOrder.getType())) {
            actionName.append("扩容续订");
        }else if (OrderType.UPGRADE.equals(serviceOrder.getType())) {
            actionName.append("扩容");
        }
        actionName.append("["+serviceOrder.getName()+"]");
        actionName.append("支付金额￥");
        actionName.append(serviceOrder.getFinalCost().setScale(2, BigDecimal.ROUND_HALF_UP));
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil.getAuthUserInfo();
//        log.info("当前用户信息:{}",JSONObject.toJSONString(authUserInfo));
        HttpServletRequest request = SpringContextHolder.getHttpServletRequest();
        String userClient = request.getHeader("User-Agent");
        String remoteIp = IPAddressUtil.getRemoteHostIp(request);
        mongoTemplate.insert(ActionLog.builder()
                        .actionName(actionName.toString())
                        .account(authUserInfo.getAccount())
                        .actionPath(request.getRequestURI())
                        .actionTime(new Date())
                        .httpMethod(request.getMethod())
                        .lbIp(request.getRemoteAddr())
                        .remoteIp(remoteIp)
                        .roleName("客户管理员(内置)")
                        .client(userClient).build()
                , "action_log");
    }

    /**
     * 校验预部署弹性文件
     * @param productInfo 产品
     */
    private void checkSfsConfig(List<ProductInfoVO> productInfo) {
        List<ProductInfoVO> productInfoVOs = productInfo.stream()
                .filter(infoVO -> ProductCodeEnum.SFS2.getProductType()
                        .equals(infoVO.getProductCode()))
                .collect(
                        Collectors.toList());
        //获取弹性文件的大小
        Integer size=0;
        List<Long> sizeList=new ArrayList<>();
        for(ProductInfoVO productInfoVO:productInfoVOs){
            ResShare resShare = BeanConvertUtil.convert(
                    JSONUtil.parseObj(productInfoVO.getData()).getStr(productInfoVO.getProductCode()),
                    ResShare.class);
            size+=resShare.getSize();
            sizeList.add(Long.valueOf(resShare.getSize()));
        }
        //内置弹性文件校验
     //   iShareService.checkPreShareConfig(Long.valueOf(size),sizeList);

    }

}
