/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.notice.mapper;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.entity.SysMNotice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface SysMNoticeMapper extends BaseMapper<SysMNotice> {

    List<SysMNotice> selectList(Criteria criteria);

    /**
     * 查询已发布的公告
     * @param usrSid
     * @param publishEndDate
     * @return
     */
    List<SysMNotice> selectNoticeList(@Param("usrSid") Long usrSid ,@Param("publishEndDate") Date publishEndDate);

}
