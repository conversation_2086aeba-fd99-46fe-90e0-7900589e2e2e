/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpgradeServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.UpgradeDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <AUTHOR>
 * @date 2020/6/16.
 */
public interface BaseOrderService {

    /**
     * 产品购买前
     * @return authUserInfo
     */
    default ApplyEntity before() {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        return ApplyEntity.builder().authUser(authUserInfo).build();
    }

    /**
     * 产品规格校验
     * @param serviceVO 请求参数
     */
    default void validateSpec(ApplyServiceVO serviceVO) {
    }

    /**
     * 购买
     * @param serviceVO 请求参数
     * @return 资源ID
     */
    default String apply(ApplyServiceVO serviceVO) {
        return "";
    }

    /**
     * 购买后
     */
    default String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {

        return "";
    }

    /**
     * 升级
     * @param serviceRequest 请求参数
     * @return 资源ID
     */
    default String upgrade(ApplyServiceRequest serviceRequest) {
        return "";
    }

    /**
     * 扩容详情
     * @param upgradeServiceRequest 请求参数
     * @return 资源ID
     */
    UpgradeDetailVO upgradeDetail(UpgradeServiceRequest upgradeServiceRequest) ;
}
