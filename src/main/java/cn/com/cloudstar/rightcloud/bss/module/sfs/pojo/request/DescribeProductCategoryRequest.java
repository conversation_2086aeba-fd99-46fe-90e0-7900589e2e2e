/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2019/11/1 16:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询产品类别请求参数")
public class DescribeProductCategoryRequest extends BaseRequest {

    /**
     * 产品类别名称
     */
    @ApiModelProperty("产品类别名称")
    @SafeHtml
    private String categoryNameLike;

}
