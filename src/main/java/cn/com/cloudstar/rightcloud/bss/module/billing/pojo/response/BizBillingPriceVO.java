/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response;

import cn.com.cloudstar.rightcloud.oss.common.enums.UnitsEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * Created on 2019/11/5
 */
@Data
public class BizBillingPriceVO {

    /**
     * 计费类型
     */
    private String category;

    /**
     * 计费频率
     */
    private String chargeCycle;

    /**
     * 按小时计费价格
     */
    private BigDecimal hourPrice;
    /**
     * 小时计费详情
     */
    private String hourPriceDesc;
    /**
     * 按月计费价格
     */
    private BigDecimal monthPrice;
    /**
     * 按月计费价格详情
     */
    private String mouthPriceDesc;
    /**
     * 单次计费价格
     */
    private BigDecimal oncePrice;
    /**
     * 单次计费详情描述
     */
    private String oncePriceDesc;

    /**
     * 平台折扣系数
     */
    private BigDecimal platformDiscount;

    /**
     * 是否指定月数
     */
    private Boolean appoint;

    /**
     * 计费类型，资源计费（resource），服务计费(service)，额外配置(extraConfig)费用
     */
    private String priceType;

    /**
     * 额外收费类型，单次（once）或周期（period）
     */
    private String extraType;

    /**
     * 价格描述
     */
    private String priceDesc;

    /**
     * 具体计费配置
     */
    private String billingSpec;

    /**
     * 具体资源配置
     */
    private String resourceConfig;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 标识
     */
    private String identification;

    /**
     * 当前购买产品code 数据盘 系统盘 EBS ，主机ECS 弹性IP EIP
     */
    private String productCode;

    /**
     * 规格类型
     */
    private String specType;

    /**
     * 按量计费单价
     */
    private BigDecimal unitHourPrice;

    /**
     * 按量计费基础价格
     */
    private BigDecimal fixedHourPrice;

    /**
     * 按量计费单价(折扣价)
     */
    private BigDecimal tradeUnitHourPrice;

    /**
     * 按量计费基础价格(折扣价)
     */
    private BigDecimal tradeFixedHourPrice;

    /**
     * 按量计费单价的单次计费价格
     */
    private BigDecimal unitOncePrice;

    /**
     * 类型族
     */
    private String productType;

    /**
     * 类型族名称
     */
    private String productTypeName;

    /**
     *  计费单位
     */
    private String unit;

    private BigDecimal convertRatio = BigDecimal.ZERO;

    /**
     * 客户自定义标志
     */
    private Boolean customFlag;
    public BizBillingPriceVO() {
        hourPrice = BigDecimal.ZERO;
        hourPriceDesc = "";
        monthPrice = BigDecimal.ZERO;
        hourPriceDesc = "";
        oncePrice = BigDecimal.ZERO;
        oncePriceDesc = "";
        platformDiscount = BigDecimal.ONE;
        appoint = Boolean.FALSE;
        unitHourPrice = BigDecimal.ZERO;
        fixedHourPrice = BigDecimal.ZERO;
        tradeUnitHourPrice = BigDecimal.ZERO;
        tradeFixedHourPrice = BigDecimal.ZERO;
        unitOncePrice = BigDecimal.ZERO;
    }

    public String getUnit() {
        this.unit = "/小时";
        if (Objects.nonNull(this.specType)) {
            switch (this.specType) {
                // HPC基础单位判定
                case "cpuUsage":
                    unit = UnitsEnum.CPUUSAGE.getUnit();
                    break;
                case "gpuUsage":
                    unit = UnitsEnum.GPUUSAGE.getUnit();
                    break;
                // ModelArts共享资源池基础单位判定
                case "resourceUsage":
                    unit = UnitsEnum.RESOURCEUSAGE.getUnit();
                    break;
                // 存储使用量基础单位判定
                case "storageUsage":
                case "capacity":
                    unit = UnitsEnum.STORAGEUSAGE.getUnit();
                    break;
                case "pflopsResourceUsage":
                    unit = UnitsEnum.PFLOPSRESOURCEUSAGE.getUnit();
                    break;
                default:
                    break;
            }
        }

        return unit;
    }
}
