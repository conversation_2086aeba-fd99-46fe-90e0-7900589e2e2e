/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.service.impl;

import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.BizCustomerActionLogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.CustomerActionLogRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.CustomerActionLogService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.BizCustomerActionLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;

/**
 * <p>
 * 查询日志记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-22
 */
@Service("customerActionLogService")
@Slf4j
public class CustomerActionLogServiceImpl extends ServiceImpl<BizCustomerActionLogMapper, BizCustomerActionLog> implements
        CustomerActionLogService {

    @Resource
    private OrgService orgService;

    @Override
    public IPage<BizCustomerActionLog> getActionLogList(CustomerActionLogRequest request) {
        // 分销商权限校验
        if (Objects.nonNull(request.getOrgSid())) {
            orgService.checkDistributorRole(request.getOrgSid(), null);
        }
        Criteria criteria = new Criteria();
        if (!StringUtil.isNullOrEmpty(request.getOpType())) {
            request.setOpType(OperationResourceEnum.DecsForType(request.getOpType()));
        }
        criteria.setConditionObject(request);
        criteria.put("orderByClause", "op_date desc");
        Page<BizCustomerActionLog> page = PageUtil.preparePageParams(request);
        String realName = request.getUserName();
        if (StringUtil.isNotEmpty(realName)) {
            criteria.put("userName",null);
            criteria.put("realNameHash", DigestUtils.sha256Hex(realName));
        }
        IPage<BizCustomerActionLog> result = this.baseMapper.selectByParams(page, criteria.getCondition());
        result.getRecords().forEach(log -> {
            log.setOpType(OperationResourceEnum.typeToDecsByI18n(log.getOpType()));
            log.setOpDetail(this.opDetailToUs(log.getOpDetail()));
        });
        return result;
    }

    private String opDetailToUs(String opDetail) {
        if (!WebUtil.getHeaderAcceptLanguage()) {
            return opDetail;
        }
        opDetail = opDetail.replaceAll("平台充值", "Platform recharge")
                .replaceAll("信用余额充值", "Credit balance recharge")
                .replaceAll("合同调整信用额度", "Contract adjustment of credit limit")
                .replaceAll("微信支付充值", "WeChat Pay Recharge")
                .replaceAll("支付宝充值", "Alipay recharge")
                .replaceAll("充值现金券充值", "Recharge with cash coupons")
                .replaceAll("调整信用额度为", "Adjust the credit limit to")
                .replaceAll("修改欠费余额", "Modify outstanding balance")
                .replaceAll("余额欠费修改信用额度", "Balance arrears, modify credit limit")
                .replaceAll("AI开发平台专属资源池", "Ascend Modelarts Exclusive Resource Pool")
                .replaceAll("资源费用", "Resource costs")
                .replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                .replaceAll("套餐包费用", "Package fee")
                .replaceAll("对象存储", "Object Storage")
                .replaceAll("优惠券抵扣", "Coupon deduction")
                .replaceAll("关联了分销商", "Associated with distributors")
                .replaceAll("创建客户折扣", "Create customer discounts")
                .replaceAll("编辑客户折扣", "Edit customer discounts")
                .replaceAll("拒绝账户激活。", "Reject account activation.")
                .replaceAll("对账户进行了激活。", "The account has been activated.")
                .replaceAll("对账户进行了认证审核。", "Verified and audited the account.")
                .replaceAll("登录失败达上限，登录锁定", "Login failed to reach the maximum limit, login locked")
                .replaceAll("锁定期满，用户解锁", "After the lock period expires, the user unlocks")
                .replaceAll("手动冻结了资源权限", "Manually frozen resource permissions")
                .replaceAll("手动解冻了资源权限", "Manually unfrozen resource permissions")
                .replaceAll("发放抵扣现金券", "Distribute cash vouchers for deduction")
                .replaceAll("修改了客户信息", "Modified customer information")
                .replaceAll("删除客户折扣", "Delete customer discount")
                .replaceAll("发放现金券", "Distribute cash vouchers")
                .replaceAll("发放优惠券", "Distribute coupons")
                .replaceAll("创建合同", "Create Contract")
                .replaceAll("现金券编号", "Cash voucher number")
                .replaceAll("优惠卷编号", "Coupon number")
                .replaceAll("自动冻结失败", "Automatic freezing failed")
                .replaceAll("自动冻结成功", "Automatic freeze successful")
                .replaceAll("冻结失败", "Freeze failed")
                .replaceAll("冻结成功", "Freeze successful")
                .replaceAll("解冻成功", "Thawing successful")
                .replaceAll("客户", "customer")
                .replaceAll("折扣名", "Discount Name")
                .replaceAll("产品", "product")
                .replaceAll("范围", "Range")
                .replaceAll("金额", "amount of money")
                .replaceAll("元", "yuan")
                .replaceAll("在线充值", "Online recharge");
        return opDetail;
    }
}
