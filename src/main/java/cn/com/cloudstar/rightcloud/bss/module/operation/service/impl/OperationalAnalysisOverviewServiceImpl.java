package cn.com.cloudstar.rightcloud.bss.module.operation.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CostBillExport;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CostBillItem;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.operation.service.OperationalAnalysisOverviewService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024-11-06 17:07
 * @Desc
 */
@Service
@Slf4j
public class OperationalAnalysisOverviewServiceImpl implements OperationalAnalysisOverviewService {

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    @Lazy
    private ExportService exportService;

    @Override
    public RestResult export(CostBillExport costBillExport) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.CONSUME_DETAIL.getCode());
        //添加下载任务数据
        download = getBizDownload(download, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult(cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.CURRENT_STATUS_NOT_DOWN));
        }
        new ExportThreadUtil(exportService,
                costBillExport,
                ModuleTypeConstants.FROM_BSS,
                ExportTypeEnum.CONSUME_DETAIL.getCode(),
                download.getDownloadId(),
                authUserInfo
        ).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1977429080));
    }

    private BizDownload getBizDownload(BizDownload download, AuthUser authUserInfo) {
        // 租户在下载任务中存入accountId
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam("{}");
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        return download;
    }
}
