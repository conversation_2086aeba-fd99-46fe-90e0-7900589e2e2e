/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * <p>
 * 运营-用户套餐包实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-3-7
 */
@Data
public class BizBagUser  implements Serializable {

  private static final long serialVersionUID = 1L;
  /**
   * id
   */
  @TableId(value = "id")
  private String id;
  /**
   *套餐包ID
   */
  private String bagId;
  /**
   *套餐包实例UUID
   */
  private String bagInstUuid;
  /**
   *计费类型
   */
  private String billingType;
  /**
   *产品代码
   */
  private String productType;
  /**
   *状态,available运行中，expired已过期
   */
  private String status;
  /**
   *套餐包规格名
   */
  private String bagSpecName;
  /**
   *套餐包类型,折扣包：discount 资源包：resource
   */
  private String bagType;
  /**
   *套餐包值
   */
  private BigDecimal bagValue;
  /**
   *过期清除策略,0 不清除  1清除
   */
  private Long clearPolicy;
  /**
   *生效时间
   */
  private Date startTime;
  /**
   *失效时间
   */
  private Date endTime;
  /**
   *所有者ID
   */
  private String ownerId;
  /**
   *组织ID
   */
  private Long orgSid;
  /**
   *版本号 默认为 1
   */
  private Long version;
  /**
   *创建人
   */
  private String createdBy;
  /**
   *创建时间
   */
  private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 订单sn
     */
    private String orderSn;
    /**
     * 组织名字
     */
    private String orgName;

  private Long entityId;

  private String entityName;

  /**
   * HPC共享资源池clusterId
   */
  private String clusterId;

  /**
   * 账户ID
   */
  @TableField(exist = false)
  @JsonSerialize(using = ToStringSerializer.class)
  private Long accountId;

}
