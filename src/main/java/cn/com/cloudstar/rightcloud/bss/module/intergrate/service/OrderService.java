/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

import cn.com.cloudstar.rightcloud.bss.common.pojo.ApproveOrderRequest;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductOperation;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.EmailVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;

/**
 * <AUTHOR>
 * @date 2020/5/9.
 */
@FeignClient(value = "${feign.url.oss:https://cmp-oss:8080}", configuration = FeignConfig.class, path = "/api/v1/oss")
public interface OrderService {

    /**
     * 查询订单列表
     */
    @GetMapping("/audit/approval/orders/feign")
    RestResult getOrderList(@SpringQueryMap Map<String, Object> query);

    /**
     * 获取订单详情
     */
    @GetMapping("/audit/approval/order/{orderId}")
    RestResult<ServiceOrder> getOrderDetailByOrderId(@PathVariable("orderId") Long orderId);

    @PostMapping("/order/renewal")
    RestResult renewResource(@RequestBody ProductOperation productOperation);

    @PostMapping("/common/email")
    RestResult sendEmail(EmailVO emailVO);


    @PostMapping("/order/release")
    @Transactional(rollbackFor = Exception.class)
    RestResult releaseOrder(@RequestBody ProductOperation productOperation);

    @PostMapping("/order/modify")
    RestResult modifyResourceOrder(ProductOperation productOperation);

    /**
     * 通过订单生成审批单
     *
     * @param id
     */
    @PostMapping("/audit/approval/startProcess/{orderId}")
    RestResult startProcessByOrder(@PathVariable("orderId") Long id);

    /**
     * 验证用户是否冻结
     *
     * @param userId
     */
    @GetMapping("/users/isFreeze/{userId}")
    RestResult isFreeze(@PathVariable("userId") Long userId);
    /**
     * 验证用户是否实名
     * @param id
     */
    @GetMapping("/users/checkAuth/{userSid}")
    RestResult checkAuth(@PathVariable("userSid") Long id);

    /**
     * 通过订单生成审批单
     * @param approveOrderRequest
     */
    @PutMapping("/audit/approval")
    RestResult updateOrder(ApproveOrderRequest approveOrderRequest);

    @GetMapping("/audit/approval/node/order/{orderId}/feign")
    RestResult getProcessNodeByOrderId(@PathVariable("orderId") Long orderId);
}
