/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.controller;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 服务
 *
 * <AUTHOR>
 * Created on 2019/10/30
 */
@Api(tags = "服务")
@RestController
public class ServiceCategoryController {

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    /**
     * 获取账户列表
     *【Since v2.5.0】
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取账户列表'", resource = OperationResourceEnum.ACCESSKEYCREATE, tagNameUs ="'Get Account List'")
    public RestResult<List<ServiceCategory>> findServiceCategory() {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        QueryWrapper<ServiceCategory> categoryQueryWrapper = new QueryWrapper<>();
        categoryQueryWrapper.eq("org_sid", authUserInfo.getOrgSid());
        categoryQueryWrapper.eq("status", "using");
        List<ServiceCategory> serviceCategories = serviceCategoryService.list(categoryQueryWrapper);
        return new RestResult(serviceCategories);
    }
}
