/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CashCouponSendRequest {

    int[] messageType;

    String mobile;

    String email;

    BigDecimal amount;

    String couponNo;

    Date startDate;

    Date endDate;

    String accountName;

    /**
     * 数据库默认deposit，现金券类型 [deposit]充值现金券 [deduct]抵扣现金券
     */
    String type;

    /**
     * 适用产品 适用的产品，多个以，分隔。[DRP][IAAS]
     */
    private String productScope;

    /**
     * 运营实体id
     */
    private Long entityId;

    /**
     * 账户名称
     */
    String entityName;

    /**
     * 账户Id
     */
    private Long accountId;
}
