/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/6 17:24
 */
@Data
public class PriceVO implements Serializable {

    /**
     * 类别
     */
    private String category;
    /**
     * 规范
     */
    private String specDesc;
    /**
     * 规范值
     */
    private String specValue;
    /**
     * 原始价格
     */
    private String originalPrice;
    /**
     * 折扣价格
     */
    private String discountPrice;


}
