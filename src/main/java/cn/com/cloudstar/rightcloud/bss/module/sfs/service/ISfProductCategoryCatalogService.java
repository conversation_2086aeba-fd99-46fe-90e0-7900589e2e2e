/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductCategoryCatalog;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.CreateProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.DescribeProductCategoryRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.DescribeProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.RelatedProductTemplatesRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductStatusRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductCategoryResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeServiceCategory;

/**
 * <p>
 * 产品类别 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-01
 */
public interface ISfProductCategoryCatalogService extends IService<SfProductCategoryCatalog> {

    /**
     * 创建产品类别
     *
     * @param name 类别名称
     * @param productIds 关联的服务id
     * @return 创建结果
     */
    boolean createCategory(String name, List<Long> productIds);

    /**
     * 创建产品时产品名称重复验证
     *
     * @param name 类别名称
     * @return 创建结果
     */
    boolean CategoryNameVerify(String name);

    /**
     * 编辑产品类别
     *
     * @param categoryId 产品类别id
     * @param name 类别名称
     * @param productIds 关联的服务id
     * @return 编辑结果
     */
    boolean updateCategory(Long categoryId, String name, List<Long> productIds,String entitySid);

    /**
     * 删除产品类别
     *
     * @param categoryId 产品类别id
     * @return 删除结果
     */
    boolean deleteCategory(Long categoryId);

    /**
     * 查询产品类别
     *
     * @param request 查询参数
     * @return 查询结果
     */
    IPage<DescribeProductCategoryResponse> listCategories(DescribeProductCategoryRequest request);

    /**
     * 查询产品
     *
     * @param request 查询参数
     * @return 查询结果
     */
    IPage<DescribeProductResponse> listProducts(DescribeProductRequest request);

    /**
     * 启用/禁用 修改产品名称
     *
     * @param request 更新参数
     * @return 更新结果
     */
    boolean updateProduct(UpdateProductRequest request);

    /**
     * 关联产品模板
     *
     * @param request 参数
     * @return 关联结果
     */
    boolean relatedProductTemplate(RelatedProductTemplatesRequest request);

    /**
     * 创建产品
     * @param request
     * @return
     */
    Long createProduct(CreateProductRequest request);

    /**
     * 删除产品
     * @param productId
     * @return
     */
    void deleteProduct(Long productId);

    /**
     * 更新产品状态
     * @param request
     * @return
     */
    boolean updateProductStatus(UpdateProductStatusRequest request);

    /**
     * 产品代码是否存在
     * @param productCode
     * @param productId
     * @return
     */
    boolean checkProductCodeExist(String productCode, Long productId);

    /**
     * 查询平台所有上过架产品
     */
    List<DescribeServiceCategory> publishProducts(String productNameLike, String entityId, String moduleType);

    /**
     * 查询平台下拉框
     */
    List<DescribeServiceCategory> publishProductsList(String productNameLike, String entityId, String moduleType);

    /**
     * 通过账户ID找到对应产品
     * @param accountId accountId
     * @return List<DescribeServiceCategory>
     */
    List<DescribeServiceCategory> findProductByAccountId(Long accountId);

    /**
     * 查询产品是否上架
     * @param serviceType serviceType
     * @return RestResult
     */
    RestResult checkServiceStatus(String serviceType);
}
