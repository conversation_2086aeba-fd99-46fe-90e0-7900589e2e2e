/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/04/24 16:47
 */
@Data
@ApiModel("密码策略")
public class PasswordPolicyResponse {

    /**
     * 最小长度
     */
    @ApiModelProperty("最小长度")
    private int minLength;

    /**
     * 密码包含规则
     */
    @ApiModelProperty("密码包含规则")
    private List<String> charactorType;

    /**
     * 不同字符数限制
     */
    @ApiModelProperty("不同字符数限制")
    private int charactorLimit;

    /**
     * 常用密码剔除
     */
    @ApiModelProperty("常用密码剔除")
    private List<String> ruleOut;

    /**
     * 登陆失败锁定开启状态
     */
    @ApiModelProperty("登陆失败锁定开启状态")
    private boolean loginfailureEnable;

    /**
     * 最大失败数
     */
    @ApiModelProperty("最大失败数")
    private int loginfailureCount;

    /**
     * 账号有效期开启状态
     */
    @ApiModelProperty("账号有效期开启状态")
    private boolean accountValidity;

    /**
     * 有效天数
     */
    @ApiModelProperty("有效天数")
    private int expireTime;

    /**
     * 密码有效期
     */
    @ApiModelProperty("密码有效期")
    private Long pwdExpireTime;

    /**
     * 密码有效期开启状态
     */
    @ApiModelProperty("密码有效期开启状态")
    private Boolean pwdExpireTimeValidity;

    /**
     * 密码最少使用天数
     */
    @ApiModelProperty("密码最少使用天数")
    private Long pwdLeastUsedDay;

    /**
     * 密码不能与前N个历史密码重复
     */
    @ApiModelProperty("密码不能与前N个历史密码重复")
    private Long pwdRepeatNum; @ApiModelProperty("密码不能与前N个历史密码重复")
    private String secAuth;
    private List<String> secInterface;

}
