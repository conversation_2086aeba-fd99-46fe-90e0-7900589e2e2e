/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UserUnLockRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.DescribeOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.ServiceProcessRequest;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;

import feign.Headers;
import feign.Response;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * feign服务
 *
 * <AUTHOR>
 * @date 2020/5/9.
 */
@FeignClient(value = "https://cmp-oss:8080", configuration = FeignConfig.class, path = "/api/v1/oss")
public interface FeignService {

    @GetMapping("/code/feign/{codeCategory}")
    RestResult getCodesByCategoryByFeign(@PathVariable("codeCategory") String codeCategory,
                                                             @RequestParam(value = "statusAll", required = false) boolean statusAll,
                                                             @RequestParam(value = "codeValue", required = false) String codeValue,
                                                             @RequestParam(value = "excludeCodeValue", required = false) String excludeCodeValue,
                                                             @RequestParam(value = "includeDedicatedResourcePool", defaultValue = "false") boolean includeDedicatedResourcePool,
                                                             @RequestParam(value = "mark", defaultValue = "false") String mark);

    @GetMapping("/configs/feign")
    RestResult displayAllMessageByFeign(@SpringQueryMap @Validated DescribeSysConfigRequest request);

    @GetMapping("/upload/image/zip/feign")
    Response downloadFile(@RequestParam String fileId) throws BizException;

    @GetMapping("/process/record/feign/{orderId}")
    RestResult getHistoryRecordByFeign(@ApiParam(name = "orderId", value = "订单ID", required = true) @PathVariable String orderId);

    @PutMapping("/users/password/reset/feign")
    RestResult resetUserPassword(@RequestBody @Valid ResetPwdRequest request);

    @PutMapping("/users/self/feign")
    RestResult updateUserBySelf(@RequestBody @Valid UpdateCompanyUserRequest request);
    @GetMapping("/configs/multi/feign")
    RestResult getConfigsByTypeList(@SpringQueryMap  @Validated DescribeSysConfigByTypeListRequest request);

    @GetMapping("/audit/approval/amount/detail/feign")
    RestResult getOrderAmountDetail(@ApiParam("订单id") @RequestParam(value = "orderId") Long orderId,
            @ApiParam("付费类型") @RequestParam(value = "chargeType") String chargeType);

    @GetMapping("/ticket/category/feign")
    RestResult getCategory();
    @PutMapping("/users/feign")
    RestResult updateCompanyUserByAccount(@RequestBody @Valid UpdateCompanyUserRequest request);
    @GetMapping("/audit/approval/asyn/feign/export")
    RestResult expertOrderList(@SpringQueryMap DescribeOrderRequest describeOrderRequest);

    @GetMapping("/sys_config/config_data/feign")
    RestResult getConfigData(
            @ApiParam("配置Key") @RequestParam(value = "configKey", required = false) String configKey,
            @ApiParam("配置Key模糊查询") @RequestParam(value = "configKeyLike", required = false) String configKeyLike,
            @ApiParam("多个配置Key,以','分割") @RequestParam(value = "configKeys", required = false) String configKeys);


    /**
     * zip压缩发送密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @PostMapping("/message/sendZipCompressPassword")
    RestResult sendZipCompressPassword(@RequestBody SendZipCompressPasswordRequest request);

    @GetMapping("/users/getAuthCompanyInfoById/feign/{companyId}")
    RestResult getAuthCompanyInfoById(@ApiParam(name = "companyId", value = "企业ID", required = true) @PathVariable Long companyId);

    @PostMapping("/users/login/feign")
    RestResult checkPassword(@RequestBody @Valid CheckPasswordRequest request);


    /**
     * 获取资源类型
     * @param availableResourceRequest
     * @return
     */
    @GetMapping("/resource_templates/resource_family")
    RestResult getResourceFamilyType(@SpringQueryMap AvailableResourceRequest availableResourceRequest);

    /**
     * 解锁子用户
     * @param request
     * @return
     */
    @PutMapping("/users/unlock/feign")
    RestResult setUserUnlockStatusFeign(@RequestBody UserUnLockRequest request);
}
