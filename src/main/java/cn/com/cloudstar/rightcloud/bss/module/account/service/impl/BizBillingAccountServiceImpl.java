/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.service.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.bss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.CustomExportCellValueConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.DealType;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.TradeType;
import cn.com.cloudstar.rightcloud.bss.common.enums.ApplicationTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.BusinessTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.IndustryTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PayStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCatalogEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.TradeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RightCloudTrunc;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DistributorAuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.MessageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserOrg;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.CustomizationInfo;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.CustomizationInfoTemplate;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserExportDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserOrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.HPCService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.enums.ExpireStrategyEnum;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizDepositMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.SysBssEntityMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.AccountExpireMessage;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizDeposit;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.CloudDataCountVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketCatalogVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.OverViewVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.WaitingCenterVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.AmountClearanceRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeDepositRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.MarketOrderPostingRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.RechargeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.SetExpirationTimeRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateBusinessTagRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateCreditLineRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateDepositRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateDistributorRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateFreeCapacityRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeRechargeAccountInfoResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.service.AmountClearanceService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.OssFeignService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.ResourcePermissionService;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagUser;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapAmount;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCostSummary;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IInstanceGaapCostService;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.CashCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.CashCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.SendZipCompressPasswordRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeCodeFullResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MailService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OssService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoice;
import cn.com.cloudstar.rightcloud.bss.module.message.service.ISysMsgService;
import cn.com.cloudstar.rightcloud.bss.module.operation.mapper.StatisticsMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.BizProductQuotaMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.impl.SfProductCategoryCatalogServiceImpl;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.BizCustomerActionLogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.SysMNotifyConfigBssMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.ConsoleMsg;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.PlatformMsg;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.PlatformMsg.AccountMsg;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.common.util.NotificationUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.action.ActionLogTracker;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCostSummaryMonth;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.BizCustomerActionLog;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ExpireConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.ProductQuotaConfig;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.BizBagUserStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.CustomBusinessTag;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Role;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendNotifyRequest;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResAllParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResShareParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.UserSyncRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.map.LinkedMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceRunnable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.CUSTOMER_MAX_NUM;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.USER_NOT_EXIST;
import static cn.com.cloudstar.rightcloud.bss.module.renewal.service.impl.ResRenewRefServiceImpl.normalList;
import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * <p>
 * 账户管理  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-22
 */
@Service("bizBillingAccountService")
@Slf4j
public class BizBillingAccountServiceImpl extends ServiceImpl<BizBillingAccountMapper, BizBillingAccount> implements
        IBizBillingAccountService {
    private static final String FLOW_PREFIX = "CZ";

    private static final String DEAL_PREFIX = "SZ";

    private static final List<String> PRODUCT_TYPE = Arrays.asList("计算", "存储", "网络");

    private static final Map<String, String> FREEZE_STATUS = Maps.newHashMap();

    public static String USER_PRE_OPEN = "该用户为预开通状态,暂未分配真实用户!";

    private static final String DISTRIBUTOR_TAG = "直营";



    /**
     * 状态：启用
     */
    private static final String ONE = "1";
    private static final String ZERO = "0";
    private static final String NORMAL = "normal";
    private static final String FREEZE = "freeze";
    //解冻中
    private static final String UNFREEZING = "unfreezing";
    //冻结中
    private static final String FREEZING = "freezing";
    private static final String UNLIMITED = "unlimited";

    /**
     * "productsQuotaAccount:"
     */
    private static final String PRODUCTS_QUOTA_ACCOUNT_KEY = "productsQuotaAccount:";

    /**
     * "productsQuota:"
     */
    private static final String PRODUCTS_QUOTA_KEY = "productsQuota:";

    /**
     * "productsQuotaWhiteList:"
     */
    private static final String PRODUCTS_QUOTA_WHITE_LIST_KEY = "productsQuotaWhiteList:";

    /**
     * 试用期账号到期清理标识
     */
    private static final String EXPIRE_TYPE = "accountExpire";

    private static final String CANCELLED = "cancelled";

    private static final String COMPLETED = "completed";

    private static final String SETTLEMENT = "结算";

    private static final String REFUND = "退款";

    private static final Long EXPIRE_TIME = 86400000L;

    private static final List<Long> ADMIN_ORG_SIDS = Arrays.asList(-1L, 0L);

    @Autowired
    private AmqpTemplate amqpTemplate;

    private static final String ACCOUNT_EXPIRE_ROUTING_KEY = "account.expire.*";

    private static final String CLEARANCE_TYPE = "CLEARANCE_TYPE";

    private static final String TANANT_ADD_BUSINESS_TAG = "tenant_add_business_tag";
    private static final String BSSMGT_ADD_BUSINESS_TAG = "bssmgt_add_business_tag";

    static {
        FREEZE_STATUS.put("0", "是");
        FREEZE_STATUS.put("1", "否");
        FREEZE_STATUS.put(null, "否");
    }

    @Autowired
    private BizDepositMapper bizDepositMapper;

    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;

    @Autowired
    private BizContractMapper bizContractMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private StatisticsMapper statisticsMapper;
    @DubboReference
    private ResAllRemoteService resAllRemoteService;

    @Autowired
    private ThreadPoolTaskExecutor cloudExecutor;

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private ISysMsgService sysMsgService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserOrgMapper sysUserOrgMapper;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private IBizBillingAccountService iBizBillingAccountServiceImp;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private SysMNotifyConfigBssMapper sysMNotifyConfigBssMapper;

    @Autowired
    private CashCouponMapper cashCouponMapper;

    @Autowired
    private ResourcePermissionService resourcePermissionService;

    @DubboReference
    private IamRemoteService iamRemoteService;

    @Autowired
    private CodeMapper codeMapper;
    @Autowired
    private OrgService orgService;

    @Value("${SKIP_HPC_FUNCTION:true}")
    private Boolean skipHPCFunction;
    @DubboReference
    private ShareRemoteService shareRemoteService;

    @Autowired
    private HPCService hpcService;

    @DubboReference
    private HPCRemoteService hpcRemoteService;

    @Autowired
    private IBizDistributorService bizDistributorService;
    @Autowired
    private IInstanceGaapCostService instanceGaapCostService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private MailService mailService;
    @Autowired
    private Tracer tracer;
    @Autowired
    private SpanNamer spanNamer;
    @Autowired
    private Map<String, AmountClearanceService> amountClearanceFactory;

    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    SfProductCategoryCatalogServiceImpl sfProductCategoryCatalogService;

    @Autowired
    BizProductQuotaMapper bizProductQuotaMapper;

    @Autowired
    private ActionLogTracker actionLogTracker;
    @Autowired
    private BizBagUserService bizBagUserService;
    @Autowired
    private FeignService feignService;

    @Autowired
    private SysBssEntityMapper sysBssEntityMapper;

    @DubboReference
    private UserSyncRemoteService userSyncRemoteService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    OssService ossService;
    @Autowired
    private BizDownloadMapper bizDownloadMapper;
    @Autowired
    @Lazy
    private ExportService exportService;
    @Autowired
    private BizCustomerActionLogMapper bizCustomerActionLogMapper;

    @Autowired
    private IServiceOrderPriceDetailService orderPriceDetailService;

    @Autowired
    private OssFeignService ossFeignService;

    @Override
    public IPage<BizBillingAccount> getBillingAccountList(DescribeBillingAccountRequest request) {
        Long adminSid = null;
        if (Objects.nonNull(request.getUserSid())){
            adminSid = request.getUserSid();
        }
        Criteria criteria = new Criteria();
        // 查询分销商为[直营]的情况
        if (!StringUtil.isNullOrEmpty(request.getDistributorName())
                && "直营".equals(request.getDistributorName())) {
            request.setDistributorName(null);
            request.setDirect("1");
        }
        criteria.setConditionObject(request);
        //关键词搜索
        if (Objects.nonNull(adminSid)){
            criteria.put("adminSid", adminSid);
        }
        String keyword = request.getKeyword();
        List<String> industryList = new ArrayList<>();
        List<String> applicationList = new ArrayList<>();
        List<String> businessList = new ArrayList<>();
        if(StrUtil.isNotBlank(keyword)){
            criteria.put("keyword", "\\" + keyword);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword) && !CCSPCacheUtil.ccspServiceOpen()) {
                criteria.put("keywordHash", DigestUtils.sha256Hex(keyword));
            }
            //关键词搜索最少要两个字
            if(DISTRIBUTOR_TAG.contains(keyword)){
                //数据库属于直营的distributor_id is null
                criteria.put("distributorTag",DISTRIBUTOR_TAG);
            }
            if(IndustryTypeEnum.SECURITY.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.SECURITY.getCode());
            }
            if(IndustryTypeEnum.INTERNET.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.INTERNET.getCode());
            }
            if(IndustryTypeEnum.MANUFACTURE.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.MANUFACTURE.getCode());
            }
            if(IndustryTypeEnum.TRAFFIC.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.TRAFFIC.getCode());
            }
            if(IndustryTypeEnum.ENERGY.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.ENERGY.getCode());
            }
            if(IndustryTypeEnum.OPERATOR.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.OPERATOR.getCode());
            }
            if(IndustryTypeEnum.FINANCE.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.FINANCE.getCode());
            }
            if(IndustryTypeEnum.DIGITAL_GOVERNMENT.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.DIGITAL_GOVERNMENT.getCode());
            }
            if(IndustryTypeEnum.MILITARY_PROJECT.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.MILITARY_PROJECT.getCode());
            }
            if(IndustryTypeEnum.EDUCATION.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.EDUCATION.getCode());
            }
            if(IndustryTypeEnum.MEDICAL_CARE.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.MEDICAL_CARE.getCode());
            }
            if(IndustryTypeEnum.SCIENTIFIC_RESEARCH.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.SCIENTIFIC_RESEARCH.getCode());
            }
            if(IndustryTypeEnum.OTHER_INDUSTY.getName().contains(keyword)){
                industryList.add(IndustryTypeEnum.OTHER_INDUSTY.getCode());
            }
            if(CollectionUtil.isNotEmpty(industryList)){
                criteria.put("industryList",industryList);
            }

            if(ApplicationTypeEnum.INTELLIGENTE_QUIPMENT.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INTELLIGENTE_QUIPMENT.getCode());
            }
            if(ApplicationTypeEnum.SMART_APPLIANCES.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.SMART_APPLIANCES.getCode());
            }
            if(ApplicationTypeEnum.INTEGRATED_CIRCUIT.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INTEGRATED_CIRCUIT.getCode());
            }
            if(ApplicationTypeEnum.INTELLIGENT_TRANSPORTATION.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INTELLIGENT_TRANSPORTATION.getCode());
            }
            if(ApplicationTypeEnum.INTELLIGENT_MEDICAL_TREATMENT.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INTELLIGENT_MEDICAL_TREATMENT.getCode());
            }
            if(ApplicationTypeEnum.INFORMATION_SAFETY.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INFORMATION_SAFETY.getCode());
            }
            if(ApplicationTypeEnum.INTELLIGENT_MANUFACTURING.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.INTELLIGENT_MANUFACTURING.getCode());
            }
            if(ApplicationTypeEnum.NEWENERGY_VEHICLE.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.NEWENERGY_VEHICLE.getCode());
            }
            if(ApplicationTypeEnum.SMART_CITY.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.SMART_CITY.getCode());
            }
            if(ApplicationTypeEnum.SMART_FINANCE.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.SMART_FINANCE.getCode());
            }
            if(ApplicationTypeEnum.NEW_DISPLAY.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.NEW_DISPLAY.getCode());
            }
            if(ApplicationTypeEnum.PHOTOVOLTAIC_NEWENERGY.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.PHOTOVOLTAIC_NEWENERGY.getCode());
            }
            if(ApplicationTypeEnum.AFTER_CLASS_SERVICE.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.AFTER_CLASS_SERVICE.getCode());
            }
            if(ApplicationTypeEnum.OTHER_APPLICATION_SCENARIOS.getName().contains(keyword)){
                applicationList.add(ApplicationTypeEnum.OTHER_APPLICATION_SCENARIOS.getCode());
            }
            if(CollectionUtil.isNotEmpty(applicationList)){
                criteria.put("applicationList",applicationList);
            }
            if(BusinessTypeEnum.EXPANSION.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.EXPANSION.getCode());
            }
            if(BusinessTypeEnum.RECORDED.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.RECORDED.getCode());
            }
            if(BusinessTypeEnum.TRIAL.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.TRIAL.getCode());
            }
            if(BusinessTypeEnum.SIGNED.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.SIGNED.getCode());
            }
            if(BusinessTypeEnum.COMMERCIAL.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.COMMERCIAL.getCode());
            }
            if(BusinessTypeEnum.ARREARAGE.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.ARREARAGE.getCode());
            }
            if(BusinessTypeEnum.CANCELLED.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.CANCELLED.getCode());
            }
            if(BusinessTypeEnum.PREOPEN.getName().contains(keyword)){
                businessList.add(BusinessTypeEnum.PREOPEN.getCode());
            }
            if(CollectionUtil.isNotEmpty(businessList)){
                criteria.put("businessList",businessList);
            }
        }
        criteria.put("orderByClause", " created_dt desc");
        criteria.put("entityId", request.getEntityId());
        String salesmen = request.getSalesmen();
        if (StringUtil.isNotEmpty(salesmen)) {
            criteria.put("salesmen", salesmen);
        }
        if (StringUtil.isNotEmpty(request.getCustomizationInfo())) {
            try {
                JSONObject jsonObject = JSON.parseObject(request.getCustomizationInfo());
                List<String> customizationInfoKey = new ArrayList<>();
                List<String> customizationInfoValue = new ArrayList<>();
                jsonObject.forEach((key, value) -> {
                    customizationInfoKey.add(key);
                    customizationInfoValue.add(String.valueOf(value));
                });
                criteria.put("customizationInfoKey", customizationInfoKey);
                criteria.put("customizationInfoValue", customizationInfoValue);
            }catch (Exception e) {
                throw new BizException("参数不合法");
            }
        }
        // 敏感信息
        Criteria.newCriteriaHash(criteria);

        String ldapOu = request.getLdapOu();
        if (StringUtil.isNotEmpty(ldapOu)) {
            criteria.put("ldapOu", ldapOu);
        }
        if (request.getExcludeUnapproved()) {
            criteria.put("notStatusList", Stream.of("2").collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getOpenProductInformation())) {
            setOpenProductInformation(request.getOpenProductInformation(), criteria);
        }
        // 过滤被逻辑删除的用户
        criteria.put("notStatus", "8");

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(request.getBusinessTag())) {
            List<String> businessTagList = cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.getBusinessTagByNameLike(request.getBusinessTag());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(businessTagList)) {
                criteria.put("businessTagList", businessTagList);
            }
            criteria.put("businessTagLike", request.getBusinessTag());
        }

        RestResult restResult = DistributorAuthUtil.put(criteria, null, AuthUtil.getAuthUser(), 3);
        if (restResult == null) {
            return PageUtil.emptyPage();
        }
        if (PageUtil.isPageQuery(request)) {
            Page<BizBillingAccount> page = PageUtil.preparePageParams(request);
            IPage<BizBillingAccount> bizBillingAccountIPage = new Page<>();
            // 48266这里特殊处理模糊搜索所属分销商"直"或"营",同时要把逻辑值"直营"给搜索出来
            if ("直".equals(request.getDistributorName()) || "营".equals(request.getDistributorName())) {
                bizBillingAccountIPage = this.baseMapper.selectByLikeDistributorName(page, criteria.getCondition());
                bizBillingAccountIPage.setRecords(bizBillingAccountIPage.getRecords().stream().peek(e -> {
                    if (StringUtil.isNullOrEmpty(e.getDistributorName())) {
                        e.setDistributorName("直营");
                    }
                    if (Objects.isNull(e.getRemark())) {
                        e.setRemark("--");
                    }
                    // 业务标识
                    adjustBusinessTag(e);
                }).collect(Collectors.toList()));
                return bizBillingAccountIPage;
            }
            // 下面就是走原有的逻辑
            bizBillingAccountIPage = this.baseMapper.selectByParam(page, criteria.getCondition());
            // 产品开通信息
            Map<String, String> accountProductInfo = openProductInformation(RequestContextUtil.getEntityId());
            bizBillingAccountIPage.setRecords(bizBillingAccountIPage.getRecords().stream().peek(e->{
                if(StringUtil.isNullOrEmpty(e.getDistributorName())){
                    e.setDistributorName("直营");
                }
                if (Objects.isNull(e.getRemark())) {
                    e.setRemark("--");
                }
                // 业务标识
                adjustBusinessTag(e);
                e.setOpenProductInformation(accountProductInfo.get(e.getAccount()));
            }).collect(Collectors.toList()));
            return bizBillingAccountIPage;
        }

        criteria.put("notStatus", Arrays.asList("4","8"));
        List<BizBillingAccount> accounts = this.baseMapper.selectByList(criteria.getCondition());
        //这里的status是user表的而非biz_billing_account表的状态
        if (criteria.get("status") != null && "1".equals(criteria.get("status"))) {
            accounts = accounts.stream().filter(t -> "1".equals(t.getUserStatus()))
                               .collect(Collectors.toList());
        }
        IPage<BizBillingAccount> page = PageUtil.emptyPage();
        page.setRecords(accounts.stream().peek(e->{
            if(StringUtil.isNullOrEmpty(e.getDistributorName())){
                e.setDistributorName("直营");
            }
            // 业务标识
            adjustBusinessTag(e);
        }).collect(Collectors.toList()));
        return page;
    }

    public static void main(String[] args) {
        String decrypt = CrytoUtilSimple.decrypt("--CIPHER--+14eZCL7AUU5gi3bACgBfP5lIfglrYK8HYWwQ84ZlbpHJBSu");
        System.out.println(decrypt);
        System.out.println(DigestUtils.sha256Hex(decrypt));
        System.out.println(DigestUtils.sha256Hex("<EMAIL>"));
        System.out.println(DigestUtils.sha256Hex("***********"));
    }
    /**
     * 赋值开通产品信息条件查询
     * @param openProductInformation openProductInformation
     * @param criteria criteria
     */
    private void setOpenProductInformation(List<String> openProductInformation, Criteria criteria) {
        List<String> accounts = new ArrayList<>();
        if (openProductInformation.contains(ProductCodeEnum.SFS2.getProductCode())) {
            accounts.addAll(getUsedResShares());
        }
        if (openProductInformation.contains(ProductCodeEnum.MA_BMS.getProductCode())) {
            accounts.addAll(getUsedMaBms());
        }
        Criteria productCriteria = new Criteria();
        productCriteria.put("inStatusList",
                Stream.of(Constants.FROZEN, Constants.NORMAL).collect(Collectors.toList()));
        productCriteria.put("productTypeIn", openProductInformation);
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(productCriteria);
        accounts.addAll(sfProductResources.stream().map(SfProductResource::getCreatedBy).distinct().collect(
                Collectors.toList()));
        if (accounts.size() > 0) {
            criteria.put("productAccountList", accounts.stream().distinct().collect(Collectors.toList()));
        } else {
            accounts.add(Constants.ONE);
            criteria.put("productAccountList", accounts);
        }
    }

    /**
     * 产品开通信息
     * @return Map<String, String>
     */
    private Map<String, String> openProductInformation(Long entityId) {
        List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectList(
                new QueryWrapper<ServiceCategory>().lambda().eq(ServiceCategory::getEntityId, entityId));
        List<String> serviceTypes = serviceCategories.stream()
                                                     .map(ServiceCategory::getServiceType)
                                                     .collect(Collectors.toList());
        Criteria productCriteria = new Criteria();
        productCriteria.put("inStatusList", Stream.of(Constants.FROZEN, Constants.NORMAL).collect(Collectors.toList()));
        productCriteria.put("productTypeIn", serviceTypes);
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(productCriteria);
        Map<String, List<SfProductResource>> accountResources = sfProductResources.stream()
                                                                                  .collect(Collectors.groupingBy(
                                                                                          SfProductResource::getCreatedBy));
        Map<String, String> result = Maps.newHashMap();
        // SFS2.0
        List<String> shareAccounts = new ArrayList<>();
        List<String> maBmsAccounts = new ArrayList<>();
        if (serviceTypes.contains(ProductCodeEnum.SFS2.getProductCode())) {
            shareAccounts = getUsedResShares();
            for (String sfsAccount : shareAccounts) {
                result.put(sfsAccount, ProductCodeEnum.SFS2.getProductName());
            }
        }
        // MA-BMS
        if (serviceTypes.contains(ProductCodeEnum.MA_BMS.getProductCode())) {
            maBmsAccounts = getUsedMaBms();
        }
        List<String> finalShareAccounts = shareAccounts;
        List<String> finalMaBmsAccounts = maBmsAccounts;
        accountResources.forEach((k, v) -> {
            String openProductInformation = v.stream()
                                             .map(t -> ProductCodeEnum.getByCode(t.getProductType()))
                                             .distinct()
                                             .collect(Collectors.joining(StrUtil.COMMA));
            if (finalShareAccounts.contains(k)) {
                openProductInformation = openProductInformation + StrUtil.COMMA + ProductCodeEnum.SFS2.getProductName();
            }
            if (finalMaBmsAccounts.contains(k)) {
                openProductInformation =
                        openProductInformation + StrUtil.COMMA + ProductCodeEnum.MA_BMS.getProductName();
            }
            result.put(k, openProductInformation);
        });
        return result;
    }

    /**
     * 获取有SFS2.0产品的账户
     * @return List<String>
     */
    private List<String> getUsedResShares() {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria shareCriteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        shareCriteria.put("inStatusList",
                Stream.of(Constants.FROZEN, Constants.AVAILABLE).collect(Collectors.toList()));
        List<ResShare> resShares = shareRemoteService.selectByParams(shareCriteria);
        return resShares.stream()
                .map(ResShare::getCreatedBy)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 获取有MA-BMS产品的账户
     * @return List<String>
     */
    private List<String> getUsedMaBms() {
        List<String> accounts = new ArrayList<>();
        List<Org> orgs = orgMapper.selectList(
                new QueryWrapper<Org>().lambda().eq(Org::getBmsEnable, 1).isNotNull(Org::getOwner));
        if (CollectionUtils.isNotEmpty(orgs)) {
            return userMapper.selectAccountByUserSidList(orgs.stream().map(Org::getOwner).collect(Collectors.toList()));
        }
        return accounts;
    }

    /**
     * 调整业务标识
     */
    private void adjustBusinessTag(BizBillingAccount e) {
        String businessTag = e.getBusinessTag();
        if (StringUtil.isNotEmpty(businessTag)) {
            if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                String[] splitList = businessTag.split(";");
                List<String> tagList = new ArrayList<>(Arrays.asList(splitList));
                if (businessTag.contains(e.getId().toString())) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) == 0) {
                            return BusinessTagEnum.ARREARAGE.getTag();
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.removeIf(s -> s.indexOf(
                            BusinessTagEnum.ARREARAGE.getTag()) == 0);
                }
                e.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
            }
        }
    }

    @Override
    public IPage<BizBillingAccount> getAccountDiscountList(Page<BizBillingAccount> page, Criteria criteria) {
        return this.baseMapper.selectDiscountByParams(page, criteria.getCondition());
    }

    @Override
    public BizBillingAccount getBillingAccountDetail(Long id) {
        // 安全校验，分销商管理员只能查看组织下账户信息
/*        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        // 分销商
        if (UserType.DISTRIBUTOR_USER.equals(authUser.getUserType()) && Objects.nonNull(authUser.getOrgSid())) {
            BizDistributor distributor = bizDistributorService.getById(authUser.getOrgSid());
            HashMap<String, Object> hashMap = Maps.newHashMap();
            hashMap.put("distributorId", distributor.getId());
            hashMap.put("id", id);
            List<BizBillingAccount> distributorId = this.baseMapper.selectByList(hashMap);
            if (CollectionUtils.isEmpty(distributorId)) {
                throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
            }
        }
        // 租户及子用户只允许访问本组织账户信息
        if (!UserType.DISTRIBUTOR_USER.equals(authUser.getUserType()) && Objects.nonNull(authUser.getOrgSid()) && !ADMIN_ORG_SIDS.contains(authUser.getOrgSid())) {
            BizBillingAccount bizBillingAccount = this.baseMapper.selectByPrimaryKey(id);
            if (bizBillingAccount == null || !bizBillingAccount.getOrgSid().equals(authUser.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
            }
        }
        // 运营管理员 只能查看当前运营实体账户信息
        if (UserType.PLATFORM_USER.equals(authUser.getUserType()) && (Objects.isNull(authUser.getOrgSid()) || ADMIN_ORG_SIDS.contains(authUser.getOrgSid()))) {
            BizBillingAccount bizBillingAccount = this.baseMapper.selectByPrimaryKey(id);
            BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndOrgSid(
                    RequestContextUtil.getEntityId(), bizBillingAccount.getOrgSid());
            id = account.getId();
        }*/
        BizBillingAccount bizBillingAccount = this.baseMapper.selectByPrimaryKey(id);
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException("查询企业不存在");
        }
/*        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        //分销商管理类用户判断数据是不是在当前分销商下
        if(authUserInfo != null && authUserInfo.getOrgSid() != null && "04".equals(authUserInfo.getUserType())){
            if (!ObjectUtils.isEmpty(bizBillingAccount.getDistributorId())){
                if (!bizBillingAccount.getDistributorId().equals(authUserInfo.getOrgSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, "越权操作");
                }
            } else if (Objects.nonNull(authUserInfo.getOrgSid())) {
                Org org = orgMapper.selectById(bizBillingAccount.getOrgSid());
                Optional.ofNullable(org).ifPresent(o->{
                    if (o.getParentId() != null && !o.getParentId().equals(authUserInfo.getOrgSid())){
                        throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, "越权操作");
                    }
                });
            }
        }

        long count = sysBssEntityMapper.selectEntityById(authUserInfo.getUserSid(), bizBillingAccount.getEntityId());
        if(count == 0 && authUserInfo.getParentSid() != null){
            count = sysBssEntityMapper.selectEntityById(authUserInfo.getParentSid(), bizBillingAccount.getEntityId());
        }
        Long adminSid = bizBillingAccount.getAdminSid();
        if(count <= 0 && !Objects.equals(adminSid, authUserInfo.getParentSid()) && !Objects.equals(adminSid, authUserInfo.getUserSid())){
            throw new BizException("运营实体数据越权,请确认当前用户的运营实体");
        }*/
        if (Objects.nonNull(bizBillingAccount.getOrgSid())) {
            bizBillingAccount.setLdapOu(orgService.selectLdapOuByOrgSid(bizBillingAccount.getOrgSid()));
        }
        String businessTag = bizBillingAccount.getBusinessTag();
        if (StringUtil.isNotEmpty(businessTag)) {
            String[] splitList = businessTag.split(";");
            List<String> tagList = new ArrayList<>(Arrays.asList(splitList));
            if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                if (!businessTag.contains(bizBillingAccount.getId().toString())) {
                    tagList.removeIf(s -> s.indexOf(
                            BusinessTagEnum.ARREARAGE.getTag()) == 0);
                } else {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) == 0) {
                            return BusinessTagEnum.ARREARAGE.getTag();
                        }
                        return s;
                    }).collect(Collectors.toList());
                }
                bizBillingAccount.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
            }
        }
        //租户被禁用，其子用户启用按钮置灰
        bizBillingAccount.setShowFlag(ONE);
        // 账号是否冻结
        bizBillingAccount.setFreezeStatus(NORMAL.equals(bizBillingAccount.getStatus()) ? ONE : ZERO);
        Org org = orgMapper.selectById(bizBillingAccount.getOrgSid());
        if (Objects.nonNull(org)) {
            bizBillingAccount.setCurrentOrg(BeanConvertUtil.convert(org, CurrentOrgVO.class));
            bizBillingAccount.setStrategyBufferPeriod(org.getStrategyBufferPeriod());
            bizBillingAccount.setBmsEnable(org.getBmsEnable());
            bizBillingAccount.setFreezingStrategy(org.getFreezingStrategy());
            bizBillingAccount.setContactName(org.getContactName());
            bizBillingAccount.setContactPhone(org.getContactPhone());
            bizBillingAccount.setContactEmail(org.getContactEmail());
            bizBillingAccount.setCustomizationInfo(org.getCustomizationInfo());
        }
        if (bizBillingAccount.getCreditLineDt() != null && !"".equals(bizBillingAccount.getCreditLineDt())) {
            if (UNLIMITED.equals(bizBillingAccount.getCreditLineDt())) {
                bizBillingAccount.setCreditLineDt(bizBillingAccount.getCreditLineDt());
            } else {
                bizBillingAccount.setCreditLineDt(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.timeStampDate(bizBillingAccount.getCreditLineDt()));
            }
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(ProductCodeEnum.MA_BMS.getProductCode());
        if (serviceCategory != null && serviceCategory.getEntityId() != null && RequestContextUtil.getEntityId() != null &&
                StringUtils.isNotBlank(serviceCategory.getPublishStatus()) && "succeed".equals(serviceCategory.getPublishStatus()) &&
                serviceCategory.getEntityId() - RequestContextUtil.getEntityId() == 0) {
            bizBillingAccount.setBmsIsHide(false);
        }

        return bizBillingAccount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recharge(RechargeBillingAccountRequest request) {
        BizBillingAccount billingAccount = this.baseMapper.selectByPrimaryKey(request.getId());
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(billingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        billingAccount.setTotalRechargeAmount(billingAccount.getTotalRechargeAmount().add(request.getAmount()));
//        this.updateById(billingAccount);

        long count = sysBssEntityMapper.selectEntityById(authUser.getUserSid(), billingAccount.getEntityId());
        if(count<=0){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        if(Objects.nonNull(request.getContractSid())){
            BizContract bizContract = bizContractMapper.selectById(request.getContractSid());
            if (Objects.isNull(bizContract)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1156181913));
            }
        }
        User user = userMapper.selectByPrimaryKey(billingAccount.getAdminSid());
        if (!WebConstants.UserStatus.AVAILABILITY.equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        //更新余额,mybatis-plus乐观锁实现
//        BizBillingAccount updateAccount = new BizBillingAccount();
        billingAccount.setBalance(billingAccount.getBalance().add(request.getAmount()));
//        billingAccount.setId(request.getId());
//        billingAccount.setAccountName(billingAccount.getAccountName());
//        billingAccount.setVersion(billingAccount.getVersion());
//        billingAccount.setOrgSid(billingAccount.getOrgSid());
//        billingAccount.setBalanceCash(billingAccount.getBalanceCash());
//        billingAccount.setCreditLine(billingAccount.getCreditLine());
//        billingAccount.setDistributorId(billingAccount.getDistributorId());
//        billingAccount.setDistributorName(billingAccount.getDistributorName());
//        billingAccount.setEntityId(billingAccount.getEntityId());
//        billingAccount.setEntityName(billingAccount.getEntityName());
//        billingAccount.setAdminSid(billingAccount.getAdminSid());
        if (!this.updateById(billingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
        }
        // 新增充值明细
        BizDeposit record = addDepositRecord(request, billingAccount);
        //新增账户收支明细
        bizAccountDealService.save(makeAccountDeal(record, billingAccount, RechargeTypeEnum.PLATFORM, true));
        //判断余额是否为负数，如果为负数则从信用额度和现金券账户中抵扣
        BigDecimal balance = billingAccount.getBalance();
        if(balance.compareTo(BigDecimal.ZERO) < 0){
            //账户为负数时平账
            doRechargeBalance(billingAccount);
        }
        // 修改用户标识
        updateBusinessArrearageTag(billingAccount, user);
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        CompletableFuture.runAsync(new TraceRunnable(tracer,spanNamer,()->{
            RequestContextHolder.setRequestAttributes(requestAttributes);
            RestResult restResult = this.doUnfreezeUser(billingAccount);
            log.warn("BizBillingAccountServiceImpl_recharge_doUnfreezeUser_result_[{}]", JSON.toJSONString(restResult));
        }),cloudExecutor);
        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("entityName", billingAccount.getEntityName());
        messageContent.put("accountType", "现金账户");
        if (BigDecimal.ZERO.compareTo(request.getAmount()) <= 0) {
            messageContent.put("action", "充值到账");
        }else {
            messageContent.put("action", "扣款");
        }
        messageContent.put("percent", String.valueOf(Math.abs(Float.parseFloat(request.getAmount().toString()))));
        sysMsgService.sendBssMessage(billingAccount.getAdminSid(), messageContent,NotificationConsts.ConsoleMsg.FinanceMsg.TENANT_BALANCE_RECHARGE, null,billingAccount.getEntityId());
        return Boolean.TRUE;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean orderPosting(MarketOrderPostingRequest request) {
        QueryWrapper<ServiceOrderPriceDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ServiceOrderPriceDetail::getOrderSn,request.getOrderSn());
        List<ServiceOrderPriceDetail> priceDetails = orderPriceDetailService.list(queryWrapper);
        if (CollectionUtil.isEmpty(priceDetails)) {
            throw new BizException(MessageUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        request.setAmount(priceDetails.get(0).getTradePrice());

        QueryWrapper<BizBillingAccount> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.lambda()
                           .eq(BizBillingAccount::getAdminSid, request.getAdminSid())
                           .eq(BizBillingAccount::getEntityId, request.getEntityId());
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(accountQueryWrapper);
        User user = userMapper.selectByPrimaryKey(request.getAdminSid());
        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
            if (Objects.nonNull(user.getOrgSid())) {
                throw new BizException(MessageUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
            //自营商品,保证后续逻辑不空指针就行
            BizBillingAccount billingAccount = new BizBillingAccount();
            billingAccount.setAdminSid(request.getAdminSid());
            bizBillingAccounts.add(billingAccount);
        }
        BizBillingAccount billingAccount = bizBillingAccounts.get(0);
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();

        String remark = SETTLEMENT;
        String superviseStatus = COMPLETED;
        Boolean existToken = false;
        if (TradeType.REFUND.equals(request.getTradeType())) {
            remark = REFUND;
            superviseStatus = CANCELLED;
            existToken = true;
            billingAccount.setBalance(billingAccount.getBalance().add(request.getAmount()));
            Query lineQuery = new Query();
            org.springframework.data.mongodb.core.query.Criteria cl =
                    org.springframework.data.mongodb.core.query.Criteria.where("orderSourceSn").in(request.getOrderSn());
            lineQuery.addCriteria(cl);
            List<InstanceGaapCost> list = mongoTemplate.find(lineQuery, InstanceGaapCost.class);
            if (CollectionUtil.isNotEmpty(list)) {
                org.springframework.data.mongodb.core.query.Criteria we =
                        org.springframework.data.mongodb.core.query.Criteria.where("orderId")
                                                                            .in(list.stream().map(InstanceGaapCost::getOrderId).collect(Collectors.toSet()));
                Update update = new Update()
                        .set("invoicable", BooleanEnum.NO.getCode());
                mongoTemplate.updateFirst(new Query(we), update, InstanceGaapCost.class, "biz_bill_usage_item");
            }
            if (!this.updateById(billingAccount)) {
                throw new BizException(MessageUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
            }
        }
        boolean showFlag = verifyTenantInOrg(request);

        //新增账户收支明细
        BizDeposit record = new BizDeposit();
        record.setDescription(String.format("订单【%s】%s", request.getOrderSn(), remark));
        record.setFlowId(String.format("DD%s", request.getOrderSn()));
        record.setAmountReceived(request.getAmount());
        record.setTradeType(request.getTradeType());
        BizAccountDeal bizAccountDeal = makeAccountDeal(record, billingAccount, RechargeTypeEnum.ACC_TCASH, existToken);
        bizAccountDeal.setEntityId(1L);
        bizAccountDeal.setOrderNo(showFlag ? request.getOrderSn() : null);
        bizAccountDeal.setSuperviseStatus(superviseStatus);
        bizAccountDealService.save(bizAccountDeal);

        return Boolean.TRUE;
    }

    /**
     * 验证组织中租户
     *
     * @param request 请求
     * @return boolean
     */
    private boolean verifyTenantInOrg(MarketOrderPostingRequest request) {
        Long orgSid = Optional.ofNullable(serviceOrderMapper.selectList(new LambdaQueryWrapper<ServiceOrder>().eq(ServiceOrder::getOrderSn,
                request.getOrderSn()))).orElse(Collections.emptyList()).stream().findFirst().orElse(new ServiceOrder()).getOrgSid();
        cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult<Boolean> result =
                Optional.ofNullable(ossFeignService.verifyTenantInOrg(request.getAdminSid(), orgSid)).orElse(new cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult<>(cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status.FAILURE, null, false));
        return result.getStatus();
    }


    public Boolean allowInvoicingById(String id) {
        Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("billNo").is(id));
        Update update = new Update();
        update.set("marketShopState", "true");
        mongoTemplate.updateFirst(query, update, BillBillingCycleCost.class, "biz_bill_billing_cycle");
        return true;
    }

    /**
     * 发送信息
     *
     * @param
     */
    private void sendMessage(User user, Map<String, String> messageContent, String msg1, String msg2, Long entityId) {

        messageContent.put("userAccount", user.getAccount());
        NotificationUtil.assembleBaseMessageContent(messageContent);

        // 系统名称
        if (msg1 != null) {
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(msg1);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setEntityId(entityId);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                        baseNotificationMqBean);
            } catch (Exception e) {
                log.error("共享资源池发送信息异常：", e.getMessage());
            }
        }
        //运营管理员发送消息

        if (msg2 != null) {
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            if (CollUtil.isNotEmpty(adminstrators)) {

                try {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds()
                            .addAll(adminstrators.stream()
                                    .map(User::getUserSid)
                                    .collect(Collectors.toSet()));
                    baseNotificationMqBean.setEntityId(entityId);
                    baseNotificationMqBean.setMsgId(msg2);
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER,
                            baseNotificationMqBean);
                } catch (Exception e) {
                    log.error("共享资源池发送信息异常：", e.getMessage());
                }
            }
        }
    }
    /**
     * 充值平账
     * 当充值后账户还是负数时执行的操作
     * */
    private void doRechargeBalance(BizBillingAccount updateAccount) {
        //获取到当前账户的余额、现金券、信用额度
        BigDecimal balance = Objects.isNull(updateAccount.getBalance())?BigDecimal.ZERO:updateAccount.getBalance();
        BigDecimal creditLine = Objects.isNull(updateAccount.getCreditLine())?BigDecimal.ZERO:updateAccount.getCreditLine();
        BigDecimal remMount = balance.add(creditLine);
        //所有账户余额加信用额度，加起来还是负数，抵扣信用额度并生成明细
        if(remMount.compareTo(BigDecimal.ZERO)<0){
            //当余额为负，信用额度为0时，收支明细中不增加调账记录
            if (creditLine.compareTo(BigDecimal.ZERO) != 0) {
                updateAccount.setBalance(balance);
                updateAccount.setCreditLine(BigDecimal.ZERO);
                //增加信用额度收支明细
                RechargeBillingAccountRequest rechargeBillingAccountRequest=new RechargeBillingAccountRequest();
                rechargeBillingAccountRequest.setAmount(creditLine.negate());
                rechargeBillingAccountRequest.setRechargeType(RechargeTypeEnum.ACC_CREDIT.getCode());
                rechargeBillingAccountRequest.setId(updateAccount.getId());
                rechargeBillingAccountRequest.setDescription("平台充值-余额欠费修改信用额度");
                BizDeposit record = addDepositRecord(rechargeBillingAccountRequest, updateAccount);
                //新增账户信用额度收支明细
                bizAccountDealService.save(makeAccountDeal(record, updateAccount, RechargeTypeEnum.ACC_CREDIT, true));

                BigDecimal upBalance = creditLine;
                updateAccount.setBalance(balance.add(upBalance));
                //增加信用额度收支明细
                RechargeBillingAccountRequest rechargeBillingAccountRequest2=new RechargeBillingAccountRequest();
                rechargeBillingAccountRequest2.setAmount(upBalance);
                rechargeBillingAccountRequest2.setRechargeType(RechargeTypeEnum.PLATFORM.getCode());
                rechargeBillingAccountRequest2.setId(updateAccount.getId());
                rechargeBillingAccountRequest2.setDescription("平台充值-余额欠费修改");
                BizDeposit record2 = addDepositRecord(rechargeBillingAccountRequest2, updateAccount);
                //新增账户信用额度收支明细
                bizAccountDealService.save(makeAccountDeal(record2, updateAccount, RechargeTypeEnum.ACC_TCASH, true));

            }
            //修改账户余额
            if (!this.updateById(updateAccount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
            }
        }else{


            updateAccount.setCreditLine(remMount);
            //增加信用额度收支明细
            RechargeBillingAccountRequest rechargeBillingAccountRequest=new RechargeBillingAccountRequest();
            rechargeBillingAccountRequest.setAmount(balance);
            rechargeBillingAccountRequest.setRechargeType(RechargeTypeEnum.ACC_CREDIT.getCode());
            rechargeBillingAccountRequest.setId(updateAccount.getId());
            rechargeBillingAccountRequest.setDescription("平台充值-余额欠费修改信用额度");
            BizDeposit record = addDepositRecord(rechargeBillingAccountRequest, updateAccount);
            //新增账户信用额度收支明细
            bizAccountDealService.save(makeAccountDeal(record, updateAccount, RechargeTypeEnum.ACC_CREDIT, true));

            updateAccount.setBalance(BigDecimal.ZERO);
            //增加现金券收支明细
            RechargeBillingAccountRequest rechargeBillingAccountRequest1=new RechargeBillingAccountRequest();
            rechargeBillingAccountRequest1.setAmount(balance.negate());
            rechargeBillingAccountRequest1.setRechargeType(RechargeTypeEnum.PLATFORM.getCode());
            rechargeBillingAccountRequest1.setId(updateAccount.getId());
            rechargeBillingAccountRequest1.setDescription("平台充值-余额欠费修改");
            BizDeposit record1 = addDepositRecord(rechargeBillingAccountRequest1, updateAccount);
            //新增账户现金券收支明细
            bizAccountDealService.save(makeAccountDeal(record1, updateAccount, RechargeTypeEnum.ACC_TCASH, true));

            if (!this.updateById(updateAccount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
            }
        }
    }

    @Override
    @Transactional
    public Boolean updateCreditLine(UpdateCreditLineRequest request) {
        BizBillingAccount account = Objects.requireNonNull(this.getById(request.getAccountId()), "账户不存在");
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        long count = sysBssEntityMapper.selectEntityById(authUserInfo.getUserSid(), account.getEntityId());
        if(count<=0){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        User user = userMapper.selectByPrimaryKey(account.getAdminSid());
        if (!WebConstants.UserStatus.AVAILABILITY.equals(user.getStatus())) {
            return false;
        }

        String creditLineString = request.getCreditLine()
                                         .setScale(5, RoundingMode.HALF_UP)
                                         .stripTrailingZeros()
                                         .toPlainString();
        BigDecimal creditLine = new BigDecimal(creditLineString);
        BigDecimal balance = account.getBalance();
        //获取信用额度日期
        String creditLineDt = request.getCreditLineDt();
        //入库
        account.setCreditLineDt(creditLineDt);
        //判断账户是否欠费
        if (account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            //修改余额，同时调整信用额度
            //调整后的新额度
            BigDecimal newBalance = creditLine.subtract(balance.negate());
            if (newBalance.compareTo(BigDecimal.ZERO) > 0) {
                account.setCreditLine(creditLine);
                //增加信用额度收支明细
                RechargeBillingAccountRequest rechargeBillingAccountRequest = new RechargeBillingAccountRequest();
                rechargeBillingAccountRequest.setAmount(creditLine);
                rechargeBillingAccountRequest.setRechargeType(RechargeTypeEnum.ACC_CREDIT.getCode());
                rechargeBillingAccountRequest.setId(request.getAccountId());
                rechargeBillingAccountRequest.setDescription("调整信用额度");
                rechargeBillingAccountRequest.setContractSid(request.getContractSid());
                BizDeposit record = addDepositRecord(rechargeBillingAccountRequest, account);
                //新增账户信用额度收支明细
                bizAccountDealService.save(makeAccountDeal(record, account, RechargeTypeEnum.ACC_CREDIT, true));

                account.setCreditLine(newBalance);
                //增加信用额度收支明细
                RechargeBillingAccountRequest creditLineRequest = new RechargeBillingAccountRequest();
                creditLineRequest.setAmount(balance);
                creditLineRequest.setRechargeType(RechargeTypeEnum.ACC_CREDIT.getCode());
                creditLineRequest.setId(request.getAccountId());
                creditLineRequest.setDescription("调整信用额度");
                creditLineRequest.setContractSid(request.getContractSid());
                BizDeposit record2 = addDepositRecord(creditLineRequest, account);
                //新增账户信用额度收支明细
                bizAccountDealService.save(makeAccountDeal(record2, account, RechargeTypeEnum.ACC_CREDIT, true));

                //修改余额
                account.setBalance(BigDecimal.ZERO);
                //增加余额收支明细
                RechargeBillingAccountRequest balanceRequest = new RechargeBillingAccountRequest();
                balanceRequest.setAmount(balance.negate());
                balanceRequest.setRechargeType(RechargeTypeEnum.PLATFORM.getCode());
                balanceRequest.setId(request.getAccountId());
                balanceRequest.setDescription("调整信用额度-修改欠费余额");
                balanceRequest.setContractSid(request.getContractSid());
                BizDeposit balanceRecord = addDepositRecord(balanceRequest, account);
                account.setCreditLine(balance.add(creditLine));
                //新增账户余额收支明细
                bizAccountDealService.save(makeAccountDeal(balanceRecord, account, RechargeTypeEnum.PLATFORM, true));

                //标记账单不可开票,异步执行
                CompletableFuture.runAsync(() -> markInvoicable(account, balance.negate()));
            } else {
                account.setBalance(account.getBalance().add(request.getCreditLine()));
                //增加余额收支明细
                RechargeBillingAccountRequest balanceRequest = new RechargeBillingAccountRequest();
                balanceRequest.setAmount(creditLine);
                balanceRequest.setRechargeType(RechargeTypeEnum.PLATFORM.getCode());
                balanceRequest.setId(request.getAccountId());
                balanceRequest.setDescription("调整信用额度-修改欠费余额");
                balanceRequest.setContractSid(request.getContractSid());
                BizDeposit balanceRecord = addDepositRecord(balanceRequest, account);
                //新增账户余额收支明细
                bizAccountDealService.save(makeAccountDeal(balanceRecord, account, RechargeTypeEnum.PLATFORM, true));
                //标记账单不可开票,异步执行
                CompletableFuture.runAsync(() -> markInvoicable(account, creditLine));
            }
        } else {
            //增加信用额度收支明细
            RechargeBillingAccountRequest rechargeBillingAccountRequest = new RechargeBillingAccountRequest();
            rechargeBillingAccountRequest.setAmount(
                    Objects.nonNull(account.getCreditLine()) ? creditLine.subtract(account.getCreditLine())
                            : creditLine);
            rechargeBillingAccountRequest.setRechargeType(RechargeTypeEnum.ACC_CREDIT.getCode());
            rechargeBillingAccountRequest.setId(request.getAccountId());
            rechargeBillingAccountRequest.setDescription("调整信用额度");
            rechargeBillingAccountRequest.setContractSid(request.getContractSid());
            account.setCreditLine(creditLine);
            BizDeposit record = addDepositRecord(rechargeBillingAccountRequest, account);
            //新增账户信用额度收支明细
            bizAccountDealService.save(makeAccountDeal(record, account, RechargeTypeEnum.ACC_CREDIT, true));
        }
        //更新余额,mybatis-plus乐观锁实现
//        BizBillingAccount updateAccount = new BizBillingAccount();
//        updateAccount.setBalance(account.getBalance());
//        updateAccount.setId(account.getId());
//        updateAccount.setAccountName(account.getAccountName());
//        updateAccount.setVersion(account.getVersion());
//        updateAccount.setOrgSid(account.getOrgSid());
//        updateAccount.setBalanceCash(account.getBalanceCash());
//        updateAccount.setCreditLine(account.getCreditLine());
//        updateAccount.setDistributorName(account.getDistributorName());
//        updateAccount.setDistributorId(account.getDistributorId());
//        updateAccount.setCreditLineDt(account.getCreditLineDt());
        if (!this.updateById(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
        }
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        CompletableFuture.runAsync(new TraceRunnable(tracer,spanNamer,()-> {
            RequestContextHolder.setRequestAttributes(requestAttributes);
            //解冻
            RestResult restResult = this.
                doUnfreezeUser(account);
            log.warn("BizBillingAccountServiceImpl_recharge_updateCreditLine_result_[{}]",
                JSON.toJSONString(restResult));
        }),cloudExecutor);
        // 修改用户标识
        updateBusinessArrearageTag(account, user);

        return true;
    }

    /**
     *
     * @param account
     * @param rechargeCreditAmount 账单抵扣的信用余额
     */
    private void markInvoicable(BizBillingAccount account, BigDecimal rechargeCreditAmount) {

        //查询最近的支付且余额为正的明细
        QueryWrapper<BizAccountDeal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(BizAccountDeal::getBalance,BigDecimal.ZERO)
                .eq(BizAccountDeal::getAccountSid,account.getId())
                .eq(BizAccountDeal::getTradeType, TradeTypeEnum.PAY.getCode())
                .orderByDesc(BizAccountDeal::getCreatedDt);
        queryWrapper.last("limit 1");
        BizAccountDeal bizAccountDeal = bizAccountDealService.getOne(queryWrapper);

        QueryWrapper<BizAccountDeal> lastBizNoQWrapper = new QueryWrapper<>();
        lastBizNoQWrapper.lambda().lt(BizAccountDeal::getBalance,BigDecimal.ZERO)
                .eq(BizAccountDeal::getTradeType, TradeTypeEnum.PAY.getCode())
                .eq(BizAccountDeal::getAccountSid,account.getId());

        //存在最近的支付且余额为正的明细则查询该订单之后的订单，否则查询所有余额为负支付订单
        if (Objects.nonNull(bizAccountDeal)){
            Date createdDt = bizAccountDeal.getCreatedDt();
            lastBizNoQWrapper.lambda().ge(BizAccountDeal::getCreatedDt,createdDt);
        }

        long count = bizAccountDealService.count(lastBizNoQWrapper);
        if (count > 0) {
            int pageSize = 50;
            long totalPage = count % pageSize > 0 ? count / pageSize + 1 : count / pageSize;
            for (int i = 0; i < totalPage; i++) {
                IPage<BizAccountDeal> bizAccountDealIPage = bizAccountDealService.page(new Page<>(i, pageSize), lastBizNoQWrapper);
                List<BizAccountDeal> records = bizAccountDealIPage.getRecords();
                for (BizAccountDeal record : records) {
                    if (NumberUtil.equals(rechargeCreditAmount, BigDecimal.ZERO)) {
                        return;
                    }
                    //修改账单
                    rechargeCreditAmount = updateBizBillingSubCredit(record.getBillNo(), rechargeCreditAmount);

                }

            }
        }
    }

    private BigDecimal updateBizBillingSubCredit(String billNo, BigDecimal rechargeCreditAmount) {
        if(NumberUtil.isGreater(rechargeCreditAmount,BigDecimal.ZERO)){
            Query query = new Query();
            org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
            criteria.and("billNo").is(billNo);
            query.addCriteria(criteria);
            InstanceGaapCost cost = mongoTemplate.findOne(query, InstanceGaapCost.class, "biz_bill_usage_item");
            if(cost !=null && StringUtil.isEmpty(cost.getInvoicable())){
                BigDecimal cashAmount = cost.getCashAmount();
                if(!NumberUtil.isGreater(cashAmount,BigDecimal.ZERO)){
                    return rechargeCreditAmount;
                }
                BigDecimal costRecCredit = cost.getRechargeCreditAmount();
                if(costRecCredit == null){
                    costRecCredit = BigDecimal.ZERO;
                }
                //查询当前账单收支明细中扣除的余额
                QueryWrapper<BizAccountDeal> lastBizNoQWrapper = new QueryWrapper<>();
                lastBizNoQWrapper.lambda().ge(BizAccountDeal::getBalance,BigDecimal.ZERO)
                        .eq(BizAccountDeal::getTradeType, TradeTypeEnum.PAY.getCode())
                        .eq(BizAccountDeal::getTradeChannel, RechargeTypeEnum.ACC_TCASH.getCode())
                        .eq(BizAccountDeal::getBillNo,billNo);
               BigDecimal payAmount =bizAccountDealService.list(lastBizNoQWrapper)
                       .stream().map(BizAccountDeal::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                BigDecimal remainRecCash = NumberUtil.sub(cashAmount, costRecCredit,payAmount);
                if(NumberUtil.isGreater(remainRecCash,rechargeCreditAmount)){
                    remainRecCash=rechargeCreditAmount;
                    rechargeCreditAmount=BigDecimal.ZERO;
                }else{
                    rechargeCreditAmount=rechargeCreditAmount.subtract(remainRecCash);
                }

                BigDecimal finalCostRecCredit = costRecCredit.add(remainRecCash);
                Update update = new Update();
                update.set("rechargeCreditAmount",finalCostRecCredit);
                if(NumberUtil.equals(finalCostRecCredit,cashAmount)){
                    update.set("invoicable", BooleanEnum.NO.getCode());
                }else if(NumberUtil.equals(finalCostRecCredit.add(payAmount),cashAmount)){
                    update.set("invoicable",BooleanEnum.YES.getCode());
                }
                Query udpateQuery = new Query();
                udpateQuery.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("billNo").is(billNo));
                mongoTemplate.updateFirst(udpateQuery,update,"biz_bill_usage_item");
                //如果该账单已经汇总，要更新汇总信息
                if("Y".equals(cost.getSummaryFlag())){
                    //更新月汇总数据
                    updateSummaryMonth(cost,remainRecCash);
                    //更新汇总数据
                    updateSummary(cost,remainRecCash);

                }
                //如果该账单已经入周期帐，更新周期帐
                if(cost.getBillBillingCycleId() !=null){
                    //更新周期帐
                    updateBillBillingCycle(cost.getBillBillingCycleId(),remainRecCash);
                }
            }
        }else{
            log.info("账单抵扣信用余额rechargeCreditAmount ={}",rechargeCreditAmount);
        }
        return rechargeCreditAmount;
    }

    /**
     * 更新月汇总数据
     * @param cost
     * @param remainCash
     */
    private void updateSummaryMonth(InstanceGaapCost cost, BigDecimal remainCash) {

        Query query = new Query();
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("orgSid").is(cost.getOrgSid()).and("billingCycle").is(cost.getBillingCycle());
        query.addCriteria(criteria);
        InstanceGaapCostSummaryMonth
                summaryMonth = mongoTemplate.findOne(query, InstanceGaapCostSummaryMonth.class, "biz_bill_usage_item_summary_month");
        BigDecimal rechargeCreditAmount = summaryMonth.getRechargeCreditAmount();
        if(rechargeCreditAmount ==null){
            rechargeCreditAmount = BigDecimal.ZERO;
        }

        Update update = new Update();
        update.set("rechargeCreditAmount",rechargeCreditAmount.add(remainCash));
        Query udpateQuery = new Query();
        udpateQuery.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(summaryMonth.getId()));
        mongoTemplate.updateFirst(udpateQuery,update,"biz_bill_usage_item_summary_month");
    }

    /**
     *
     * @param billBillingCycleId
     * @param remainCash
     */
    private void updateBillBillingCycle(String billBillingCycleId, BigDecimal remainCash) {

        Query query = new Query();
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("_id").is(billBillingCycleId);
        query.addCriteria(criteria);
        BillBillingCycleCost billBillingCycle = mongoTemplate.findOne(query, BillBillingCycleCost.class, "biz_bill_billing_cycle");
        BigDecimal rechargeCreditAmount = billBillingCycle.getRechargeCreditAmount();
        if(rechargeCreditAmount ==null){
            rechargeCreditAmount = BigDecimal.ZERO;
        }

        Update update = new Update();
        update.set("rechargeCreditAmount",rechargeCreditAmount.add(remainCash));
        Query udpateQuery = new Query();
        udpateQuery.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(billBillingCycleId));
        mongoTemplate.updateFirst(udpateQuery,update,"biz_bill_billing_cycle");
    }

    /**
     * 更新汇总数据
     * @param cost
     * @param remainCash
     */
    private void updateSummary(InstanceGaapCost cost, BigDecimal remainCash) {

        Query query = new Query();
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("orgSid").is(cost.getOrgSid());
        query.addCriteria(criteria);
        InstanceGaapCostSummary summary = mongoTemplate.findOne(query, InstanceGaapCostSummary.class, "biz_bill_usage_item_summary");
        BigDecimal rechargeCreditAmount = summary.getRechargeCreditAmount();
        if(rechargeCreditAmount ==null){
            rechargeCreditAmount = BigDecimal.ZERO;
        }

        Update update = new Update();
        update.set("rechargeCreditAmount",rechargeCreditAmount.add(remainCash));
        Query udpateQuery = new Query();
        udpateQuery.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(summary.getId()));
        mongoTemplate.updateFirst(udpateQuery,update,"biz_bill_usage_item_summary");
    }

    @Override
    public IPage<BizDeposit> getDepositList(DescribeDepositRequest request) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(request.getAccountSid());
        long count = sysBssEntityMapper.selectEntityById(authUserInfo.getUserSid(), bizBillingAccount.getEntityId());
        if(count<=0){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.setOrderByClause("A.created_dt DESC, A.pay_status asc");
        IPage<BizDeposit> result = new Page<>();
        if (request.getAdminFlag() != null && !request.getAdminFlag()) {
            User user = getUserBaseInfo();
            criteria.put("accountSid", user.getAccountId());
        }
        if (request.getAccountSid() != null) {
            criteria.put("accountSid", request.getAccountSid());
        }
        if (PageUtil.isPageQuery(request)) {
            Page<BizDeposit> page = PageUtil.preparePageParams(request);
            result = this.bizDepositMapper.selectByParams(page, criteria);
        } else {
            result.setRecords(this.bizDepositMapper.selectByList(criteria));
        }
        //分销商查询则过滤
        if (authUserInfo.getOrgSid()!=null && authUserInfo.getOrgSid()!=0){
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(new QueryWrapper<BizBillingAccount>().eq("distributor_id", authUserInfo.getOrgSid()));
            List<Long> list = bizBillingAccounts.stream().map(BizBillingAccount::getOrgSid).collect(Collectors.toList());
            result.setRecords(result.getRecords().stream().filter(bizDeposit -> list.contains(bizDeposit.getOrgSid())).collect(Collectors.toList()));
        }
        //设置
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        result.getRecords().forEach(e -> {
            e.setPayStatusName(PayStatusEnum.codeFromName(e.getPayStatus()));
            e.setChannelName(RechargeTypeEnum.codeFromName(e.getChannel()));
            if (isUs) {
                String desc = e.getDescription();
                e.setDescription(this.descToUs(desc));
            }
        });
        return result;
    }

    private String descToUs(String desc) {
        desc = desc.replaceAll("平台充值", "Platform recharge")
                .replaceAll("信用余额充值", "Credit balance recharge")
                .replaceAll("合同调整信用额度", "Contract adjustment of credit limit")
                .replaceAll("微信支付充值", "WeChat Pay Recharge")
                .replaceAll("支付宝充值", "Alipay recharge")
                .replaceAll("充值现金券充值", "Recharge with cash coupons")
                .replaceAll("调整信用额度", "Adjust credit limit")
                .replaceAll("修改欠费余额", "Modify outstanding balance")
                .replaceAll("余额欠费修改信用额度", "Balance arrears, modify credit limit")
                .replaceAll("AI开发平台专属资源池", "Ascend Modelarts Exclusive Resource Pool")
                .replaceAll("资源费用", "Resource costs")
                .replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                .replaceAll("套餐包费用", "Package fee")
                .replaceAll("对象存储", "Object Storage")
                .replaceAll("优惠券抵扣", "Coupon deduction")
                .replaceAll("元", "yuan")
                .replaceAll("在线充值", "Online recharge");
        return desc;
    }

    @Override
    public Boolean updateDeposit(UpdateDepositRequest request) {
        // 充值明细是否存在
        BizDeposit record = bizDepositMapper.selectByPrimaryKey(Long.valueOf(request.getId()));
        if (Objects.isNull(record)) {
            BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_QUERY_FAILURE));
        }
        // 所关联合同是否存在
        if (NoUtil.str2Long(request.getContractSid()) > 0) {
            BizContract contract = bizContractMapper.selectById(Long.valueOf(request.getContractSid()));
            if (Objects.isNull(contract)) {
                BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_QUERY_FAILURE));
            }
        }
        BizDeposit deposit = BeanConvertUtil.convert(request, BizDeposit.class);
        WebUserUtil.prepareUpdateParams(deposit);
        deposit.setContractSid(NoUtil.str2Long(request.getContractSid()));
        return bizDepositMapper.updateById(deposit) > 0;
    }

    @Override
    public Map<String, Object> getAccountOverviews(Boolean isShieldedProducts) {
        Map<String, Object> returnMap = new HashMap<>(5);
        // 1、用户信息,每次从数据库获取用户名称，上次登录时间，账户信息等。
        returnMap.put("user", makeUserInfoOverview());
        // 2、待办中心数据
        returnMap.put("waitingCenter", makeTodoOverview());

        // 3、推荐产品ID
        returnMap.put("product", getRecommendProduct());

        returnMap.put("overViews", this.getOverViews(isShieldedProducts));
        return returnMap;
    }

    @Override
    public BizBillingAccount getBizBillingAccountByOrgId(Long orgId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long entityId = Optional.ofNullable(authUserInfo)
                .map(AuthUser::getParentSid)
                .map(sysBssEntityMapper::selectEntityByUserSid)
                .orElse(authUserInfo.getEntityId());
        // 账户信息
        return this.baseMapper.selectByOrgIdAndEntityId(orgId, entityId);
    }

    @Override
    public Object getBizBillingAccountIdByOrgIdOrEntityId(AuthUser authUserInfo) {
        List<BizBillingAccount> bizBillingAccounts = this.baseMapper
                .selectListByOrgIdAndEntityId(authUserInfo.getOrgSid(), authUserInfo.getEntityId());
        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
            return null;
        }
        if (bizBillingAccounts.size() == 1) {
            return bizBillingAccounts.get(0).getId().toString();
        }
        return bizBillingAccounts.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
    }


    @Override
    public User getUserBaseInfo() {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            return null;
        }
        User currentUser = this.userMapper.selectByPrimaryKey(authUserInfo.getUserSid());
        if (currentUser == null) {
            BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_QUERY_FAILURE));
        }
        currentUser.setAdminFlag(authUserInfo.getAdminFlag());
        // 设置账户信息
        QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("admin_sid", currentUser.getUserSid());
        List<BizBillingAccount> accounts = this.list(queryWrapper);
        currentUser.setAccountIds(accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList()));
        return currentUser;
    }

    @Override
    public void reduceBalance(Long bizBillingAccountId, BigDecimal subtractAmount) {
        BizBillingAccount bizBillingAccount = this.getById(bizBillingAccountId);
        bizBillingAccount.setBalance(bizBillingAccount.getBalance().subtract(subtractAmount));
        this.updateById(bizBillingAccount);
    }

    @Override
    public void increaseBalance(Long bizBillingAccountId, BigDecimal addAmount) {
        BizBillingAccount bizBillingAccount = this.getById(bizBillingAccountId);
        bizBillingAccount.setBalance(bizBillingAccount.getBalance().add(addAmount));
        this.updateById(bizBillingAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAccount(UpdateDistributorRequest request) {
        BizBillingAccount account = Objects
                .requireNonNull(this.getById(request.getAccountId()),
                        "账户不存在");
        User user = userMapper.selectByPrimaryKey(account.getAdminSid());
        if (user == null) {
            throw new BizException(USER_NOT_EXIST);
        }
        if (user.getBusinessTag() != null && user.getBusinessTag().contains(
                cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.preOpen.getTag())) {
            throw new BizException(USER_PRE_OPEN);
        }
        //解决bug：将关联分销商的用户改为直营，接口空指针的问题
        if (Objects.nonNull(request.getDistributorId())){
            if (Objects.nonNull(request.getSalesmenId())) {
                if (100L==request.getSalesmenId()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1633824524));
                }
                //查询关联的角色是否包含分销商
                List<Role> roles = userMapper.selectRoleByUserSid(request.getSalesmenId());
                if(CollectionUtil.isNotEmpty(roles)){
                    List<Role> newRoles = roles.stream().filter(r-> "bss".equalsIgnoreCase(r.getRoleType()) || "bss-distributor".equalsIgnoreCase(r.getRoleType())).collect(Collectors.toList());
                    if(CollectionUtil.isEmpty(newRoles)){
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1814506067));
                    }
                }else{
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1814506067));
                }
            }
            BizDistributor bizDistributor = bizDistributorService.getById(request.getDistributorId());
            if (!Objects.equals(RequestContextUtil.getEntityId(), bizDistributor.getEntityId())) {
                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            Map<String,Object> query=new HashMap<>();
            query.put("distributorId",request.getDistributorId());
            List<BizBillingAccount> accounts = this.baseMapper.selectByList(query);
            //判断分销商下用户数量
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(request.getAccountId());
            //从其他分销商关联
            if(!request.getDistributorId().equals(bizBillingAccount.getDistributorId())){
                //判断分销商下用户数量
                if (bizDistributor.getCustomerMaxNum() !=null && accounts.size() >=bizDistributor.getCustomerMaxNum()){
                    throw new BizException(CUSTOMER_MAX_NUM);
                }
            }else{
                //自己关联自己
                BizBillingAccount bizBillingAccount1 =bizBillingAccountMapper.getBillingAccountByIdAndSalesmenId(request.getAccountId(),request.getSalesmenId(),request.getDistributorId());
                if(Objects.nonNull(bizBillingAccount1)){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_758595482));
                }
            }
        }

        account.setDistributorId(request.getDistributorId());
        account.setSalesmenId(request.getSalesmenId());
        UpdateWrapper<BizBillingAccount> updateAccount = new UpdateWrapper<>();
        updateAccount.lambda()
                .set(BizBillingAccount::getDistributorId, account.getDistributorId())
                .set(BizBillingAccount::getSalesmenId, account.getSalesmenId())
                .eq(BizBillingAccount::getId, account.getId());
        this.update(updateAccount);
        Long salesmenId = request.getSalesmenId();
        if (Objects.nonNull(salesmenId)) {
            this.getBaseMapper().updateSalesmenAccount(salesmenId, account.getId());
        }
        // 关联分销商的情况
        if (null != request.getDistributorId()) {
            //更新组织treepath，用于过滤数据
            Org orgIndex = new Org();
            orgIndex.setOrgSid(account.getOrgSid());
            orgIndex.setParentId(request.getDistributorId());
            Org parentOrg = orgMapper.selectById(request.getDistributorId());
            orgIndex.setTreePath("/" + request.getDistributorId() + parentOrg.getTreePath());
            orgMapper.updateById(orgIndex);

            //组织下的项目更新treepath
            QueryWrapper<Org> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Org::getParentId, account.getOrgSid());
            List<Org> subOrgList = orgMapper.selectList(queryWrapper);
            subOrgList.forEach(subOrg -> {
                subOrg.setTreePath("/" + subOrg.getParentId() + orgIndex.getTreePath());
                orgMapper.updateById(subOrg);
            });
            // 更新业务标识已备案
            String businessTag = user.getBusinessTag();
            List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
            List<String> tagListNew = new ArrayList<>();
            for (String data : tagList) {
                List<String> stringList = BusinessTagEnum.getTagByType(1);
                if (stringList.contains(data)) {
                    data = BusinessTagEnum.RECORDED.getTag();
                }
                tagListNew.add(data);
            }
            user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagListNew, ";"));
        }
        // 关联直营的情况
        else {
            Org orgIndex = new Org();
            orgIndex.setOrgSid(account.getOrgSid());
            orgIndex.setParentId(null);
            orgIndex.setTreePath("/");
            LambdaUpdateWrapper<Org> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Org::getOrgSid,orgIndex.getOrgSid());
            updateWrapper.set(Org::getParentId,null);
            updateWrapper.set(Org::getTreePath,"/");
            orgMapper.update(orgIndex,updateWrapper);

            //组织下的项目更新treepath
            QueryWrapper<Org> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Org::getParentId, account.getOrgSid());
            List<Org> subOrgList = orgMapper.selectList(queryWrapper);
            subOrgList.forEach(subOrg -> {
                subOrg.setTreePath("/" + subOrg.getParentId() + "/");
                orgMapper.updateById(subOrg);
            });
            // 更新业务标识已备案
            String businessTag = user.getBusinessTag();
            List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
            List<String> tagListNew = new ArrayList<>();
            for (String data : tagList) {
                if (BusinessTagEnum.RECORDED.getTag().equals(data)) {
                    data = BusinessTagEnum.EXPANSION.getTag();
                }
                tagListNew.add(data);
        }
            user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagListNew, ";"));
        }
        // 修改用户业务标识Tag
        User modifyTag = new User();
        modifyTag.setUserSid(user.getUserSid());
        modifyTag.setBusinessTag(user.getBusinessTag());
        userMapper.updateByPrimaryKeySelective(modifyTag);
        userSyncRemoteService.updateUser(BeanUtil.copyProperties(modifyTag, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        updateBusinessArrearageTag(account, user);
        return true;
    }

    /**
     * 新增充值记录
     */
    private BizDeposit addDepositRecord(RechargeBillingAccountRequest request, BizBillingAccount billingAccount) {
        RechargeTypeEnum rechargeEnum = RechargeTypeEnum.getEnum(request.getRechargeType());
        switch (rechargeEnum) {
            case PLATFORM:
                return addPlatformDepositRecord(request.getId(), request.getAmount(), request.getContractSid(), request.getDescription(), billingAccount, PayStatusEnum.PAID, RechargeTypeEnum.PLATFORM);
            case COUPON:
            case ALI_PAY:
            case ACC_CREDIT:
                return addPlatformDepositRecord(request.getId(), request.getAmount(), request.getContractSid(), request.getDescription(), billingAccount, PayStatusEnum.PAID, RechargeTypeEnum.ACC_CREDIT);
            case ACC_BCASH:
                return addPlatformDepositRecord(request.getId(), request.getAmount(), request.getContractSid(), request.getDescription(), billingAccount, PayStatusEnum.PAID, RechargeTypeEnum.ACC_BCASH);
            case UNION_PAY:
                return addPlatformDepositRecord(request.getId(), request.getAmount(), request.getContractSid(), request.getDescription(), billingAccount, PayStatusEnum.PAID, RechargeTypeEnum.UNION_PAY);
                default:
                break;
        }
        return null;
    }

    /**
     * 新增平台充值明细
     */
    @Override
    public BizDeposit addPlatformDepositRecord(Long accountSid, BigDecimal amount, String contractSid, String description, BizBillingAccount billingAccount, PayStatusEnum payStatusEnum, RechargeTypeEnum rechargeTypeEnum) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        BizDeposit deposit = new BizDeposit();
        deposit.setAccountSid(accountSid);
        deposit.setPayTime(new Date());
        deposit.setChannel(rechargeTypeEnum.getCode());
        deposit.setAmountDeposit(amount);
        deposit.setAmountReceived(amount);
        deposit.setContractSid(NoUtil.str2Long(contractSid));
        deposit.setPayStatus(payStatusEnum.getCode());
        if (description == null) {
            description = StringUtil.EMPTY;
        }
        description = "[" + rechargeTypeEnum.getName() + "充值" + "]" + description;
        deposit.setDescription(description);
        deposit.setFlowId(NoUtil.getFlowId(FLOW_PREFIX));
        deposit.setActionUserSid(authUser.getUserSid());
        deposit.setActionUserName(authUser.getRealName());
        deposit.setOrgSid(billingAccount.getOrgSid());
        deposit.setUserSid(billingAccount.getAdminSid());
        WebUserUtil.prepareInsertParams(deposit);
        bizDepositMapper.insert(deposit);
        return deposit;
    }

    /**
     * 新增收支明细
     */
    @Override
    public BizAccountDeal makeAccountDeal(BizDeposit deposit, BizBillingAccount billingAccount, RechargeTypeEnum rechargeTypeEnum, boolean existToken) {
        BizAccountDeal accountDeal = new BizAccountDeal();
        accountDeal.setFlowNo(NoUtil.generateNo(DEAL_PREFIX));
        accountDeal.setType(DealType.IN);
        accountDeal.setTradeType(TradeType.CHARGE);
        accountDeal.setTradeChannel(rechargeTypeEnum.getCode());
        accountDeal.setTradeNo(deposit.getFlowId());
        if (rechargeTypeEnum.equals(RechargeTypeEnum.ALI_PAY) || rechargeTypeEnum.equals(RechargeTypeEnum.WECHAT_PAY)
                || rechargeTypeEnum.equals(RechargeTypeEnum.ACC_BCASH) || rechargeTypeEnum.equals(
                RechargeTypeEnum.UNION_PAY)) {
            accountDeal.setTradeNo(deposit.getThirdPaymentNo());
        }
        accountDeal.setRemark(deposit.getDescription());
        accountDeal.setBillingCycle(LocalDate.now().format(DateTimeFormatter.ofPattern(
                cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.MONTH_PATTERN)));
        accountDeal.setAmount(deposit.getAmountReceived());
        accountDeal.setBalance(billingAccount.getBalance());
        accountDeal.setBalanceCredit(billingAccount.getCreditLine());
        accountDeal.setBalanceCash(billingAccount.getBalanceCash());
        accountDeal.setAccountSid(billingAccount.getId());
        accountDeal.setAccountName(billingAccount.getAccountName());
        accountDeal.setOrgSid(billingAccount.getOrgSid());
        accountDeal.setUserSid(billingAccount.getId());
        accountDeal.setEntityId(billingAccount.getEntityId());
        accountDeal.setEntityName(billingAccount.getEntityName());
        accountDeal.setDealTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        if (existToken) {
            WebUserUtil.prepareInsertParams(accountDeal);
        } else {
            Date date = new Date();
            log.info("BizBillingAccountServiceImpl.makeAccountDeal-adminSid: {}", billingAccount.getAdminSid());
            User user = userMapper.selectByPrimaryKey(billingAccount.getAdminSid());
            accountDeal.setCreatedBy(user.getAccount());
            accountDeal.setCreatedDt(date);
            accountDeal.setUpdatedBy(user.getAccount());
            accountDeal.setUpdatedDt(date);
            accountDeal.setVersion(1L);
        }
        return accountDeal;
    }

    /**
     * 概览页-设置用户信息
     */
    private User makeUserInfoOverview() {
        return this.getUserBaseInfo();
    }

    /**
     * 概览页-待办中心
     */
    private List<WaitingCenterVO> makeTodoOverview() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            BizException.throwException(RestConst.HttpConst.Unauthorized.getType());
        }
        List<WaitingCenterVO> waitingCenterVOS = new ArrayList<>();
        QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("org_sid", authUser.getOrgSid());
        List<BizBillingAccount> accounts = this.list(queryWrapper);
        for (BizBillingAccount account : accounts) {
            WaitingCenterVO waitingCenter = new WaitingCenterVO();
            waitingCenter.setEndTime(account.getEndTime());
            waitingCenter.setActionStrategy(account.getActionStrategy());
            waitingCenter.setAccountId(account.getId());
            // 账户名称=实体名称
            waitingCenter.setEntityName(account.getEntityName());
            // 状态
            waitingCenter.setStatus(account.getStatus());
            // 账户关联产品
            waitingCenter.setProducts(sfProductCategoryCatalogService.findProductByAccountId(account.getId()));
            Criteria example = new Criteria();
            ResAllParams resAllParams = new ResAllParams();
            if (sysUserService.checkUserHasSomeAccess(Objects.requireNonNull(AuthUtil.getAuthUser()).getUserSid(),
                    AuthConstants.TENANT_ACCESS)) {
                example.put("orgSid", AuthUtil.getAuthUser().getOrgSid());
                resAllParams.setOrgSid(AuthUtil.getAuthUser().getOrgSid());
            } else {
                QueryWrapper<UserOrg> userOrgQuery = new QueryWrapper<>();
                userOrgQuery.lambda().eq(UserOrg::getUserSid, AuthUtil.getAuthUser().getUserSid());
                List<UserOrg> userOrgList = sysUserOrgMapper.selectList(userOrgQuery);
                example.put("projectIds", userOrgList.stream().map(UserOrg::getOrgSid).distinct()
                        .collect(Collectors.toList()));
                resAllParams.setProjectIds(userOrgList.stream().map(UserOrg::getOrgSid).distinct()
                        .collect(Collectors.toList()));
            }

            // 1、资源即将到期个数
            resAllParams.setChargeType("PrePaid");
            resAllParams.setStatusIn(normalList);
            waitingCenter.setExpireCount(this.sfProductResourceMapper.countSfExpire(example));
            // 2、资源待开通个数
            waitingCenter.setOrderCount(this.statisticsMapper.countServiceOrder(example));
            // 3、工单待处理个数
            waitingCenter.setWorkOrderCount(
                    this.statisticsMapper.countWorkOrder(new Criteria("orgSid", authUser.getOrgSid())));

            // 4、账户余额
            waitingCenter.setAccountBalance(BigDecimalUtil.getTwoPointAmount(account.getBalance()));
            // 5、本月消费明细
            example.put("subscriptionType", "PayAsYouGo");
            waitingCenter.setCurrentMonthAmount(this.statisticsMapper.selectUserCurrentConsume(example));
            // 6、优惠券数量和七天内到期
            WaitingCenterVO waitingCenterVO = this.statisticsMapper.countCoupon(account.getId());
            waitingCenter.setCouponCount(waitingCenterVO.getCouponCount());
            waitingCenter.setExpireCouponCount(waitingCenterVO.getExpireCouponCount());
            // 7、现金券余额
            waitingCenter.setCashAmount(BigDecimalUtil.getTwoPointAmount(account.getBalanceCash()));
            // 8、抵扣现金券余额
            QueryWrapper<CashCoupon> cashCouponQuery = new QueryWrapper<>();
            cashCouponQuery.eq("account_id", account.getId());
            cashCouponQuery.eq("type", "deduct");
            cashCouponQuery.eq("status", CouponStatusEnum.AVAILABLE.getCode());
            List<CashCoupon> cashCoupons = cashCouponMapper.selectList(cashCouponQuery);
            if (CollectionUtils.isNotEmpty(cashCoupons)) {
                BigDecimal index = BigDecimal.valueOf(
                        cashCoupons.stream().mapToDouble(t -> Double.parseDouble(t.getBalance())).sum());
                waitingCenter.setDeductCashAmount(index.setScale(5, RoundingMode.HALF_UP));
            } else {
                waitingCenter.setDeductCashAmount(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            // 信用额度期限时间
            if (account.getCreditLineDt() != null && !"".equals(account.getCreditLineDt())) {
                if (UNLIMITED.equals(account.getCreditLineDt())) {
                    waitingCenter.setCreditLineDt(account.getCreditLineDt());
                } else {
                    waitingCenter.setCreditLineDt(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.timeStampDate(account.getCreditLineDt()));
                }
            } else {
                waitingCenter.setCreditLineDt("");
            }
            // 计算可用信用额度
            if (Objects.isNull(account.getCreditLine())) {
                waitingCenter.setCreditLine(new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                waitingCenter.setCreditLine(BigDecimalUtil.getTwoPointAmount(account.getCreditLine()));
            }
            // 信用额度期限时间
            if (account.getCreditLineDt() != null && !"".equals(account.getCreditLineDt())) {
                if (UNLIMITED.equals(account.getCreditLineDt())) {
                    waitingCenter.setCreditLineDt(account.getCreditLineDt());
                } else {
                    waitingCenter.setCreditLineDt(cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.timeStampDate(account.getCreditLineDt()));
                }
            } else {
                waitingCenter.setCreditLineDt("");
            }
            //查询卡时包
            BizBagUser bizBagUser = bizBagUserService.lambdaQuery()
                                                     .eq(BizBagUser::getOwnerId, account.getAdminSid())
                                                     .eq(BizBagUser::getEntityId, account.getEntityId())
                                                     .eq(BizBagUser::getBagType, StatusType.CARD_HOUR)
                                                     .in(BizBagUser::getStatus,Arrays.asList(BizBagUserStatus.AVAILABLE,BizBagUserStatus.NOT_USED))
                                                     .orderByAsc(BizBagUser::getStartTime)
                                                     .list()
                                                     .stream()
                                                     .findFirst()
                                                     .orElse(new BizBagUser());
            waitingCenter.setCardHourValue(Optional.ofNullable(bizBagUser.getBagValue()).orElse(BigDecimal.ZERO));
            waitingCenter.setCardHourExpiredDate(bizBagUser.getEndTime());
            waitingCenterVOS.add(waitingCenter);
        }
        return waitingCenterVOS;
    }

    /**
     * 获取推荐产品ID
     */
    private Map<String, Long> getRecommendProduct() {
        List<MarketCatalogVO> catalogs = this.baseMapper.selectProductCatalogWithService();
        if (CollectionUtil.isEmpty(catalogs)) {
            return null;
        }
        catalogs = catalogs.stream().filter(marketCatalog -> PRODUCT_TYPE.contains(marketCatalog.getCatalogName()))
                .collect(Collectors.toList());

        Map<String, Long> map = Maps.newHashMap();
        try {
            if (CollectionUtil.isNotEmpty(catalogs)) {
                catalogs.forEach(catalog -> {
                    if (Objects.equals(catalog.getCatalogName(), ProductCatalogEnum.NETWORK.getCatalog())) {
                        Optional<MarketServiceVO> service = catalog.getServices().stream()
                                .filter(s -> Objects.equals(ProductCatalogEnum.NETWORK.getKey(), s.getServiceForm())).findFirst();
                        service.ifPresent(marketServiceVO -> map.put(marketServiceVO.getServiceForm(), marketServiceVO.getId()));

                    } else if (Objects.equals(catalog.getCatalogName(), ProductCatalogEnum.VOLUME.getCatalog())) {
                        Optional<MarketServiceVO> service = catalog.getServices().stream()
                                .filter(s -> Objects.equals(ProductCatalogEnum.VOLUME.getKey(), s.getServiceForm())).findFirst();
                        service.ifPresent(marketServiceVO -> map.put(marketServiceVO.getServiceForm(), marketServiceVO.getId()));
                    } else if (Objects.equals(catalog.getCatalogName(), ProductCatalogEnum.COMPUTE.getCatalog())) {
                        Optional<MarketServiceVO> service = catalog.getServices().stream()
                                .filter(s -> Objects.equals(ProductCatalogEnum.COMPUTE.getKey(), s.getServiceForm())).findFirst();
                        service.ifPresent(marketServiceVO -> map.put(marketServiceVO.getServiceForm(), marketServiceVO.getId()));
                    }
                });
            }
        } catch (Exception e) {
            log.error("获取推荐云产品失败！", e.getMessage());
        }
        return map;
    }

    @Override
    public Long countCustomers() {
        return this.baseMapper.countCustomers(RequestContextUtil.getAuthUserInfo().getUserSid());
    }

    /**
     * 获取用户总览信息
     *
     * @param isShieldedProducts 是否屏蔽没有上的产品
     * @return {@link OverViewVO}
     */
    private OverViewVO getOverViews(Boolean isShieldedProducts) {
        OverViewVO vo = new OverViewVO();
        //获取费用
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long userSid = authUserInfo.getUserSid();
        List<Long> accountIds = null;
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (!Objects.isNull(authUserInfo.getOrgSid())) {
            //查询accountId
            QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("org_sid", authUserInfo.getOrgSid());
            List<BizBillingAccount> accounts = this.list(queryWrapper);
            accountIds = accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(accountIds)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_558443810));
            }
        }
        BigDecimal historyExpense = new BigDecimal(0);
        BigDecimal currentMonthExpense = new BigDecimal(0);
        BigDecimal lastMonthExpense = new BigDecimal(0);





        Date lastStartTime = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getFirstDay();
        Date LastEndTime = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getLastDay();
        Date currFirstDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getMonthFistDay();
        Date currLastDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getMonthLastDay();


        List<Long> orgSids =  orgService.selectCustomerOrgSids(AuthUtil.getAuthUser().getOrgSid());

        //当月
        org.springframework.data.mongodb.core.query.Criteria curMonthCriteria = new org.springframework.data.mongodb.core.query.Criteria();

        if (userSid != null) {
            //判断是否是子账号,如果是子账户就显示父账号的数据
            User user = userMapper.selectByPrimaryKey(userSid);
            if (Objects.isNull(user)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_204509358));
        }
            userSid = user.getParentSid() == null ? userSid : user.getParentSid();
            curMonthCriteria.and("ownerId").is(userSid.toString());
        } else {
            curMonthCriteria.and("ownerId").is("-1");
        }
        curMonthCriteria.andOperator(
                org.springframework.data.mongodb.core.query.Criteria.where("payTime").gte(currFirstDay)
                , org.springframework.data.mongodb.core.query.Criteria.where("payTime").lte(currLastDay));

        Aggregation curAggregation = Aggregation.newAggregation(Aggregation.match(curMonthCriteria),
                                                                Aggregation.project("pretaxAmount"),
            Aggregation.group().sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("pretaxAmount").convertToDecimal(),2))).as("pretaxAmount")

        );

        AggregationResults<InstanceGaapAmount> curAggregate = mongoTemplate.aggregate(curAggregation,
                                                                                      "biz_bill_usage_item",
                                                                                      InstanceGaapAmount.class);

        List<InstanceGaapAmount> curMonthAmountList = curAggregate.getMappedResults();

        if (!CollectionUtil.isEmpty(curMonthAmountList)) {
            for (InstanceGaapAmount curCost : curMonthAmountList) {
                currentMonthExpense = currentMonthExpense.add(
                        curCost.getPretaxAmount() == null ? BigDecimal.ZERO
                                : curCost.getPretaxAmount());
            }
        }
        vo.setCurrentMonthExpense(currentMonthExpense);
        //上月
        org.springframework.data.mongodb.core.query.Criteria lastMonthCriteria = new org.springframework.data.mongodb.core.query.Criteria();
        if (userSid != null) {
            lastMonthCriteria.and("ownerId").is(userSid.toString());
        } else {
            lastMonthCriteria.and("ownerId").is("-1");
        }
        lastMonthCriteria.andOperator(
                org.springframework.data.mongodb.core.query.Criteria.where("payTime").gte(lastStartTime)
                , org.springframework.data.mongodb.core.query.Criteria.where("payTime").lte(LastEndTime));

        Aggregation lastMonthAggregation = Aggregation.newAggregation(Aggregation.match(lastMonthCriteria),
                                                                      Aggregation.project("pretaxAmount"),
            Aggregation.group().sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("pretaxAmount").convertToDecimal(),2))).as("pretaxAmount")
        );

        AggregationResults<InstanceGaapAmount> lastMonthAggregate = mongoTemplate.aggregate(lastMonthAggregation,
                                                                                            "biz_bill_usage_item",
                                                                                            InstanceGaapAmount.class);
        List<InstanceGaapAmount> lastMonthAmountList = lastMonthAggregate.getMappedResults();

        if (!CollectionUtil.isEmpty(lastMonthAmountList)) {
            for (InstanceGaapAmount lastCost : lastMonthAmountList) {
                lastMonthExpense = lastMonthExpense.add(
                        lastCost.getPretaxAmount() == null ? BigDecimal.ZERO
                                : lastCost.getPretaxAmount());
            }
        }
        vo.setLastMonthExpense(lastMonthExpense);


        //总金额
        org.springframework.data.mongodb.core.query.Criteria historyCriteria = new org.springframework.data.mongodb.core.query.Criteria();
        if (userSid != null && CollectionUtils.isNotEmpty(orgSids)) {
            historyCriteria.and("orgSid").in(orgSids);
        } else {
            historyCriteria.and("orgSid").is("-1");
        }
        Aggregation historyMonthAggregation = Aggregation.newAggregation(Aggregation.match(historyCriteria),
                                                                         Aggregation.project("pretaxAmount"),
            Aggregation.group().sum(RightCloudTrunc.truncValueOf(Arrays.asList(ConvertOperators.valueOf("pretaxAmount").convertToDecimal(),2))).as("pretaxAmount")


        );

        AggregationResults<InstanceGaapAmount> historyMonthAggregate = mongoTemplate.aggregate(historyMonthAggregation,
                                                                                               "biz_bill_usage_item_summary",
                                                                                               InstanceGaapAmount.class);
        List<InstanceGaapAmount> historyMonthAmountList = historyMonthAggregate.getMappedResults();

        if (CollectionUtil.isNotEmpty(historyMonthAmountList)) {
            for (InstanceGaapAmount historyCost : historyMonthAmountList) {
                historyExpense = historyExpense.add(
                        historyCost.getPretaxAmount() == null ? BigDecimal.ZERO : historyCost.getPretaxAmount());
            }
        }
        vo.setHistoryExpense(historyExpense);


        List<CloudDataCountVO> totalCloudData = new ArrayList<>();
        List<CloudDataCountVO> serviceCloudDataCount = new ArrayList<>();

        //获取云产品资源


        List<cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO> marketCatalogVOS
                = this.serviceOrderService.selectProductCatalogWithService();
        //获取服务单云产品信息
        List<cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo> orderList = new ArrayList<>();
        List<cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo> orderListIndex =
                this.serviceOrderService.selectCloudProductInfo(accountIds);
        orderList.addAll(orderListIndex);

        Map<String, ServiceOrderVo> collect = new HashMap<>();
        if(CollectionUtil.isNotEmpty(orderList)){
            // 跳过HPC
            if (skipHPCFunction) {
                orderList = orderList.stream().filter(order
                        -> !ProductCodeEnum.HPC.getProductCode().equals(order.getServiceType())
                                && !ProductCodeEnum.SFS.getProductCode().equals(order.getServiceType())).collect(Collectors.toList());
            }
            collect = orderList.stream().collect(Collectors.toMap(ServiceOrderVo::getServiceId, o -> o));
        }


        if(CollectionUtil.isNotEmpty(marketCatalogVOS) && marketCatalogVOS.size()>0){
            for (cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO marketCatalogVO : marketCatalogVOS) {
                List<cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO> services = marketCatalogVO.getServices();
                if(CollectionUtil.isNotEmpty(services) && services.size()>0 && orderList.size()>0 ){
                    for (cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO service : services) {
                        if (marketCatalogVO.getCatalogName() != null && marketCatalogVO.getCatalogName()
                                                                                       .contains("计算")) {
                            service.setServiceName(service.getProductName());
                        }
                        ServiceOrderVo serviceOrderVo = collect.get(String.valueOf(service.getId()));
                        if (serviceOrderVo != null) {
                            service.setQuantity(serviceOrderVo.getQuantity());
                        }
                    }
                    this.sortMarketServiceVO(services);
                    if (isShieldedProducts){
                        //屏蔽掉暂时没有上的产品
                        marketCatalogVO.setServices(services.stream()
                                                            .filter(s -> includeServiceType(s.getServiceType())
                                                            ).collect(Collectors.toList()));
                    }
                }
                // 弹性文件
                if (CollectionUtil.isNotEmpty(services) && services.size() > 0
                        && "存储".equalsIgnoreCase(marketCatalogVO.getCatalogName())) {
                    for (cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO service : services) {
                        ResShareParams resShare = new ResShareParams();
                        resShare.setCategoryId(service.getId());
                        int count = 0;
                        if (CollectionUtil.isNotEmpty(orgSids)) {
                            for (Long orgSid : orgSids) {
                                resShare.setOrgSid(orgSid);
                                count = count+shareRemoteService.countShareByParams(resShare);
                            }
                        } else{
                            Long currentOrgSid = AuthUtil.getCurrentOrgSid();
                            if(currentOrgSid !=null){
                                resShare.setOrgSid(currentOrgSid);
                                count = count+shareRemoteService.countShareByParams(resShare);
                            }
                        }
                        service.setQuantity(count);
                    }
                }

                //屏蔽掉暂时没有上的产品
                List<cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO> tempService = new ArrayList<>();
                if(services != null){
                    services.stream().forEach(s->{
                        String serviceType = s.getServiceType();
                        boolean b = ProductCodeEnum.MODEL_ARTS.getProductCode().equalsIgnoreCase(serviceType)
                                || ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equalsIgnoreCase(serviceType)
                                || ProductCodeEnum.HPC_DRP.getProductCode().equalsIgnoreCase(serviceType);

                        if (b || ProductCodeEnum.HPC.getProductCode().equalsIgnoreCase(serviceType)
                                || ProductCodeEnum.HPC_SAAS.getProductCode().equalsIgnoreCase(serviceType)
                        ){
                            tempService.add(s);
                        }
                    });
                }
                //只统计modelArts的和DRP的
                marketCatalogVO.setServices(tempService);
            }
        }


        vo.setCountVOS(marketCatalogVOS);
        return vo;
    }

    private static boolean includeServiceType(String serviceType) {
        return Stream.of(ProductCodeEnum.MODEL_ARTS.getProductCode(),
            ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode(),
            ProductCodeEnum.HPC_DRP.getProductCode(),
            ProductCodeEnum.HPC.getProductCode(),
            ProductCodeEnum.HPC_SAAS.getProductCode()).anyMatch(code -> code.equalsIgnoreCase(serviceType));
    }

    private List<cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO> sortMarketServiceVO(List<cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO> list) {
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO vo1 = (cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO) o1;
                cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO vo2 = (cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketServiceVO) o2;
                if (vo1.getQuantity() > vo2.getQuantity()) {
                    return -1;
                } else if (Objects.equals(vo1.getQuantity(), vo2.getQuantity())) {
                    return 0;
                } else {
                    return 1;
                }
            }
        });
        return list;
    }

    private List<CloudDataCountVO> sort(List<CloudDataCountVO> list) {
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                CloudDataCountVO vo1 = (CloudDataCountVO) o1;
                CloudDataCountVO vo2 = (CloudDataCountVO) o2;
                if (vo1.getQuantity() > vo2.getQuantity()) {
                    return -1;
                } else if (Objects.equals(vo1.getQuantity(), vo2.getQuantity())) {
                    return 0;
                } else {
                    return 1;
                }
            }
        });
        return list;
    }

    @Override
    public RestResult doUnfreezeUser(Long accountId) {
        BizBillingAccount billingAccount = this.baseMapper.selectById(accountId);
        return doUnfreezeUser(billingAccount);
    }

    @Override
    public RestResult doUnfreezeUser(BizBillingAccount billingAccount) {
        if (Objects.nonNull(billingAccount)) {
            if (Objects.isNull(billingAccount)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1777448447));
            }
            if(Objects.nonNull(billingAccount.getUnfreezeType()) && "1".equals(billingAccount.getUnfreezeType())){
                return new RestResult(RestResult.Status.FAILURE,WebUtil.getMessage(MsgCd.ERR_MSG_BSS_133625621));
            }

            // 拥有按量付费产品的组织
            List<Long> parentOrgSidByPostPaid = sfProductResourceMapper.getParentOrgSidByPostPaid();
            if (parentOrgSidByPostPaid.contains(billingAccount.getOrgSid())) {
                // 满足条件解冻账户
                thawStatus(billingAccount);
            }

        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1512721855));
        }
        return new RestResult(RestResult.Status.SUCCESS,WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1072498887));
    }




    private boolean isFreezeStatus(BizBillingAccount billingAccount) {
        return Objects.isNull(billingAccount.getFreezeStatus()) || !"1".equals(billingAccount.getFreezeStatus());
    }

    /**
     * 查看账户是否欠费
     * @param billingAccount
     * @return
     */
    private boolean checkAccountAmount(BizBillingAccount billingAccount,BigDecimal thresholdValue) {
        //总余额=账户余额+现金卷余额+信用额度
        BigDecimal balance = billingAccount.getBalance()==null?BigDecimal.ZERO:billingAccount.getBalance();
        BigDecimal balanceCash = billingAccount.getBalanceCash()==null?BigDecimal.ZERO:billingAccount.getBalanceCash();
        BigDecimal creditLine = billingAccount.getCreditLine()==null?BigDecimal.ZERO:billingAccount.getCreditLine();
        BigDecimal totalAmount = balance.add(balanceCash).add(creditLine).setScale(3,BigDecimal.ROUND_HALF_UP);
        log.info("用户余额：[{}]",totalAmount);
        return totalAmount
                .compareTo(thresholdValue) > -1;
    }

    private void changeUserFreeze(Long userSid) {
        List<User> users = userMapper.selectAllUserByUserSid(userSid);
        /**
         * 1.如果parentSid 为空 则代表租户管理员，则更新parentSid = userSid的所有账户
         * 2.如果parentSid 不为空， 则更新parentSid = parentSid的所有客户 以及 userSid = parentSid的主账号
         */
        if (CollectionUtil.isNotEmpty(users) && users.size() > 0) {
            for (User u : users) {
                User user = new User();
                user.setUserSid(u.getUserSid());
                //1:解冻，0:冻结
                user.setFreezeStatus("1");
                user.setUpdatedDt(new Date());
                user.setUnfreezeTime(new Date());
                userMapper.updateByPrimaryKeySelective(user);
                userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));
            }
        }
        User user = userMapper.selectByPrimaryKey(userSid);

        //启用 hpc
        Org org = orgService.selectRootOrg(user.getOrgSid());
//        List<SfProductResource> hpcStatus = hpcService.getHpcStatus(org.getOrgSid());
//        hpcStatus.forEach(hpc->{
//            if (SfProductEnum.FROZEN.getStatus().equals(hpc.getStatus())) {
//                SfProductResource sfProductResource = hpc;
//                sfProductResource.setStatus(SfProductEnum.NORMAL.getStatus());
//                BasicWebUtil.prepareUpdateParams(sfProductResource);
//                sfProductResourceMapper.updateByPrimaryKeySelective(sfProductResource);
//            }
//        });


        //修改hpc共享资源池状态
        changeHPCFreezeStatus(user.getOrgSid(), false);

        // 解冻权限设置
        resourcePermissionService.resourcePermissionAdd(userSid);
    }

    /**
     * 解冻
     * @param userSid
     */
    private void unFreeze(Long userSid) {

        try {
            if(Objects.nonNull(userSid)){
                User user = userMapper.selectByPrimaryKey(userSid);
                if(Objects.nonNull(user)){
                    ActionLog log = new ActionLog();
                    log.setAccount(user.getAccount());
                    log.setActionName("系统冻结解冻策略生效");
                    log.setTagName("系统冻结解冻策略生效:"+user.getAccount());
                    log.setResource("系统冻结解冻策略生效");
                    log.setActionTime(new Date());
                    log.setSuccess("成功");
                    HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
                    if (Objects.isNull(request)) {
                        return;
                    }
                    String remoteIp = IPAddressUtil.getRemoteHostIp(request);
                    String ipAddress = IPAddressUtil.getIpAddress();
                    log.setActionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request));
                    log.setActionMethod("BizBillingAccountServiceImpl.doUnfreezeUser");
                    log.setHttpMethod(request.getMethod());
                    log.setLbIp(remoteIp);
                    log.setRemoteIp(ipAddress);
                    log.setRoleName("系统");
                    addTraceId(log);
                    String userClient = request.getHeader("User-Agent");
                    log.setClient(userClient);
                    ActionLog action_log = mongoTemplate.insert(log, "action_log");
                }
            }
        } catch (Exception e) {
            log.error("BizBillingAccountServiceImpl_unFreeze_create_error_【{}】", JSON.toJSONString(e.getMessage()));
        }
    }

    @Override
    public List<BizBillingAccount> getSimpleAccountAndDistributorInfo(Map param) {
        return this.baseMapper.getSimpleAccountAndDistributorInfo(param);
    }

    @Override
    public List<BizBillingAccount> getByDistributorNameAndIds(String distributorName,Set<Long> ids) {
        return this.baseMapper.getByDistributorNameAndIds(distributorName,ids);
    }

    @Override
    public List<BizBillingAccount> selectAccountInfoByCustomerName(DescribeGaapCostRequest request) {
        return this.baseMapper.selectAccountInfoByCustomerName(request);
    }

    @Override
    public List<BizBillingAccount> accountInfExport(DescribeBillingAccountRequest request) {
        Criteria criteria = new Criteria();
        User authUser = AuthUtil.getAuthUser();
        List<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> roleList =roleMapper.selectByUserSid(authUser.getUserSid());
        List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = BeanConvertUtil.convert(roleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
        String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
        //运营实体下数据
        if(DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxScope)){
            request.setDistributorId(authUser.getOrgSid());
            //当前组织-个人及关联客户数据
        }else if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
            request.setSalesmenId(authUser.getUserSid());
        }
        // 查询分销商为[直营]的情况
        if (!StringUtil.isNullOrEmpty(request.getDistributorName())
                && "直营".equals(request.getDistributorName())) {
            request.setDistributorName(null);
            request.setDirect("1");
        }
        criteria.setConditionObject(request);
        criteria.put("orderByClause", " A.distributor_id asc,created_dt desc");
        criteria.put("entityId",authUser.getEntityId());
        DistributorAuthUtil.put(criteria, null, AuthUtil.getAuthUser(), 3, null);
        if (CollectionUtils.isNotEmpty(request.getOpenProductInformation())) {
            setOpenProductInformation(request.getOpenProductInformation(), criteria);
        }
        if (StringUtils.isNotBlank(request.getKeyword()) && !CCSPCacheUtil.ccspServiceOpen()) {
            criteria.put("keywordHash", DigestUtils.sha256Hex(request.getKeyword()));
        }
        List<BizBillingAccount> records  = this.baseMapper.selectByExport(criteria);
        Map<String, String> accountProductInfo = openProductInformation(RequestContextUtil.getEntityId());
        records.forEach(t -> {
            t.setOpenProductInformation(
                    StringUtil.isEmpty(accountProductInfo.get(t.getAccount())) ? accountProductInfo.get(t.getAccount())
                            : "--");
            if (StringUtil.isNullOrEmpty(t.getDistributorName())) {
                t.setDistributorName("直营");
            } else {
                t.setDistributorName(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getDistributorName()));
            }
            t.setFreezeStatusName(FREEZE_STATUS.get(t.getFreezeStatus()));
            if (StringUtil.isEmpty(t.getIndustryName())) {
                t.setIndustryName("--");
            } else {
                t.setIndustryName(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getIndustryName()));
            }
            if (StringUtil.isEmpty(t.getApplicationScenarioName())) {
                t.setApplicationScenarioName("--");
            } else {
                t.setApplicationScenarioName(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getApplicationScenarioName()));
            }
            if (StringUtil.isEmpty(t.getAddress())) {
                t.setAddress("--");
            } else {
                t.setAddress(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getAddress()));
            }
            if (StringUtil.isEmpty(t.getPersonnelSizeName())) {
                t.setPersonnelSizeName("--");
            }
            if (Objects.isNull(t.getCreditLine())) {
                t.setCreditLine(BigDecimal.ZERO);
            }
            if (Objects.isNull(t.getSalesmen())) {
                t.setSalesmen("--");
            }
            if (Objects.isNull(t.getRemark())) {
                t.setRemark("--");
            } else {
                t.setRemark(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getRemark()));
            }
            t.setAccountName(cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.specailCharacterFilter(t.getAccountName()));
        });
        return records;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response,String fileType) {
        String resourceName = "template/"+fileType+"-import-template.xlsx";
        try {
            // 判断模板是否存在
            if (FileUtil.exist(resourceName)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_130599775));
            }
            ClassPathResource classPathResource = new ClassPathResource(resourceName);
            InputStream in = classPathResource.getInputStream();
            String destFileName = Objects.equals(fileType,"customer")?"导入客户模板.xlsx":"导入子用户模板.xlsx";
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            OutputStream out = response.getOutputStream();
            byte[] buff = new byte[2048];
            int len;
            while ((len = in.read(buff)) != -1) {
                out.write(buff, 0, len);
            }
            out.flush();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            if (e instanceof FileNotFoundException) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1572881348));
            }
            log.error(resourceName + "下载失败! {}", e.getMessage());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1234800146));
        }

    }

    @Override
    public BizBillingAccount getByEntityIdAndUserId(Long entitySid, Long userSid) {
        return bizBillingAccountMapper.getByEntityIdAndUserId(entitySid, userSid);
    }

    @Override
    public BizBillingAccount getFirst() {
        return bizBillingAccountMapper.getFirst();
    }


    @Override
    public List<BizBillingAccount> getByAdminSid(Long adminSid) {
        return bizBillingAccountMapper.getByAdminSid(adminSid);
    }


    @Override
    public List<BizBillingAccount> getByOrgSid(Long orgSid) {
        return bizBillingAccountMapper.getByOrgSid(orgSid);
    }

    @Override
    public DescribeRechargeAccountInfoResponse getRechargeAccountDetail(Long accountId) {
      AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            BizException.throwException(RestConst.HttpConst.Unauthorized.getType());
        }
        BizBillingAccount bizBillingAccount = this.getById(accountId);
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        //子用户判断父id跟账户id是否相等
        if(authUser.getParentSid() != null){
            if(!authUser.getParentSid().equals(bizBillingAccount.getAdminSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }else{
            if(!authUser.getUserSid().equals(bizBillingAccount.getAdminSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }


        String account = bizBillingAccount.getEntityName();
        BigDecimal accountBalance = bizBillingAccount.getBalance().setScale(5, BigDecimal.ROUND_HALF_UP);
        BigDecimal cashAmount;
        if (Objects.isNull(bizBillingAccount.getCreditLine())) {
            cashAmount = new BigDecimal(0);
        } else {
            cashAmount = bizBillingAccount.getBalanceCash().setScale(5, BigDecimal.ROUND_HALF_UP);
        }
        return new DescribeRechargeAccountInfoResponse(account, accountBalance, cashAmount);
    }

    @Override
    public Map<String, Object> getCollectorInfo() {
        HashMap<String, Object> resMap = new HashMap<>();
        //客户账号
        cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria userCri = new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria();
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> users = userMapper.selectByExample(userCri);
        Map<Long, cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> userSidMap = new HashMap<>(10);
        Map<String, cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> userAcountMap = new HashMap<>(10);
//        users.stream().forEach(user -> {
        for (cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user : users) {
            String status = user.getStatus();
            if (!WebConstants.UserStatus.NOTAPPROVE.equals(status)
                    && !WebConstants.UserStatus.REJECTED.equals(status)
                    && !WebConstants.UserStatus.DELETED.equals(status)
                    && user.getOrgSid() != null) {
                userSidMap.put(user.getUserSid(), user);
                userAcountMap.put(user.getAccount(), user);
            }
        }
//        });

        //查询所有账户
        Map<Long, cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount> accountMap = new HashMap<>(10);
        cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria accountCri = new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria();
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount> accounts = bizBillingAccountMapper.selectByParamsForCollector(accountCri);
        accounts.stream().forEach(ac -> {
            accountMap.put(ac.getAdminSid(), ac);
        });
        resMap.put("userSidMap",userSidMap);
        resMap.put("userAcountMap",userAcountMap);
        resMap.put("accountMap",accountMap);
        return resMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setExpirationTime(SetExpirationTimeRequest request) {

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        //校验传入的账户id是否存在
        List<BizBillingAccount> account = bizBillingAccountMapper.getBillingAccountByEntityId(authUserInfo.getEntityId());
        AssertUtil.requireNonBlank(account, "当前运营实体下没有账户");
        Set<Long> accountId = account.stream().map(it -> it.getId()).collect(Collectors.toSet());
        List<BizBillingAccount> collect = account.stream().filter(it -> request.getId().contains(it.getId())).collect(Collectors.toList());
        List<Long> entityIds = collect.stream().map(BizBillingAccount::getEntityId).collect(Collectors.toList());
        Boolean flag = false;
        for (Long entityId : entityIds) {
            if (!authUserInfo.getEntityId().equals(entityId)) {
                flag = true;
            }
        }
        if(flag){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_43534325));
        }

        if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectBatchIds(request.getId());
            if (bizBillingAccounts.stream().anyMatch(e -> !Objects.equals(authUserInfo.getOrgSid(), e.getDistributorId()))) {
                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }
        List<Long> userId = collect.stream().map(it -> it.getAdminSid()).distinct().collect(Collectors.toList());
        List<String> userStatus = userMapper.selectStatusByIdList(userId);
        if(userStatus.contains("2")||userStatus.contains("4")){
            throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        //参数业务校验
        for (Long id : request.getId()) {
            if (!accountId.contains(id)) {
                log.info("账户{}不存在！", id);
                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.CUSTOMER_DOES_NOT_EXIST));
            }
        }
        //若为关闭状态，则到期时间和到期策略置为空
        if (!request.getStatus()) {
            request.setEndTime(null);
            request.setActionStrategy(null);
        }
        BizBillingAccount bizBillingAccount = new BizBillingAccount();

        bizBillingAccount.setEndTime(DateUtil.formatDateTime(request.getEndTime()));
        bizBillingAccount.setActionStrategy(request.getActionStrategy());
        bizBillingAccount.setSkipCCSPHandle(true);
        boolean success = bizBillingAccountMapper.updateEndTime(bizBillingAccount, request.getId());
        //查询管理员信息
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> adminstrators = userMapper.findAdminstrators();
        //查询用户信息
        QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BizBillingAccount::getId, request.getId());
        List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(queryWrapper);
        Map<Long, BizBillingAccount> accountToUserId = accounts.stream().collect(Collectors.toMap(BizBillingAccount::getId, o -> o));
        AssertUtil.requireNonBlank(accounts, "账号不存在！");
        List<Long> userIds = accounts.stream().map(it -> it.getAdminSid()).distinct().collect(Collectors.toList());
        List<cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser> userList = userMapper.selectBatchIds(userIds);
        Map<Long, String> idAccountMap = userList.stream()
                                                 .collect(Collectors.toMap(cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser::getUserSid,
                                                                           cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser::getAccount));
        List<String> des = new ArrayList<>();
        if(StringUtil.isNotBlank(request.getActionStrategy())){
            String[] split = request.getActionStrategy().split(",");
            for(String code : split){
                ExpireStrategyEnum desByCode = ExpireStrategyEnum.getDesByCode(code);
                if (ObjectUtil.isEmpty(desByCode)) {
                    BizException.e("处理策略不符合规范");
                }
                String codeDes = desByCode.getDes();
                des.add(codeDes);
            }
        }
        String strategy = des.isEmpty() ? null : parseListToStr(des);
        //发送设置到期时间启用和关闭通知
        if (success) {
            for (Long id : request.getId()) {
                //发送账号到期功能启用通知
                if (request.getStatus()) {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    Map<String, String> messageContent = new HashMap<>();
                    messageContent.put("userAccount", idAccountMap.get(accountToUserId.get(id).getAdminSid()));
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateString = formatter.format(request.getEndTime());
                    messageContent.put("endDate", dateString);
                    messageContent.put("action", strategy);
                    NotificationUtil.assembleBaseMessageContent(messageContent);
                    baseNotificationMqBean.setMsgId(ConsoleMsg.AccountMsg.TENANT_BSSACCOUNT_EXPIRED_ENABLE);
                    baseNotificationMqBean.setMap(messageContent);
                    baseNotificationMqBean.getImsgUserIds().add(accountToUserId.get(id).getAdminSid());
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT,
                                                  baseNotificationMqBean);
                    if (adminstrators != null) {
                        try {
                            baseNotificationMqBean.getImsgUserIds().clear();
                            baseNotificationMqBean.getImsgUserIds()
                                                  .addAll(adminstrators.stream()
                                                                       .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid)
                                                                       .collect(Collectors.toSet()));
                            baseNotificationMqBean.setMsgId(
                                    PlatformMsg.AccountMsg.BSSMGT_BSSACCOUNT_EXPIRED_ENABLE);
                            baseNotificationMqBean.setEntityId(accountToUserId.get(id).getEntityId());
                            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT,
                                                          baseNotificationMqBean);
                        } catch (Exception e) {
                            log.error("账号到期功能启用通知消息发送异常：", e);
                        }
                    }
                    AccountExpireMessage accountExpireMessage = new AccountExpireMessage();
                    accountExpireMessage.setId(id);
                    accountExpireMessage.setExpireStrategy(request.getActionStrategy());
                    Date nowDate = new Date();
                    long time = 10;
                    if (!nowDate.after(request.getEndTime())) {
                        time = DateUtil.betweenMs(nowDate, request.getEndTime());
                    }
                    final long delayMilliseconds = time;
                    //时间小于1天时才给延迟队列发消息，否则直接通过schedule每天的定时任务发
                    if(time < EXPIRE_TIME){
                        // 给延迟队列发送消息
                        amqpTemplate.convertAndSend(ExpireConstants.EXCHANGE_NAME, ACCOUNT_EXPIRE_ROUTING_KEY,
                                                    accountExpireMessage, (message) -> {
                                    // 给消息设置延迟毫秒值
                                    message.getMessageProperties().setHeader("x-delay", delayMilliseconds);
                                    return message;
                                });
                    }

                } else {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    Map<String, String> messageContent = new HashMap<>();
                    messageContent.put("userAccount", idAccountMap.get(accountToUserId.get(id).getAdminSid()));
                    NotificationUtil.assembleBaseMessageContent(messageContent);
                    baseNotificationMqBean.setMsgId(ConsoleMsg.AccountMsg.TENANT_BSSACCOUNT_EXPIRED_DISABLE);
                    baseNotificationMqBean.setMap(messageContent);
                    baseNotificationMqBean.getImsgUserIds().add(accountToUserId.get(id).getAdminSid());
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT,
                                                  baseNotificationMqBean);
                    if (adminstrators != null) {
                        try {
                            baseNotificationMqBean.getImsgUserIds().clear();
                            baseNotificationMqBean.getImsgUserIds()
                                                  .addAll(adminstrators.stream()
                                                                       .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid)
                                                                       .collect(Collectors.toSet()));
                            baseNotificationMqBean.setMsgId(
                                    AccountMsg.BSSMGT_BSSACCOUNT_EXPIRED_DISABLE);
                            baseNotificationMqBean.setEntityId(authUserInfo.getEntityId());
                            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER,
                                                          baseNotificationMqBean);
                        } catch (Exception e) {
                            log.error("账号到期功能关闭通知消息发送异常：", e);
                        }
                    }
                }
            }
            return true;
        }
        return false;
    }


    public static <T> String parseListToStr(List<T> list) {
        String result = Joiner.on(",").join(list);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBusinessTag(UpdateBusinessTagRequest request) {
        BizBillingAccount account = this.getById(request.getAccountId());
        if (ObjectUtil.isEmpty(account)) {
            BizException.e("账户不存在");
        }
        String businessTag = request.getBusinessTag();
        if (StringUtil.isBlank(businessTag)) {
            businessTag = BusinessTagEnum.RECORDED.getTag();
        }
        String[] split = businessTag.split(";");
        List<String> tagList = new ArrayList<>(Arrays.asList(split));
        User user = userMapper.selectByPrimaryKey(account.getAdminSid());

        // 校验业务标识 是否有效
        checkTag(tagList);

        Map<String, String> noticeMap = null;
        if (Constants.CUSTOM.equals(
                PropertiesUtil.getProperty(cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.BUSINESS_TAG_CONFIG))) {
            // 自定义业务标识
            noticeMap = this.checkAndGetCustomTag(tagList, user);
        } else {
            // 标准业务标识
            this.checkAndGetStandardTag(tagList, user);
            List<String> customBusinessTagList = getCustomBusinessTagList();
            //剔除自定义业务标识
            tagList.removeIf(s -> customBusinessTagList.contains(s));
            if (CollectionUtil.isEmpty(tagList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_483786104));
            }

        }

        // 不做密码/邮箱/电话的修改
        user.setPassword(null);
        user.setEmail(null);
        user.setMobile(null);

        user.setUserSid(account.getAdminSid());
        user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
        //远程更新keycloak得用户
        try {
            log.info("updateBusinessTag，开始修改业务标识");
//            IamUser iamUser = BeanConvertUtil.convert(user, IamUser.class);
//            iamUser.setPassword(null);
//            // 修改当前用户的业务标识
//            iamRemoteService.updateUser(iamUser);
            User modifyTag = new User();
            modifyTag.setUserSid(user.getUserSid());
            modifyTag.setBusinessTag(user.getBusinessTag());
            userMapper.updateByPrimaryKeySelective(modifyTag);
            userSyncRemoteService.updateUser(BeanUtil.copyProperties(modifyTag, cn.com.cloudstar.rightcloud.common.pojo.User.class));


            // add_xiongwei 同步子用户的业务标识
            Criteria criteria = new Criteria();
            criteria.put("parentSid", user.getUserSid());
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> childUsers = sysUserService.selectByParams(criteria);
            log.info("updateBusinessTag，子用户个数", childUsers.size());
            for (cn.com.cloudstar.rightcloud.bss.module.access.bean.User childUser : childUsers) {
                User modifySubTag = new User();
                modifySubTag.setUserSid(childUser.getUserSid());
                modifySubTag.setBusinessTag(user.getBusinessTag());
                userMapper.updateByPrimaryKeySelective(modifySubTag);
                userSyncRemoteService.updateUser(BeanUtil.copyProperties(modifyTag, cn.com.cloudstar.rightcloud.common.pojo.User.class));
            }
        } catch (Exception e) {
            log.info("更新标签失败:{}", e.getMessage());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_394199893));
        }

        log.info("BizBillingAccountServiceImpl.updateBusinessTag noticeMap: {}", noticeMap == null ? null : JSON.toJSONString(noticeMap));
        if (Objects.nonNull(noticeMap)) {
            // 根据配置发送消息
            this.sendBusinessTagMessage(noticeMap, user);
        }

        return true;
    }

    private void checkTag(List<String> tagList) {
        List<String> allTags = cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.getAllTag();

        // 判断是否包含已注销，已注销的标签不能包含其他的
        boolean cancelled = tagList.contains( cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.CANCELLED.getTag());
        if (cancelled) {
            long count = tagList.stream().filter(allTags::contains).count();
            if (count > 1) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1865846735));
            }
        }

        String customBusinessTagStr =
            PropertiesUtil.getProperty(cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.CUSTOM_BUSINESS_TAG);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(customBusinessTagStr)) {
            List<CustomBusinessTag> customBusinessTags = JSON.parseArray(customBusinessTagStr, CustomBusinessTag.class);
            List<String> tagKeys = customBusinessTags.stream().map(CustomBusinessTag::getTagKey).collect(Collectors.toList());
            allTags.addAll(tagKeys);
        }

        Long collect = tagList.stream().filter(s -> !allTags.contains(s)).collect(Collectors.counting());
        if (collect > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_483786104));
        }
    }


    private void sendBusinessTagMessage(Map<String, String> tagList, User user) {
        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("userAccount", user.getAccount());
        NotificationUtil.assembleBaseMessageContent(messageContent);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tagList.get("tenantUser"))) {
            messageContent.put("businessTag", tagList.get("tenantUser"));
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(TANANT_ADD_BUSINESS_TAG);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.getToUserIds().add(user.getUserSid());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER,
                        baseNotificationMqBean);
            } catch (Exception e) {
                log.error("租户自定义业务标识发送消息异常：", e);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tagList.get("operation"))) {
            messageContent.put("businessTag", tagList.get("operation"));
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(null);
            List<Long> adminIds = adminstrators.stream().map(User::getUserSid).collect(Collectors.toList());
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(BSSMGT_ADD_BUSINESS_TAG);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.getToUserIds().addAll(adminIds);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER,
                        baseNotificationMqBean);
            } catch (Exception e) {
                log.error("运营管理员自定义业务标识发送消息异常：", e);
            }
        }
    }

    private Map<String, String> checkAndGetCustomTag(List<String> tagList, User user) {
        String customBusinessTagStr =
                PropertiesUtil.getProperty(cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.CUSTOM_BUSINESS_TAG);
        if (org.apache.commons.lang3.StringUtils.isEmpty(customBusinessTagStr)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SYS_CONFIG_DELETION));
        }

        List<CustomBusinessTag> customBusinessTags = JSON.parseArray(customBusinessTagStr, CustomBusinessTag.class);
//        List<String> tagKeys = customBusinessTags.stream().map(CustomBusinessTag::getTagKey).collect(Collectors.toList());
//        if (!tagKeys.containsAll(tagList)) {
//            log.info("BizBillingAccountServiceImpl.checkAndGetCustomTag tagKeys: {}",
//                    CollectionUtil.isEmpty(tagKeys) ? "[]" : JSON.toJSONString(tagKeys));
//            throw new BizException("请选择正确的业务标识!");
//        }

        long count = tagList.stream().distinct().count();
        if (tagList.size() != count) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUPPORTED_REPEAT_IDENTIFICATION));
        }

        List<String> userBusinessTags = Arrays.asList(user.getBusinessTag().split(";"));
        List<String> addTags = tagList.stream().filter(tag -> !userBusinessTags.contains(tag)).collect(Collectors.toList());
        StringBuilder tenantUser = new StringBuilder();
        StringBuilder operation = new StringBuilder();
        customBusinessTags.forEach(template -> {
            if (addTags.contains(template.getTagKey())) {
                if (Objects.nonNull(template.getTenantUser())
                        && template.getTenantUser()) {
                    if (org.apache.commons.lang3.StringUtils.isEmpty(tenantUser)) {
                        tenantUser.append(template.getTagName());
                    }else {
                        tenantUser.append("，");
                        tenantUser.append(template.getTagName());
                    }
                }
                if (Objects.nonNull(template.getOperation())
                        && template.getOperation()) {
                    if (org.apache.commons.lang3.StringUtils.isEmpty(operation)) {
                        operation.append(template.getTagName());
                    }else {
                        operation.append("，");
                        operation.append(template.getTagName());
                    }
                }
            }
        });
        HashMap<String, String> noticeMap = new HashMap<>(2);
        noticeMap.put("tenantUser", tenantUser.toString());
        noticeMap.put("operation", operation.toString());
        return noticeMap;
    }

    private void checkAndGetStandardTag(List<String> tagList, User user) {
        // 因为只有一类有多个tag,所以就查看一类是否有重复的
        List<String> stringList = cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.getTagByType(1);
        Long count = tagList.stream().filter(s -> stringList.contains(s)).collect(Collectors.counting());
        if (count > 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1691783089));
        }
        // 查询当前用户已存在的标签
        String tags = user.getBusinessTag();

        if (StringUtil.isNotEmpty(tags)) {
            if (tags.indexOf(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                String substring = tags.substring(tags.indexOf("["), tags.indexOf("]") + 1);
                tagList = tagList.stream().map(s -> {
                    if (s.indexOf(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.ARREARAGE.getTag()) == 0) {
                        s = s + substring;
                    }
                    return s;
                }).collect(Collectors.toList());
            }
            List<String> existTags = Arrays.asList(tags.split(";"));
            // 该客户已存在的标签和要修修改的标签取差集，看是否在允许修改的tag范围内
            List<String> allowedUpdateTags = Arrays.asList(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.EXPANSION.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.RECORDED.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.COMMERCIAL.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.TRIAL.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.SIGNED.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.CANCELLED.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.preOpen.getTag());

            List<String> complementList = new ArrayList<>();
            //当存在的tag里面包含有 预开通tag,更新的参数里面不包含预开通的tag的时候需要校验电话和邮箱是否唯一
            if (existTags.contains(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.preOpen.getTag()) && !tagList.contains(
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.preOpen.getTag())) {
                checkEmail(user.getEmail(), user.getUserSid());
                checkMobile(user.getMobile(), user.getUserSid());
            }
            List<String> customBusinessTagList = getCustomBusinessTagList();

            if (existTags.size() >= tagList.size()) {
                List<String> finalTagList = tagList;
                complementList = existTags.stream()
                        .filter(s -> !finalTagList.contains(s) && !customBusinessTagList.contains(s))
                        .collect(
                                Collectors.toList())
                        .stream()
                        .filter(tag -> !allowedUpdateTags.contains(tag))
                        .collect(Collectors.toList());
            } else {
                complementList = tagList.stream()
                        .filter(s -> !existTags.contains(s) && !customBusinessTagList.contains(s))
                        .collect(
                                Collectors.toList())
                        .stream()
                        .filter(tag -> !allowedUpdateTags.contains(tag))
                        .collect(Collectors.toList());
            }

            if (complementList.size() > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1865846734));
            }
        } else {
            List<String> allowedUpdateTags = Arrays.asList(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.TRIAL.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.SIGNED.getTag(),
                    cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.CANCELLED.getTag());
            Long collect1 = tagList.stream().filter(s -> !allowedUpdateTags.contains(s)).collect(Collectors.counting());
            if (collect1 > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1865846734));
            }
        }
    }

    private List<String> getCustomBusinessTagList() {
        List<String> customBusinessTagList = new ArrayList<>();
        String customBusinessTagStr =
            PropertiesUtil.getProperty(cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.CUSTOM_BUSINESS_TAG);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(customBusinessTagStr)) {
            List<CustomBusinessTag> customBusinessTags = JSON.parseArray(customBusinessTagStr, CustomBusinessTag.class);
            List<String> tagKeys = customBusinessTags.stream().map(CustomBusinessTag::getTagKey).collect(Collectors.toList());
            customBusinessTagList.addAll(tagKeys);
        }
        return customBusinessTagList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void amountClearance(AmountClearanceRequest request) {
        BizBillingAccount billingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getId());
        User user = userMapper.selectByPrimaryKey(billingAccount.getAdminSid());
        //获取字段
        RestResult feignRestResult = feignService.getCodesByCategoryByFeign(CLEARANCE_TYPE, false, null, null, false, "false");
        HashMap<String, String> codes = new HashMap<>();
        if (feignRestResult != null) {
            List<DescribeCodeFullResponse> data = BeanConvertUtil.convert((List<LinkedMap>)feignRestResult.getData(), DescribeCodeFullResponse.class);
            data.forEach(e -> {
                codes.put(e.getCodeValue(), e.getCodeDisplay());
            });
        }
        if (CollectionUtils.isEmpty(codes)) {
            throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_NOT_SUPPORT));
        }
        if (!codes.keySet().containsAll(request.getClearanceTypes())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        HashMap<String, String> content = new HashMap<>();
        content.put("userAccount", user.getAccount());
        content.put("entityName", billingAccount.getEntityName());
        String clearanceTypes = request.getClearanceTypes().stream().map(e -> codes.get(e))
                                       .collect(Collectors.joining("，"));
        content.put("cleaningScope", clearanceTypes);
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> adminstrators = userMapper.findAdminstrators();
        try {
            request.getClearanceTypes().forEach(e -> {
                AmountClearanceService service = amountClearanceFactory.get(e);
                if (Objects.isNull(service)) {
                    throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_NOT_SUPPORT));
                }
                service.clearance(request.getId());
            });
        } catch (Exception e) {
            BaseNotificationMqBean base = new BaseNotificationMqBean();
            LinkedHashSet<Long> toUserIds = new LinkedHashSet<>();
            toUserIds.add(AuthUserHolder.getAuthUser().getUserSid());
            base.setEntityId(billingAccount.getEntityId());
            base.setToUserIds(toUserIds);
            base.setMap(content);
            base.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_ACCOUNT_CLEARING_ERROR);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, base);
            throw new BizException(e);
        }
        BaseNotificationMqBean base = new BaseNotificationMqBean();
        base.setEntityId(billingAccount.getEntityId());
        LinkedHashSet<Long> toAdminIds = new LinkedHashSet<>();
        if (EXPIRE_TYPE.equals(request.getExpireType())) {
            toAdminIds.addAll(adminstrators.stream()
                                           .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid)
                                           .collect(Collectors.toSet()));
        } else {
            toAdminIds.add(AuthUserHolder.getAuthUser().getUserSid());
        }
        base.setToUserIds(toAdminIds);
        base.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_ACCOUNT_CLEARING);
        base.setMap(content);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, base);

        BaseNotificationMqBean toUser = new BaseNotificationMqBean();
        toUser.setEntityId(billingAccount.getEntityId());
        LinkedHashSet<Long> toUserIds = new LinkedHashSet<>();
        toUserIds.add(user.getUserSid());
        toUser.setToUserIds(toUserIds);
        toUser.setMap(content);
        toUser.setMsgId(NotificationConsts.ConsoleMsg.AccountMsg.TENANT_ACCOUNT_CLEARING);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, toUser);
    }

    /**
     * 校验邮箱唯一性
     *
     * @param email
     */
    private void checkEmail(String email, Long userSid) {
        if (StringUtils.isNullOrEmpty(email)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_684266669));
        }
        String regEx1 = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
        if (!email.matches(regEx1)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1899066914));
        }
        if (email.length() > 80) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_671607287));
        }

        // 数据库已加密
        String emailNew = CCSPCacheUtil.verifyAndCCSPEncrypt(email);
        LambdaQueryWrapper<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> tWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.equals(emailNew, email)) {
            tWrapper.eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getEmailHash, DigestUtils.sha256Hex(email));
        } else {
            tWrapper.eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getEmail, emailNew);
        }
        tWrapper.ne(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getStatus, 8);
        tWrapper.ne(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getStatus, 4);
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = sysUserMapper.selectList(tWrapper);

        if (!CollectionUtils.isEmpty(userList)) {
            userList = userList.stream().filter(u -> u.getUserSid().compareTo(userSid) != 0).collect(
                    Collectors.toList());
            if (!CollectionUtils.isEmpty(userList)) {
                throw new BizException(
                        cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_MSG_00013));
            }
        }
    }

    /**
     * 校验电话号码唯一性
     *
     * @param mobile
     */
    private void checkMobile(String mobile, Long userSid) {
        if (StringUtils.isNullOrEmpty(mobile)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_641213096));
        }
        // 数据库已加密
        String mobileNew = CCSPCacheUtil.verifyAndCCSPEncrypt(mobile);
        LambdaQueryWrapper<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> tWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.equals(mobileNew, mobile)) {
            tWrapper.eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getMobileHash, DigestUtils.sha256Hex(mobile));
        } else {
            tWrapper.eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getMobile, mobileNew);
        }
        tWrapper.ne(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getStatus, 8);
        tWrapper.ne(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getStatus, 4);
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = sysUserMapper.selectList(tWrapper);

        if (!CollectionUtils.isEmpty(userList)) {
            userList = userList.stream().filter(u -> u.getUserSid().compareTo(userSid) != 0).collect(
                    Collectors.toList());
            if (!CollectionUtils.isEmpty(userList)) {
                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_MSG_00012));
            }
        }
    }

    /**
     * 修改HPC共享资源池冻结状态
     *
     * @param orgSid
     * @param isFrozen
     */
    private void changeHPCFreezeStatus(Long orgSid, boolean isFrozen) {

        Criteria criteria = new Criteria();
        criteria.put("org_sid", orgSid);
        criteria.put("product_type", "HPC");
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(sfProductResources)) {
            SfProductResource productRes = CollectionUtil.getFirst(sfProductResources);
            if (isFrozen) {
                String resStatus = productRes.getStatus();
                if (!SfProductEnum.FROZEN.getStatus().equals(resStatus)) {
                    productRes.setPreStatus(resStatus);
                }
                productRes.setStatus(ResHpcClusterStatus.FROZEN);
            } else {
                productRes.setStatus(productRes.getPreStatus());
            }
            sfProductResourceMapper.updateByPrimaryKeySelective(productRes);

            Long clusterId = productRes.getClusterId();
            if (clusterId != null) {
                ResHpcClusterRemoteModule hpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
                if (hpcClusterRemoteModule != null) {
                    if (isFrozen) {
                        String status = hpcClusterRemoteModule.getStatus();
                        if (!ResHpcClusterStatus.FROZEN.equals(status)) {
                            hpcClusterRemoteModule.setPreStatus(status);
                        }
                        hpcClusterRemoteModule.setStatus(ResHpcClusterStatus.FROZEN);
                    } else {
                        hpcClusterRemoteModule.setStatus(hpcClusterRemoteModule.getPreStatus());
                    }
                    hpcRemoteService.updateByPrimaryKeySelective(hpcClusterRemoteModule);
                }
            }
        }
    }
    /**
     * 解冻/冻结账户
     */
    private void thawStatus(BizBillingAccount bizBillingAccount) {
        log.info("-------BizBillingAccountServiceImpl.thawStatus 解冻/冻结账户开始--------");
        // 标识：是否冻结
        boolean isFreeze = false;
        // 优先级标识
        boolean skip = false;
        // 用户id
        Long adminSid = bizBillingAccount.getAdminSid();
        // 账户ID
        Long accountId = bizBillingAccount.getId();
        // 运营实体ID
        Long entityId = bizBillingAccount.getEntityId();
        // 运营实体名称
        String entityName = bizBillingAccount.getEntityName();
        // 组织
        List<Long> orgSids = orgMapper.selectCustomerOrgSids(bizBillingAccount.getOrgSid());
        //防止bizBillingAccount.getCreditLine()为null
        BigDecimal credit = bizBillingAccount.getCreditLine() != null ? bizBillingAccount.getCreditLine() : BigDecimal.valueOf(0);
        // 现金余额
        BigDecimal balance = Optional.ofNullable(bizBillingAccount.getBalance())
                                         .map(v -> NumberUtil.add(v, bizBillingAccount.getCreditLine(), bizBillingAccount.getBalanceCash()))
                                         .orElse(BigDecimal.ZERO);

        // 最小冻结金额：用于站内信
        String minFrozenAmount = null;
        // 该客户拥有的按量付费serviceId
        List<String> serviceIdByAccount = bizProductQuotaMapper.findServiceIdByProductQuota(adminSid);
        // 查询该运营实体所关联的按量计费产品serviceId
        List<String> serviceIds = bizProductQuotaMapper.findServiceIdPostPaidByEntityId(entityId, orgSids);
        // 查询客户限额
        String allFrozenAmount = JedisUtil.INSTANCE.hget(PRODUCTS_QUOTA_ACCOUNT_KEY + accountId, ZERO);
        if (Objects.nonNull(allFrozenAmount)) {
            log.info("BizBillingAccountServiceImpl.thawStatus  存在客户限额，产品【全部】，最小冻结金额【{}】", allFrozenAmount);
            if (balance.compareTo(new BigDecimal(allFrozenAmount)) < 0) {
                isFreeze = true;
                minFrozenAmount = allFrozenAmount;
            } else {
                skip = true;
            }
        } else {
            for (String serviceId : serviceIds) {
                String frozenAmount = JedisUtil.INSTANCE.hget(PRODUCTS_QUOTA_ACCOUNT_KEY + accountId, serviceId);
                if (Objects.nonNull(frozenAmount)) {
                    log.info("BizBillingAccountServiceImpl.thawStatus  存在客户限额，产品【{}】，最小冻结金额【{}】", serviceId, frozenAmount);
                    if (balance.compareTo(new BigDecimal(frozenAmount)) < 0) {
                        // 冻结账户
                        isFreeze = true;
                        minFrozenAmount = frozenAmount;
                        break;
                    } else {
                        skip = true;
                    }
                }
            }
        }

        if (!isFreeze && !skip) {
            // 查询产品限额
            for (String serviceId : serviceIds) {
                String productFrozenAmount = JedisUtil.INSTANCE.get(PRODUCTS_QUOTA_KEY + serviceId);
                if (Objects.nonNull(productFrozenAmount)) {
                    log.info("BizBillingAccountServiceImpl.thawStatus  存在产品限额，产品【{}】，最小冻结金额【{}】", serviceId, productFrozenAmount);
                    if (balance.compareTo(new BigDecimal(productFrozenAmount)) < 0) {
                        // 冻结账户
                        isFreeze = true;
                        minFrozenAmount = productFrozenAmount;
                        break;
                    } else {
                        skip = true;
                    }
                }
            }
        }

        if (!isFreeze && !skip) {
            // 平台限额
            if ("true".equals(
                    PropertiesUtil.getProperty(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT_OPEN + entityId))
                    && balance.compareTo(new BigDecimal(
                    PropertiesUtil.getProperty(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + entityId))) < 0) {
                log.info("BizBillingAccountServiceImpl.thawStatus  存在平台限额，最小冻结金额【{}】",
                         PropertiesUtil.getProperty(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + entityId));
                List<String> accountIdList = JedisUtil.INSTANCE.getList(entityId + PRODUCTS_QUOTA_WHITE_LIST_KEY);

                AtomicReference<Boolean> index = new AtomicReference<>(Boolean.TRUE);
                accountIdList.forEach(t -> {
                    if (String.valueOf(accountId).equals(t)) {
                        index.set(Boolean.FALSE);
                    }
                });
                // 冻结账户
                if (index.get()) {
                    isFreeze = true;
                    minFrozenAmount = PropertiesUtil.getProperty(
                            ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + entityId);
                } else {

                }
            }
        }
        log.info("BizBillingAccountServiceImpl.thawStatus isFreeze:[{}], accountStatus:[{}]", isFreeze, bizBillingAccount.getStatus());
        if (!isFreeze) {
            // 账户解冻
            if (FREEZE.equals(bizBillingAccount.getStatus())) {
                changeUserFreeze(adminSid, bizBillingAccount);
            }
        } else {
            // 冻结
            if (NORMAL.equals(bizBillingAccount.getStatus())) {
                frozen(adminSid, minFrozenAmount, bizBillingAccount);
            }
        }
        log.info("-------BizBillingAccountServiceImpl.thawStatus  解冻/冻结账户结束--------");
    }

    /**
     * 冻结账户
     */
    private void frozen(Long userSid, String minFrozenAmount, BizBillingAccount bizBillingAccount) {
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        if (NORMAL.equals(bizBillingAccount.getStatus())) {
            cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult restResult = ossService.disableFreezeBssStatus(
                    bizBillingAccount.getId(), minFrozenAmount);
            log.info("BizBillingAccountServiceImpl.frozen accountId:[{}], restResult: {}",bizBillingAccount.getId(), JSON.toJSONString(restResult));
            if (restResult.getData() instanceof Boolean && (Boolean) restResult.getData()) {
                bizBillingAccountMapper.updateFrozenAccount(bizBillingAccount.getId(), ZERO);
                HashMap<String, String> platMessageContent = new HashMap<>(8);
                if (BigDecimal.ZERO.compareTo(new BigDecimal(minFrozenAmount)) > 0) {
                    platMessageContent.put("symbolAmount", "-");
                } else {
                    platMessageContent.put("symbolAmount", "");
                }
                platMessageContent.put("minFrozenAmount",
                    String.valueOf(
                        Math.abs(Double.parseDouble(new BigDecimal(minFrozenAmount).setScale(2, BigDecimal.ROUND_DOWN).toString()))));

                String balance = bizBillingAccount.getBalance()
                    .setScale(2, BigDecimal.ROUND_DOWN)
                    .toString();
                if (Objects.isNull(bizBillingAccount.getCreditLine())) {
                    bizBillingAccount.setCreditLine(BigDecimal.ZERO);
                }
                String creditLine = bizBillingAccount.getCreditLine()
                    .setScale(2, BigDecimal.ROUND_DOWN)
                    .toString();
                platMessageContent.put("balance", String.valueOf(Math.abs(Double.parseDouble(balance))));
                platMessageContent.put("creditLine", String.valueOf(Math.abs(Double.parseDouble(creditLine))));
                platMessageContent.put("userAccount",
                    sysUserService.selectByPrimaryKey(bizBillingAccount.getAdminSid())
                        .getAccount());
                platMessageContent.put("entityName", bizBillingAccount.getEntityName());
                if (BigDecimal.ZERO.compareTo(bizBillingAccount.getBalance()) > 0) {
                    platMessageContent.put("symbol", "-");
                } else {
                    platMessageContent.put("symbol", "");
                }
                if (BigDecimal.ZERO.compareTo(bizBillingAccount.getCreditLine()) > 0) {
                    platMessageContent.put("symbolCredit", "-");
                } else {
                    platMessageContent.put("symbolCredit", "");
                }


                sysMsgService.sendBssMessage(user.getUserSid(), platMessageContent, NotificationConsts.ConsoleMsg.AccountMsg.TENANT_MIN_FROZEN_AMOUNT
                    , NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_MIN_FROZEN_AMOUNT, bizBillingAccount.getEntityId());
                frozenLog(userSid, "自动冻结", "租户自动冻结", "自动冻结","BizBillingAccountServiceImpl.frozen",true);

            } else {
                HashMap<String, String> platMessageContent = new HashMap<>(8);
                platMessageContent.put("entityName",bizBillingAccount.getEntityName());
                sysMsgService.sendBssMessage(user.getUserSid(), platMessageContent,null, NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_ACCOUNT_FREEZE_ERROR, bizBillingAccount.getEntityId());
                frozenLog(userSid, "自动冻结", "租户自动冻结", "自动冻结","BizBillingAccountServiceImpl.frozen",false);
            }
        }
    }


    /**
     * 解冻账户
     */
    private void changeUserFreeze(Long userSid, BizBillingAccount bizBillingAccount) {
        cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult restResult = ossService.disableThawStatus(
                bizBillingAccount.getId());
        HashMap<String, String> platMessageContent = new HashMap<>(8);
        platMessageContent.put("entityName",bizBillingAccount.getEntityName());
        log.info("BizBillingAccountServiceImpl.changeUserFreeze accountId:[{}], restResult: {}",bizBillingAccount.getId(), JSON.toJSONString(restResult));
        if (restResult.getData() instanceof Boolean && (Boolean) restResult.getData()) {
            bizBillingAccountMapper.updateFreezeAccount(bizBillingAccount.getId());
            platMessageContent.put("unfreezeInfo","成功");
            sysMsgService.sendBssMessage(bizBillingAccount.getAdminSid(), platMessageContent,null, AccountMsg.BSSMGT_ACCOUNT_UNFREEZE, bizBillingAccount.getEntityId());
            frozenLog(userSid, "自动解冻", "充值金额租户自动解冻", "自动解冻","BizBillingAccountServiceImpl.frozen",true);
        } else {
            platMessageContent.put("unfreezeInfo","失败");
            sysMsgService.sendBssMessage(bizBillingAccount.getAdminSid(), platMessageContent,null, AccountMsg.BSSMGT_ACCOUNT_UNFREEZE, bizBillingAccount.getEntityId());
            frozenLog(userSid, "自动解冻", "充值金额租户自动解冻", "自动解冻","BizBillingAccountServiceImpl.frozen",false);
        }
    }

    /**
     * 解冻/冻结日志
     */
    private void frozenLog(Long userSid, String actionName,String resourceName,String reouce,String actionMethod,boolean success) {
        try {
            if (Objects.nonNull(userSid)) {
                User user = userMapper.selectByPrimaryKey(userSid);
                if (Objects.nonNull(user)) {
                    ActionLog log = new ActionLog();
                    log.setAccount("系统");
                    log.setActionName(actionName);
                    log.setResource(reouce);
                    log.setTagName(resourceName+user.getAccount());
                    log.setSuccess("成功");
                    if (!success) {
                        log.setSuccess("失败");
                    }
                    log.setActionTime(new Date());
                    HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
                    String remoteIp = IPAddressUtil.getRemoteHostIp(request);
                    String ipAddress = IPAddressUtil.getIpAddress();
                    log.setActionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request));
                    log.setActionMethod("ProductQuotaController.operateBizAccountProductQuotas");
                    if (StringUtils.isNotEmpty(actionMethod)) {
                        log.setActionMethod(actionMethod);
                    }
                    log.setHttpMethod(request.getMethod());
                    log.setLbIp(remoteIp);
                    log.setRemoteIp(ipAddress);
                    log.setRoleName("系统");
                    addTraceId(log);
                    String userClient = request.getHeader("User-Agent");
                    log.setClient(userClient);
                    mongoTemplate.insert(log, "action_log");

                    String logDetail  = actionName+log.getSuccess();
                    OperationResourceEnum opEnum = OperationResourceEnum.ENABLE_FREEZESTATUS;
                    if (StringUtil.containsIgnoreCase(actionName, "冻结")) {
                        opEnum = OperationResourceEnum.DISABLE_RESOURCESTATUS;
                    }
                    insertActionLog(user, opEnum, logDetail);
                }
            }
        } catch (Exception e) {
            log.error("ProductQuotaController_operateBizAccountProductQuotas_frozenLog【{}】", JSON.toJSONString(e.getMessage()));
        }
    }

    /**
     * 插入操作日志(mysql-biz_customer_action_log)
     *
     * @param user             用户信息
     * @param operationResourceEnum 操作资源枚举
     * @param logDetail             日志详细
     */
    private void insertActionLog(User user, OperationResourceEnum operationResourceEnum, String logDetail) {
        BizCustomerActionLog actionLogDTO = new BizCustomerActionLog();

        actionLogDTO.setUserSid(user.getUserSid());
        actionLogDTO.setOrgSid(user.getOrgSid());
        actionLogDTO.setOpType(operationResourceEnum.getType());
        actionLogDTO.setOpDetail(logDetail);
        actionLogDTO.setOpDate(new Date());
        actionLogDTO.setOpUser(RequestContextUtil.getAuthUserInfo().getUserSid());
        bizCustomerActionLogMapper.insert(actionLogDTO);
    }

    @Override
    public void sendUpdateCreditLineSchedule(UpdateCreditLineRequest request) {
        log.info("request 中的CreditLineDt is:::: [{}]", request.getCreditLineDt());
        BizBillingAccount account = Objects.requireNonNull(this.getById(request.getAccountId()), "账户不存在");
        User user = userMapper.selectByPrimaryKey(account.getAdminSid());
        String accLineDt = account.getCreditLineDt();
        log.info("当前的过期时间是acct中的 CreditLineDt===============================================[{}]", accLineDt);
        //修改后将小于一天的加入延时队列
        if (account.getCreditLineDt() != null && !"".equals(account.getCreditLineDt()) && !UNLIMITED.equals(account.getCreditLineDt())) {
            log.info("开始转发SCHEDULE定时任务===============================================");
            sendUpdateCreditLineSchedule(account.getId());
        } else {
            if (account.getCreditLineDt() == null) {
                log.info("account.getCreditLineDt() is null===============================================");
            } else {
                log.info("account.getCreditLineDt() is ===============================================[{}]", accLineDt);
                log.info("account.getCreditLineDt() length is ===============================================[{}]", accLineDt.length());
            }
            log.info("当前的过期时间不满足条件，没有转发！");
            if (request.getCreditLineDt() != null && !"".equals(request.getCreditLineDt()) && !UNLIMITED.equals(request.getCreditLineDt())) {
                log.info("===========换成request，继续转发！");
                sendUpdateCreditLineSchedule(account.getId());
            }
        }
        String creditLine = String.format("%.2f" , request.getCreditLine());
        //修改成功后发送通知给账单客户
        sendUpdateCreditLine(user, creditLine, account.getEntityId(), account.getId().toString(), account.getEntityName());
        //修改成功后发送通知给运营
        sendUpdateCreditLineAdmin(user, creditLine);

    }


    private void updateBusinessArrearageTag(BizBillingAccount bizBillingAccount, User user) {
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
            } else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            User modifyTag = new User();
            modifyTag.setUserSid(user.getUserSid());
            modifyTag.setBusinessTag(user.getBusinessTag());
            userMapper.updateByPrimaryKeySelective(modifyTag);
            userSyncRemoteService.updateUser(BeanUtil.copyProperties(modifyTag, cn.com.cloudstar.rightcloud.common.pojo.User.class));
            userMapper.updateByPrimaryKeySelective(user);
            userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));

        } else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (org.springframework.util.CollectionUtils.isEmpty(accountIdList)) {
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
                    User modifyTag = new User();
                    modifyTag.setUserSid(user.getUserSid());
                    modifyTag.setBusinessTag(user.getBusinessTag());
                    userMapper.updateByPrimaryKeySelective(modifyTag);
                    userSyncRemoteService.updateUser(BeanUtil.copyProperties(modifyTag, cn.com.cloudstar.rightcloud.common.pojo.User.class));

                }
            }
        }
    }

    /**
     * 信用额度修改加入延时
     */
    public void sendUpdateCreditLineSchedule(Long id) {
        try {
            //修改后将小于一天的加入延时队列
            log.info("进入CreditLine延时队列,id{}", id.toString());
            mailService.sendUpdateCreditLineSchedule(id);
        } catch (Exception e) {
            log.error("信用额度修改后延时队列加入异常：", e.getMessage());
        }
    }

    @Override
    public List<BizBillingAccount> getBizBillingAccountsByOrgId(Long orgSid) {
        return bizBillingAccountMapper.selectList(new QueryWrapper<BizBillingAccount>().eq("distributor_id", orgSid));
    }

    @Override
    public boolean save(BizBillingAccount entity) {
        return super.save(entity);
    }

    /**
     * 账户开票升级流程
     * 1.通过充值记录查询账户信息
     * 2.根据充值账户更新充值总金额
     * 3.通过充值记录的账户，查询有开票记录的账户
     * 4.根据开票记录更新账户已开票金额
     */
    @Override
    public void invoicingUpgrade() {
        ArrayList<BizDeposit> bizDeposits = new ArrayList<>();
        //查询充值记录
        bizDepositMapper.selectListStream(resultContext -> bizDeposits.add(resultContext.getResultObject()));
        Map<Long, List<BizDeposit>> bizDepositListMap = bizDeposits.stream().collect(Collectors.groupingBy(BizDeposit::getAccountSid));
        Set<Long> accountIds = bizDepositListMap.keySet();
        if (accountIds.isEmpty()) {
            return;
        }

        List<BizBillingAccount> bizBillingAccounts =
                bizBillingAccountMapper.selectList(Wrappers.lambdaQuery(BizBillingAccount.class)
                                                           .in(BizBillingAccount::getId, accountIds));

        //查询开票记录
        Map<String, List<BizInvoice>> bizInvoiceListMap =
                bizInvoiceMapper.selectList(Wrappers.lambdaQuery(BizInvoice.class)
                                                    .ne(BizInvoice::getInvoiceStatus, InvoiceStatusEnum.REJECTED.getCode())
                                                    .in(BizInvoice::getAccountId, accountIds))
                                .stream()
                                .collect(Collectors.groupingBy(BizInvoice::getAccountId));


        for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
            List<BizDeposit> deposits = bizDepositListMap.get(bizBillingAccount.getId());
            //计算总充值金额
            if (ObjectUtil.isNotEmpty(deposits)) {
                BigDecimal totalRechargeAmount = BigDecimal.ZERO;
                for (BizDeposit deposit : deposits) {
                    totalRechargeAmount = totalRechargeAmount.add(deposit.getAmountDeposit());
                }
                bizBillingAccount.setTotalRechargeAmount(totalRechargeAmount);
            }

            List<BizInvoice> bizInvoices = bizInvoiceListMap.get(bizBillingAccount.getId().toString());
            //计算已开票金额
            if (ObjectUtil.isNotEmpty(bizInvoices)) {
                BigDecimal invoicedAmount = BigDecimal.ZERO;
                for (BizInvoice bizInvoice : bizInvoices) {
                    invoicedAmount = invoicedAmount.add(bizInvoice.getDepositeAmount());
                }
                bizBillingAccount.setInvoicedAmount(invoicedAmount);
            }
        }

        this.updateBatchById(bizBillingAccounts);
    }

    /**
     * 信用额度修改发送通知
     */
    public void sendUpdateCreditLine(User user, String creditLine, Long entityId, String accountId, String entityName) {
        //通知用户
        SendNotifyRequest sendNotifyRequest = new SendNotifyRequest();
        sendNotifyRequest.setIsCC(false);
        sendNotifyRequest.setMessageId(NotificationConsts.ConsoleMsg.FinanceMsg.TENANT_SEND_CREDIT_LINE);
        sendNotifyRequest.getToUserIds().add(user.getUserSid());
        HashMap<String, String> content = new HashMap<>();
        content.put("userAccount", user.getAccount());
        content.put("creditLine", creditLine);
        content.put("accountId", accountId);
        content.put("entityName", entityName);
        sendNotifyRequest.setMessageContent(content);
        sendNotifyRequest.setEntityId(entityId);
        try {
            //发送通知
            mailService.sendUpdateCreditLine(sendNotifyRequest);
        } catch (Exception e) {
            log.error("信用额度自动重置发送通知信息异常：", e.getMessage());
        }
    }

    /**
     * 信用额度发送管理员通知
     */
    public void sendUpdateCreditLineAdmin(User user, String creditLine) {
        //查询所有运营管理员
        List<User> users = userMapper.findAdminstratorsByEntityId(RequestContextUtil.getEntityId());
        List<Long> userIds = users.stream().map(User::getUserSid).collect(Collectors.toList());
        HashMap<String, String> content = new HashMap<>();
        content.put("userAccount", user.getAccount());
        content.put("creditLine", creditLine);
        try {
            if (CollectionUtil.isNotEmpty(users)) {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.getImsgUserIds().addAll(userIds);
                baseNotificationMqBean.setEntityId(RequestContextUtil.getEntityId());
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.FinanceMsg.BSSMGT_SEND_CREDIT_LINE);
                baseNotificationMqBean.setMap(content);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT, baseNotificationMqBean);
            }
        } catch (Exception e) {
            log.error("信用额度通知发送用户信息异常：", e.getMessage());
        }

        log.info("信用额度通知发送管理员通知完成");
    }

    private void addActionLog(BizBillingAccount updateAccount, User user){
        ActionLog log = new ActionLog();
        log.setAccount(updateAccount.getAccount());
        log.setActionName("更新信用额度");
        log.setActionTime(new Date());
        HttpServletRequest request = SpringContextHolder.getHttpServletRequest();
        String remoteIp = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getRemoteHostIp(request);
        String ipAddress = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getIpAddress();
        log.setActionPath(request.getRequestURI());
        log.setActionMethod("put");
        log.setHttpMethod(request.getMethod());
        log.setLbIp(remoteIp);
        log.setRemoteIp(ipAddress);
        log.setType("更新");
        log.setTraceId(request.getRequestedSessionId());
        log.setResource("信用额度调整");
        log.setRoleName("系统管理员(内置)运营管理员(内置)");
        log.setUserSid(user.getUserSid());
        log.setUserType(user.getUserType());
        log.setBizId(updateAccount.getId());
        addTraceId(log);
        String userClient = request.getHeader("User-Agent");
        log.setClient(userClient);
        actionLogTracker.add(log);
    }

    private void addTraceId(ActionLog log) {
        if (Objects.nonNull(this.tracer)) {
            Span span = this.tracer.currentSpan();
            if (Objects.nonNull(span) && Objects.nonNull(span.context())) {
                log.setTraceId(span.context().traceId());
                log.setSpanId(span.context().spanId());
            }
        }
    }

    @Override
    public List<BizBillingAccount> getAccountDiscountListExport(Criteria criteria) {
        return this.baseMapper.selectDiscountByParamsExport(criteria.getCondition());
    }

    @Override
    public RestResult asyncExport(DescribeBillingAccountRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.BIZ_BILLING_ACCOUNT.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.CURRENT_STATUS_NOT_DOWN));
        }
        new ExportThreadUtil(exportService,
            request,
            ModuleTypeConstants.FROM_BSS,
            ExportTypeEnum.BIZ_BILLING_ACCOUNT.getCode(),
            download.getDownloadId(),
            authUserInfo
        ).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1977429080));
    }

    private BizDownload getBizDownload(BizDownload download, DescribeBillingAccountRequest request,AuthUser authUserInfo) {
        // 租户在下载任务中存入accountId
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        return download;
    }

    /**
     * 更新用户 OBS 免费额度
     *
     * @param request
     * @return boolean
     */
    @Override
    public boolean updateFreeCapacity(UpdateFreeCapacityRequest request) {
        BizBillingAccount account = Objects.requireNonNull(this.getById(request.getAccountId()), WebUtil.getMessage(MsgCd.CUSTOMER_DOES_NOT_EXIST));
        User user = userMapper.selectByPrimaryKey(account.getAdminSid());
        if (!WebConstants.UserStatus.AVAILABILITY.equals(user.getStatus())) {
            return false;
        }
        //更新
        BizBillingAccount updateAccount = new BizBillingAccount();
        BeanUtils.copyProperties(account, updateAccount);
        BigDecimal freeCapacity = request.getFreeCapacity();
        updateAccount.setObsFreeCapacity(freeCapacity);
        if (!this.updateById(updateAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARN_RECHARGE_ACCOUNT));
        }
        return true;
    }

    @Override
    public IPage<BizBillingAccount> getBillingAccountOpenApiList(DescribeBillingAccountRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("orderByClause", "convert(distributor_name USING gbk) asc,created_dt desc");
        Page<BizBillingAccount> page = PageUtil.preparePageParams(request);
        IPage<BizBillingAccount> bizBillingAccountIPage = new Page<>();
        // 下面就是走原有的逻辑
        bizBillingAccountIPage = this.baseMapper.selectByParam(page, criteria.getCondition());
        // 产品开通信息
        Map<String, String> accountProductInfo = openProductInformation(RequestContextUtil.getEntityId());
        bizBillingAccountIPage.setRecords(bizBillingAccountIPage.getRecords().stream().peek(e -> {
            if (StringUtil.isNullOrEmpty(e.getDistributorName())) {
                e.setDistributorName("直营");
            }
            if (Objects.isNull(e.getRemark())) {
                e.setRemark("--");
            }
            // 业务标识
            adjustBusinessTag(e);
            e.setOpenProductInformation(accountProductInfo.get(e.getAccount()));
        }).collect(Collectors.toList()));
        return bizBillingAccountIPage;
    }

    @Override
    public void exportAccountWithCustomInfo(List<BizBillingAccount> bizBillingAccounts, HttpServletResponse response, List<UserExportDTO> resUser,
                                            String customInfoTemplate) {

        XSSFWorkbook workbook = createWorkbookAccountWithCustomInfo( bizBillingAccounts,resUser,customInfoTemplate);
        //下载文件
        downloadExcel(workbook, response);
    }


    @Override
    public XSSFWorkbook createWorkbookAccountWithCustomInfo(List<BizBillingAccount> bizBillingAccounts, List<UserExportDTO> resUser,
                                                            String customInfoTemplate) {

        List<CustomizationInfoTemplate> customizationInfoTemplates = JSON.parseArray(customInfoTemplate, CustomizationInfoTemplate.class);

        // 数据集合转换
        List<Map<String, String>> dataMaps = this.convertListToMap(bizBillingAccounts);
        List<Map<String, String>> resUserDataMaps = this.convertListToMap(resUser);

        // 1. 创建一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();

        // 2.创建第一个sheet
        XSSFSheet sheet = workbook.createSheet("客户信息列表");
        XSSFSheet resUserSheet = workbook.createSheet("子用户信息列表");

        //设置第1列单元格列宽,默认1/256字符宽度
        sheet.setColumnWidth(0, 20 * 256);

        // 3.头部设置样式
        CellStyle styleHeader = workbook.createCellStyle();
        // 设置水平居中
        styleHeader.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        styleHeader.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体加粗
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 9);
        font.setColor(IndexedColors.BLUE_GREY.getIndex());
        styleHeader.setFont(font);
        //下边框
        styleHeader.setBorderBottom(BorderStyle.DOUBLE);
        //左边框
        styleHeader.setBorderLeft(BorderStyle.THIN);
        //上边框
        styleHeader.setBorderTop(BorderStyle.THIN);
        //右边框
        styleHeader.setBorderRight(BorderStyle.THIN);
        //设置背景色
        styleHeader.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        styleHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        List<String> customAttrNames = customizationInfoTemplates.stream().map(CustomizationInfoTemplate::getAttrName).collect(Collectors.toList());
        List<String> customKeys = customizationInfoTemplates.stream().map(CustomizationInfoTemplate::getAttrKey).collect(Collectors.toList());
        //写表头（第二个sheet类似）
        setHeaderData(styleHeader, sheet, customAttrNames);
        setHeaderData(styleHeader, resUserSheet, null);
        //填写数据
        setContentData(dataMaps, sheet, customKeys, workbook);
        setContentData(resUserDataMaps, resUserSheet, null, workbook);
        //下载文件
        return workbook;
    }

    private void setHeaderData(CellStyle styleHeader, XSSFSheet sheet, List<String> customKeys) {
        //确定行：假如第一个sheet有4行（其中有一行是表头行），其余需要从库中查出数据
        //确定列：假如第一个sheet有8列，需要从库中查出数据
        //假如有3行，8列，其中8是固定的4列加上动态的列数4(这里4是动态的，可以是5，6，7或者8)
        Row row = sheet.createRow(0);
        if (org.springframework.util.CollectionUtils.isEmpty(customKeys)) {
            // 子用户信息sheet
            //确定首行中的固定列，也就是表头固定列（excel中列是从第0列开始的）
            for (int j = 0; j < CustomExportCellValueConstants.RES_USERS_CELL_VALUE.size(); j++) {
                Cell cell = row.createCell(j);
                //设置表头样式
                cell.setCellStyle(styleHeader);
                sheet.setColumnWidth(j, 20 * 256);
                cell.setCellValue(CustomExportCellValueConstants.RES_USERS_CELL_VALUE.get(j));
            }
        } else {
            // 客户信息sheet
            //确定首行中的固定列，也就是表头固定列（excel中列是从第0列开始的）
            int size = CustomExportCellValueConstants.ACCOUNT_CELL_VALUE.size() - 1;
            for (int j = 0; j < size; j++) {
                Cell cell = row.createCell(j);
                //设置表头样式
                cell.setCellStyle(styleHeader);
                sheet.setColumnWidth(j, 20 * 256);
                cell.setCellValue(CustomExportCellValueConstants.ACCOUNT_CELL_VALUE.get(j));
            }

            for (int i = size; i <= size + customKeys.size(); i++) {
                Cell cell = row.createCell(i);
                //设置表头样式
                cell.setCellStyle(styleHeader);
                //设置每一列的宽度（每列的宽度只需要在首行中设置就行）
                sheet.setColumnWidth(i, 20 * 256);
                if (i == size + customKeys.size()) {
                    cell.setCellValue("备注");
                }else {
                    cell.setCellValue(customKeys.get(i - size));
                }
            }
        }
    }

    private void setContentData(List<Map<String, String>> dataMaps, XSSFSheet sheet, List<String> customKeys, XSSFWorkbook workbook) {
        //设置数据格式
        CellStyle style = workbook.createCellStyle();
        // 设置水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //开始填充数据
        for (int rowNum = 1; rowNum <= dataMaps.size(); rowNum++) {
            Row dataRow = sheet.createRow(rowNum);
            //1.填充固定列数据（固定列数据可以抽象为一个对象）
            if (org.springframework.util.CollectionUtils.isEmpty(customKeys)) {
                // 填写子用户信息
                for (int i = 0; i < CustomExportCellValueConstants.RES_USERS_CELL_FIELD.size(); i++) {
                    Cell cell = dataRow.createCell(i);
                    cell.setCellStyle(style);
                    cell.setCellValue(dataMaps.get(rowNum - 1).get(CustomExportCellValueConstants.RES_USERS_CELL_FIELD.get(i)));
                }
            } else {
                // 填写客户信息
                int size = CustomExportCellValueConstants.ACCOUNT_CELL_FIELD.size() - 1;
                for (int i = 0; i < size; i++) {
                    Cell cell = dataRow.createCell(i);
                    cell.setCellStyle(style);
                    cell.setCellValue(dataMaps.get(rowNum - 1).get(CustomExportCellValueConstants.ACCOUNT_CELL_FIELD.get(i)));
                }
                //2.填充动态列数据（注意这里的起始列是固定列的最后一行加1）
                for (int i = size; i <= size + customKeys.size(); i++) {
                    Cell cell = dataRow.createCell(i);
                    cell.setCellStyle(style);
                    if (i == size + customKeys.size()) {
                        cell.setCellValue(dataMaps.get(rowNum - 1).get(CustomExportCellValueConstants.ACCOUNT_CELL_FIELD.get(size)));
                    }else {
                        cell.setCellValue(dataMaps.get(rowNum - 1).get(customKeys.get(i - size)));
                    }
                }
            }
        }
    }

    public void downloadExcel(Workbook workbook, HttpServletResponse response) {

        ByteArrayOutputStream outZip = null;
        OutputStream outputStream = null;
        ByteArrayOutputStream outExcel = null;
        InputStream inZip = null;
        InputStream inExcel = null;
        try {
            //设置响应头
            String format = new SimpleDateFormat("yyyyMMddHHmm").format(new Date());
            String fileName = "客户信息列表" + format + ".xlsx";
            String zipFileName = "客户信息列表" + format + ".zip";
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
            outputStream = response.getOutputStream();
            outExcel = new ByteArrayOutputStream();
            workbook.write(outExcel);
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, fileName, password, true);
            outZip = ZipUtil.toOutputStream(inZip);
            outZip.writeTo(outputStream);
            //发送压缩密码至用户
            //发送压缩密码至用户
            AuthUser user = RequestContextUtil.getAuthUserInfo();
            if (!ObjectUtils.isEmpty(user) && !ObjectUtils.isEmpty(user.getEmail())) {
                SendZipCompressPasswordRequest sendRequest = new SendZipCompressPasswordRequest();
                sendRequest.setUserSid(user.getUserSid());
                sendRequest.setFileName(zipFileName);
                sendRequest.setPassword(password);
                feignService.sendZipCompressPassword(sendRequest);
            }

            outputStream.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (Objects.nonNull(outZip)) {
                    outZip.close();
                }
                if (Objects.nonNull(inZip)) {
                    inZip.close();
                }
                if (Objects.nonNull(outExcel)) {
                    outExcel.close();
                }
                if (Objects.nonNull(inExcel)) {
                    inExcel.close();
                }
                workbook.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private <T> List<Map<String, String>> convertListToMap(List<T> dataList) {
        List<Map<String, String>> dataMaps = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataList)) {
            return dataMaps;
        }
        dataList.forEach(data -> {
            Map<String, String> map = new HashMap<>();
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    if ("customizationInfo".equals(field.getName())) {
                        Object value = field.get(data);
                        if (Objects.nonNull(value)) {
                            List<CustomizationInfo> infoDetails = JSONArray.parseArray(value.toString(), CustomizationInfo.class);
                            for (CustomizationInfo infoDetail : infoDetails) {
                                map.put(infoDetail.getAttrKey(), infoDetail.getAttrValue());
                            }
                        }
                    } else {
                        Object value = field.get(data);
                        if (value != null) {
                            map.put(field.getName(), value.toString());
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            dataMaps.add(map);
        });
        return dataMaps;
    }

}
