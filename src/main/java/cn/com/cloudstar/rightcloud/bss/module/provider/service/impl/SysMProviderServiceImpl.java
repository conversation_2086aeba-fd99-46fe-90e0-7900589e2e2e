package cn.com.cloudstar.rightcloud.bss.module.provider.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.message.service.ISysMsgService;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.entity.SysMProvider;
import cn.com.cloudstar.rightcloud.bss.module.provider.mapper.SysMProviderMapper;
import cn.com.cloudstar.rightcloud.bss.module.provider.service.ISysMProviderService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysMProviderStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;

/**
 * 供应商服务实现类
 * <AUTHOR>
 * @date 2025/1/21 14:38
 */
@Service
@Slf4j
public class SysMProviderServiceImpl extends ServiceImpl<SysMProviderMapper, SysMProvider> implements ISysMProviderService {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private ISysMsgService sysMsgService;
    @Autowired
    private OrgService orgService;

    @Override
    @Transactional(rollbackFor = Exception.class )
    public Long applyProvider(SysMProvider sysMProvider) {
        log.info("供应商入驻申请-INPUT");
        Date current = new Date();
        //仅允许租户管理员提交供应商入驻申请。
        Long orgSid = sysMProvider.getCompanyId();
        User user = sysUserService.selectByPrimaryKey(sysMProvider.getOwnerId());
        if (orgSid == null || user.getParentSid() != null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1762351419));
        }
        //用户已完成实名认证或企业认证；
        boolean b = sysUserService.checkCertification(sysMProvider.getOwnerId());
        if (!b) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00031));
        }
        //数据唯一性校验
        Long sysMProviderId = sysMProvider.getId();
        if (sysMProviderId == null) {
            List<SysMProvider> sysMProviderList = this.lambdaQuery().eq(SysMProvider::getCompanyId, sysMProvider.getCompanyId())
                .ne(SysMProvider::getStatus, SysMProviderStatusEnum.CANCELLED.getCode()).list();
            boolean exist = sysMProviderList.size() > 0;
            if (exist) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_806681152));
            }
            sysMProvider.setCreatedBy(user.getAccount());
            sysMProvider.setCreatedDt(current);
            sysMProvider.setVersion(1L);
        } else {
            SysMProvider oldSysMProvider = this.getById(sysMProviderId);
            List<String> list = Arrays.asList(SysMProviderStatusEnum.APPLY_FAILED.getCode(), SysMProviderStatusEnum.ENTERED.getCode());
            if (!list.contains(oldSysMProvider.getStatus()) || !Objects.equals(sysMProvider.getCompanyId(), oldSysMProvider.getCompanyId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1762351419));
            }
        }
        sysMProvider.setStatus(SysMProviderStatusEnum.APPLYING.getCode());
        sysMProvider.setUpdatedBy(user.getAccount());
        sysMProvider.setUpdatedDt(current);
        this.saveOrUpdate(sysMProvider);
        log.info("供应商入驻申请-发送站内信");
        Org org = orgService.getById(orgSid);
        Map<String, String> messageContent = new HashMap<>(10);
        messageContent.put("orgName", org.getOrgName());
        messageContent.put("providerCompanyName", sysMProvider.getCompanyName());
        sysMsgService.sendBssMessage(user.getUserSid(), messageContent,null,NotificationConsts.BSSMGT_APPLY_PROVIDER, null);
        return sysMProvider.getId();
    }

    /**
     * 注销供应商申请
     *
     * @return
     */
    @Override
    public void cancelProvider(Long userSid,Long providerId) {
        log.info("供应商注销申请-INPUT:userSid=[{}],providerId=[{}]", userSid,providerId);
        //仅允许租户管理员提交供应商入驻申请。
        User user = sysUserService.selectByPrimaryKey(userSid);
        if (user.getCompanyId() == null || user.getParentSid() != null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1762351419));
        }
        Org org = orgService.getById(user.getCompanyId());
        //数据唯一性校验
        SysMProvider provider =
            this.lambdaQuery().eq(SysMProvider::getCompanyId, user.getCompanyId())
            .eq(SysMProvider::getId, providerId).one();
        if (provider == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1231631582));
        }
        if (!SysMProviderStatusEnum.ENTERED.getCode().equals(provider.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1762351419));
        }
        this.lambdaUpdate().eq(SysMProvider::getId, provider.getId())
            .set(SysMProvider::getStatus, SysMProviderStatusEnum.CANCELLING.getCode())
            .set(SysMProvider::getUpdatedDt, new Date())
            .update();

        log.info("供应商注销申请-发送站内信");
        Map<String, String> messageContent = new HashMap<>(10);
        messageContent.put("orgName", org.getOrgName());
        messageContent.put("providerCompanyName", provider.getCompanyName());
        sysMsgService.sendBssMessage(user.getUserSid(), messageContent,null,NotificationConsts.BSSMGT_CANCEL_PROVIDER, null);
    }
}
