/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constants;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;

/**
 * DESC:云环境类型
 *
 * <AUTHOR>
 * Created on 2019/07/12 15:13
 */
public enum CloudEnvType {

    // 公有云
    /**
     * 阿里云
     */
    ALIYUN(true, "阿里云", "Aliyun"),
    /**
     * 腾讯云
     */
    QCLOUD(true, "腾讯云", "Qcloud"),

    /**
     * AWS
     */
    AWS(true, "AWS", "Aws"),

    /**
     * 华为云
     */
    HUAWEICLOUD(true, "华为云", "HuaweiCloud"),

    /**
     * 微软云
     */
    AZURE(true, "微软云", "Azure"),

    // 私有云
    /**
     * VMWARE
     */
    VMWARE(false, "VMWARE", "VMware"),

    /**
     * OpenStack系列云环境（管理员接入）
     */
    OPEN_STACK_ADMIN(false, true, "OpenStack系列云环境（管理员接入）", "ESCloud-Admin",
                     "Nine9Cloud-Admin", "OpenStack-Admin"),

    /**
     * OpenStack系列云环境：OPEN_STACK/ES_CLOUD
     */
    OPEN_STACK(false, "OpenStack系列云环境", OPEN_STACK_ADMIN.getValue(),
               "OpenStack", "ESCloud", "Nine9Cloud"),

    /**
     * POWER_VC
     */
    POWER_VC(false, "POWER_VC", "PowerVC"),

    /**
     * 华为私有云
     */
    FUSIONCOMPUTE(false, "华为私有云", "FusionCompute"),
    /**
     * HCSO
     */
    HCSO(false, "华为云（HCSO）", "HCSO"),

    /**
     * HYPER_V
     */
    HYPER_V(false, "HYPER_V", "Hyper-V"),

    /**
     * HMC
     */
    HMC(false, "HMC", "Hmc"),

    /**
     * CLOUDOS
     */
    CLOUDOS(false, "CLOUDOS", "CloudOS"),

    /**
     * CLOUDOS_ADMIN
     */
    CLOUDOS_ADMIN(false, "CLOUDOS_ADMIN", "CloudOS-Admin");

    /**
     * 是否公有云
     */
    private boolean isPublic;

    private String desc;

    private List<String> value;

    /**
     * 从云环境类型type还原CloudEnvType枚举时，是否跳过该枚举 eg: OPEN_STACK_ADMIN属于OpenStack系列云环境， 还原云环境类型的枚举时，不用考虑该项
     *
     * 需要skip的枚举，在单独判断时也可使用 eg: CloudEnvType.OPEN_STACK_ADMIN.equals(env.getEnvType())
     */
    private boolean skip;

    CloudEnvType(boolean isPublic, String desc, String... value) {
        this.isPublic = isPublic;
        this.desc = desc;
        this.value = Lists.newArrayList(value);
    }

    CloudEnvType(boolean isPublic, boolean skip, String desc, String... value) {
        this.skip = skip;
        this.isPublic = isPublic;
        this.desc = desc;
        this.value = Lists.newArrayList(value);
    }

    CloudEnvType(boolean isPublic, String desc, List<String> types, String... value) {
        this.isPublic = isPublic;
        this.desc = desc;
        this.value = Lists.newArrayList(value);
        this.value.addAll(types);
    }

    public boolean valueContains(String other) {
        return value.contains(other);
    }

    /**
     * 根据type 返回云环境类型
     *
     * @param type
     */
    public static CloudEnvType from(String type) {
        return Arrays.asList(values())
                     .stream()
                     .filter(v -> !v.skip)
                     .filter(v -> v.valueContains(type))
                     .findFirst()
                     .orElseThrow(() -> new BizException("暂不支持的云环境"));
    }

    @Override
    public String toString() {
        return name();
    }

    public boolean isPublic() {
        return isPublic;
    }

    public String getDesc() {
        return desc;
    }

    public List<String> getValue() {
        return value;
    }
}
