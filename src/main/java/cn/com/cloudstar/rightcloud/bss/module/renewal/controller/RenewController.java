/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.ISfServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.CalculateDateRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.DescribeRenewDetailRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.DescribeRenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.ListProductAndCloudEnvRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.ModifyUnifyDateRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.RenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.response.CalculateDateResponse;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.response.DescribeRenewResponse;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.response.ListProductAndCloudEnvResponse;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.RenewService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.ResRenewRefService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.common.util.Il8lUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.DiscountScope;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResBmsNodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResVmNodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;

/**
 * 续费管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/renew")
@Api("续费管理")
public class RenewController {

    @Autowired
    private ResRenewRefService resRenewRefService;

    @Autowired
    private RenewService renewService;

    @Autowired
    private IServiceOrderService serviceOrderService;
    @Autowired
    private SfProductResourceMapper sfProductMapper;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private BizBillingAccountMapper billingAccountMapper;

    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;
    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;
    @DubboReference
    private ResVmRemoteService resVmRemoteService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    ISfProductResourceService sfProductResourceService;
    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private SysHpcPassService hpcPassService;
    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;
    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;
    @DubboReference
    private ResAllRemoteService resAllRemoteService;

    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private ISfServiceCategoryService sfServiceCategoryService;
    /**
     * 运营控制台
     */
    private static final String FROM_BSS = "bss";

    /**
     * 查询支持的云环境和产品
     *
     * @param request 查询项目下的产品和云环境类型请求体
     * @return {@code ListProductAndCloudEnvResponse}
     */
    @ApiOperation("查询支持的云环境和产品")
    public ListProductAndCloudEnvResponse listProductAndCloudEnv(
            ListProductAndCloudEnvRequest request) {
        return resRenewRefService.listProductAndCloud(request);
    }


    /**
     * 查询续费资源列表
     *
     * @param request 续费列表请求体
     * @return {@code List<DescribeRenewResponse>}
     */
    @ApiOperation("查询续费资源列表")
    @Authorize(action = "billcenter:renew:ListRenewal")
    public List<DescribeRenewResponse> listRenewal(@Valid DescribeRenewRequest request) {
        return BeanConvertUtil.convertHelperPage((Page<?>) resRenewRefService.listRenewal(request), DescribeRenewResponse.class);
    }

    /**
     * 查询续费资源详情
     *
     * @param id      id
     * @param request 查询续费资源详情请求体
     * @return {@code RestResult}
     */
    @ApiOperation("查询续费资源详情")
    @AuthorizeBss(action = CB.CB1907)
    @GetMapping("/resource_detail/{id}")
    @DataPermission(resource = OperationResourceEnum.QUERY_RENEW_DETAIL,bizId = "#id")
    public RestResult<Map<String, Object>> queryRenewDetail(@PathVariable("id") String id,
                                                           @Valid DescribeRenewDetailRequest request) {
        String type = resAllRemoteService.selectProductTypeById(id);
        if (!ProductCodeEnum.SFS2.getProductCode().equals(type)) {
            dataPermission(id);
        }
        return new RestResult(
                ProductCodeEnum.isInnerProduct(request.getProductType()) || ProductCodeEnum.isCmpApiProduct(
                        request.getProductType()) ||ProductCodeEnum.isFederationProduct(
                        request.getProductType()) ? resRenewRefService
                        .renewInnerProductDetail(id, true) : resRenewRefService.renewDetail(id));
    }

    /**
     * 查询续费资源详情
     *
     * @param id      id
     * @param request 查询续费资源详情请求体
     * @return {@code RestResult}
     */
    @ApiOperation("查询续费资源详情")
    @AuthorizeBss(action = AuthModule.BQ.BQ010701)
    @GetMapping("/resource_detail/{id}/feign")
    public RestResult<Map<String, Object>> queryRenewDetailByFeign(@PathVariable("id") String id,
                                                           @Valid DescribeRenewDetailRequest request) {
        AuthUser userInfo = BasicInfoUtil.getCurrentUserInfo();
        if (userInfo == null || !FROM_BSS.equals(userInfo.getRemark())) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        if (ProductCodeEnum.isInnerProduct(request.getProductType())) {
            SfProductResource sfProductResource = sfProductResourceService.lambdaQuery().eq(SfProductResource::getId,id).one();
            if(sfProductResource != null){
                Long entityId = serviceOrderService.lambdaQuery().eq(ServiceOrder::getId, sfProductResource.getServiceOrderId()).one().getEntityId();
                if (userInfo.getEntityId() == null || !userInfo.getEntityId().equals(entityId)) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
        }else{
            ServiceOrderPriceDetail orderPriceDetail = serviceOrderPriceDetailService.lambdaQuery().like(ServiceOrderPriceDetail::getRefInstanceId, id)
                                                                                     .eq(ServiceOrderPriceDetail::getProductCode, request.getProductType())
                                                                                     .eq(ServiceOrderPriceDetail::getType, OrderType.APPLY).one();
            if (orderPriceDetail != null) {
                ServiceOrder serviceOrder = serviceOrderService.lambdaQuery().eq(ServiceOrder::getOrderSn, orderPriceDetail.getOrderSn()).one();
                if (serviceOrder != null) {
                    if (userInfo.getEntityId() == null || !userInfo.getEntityId().equals(serviceOrder.getEntityId())) {
                        throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                    }
                }
            }
        }
        Map<String, Object> stringObjectMap = ProductCodeEnum.isInnerProduct(request.getProductType()) ? resRenewRefService
                .renewInnerProductDetail(id, true) : resRenewRefService.renewDetail(id);
        // 国际化处理
        if (WebUtil.getHeaderAcceptLanguage()) {
            stringObjectMap.put("config", Il8lUtil.il8l(stringObjectMap.get("config").toString()));
            String endTime = stringObjectMap.get("endTime").toString();
            if (StrUtil.isNotBlank(endTime)) {
                stringObjectMap.put("endTime",cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.parse(endTime), DatePattern.HTTP_DATETIME_FORMAT.getPattern()));
            }
        }
        return new RestResult(stringObjectMap);
    }

    /**
     * 查询统一到期时间
     *
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CB.CB1907)
    @ApiOperation("查询统一到期时间")
    @GetMapping("/unify_date")
    public RestResult<HashMap<Object, Object>> queryUnifyDate() {
        User authUser = AuthUtil.getAuthUser();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("unifyDate", resRenewRefService.selectUnifyDate(authUser.getOrgSid()));
        return new RestResult(map);
    }
    /**
     * [INNER API] 查询统一到期时间
     *
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("查询统一到期时间")
    @GetMapping("/unify_date/feign")
    public RestResult<HashMap<Object, Object>> queryUnifyDateFeign() {
        User authUser = AuthUtil.getAuthUser();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("unifyDate", resRenewRefService.selectUnifyDate(authUser.getOrgSid()));
        return new RestResult(map);
    }


    /**
     * 修改统一到期时间
     *
     * @param request 修改统一到期时间请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.BQ.BQ010701)
    @ApiOperation("修改统一到期时间")
    @PutMapping("/unify_date")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName ="#request.unifyDate", resource = OperationResourceEnum.MODIFY_UNIFYDATE)
    public RestResult modifyUnifyDate(@RequestBody @Valid ModifyUnifyDateRequest request) {
        Long orgSid = AuthUtil.getCurrentOrgSid();
        if (Objects.nonNull(orgSid)) {
            resRenewRefService.modifyUnifyDate(orgSid, request.getUnifyDate());
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 续费
     *
     * @param request 续订请求体
     * @return {@code RestResult}
     */
    @ApiOperation("续费")
    @PostMapping
    //@SmsValidation
    @AuthorizeBss(action = CB.CB19 + "," + BQ.BQ0107)
    @OperationLog(type = OperationTypeEnum.UPDATE, param = "#request", tagName = "'续费'", bizId = "#request.userSid", tagNameUs ="'Renewal'", 
            resource = OperationResourceEnum.RENEW_RESOURCE)
    @ListenExpireBack
    @DataPermission(resource = OperationResourceEnum.RENEW_RESOURCE,param = "#request")
    public RestResult renewResource(@RequestBody @Valid RenewRequest request) {
        AuthUser loginUser =  RequestContextUtil.getAuthUserInfo();
        SfProductResource sfProductResource = null;
        if (ProductCodeEnum.SFS2.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            ProductInfoVO productInfoVO = request.getProductInfo().get(0);
            serviceOrderService.checkPendingOrder(productInfoVO.getId(), productInfoVO.getProductCode());
            log.info("弹性文件退订id {}",request.getProductInfo().get(0).getId());
        } else if (ProductCodeEnum.ECS.getProductCode().equals(request.getProductInfo().get(0).getProductCode())){
            for (ProductInfoVO productInfoVO : request.getProductInfo()) {
                sfProductResource = sfProductResourceMapper.selectById(Long.valueOf(productInfoVO.getId()));
                if(sfProductResource!=null && StringUtils.equals(sfProductResource.getProductType(), productInfoVO.getProductCode())) {
                    if (!Constants.NORMAL.equals(sfProductResource.getStatus()) &&
                            !Constants.FROZEN.equals(sfProductResource.getStatus()) &&
                            !SfProductEnum.EXPIRED.getStatus().equals(sfProductResource.getStatus())) {
                        String nameByI18n = SfProductEnum.getNameByI18n(sfProductResource.getStatus(), WebUtil.getHeaderAcceptLanguage());
                        throw new BizException(WebUtil.getMessage(MsgCd.CURRENT_OPERATION_NOT_SUPPORTED, new Object[]{nameByI18n}));
                    }
                }else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
                }
            }
        } else if (ProductCodeEnum.DCS.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            request.getProductInfo().get(0).setQuantity(1);
        } else {
            sfProductResource = sfProductResourceMapper.selectById(Long.valueOf(request.getProductInfo().get(0).getId()));
            if(sfProductResource!=null && StringUtils.equals(sfProductResource.getProductType(), request.getProductInfo().get(0).getProductCode())) {
                if (!Constants.NORMAL.equals(sfProductResource.getStatus()) &&
                        !Constants.FROZEN.equals(sfProductResource.getStatus()) &&
                    !SfProductEnum.EXPIRED.getStatus().equals(sfProductResource.getStatus())) {
                    String nameByI18n = SfProductEnum.getNameByI18n(sfProductResource.getStatus(), WebUtil.getHeaderAcceptLanguage());
                    throw new BizException(WebUtil.getMessage(MsgCd.CURRENT_OPERATION_NOT_SUPPORTED, new Object[]{nameByI18n}));
                }
            }else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
            }
        }
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            cn.com.cloudstar.rightcloud.module.support.access.pojo.Org org = BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid());
            AuthUserHolder.setOrg(org);
        }
        User applyUser = AuthUtil.getAuthUser();

        //校验开通参数 订购时常和弹性文件名称
        //校验开通参数 订购时常和弹性文件名称
        List<String> chargetType = request.getProductInfo()
                                          .stream()
                                          .map(ProductInfoVO::getChargeType)
                                          .collect(Collectors.toList());
     if(chargetType.size()>0){
            if(("PrePaid").equals(chargetType.get(0))){
                List<BigDecimal> periods = request.getProductInfo()
                                                  .stream()
                                                  .map(ProductInfoVO::getPeriod)
                                                  .collect(Collectors.toList());
                List<String> names = request.getProductInfo()
                                                  .stream()
                                                  .map(ProductInfoVO::getName)
                                                  .collect(Collectors.toList());
                if(periods.size()>0){
                    if(new BigDecimal(periods.get(0).intValue()).compareTo(periods.get(0))!=0 || (periods.get(0).intValue()<1) || (periods.get(0).intValue()>120)){
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_244609625)+periods.get(0)+WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
                //产品名称不能为空
                if(names.size()==0){
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }
                if(names.size()>0){
                    if(Objects.isNull(names.get(0)) || "".equals(names.get(0))){
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                    }
                }else{
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }

            }
        }
        //判断账户是否冻结
        ServiceOrder serviceOrder = null;
        if (ProductCodeEnum.SFS2.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            serviceOrder = serviceOrderService.selectOrderDetailByResourceId(
                    request.getProductInfo().get(0).getId()
                    , request.getProductInfo().get(0).getProductCode());
        }else {
            sfProductResource = sfProductMapper.selectById(request.getProductInfo().get(0).getId());
        if(Objects.nonNull(sfProductResource)){
            //判断是不是续订中，如果是不让续订
            if(SfProductEnum.RENEWING.getStatus().equals(sfProductResource.getStatus())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1464118032));
            }
            SfProductResource finalSfProductResource = sfProductResource;
            if (Stream.of(SfProductEnum.NORMAL, SfProductEnum.EXPIRED, SfProductEnum.FROZEN, SfProductEnum.SOONEXPIRE)
                      .noneMatch(e -> e.getStatus().equals(finalSfProductResource.getStatus()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1707031022));
            }

             serviceOrder = serviceOrderMapper.selectById(sfProductResource.getServiceOrderId());
            }
        }
            if(Objects.nonNull(serviceOrder)){
                User authUser = AuthUtil.getAuthUser();
                    if(serviceOrder.getBehalfUserSid() != null){
                        Criteria cw = new Criteria();
                        cw.put("userSid",loginUser.getUserSid());
                        cw.put("roleSid",301);
                        List<UserRole> roles = userRoleMapper.selectByParams(cw);
                        if(CollectionUtil.isEmpty(roles)){
                            throw new BizException(WebUtil.getMessage(MsgCd.NO_PERMISSION_OPERATION));
                        }
                    }

                List<BizBillingAccount> accounts = billingAccountMapper.findSelfAndSelfCustomer(
                        authUser.getUserSid(), serviceOrder.getEntityId());
                if(accounts.size()>0) {
                    BizBillingAccount account = accounts.get(0);
                    if (SysMNotifyConfigConstant.ExpireStrategyEnum.FREEZE.getValue().equals(account.getStatus())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729724604));
                    }
                    if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1095922662));
                    }
                }

            }

        if(Objects.nonNull(sfProductResource) && Objects.nonNull(serviceOrder)) {
            String productType = sfProductResource.getProductType();
            if (productType.equals(ProductCodeEnum.HPC.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP.getProductCode()) ||
                    productType.equals(ProductCodeEnum.HPC_SAAS.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP_STANDARD.getProductCode())) {
                String password = hpcPassService.findPassword(Long.valueOf(serviceOrder.getOwnerId()));
                if (password == null) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1094852577));
                }
            }
        }

        // 续费
        // 2022-09-26 shupf修改 是HPC資源才执行
        RestResult result = renewService.renew(request);
        if(Objects.nonNull(sfProductResource)){
            String productType = sfProductResource.getProductType();
            if (productType.equals(ProductCodeEnum.HPC.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP.getProductCode()) ||
                    productType.equals(ProductCodeEnum.HPC_SAAS.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP_STANDARD.getProductCode())) {
                // //保存变更记录
                 ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
                 //获取配置
                 Long clusterId = sfProductResource.getClusterId();
               //  String nodeInfo = hpcRemoteService.getNodeInfo(clusterId);
              //   resChangeRecordDTO.setConfigDesc(nodeInfo);

                 saveResChangeRecord(clusterId,resChangeRecordDTO,request);
                 //插入
                 resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
            }
        }
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return result;
    }

    /**
     * 续费
     *【Since v2.5.0】
     * [INNER API] 续费
     * @param request 续订请求体
     * @return {@code RestResult}
     */
    @ApiOperation("续费")
    @PostMapping("/feign")
    @RejectCall
    public RestResult renewResourceFeign(@RequestBody @Valid RenewRequest request) {
        if (StringUtils.isNotBlank(request.getProductInfo().get(0).getProductCode())) {
            Integer count = sfServiceCategoryService.lambdaQuery().eq(ServiceCategory::getServiceType, request.getProductInfo().get(0).getProductCode())
                .eq(ServiceCategory::getEntityId, RequestContextUtil.getEntityId()).count();
            if (count == 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
        }
        SfProductResource sfProductResource = null;
        if (ProductCodeEnum.SFS2.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            ProductInfoVO productInfoVO = request.getProductInfo().get(0);
            serviceOrderService.checkPendingOrder(productInfoVO.getId(), productInfoVO.getProductCode());
            log.info("弹性文件退订id {}",request.getProductInfo().get(0).getId());
        }else{
            sfProductResource = sfProductResourceMapper.selectById(Long.valueOf(request.getProductInfo().get(0).getId()));
            if(sfProductResource!=null && StringUtils.equals(sfProductResource.getProductType(), request.getProductInfo().get(0).getProductCode())) {
                if (!Constants.NORMAL.equals(sfProductResource.getStatus()) &&
                        !Constants.FROZEN.equals(sfProductResource.getStatus()) &&
                        !SfProductEnum.EXPIRED.getStatus().equals(sfProductResource.getStatus())
                ) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1373253110));
                }
            }else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
            }
        }
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()),
                                                                                                            cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        User applyUser = AuthUtil.getAuthUser();
//        if(!sfProductResource.getOrgSid().equals(applyUser.getOrgSid())){
//
//        }

        //校验开通参数 订购时常和弹性文件名称
        //校验开通参数 订购时常和弹性文件名称
        List<String> chargetType = request.getProductInfo()
                                          .stream()
                                          .map(ProductInfoVO::getChargeType)
                                          .collect(Collectors.toList());
        if(chargetType.size()>0){
            if(("PrePaid").equals(chargetType.get(0))){
                List<BigDecimal> periods = request.getProductInfo()
                                                  .stream()
                                                  .map(ProductInfoVO::getPeriod)
                                                  .collect(Collectors.toList());
                List<String> names = request.getProductInfo()
                                            .stream()
                                            .map(ProductInfoVO::getName)
                                            .collect(Collectors.toList());
                if(periods.size()>0){
                    if(new BigDecimal(periods.get(0).intValue()).compareTo(periods.get(0))!=0 || (periods.get(0).intValue()<1) || (periods.get(0).intValue()>120)){
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_244609625)+periods.get(0)+WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
                //产品名称不能为空
                if(names.size()==0){
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }
                if(names.size()>0){
                    if(Objects.isNull(names.get(0)) || "".equals(names.get(0))){
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                    }
                }else{
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }

            }
        }
        //判断账户是否冻结
        ServiceOrder serviceOrder = null;
        if (ProductCodeEnum.SFS2.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            serviceOrder = serviceOrderService.selectOrderDetailByResourceId(
                    request.getProductInfo().get(0).getId()
                    , request.getProductInfo().get(0).getProductCode());
        }else {
            sfProductResource = sfProductMapper.selectById(request.getProductInfo().get(0).getId());
            if (Objects.nonNull(sfProductResource)) {
                //判断是不是续订中，如果是不让续订
                if(SfProductEnum.RENEWING.getStatus().equals(sfProductResource.getStatus())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1464118032));
                }
                serviceOrder = serviceOrderMapper.selectById(sfProductResource.getServiceOrderId());
            }
        }
        if(Objects.nonNull(serviceOrder)){
            User authUser = AuthUtil.getAuthUser();
            List<BizBillingAccount> accounts = billingAccountMapper.findSelfAndSelfCustomer(
                    authUser.getUserSid(), serviceOrder.getEntityId());
            if(accounts.size()>0){
                BizBillingAccount account = accounts.get(0);
                if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1095922662));
                }
                if(SysMNotifyConfigConstant.ExpireStrategyEnum.FREEZE.getValue().equals(account.getStatus())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729724604));
                }
            }

        }

        if(Objects.nonNull(sfProductResource) && Objects.nonNull(serviceOrder)) {
            String productType = sfProductResource.getProductType();
            if (productType.equals(ProductCodeEnum.HPC.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP.getProductCode()) ||
                    productType.equals(ProductCodeEnum.HPC_SAAS.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP_STANDARD.getProductCode())) {
                String password = hpcPassService.findPassword(Long.valueOf(serviceOrder.getOwnerId()));
                if (password == null) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1094852577));
                }
            }
        }

        // 续费
        // 2022-09-26 shupf修改 是HPC資源才执行
        RestResult result = renewService.renew(request);

        if(Objects.nonNull(sfProductResource)){
            String productType = sfProductResource.getProductType();
            if (productType.equals(ProductCodeEnum.HPC.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP.getProductCode()) ||
                    productType.equals(ProductCodeEnum.HPC_SAAS.getProductCode()) || productType.equals(ProductCodeEnum.HPC_DRP_STANDARD.getProductCode())) {
                // //保存变更记录
                ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
                //获取配置
                Long clusterId = sfProductResource.getClusterId();
             //   String nodeInfo = hpcRemoteService.getNodeInfo(clusterId);
             //   resChangeRecordDTO.setConfigDesc(nodeInfo);

                saveResChangeRecord(clusterId,resChangeRecordDTO,request);
                //插入
                resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
            }
        }

        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
        }
        return result;
    }

    private ResChangeRecordDTO saveResChangeRecord(Long clusterId, ResChangeRecordDTO resChangeRecordDTO ,RenewRequest request) {
        //获取时间
        ProductInfoVO productInfo = request.getProductInfo().get(0);
        String productConfigDesc = JSONUtil.toJsonStr(productInfo.getProductConfigDesc());
        cn.hutool.json.JSONObject productConfigDescJsonOB = JSONUtil.parseObj(productConfigDesc);
        cn.hutool.json.JSONArray changeDescJsonArr = productConfigDescJsonOB.getJSONArray("changeDesc");
        Date oldEndTime;
        Date newEndTime;
        cn.hutool.json.JSONObject changeJsonOB = (cn.hutool.json.JSONObject)changeDescJsonArr.get(0);
        String oldValue = changeJsonOB.getStr("oldValue");
        String newValue = changeJsonOB.getStr("newValue");
        oldEndTime = cn.hutool.core.date.DateUtil.parse(oldValue);
        newEndTime = cn.hutool.core.date.DateUtil.parse(newValue);
        //获取节点
        List<NodeInfo> nodeInfoList = getNodeInfos(clusterId);
        nodeInfoList.removeIf(nodeInfo -> ResHpcClusterNodeStatus.REMOVING.equals(nodeInfo.getStatus()));
        Date curr = new Date();
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        //插入变更记录
        resChangeRecordDTO.setCloudEnvId(resHpcCluster.getCloudEnvId());
        resChangeRecordDTO.setResourceId(resHpcCluster.getId().toString());
        resChangeRecordDTO.setInstanceId(resHpcCluster.getResourceId());
        resChangeRecordDTO.setResType(ProductCodeEnum.HPC_DRP.getProductCode());
        long nodeNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType()))
                .count();
        resChangeRecordDTO.setOriginalType(String.valueOf(nodeNum));
        resChangeRecordDTO.setCreatedDt(curr);
        resChangeRecordDTO.setCreatedBy(resHpcCluster.getCreatedBy());
        resChangeRecordDTO.setUpdatedDt(curr);
        resChangeRecordDTO.setUpdatedBy(resHpcCluster.getCreatedBy());
        resChangeRecordDTO.setOrgSid(resHpcCluster.getOrgSid());
        resChangeRecordDTO.setOwnerId(resHpcCluster.getOwnerId());
        resChangeRecordDTO.setChangeStartTime(oldEndTime);
        String oldExtraJson = getExtraJson(nodeInfoList);
        resChangeRecordDTO.setOriginalExtra(oldExtraJson);
        resChangeRecordDTO.setNewType(String.valueOf(nodeNum));
        resChangeRecordDTO.setNewExtra(oldExtraJson);
        resChangeRecordDTO.setChangeEndTime(newEndTime);
        resChangeRecordDTO.setUpdatedDt(curr);
        resChangeRecordDTO.setChangeType(DiscountScope.RENEW);
        return resChangeRecordDTO;
    }

    private List<NodeInfo> getNodeInfos(Long clusterId) {
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
        if(CollectionUtil.isNotEmpty(bmsInfoList)){
            nodeInfoList.addAll(bmsInfoList);
        }
        List<ResVmNodeInfo> resInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resInfoList)) {
            nodeInfoList.addAll(resInfoList);
        }
        return nodeInfoList;
    }

    private String getExtraJson(List<NodeInfo> nodeInfoList) {
        long vncNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HpcPointType.VNC.equals(resource.getHpcPointType()))
                .count();
        long cliNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HpcPointType.CCS_CLI.equals(resource.getHpcPointType()))
                .count();
        long computeNum = nodeInfoList.stream()
                .filter(resource -> "compute".equals(resource.getNodeType()))
                .count();
        Map<String, Long> nodeMap = new HashMap<>();
        nodeMap.put("计算节点数", computeNum);
        nodeMap.put("登录节点数", cliNum);
        nodeMap.put("VNC节点数", vncNum);
        return JSON.toJSONString(nodeMap);
    }

    /**
     * 计算到期时间
     *
     * @param request 计算到期时间请求体
     * @return {@code CalculateDateResponse}
     */
    @AuthorizeBss(action = AuthModule.CB.CB1907)
    @ApiOperation("计算到期时间")
    @GetMapping("/date")
    public CalculateDateResponse calculateDate(@Valid CalculateDateRequest request) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        DateTime endTimeBeforeRenew = null;
        try {
            Date endTime = format.parse(request.getEndTime());
            endTimeBeforeRenew = DateTime.of(endTime);
        } catch (ParseException e) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_849145697));
        }

        CalculateDateResponse calculateDateResponse = new CalculateDateResponse();
        Integer period = request.getPeriod();
        String frozenTime = request.getFrozenTime();
        DateTime frozenDateTime = null;
        Integer makeUpOffDay= 0;
        if(StringUtil.isNotEmpty(frozenTime)){
            frozenDateTime = DateTime.of(frozenTime, "yyyy年MM月dd日 HH:mm:ss");
            makeUpOffDay = DateUtil.calculateOffDay(endTimeBeforeRenew,frozenDateTime);
        }
        DateTime now = DateTime.now();
        Integer offDay = DateUtil.calculateOffDay(endTimeBeforeRenew, now);
        if(frozenDateTime !=null){
            if(makeUpOffDay>=offDay){
                endTimeBeforeRenew =  endTimeBeforeRenew.offsetNew(DateField.HOUR,makeUpOffDay*24);
            }else{
                endTimeBeforeRenew = now;
            }
        }else if(offDay>0){
            endTimeBeforeRenew =  endTimeBeforeRenew.offsetNew(DateField.HOUR,offDay*24);
        }

        DateTime endTimeAfterRenew = endTimeBeforeRenew.offsetNew(DateField.MONTH, period);
        DateTime clone = (DateTime) endTimeAfterRenew.clone();
        if (Objects.nonNull(request.getUnifyDate())) {
            // 如果设置了统一到期时间，计算新的续费时长
            int i = endTimeAfterRenew.dayOfMonth();
            if (i > request.getUnifyDate()) {
                endTimeAfterRenew.offset(DateField.DAY_OF_MONTH, 30);
            }
            endTimeAfterRenew.setField(DateField.DAY_OF_MONTH, request.getUnifyDate());
            endTimeAfterRenew.setField(DateField.HOUR_OF_DAY, 23);
            endTimeAfterRenew.setField(DateField.MINUTE, 59);
            endTimeAfterRenew.setField(DateField.SECOND, 59);
        }
        long offset = (endTimeAfterRenew.getTime() - clone.getTime()) / (24 * 60 * 60 * 1000);
        calculateDateResponse.setEndTime(endTimeAfterRenew.toString("yyyy年MM月dd日 HH:mm:ss"));
        calculateDateResponse.setPeriod(period);
        calculateDateResponse.setOffset(offset);
        return calculateDateResponse;
    }

    /**
     * [INNER API] 计算到期时间
     *
     * @param request 计算到期时间请求体
     * @return {@code CalculateDateResponse}
     */
    @RejectCall
    @ApiOperation("计算到期时间")
    @GetMapping("/date/feign")
    public RestResult<CalculateDateResponse> calculateDateFeign(@Valid CalculateDateRequest request) {
        CalculateDateResponse calculateDateResponse = new CalculateDateResponse();
        Integer period = request.getPeriod();
        DateTime endTimeBeforeRenew = DateTime.of(request.getEndTime(), "yyyy年MM月dd日 HH:mm:ss");
        String frozenTime = request.getFrozenTime();
        DateTime frozenDateTime = null;
        Integer makeUpOffDay= 0;
        if(StringUtil.isNotEmpty(frozenTime)){
            frozenDateTime = DateTime.of(frozenTime, "yyyy年MM月dd日 HH:mm:ss");
            makeUpOffDay = DateUtil.calculateOffDay(endTimeBeforeRenew,frozenDateTime);
        }
        DateTime now = DateTime.now();
        Integer offDay = DateUtil.calculateOffDay(endTimeBeforeRenew, now);
        if(frozenDateTime !=null){
            if(makeUpOffDay>=offDay){
                endTimeBeforeRenew =  endTimeBeforeRenew.offsetNew(DateField.HOUR,makeUpOffDay*24);
            }else{
                endTimeBeforeRenew = now;
            }
        }else if(offDay>0){
            endTimeBeforeRenew =  endTimeBeforeRenew.offsetNew(DateField.HOUR,offDay*24);
        }

        DateTime endTimeAfterRenew = endTimeBeforeRenew.offsetNew(DateField.MONTH, period);
        DateTime clone = (DateTime) endTimeAfterRenew.clone();
        if (Objects.nonNull(request.getUnifyDate())) {
            // 如果设置了统一到期时间，计算新的续费时长
            int i = endTimeAfterRenew.dayOfMonth();
            if (i > request.getUnifyDate()) {
                endTimeAfterRenew.offset(DateField.DAY_OF_MONTH, 30);
            }
            endTimeAfterRenew.setField(DateField.DAY_OF_MONTH, request.getUnifyDate());
            endTimeAfterRenew.setField(DateField.HOUR_OF_DAY, 23);
            endTimeAfterRenew.setField(DateField.MINUTE, 59);
            endTimeAfterRenew.setField(DateField.SECOND, 59);
        }
        long offset = (endTimeAfterRenew.getTime() - clone.getTime()) / (24 * 60 * 60 * 1000);
        calculateDateResponse.setEndTime(endTimeAfterRenew.toString("yyyy年MM月dd日 HH:mm:ss"));
        calculateDateResponse.setPeriod(period);
        calculateDateResponse.setOffset(offset);
        return new RestResult(calculateDateResponse);
    }

    private void dataPermission(String id) {
        User authUser = AuthUtil.getAuthUser();
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(id);

        if (ObjectUtil.isNull(sfProductResource)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }
        if(authUser.getUserSid().equals(sfProductResource.getOrgSid())){
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }

        QueryWrapper<ServiceOrderResourceRef> refQuery = new QueryWrapper<>();
        refQuery.lambda().eq(ServiceOrderResourceRef::getResourceId,sfProductResource.getId()).eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType());
        ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(refQuery);
        if (ObjectUtil.isNull(serviceOrderResourceRef)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceOrderDetail orderDetail = serviceOrderDetailService.getById(serviceOrderResourceRef.getOrderDetailId());
        if (ObjectUtil.isNull(orderDetail)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceOrderVo serviceOrderVo = serviceOrderMapper.selectOrderDetailById(orderDetail.getOrderId());
        if (ObjectUtil.isNull(serviceOrderVo)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }
        if (!serviceOrderVo.getOwnerId().equals(String.valueOf(authUser.getUserSid()))){
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(
                    MsgCd.AUTHORIZE_FAILURE));
        }
    }

}
