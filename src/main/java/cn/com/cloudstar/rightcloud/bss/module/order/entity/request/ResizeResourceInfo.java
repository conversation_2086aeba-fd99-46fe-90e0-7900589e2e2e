/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.entity.request;


import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.oss.common.util.annotation.StartWithWord;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/28 11:39
 */
@Data
@ApiModel("资源配置")
public class ResizeResourceInfo {

    /**
     * id
     */
    @ApiModelProperty("资源ID")
    @NotBlank
    private String id;

    /**
     * 变更目标类型
     */
    //@StartWithWord(words = {"SFS","HPC"}, message = "该资源类型不存在")
    @ApiModelProperty("变更目标类型")
    private String resourceType;

    /**
     * 变更目标大小
     */
    @ApiModelProperty("变更目标大小")
    //@Min(1)
    //@Max(1000)
    @NotNull
    private Integer size;

    private String spec;
}
