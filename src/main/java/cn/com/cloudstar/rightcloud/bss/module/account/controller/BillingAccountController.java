/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.PayStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.MessageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserExportDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.ExportBizAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.AccountExpireClearRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.AmountClearanceRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeDepositRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.MarketOrderPostingRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.RechargeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.SetExpirationTimeRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateBusinessTagRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateCreditLineRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateDistributorRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateFreeCapacityRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateRemarkRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.UpdateSolutionRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.AccountsSimpleResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeBillingAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeDepositResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeOverviewResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeRechargeAccountInfoResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.ResultUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.SendZipCompressPasswordRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.entity.SysMProvider;
import cn.com.cloudstar.rightcloud.bss.module.provider.service.ISysMProviderService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.util.BeanCopierUtils;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.service.ExcelExportService;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysMProviderStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.CustomBusinessTag;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.CheckSmsCodeUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.bss.common.enums.ScopeTypeEnum.UNLIMITED;

/**
 * 账户管理
 *
 * <AUTHOR>
 * @date 2019/10/17.
 */
@Api(tags = "账户管理")
@RestController
@RequestMapping("/billing_account")
@Slf4j
public class BillingAccountController {

    private final static String NORMAL = "normal";
    private final static String ONE = "1";
    private final static String ZERO = "0";

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SysUserService userService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private FeignService feignService;

    @Autowired
    BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private IBizDistributorService bizDistributorService;
    @Autowired
    private ISysMProviderService sysMProviderService;



    private static final Map<String, String> STATUS = Maps.newHashMap();

    //客户信息的认证状态
    private static final Map<String, String> CERTIFICATION_STATUS = Maps.newHashMap();

    static {
        STATUS.put("0", "禁用");
        STATUS.put("1", "启用");
        STATUS.put("2", "待审核");
        STATUS.put("4", "已拒绝");

        CERTIFICATION_STATUS.put("noAuth", "待认证");
        CERTIFICATION_STATUS.put("authing", "认证中");
        CERTIFICATION_STATUS.put("authSucceed", "认证成功");
        CERTIFICATION_STATUS.put("authFiled", "认证失败");
    }

    /**
     * 试用期账号批量设置到期时间和处理策略(客户管理-客户信息)
     *【Since v2.5.0】
     * [INNER API] 试用期账号批量设置到期时间和处理策略
     * @param request 试用期账号批量设置到期时间和处理策略
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "POST", value = "试用期账号批量设置到期时间和处理策略")
    @PutMapping("/expirationTime")
    public RestResult setExpirationTime(@RequestBody @Valid SetExpirationTimeRequest request) {
        if (request.getEndTime() != null) {
            long year = cn.hutool.core.date.DateUtil.betweenYear(request.getEndTime(), new Date(), true);
            if (year > 10) {
                BizException.e("选择日期距离当天最多10年");
            }
        }
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        boolean success = bizBillingAccountService.setExpirationTime(request);
        if(success){
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        }else{
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
    }

    /**
     * [INNER API] 获取账户列表
     *
     * @param request 账户列表查询请求体
     *
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.1
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @GetMapping()
    public IPage<DescribeBillingAccountResponse> findBillingAccounts(DescribeBillingAccountRequest request) {
        request.setEntityId(RequestContextUtil.getEntityId());

        IPage<BizBillingAccount> billingAccountList = bizBillingAccountService.getBillingAccountList(request);
        long total = billingAccountList.getRecords().size();
        if (total == 0) {
            // 字段名变更，配合前端逻辑
            IPage<DescribeBillingAccountResponse> describeBillingAccountResponseIPage = BeanConvertUtil.convertPage(
                billingAccountList,
                DescribeBillingAccountResponse.class);
            return describeBillingAccountResponseIPage;
        }

        String obsFree = PropertiesUtil.getProperty("obs_free_capacity");

        List<Long> orgSidList = billingAccountList.getRecords().stream().map(BizBillingAccount::getOrgSid).collect(Collectors.toList());
        List<User> userList = userService.selectByParams(new Criteria("orgSids", orgSidList));
        Map<Long, List<User>> orgSidUserMap = userList.stream().collect(Collectors.groupingBy(User::getOrgSid));

        List<Org> orgList = orgService.lambdaQuery().in(Org::getOrgSid, orgSidList).list();
        Map<Long, Org> orgMap = orgList.stream().collect(Collectors.toMap(Org::getOrgSid, org -> org));

        List<SysMProvider> sysMProviderList = sysMProviderService.lambdaQuery()
            .in(SysMProvider::getCompanyId, orgSidList)
            .orderByDesc(SysMProvider::getCreatedDt)  // 按创建时间降序排列
            .list();
        Map<Long, SysMProvider> companyIdProviderMap = sysMProviderList.stream().collect(Collectors.toMap(SysMProvider::getCompanyId, Function.identity(), (v1, v2) -> v1));

        //新增企业认证的信息
        billingAccountList.getRecords().forEach(bizBillingAccount -> {
            Org org = rootOrg(bizBillingAccount.getOrgSid(),orgMap);
            if (Objects.nonNull(org)) {
                CurrentOrgVO currentOrgVO = new CurrentOrgVO();
                BeanUtils.copyProperties(org, currentOrgVO);
                bizBillingAccount.setCurrentOrg(currentOrgVO);
            }
            BigDecimal obs = bizBillingAccount.getObsFreeCapacity();
            bizBillingAccount.setObsFreeCapacity(obs == null ? new BigDecimal(obsFree) : obs);
        });
        // 字段名变更，配合前端逻辑
        IPage<DescribeBillingAccountResponse> describeBillingAccountResponseIPage = BeanConvertUtil.convertPage(
                billingAccountList,
                DescribeBillingAccountResponse.class);
        List<DescribeBillingAccountResponse> records = describeBillingAccountResponseIPage.getRecords();
        List<DescribeBillingAccountResponse> subUsers = new ArrayList<>();
        for (DescribeBillingAccountResponse data : records) {
            data.setFreezeStatus(NORMAL.equals(data.getStatus()) ? ONE : ZERO);
            data.setStatus(data.getUserStatus());
            if (StringUtils.isBlank(data.getIndustryName())) {
                data.setIndustry("otherIndusty");
                data.setIndustryName("其他");
            }
            if (request.getSubUser()) {
                List<User> users = orgSidUserMap.get(data.getOrgSid());

                users = users.stream()
                             .filter(t -> !data.getAccount().equals(t.getAccount()))
                             .collect(Collectors.toList());
                for (User subUser : users) {
                    DescribeBillingAccountResponse describeBillingAccountResponse = new DescribeBillingAccountResponse();
                    describeBillingAccountResponse.setAccount(subUser.getAccount());
                    describeBillingAccountResponse.setAdminName(subUser.getRealName());
                    subUsers.add(describeBillingAccountResponse);
                }
            }
            if (CCSPCacheUtil.ccspServiceOpen() && StringUtils.isNotBlank(data.getSalesmen())) {
                data.setSalesmen(CCSPCacheUtil.verifyAndCCSPDecrypt(data.getSalesmen()));
            }
            data.setProviderStatus(SysMProviderStatusEnum.UNENTERED.getCode());
            Optional.ofNullable(companyIdProviderMap.get(data.getOrgSid())).ifPresent(sysMProvider -> {
                Optional.ofNullable(sysMProvider.getId()).ifPresent(id -> data.setProviderId(String.valueOf(id)));
                data.setProviderCompanyName(sysMProvider.getCompanyName());
                data.setProviderStatus(sysMProvider.getStatus());
            });
        }
        if (subUsers.size() > 0) {
            records.addAll(subUsers);
        }
        DesensitizationUtil.desensitization(records);
        return describeBillingAccountResponseIPage;
    }

    /**
     * 获取root组织
     * @param orgSid
     * @param orgMap
     * @return
     */
    private Org rootOrg(Long orgSid, Map<Long, Org> orgMap) {
        if (Objects.isNull(orgSid)) {
            return null;
        }
        Org org = orgMap.get(orgSid);
        if (Objects.isNull(org)) {
            org = this.orgService.getById(orgSid);
        }
        if (Objects.isNull(org)) {
            return null;
        }
        if ("company".equals(org.getOrgType())||Objects.isNull(org.getParentId())) {
            return org;
        } else {
            return rootOrg(org.getParentId(),orgMap);
        }
    }


    /**
     * [INNER API] 获取账户详情
     *
     * @param id 账户id
     *
     * @return {@code DescribeBillingAccountResponse}
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "获取账户详情")
    @GetMapping("/{id}")
    public RestResult<DescribeBillingAccountResponse> getBillingAccountDetail(@PathVariable("id")
                                                                              @ApiParam(value = "账户ID", type = "Long", required = true) Long id) {

        DescribeBillingAccountResponse convert = BeanConvertUtil.convert(
                bizBillingAccountService.getBillingAccountDetail(id), DescribeBillingAccountResponse.class);

        //查询供应商状态和公司名称
        Optional<SysMProvider> lastProviderInfoOp = sysMProviderService.lambdaQuery()
            .in(SysMProvider::getCompanyId, convert.getOrgSid())
            .orderByDesc(SysMProvider::getId)
            .list()
            .stream().findFirst();
        convert.setProviderStatus(SysMProviderStatusEnum.UNENTERED.getCode());
        lastProviderInfoOp.ifPresent(provider -> {
            convert.setProviderStatus(provider.getStatus());
            convert.setProviderCompanyName(provider.getCompanyName());
        });
        DesensitizationUtil.doDesensitization(convert);
        if (CCSPCacheUtil.ccspServiceOpen() && StringUtils.isNotBlank(convert.getSalesmen())) {
            convert.setSalesmen(CCSPCacheUtil.verifyAndCCSPDecrypt(convert.getSalesmen()));
        }
        return new RestResult(convert);
    }

    /**
     * [INNER API] 账户充值
     *
     * @param request 账户充值请求体
     *
     * @return {@code RestResult}
     * @since 2.4.1
     */
    @RejectCall
    @DataPermission(resource = OperationResourceEnum.CHARGE, bizId = "#request.id")
    @ApiOperation(httpMethod = "POST", value = "账户充值")
    @PostMapping("/recharge")
    public RestResult<Long> recharge(@RequestBody @Valid RechargeBillingAccountRequest request) {
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getById(request.getId());
        if (Objects.nonNull(bizBillingAccount) && !bizBillingAccount.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());

        String description = request.getDescription();
        if (StringUtil.isNotEmpty(description) && description.length() > 110) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_902136194));
        }

        if (bizBillingAccountService.recharge(request)) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ACCOUNT_RECHARGE_SUCCESS), request.getId());
        }
        return new RestResult(RestResult.Status.FAILURE,
                              WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
    }

    /**
     * 市场订单入账
     *
     * @param request 账户充值请求体
     * @return {@link RestResult}<{@link Long}>
     */
    @RejectCall
    // @DataPermission(resource = OperationResourceEnum.CHARGE, bizId = "#request.id")
    @ApiOperation(httpMethod = "POST", value = "市场订单入账")
    @PostMapping("/order_posting")
    public RestResult orderPosting(@RequestBody @Valid MarketOrderPostingRequest request) {
        if (bizBillingAccountService.orderPosting(request)) {
            return new RestResult(RestResult.Status.SUCCESS);
        }
        return new RestResult(RestResult.Status.FAILURE,
                              MessageUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
    }


    /**
     * 根据id允许模型集市开具发票
     *
     * @param id 订单周期id
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "PUT", value = "允许开票")
    @PutMapping("/allow_invoicing/{id}")
    RestResult allowInvoicingById(@PathVariable("id") String id){
        if (bizBillingAccountService.allowInvoicingById(id)) {
            return new RestResult(RestResult.Status.SUCCESS);
        }
        return new RestResult(RestResult.Status.FAILURE,
                              MessageUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
    }


    /**
     * 账户开票升级
     *【Since v2.5.0】
     */
    public void invoicingUpgrade() {
        bizBillingAccountService.invoicingUpgrade();
    }

    /**
     * 获取充值账户信息详情
     * @param accountId 账户id
     * @return DescribeRechargeAccountInfoResponse
     * @since 2.4.1
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C103)
    @ApiOperation(httpMethod = "GET", value = "获取充值账户信息详情")
    @GetMapping("/recharge/{accountId}")
    public DescribeRechargeAccountInfoResponse getRechargeAccountDetail(
            @PathVariable @ApiParam(name = "accountId", value = "账户 ID") Long accountId) {
        return bizBillingAccountService.getRechargeAccountDetail(accountId);
    }

    /**
     * 获取充值明细
     *
     * @param request 账户充值明细查询请求体
     *
     * @return {@code IPage<DescribeDepositResponse>}
     * @since 2.4.1
     */
    @AuthorizeBss(action = BQ.BQ010803)
    @ApiOperation(httpMethod = "GET", value = "获取充值明细")
    @GetMapping("/deposits")
    public IPage<DescribeDepositResponse> getDepositRecords(DescribeDepositRequest request) {
        //默认查询已支付的充值记录
        if (Objects.isNull(request.getPayStatus())) {
            request.setPayStatus(PayStatusEnum.PAID.getCode());
        }
        if (Objects.isNull(request.getAccountSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return BeanConvertUtil.convertPage(bizBillingAccountService.getDepositList(request),
                                           DescribeDepositResponse.class);
    }

    /**
     * 获取概览信息(用户控制台-总览)
     *
     * @return {@code DescribeOverviewResponse}
     * @since 2.4.1
     */
    @ApiOperation(httpMethod = "GET", value = "获取概览信息")
    @GetMapping("/overview")
    @AuthorizeBss(action = AuthModule.CA.CA)
    public DescribeOverviewResponse getAccountOverviews() {
        Map<String, Object> accountOverviews = bizBillingAccountService.getAccountOverviews(false);
        return BeanConvertUtil.convert(accountOverviews, DescribeOverviewResponse.class);
    }

    /**
     * 获取概览信息(用户控制台-优惠管理-去充值)
     *【Since v2.5.0】
     * @return {@code DescribeOverviewResponse}
     * @since 2.4.1
     */
    @ApiOperation(httpMethod = "GET", value = "获取概览信息")
    @AuthorizeBss(action = AuthModule.CC.CC0201)
    public DescribeOverviewResponse getAccountOverviewsDetop() {
        return BeanConvertUtil.convert(bizBillingAccountService.getAccountOverviews(true), DescribeOverviewResponse.class);
    }

    /**
     * 编辑备注
     *
     * @param remarkRequest 修改备注请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "编辑备注")
    @PutMapping("/remark")
    @AuthorizeBss(action = AuthModule.BQ.BQ01080202)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#remarkRequest.remark", resource = OperationResourceEnum.REMARK, bizId = "#remarkRequest.accountId", param = "#remarkRequest")
    public RestResult updateRemark(@Valid @RequestBody UpdateRemarkRequest remarkRequest) {
        //当前登录用户分销商，判断accountId的权限
        if (!bizDistributorService.checkAccountId(remarkRequest.getAccountId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        BizBillingAccount account = Objects
                .requireNonNull(bizBillingAccountService.getById(remarkRequest.getAccountId()),
                                "账户不存在");
        account.setRemark(remarkRequest.getRemark());
        return ResultUtil.ofOperate(bizBillingAccountService.updateById(account));
    }

    /**
     * 编辑解决方案
     *
     * @param solutionRequst 请求解决方案
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.BQ.BQ01080202)
    @ApiOperation(httpMethod = "PUT", value = "编辑解决方案")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'编辑客戶解决方案'", resource = OperationResourceEnum.EDIT_CUSTOMER, bizId = "#solutionRequst.accountId", tagNameUs ="'Edit Customer Solution'")
    @PutMapping("/solution")
    public RestResult updateSolution(@Valid @RequestBody UpdateSolutionRequest solutionRequst) {
        //当前登录用户分销商，判断accountId的权限
        if (!bizDistributorService.checkAccountId(solutionRequst.getAccountId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        BizBillingAccount account = Objects
                .requireNonNull(bizBillingAccountService.getById(solutionRequst.getAccountId()),
                        "账户不存在");

        Long orgSid = account.getOrgSid();
        QueryWrapper<Org> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("org_sid", orgSid);
        Org one = orgService.getOne(queryWrapper);
        one.setSolution(solutionRequst.getSolution());
        return ResultUtil.ofOperate(orgService.updateSolutionById(one));
    }

    /**
     * 更新信用额度
     *
     * @param request 更新信用额度请求体
     *
     * @return {@code RestResult}
     *
     * @since 2.4.2
     */
    @AuthorizeBss(action = AuthModule.BQ.BQ01080204)
    @PutMapping("/credit_line")
    @ApiOperation("更新信用额度")
    @DataPermission(resource = OperationResourceEnum.CREDIT, bizId = "#request.accountId")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新信用额度'", tagNameUs ="'Renew credit limit'",
            resource = OperationResourceEnum.CREDIT, param = "#request",
            bizId = "#request.accountId")
    @ListenExpireBack
    public RestResult updateCreditLine(@Valid @RequestBody UpdateCreditLineRequest request) {
        if (StringUtils.isEmpty(request.getCreditLineDt())){
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_MSG_00032));
        }
        String creditLineDt = request.getCreditLineDt();
        log.info("creditLineDt before save is ----------------creditLineDt:[{}]",request.getCreditLineDt());
        if (!UNLIMITED.getCode().equals(creditLineDt)) {
            try {
                if (Long.parseLong(creditLineDt)<System.currentTimeMillis()) {
                    log.info("creditLineDt failure is ----------------creditLineDt:[{}]",request.getCreditLineDt());
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_MSG_00033));
                } else if (4102416000000L < Long.parseLong(creditLineDt)) {
                    // 2100-1-1 00:00:00
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_MSG_00034));
                }
            } catch (Exception e) {
                log.info("creditLineDt exception is ----------------creditLineDt:[{}]",request.getCreditLineDt());
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_MSG_00034));
            }
        }
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        Boolean aBoolean = bizBillingAccountService.updateCreditLine(request);
        log.info("creditLineDt after save is ----------------creditLineDt:[{}]",request.getCreditLineDt());
        bizBillingAccountService.sendUpdateCreditLineSchedule(request);
        return ResultUtil.ofOperate(aBoolean);
    }

    /**
     * 更新用户 OBS 免费额度
     */
    @AuthorizeBss(action = AuthModuleOss.BQ.BQ01080208)
    @PutMapping("/free_capacity")
    @ApiOperation("更新OBS免费额度")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新OBS免费额度'", resource = OperationResourceEnum.UPDATE_OBS_FREE_CAPACITY, tagNameUs ="'Update'",
            param = "#request", bizId = "#request.accountId")
    public RestResult updateFreeCapacity(@Valid @RequestBody UpdateFreeCapacityRequest request) {
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        return ResultUtil.ofOperate(bizBillingAccountService.updateFreeCapacity(request));
    }

    /**
     * 调整业务标识Tag 【Since v2.5.0】
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = AuthModuleOss.BQ.BQ01080206)
    @PutMapping("/business_tag")
    @ApiOperation("调整业务标识Tag")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'账户'", resource = OperationResourceEnum.BUSINESS_TAG, tagNameUs ="'Account'",
            param = "#request", bizId = "#request.accountId")
    public RestResult updateBusinessTag(@Valid @RequestBody UpdateBusinessTagRequest request) {
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        return ResultUtil.ofOperate(bizBillingAccountService.updateBusinessTag(request));
    }


    /**
     * 更新分销商
     *
     * @param request 更新分销商请求体
     *
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BQ.BQ0104)
    @PutMapping("/distributor")
    @ApiOperation("更新分销商")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'账户'", tagNameUs ="'Account'",
            resource = OperationResourceEnum.DISTRIBUTOR, bizId = "#request.accountId", param = "#request")
    public RestResult updateDistributor(@Valid @RequestBody UpdateDistributorRequest request) {
        List<Long>  accountSids = new ArrayList<>();
        List<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> currentRoleList =  roleMapper.findRolesByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
        List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
        String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
        if(ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(RequestContextUtil.getAuthUserInfo().getRemark())){
            if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
                QueryWrapper<BizBillingAccount> qwer = new QueryWrapper();
                qwer.eq("entity_id",RequestContextUtil.getEntityId());
                qwer.eq("salesmen_id",RequestContextUtil.getAuthUserInfo().getUserSid());
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(qwer);
                if(CollectionUtil.isNotEmpty(accounts)){
                    for(BizBillingAccount ac : accounts){
                        accountSids.add(ac.getId());
                    }
                }
                if(!accountSids.contains(request.getAccountId())){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }else if(DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxScope)){
                QueryWrapper<BizBillingAccount> qwer = new QueryWrapper();
                qwer.eq("entity_id",RequestContextUtil.getEntityId());
                qwer.eq("distributor_id",RequestContextUtil.getAuthUserInfo().getOrgSid());
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(qwer);
                if(CollectionUtil.isNotEmpty(accounts)){
                    for(BizBillingAccount ac : accounts){
                        accountSids.add(ac.getId());
                    }
                }
                if(!accountSids.contains(request.getAccountId())){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }

        return ResultUtil.ofOperate(bizBillingAccountService.updateAccount(request));
    }

    /**
     * 统计当前登录用户的已邀请客户数量
     *
     * @return {@code Long}
     */
    @ApiOperation(httpMethod = "GET", value = "统计当前登录用户的已邀请客户数量")
    @GetMapping("/count/customers")
    @AuthorizeBss(action = BQ.BQ07)
    public Long countCustomers() {
        return bizBillingAccountService.countCustomers();
    }

    /**
     * 异步导出客户信息列表
     *
     * @param request 客户列表查询请求体
     */
    @ApiOperation(httpMethod = "GET", value = "导出客户信息列表")
    @RejectCall
    @GetMapping("/async/export/feign")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'客户信息列表'", tagNameUs ="'Customer Information List'",
        resource = OperationResourceEnum.EXPORT_CUSTOMER_INFORMATION_LIST, param = "#request")
    @Idempotent
    public RestResult asyncExportCustomerList(DescribeBillingAccountRequest request) {
        return bizBillingAccountService.asyncExport(request);
    }

    /**
     * 导出客户信息列表
     * 弃用
     * @param request 账户列表查询请求体
     * @param response 响应
     */
//    @AuthorizeBss(action = BQ.BQ0103)
//    @ApiOperation(httpMethod = "GET", value = "导出客户信息列表")
//    @GetMapping("/export")
    @Deprecated
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'客户信息列表'", tagNameUs ="'Customer Information List'",
            resource = OperationResourceEnum.EXPORT_CUSTOMER_INFORMATION_LIST, param = "#request")
//    @Idempotent
    public void expertCustomerList(DescribeBillingAccountRequest request, HttpServletResponse response) {
        log.info("导出客户开始");
        long start = System.currentTimeMillis();
        List<BizBillingAccount> bizBillingAccounts = this.bizBillingAccountService.accountInfExport(request);
        List<UserExportDTO> resUser = new ArrayList<>();

        String customBusinessTag = PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_BUSINESS_TAG);
        Map<String, String> customMap = null;
        if (StringUtils.isNotEmpty(customBusinessTag)) {
            try {
                List<CustomBusinessTag> customBusinessTags = JSON.parseArray(customBusinessTag, CustomBusinessTag.class);
                customMap =
                        customBusinessTags.stream().collect(Collectors.toMap(CustomBusinessTag::getTagKey, CustomBusinessTag::getTagName));
            }catch (Exception e) {
                log.error("BillingAccountController-expertCustomerList-customBusinessTags-JSON转换异常：{}", e.getMessage());
            }
        }

        for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
            bizBillingAccount.setContactName(bizBillingAccount.getContactName());
            bizBillingAccount.setAdminName(bizBillingAccount.getAdminName());
            bizBillingAccount.setEmail(bizBillingAccount.getEmail());
            bizBillingAccount.setMobile(bizBillingAccount.getMobile());
            bizBillingAccount.setStatus(STATUS.get(bizBillingAccount.getUserStatus()));
            Criteria criteria = new Criteria();
            criteria.put("orgSid", bizBillingAccount.getOrgSid());
            List<User> users = userService.selectByParams(criteria).stream()
                                          .filter(item -> !item.getUserSid().equals(bizBillingAccount.getUserSid()))
                                          .collect(Collectors.toList());
            String businessTag = bizBillingAccount.getBusinessTag();
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>();
                String[] split = businessTag.split(";");
                for (String tag : split) {
                    if (tag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                        String[] splitTag = tag.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                               .replaceAll("\\[", "")
                                               .replaceAll("]", "")
                                               .split(",");
                        List<String> accountIdList = Arrays.asList(splitTag);
                        if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                            tagList.add(BusinessTagEnum.ARREARAGE.getName());
                        }
                    } else {
                        if (Objects.nonNull(BusinessTagEnum.getBusinessTagEnumByTag(tag))) {
                            tagList.add(BusinessTagEnum.getBusinessTagEnumByTag(tag).getName());
                        }else if (!CollectionUtils.isEmpty(customMap) && StringUtils.isNotEmpty(customMap.get(tag))) {
                            tagList.add(customMap.get(tag));
                        }
                    }
                }
                bizBillingAccount.setBusinessTag(StringUtils.join(tagList, ","));
            }
            users.stream().map(user -> {
                user.setBusinessTag(bizBillingAccount.getBusinessTag());
                return user;
            }).collect(Collectors.toList());
            List<UserExportDTO> exportUser = BeanConvertUtil.convert(users, UserExportDTO.class);
            exportUser.forEach(e -> {
                e.setAccountName(bizBillingAccount.getAccountName());
                e.setContactName(bizBillingAccount.getContactName());
                e.setAdminName(bizBillingAccount.getAdminName());
                e.setAccountName(bizBillingAccount.getAccountName());
                e.setAccount(e.getAccount());
            });
            resUser.addAll(exportUser);
        }

        String customInfoTemplate = PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_TEMPLATE);
        if (CommonPropertyKeyEnum.ONE.getCode().equals(PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_SWITCH))
                && StringUtils.isNotEmpty(customInfoTemplate) && !CollectionUtils.isEmpty(bizBillingAccounts)) {
            // 武汉定制化-客户信息导出
            bizBillingAccountService.exportAccountWithCustomInfo(bizBillingAccounts , response, resUser, customInfoTemplate);
            return;
        }

        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;
        //设置响应
        try (OutputStream out = response.getOutputStream()) {
            //导出到excel
            //安全随机数
            String format = DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                    + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
            String destFileName = "客户信息列表" + format + ".xlsx";
            String zipFileName = "客户信息列表" + format + ".zip";
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            response.setContentType("application/octet-stream;charset=UTF-8");
            Map<String, Object> data = Maps.newHashMap();
            // 导入客户列表
            List<ExportBizAccount> list = bizBillingAccounts.stream().map(e -> {
                ExportBizAccount bean = BeanUtil.copyProperties(e, ExportBizAccount.class);
                bean.setBalance(Objects.nonNull(e.getBalance()) ? String.valueOf(BigDecimalUtil.getTwoPointAmount(e.getBalance()).doubleValue()) : "0.0");
                bean.setBalanceCash(String.valueOf(e.getBalanceCash().doubleValue()));
                bean.setCreditLine(String.valueOf(e.getCreditLine().doubleValue()));
                bean.setOfflineInvoicedAmount(Objects.nonNull(e.getOfflineInvoicedAmount()) ? String.valueOf(e.getOfflineInvoicedAmount().doubleValue()) : "0.0");
                return bean;
            }).collect(Collectors.toList());

            outExcel = new ByteArrayOutputStream();
            ExcelExportService exportService = ExcelUtil.write().buildWriter(outExcel, "template/customer-export-template.xlsx");
            exportService.buildSheet("客户信息列表").fill(list);
            List<UserExportDTO> convert = BeanConvertUtil.convert(resUser, UserExportDTO.class);
            exportService.buildSheet("子用户信息列表").fill(convert).finish();

            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = WebUtil.randomPwd(8);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, password, true);
            out.write(ZipUtil.toOutputStream(inZip).toByteArray());

            //发送压缩密码至用户
            AuthUser user = RequestContextUtil.getAuthUserInfo();
            if (!ObjectUtils.isEmpty(user) && !ObjectUtils.isEmpty(user.getEmail())) {
                SendZipCompressPasswordRequest sendRequest = new SendZipCompressPasswordRequest();
                sendRequest.setUserSid(user.getUserSid());
                sendRequest.setFileName(zipFileName);
                sendRequest.setPassword(password);
                feignService.sendZipCompressPassword(sendRequest);
            }

            out.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
            IOUtils.closeQuietly(inZip);
        }
    }

    /**
     * [INNER API] 下载映射租户导入模版
     *
     * @param fileType 文件类型
     */
    @RejectCall
    @ApiOperation("下载导入模版")
    @GetMapping("/import/download_template")
    public void downloadPricingTemplate(HttpServletResponse response, @RequestParam("fileType") String fileType) {
        bizBillingAccountService.downloadTemplate(response, fileType);
    }

    /**
     * 根据登录用户查询对应账户
     *
     * @return {@link List}<{@link BizBillingAccount}>
     * @since 2.4.1
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C116)
    @ApiOperation("根据登录用户查询对应账户")
    @GetMapping("/billing-account")
    public List<BizBillingAccount> getBillingAccount() {
        cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
        }
        List<BizBillingAccount> billingAccounts = bizBillingAccountService.getByAdminSid(authUser.getUserSid());
        if (CollectionUtil.isNotEmpty(billingAccounts)) {
            return billingAccounts;
        }
        billingAccounts = bizBillingAccountService.getByOrgSid(authUser.getOrgSid());
        return billingAccounts;
    }


    /**
     * 运营管理员/分销管理员询对应账户
     *
     * @return {@link List}<{@link AccountsSimpleResponse}>
     * @since 2.5.0
     */
    @ApiOperation("运营管理员/分销管理员询对应账户")
    @GetMapping("/getAccounts")
    @AuthorizeBss(action = AuthModule.BC.BC03.BC030202)
    public List<AccountsSimpleResponse> getAccounts() {
        cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (Objects.nonNull(authUser.getParentSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        DescribeBillingAccountRequest describeBillingAccountRequest = new DescribeBillingAccountRequest();
        describeBillingAccountRequest.setEntityId(RequestContextUtil.getEntityId());
        describeBillingAccountRequest.setExcludeUnapproved(true);
        IPage<BizBillingAccount> billingAccountList = bizBillingAccountService.getBillingAccountList(describeBillingAccountRequest);
        return BeanConvertUtil.convert(billingAccountList.getRecords(), AccountsSimpleResponse.class);
    }

    /**
     * 获取采集所需信息
     * [INNER API] 话单采集获取信息
     *
     * @return {@link Map}
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "获取采集所需信息")
    @GetMapping("/get_collector_info")
    public RestResult getCollectorInfo() {

        return new RestResult(bizBillingAccountService.getCollectorInfo());
    }

    /**
     * 账户金额清理
     *
     * @param request 账户金额清理
     * @return {@code RestResult}
     * @Since 2.5.0
     */
    @ApiOperation(httpMethod = "POST", value = "账户金额清理")
    @PostMapping("/amount_clearance")
    @AuthorizeBss(action = BQ.BQ0112)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'账户金额清理'", tagNameUs ="'Account amount clearing'",
            resource = OperationResourceEnum.CLEARANCE, param = "#request",bizId = "#request.id")
    public RestResult amountClearance(@Valid @RequestBody AmountClearanceRequest request) {
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        BizBillingAccount billingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getId());
        if (Objects.isNull(billingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        if (!Objects.equals(RequestContextUtil.getEntityId(), billingAccount.getEntityId())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        User user = userService.selectByPrimaryKey(billingAccount.getAdminSid());
        if (!WebConstants.UserStatus.AVAILABILITY.equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        try {
            bizBillingAccountService.amountClearance(request);
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        } catch (BizException e) {
            e.printStackTrace();
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));

        }
    }

    /**
     * 帐户到期金额清除
     * [INNER API] 试用期账号到期清理金额
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "账户金额清理")
    @PostMapping("/amount_clearance/feign")
    @RejectCall
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'账户金额清理'", tagNameUs ="'Account amount clearing'",
            resource = OperationResourceEnum.CLEARANCE, param = "#request",bizId = "#request.id")
    public RestResult accountExpireClear(@Valid @RequestBody AccountExpireClearRequest request) {
        try {
            AmountClearanceRequest amountClearanceRequest = new AmountClearanceRequest();
            BeanCopierUtils.copyProperties(request,amountClearanceRequest);
            bizBillingAccountService.amountClearance(amountClearanceRequest);
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        } catch (BizException e) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));

        }
    }

}
