/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "产品参数详情")
public class ProductInfoVO extends InquiryBaseVO {

    /**
     * 具体配置
     */
    @ApiModelProperty(notes = "具体配置")
    private Object data;

    /**
     * 产品配置描述
     */
    @ApiModelProperty(notes = "产品配置描述")
    private ProductConfigDesc productConfigDesc;

    /**
     * 产品规格
     */
    @ApiModelProperty(notes = "产品规格")
    private String productCategory;

    /**
     * 集群类型id
     */
    @ApiModelProperty(notes = "集群类型ID")
    private String shareTypeId;


    @ApiModelProperty(notes = "申请类型:开发训练/部署上线")
    private String applyType;

    private Long projectId;

    private String clusterId;

    /**
     * ECS BMS 资源ID
     */
    private String nodeIdList;
    /**
     * ECS BMS 资源intanceId
     */
    private String nodeInstanceIdList;

    @JSONField(serialize = false)
    private Date nodeStartTime;
    @JSONField(serialize = false)
    private Date nodeEndTime;
    @ApiModelProperty(notes = "转化系数")
    private BigDecimal convertRatio;

    @ApiModelProperty(notes = "算力")
    private String computingPower;

    @ApiModelProperty(notes = "单位算力")
    private String unitComputingPower;

    private String freezingStrategy;

    @ApiModelProperty(notes = "资源池类型 MA专属必传")
    private String poolType;


    @ApiModelProperty(notes = "架构 MA专属必传")
    private String cpuArch;

    @ApiModelProperty(notes = "是否是客户定价")
    private Boolean customFlag;

    @ApiModelProperty(notes = "订单类型，模型集市为shop")
    private String orderType;

    @ApiModelProperty(notes = "资金监管状态，模型集市需填写")
    private String superviseStatus;

    /**
     * 模型集市的规格id
     */
    private Long priceJoinId;

    private BigDecimal oldPrice;
    private BigDecimal targetPrice;

    /**
     * 容量方式的大小（确定是扩缩容）
     */
    private Long size;

    private Long bmsOrderDetailId;

    /**
     * 块存储用途
     */
    private String storagePurpose;
}
