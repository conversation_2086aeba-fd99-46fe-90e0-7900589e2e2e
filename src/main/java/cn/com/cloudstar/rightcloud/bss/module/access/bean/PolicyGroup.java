/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 策略与组关联表 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-26
 */
@Data
@TableName("sys_m_policy_group")
public class PolicyGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户组ID
     */
    private Long groupSid;

    /**
     * 策略ID
     */
    private Long policySid;
    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    private Long orgSid;

}
