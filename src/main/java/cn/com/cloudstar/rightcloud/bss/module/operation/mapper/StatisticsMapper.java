/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.operation.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

import cn.com.cloudstar.rightcloud.bss.common.mybatis.annotation.DataFilter;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.WaitingCenterVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.BillVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ConsumeTrendVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ConsumeVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.CouponStatisticsVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectOverviewVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectStatisticsVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.RechargeVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ResourceVO;

/**
 * StatisticsMapper 运营统计
 *
 * <AUTHOR>
 * @date 2019/10/21.
 */
@Repository
public interface StatisticsMapper {

    /**
     * 运营统计-订单信息
     * @return
     */
    OrderVO selectOrderStatistics(Criteria criteria);

    /**
     * 运营统计-充值信息
     * @return
     */
    RechargeVO selectRechargeStatistics(Criteria criteria);

    /**
     * 运营统计-账单信息
     * @return
     */
    BillVO selectBillStatistics(Criteria criteria);

    /**
     * 运营统计-消费分布
     * @return
     */
    List<ConsumeVO> selectConsumeStatistics(Criteria criteria);

    /**
     * 运营统计-用户资源到期top10
     * @return
     */
    List<ResourceVO> selectResourceExpireTop(Criteria criteria);

    /**
     * 运营统计-消费趋势
     * @param criteria
     * @return
     */
    List<ConsumeTrendVO> selectConsumeTrend(Criteria criteria);

    /**
     * 资源即将到期个数
     * @param criteria
     * @return
     */
    int countExpireResource(Criteria criteria);

    /**
     * 资源待开通个数
     * @return
     */
    int countServiceOrder(Criteria criteria);

    /**
     * 工单待处理个数
     * @return
     */
    @DataFilter
    int countWorkOrder(Criteria criteria);

    /**
     * 查询优惠券抵扣明细
     * @param criteria 条件
     * @return
     */
    List<CouponStatisticsVO> selectCouponStatistic(Criteria criteria);

    /**
     * 统计优惠券数量
     * @param accountId
     * @return
     */
    WaitingCenterVO countCoupon(@Param("accountId") Long accountId);

    /**
     * 查询用户本月消费
     * @param criteria
     * @return
     */
    BigDecimal selectUserCurrentConsume(Criteria criteria);

    /**
     * 查询主机相关数量
     * @param criteria
     * @return
     */
    ProjectOverviewVO selectProjectVmCount(Criteria criteria);

    /**
     * 查询硬盘相关数量
     * @param criteria
     * @return
     */
    ProjectOverviewVO selectProjectVdCount(Criteria criteria);
    /**
     * 查询IP相关数量
     * @param criteria
     * @return
     */
    ProjectOverviewVO selectProjectFloatingIpCount(Criteria criteria);

    /**
     * 查询负载均衡相关数量
     * @param criteria
     * @return
     */
    ProjectOverviewVO selectProjectLbCount(Criteria criteria);

    /**
     * 获取数量报表统计
     * @param criteria
     * @return
     */
    List<ProjectStatisticsVO> selectProjectStatistics(Criteria criteria);

    /**
     * 获取资源相关类型消费报表统计
     * @param criteria
     * @return
     */
    List<ProjectStatisticsVO> selectResBillStatistics(Criteria criteria);
}
