package cn.com.cloudstar.rightcloud.bss.module.access.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.SysIamClient;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.common.common.RightCloudResult;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.module.support.access.constants.BasicSqlEnum;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.cmp.system.form.SysIamAuthFeignForm;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.cmp.system.result.SysIamAuthFeignResult;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 系统授权控制器
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@RestController
@CrossOrigin
@RequestMapping("/authorize")
@Slf4j
public class SystemAuthorizeController {

    @Value("${skipRoleAuth:false}")
    private Boolean skipRoleAuth;

    @Autowired
    private SysIamClient sysIamClient;


    /**
     * 用户接口权限认证
     *
     * @param userSid 用户sid
     * @return {@link RestResult }<{@link Boolean }>
     */
    @PostMapping("/role_auth/feign")
    @RejectCall
    RestResult<Boolean> roleAuth(@RequestParam(value = "userSid") Long userSid,
                                 @RequestParam(value = "requestURI") String requestURI,
                                 @RequestParam(value = "method") String method) {
        return new RestResult<>(systemAuthentication(userSid, requestURI, method));
    }

    /**
     * 是否是租户
     *
     * @param userSid 用户sid
     * @return {@link RestResult }<{@link Boolean }>
     */
    @PostMapping("/tenant_auth/feign")
    @RejectCall
    RestResult<Boolean> whetherItIsATenant(@RequestParam(value = "userSid") Long userSid) {
        return new RestResult<>(RequestContextUtil.whetherItIsATenant(userSid));
    }
    /**
     * 系统权限认证
     *
     * @param userSid    用户sid
     * @param requestURI 请求uri
     * @param method     方法
     * @return {@link Boolean }
     */
    public Boolean systemAuthentication(Long userSid, String requestURI, String method) {
        if (skipRoleAuth) {
            return true;
        }
        List<Role> roles = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_ROLE_BY_USER.getValue(), Role.class, userSid);
        if (!roles.isEmpty()) {
            List<String> collect = roles.stream().map(Role::getModuleCategory).collect(Collectors.toList());
            log.debug("当前用户:{},可访问模块:{}", userSid, collect);
            if (collect.contains("console")) {
                SysIamAuthFeignForm req = new SysIamAuthFeignForm();
                req.setUserId(userSid);
                req.setPath(requestURI);
                req.setMethod(method);
                log.debug("iamAuthForm....,{}", JSONUtil.toJsonStr(req));
                RightCloudResult<SysIamAuthFeignResult> respDto = sysIamClient.roleAuth(req);
                log.debug("respDto....,{}", JSONUtil.toJsonStr(respDto));
                return Objects.nonNull(respDto) && respDto.isSuccess() && respDto.getData().getIsPassAuth();

            }
        } else {
            log.info("未获取到用户角色:{}", userSid);
        }
        return false;
    }
}
