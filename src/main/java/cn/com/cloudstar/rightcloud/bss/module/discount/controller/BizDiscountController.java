/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.discount.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.Valid;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.enums.CloudEnvEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.OriginTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ScopeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WrapperUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.SysBssEntityMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.CreateDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.OperateDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.UpdateDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.response.DescribeCustomerDiscountResponse;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.response.DescribeDiscountDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.response.DescribeDiscountResponse;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BR.BR01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BR.BR02;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;

/**
 * 折扣管理
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@RestController
@RequestMapping("/discount")
@Api(tags = "折扣管理")
@Validated
@Slf4j
public class BizDiscountController {

    /**
     * 启用
     */
    private static final String ENABLE = "enabled";

    /**
     * 禁用
     */
    private static final String DISABLE = "disabled";

    /**
     * 删除
     */
    private static final String DELETE = "delete";
    /**
     * 客户折扣标识
     */
    private static final String CUSTOMER = "customer";
    @Autowired
    private IBizDiscountService bizDiscountService;

    @Autowired
    private BizDiscountMapper bizDiscountMapper;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private SysBssEntityMapper sysBssEntityMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private FeignService feignService;

    @Autowired()
    @Lazy
    private StringRedisTemplate redisTemplate;
    @Autowired
    @Lazy
    private ExportService exportService;
    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    /**
     * 获取平台折扣列表
     *
     * @param request 折扣列表查询请求体
     * @return {@code IPage<DescribeDiscountResponse>}
     */
    @AuthorizeBss(action = BR01.BR01 + "," + AuthModule.BQ.BQ01080801)
    @ApiOperation(httpMethod = "GET", value = "获取平台折扣列表")
    @GetMapping("/platform")
    @ListenExpireBack
    public IPage<DescribeDiscountResponse> findPlatformDiscounts(DescribeDiscountRequest request) {
        if (("all").equals(request.getProductScopeLike())) {
            request.setProductScopeLike(null);
        }
        IPage<BizDiscount> page = PageUtil.preparePageParams(request, "created_dt", "desc");
        QueryWrapper<BizDiscount> queryWrapper = WrapperUtil.wrapQuery(request);
        queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
        IPage<BizDiscount> result = bizDiscountService.page(page, queryWrapper);
        result.getRecords().forEach(this::convertToDesc);
        return BeanConvertUtil.convertPage(result, DescribeDiscountResponse.class);
    }

    /**
     * 导出折扣列表
     *【Since v2.5.0】
     * @param request 折扣列表查询请求体
     * @return {@code IPage<DescribeDiscountResponse>}
     */
    @AuthorizeBss(action = BR01.BR0106 + "," + BR02.BR0206)
    @ApiOperation(httpMethod = "GET", value = "获取平台折扣列表")
    @GetMapping("/platform/export")
    @ListenExpireBack
    public RestResult findPlatformDiscountsExport(@Valid DescribeDiscountRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }

        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.PLATFORM_DISCOUNT.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.CURRENT_STATUS_NOT_DOWN));
        }
        new ExportThreadUtil(exportService,
                request,
                ModuleTypeConstants.FROM_BSS,
                ExportTypeEnum.PLATFORM_DISCOUNT.getCode(),
                download.getDownloadId(),
                authUserInfo
        ).submit();

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.PLATFORM_DISCOUNT_DOWNING));
    }
    private BizDownload getBizDownload(BizDownload download, DescribeDiscountRequest request, AuthUser authUserInfo) {
        // 租户在下载任务中存入accountId
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        return download;
    }

    /**
     * 导出客户折扣列表
     *
     * @param request 折扣列表查询请求体
     * @return {@code IPage<DescribeCustomerDiscountResponse>}
     */
    @AuthorizeBss(action = BR02.BR02)
    @ApiOperation(httpMethod = "GET", value = "导出客户折扣列表")
    @GetMapping("/customer/export")
    @ListenExpireBack
    public RestResult findCustomerDiscountsExport(DescribeDiscountRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }

        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.CUSTOMER_DISCOUNT.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928072026));
        }
        new ExportThreadUtil(exportService,
                request,
                ModuleTypeConstants.FROM_BSS,
                ExportTypeEnum.CUSTOMER_DISCOUNT.getCode(),
                download.getDownloadId(),
                authUserInfo
        ).submit();

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928072016));
    }
    /**
     * 获取客户折扣列表
     *
     * @param request 折扣列表查询请求体
     * @return {@code IPage<DescribeCustomerDiscountResponse>}
     */
    @AuthorizeBss(action = BR02.BR02 + "," + AuthModule.BQ.BQ0108080201)
    @ApiOperation(httpMethod = "GET", value = "获取客户折扣列表")
    @GetMapping("/customer")
    @ListenExpireBack
    public IPage<DescribeCustomerDiscountResponse> findCustomerDiscounts(DescribeDiscountRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("orderByClause", "created_dt desc");
        if (request.getUserSid() != null) {
            orgService.checkDistributorRole(null, request.getUserSid());
            List<Long> acountList = new ArrayList<Long>();
            acountList.add(request.getUserSid());
            criteria.getCondition().put("accountIds", acountList);
        } else {
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        Page<BizBillingAccount> page = PageUtil.preparePageParams(request);
        IPage<BizBillingAccount> accountIPage = bizBillingAccountService.getAccountDiscountList(page, criteria);
        accountIPage.getRecords().forEach(account -> {
            account.getDiscounts().forEach(this::convertToDesc);
        });
        return BeanConvertUtil.convertPage(accountIPage, DescribeCustomerDiscountResponse.class);
    }

    /**
     * [INNER API] 创建折扣
     *
     * @param request 创建折扣请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "POST", value = "创建折扣")
    @PostMapping()
    @ListenExpireBack
    public RestResult<Long> createDiscount(@Valid @RequestBody CreateDiscountRequest request) {
        if (StringUtils.isNotBlank(request.getScopeValue())) {
            String[] scopeValue = request.getScopeValue().split("-");
            if (scopeValue.length == 2) {
                if (!NumberUtil.isInteger(scopeValue[0])
                        || (NumberUtil.isInteger(scopeValue[1]) && Integer.parseInt(scopeValue[0]) >= Integer.parseInt(scopeValue[1]))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if (!NumberUtil.isInteger(scopeValue[1])) {
                    request.setScopeValue(scopeValue[0] + "-");
                }
            }
        }
        if (ObjectUtils.isNotEmpty(request.getUserSid())) {
            BizBillingAccount account = bizBillingAccountService.getById(request.getUserSid());
            if (ObjectUtils.isEmpty(account)) {
                BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_4));
            }
            String status = Optional.ofNullable(userMapper.selectById(account.getAdminSid()))
                    .map(cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser::getStatus)
                    .orElseThrow(() -> new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_4)));
            if (WebConstants.UserStatus.REJECTED.equals(status)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_87253504));
            }
        }

        BizDiscount bizDiscount = BeanConvertUtil.convert(request, BizDiscount.class);
        //增加运营实体字段
        bizDiscount.setEntityId(RequestContextUtil.getEntityId());
        if (DateUtil.getDayDiff(request.getStartTime(), new Date()) < 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        Calendar start = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        start.setTime(bizDiscount.getStartTime());
        end.setTime(bizDiscount.getEndTime());
        if (start.get(Calendar.YEAR) >= 10000 || end.get(Calendar.YEAR) >= 10000) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1985154311));
        }
        if (bizDiscount.getStartTime().after(bizDiscount.getEndTime())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_799737574));
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        if(request.getStartTime().before(c.getTime())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_163544946));
        }

        //如果是客户折扣需要校验，平台折扣无需校验
        if(CUSTOMER.equals(request.getDiscountType())){
            // 校验客户是否能被创建折扣
            bizDiscountService.validateUser(bizDiscount);
        }
        // 校验适用环境和适用产品
        bizDiscountService.validateParams(bizDiscount);
        // 校验扣折扣名称唯一性
        QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
        if (bizDiscount==null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780030056));
        }
        queryWrapper.lambda().eq(BizDiscount::getDiscountName
                , bizDiscount.getDiscountName()).eq(BizDiscount::getUserSid, bizDiscount.getUserSid());
        List<BizDiscount> discounts = bizDiscountService.list(queryWrapper);
        if (discounts.size() > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_448380951));
        }
        // 默认禁用
        bizDiscount.setStatus(0);
        bizDiscount.setOriginType(OriginTypeEnum.DISCOUNT.getCode());
        bizDiscount.setEndTime(DateUtil.getEndOfDay(bizDiscount.getEndTime()));
        WebUserUtil.prepareInsertParams(bizDiscount);
        if (bizDiscount.getScopeValue().length() > 9) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361663018));
        }
        try {
            int flag = bizDiscountMapper.insert(bizDiscount);
            if (flag > 0) {
                return new RestResult(RestResult.Status.SUCCESS,
                        WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS),
                        bizDiscount.getDiscountSid());
            }
            return new RestResult(RestResult.Status.FAILURE,
                    WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
        } catch (Exception e) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_985538059));
        }
    }

    /**
     * 获取折扣详情
     *
     * @param id id
     * @return {@code DescribeDiscountResponse}
     */
    @AuthorizeBss(action = BR02.BR02 + "," + AuthModule.BQ.BQ0108080202)
    @ApiOperation(httpMethod = "GET", value = "获取折扣详情")
    @GetMapping("/{id}")
    @ListenExpireBack
    public DescribeDiscountDetailResponse findPDiscountDetail(@PathVariable("id")
                                                        @ApiParam(value = "折扣ID", type = "Long", required = true) Long id) {
        BizDiscount bizDiscount = bizDiscountService.getById(id);
        if (bizDiscount == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return BeanConvertUtil.convert(convertToList(bizDiscount), DescribeDiscountDetailResponse.class);
    }

    /**
     * [INNER API] 编辑折扣
     *
     * @param request 编辑折扣请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "PUT", value = "编辑折扣")
    @PutMapping()
    @ListenExpireBack
    public RestResult<Long> updateDiscount(@Valid @RequestBody UpdateDiscountRequest request) {
        if (StringUtils.isNotBlank(request.getScopeValue())) {
            String[] scopeValue = request.getScopeValue().split("-");
            if (scopeValue.length == 2 && !"+".equals(scopeValue[1])) {
                if (Integer.parseInt(scopeValue[0]) >= Integer.parseInt(scopeValue[1])) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(
                            WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
        }
        BizDiscount bizDiscount = bizDiscountService.getById(request.getDiscountSid());
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (bizDiscount == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Long userSid = request.getUserSid();
        if (userSid != null && bizDiscount.getUserSid() != null && userSid - bizDiscount.getUserSid() != 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if(request.getStartTime().after(request.getEndTime())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_30899181));
        }
        if(!StringUtils.equals(bizDiscount.getDiscountType(), request.getDiscountType())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_629792857));
        }

        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        if (endTime != null) {
            if (startTime == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_396328749));
            }

            if (endTime.before(startTime)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_770017632));
            }

        }
        long count = sysBssEntityMapper.selectEntityById(authUserInfo.getUserSid(), bizDiscount.getEntityId());
        if(count<=0){
            if (Objects.isNull(authUserInfo.getOrgSid())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
            }
        }
        if (bizDiscount.getStatus() != 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_921225621));
        }
        bizDiscount = BeanConvertUtil.convert(request, BizDiscount.class);
        Calendar start = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        start.setTime(bizDiscount.getStartTime());
        end.setTime(bizDiscount.getEndTime());
        if (start.get(Calendar.YEAR) >= 10000 || end.get(Calendar.YEAR) >= 10000) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1985154311));
        }
        // 校验适用环境和适用产品
        bizDiscountService.validateParams(bizDiscount);

        WebUserUtil.prepareUpdateParams(bizDiscount);
        boolean flag = bizDiscountService.updateById(bizDiscount);
        if (flag) {
            return new RestResult(RestResult.Status.SUCCESS,
                    WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS),
                    bizDiscount.getDiscountSid());
        }
        return new RestResult(RestResult.Status.FAILURE,
                WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
    }

    /**
     * [INNER API] 删除折扣
     *
     * @param id id
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "DELETE", value = "删除折扣")
    @DeleteMapping("/{id}")
    @ListenExpireBack
    public RestResult<Long> deleteDiscount(@PathVariable("id") Long id) {
        BizDiscount bizDiscount = bizDiscountService.getById(id);
        if (bizDiscount == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
        }
        if (!bizDiscount.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        if (bizDiscount.getStatus() != 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_921195612));
        }
        if (bizDiscountService.removeById(id)) {
            return new RestResult(RestResult.Status.SUCCESS,
                    WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS),
                    id);
        }
        return new RestResult(RestResult.Status.SUCCESS,
                WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE),
                id);
    }

    /**
     * [INNER API] 启用、禁用折扣
     *
     * @param request 操作折扣，禁用、启用，删除客户下所有折扣请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "PUT", value = "启用、禁用折扣")
    @PutMapping("/operate")
    @ListenExpireBack
    public RestResult updateBillingStrategyStatus(@RequestBody OperateDiscountRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizDiscount bizDiscount1 = bizDiscountMapper.selectById(request.getDiscountSid());
        if (!DELETE.equals(request.getType()) && Objects.isNull(bizDiscount1)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if(Objects.nonNull(authUserInfo.getEntityId()) && Objects.nonNull(bizDiscount1)){
            if(!authUserInfo.getEntityId().equals(bizDiscount1.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
            }
        }
        if (Objects.nonNull(request.getUserSid())) {
            // 删除客户除合同来源的折扣
            QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizDiscount::getUserSid, request.getUserSid())
                    .eq(BizDiscount::getOriginType, OriginTypeEnum.DISCOUNT.getCode());
            if (DELETE.equals(request.getType())) {
                List<BizDiscount> bizDiscounts = bizDiscountService.list(queryWrapper);
                Set<Integer> statusSet = bizDiscounts.stream().map(BizDiscount::getStatus).collect(Collectors.toSet());
                // 存在已启用
                if (statusSet.contains(1)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1885667064));
                }
                bizDiscountService.remove(queryWrapper);
            } else if (ENABLE.equals(request.getType()) || DISABLE.equals(request.getType())) {
                BizDiscount bizDiscount = new BizDiscount();
                bizDiscount.setStatus(request.getStatus());


                //查询出状态
                LambdaQueryWrapper<BizDiscount> qw = new LambdaQueryWrapper<>();
                qw.eq(BizDiscount::getUserSid, request.getUserSid())
                        .eq(BizDiscount::getOriginType, OriginTypeEnum.DISCOUNT.getCode());
                BizDiscount dbBean = bizDiscountService.getOne(qw);
                if(dbBean != null )
                {
                    //判断是否状态相同；
                    if(dbBean.getStatus().toString().equals(request.getStatus().toString()))
                    {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1253324408));
                    }
                }

                bizDiscountService.update(bizDiscount, queryWrapper);
            }
        } else {
            if (Objects.isNull(request.getDiscountSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if (DELETE.equals(request.getType())) {
                BizDiscount bizDiscount = bizDiscountService.getById(request.getDiscountSid());
                if (OriginTypeEnum.CONTRACT.getCode().equals(bizDiscount.getOriginType())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1418475853));
                }
                bizDiscountService.removeById(request.getDiscountSid());
            } else if (ENABLE.equals(request.getType()) || DISABLE.equals(request.getType())) {
                BizDiscount bizDiscount = new BizDiscount();
                bizDiscount.setStatus(request.getStatus());
                bizDiscount.setDiscountSid(request.getDiscountSid());


                //查询出状态
                BizDiscount dbBean = bizDiscountService.getById(request.getDiscountSid());
                if(dbBean != null ) {
                    //判断是否状态相同；
                    if(dbBean.getStatus().toString().equals(request.getStatus().toString())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1253324408));
                    }
                    if (DiscountPolicyEnum.PLATFORM.getCode().equals(dbBean.getDiscountType())&& ENABLE.equals(request.getType())) {
                        LambdaQueryWrapper<BizDiscount> qw = new LambdaQueryWrapper<>();
                        qw.eq(BizDiscount::getDiscountType, DiscountPolicyEnum.PLATFORM.getCode());
                        qw.eq(BizDiscount::getStatus, request.getStatus());
                        List<BizDiscount> list = bizDiscountService.list(qw);
                        String[] enableProducts = dbBean.getProductScope().split(",");
                        if (list.stream().anyMatch(e -> matchScopeAndTime(dbBean, enableProducts, e))) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1381461091));
                        }
                    }
                }

                bizDiscountService.updateById(bizDiscount);
            }
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    private static boolean matchScopeAndTime(BizDiscount dbBean, String[] enableProducts, BizDiscount e) {
        return StringUtils.containsAny(e.getProductScope(), enableProducts) && isBizDiscountOverlappingInTime(dbBean, e);
    }

    private static boolean isBizDiscountOverlappingInTime(BizDiscount dbBean, BizDiscount e) {
        return isBizDiscountOverlappingInTStartTime(dbBean, e)
            || isBizDiscountOverlappingInTEndTime(dbBean, e);
    }

    private static boolean isBizDiscountOverlappingInTEndTime(BizDiscount dbBean, BizDiscount e) {
        return e.getStartTime().after(dbBean.getStartTime()) && e.getStartTime().before(dbBean.getEndTime());
    }

    private static boolean isBizDiscountOverlappingInTStartTime(BizDiscount dbBean, BizDiscount e) {
        return e.getStartTime().compareTo(dbBean.getStartTime()) <= 0 && e.getEndTime().after(dbBean.getStartTime());
    }

    /**
     * 校验折扣名称唯一性
     *【Since v2.5.0】
     * @param name 名字
     * @param type 类型
     * @return {@code Boolean}
     */
    @ApiOperation(httpMethod = "GET", value = "校验折扣名称唯一性")
    @ListenExpireBack
    public Boolean checkDiscount(@ApiParam(value = "名称", type = "String") @RequestParam("name") String name,
                                 @ApiParam(value = "类型", type = "String") @RequestParam("type") String type) {

        QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizDiscount::getDiscountName, name).eq(BizDiscount::getDiscountType, type);
        List<BizDiscount> discounts = bizDiscountService.list(queryWrapper);
        return discounts.size() > 0;
    }
    /**
     * 转换描述
     * @param bizDiscount
     */
    private BizDiscount convertToDesc(BizDiscount bizDiscount) {
        bizDiscount.setScopeType(ScopeTypeEnum.codeFromName(bizDiscount.getScopeType()));
        bizDiscount.setProductScopes(ProductComponentEnum.transformDesc(StrUtil.splitToArray(bizDiscount.getProductScope(), StrUtil.COMMA)));
        bizDiscount.setCloudEnvScopes(CloudEnvEnum.transformDesc(StrUtil.splitToArray(bizDiscount.getCloudEnvScope(), StrUtil.COMMA)));
        bizDiscount.setOriginType(OriginTypeEnum.codeFromName(bizDiscount.getOriginType()));
        return bizDiscount;
    }

    /**
     * 转换描述
     * @param bizDiscount
     */
    private BizDiscount convertToList(BizDiscount bizDiscount) {
        bizDiscount.setProductScopes(Arrays.asList(StrUtil.splitToArray(bizDiscount.getProductScope(), StrUtil.COMMA)));
        bizDiscount.setCloudEnvScopes(Arrays.asList(StrUtil.splitToArray(bizDiscount.getCloudEnvScope(), StrUtil.COMMA)));
        return bizDiscount;
    }

}
