/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DistributorAuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDealDTO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeDealsRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <p>
 * 账号收支记录表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Service
public class BizAccountDealServiceImpl extends ServiceImpl<BizAccountDealMapper, BizAccountDeal> implements
        IBizAccountDealService {

    public static final String FROM_CONSOLE = "console";

    @Resource
    private OrgService orgService;

    private BigDecimal scaleFive(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(5, BigDecimal.ROUND_HALF_UP);
    }

    private BigDecimal scaleTwo(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    @Autowired
    private IBizBillingAccountService iBizBillingAccountService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    @Lazy
    private ExportService exportService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Override
    public IPage<BizAccountDeal> list(DescribeDealsRequest request) {
        /* 将请求信息转化为查询类*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        // 账户名称查询
        if (Objects.nonNull(request.getEntityName())) {
            List<String> entityNameLists = Arrays.asList(request.getEntityName().split(StrUtil.COMMA));
            criteria.put("entityName", entityNameLists);
        }
        if (Objects.nonNull(request.getSortdatafield())) {
            criteria.put("orderByClause", "d." + WebUserUtil.toClumn(request.getSortdatafield()) + " " + request.getSortorder());
        } else {
            criteria.put("orderByClause", "d.deal_time desc");
        }

        /*获取当前用户*/
        User authUser = AuthUtil.getAuthUser();
        if (authUser == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1924110710));
        }

        /*如果为管理员 则获取该用户当前组织下所有客户的组织(包括客户的项目)*/
        if (FROM_CONSOLE.equals(authUser.getRemark())) {
            criteria.put("orgSids", orgService.selectCustomerOrgSids(authUser.getOrgSid()));
        }

        /* 根据查询条件获取账单信息*/
        if (PageUtil.isPageQuery(request)) {
            Page<BizAccountDeal> page = PageUtil.preparePageParams(request);
            if ("04".equals(authUser.getUserType())) {
                Criteria c = new Criteria();
                List<String> accountNames = new ArrayList<>();
                c.put("distributorId", authUser.getOrgSid());
                List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByList(c.getCondition());
                for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
                    accountNames.add(bizBillingAccount.getAccountName());
                }
                if (Objects.isNull(request.getAccountNameLike())) {
                    criteria.put("accountNames", accountNames);
                }
            }
            IPage<BizAccountDeal> result = this.baseMapper.selectByParams(page, criteria.getCondition());
            result.getRecords().forEach(bizAccountDeal -> {
                // 获取所属分销商
                String distributorName = DistributorAuthUtil.seachDistributor(bizAccountDeal.getAccountSid());
                bizAccountDeal.setDistributorName(StringUtils.isEmpty(distributorName) ? "直营" : distributorName);

                bizAccountDeal.setAmount(scaleTwo(bizAccountDeal.getAmount()));
                bizAccountDeal.setBalanceCredit(scaleTwo(bizAccountDeal.getBalanceCredit()));
                bizAccountDeal.setBalance(scaleTwo(bizAccountDeal.getBalance()));
                bizAccountDeal.setCashAmount(scaleTwo(bizAccountDeal.getCashAmount()));
                bizAccountDeal.setBalanceCash(scaleTwo(bizAccountDeal.getBalanceCash()));
                // 暂时处理收支明细应该直接从biz_account_deal获取entityId、entityName
                if (Objects.isNull(bizAccountDeal.getEntityName()) && Objects.isNull(bizAccountDeal.getEntityId())) {
                    BizAccountDeal bizAccountDealTemp = this.baseMapper.selectById(bizAccountDeal.getDealSid());
                    bizAccountDeal.setEntityName(bizAccountDealTemp.getEntityName());
                    bizAccountDeal.setEntityId(bizAccountDealTemp.getEntityId());
                }
            });
            return result;
        }

        return null;
    }

    @Override
    public RestResult expertRegisterUserInfo(DescribeDealsRequest request, String moduleType) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.BIZ_ACCOUNT_DEAL.getCode());
        //添加下载任务数据
        if (authUserInfo == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1924110710));
        }
        // 校验参数accountIds是否可用
        if (Objects.nonNull(request.getAccountSid())){
            if (Objects.isNull(bizBillingAccountMapper.selectById(Long.valueOf(request.getAccountSid())))){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_139667282));
            }
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
                List<Long> orgSids = orgService.selectCustomerOrgSids(authUserInfo.getOrgSid());
                List<BizBillingAccount> accountList = bizBillingAccountMapper.getByOrgSids(orgSids);
                if (!accountList.stream()
                                .map(BizBillingAccount::getId).map(String::valueOf)
                                .collect(Collectors.toList())
                                .contains(request.getAccountSid())) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
        }

        getBizDownload(download, request, moduleType, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, "收支明细数据下载异常，请稍后重试!");
        }
        //采用线程池
        new ExportThreadUtil(exportService, request, moduleType, ExportTypeEnum.BIZ_ACCOUNT_DEAL.getCode(),
                download.getDownloadId(), AuthUtil.getAuthUser(), RequestContextUtil.getAuthUserInfo())
                .submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928072006));
    }

    private void getBizDownload(BizDownload download, DescribeDealsRequest request, String moduleType,
                                AuthUser authUserInfo) {
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setCreatedBy(authUserInfo.getAccount());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        bizDownloadMapper.insert(download);
    }

    @Override
    public IPage<BizAccountDealDTO> getList(DescribeDealsRequest request){
        /* 将请求信息转化为查询类*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        // 账户名称查询
        if (Objects.nonNull(request.getEntityName())) {
            criteria.put("entityName", request.getEntityName());
        }
        if (Objects.nonNull(request.getAccountLike())) {
            criteria.put("account", request.getAccountLike());
        }
        if (Objects.nonNull(request.getSortdatafield())) {
            criteria.put("orderByClause", "d." + WebUtil.toClumn(request.getSortdatafield()) + " " + request.getSortorder());
        } else {
            criteria.put("orderByClause", "d.deal_time desc");
        }
        if (StringUtils.isNotEmpty(request.getCustomizationInfo())) {
            try {
                JSONObject jsonObject = JSON.parseObject(request.getCustomizationInfo());
                List<String> customizationInfoKey = new ArrayList<>();
                List<String> customizationInfoValue = new ArrayList<>();
                jsonObject.forEach((key, value) -> {
                    customizationInfoKey.add(key);
                    customizationInfoValue.add(String.valueOf(value));
                });
                criteria.put("customizationInfoKey", customizationInfoKey);
                criteria.put("customizationInfoValue", customizationInfoValue);
            }catch (Exception e) {
                throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException("参数不合法");
            }
        }


        /*获取当前用户*/
        User authUser = AuthUtil.getAuthUser();
        if (authUser == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1924110710));
        }

        DistributorAuthUtil.put(criteria, null, AuthUtil.getAuthUser(), 3);
        /* 根据查询条件获取账单信息*/
        if (PageUtil.isPageQuery(request)) {
            Page<BizAccountDealDTO> page = PageUtil.preparePageParams(request);
            //账户名称查询
            if (Objects.nonNull(request.getAccountNameLike())) {
                criteria.put("accountName", request.getAccountNameLike());
            }

            //如果为管理员 则获取该用户当前组织下所有客户的组织(包括客户的项目)
            if (FROM_CONSOLE.equals(authUser.getRemark())) {
                criteria.put("consoleOrgSid", authUser.getOrgSid());
            }else{
                //分销商查询下属所有用户组织
                if (UserType.DISTRIBUTOR_USER.equals(authUser.getUserType()) && authUser.getOrgSid() != null) {
                    //查询当前组织及当前组织以下所有客户的数据
                    /*List<Long> orgSids = orgMapper.selectCustomerOrgSids(authUser.getOrgSid());
                    if (CollectionUtil.isEmpty(orgSids)) {
                        orgSids = new ArrayList<>();
                    }
                    criteria.put("orgSidIn", orgSids);*/
                    List<Long> orgSids = orgMapper.selectAllSidsByOrgSid(authUser.getOrgSid());
                    List<Long> orgIds = orgMapper.getByParentId(orgSids);
                    orgSids.addAll(orgIds);
                    if (Objects.nonNull(authUser.getOrgSid())) {
                        if(CollectionUtil.isEmpty(orgSids)){
                            orgSids = new ArrayList<>();
                            criteria.put("orgSidIn", orgSids);
                        }
                        criteria.put("orgSidIn", orgSids);
                    }

                }
            }
            IPage<BizAccountDealDTO> result = this.baseMapper.getList(page, criteria.getCondition());
            boolean isUs = WebUtil.getHeaderAcceptLanguage();
            result.getRecords().forEach(bizAccountDeal -> {
                // 获取所属分销商
                String distributorName = DistributorAuthUtil.seachDistributor(bizAccountDeal.getAccountSid());
                bizAccountDeal.setDistributorName(StringUtils.isEmpty(distributorName) ? "直营" : distributorName);
                BigDecimal money = scaleTwo(bizAccountDeal.getAmount());
                //当前抵扣现金券余额
                bizAccountDeal.setDeductBalanceCash(scaleTwo(bizAccountDeal.getDeductBalanceCash()));
                //当前金额
                String tradeChannel = bizAccountDeal.getTradeChannel();
                List<String> tradeChannels = Arrays.asList(RechargeTypeEnum.ACC_TCASH.getCode(), RechargeTypeEnum.PLATFORM.getCode(),
                                                  RechargeTypeEnum.WECHAT_PAY.getCode(),
                                                  RechargeTypeEnum.ALI_PAY.getCode());
                bizAccountDeal.setAmount(tradeChannels.contains(tradeChannel) ? money : BigDecimal.ZERO);
                //信用额度
                bizAccountDeal.setBalanceCredit(scaleTwo(bizAccountDeal.getBalanceCredit()));
                //现金余额
                bizAccountDeal.setBalance(scaleTwo(bizAccountDeal.getBalance()));
                //当前现金券金额
                bizAccountDeal.setCashAmount(scaleTwo(bizAccountDeal.getCashAmount()));
                if ("balanceCash".equals(tradeChannel)) {
                    bizAccountDeal.setCashAmount(money);
                }
                if ("deductBalanceCash".equals(tradeChannel)) {
                    bizAccountDeal.setDeductBalanceCash(money);
                }
                //现金券余额
                bizAccountDeal.setBalanceCash(scaleTwo(bizAccountDeal.getBalanceCash()));
                //信用额度消费金额
                bizAccountDeal.setBalanceCreditAmount("accCredit".equals(bizAccountDeal.getTradeChannel()) ? money : BigDecimal.ZERO);
                bizAccountDeal.setChargingType(bizAccountDeal.getChargingType());
                if("01".equals(bizAccountDeal.getChargingType())){
                    bizAccountDeal.setChargingTypeForExcel("正常计费");
                }else if("02".equals(bizAccountDeal.getChargingType())){
                    bizAccountDeal.setChargingTypeForExcel("销售计费");
                }else {
                    bizAccountDeal.setChargingTypeForExcel(null);
                }
                // 暂时处理收支明细应该直接从biz_account_deal获取entityId、entityName
                if (Objects.isNull(bizAccountDeal.getEntityName()) && Objects.isNull(bizAccountDeal.getEntityId())) {
                    BizAccountDeal bizAccountDealTemp = this.baseMapper.selectById(bizAccountDeal.getDealSid());
                    bizAccountDeal.setEntityName(bizAccountDealTemp.getEntityName());
                    bizAccountDeal.setEntityId(bizAccountDealTemp.getEntityId());
                }
                if (isUs) {
                    bizAccountDeal.setRemark(this.remarkToUs(bizAccountDeal.getRemark()));
                }
            });
            return result;
        }

        return null;
    }

    private String remarkToUs(String remark) {
        remark = remark.replaceAll("平台充值", "Platform recharge")
                .replaceAll("信用余额充值", "Credit balance recharge")
                .replaceAll("合同调整信用额度", "Contract adjustment of credit limit")
                .replaceAll("微信支付充值", "WeChat Pay Recharge")
                .replaceAll("支付宝充值", "Alipay recharge")
                .replaceAll("充值现金券充值", "Recharge with cash coupons")
                .replaceAll("调整信用额度", "Adjust credit limit")
                .replaceAll("修改欠费余额", "Modify outstanding balance")
                .replaceAll("余额欠费修改信用额度", "Balance arrears, modify credit limit")
                .replaceAll("AI开发平台专属资源池", "Ascend Modelarts Exclusive Resource Pool")
                .replaceAll("资源费用", "Resource costs")
                .replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                .replaceAll("套餐包费用", "Package fee")
                .replaceAll("对象存储", "Object Storage")
                .replaceAll("优惠券抵扣", "Coupon deduction")
                .replaceAll("元", "yuan")
                .replaceAll("云硬盘", "Cloud hard drive")
                .replaceAll("在线充值", "Online recharge");
        return remark;
    }

}
