/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response;

import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2019/10/18 10:14
 */
@ToString
@Setter
@Getter
public class DescribeInvoiceSettingResponse {
    /**
     * 配置ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "ID")
    private String id;

    /**
     * 账号id
     */
    @ApiModelProperty(notes = "账号")
    private String accountId;

    /**
     * 发票类型
     */
    @ApiModelProperty(notes = "发票类型")
    private String invoiceType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(notes = "发票类型")
    private String invoiceHead;

    /**
     * 接受人
     */
    @ApiModelProperty(notes = "收件人")
    @DesensitizationField(type = DesensitizedType.NAME)
    private String receiver;

    /**
     * 寄送地址
     */
    @ApiModelProperty(notes = "寄送地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String address;

    /**
     * 邮编
     */
    @ApiModelProperty(notes = "邮编")
    private String postCode;

    /**
     * 联系电话
     */
    @ApiModelProperty(notes = "联系电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String phone;

    /**
     * 纳税识别号
     */
    @ApiModelProperty(notes = "纳税识别号")
    private String taxId;

    /**
     * 开户行
     */
    @ApiModelProperty(notes = "开户行")
    private String depositBank;

    /**
     * 银行账户
     */
    @ApiModelProperty(notes = "银行账户")
    private String bankAccount;

    /**
     * 注册场所地址
     */
    @ApiModelProperty(notes = "注册场所地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String registerAddress;

    /**
     * 公司注册电话
     */
    @ApiModelProperty(notes = "公司注册电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String registerPhone;

    /**
     * 可以开发票的金额
     */
    @ApiModelProperty(notes = "可开发票金额")
    private BigDecimal totalMoney;
    /**
     * 已消费金额
     */
    private BigDecimal consumptionAmount;

    /**
     * 充值总金额
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 手动录入的线下开票金额
     */
    private BigDecimal offlineInvoicedAmount;

    /**
     * 账单可开票金额
     */
    private BigDecimal billInvoicableAmount;

    /**
     * 充值可开票金额
     */
    private BigDecimal rechargeInvoicableAmount;
}
