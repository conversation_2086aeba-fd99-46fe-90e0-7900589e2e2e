/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;

/**
 * <AUTHOR>
 * Created on 2019/10/23
 */
public interface BizBillingTariffSpecChargeMapper extends BaseMapper<BizBillingTariffSpecCharge> {

    Integer getUnitMonthPriceByConfig(String spec);

    /**
     * 带宽（容量变配价格查询）
     * @param specType 类型查询
     * @return 价格
     */
    Integer getUnitMonthPriceBySpecType(String specType);
}
