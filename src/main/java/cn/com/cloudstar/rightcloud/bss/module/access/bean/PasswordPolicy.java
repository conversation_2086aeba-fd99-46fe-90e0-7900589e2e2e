/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class PasswordPolicy implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userSid;

    /**
     * 密码包含规则
     */
    private String rule;

    /**
     * 密码最小长度
     */
    private Integer minLength;

    /**
     * 不同字符数限制
     */
    private Integer charactorLimit;

    /**
     * 常用密码剔除
     */
    private String ruledOut;

    /**
     * 登陆失败锁定开启状态 0:关闭；1开启；
     */
    private Boolean loginfailureEnable;

    /**
     * 登陆失败最大次数
     */
    private Integer loginfailureCount;

    /**
     * 账号有效期
     */
    private Boolean accountValidity;

    /**
     * 有效天数
     */
    private Integer expireTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 版本号
     */
    private Long orgSid;


    /**
     * 密码有效期
     */
    private Long pwdExpireTime;

    /**
     * 密码有效期开启状态
     */
    private Boolean pwdExpireTimeValidity;

    /**
     * 密码最少使用天数
     */
    private Long pwdLeastUsedDay;

    /**
     * 密码不能与前N个历史密码重复
     */
    private Long pwdRepeatNum;
    private String secAuth;

}