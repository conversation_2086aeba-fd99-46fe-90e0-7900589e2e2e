/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response;

import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.DepositVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;
import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2019/10/21 13:49
 */
@Setter
@Getter
@ToString
public class DescribeInvoiceDetailResponse {


    /**
     * 发票sid
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "ID")
    private Long invoiceSid;
    /**
     * 发票流水号
     */
    @ApiModelProperty(notes = "发票流水号")
    @JsonIgnore
    private String invoiceNo;

    /**
     * 发票类型 0普通发票 1增值税专用发票 2企业增值税普通发票
     */
    @ApiModelProperty(notes = "发票类型 0普通发票 1增值税专用发票 2企业增值税普通发票")
    private String invoiceType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(notes = "发票抬头")
    private String invoiceHead;

    /**
     * 纳税识别号
     */
    @ApiModelProperty(notes = "纳税识别号")
    private String taxId;

    /**
     * 开户行
     */
    @ApiModelProperty(notes = "开户行")
    private String depositBank;

    /**
     * 银行账号
     */
    @ApiModelProperty(notes = "银行账号")
    private String bankAccount;

    /**
     * 注册场所地址
     */
    @ApiModelProperty(notes = "注册场所地址")
    private String registerAddress;

    /**
     * 公司注册电话
     */
    @ApiModelProperty(notes = "公司注册电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String registerPhone;

    /**
     * 寄送地址
     */
    @ApiModelProperty(notes = "寄送地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String address;

    /**
     * 邮政编码
     */
    @ApiModelProperty(notes = "邮政编码")
    private String postCode;
    /**
     * 发票状态
     */
    @ApiModelProperty(notes = "发票状态")
    private String invoiceStatus;

    /**
     * 接受人
     */
    @ApiModelProperty(notes = "收件人")
    @DesensitizationField(type = DesensitizedType.NAME)
    private String receiver;

    /**
     * 联系电话
     */
    @ApiModelProperty(notes = "联系电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String phone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(notes = "联系人邮箱")
    @DesensitizationField(type = DesensitizedType.EMAIL)
    private String email;

    /**
     * 充值金额
     */
    @ApiModelProperty(notes = "充值金额")
    private BigDecimal depositeAmount;

    /**
     * 充值记录
     */
    @ApiModelProperty(notes = "充值记录")
    @JsonIgnore
    private List<DepositVO> deposits;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 费用清单
     */
    private List<BillBillingCycleCostVoDetailResponse> costList;


    /**
     * 分销商ID
     */
    @ApiModelProperty(notes = "分销商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long distributorId;

    /**
     * 开票时间
     */
    @ApiModelProperty(notes = "开票时间")
    private Date invoiceDt;

    /**
     *
     * 开票方式:billing账单、recharge_amount充值金额
     */
    private String invoiceMethod;
}
