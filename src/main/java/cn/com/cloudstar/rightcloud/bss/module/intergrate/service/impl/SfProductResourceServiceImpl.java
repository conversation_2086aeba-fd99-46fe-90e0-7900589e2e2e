/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareStopJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareStopJobResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WrapperUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PolicyAssertion;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PolicyHpcResourceVO;
import cn.com.cloudstar.rightcloud.bss.module.access.constance.HpcConstance;
import cn.com.cloudstar.rightcloud.bss.module.access.extension.PolicyHpcResourceStrategy;
import cn.com.cloudstar.rightcloud.bss.module.access.extension.PolicyResourceStrategy;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PolicyAssertionMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.ServiceOrderRecordMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyAssertionService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingSpecMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.ExampleFreezeStrategyRecord;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.EditFreezingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.GetNodeRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.StopJobRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.UpdateResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.HPCResponseShareVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.HpcClusterNodeResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.NodeDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.NodeInfo;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.PriceVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ProductResourceHPCResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResBmsDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResEcsDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterDetailDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterInstancesResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ExampleFreezeStrategyRecordService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcFeignService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.FilterOrgFunService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.SysMNotifyConfigBssMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.enums.FreezingStrategyEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.HPCClusterTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.BssTypeEnum;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.ExpireStrategyEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMNotifyConfig;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ServiceConfigArrKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ScenarioEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BillingPricesDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.DataDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.HpcDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ProductConfigDescDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ResHpcClusterVO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ServiceConfig;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SfsDTO;
import cn.com.cloudstar.rightcloud.oss.common.util.CloudClientFactory;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.ActiveDirectory;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ReleaseHPCSharePool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * <p>
 * 产品对应资源信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Service
@Slf4j
public class SfProductResourceServiceImpl extends
        ServiceImpl<SfProductResourceMapper, SfProductResource> implements ISfProductResourceService {

    public static final String CONSOLE = "console";
    public static final String BILLING_STRATEGY_TYPE = "BILLING_STRATEGY_TYPE";


    public static final String DATA = "data";
    public static final String HOUR = "Hour";
    /**
     * 试用期账号到期清理标识
     */
    private static final String EXPIRE_TYPE = "accountExpire";

    private static final List<String> STATUS_NOT_IN_LIST = Arrays.asList("unsubscribed", "deleted", "unsubscribe_error", "unsubscribing_retry");
    private static final String ECS = "ECS";
    private static final String ECS_X86 = "ECS-X86";

    private static final String SUCCESS = "success";

    private static final String ROOT_URL = "ROOT_URL";

    @Autowired
    private FilterOrgFunService filterOrgFunService;
    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private ServiceCategoryService categoryService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private HpcClusterService hpcClusterService;
    @Autowired
    private CodeMapper codeMapper;
    @Autowired
    private BizBillingSpecMapper bizBillingSpecMapper;
    @Autowired
    private IPolicyAssertionService policyAssertionService;
    @Autowired
    private BizContractMapper bizContractMapper;
    @Autowired
    private ServiceOrderRecordMapper serviceOrderRecordMapper;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private PolicyAssertionMapper policyAssertionMapper;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    @Autowired
    private IServiceOrderService serviceOrderService;
    @Autowired
    private OrgMapper orgMapper;
    @DubboReference
    private ShareRemoteService shareRemoteService;
    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private BizInquiryPriceService inquiryPriceService;
    @Autowired
    private Tracer tracer;
    @Autowired
    private ExampleFreezeStrategyRecordService exampleFreezeStrategyRecordService;
    @Autowired
    private SysMNotifyConfigBssMapper sysMNotifyConfigBssMapper;
    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private HpcFeignService hpcFeignService;

    @Autowired
    private IServiceOrderPriceDetailService iServiceOrderPriceDetailService;

    @Override
    public IPage<DescribeProductResourceResponse> listResources(
            DescribeProductResourceRequest request) {
        String status = request.getStatus();
        Long projectId = request.getProjectId();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)) {
            request.setStatus(SfProductEnum.NORMAL.getStatus());
        }
        Long parentOrgSid = request.getParentOrgSid();
        List<Long> orgSids = Lists.newArrayList();
        if (Objects.nonNull(parentOrgSid)) {
            request.setParentOrgSid(null);
            orgSids.add(parentOrgSid);
            List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(parentOrgSid);
            if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                orgSids.addAll(childrenOrgIds);
            }
        }
        boolean mgtConsole = !Objects.isNull(request.getMgtConsole()) && request.getMgtConsole();
        String accountNameLike = request.getAccountNameLike();
        String chargeType = request.getChargeType();
        String orderSn = request.getOrderSn();
        String distributorName = request.getDistributorName();
        String productNameLike = request.getProductNameLike();
        String productType = request.getProductType();
        Long accountId = request.getAccountId();
        request.setProjectId(null);
        request.setMgtConsole(null);
        request.setAccountNameLike(null);
        request.setOrderSn(null);
        request.setChargeType(null);
        request.setDistributorName(null);
        request.setProductNameLike(null);
        request.setProductType(null);
        request.setAccountId(null);
        boolean mark=false;
        if (SfProductEnum.SOONEXPIRE.getStatus().equals(status)){
            mark=true;
            status=SfProductEnum.NORMAL.getStatus();
            request.setStatus(SfProductEnum.NORMAL.getStatus());
        }
        // 租户只能看自己的
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<Long> orgSidConsole = new ArrayList<>();
        if (CONSOLE.equals(authUser.getRemark())) {
            orgSidConsole = orgMapper.selectCustomerOrgSids(authUser.getOrgSid());
        }
        QueryWrapper<SfProductResource> queryWrapper = WrapperUtil.wrapQuery(request, "createDt");
        if (orgSidConsole.size() > 0) {
            queryWrapper.lambda().in(SfProductResource::getOrgSid, orgSidConsole);
        }
        if (!StringUtil.isNullOrEmpty(productType)) {
            if (ECS.equals(productType) || ECS_X86.equals(productType)) {
                queryWrapper.lambda().in(SfProductResource::getProductType, ECS, ECS_X86);
            } else {
                queryWrapper.lambda().eq(SfProductResource::getProductType, productType);
            }
        }
        if (!mgtConsole) {
            List<String> notInProductTypes = Lists.newArrayList(ProductCodeEnum.HPC.getProductCode());
            if (StrUtil.isEmpty(productType)) {
                notInProductTypes.add(ProductCodeEnum.MODEL_ARTS.getProductCode());
                notInProductTypes.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
            }
            queryWrapper.lambda().notIn(SfProductResource::getProductType, notInProductTypes);
        }

        DateTime now = DateUtil.date();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)) {
            queryWrapper.lambda().lt(SfProductResource::getEndTime, now);
        }
        int days = Integer.parseInt(
                cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty("upcoming_expired_days"));
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, +days);
        if (mgtConsole) {
            if (mark) {
                if (SfProductEnum.NORMAL.getStatus().equals(status)) {
                    queryWrapper.lambda().between(SfProductResource::getEndTime, now, cal.getTime());
                }
            } else {
                if (SfProductEnum.NORMAL.getStatus().equals(status)) {
                    queryWrapper.lambda().and(wrapper -> wrapper.ge(SfProductResource::getEndTime, cal.getTime())
                                                                .or(w -> w.isNull(SfProductResource::getEndTime)));
                }
            }
        } else {
            if (SfProductEnum.NORMAL.getStatus().equals(status)) {
                queryWrapper.lambda().and(wrapper -> wrapper.ge(SfProductResource::getEndTime, now)
                                                            .or(w -> w.isNull(SfProductResource::getEndTime)));
            }
        }
        if (Objects.nonNull(projectId)) {
            queryWrapper.lambda().eq(SfProductResource::getOrgSid, projectId);
            request.setProjectId(projectId);
        } else {
            if (CollectionUtil.isNotEmpty(orgSids)) {
                queryWrapper.lambda().in(SfProductResource::getOrgSid, orgSids);
            }
        }
        if (!StringUtil.isNullOrEmpty(accountNameLike) || Objects.nonNull(accountId)) {
            Set<Long> userSidList = new HashSet<>();
            if (!StringUtil.isNullOrEmpty(accountNameLike)) {
                //查询申请客户
                // Bug:29857 修改为客户管理，客户信息
                List<User> userList = userMapper.selectAllUserByAccountNameLike(accountNameLike);
                userSidList.addAll(userList.stream().map(User::getUserSid).collect(Collectors.toSet()));
            }
            if (Objects.nonNull(accountId)) {
                List<User> userList = userMapper.selectAllUserByAccountId(accountId);
                userSidList.addAll(userList.stream().map(User::getUserSid).collect(Collectors.toSet()));
            }

            if (CollectionUtil.isNotEmpty(userSidList)) {
                List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByOwnerId(new ArrayList<>(userSidList));
                if (CollectionUtil.isNotEmpty(resourceIdList)) {
                    queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
                } else {
                    queryWrapper.lambda().in(SfProductResource::getId, -1);
                }
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        // 实例名称模糊
        if (!StringUtil.isNullOrEmpty(productNameLike)) {
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListBypProductNameLike(
                    productNameLike);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        if (!StringUtil.isNullOrEmpty(orderSn)) {
            //查询订单编号
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByOrderSn(orderSn);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }

        //modelArts产品根据运营实体过滤数据
        if (UserType.PLATFORM_USER.equals(authUser.getUserType()) && Objects.isNull(authUser.getOrgSid())) {
            if (authUser.getEntityId() != null) {
                List<String> orderSnList = serviceOrderMapper.selectOrderSnByEntityId(authUser.getEntityId());

                if (CollectionUtil.isNotEmpty(orderSnList)) {
                    queryWrapper.lambda().in(SfProductResource::getServiceOrderId, orderSnList);
                } else {
                    queryWrapper.lambda().in(SfProductResource::getServiceOrderId, -1L);
                }
            }
        }

        if (!StringUtil.isNullOrEmpty(chargeType)) {
            //查询计费模式
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByChargeType(chargeType);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        //查询所属分销商
        if (!StringUtil.isNullOrEmpty(distributorName)) {
            //获取所有直营的org集合
            List<Long> directlyOrgList = Lists.newArrayList();
            List<BizBillingAccount> bizBillingAccountList = bizBillingAccountMapper.selectAllDirectlyAccount();
            bizBillingAccountList.forEach(e -> {
                directlyOrgList.add(e.getOrgSid());
                List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e.getOrgSid());
                if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                    directlyOrgList.addAll(childrenOrgIds);
                }
            });
            if ("直营".equals(distributorName) || "直".equals(distributorName) || "营".equals(distributorName)) {
                orgSids.addAll(directlyOrgList);
            } else {
                List<Long> finalOrgSids = orgSids;
                orgService.selectOrgIdListByOrgNameLike(distributorName).forEach(e -> {
                    finalOrgSids.add(e);
                    List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e);
                    if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                        finalOrgSids.addAll(childrenOrgIds);
                    }
                });
                //移除直营的Org集合
                orgSids.removeAll(directlyOrgList);
            }
            if (CollectionUtil.isNotEmpty(orgSids)) {
                if (Objects.nonNull(request.getOrgId())) {
                    orgSids = orgMapper.selectCustomerOrgSids(request.getOrgId());
                }
                queryWrapper.lambda().in(CollectionUtil.isNotEmpty(orgSids),SfProductResource::getOrgSid, orgSids);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }

        String createDt = request.getCreateDt();
        if (StringUtils.isNotEmpty(createDt)) {
            queryWrapper.lambda()
                        .and(wrapper -> wrapper.ge(SfProductResource::getCreatedDt,
                                                   DateUtil.parse(createDt, "yyyy-MM-dd HH:mm:ss"))
                        );
        }
        //AL基础服务不能包含HPC资源
        List<String> productTypeList = new ArrayList<String>() {
            {
                this.add(ProductCodeEnum.HPC.getProductCode());
                this.add(ProductCodeEnum.HPC_DRP.getProductCode());
                this.add(ProductCodeEnum.HPC_SAAS.getProductCode());
            }
        };
        if (CollectionUtil.isNotEmpty(productTypeList)) {
            queryWrapper.lambda().notIn(SfProductResource::getProductType, productTypeList);
        }
        Page<SfProductResource> pageParams = PageUtil
                .preparePageParams(request, "createdDt", "desc");


        IPage<SfProductResource> resourcePage = this.page(pageParams, queryWrapper);
        IPage<DescribeProductResourceResponse> responsePage = BeanConvertUtil
                .convertPage(resourcePage, DescribeProductResourceResponse.class);
        responsePage.getRecords().forEach(record -> setOrderInfo(record, now, false));
        int size = responsePage.getRecords().size();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)) {
            //由于按量计费的IA状态被修改为已退订，已到期条件查询的情况下应该过滤掉这部分数据
            responsePage.setRecords(responsePage.getRecords().stream()
                                                .filter(record -> SfProductEnum.EXPIRED.getStatus()
                                                                                       .equals(record.getStatus()))
                                                .collect(Collectors.toList()));
            //分页的参数total也应该改变
            responsePage.setTotal(responsePage.getTotal() - (size - responsePage.getRecords().size()));
        }

        return responsePage;
    }

    @Override
    public IPage<DescribeProductResourceResponse> listResourcesFeign(
            DescribeProductResourceRequest request) {
        String status = request.getStatus();
        Long projectId = request.getProjectId();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)) {
            request.setStatus(SfProductEnum.NORMAL.getStatus());
        }
        Long parentOrgSid = request.getParentOrgSid();
        List<Long> orgSids = new ArrayList<>();
        if (Objects.nonNull(parentOrgSid)) {
            request.setParentOrgSid(null);
            orgSids.add(parentOrgSid);
            List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(parentOrgSid);
            if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                orgSids.addAll(childrenOrgIds);
            }
        }
        Long orgId = request.getOrgId();
        if (orgId != null) {
            orgSids.add(orgId);
            List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(orgId);
            if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                orgSids.addAll(childrenOrgIds);
            }
            request.setOrgId(null);
        }
        List<Long> reqOrgIdList = request.getOrgIdList();
        if (CollectionUtil.isNotEmpty(reqOrgIdList)) {
            request.setOrgIdList(null);
            orgSids.addAll(reqOrgIdList);
        }
        boolean mgtConsole = !Objects.isNull(request.getMgtConsole()) && request.getMgtConsole();
        String accountNameLike = request.getAccountNameLike();
        String chargeType = request.getChargeType();
        String orderSn = request.getOrderSn();
        String distributorName = request.getDistributorName();
        String productNameLike = request.getProductNameLike();
        String productType = request.getProductType();
        request.setProjectId(null);
        request.setMgtConsole(null);
        request.setAccountNameLike(null);
        request.setOrderSn(null);
        request.setChargeType(null);
        request.setDistributorName(null);
        request.setProductNameLike(null);
        request.setProductType(null);
        boolean mark = false;
        if (SfProductEnum.SOONEXPIRE.getStatus().equals(status)) {
            mark = true;
            status = SfProductEnum.NORMAL.getStatus();
            request.setStatus(SfProductEnum.NORMAL.getStatus());
        }
        // 租户只能看自己的
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<Long> orgSidConsole = new ArrayList<>();
        if (CONSOLE.equals(authUser.getRemark())) {
            orgSidConsole = orgMapper.selectCustomerOrgSids(authUser.getOrgSid());
        }

        //冻结失败
        if("frozenError".equals(status) && mgtConsole){
            request.setDrpFrozenStatus(status);
            request.setStatus("frozen");
            status="frozenError";
        }

        QueryWrapper<SfProductResource> queryWrapper = WrapperUtil.wrapQuery(request, "createDt","orgIdList","orgId");
        if (orgSidConsole.size() > 0) {
            queryWrapper.lambda().in(SfProductResource::getOrgSid, orgSidConsole);
        }
        //冻结失败
        if("frozenError".equals(status) && mgtConsole){
            queryWrapper.lambda().eq(SfProductResource::getDrpFrozenStatus, "frozenError");
        }
        if("frozen".equals(status) && mgtConsole){
            queryWrapper.lambda().isNull(SfProductResource::getDrpFrozenStatus);
        }
        if (!StringUtil.isNullOrEmpty(productType)) {
            if (ECS.equals(productType) || ECS_X86.equals(productType)) {
                queryWrapper.lambda().in(SfProductResource::getProductType, ECS, ECS_X86);
            } else {
                queryWrapper.lambda().eq(SfProductResource::getProductType, productType);
            }
        }
        if (!mgtConsole) {
            List<String> notInProductTypes = Lists.newArrayList(ProductCodeEnum.HPC.getProductCode());
            if (StrUtil.isEmpty(productType)) {
                notInProductTypes.add(ProductCodeEnum.MODEL_ARTS.getProductCode());
                notInProductTypes.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
            }
            queryWrapper.lambda().notIn(SfProductResource::getProductType, notInProductTypes);
        }

        DateTime now = DateUtil.date();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)) {
            queryWrapper.lambda().lt(SfProductResource::getEndTime, now);
        }
        int days= Integer.parseInt(
                cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty("upcoming_expired_days"));
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, +days);
        if(mgtConsole){
            if (mark){
                if (SfProductEnum.NORMAL.getStatus().equals(status)) {
                    queryWrapper.lambda().between(SfProductResource::getEndTime, now,cal.getTime());
                }
            }else {
                if(SfProductEnum.NORMAL.getStatus().equals(status)){
                    queryWrapper.lambda().and(wrapper -> wrapper.ge(SfProductResource::getEndTime, cal.getTime())
                                                                .or(w -> w.isNull(SfProductResource::getEndTime)));
                }
            }
        }else {
            if (SfProductEnum.NORMAL.getStatus().equals(status)) {
                queryWrapper.lambda().and(wrapper -> wrapper.ge(SfProductResource::getEndTime, now)
                                                            .or(w -> w.isNull(SfProductResource::getEndTime)));
            }
        }
        if (Objects.nonNull(projectId)) {
            queryWrapper.lambda().eq(SfProductResource::getOrgSid, projectId);
            request.setProjectId(projectId);
        } else {
            if (CollectionUtil.isNotEmpty(orgSids)) {
                queryWrapper.lambda().in(SfProductResource::getOrgSid, orgSids);
            }
        }
        if (!StringUtil.isNullOrEmpty(accountNameLike)) {
            //查询申请客户
            // Bug:29857 修改为客户管理，客户信息
            List<User> userList = userMapper.selectAllUserByAccountNameLike(accountNameLike);
            List<Long> userSidList = userList.stream().map(User::getUserSid).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userSidList)) {
                List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByOwnerId(userSidList);
                if (CollectionUtil.isNotEmpty(resourceIdList)) {
                    queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
                } else {
                    queryWrapper.lambda().in(SfProductResource::getId, -1);
                }
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        // 实例名称模糊
        if (!StringUtil.isNullOrEmpty(productNameLike)) {
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListBypProductNameLike(productNameLike);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        if (!StringUtil.isNullOrEmpty(orderSn)) {
            //查询订单编号
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByOrderSn(orderSn);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }

        //modelArts产品根据运营实体过滤数据
        if (UserType.PLATFORM_USER.equals(authUser.getUserType()) && Objects.isNull(authUser.getOrgSid())) {
            if(authUser.getEntityId() != null){
                List<String> orderSnList = serviceOrderMapper.selectOrderSnByEntityId(authUser.getEntityId());

                if(CollectionUtil.isNotEmpty(orderSnList)){
                    queryWrapper.lambda().in(SfProductResource::getServiceOrderId,orderSnList);
                } else {
                    queryWrapper.lambda().in(SfProductResource::getServiceOrderId, -1L);
                }
            }
        }


        if (!StringUtil.isNullOrEmpty(chargeType)) {
            //查询计费模式
            List<Long> resourceIdList = serviceOrderMapper.selectProductResourceIdListByChargeType(chargeType);
            if (CollectionUtil.isNotEmpty(resourceIdList)) {
                queryWrapper.lambda().in(SfProductResource::getId, resourceIdList);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }
        //查询所属分销商
        if (!StringUtil.isNullOrEmpty(distributorName)) {
            //获取所有直营的org集合
            List<Long> directlyOrgList = Lists.newArrayList();
            List<BizBillingAccount> bizBillingAccountList = bizBillingAccountMapper.selectAllDirectlyAccount();
            bizBillingAccountList.forEach(e -> {
                directlyOrgList.add(e.getOrgSid());
                List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e.getOrgSid());
                if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                    directlyOrgList.addAll(childrenOrgIds);
                }
            });
            if ("直营".equals(distributorName) || "直".equals(distributorName) || "营".equals(distributorName)) {
                orgSids.addAll(directlyOrgList);
            } else {
                orgService.selectOrgIdListByOrgNameLike(distributorName).forEach(e -> {
                    orgSids.add(e);
                    List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e);
                    if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                        orgSids.addAll(childrenOrgIds);
                    }
                });
                //移除直营的Org集合
                orgSids.removeAll(directlyOrgList);
            }
            if (CollectionUtil.isNotEmpty(orgSids)) {
                queryWrapper.lambda().in(SfProductResource::getOrgSid, orgSids);
            } else {
                queryWrapper.lambda().in(SfProductResource::getId, -1);
            }
        }

        String createDt = request.getCreateDt();
        if (StringUtils.isNotEmpty(createDt)) {
            queryWrapper.lambda().and(wrapper -> wrapper.ge(SfProductResource::getCreatedDt, DateUtil.parse(createDt, "yyyy-MM-dd HH:mm:ss"))
            );
        }
        //AL基础服务不能包含HPC资源
        List<String> productTypeList = new ArrayList<String>() {
            {
//                this.add(ProductCodeEnum.HPC.getProductCode());
//                this.add(ProductCodeEnum.HPC_DRP.getProductCode());
//                this.add(ProductCodeEnum.HPC_SAAS.getProductCode());
                this.add(ProductCodeEnum.MODEL_ARTS.getProductCode());
                this.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
            }
        };
        if(CollectionUtil.isNotEmpty(productTypeList)){
            queryWrapper.lambda().in(SfProductResource::getProductType, productTypeList);
        }
        Page<SfProductResource> pageParams = PageUtil
                .preparePageParams(request, "createdDt", "desc");
        IPage<SfProductResource> resourcePage = this.page(pageParams, queryWrapper);
        IPage<DescribeProductResourceResponse> responsePage = BeanConvertUtil
                .convertPage(resourcePage, DescribeProductResourceResponse.class);
        responsePage.getRecords().forEach(record -> setOrderInfo(record, now, mgtConsole));
        int size = responsePage.getRecords().size();
        if (SfProductEnum.EXPIRED.getStatus().equals(status)){
            //由于按量计费的IA状态被修改为已退订，已到期条件查询的情况下应该过滤掉这部分数据
            responsePage.setRecords(responsePage.getRecords().stream()
                                                .filter(record -> SfProductEnum.EXPIRED.getStatus().equals(record.getStatus()))
                                                .collect(Collectors.toList()));
            //分页的参数total也应该改变
            responsePage.setTotal(responsePage.getTotal()-(size-responsePage.getRecords().size()));
        }

        return responsePage;
    }

    private void setOrderInfo(DescribeProductResourceResponse record, Date now, Boolean mgtConsole) {
        Date endTime = record.getEndTime();
        List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper.selectList(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getResourceId, record.getId())
                        .eq(ServiceOrderResourceRef::getType, record.getProductType()));
        if (ObjectUtils.isEmpty(serviceOrderResourceRefs)) {
            return;
        }
        ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper
                .selectById(serviceOrderResourceRefs.get(0).getOrderDetailId());
        if (ObjectUtils.isEmpty(serviceOrderDetail)) {
            return;
        }
        record.setChargeType(serviceOrderDetail.getChargeType());
       //如果是按量计费，状态不能是已过期，应该是已退订
        if (SfProductEnum.NORMAL.getStatus().equals(record.getStatus()) && Objects.nonNull(endTime)
                && endTime.before(now)) {
            //计费模式按量计费,不是已过期
            if(!ChargeTypeEnum.PostPaid.getType().equals(record.getChargeType())){
                record.setStatus(SfProductEnum.EXPIRED.getStatus());
            }else{
                record.setStatus(SfProductEnum.UNSUBSCRIBED.getStatus());
            }

        }

        //专属资源池冻结失败,状态为冻结失败,运营控制台
        if("frozenError".equalsIgnoreCase(record.getDrpFrozenStatus()) &&
                SfProductEnum.FROZEN.getStatus().equalsIgnoreCase(record.getStatus()) && mgtConsole){
            record.setStatus("frozenError");
        }

        String serviceConfig = serviceOrderDetail.getServiceConfig();
        String newServiceConfig = setServiceConfigJsonNode(serviceConfig,record);

        record.setConfigDesc(newServiceConfig);
        record.setQuantity(serviceOrderDetail.getQuantity());
        ServiceOrderVo serviceOrder = serviceOrderMapper.selectOrderDetailById(serviceOrderDetail.getOrderId());
        if (serviceOrder == null) {
            return;
        }
        User user = userMapper.selectByPrimaryKey(Long.valueOf(serviceOrder.getOwnerId()));
        Org org = orgService.getById(serviceOrder.getOrgSid());
        record.setOrgName(Objects.isNull(org) ? "" : org.getOrgName());
        record.setBehalfUserSid(serviceOrder.getBehalfUserSid());
        record.setContractId(serviceOrder.getContractId() == null ? null : serviceOrder.getContractId().toString());
        record.setContractName(serviceOrder.getContractTitle());
        record.setSettlementType(serviceOrder.getSettlementType());
        record.setName(serviceOrder.getName());
        record.setContractFile(serviceOrder.getContractFile());
        record.setContractFileName(serviceOrder.getContractFileName());
        record.setOriginalCost(serviceOrder.getOriginalCost());
        record.setFinalCost(serviceOrder.getFinalCost());
        if (ProductCodeEnum.HPC.getProductCode().equals(record.getProductType())) {
            record.setOrgName(null);
            record.setStartTime(serviceOrderDetail.getStartTime());
        }
        if (Objects.isNull(serviceOrder.getServiceId())) {
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(record.getProductType());
            record.setServiceId(serviceCategory.getId());
        } else {
            record.setServiceId(Long.valueOf(serviceOrder.getServiceId()));
        }
        //MA专属资源池
        if(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(record.getProductType())){
            //判断是开发训练开始线上部署 2--开发训练，1-线上部署
            /*if(Integer.valueOf(2).equals(record.getMaVersion())){
                record.setApplyType("depTrain");
            }else{
                record.setApplyType("depOnline");
            } */
            record.setApplyType(serviceOrderDetail.getApplyType());
        }
        if (Objects.nonNull(user)) {

            //根据产品查询对应的账户
            QueryWrapper<ServiceCategory> query = new QueryWrapper<>();
            query.eq("id",serviceOrderDetail.getServiceId());
            ServiceCategory category = categoryService.getOne(query);
            QueryWrapper<BizBillingAccount> accountQuery = new QueryWrapper<>();
            accountQuery.eq("admin_sid",user.getUserSid());
            accountQuery.eq("entity_id",category.getEntityId());
            BizBillingAccount billingAccount = bizBillingAccountMapper.selectOne(accountQuery);
            if (!Objects.isNull(billingAccount) && !StringUtil.isNullOrEmpty(billingAccount.getDistributorName())) {
                record.setDistributorName(billingAccount.getDistributorName());
            } else {
                record.setDistributorName("直营");
            }
            if (!Objects.isNull(billingAccount)) {
                record.setAccountName(billingAccount.getAccountName());
            }
        }
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(record.getProductType())
                && ChargeTypeEnum.PostPaid.getType().equals(record.getChargeType())) {
            record.setEndTime(null);
        }
        //退订资源走这个查询流程
        if (SfProductEnum.UNSUBSCRIBED.getStatus().equals(record.getStatus())){
            List<ServiceOrderDetail> serviceOrderDetailList = serviceOrderDetailMapper.selectList(
                    Wrappers.<ServiceOrderDetail>lambdaQuery()
                            .like(ServiceOrderDetail::getServiceConfig,serviceOrderDetail.getOrderId()));
            if (ObjectUtils.isEmpty(serviceOrderDetailList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            List<Long> orderIdList = serviceOrderDetailList.stream()
                    .map((ServiceOrderDetail orderDetail) -> orderDetail.getOrderId())
                    .collect(Collectors.toList());

            QueryWrapper<ServiceOrder> queryWrapper = new QueryWrapper<>();
            if (orderIdList.size()!=0) {
                queryWrapper.in("id", orderIdList);
            }
            queryWrapper.eq("type","release");
            queryWrapper.eq("status","completed");
            List<ServiceOrder> orderList = serviceOrderMapper.selectList(queryWrapper);
            if (ObjectUtils.isEmpty(orderList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            ServiceOrder order = CollectionUtil.getFirst(orderList);
            record.setOrderSn(order.getOrderSn());
            record.setApplyOrderSn(String.valueOf(order.getId()));
        }else {
            record.setOrderSn(serviceOrder.getOrderSn());
            record.setApplyOrderSn(String.valueOf(serviceOrderDetail.getOrderId()));
        }
    }

    /**
     * 设置时长
     * @param serviceConfig
     * @param record
     * @return
     */
    private String setServiceConfigJsonNode(String serviceConfig,
                                              DescribeProductResourceResponse record) {

        Date startTime = record.getStartTime();
        Date endTime = record.getEndTime();
        if(startTime==null || endTime ==null){
            return serviceConfig;
        }

        long month = DateUtil.betweenMonth(startTime, endTime, true);

        JsonNode serviceConfigJsonNode = JsonUtil.fromJson(serviceConfig);
        JsonNode productConfigDesc = serviceConfigJsonNode.findPath("productConfigDesc");
        JsonNode currentDescJsonNode = productConfigDesc.findPath("currentConfigDesc");
        if(currentDescJsonNode !=null){
            JsonNode desc = currentDescJsonNode.get("currentDesc");
            if(desc != null){
                coinfigMonth(desc, month);
            }else{
                String descArryStr = currentDescJsonNode.textValue();
                JsonNode jsonNode = JsonUtil.fromJson(descArryStr);
                if(Objects.nonNull(jsonNode)){
                    coinfigMonth(jsonNode, month);
                    ObjectNode productConfig = (ObjectNode) productConfigDesc;
                    productConfig.put("currentConfigDesc", JsonUtil.toJson(jsonNode));
                }

            }
        }
        return serviceConfigJsonNode.toString();
    }

    /**
     * 设置时长
     * @param jsonNode
     * @param month
     */
    private void coinfigMonth(JsonNode jsonNode, long month) {
        if (month <= 0) {
            return;
        }
        for (JsonNode node : jsonNode) {
            if(!node.isObject()) {
                continue;
            }
            ObjectNode objectNode = (ObjectNode)node;
            if (Objects.isNull(objectNode.get("label"))) {
                continue;
            }
            String label = objectNode.get("label").textValue();
            if ("时长".equals(label)) {
                objectNode.put("value", month + "个月");
            }
        }
    }

    @Override
    public DescribeProductResourceResponse getDetail(Long id) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        SfProductResource sfProductResource = this.getById(id);
        if (sfProductResource == null) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1493182351));
        }
        String moduleType = WebUtil.getRequest().getHeader("moduleType");
        if (StringUtils.isNotEmpty(moduleType) && "console".equals(moduleType)) {
            if(authUser.getOrgSid() != null && !authUser.getAccount().equals(sfProductResource.getCreatedBy())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

        if(authUser.getOrgSid() != null && !authUser.getAccount().equals(sfProductResource.getCreatedBy())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (sysUserService.checkOperationAdmin(authUser.getUserSid())) {
            List<EntityDTO> serviceTypes = serviceCategoryService.getByServiceType(sfProductResource.getProductType());
            if(CollectionUtil.isNotEmpty(serviceTypes) && !authUser.getEntityId().equals(serviceTypes.get(0).getEntityId())){
                throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
            }
        }
        DescribeProductResourceResponse response = BeanConvertUtil
                .convert(sfProductResource, DescribeProductResourceResponse.class);
        if (Objects.nonNull(response)) {
            setOrderInfo(response, DateUtil.date(), false);
        }
        return response;
    }

    @Override
    public ServiceOrderVo getProductOrderInfo(String productType, Long userId) {
        ServiceOrderVo serviceOrderVo = new ServiceOrderVo();
        // 用户判断
        User userInfo = userMapper.selectByPrimaryKey(userId);
        if (Objects.nonNull(userInfo) && Objects.nonNull(userInfo.getParentSid())) {
            userId = userInfo.getParentSid();
        }
        Criteria criteria = new Criteria();
        criteria.put("serviceTypeIn", productType);
        criteria.put("ownerId", String.valueOf(userId));
        criteria.setOrderByClause("so.created_dt desc");
        List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectServiceOrderAndDetail(criteria);

        if (CollectionUtil.isNotEmpty(serviceOrderVos)) {
            serviceOrderVo = serviceOrderVos.get(0);
        }

        return serviceOrderVo;
    }

    @Override
    public List<ServiceOrderVo> getProductOrderInfos(String productType, Long userId) {
        // 用户判断
        User userInfo = userMapper.selectByPrimaryKey(userId);
        if (Objects.nonNull(userInfo) && Objects.nonNull(userInfo.getParentSid())) {
            userId = userInfo.getParentSid();
        }
        Criteria criteria = new Criteria();
        criteria.put("serviceTypeIn", productType);
        criteria.put("ownerId", String.valueOf(userId));
        criteria.setOrderByClause("so.created_dt desc");
        List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectServiceOrderAndDetail(criteria);
        Map<String, List<ServiceOrderVo>> map = serviceOrderVos.stream().collect(Collectors.groupingBy(ServiceOrderVo::getServiceType));

        ArrayList<ServiceOrderVo> resultList = new ArrayList<>();
        for (String s : map.keySet()) {
            resultList.add(map.get(s).get(0));
        }
        return resultList;
    }

    @Override
    public List<DescribeProductResourceResponse> actionResources(List<String> productTypes, Long parentOrgSid) {
        List<DescribeProductResourceResponse> resourceInfos = org.apache.commons.compress.utils.Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(productTypes)) {
            productTypes.stream().forEach(productType -> {
                DescribeProductResourceRequest request = new DescribeProductResourceRequest();
                request.setProductType(productType);
                request.setParentOrgSid(parentOrgSid);
                request.setPagenum(Long.MAX_VALUE);
                request.setPagesize(Long.MAX_VALUE);
                IPage<DescribeProductResourceResponse> responseIPage = this.listResources(request);
                if (Objects.nonNull(responseIPage) && responseIPage.getSize() > 0) {
                    resourceInfos.addAll(responseIPage.getRecords());
                }
            });
        }
        return resourceInfos.stream().filter(
                resourceInfo -> !SfProductEnum.DELETED.getStatus().equals(resourceInfo.getStatus()) && !SfProductEnum.UNSUBSCRIBED.getStatus().equals(resourceInfo.getStatus()) ).collect(
                Collectors.toList());
    }

    @Override
    public List<SfProductResource> getUsingResource(Long orgSid) {
        Criteria criteria = new Criteria();
        criteria.put("org_sid", orgSid);
        criteria.put("noInStatusList", Arrays.asList(SfProductEnum.DELETED.getStatus(),SfProductEnum.UNSUBSCRIBED.getStatus()));
        return this.baseMapper.selectByParams(criteria);
    }

    @Override
    public BaseGridReturn queryHpcCluster(QueryResHpcClusterRequest request) {

        BaseGridReturn baseGridReturn = new BaseGridReturn();
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_SEARCH_STATUS);
        queryResHpcClusterRequest.setOrgSid(request.getOrgSid());
        List<Long> cluIdList = policyAssertionService.findResourceRelationByUser("hpc", request.getOwnerId(),
                                                                                 request.getOrgSid(),
                                                                                 (Function<ResHpcClusterVO, Long>) (a) -> a
                                                                                         .getId(), queryResHpcClusterRequest);
        if (CollectionUtils.isEmpty(cluIdList)) {
            return baseGridReturn;
        }
        request.setClusterIdList(cluIdList);
        request.setOwnerId(null);

        //设置排除条件，排除已退订、已删除
        List<String> statusNoInList = request.getStatusNoInList();
        statusNoInList.add("unsubscribed");
        statusNoInList.add("deleted");
        statusNoInList.add("unsubscribing");

        RestResult clusterResult = hpcClusterService.getResHpcCluster(request);
        if (clusterResult.getStatus()) {
            Object data = clusterResult.getData();
            baseGridReturn = BeanConvertUtil.convert(data, BaseGridReturn.class);
            List list = baseGridReturn.getDataList();
            List<ProductResourceHPCResponse> hpcResponses = BeanConvertUtil.convert(list, ProductResourceHPCResponse.class);

            List<Long> clusterIdList = hpcResponses.stream().map(ProductResourceHPCResponse::getClusterId).collect(Collectors.toList());

            if (!CollectionUtil.isEmpty(clusterIdList)) {
                Criteria criteria = new Criteria();
                criteria.put("clusterIdIn", clusterIdList);
                List<SfProductResource> sfProductResources = this.baseMapper.selectByParams(criteria);
                Map<Long, SfProductResource> rfResMap = sfProductResources.stream()
                        .collect(Collectors.toMap(SfProductResource::getClusterId, res -> res));

                hpcResponses.forEach(resp -> {
                    SfProductResource productResource = rfResMap.get(resp.getClusterId());
                    if (productResource != null) {
                        resp.setId(productResource.getId());
                        resp.setProductName(productResource.getProductName());
                        resp.setProductType(productResource.getProductType());
                        if (StringUtils.isEmpty(resp.getPoolName())) {
                            resp.setPoolName(resp.getClusterName());
                            resp.setName(resp.getClusterName());
                        } else {
                            resp.setName(resp.getPoolName());
                        }
                        //开始/结束时间由res_hpc_cluster获取值

                        if (productResource.getProductType().equals(ProductCodeEnum.HPC_DRP.getProductCode())) {
                            resp.setStorageType("1");
                        } else {
                            resp.setStorageType("2");
                        }
                        resp.setFrozenTime(productResource.getFrozenTime());
                        resp.setProductStatus(productResource.getStatus());

                        QueryWrapper<ServiceOrderResourceRef> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("resource_id", productResource.getId());
                        queryWrapper.likeLeft("type", "HPC");
                        List<ServiceOrderResourceRef> refs = serviceOrderResourceRefMapper.selectList(queryWrapper);
                        if (CollectionUtil.isEmpty(refs)) {
                            return;
                        }
                        ServiceOrderResourceRef resourceRef = CollectionUtil.getFirst(refs);
                        Long orderDetailId = resourceRef.getOrderDetailId();
                        ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectById(orderDetailId);
                        String serviceConfig = serviceOrderDetail.getServiceConfig();
                        if (StringUtil.isNotEmpty(serviceConfig)) {
                            ServiceConfig config = JSONObject.parseObject(serviceConfig, ServiceConfig.class);
                            DataDTO dataDTO = config.getData();
                            if (dataDTO == null) {
                                return;
                            }
                            HpcDTO hpc = dataDTO.getHpc();
                            String fileStorageType = hpc.getFileStorageType();
                            if ("1".equals(fileStorageType)) {
                                resp.setStorageType("3");
                            }
                            //老版本 都是专属文件系统
                            if (!"3".equals(hpc.getHpcVersion())) {
                                resp.setStorageType("1");
                            }
                        }

                        String handlerMsg = serviceOrderRecordMapper.getLastHandlerMsgByOrderId(serviceOrderDetail.getOrderId());
                        resp.setOrderHanlderMsg(handlerMsg);
                    }
                });
            }
            baseGridReturn.setDataList(hpcResponses);

        } else {
            log.info("SfProductResourceServiceImpl.queryHpcCluster call hpcClusterService.getResHpcCluster: {}", clusterResult);
        }
        return baseGridReturn;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHpcCluster(Long orgSid, Long clusterId) {
        log.info("deleteHpcCluster:orgSid[{}],clusterId[{}]", orgSid, clusterId);
        RestResult restResult = hpcClusterService.resHpcClusterDetail(orgSid, clusterId);
        if (restResult.getStatus()) {
            Object data = restResult.getData();
            if (data != null) {
                ResHpcClusterDetailDTO detailDTO = BeanConvertUtil.convert(data, ResHpcClusterDetailDTO.class);
                if (detailDTO != null && StringUtils.equalsIgnoreCase("rejected", detailDTO.getStatus())) {

                    //逻辑删除
                    RestResult result = hpcClusterService.deleteHpcCluster(clusterId);
                    if (!restResult.getStatus()) {
                        log.error("删除HPC集群失败：{}", result.getMessage());
                        throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
                    }
                    log.info("deleteHpcCluster:orgSid[{}],clusterId[{}] SUCCESS", orgSid, clusterId);
                    return;
                } else {
                    throw new BizException(WebUtil.getMessage(
                            MsgCd.HPC_CLUSTER_NO_EXIST));
                }
            }
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
    }

    @Override
    @Transactional
    public RestResult updateHpcCluster(UpdateResHpcClusterRequest updateResHpcClusterRequest) {

        String clusterName = updateResHpcClusterRequest.getClusterName();
        Long orgSid = updateResHpcClusterRequest.getOrgSid();
        Long clusterId = updateResHpcClusterRequest.getClusterId();
        if (StringUtils.isNotEmpty(clusterName)) {
            QueryWrapper<SfProductResource> tWrapper = new QueryWrapper<>();
            tWrapper.eq("org_sid", orgSid);
            tWrapper.eq("cluster_id", clusterId);
            List<SfProductResource> sfProductResources = this.baseMapper.selectList(tWrapper);
            if (CollectionUtil.isEmpty(sfProductResources)) {
                throw new BizException(WebUtil.getMessage(
                        MsgCd.HPC_CLUSTER_NO_EXIST));
            }
            SfProductResource first = CollectionUtil.getFirst(sfProductResources);
            first.setClusterName(clusterName);
            this.baseMapper.updateById(first);
        }
        return hpcClusterService.updateHpcCluster(updateResHpcClusterRequest);
    }

    @Override
    public boolean hpcExist(String productType, Long orgSid, Long serviceId) {
        boolean isHpcExist = false;
        QueryWrapper<SfProductResource> tWrapper = new QueryWrapper<>();
        Integer count = 0;
        tWrapper.eq("org_sid", orgSid);
        tWrapper.ne("status", SfProductEnum.DELETED.getStatus());
        User authUser = AuthUtil.getAuthUser();
        //是否有hpc cluster
        if (StringUtils.isEmpty(productType)) {
            QueryResHpcClusterRequest request=new QueryResHpcClusterRequest();
            request.setPagenum("0");
            request.setPagesize("10");
            request.setOrgSid(authUser.getOrgSid());
            request.setOwnerId(authUser.getUserSid());
            List<Long> cluIdList = policyAssertionService.findResourceRelationByUser("hpc", request.getOwnerId(),
                                                                                     request.getOrgSid(),
                                                                                     (Function<ResHpcClusterVO, Long>) (a) -> a
                                                                                             .getId());
            request.setClusterIdList(cluIdList);
            request.setOwnerId(null);
            RestResult resHpcCluster = hpcClusterService.getResHpcCluster(request);
            if (resHpcCluster.getStatus()) {
                Object data = resHpcCluster.getData();
                List list = BeanConvertUtil.convert(data, BaseGridReturn.class).getDataList();
                if (CollectionUtil.isNotEmpty(list)){
                    isHpcExist = true;
                }
            }
            return isHpcExist;
        } else {//是否开通了hpc
            if (StringUtils.equalsIgnoreCase(productType, ProductCodeEnum.HPC.getProductCode())
                    || StringUtils.equalsIgnoreCase(productType, ProductCodeEnum.HPC_SAAS.getProductCode())) {
                QueryResHpcClusterRequest request = new QueryResHpcClusterRequest();
                //当前销售组织
                request.setOrgSid(authUser.getOrgSid());
                //当前账户id
                request.setOwnerId(authUser.getUserSid());
                //集群类型
                request.setClusterType("SAASShare");
                //状态
                ArrayList<String> statusNoInList = new ArrayList<>();
                statusNoInList.add("rejected");
                statusNoInList.add("unsubscribed");
                statusNoInList.add("deleted");
                statusNoInList.add("unsubscribe_error");
                statusNoInList.add("unsubscribing");
                statusNoInList.add(ResHpcClusterStatus.UNSUBSCRIBING_RETRY);
                request.setStatusNoInList(statusNoInList);

                // 运营实体对应HPC版本取得
                if (Objects.nonNull(serviceId)) {
                    List<SfServiceCategoryHpcClusterPool> hpcClusterPools = serviceCategoryMapper.getClusterPoolByServiceId(
                            serviceId);
                    if (Objects.nonNull(hpcClusterPools)) {
                        for(SfServiceCategoryHpcClusterPool hpcClusterPool: hpcClusterPools){
                            request.setPoolUuid(hpcClusterPool.getClusterId());
                            request.setHpcVersion(hpcClusterPool.getHpcVersion());
                //查询hpc资源
                RestResult restResult = hpcClusterService.getResHpcCluster(request);
                log.info("restResult...,{}",restResult.getStatus());
                if (restResult.getStatus()) {
                    Object data = restResult.getData();
                    List list = BeanConvertUtil.convert(data, BaseGridReturn.class).getDataList();

                                List<ProductResourceHPCResponse> hpcResponses = BeanConvertUtil.convert(list, ProductResourceHPCResponse.class);
                                if (CollectionUtil.isNotEmpty(hpcResponses)){
                                    log.info("用户未退订的hpc共享资源池数量：[{}]",hpcResponses.size());
                                    count = hpcResponses.size();
                    }
                }else{
                    tWrapper.eq("product_type", productType);
                    count = this.baseMapper.selectCount(tWrapper);
                }
        if (count > 0) {
                                isHpcExist = true;
                                break;
                            }

        }
                    }
                }
            }
        }

        return isHpcExist;
    }

    @Override
    public RestResult listResourcesByEnvId(Long envId) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId",envId);
        criteria.put("status","normal");
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        Map<String,Object>map=new HashMap<>();
        if (sfProductResources.size()>0){
            map.put("canBeDelete",false);
            return new RestResult(Status.FAILURE,WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1994946517),map);
        }
        map.put("canBeDelete",true);
        return new RestResult(map);
    }

    @Override
    public RestResult checkHpcClusterName(String clusterName) {
        return hpcClusterService.checkHpcClusterName(clusterName);
    }

    @Override
    public RestResult getHpcClusterName(String account) {
        return new RestResult();
    }

    @Override
    public BaseGridReturn queryHpcClusterInstances(DescribeProductResourceRequest request, Integer days, Boolean flag) {
        String status = request.getStatus();
        String chargeType = request.getChargeType();
        String accountNameLike = request.getAccountNameLike();
        String orderSn = request.getOrderSn();
        String distributorName = request.getDistributorName();
        String productNameLike = request.getProductNameLike();

        //在表sf_service_category 中通过entity_id查询 到id ,在通过id到sf_service_category_hpc_cluster_pool表中查询获取uuid
        //再通过uuid 去res_hpc_cluster 中查询实列
        //modelArts产品根据运营实体过滤数据
//        List<String> resourceIds = new ArrayList<>();
        List<String> clusterTypes = new ArrayList<>();
        List<Long> resourceIdList = new ArrayList<>();
        String productType = null;
        if (flag) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(
                    WebUtil.getRequest());
            if (authUserInfo != null && authUserInfo.getEntityId() != null) {
                List<String> serviceTypes = serviceCategoryMapper.getProductVersionByEntityId(
                        authUserInfo.getEntityId());
                if (CollectionUtil.isNotEmpty(serviceTypes)) {
                    if (serviceTypes.contains(ProductCodeEnum.HPC_DRP.getProductCode())) {
                        clusterTypes.addAll(HPCClusterTypeEnum.getHPCPrivateTypes());
                    }
                    if (serviceTypes.contains(ProductCodeEnum.HPC_SAAS.getProductCode())
                            || serviceTypes.contains(ProductCodeEnum.HPC.getProductCode())) {
                        clusterTypes.add(HPCClusterTypeEnum.SAAS_SHARE.getCode());
                    }
                }
                //根据运营实体查询出对应的资源id
                if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUserInfo.getRemark())) {
                    resourceIdList = addResourceIdByEntityId(authUserInfo);
                }
            }
            if (StringUtils.equalsIgnoreCase(ProductCodeEnum.HPC.getProductCode(), request.getProductType())
                    || StringUtils.equalsIgnoreCase(ProductCodeEnum.HPC_SAAS.getProductCode(),
                                                    request.getProductType())) {
                productType = HPCClusterTypeEnum.SAAS_SHARE.getCode();
            } else if (StringUtils.equalsIgnoreCase(ProductCodeEnum.HPC_DRP.getProductCode(),
                                                    request.getProductType())) {
                // productType = "SAASPrivate";
                clusterTypes.removeIf(info -> HPCClusterTypeEnum.SAAS_SHARE.getCode().equals(info));

                clusterTypes.addAll(HPCClusterTypeEnum.getHPCPrivateTypes());
        } else {
            productType = request.getProductType();
        }
        } else {
            clusterTypes.addAll(HPCClusterTypeEnum.getHPCPrivateTypes());
        }

        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        if (flag) {
            queryResHpcClusterRequest.setUpcomingExpiredDays(days);
        }
        queryResHpcClusterRequest.setFixedSorting(Boolean.FALSE);
        Long pagenum = request.getPagenum();
        Long pagesize = request.getPagesize();
        queryResHpcClusterRequest.setPagenum(pagenum != null ? String.valueOf(pagenum-1): null);
        queryResHpcClusterRequest.setPagesize(pagesize != null ? pagesize.toString() : null);
        Long orgSid = request.getParentOrgSid();
        //客户管理
        if (!StringUtil.isNullOrEmpty(orgSid)) {
            List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(orgSid);
            childrenOrgIds.add(orgSid);
            queryResHpcClusterRequest.setOrgSidList(childrenOrgIds);
        }

        if (CollectionUtil.isNotEmpty(resourceIdList)) {
            queryResHpcClusterRequest.setResourceIds(resourceIdList);
        }
        if (CollectionUtil.isNotEmpty(clusterTypes)) {
            queryResHpcClusterRequest.setClusterTypes(clusterTypes);
        }
        if (StringUtils.isNotEmpty(chargeType)) {
            queryResHpcClusterRequest.setChargeType(chargeType);
        }
        if (StringUtils.isNotEmpty(status)) {
            queryResHpcClusterRequest.setStatus(status);
        }
        if(StringUtils.isNotEmpty(productNameLike)){
            queryResHpcClusterRequest.setClusterName(productNameLike);
        }
        if (StringUtils.isNotEmpty(productType)) {
            queryResHpcClusterRequest.setClusterType(productType);
        }

        BaseGridReturn baseGridReturn = new BaseGridReturn();
        RestResult clusterResult = null;
        if (!StringUtil.isNullOrEmpty(accountNameLike)) {
            //查询申请客户
            // Bug:29857 修改为客户管理，客户信息
            List<User> userList = userMapper.selectAllUserByAccountNameLike(accountNameLike);
            List<Long> userSidList = userList.stream().map(User::getUserSid).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(userList)) {
                queryResHpcClusterRequest.setOwnerIdList(userSidList);
                clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
            }

        } else if (!StringUtil.isNullOrEmpty(orderSn)) {
            //查询订单编号
            QueryWrapper<ServiceOrder> tWrapper = new QueryWrapper<>();
            tWrapper.like("order_sn", orderSn);
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(tWrapper);
            List<Long> orderIds = serviceOrders.stream().map(ServiceOrder::getId).collect(Collectors.toList());
            if(orderIds.size()>0){
                QueryWrapper<SfProductResource> sfPWrapper = new QueryWrapper<>();
                List<Long> clusterIdList = sfProductResourceMapper.selectList(sfPWrapper.in("service_order_id", orderIds))
                        .stream().map(SfProductResource::getClusterId).collect(Collectors.toList());

                //HPC共享资源池退订后，无法在productResource表中查询到
                if (CollectionUtils.isEmpty(clusterIdList)){
                    serviceOrders.forEach(serviceOrder -> {
                        clusterIdList.add(serviceOrder.getClusterId());
                    });
                }
                if (CollectionUtils.isNotEmpty(clusterIdList)) {
                    queryResHpcClusterRequest.setClusterIdList(clusterIdList);
                    clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
                }
            }



        } else if (!StringUtil.isNullOrEmpty(distributorName)) {   //查询所属分销商
            List<Long> orgSids = new ArrayList<>();
            //获取所有直营的org集合
            List<Long> directlyOrgList = Lists.newArrayList();
            List<BizBillingAccount> bizBillingAccountList = bizBillingAccountMapper.selectAllDirectlyAccount();
            bizBillingAccountList.forEach(e -> {
                directlyOrgList.add(e.getOrgSid());
                List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e.getOrgSid());
                if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                    directlyOrgList.addAll(childrenOrgIds);
                }
            });
            if ("直营".equals(distributorName) || "直".equals(distributorName) || "营".equals(distributorName)) {
                orgSids.addAll(directlyOrgList);
            } else {
                orgService.selectOrgIdListByOrgNameLike(distributorName).forEach(e -> {
                    orgSids.add(e);
                    List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e);
                    if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                        orgSids.addAll(childrenOrgIds);
                    }
                });
                log.info("orgSids————>{}",orgSids);
                //移除直营的Org集合
                orgSids.removeAll(directlyOrgList);
            }
            if (CollectionUtil.isNotEmpty(orgSids)) {
                log.info("orgSids————>{}",orgSids);
                queryResHpcClusterRequest.setOrgSidList(orgSids);
                clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
            }
        } else {
            clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
        }
        List<ResHpcClusterInstancesResponse> instancesResponses = new ArrayList<>();
        if (clusterResult != null && clusterResult.getStatus()) {
            Object data = clusterResult.getData();
            baseGridReturn = BeanConvertUtil.convert(data, BaseGridReturn.class);
            List list = baseGridReturn.getDataList();
            List<ProductResourceHPCResponse> hpcResponses = BeanConvertUtil.convert(list, ProductResourceHPCResponse.class);
            Map<Long, ProductResourceHPCResponse> hpcResponseMap = hpcResponses.stream()
                    .collect(Collectors.toMap(ProductResourceHPCResponse::getClusterId, prh -> prh));
            Set<Long> clusterIdSet = hpcResponseMap.keySet();
            if (CollectionUtil.isEmpty(clusterIdSet)) {
                baseGridReturn.setDataList(instancesResponses);
                return baseGridReturn;
            }
            QueryWrapper<SfProductResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("cluster_id", clusterIdSet);
            queryWrapper.in("product_type", Arrays.asList(ProductCodeEnum.HPC_DRP.getProductCode(),ProductCodeEnum.HPC.getProductCode(),ProductCodeEnum.HPC_SAAS.getProductCode()));
            queryWrapper.orderByDesc( "id");
            List<SfProductResource> sfProductResources = this.baseMapper.selectList(queryWrapper);
            Map<Long, SfProductResource> orderIdProductMap = sfProductResources.stream()
                    .filter(sfP->sfP.getServiceOrderId() !=null).collect(Collectors.toMap(SfProductResource::getServiceOrderId, sfp -> sfp));
            Set<Long> orderIdSet = orderIdProductMap.keySet();
            Map<Long, SfProductResource> clusterIdProductMap = sfProductResources.stream()
                    .filter(Objects::nonNull).collect(Collectors.toMap(SfProductResource::getClusterId, sfp -> sfp,(oldValue,newValue)->newValue));

            QueryWrapper<ServiceOrder> tWrapper = new QueryWrapper<>();

            tWrapper.in("cluster_id",clusterIdSet);
            if (CollectionUtil.isNotEmpty(orderIdSet)) {
                tWrapper.or().in("id", orderIdSet);
            }
            tWrapper.orderByDesc("created_dt");
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(tWrapper);
            Map<String, ServiceOrder> orderMap = new HashMap<>(5);
            Map<String, ServiceOrder> orderClusterMap = new HashMap<>(5);
            for (ServiceOrder serviceOrder : serviceOrders) {

                QueryWrapper<ServiceOrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
                orderDetailQueryWrapper.lambda()
                                       .eq(ServiceOrderDetail::getOrderId, serviceOrder.getId())
                                       .in(ServiceOrderDetail::getServiceType,
                                           Arrays.asList(ProductCodeEnum.HPC.getProductCode(),
                                                         ProductCodeEnum.HPC_DRP.getProductCode(),
                                                         ProductCodeEnum.HPC_SAAS.getProductCode()));
                Integer selectCount = serviceOrderDetailMapper.selectCount(orderDetailQueryWrapper);
                if (selectCount < 1) {
                    continue;
                }

                orderMap.put(serviceOrder.getId().toString(),serviceOrder);
                if (Objects.nonNull(serviceOrder.getClusterId())) {
                    orderClusterMap.put(serviceOrder.getClusterId().toString(), serviceOrder);
                }
            }
            hpcResponses.stream().forEach(resourceHPCResponse -> {

                ResHpcClusterInstancesResponse instResp = new ResHpcClusterInstancesResponse();
                instResp.setProessPhase(resourceHPCResponse.getProessPhase());
                instResp.setProessStatus(resourceHPCResponse.getProessStatus());
                instancesResponses.add(instResp);
                Long clusterId = resourceHPCResponse.getClusterId();

                SfProductResource sfp = clusterIdProductMap.get(clusterId);
                Long serviceOrderId;
                if (Objects.nonNull(sfp)) {
                    serviceOrderId = sfp.getServiceOrderId();
                    if (StringUtils.isBlank(sfp.getFreezingStrategy())) {
                        if (ArrayUtils.contains(new String[]{ProductCodeEnum.HPC.getProductCode(), ProductCodeEnum.HPC_SAAS.getProductCode()}, sfp.getProductType())) {
                            sfp.setFreezingStrategy(FreezingStrategyEnum.CONTINUE_OPERATION.getCode());
                        } else if (StringUtils.equals(ProductCodeEnum.HPC_DRP.getProductCode(), sfp.getProductType())) {
                            sfp.setFreezingStrategy(FreezingStrategyEnum.RESERVED_RESOURCE.getCode());
                        }
                    }else {
                        instResp.setFreezingStrategy(sfp.getFreezingStrategy());
                        instResp.setStrategyBufferPeriod(sfp.getStrategyBufferPeriod());
                    }
                } else {
                    ServiceOrder order = orderClusterMap.get(clusterId.toString());
                    if (Objects.nonNull(order)) {
                        serviceOrderId = order.getId();
                    } else {
                        serviceOrderId = null;
                    }

                }
                if (sfp == null) {
                    instResp.setOrgSid(resourceHPCResponse.getOrgSid());
                    instResp.setStatus(resourceHPCResponse.getStatus());
                    instResp.setChargeType(resourceHPCResponse.getChargeType());
                    instResp.setClusterId(clusterId);
                    instResp.setName(resourceHPCResponse.getClusterName());
                }else{
                    BeanUtils.copyProperties(sfp,instResp);
                    instResp.setName(resourceHPCResponse.getClusterName());
                }
                instResp.setOwnerId(resourceHPCResponse.getOwnerId());

                if(serviceOrderId ==null){
                    return;
                }
                ServiceOrder serviceOrder = orderMap.get(serviceOrderId.toString());
                if (serviceOrder == null) {
                    return;
                }
                List<ServiceOrderDetail> serviceOrderDetails = iServiceOrderPriceDetailService.selectOrderDetailByCriteria(new Criteria("orderId",serviceOrderId));
                Optional<ServiceOrderDetail> detailOptional = serviceOrderDetails.stream()
                     .filter(detail ->
                         ProductCodeEnum.HPC.getProductCode()
                                        .equals(detail.getServiceType())
                             || ProductCodeEnum.HPC_DRP.getProductCode()
                                                       .equals(detail.getServiceType())
                             || ProductCodeEnum.HPC_SAAS.getProductCode()
                                                        .equals(detail.getServiceType()))
                     .findFirst();
                if(!detailOptional.isPresent()){
                    return;
                }

                ServiceOrderDetail serviceOrderDetail = detailOptional.get();
                instResp.setProductName(serviceOrder.getProductName());
                instResp.setProductType(serviceOrderDetail.getServiceType());
                instResp.setClusterId(clusterId);
                instResp.setChargeType(serviceOrderDetail.getChargeType());
                iServiceOrderPriceDetailService.setProductConfigDescNodeInfo(serviceOrderDetail);
                instResp.setConfigDesc(serviceOrderDetail.getServiceConfig());
                instResp.setQuantity(serviceOrderDetail.getQuantity());
                if (serviceOrder.getContractId() != null) {
                    BizContract bizContract = bizContractMapper.selectById(serviceOrder.getContractId());
                    instResp.setContractName(bizContract.getContractTitle());
                    instResp.setContractFile(bizContract.getContractFile());
                    instResp.setContractFileName(bizContract.getContractFileName());
                }
                instResp.setApplyOrderSn(String.valueOf(serviceOrderDetail.getOrderId()));
                instResp.setOrderSn(serviceOrder.getOrderSn());
                User user = userMapper.selectByPrimaryKey(Long.valueOf(serviceOrder.getOwnerId()));
                Org org = orgService.getById(serviceOrder.getOrgSid());
                instResp.setOrgName(Objects.isNull(org) ? "" : org.getOrgName());
                instResp.setBehalfUserSid(serviceOrder.getBehalfUserSid());
                instResp.setContractId(serviceOrder.getContractId() == null ? null : serviceOrder.getContractId().toString());

                instResp.setSettlementType(serviceOrder.getSettlementType());

                instResp.setOriginalCost(serviceOrder.getOriginalCost());
                instResp.setFinalCost(serviceOrder.getFinalCost());
                instResp.setStartTime(serviceOrderDetail.getStartTime());
                User authUser = AuthUtil.getAuthUser();

                if (Objects.nonNull(user)) {
                    instResp.setDistributorName("直营");
                    cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(
                            WebUtil.getRequest());
                    BizBillingAccount billingAccount = new BizBillingAccount();
                    if (flag) {
                        billingAccount = bizBillingAccountMapper.selectByOrgIdAndEntityId(user.getCompanyId(),
                                                                                          authUserInfo.getEntityId());
                    } else {
                        billingAccount = bizBillingAccountMapper.selectByOrgIdAndEntityId(user.getCompanyId(), null);
                    }
                    if (!Objects.isNull(billingAccount) && !StringUtil.isNullOrEmpty(billingAccount.getDistributorName())) {
                        instResp.setDistributorName(billingAccount.getDistributorName());
                    }
                    if (!Objects.isNull(billingAccount)) {
                        instResp.setAccountName(billingAccount.getAccountName());
                    }
                }

                if (ChargeTypeEnum.PostPaid.getType().equals(instResp.getChargeType())) {
                    instResp.setEndTime(null);
                }

                //查询如果是已退订的共享资源池，则添加结束时间
                boolean isUnsubscribedHpc = (ProductCodeEnum.HPC.getProductCode().equals(instResp.getProductType())
                        || ProductCodeEnum.HPC_SAAS.getProductCode().equals(instResp.getProductType()))
                        && SfProductEnum.UNSUBSCRIBED.getStatus().equals(resourceHPCResponse.getStatus());
                if (isUnsubscribedHpc) {
                    instResp.setEndTime(resourceHPCResponse.getEndTime());
                }


                ProductResourceHPCResponse response = hpcResponseMap.get(clusterId);
                if(response !=null){
                    instResp.setStatus(response.getStatus());
                }

            });

        }
        baseGridReturn.setDataList(instancesResponses);
        return baseGridReturn;
    }


    private List<Long> addResourceIdByEntityId(
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo) {
        Criteria criteria = new Criteria();
        criteria.put("entityId", authUserInfo.getEntityId());
        criteria.put("serviceType", "HPC");
        List<Long> resourceIdList = this.baseMapper.getClusterIdByEntityId(criteria);

        //HPC共享资源池退订后，无法在productResource表中查询到
        Criteria criteria1 = new Criteria();
        criteria1.put("entityId", authUserInfo.getEntityId());
        criteria1.put("type", "release");
        criteria1.put("serviceTypeIn",
                      ProductCodeEnum.HPC.getProductCode() + "," + ProductCodeEnum.HPC_SAAS.getProductCode() + ","
                              + ProductCodeEnum.HPC_DRP.getProductCode());
        List<ServiceOrderVo> serviceOrderVos = this.serviceOrderMapper.selectServiceOrderAndDetail(criteria1);
        if (CollectionUtils.isNotEmpty(serviceOrderVos)) {
            for (ServiceOrderVo serviceOrder : serviceOrderVos) {
                if (serviceOrder.getClusterId() != null) {
                    resourceIdList.add(serviceOrder.getClusterId());
                }
            }
            //去重
            resourceIdList = resourceIdList.stream().distinct().collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(resourceIdList)) {
            //查询使用的是IN，当该运营实体没有对应的资源时，用不存在资源id去过滤数据
            resourceIdList.add(-1L);
        }
        return resourceIdList;
    }


    @Override
    public ResHpcClusterDetailVO getHpcClusterDetailById(Long orgSid, Long clusterId) {
        User authUser = AuthUtil.getAuthUser();

        User user = userMapper.selectByPrimaryKey(authUser.getUserSid());
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_SEARCH_STATUS);
        queryResHpcClusterRequest.setOrgSid(orgSid);
        List<Long> cluIdList = policyAssertionService.findResourceRelationByUser("hpc", user.getUserSid(),
                orgSid,
                (Function<ResHpcClusterVO, Long>) (a) -> a
                        .getId(), queryResHpcClusterRequest);

        RestResult restResult;
        if (!CollectionUtil.isEmpty(cluIdList) && cluIdList.contains(clusterId)) {
            restResult = hpcClusterService.resHpcClusterDetail(orgSid, clusterId);
        } else {
            restResult = null;
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1982535027));
        }

        if (restResult.getStatus()) {
            Object data = restResult.getData();
            ResHpcClusterDetailVO detailVO = BeanConvertUtil.convert(data, ResHpcClusterDetailVO.class);
            if (detailVO != null) {
                Integer vncNodeNum = detailVO.getVNCNodeNum();
                if (Objects.isNull(vncNodeNum) || vncNodeNum < 0) {
                    detailVO.setVNCNodeNum(0);
                }
                ResHpcClusterDetailDTO detailDTO = BeanConvertUtil.convert(data, ResHpcClusterDetailDTO.class);
                List<NodeInfo> nodeList = detailVO.getNodeList();
                List<Code> nodeTypeList = codeMapper.selectByCodeCategory("NODE_TYPE");
                Map<String, String> nodeTypeMap = nodeTypeList.stream().collect(Collectors.toMap(Code::getCodeValue, Code::getCodeDisplay));
                List<ResBmsDTO> bmsDTOList = detailDTO.getBmsDTOList();
                bmsDTOList.forEach(dto -> {
                    NodeInfo nodeInfo = BeanConvertUtil.convert(dto, NodeInfo.class);
                    nodeInfo.setNodeType(dto.getNodeType());
                    nodeInfo.setName(assembleNodeName(dto));
                    nodeList.add(nodeInfo);
                });
                List<ResEcsDTO> ecsDTOList = detailDTO.getEcsDTOList();
                ecsDTOList.forEach(dto -> {
                    NodeInfo nodeInfo = BeanConvertUtil.convert(dto, NodeInfo.class);
                    nodeInfo.setNodeType(dto.getNodeType());
                    nodeInfo.setName(assembleNodeName(dto));
                    nodeList.add(nodeInfo);
                });
                if (clusterId != null ) {
                    Criteria criteria = new Criteria();
                    criteria.put("cluster_id", clusterId);
                    criteria.put("product_type_like", ProductCodeEnum.HPC.getProductCode());
                    List<SfProductResource> sfProductResources = this.baseMapper.selectByParams(criteria);
                    if (CollectionUtil.isNotEmpty(sfProductResources)) {
                        SfProductResource productResource = CollectionUtil.getFirst(sfProductResources);
                        detailVO.setId(productResource.getId());
                        detailVO.setProductType(productResource.getProductType());
                        detailVO.setProductName(productResource.getProductName());
                        if (StringUtils.isEmpty(detailVO.getPoolName())) {
                            detailVO.setPoolName(detailVO.getClusterName());
                            detailVO.setName(detailVO.getClusterName());
                        } else {
                            detailVO.setName(detailVO.getPoolName());
                        }
                        detailVO.setStartTime(productResource.getStartTime());
                        detailVO.setEndTime(productResource.getEndTime());
                        detailVO.setFrozenTime(productResource.getFrozenTime());
                        if (ScenarioEnum.getByCode(detailVO.getScenario()) != null) {
                            detailVO.setScenario(ScenarioEnum.getByCode(detailVO.getScenario()).getMessage());
                        }else {
                            detailVO.setScenario(ScenarioEnum.Unpaired.getMessage());
                        }
                        Long serviceOrderId = productResource.getServiceOrderId();
                        ServiceOrder serviceOrder = serviceOrderMapper.selectById(serviceOrderId);
                        if (serviceOrder != null) {
                            //查询现金余额
                            Long userSid = authUser.getUserSid();
                            if (Objects.nonNull(authUser.getParentSid())) {
                                // 子用户需找租户账户
                                userSid = authUser.getParentSid();
                            }
                            BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(
                                    serviceOrder.getEntityId(), userSid);
                            if (Objects.isNull(account) || Objects.isNull(account.getBalance())
                                    || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
                                detailVO.setNodeDeletable(false);
                            }
                            detailVO.setEntityId(serviceOrder.getEntityId());
                        }
                        if (ProductCodeEnum.HPC_DRP.getProductCode().equals(productResource.getProductType())) {
                            List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectList(
                                    new QueryWrapper<ServiceOrderDetail>().lambda()
                                                                          .eq(ServiceOrderDetail::getOrderId,
                                                                              serviceOrderId));
                            if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {
                                String productConfigDesc = serviceOrderDetails.stream().filter(orderDetail ->
                                                                                                       StringUtils.equalsIgnoreCase(
                                                                                                               orderDetail.getServiceType(),
                                                                                                               ProductCodeEnum.HPC_DRP.getProductCode()))
                                                                              .map(ServiceOrderDetail::getProductConfigDesc)
                                                                              .findFirst().orElse("");
                                if (StringUtils.isNotEmpty(productConfigDesc)) {
                                    JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
                                    for (Object jsonO : jsonArray) {
                                        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                                           ServiceConfigArrKey.MAX_COMPUTE_NODE_NUM)) {
                                            detailVO.setMaxComputeNodeNum(
                                                    jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE));
                                        }
                                        if (detailVO.getMinComputeNodeNum() == null) {
                                            detailVO.setMinComputeNodeNum(1);
                                        }
                                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                                           ServiceConfigArrKey.MIN_COMPUTE_NODE_NUM)) {
                                            detailVO.setMinComputeNodeNum(
                                                    jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE));
                                        }
                                    }
                                }
                            }
                            return detailVO;
                        }
                        List<PriceVO> priceList = detailVO.getPriceList();
                        if (serviceOrderId != null) {
                            QueryWrapper<ServiceOrderDetail> tWrapper = new QueryWrapper<>();
                            tWrapper.eq("order_id", serviceOrderId);
                            List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectList(tWrapper);
                            if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {

                                String productConfigDesc = serviceOrderDetails.stream().filter(orderDetail ->
                                                                                                       StringUtils.equalsIgnoreCase(
                                                                                                               orderDetail.getServiceType(),
                                                                                                               ProductCodeEnum.HPC_DRP.getProductCode()))
                                                                              .map(ServiceOrderDetail::getProductConfigDesc)
                                                                              .findFirst().orElse("");
                                if (StringUtils.isNotEmpty(productConfigDesc)) {
                                    JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
                                    for (Object jsonO : jsonArray) {
                                        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                                           ServiceConfigArrKey.MAX_COMPUTE_NODE_NUM)) {
                                            detailVO.setMaxComputeNodeNum(
                                                    jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE));
                                        }
                                        if (detailVO.getMinComputeNodeNum() == null) {
                                            detailVO.setMinComputeNodeNum(1);
                                        }
                                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                                           ServiceConfigArrKey.MIN_COMPUTE_NODE_NUM)) {
                                            detailVO.setMinComputeNodeNum(
                                                    jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE));
                                        }
                                    }
                                }

                                //查询规格
                                QueryWrapper<BizBillingSpec> tWrapper1 = new QueryWrapper<>();
                                List<BizBillingSpec> bizBillingSpecs = bizBillingSpecMapper.selectList(tWrapper1);
                                Map<String, BizBillingSpec> specMap = bizBillingSpecs.stream().collect(Collectors.toMap(spec -> spec.getCategory() + "." + spec.getSpecName(), spec -> spec));
                                //查询类型
                                List<Code> strategyTypeList = codeMapper.selectByCodeCategory(BILLING_STRATEGY_TYPE);

                                //存储容量、使用量
                                List<HPCResponseShareVO> shareList = detailDTO.getShareList();
                                //公共目录大小
                                Long publicSize = 0L;
                                //专属文件系统大小
                                Long size = 0L;
                                //使用量（超出用量部分）
                                BigDecimal usedSize = BigDecimal.ZERO;
                                if (CollectionUtil.isNotEmpty(shareList)) {
                                    for (HPCResponseShareVO hpcResponseShareVO : shareList) {
                                        Long shareVOSize = hpcResponseShareVO.getSize() == null ? 0 : hpcResponseShareVO.getSize();
                                        if (Boolean.TRUE.equals(hpcResponseShareVO.getIsVirtual())) {
                                            publicSize = publicSize + shareVOSize;
                                        } else {
                                            size = size + shareVOSize;

                                            BigDecimal shareVOUsedSize = hpcResponseShareVO.getUsedSize();
                                            BigDecimal subSize = NumberUtil.sub(shareVOUsedSize == null ? BigDecimal.ZERO : shareVOUsedSize, shareVOSize);
                                            if(NumberUtil.isGreater(subSize,BigDecimal.ZERO)){
                                                usedSize = NumberUtil.add(subSize,usedSize);
                                            }
                                        }

                                    }
                                }
                                if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {
                                    //shareList无数据取订单中的容量
                                    if(CollectionUtil.isEmpty(shareList)){

                                        for (ServiceOrderDetail ordeDetail : serviceOrderDetails) {
                                            String serviceConfig = ordeDetail.getServiceConfig();
                                            ServiceConfig config = JSONObject.parseObject(serviceConfig, ServiceConfig.class);
                                            DataDTO configData = config.getData();
                                            HpcDTO hpc = configData.realHpcDTO();
                                            if(hpc !=null){
                                                String hpcVersion = hpc.getHpcVersion();
                                                if("3".equals(hpcVersion) && hpc.getSize() !=null){
                                                    publicSize =Long.valueOf(hpc.getSize());
                                                }
                                            }
                                            SfsDTO sfs = configData.realSfsDTO();
                                            if(sfs !=null && sfs.getSize() !=null){
                                                size=Long.valueOf(sfs.getSize());
                                            }
                                        }
                                    }


                                    for (ServiceOrderDetail ordeDetail : serviceOrderDetails) {
                                        String serviceConfig = ordeDetail.getServiceConfig();
                                        addPriceVo(priceList, serviceConfig, serviceOrderId, specMap, strategyTypeList,
                                                   size, publicSize, usedSize);
                                    }
                                }
                            }

                        }
                    }
                }
            }
            return detailVO;

        } else {
            log.info("call hpcClusterService.resHpcClusterDetail:{}", restResult);
        }
        return null;
    }

    private void assembleBaseMessageContent(Map<String, String> messageContent) {
        String companyName = PropertiesUtil.getProperty("company.name");
        String consoleUrl = PropertiesUtil.getProperty("rightcloud.console.url");
        String portalUrl = PropertiesUtil.getProperty("rightcloud.portal.url");
        messageContent.put("consoleUrl", consoleUrl);
        messageContent.put("portalUrl", portalUrl);
        messageContent.put("companyName", companyName);
        messageContent.put("logoUrl", consoleUrl + PropertiesUtil.getProperty("platform.large.logo"));
        messageContent.put("systemName", PropertiesUtil.getProperty("system.name"));
        messageContent.put("companyPhone", PropertiesUtil.getProperty("system.contact.number"));
    }

    @Override
    public void releasUnsubErrorRes(long id, Long userSid, String type) {
        Long behalfUserSid = null;
        try {
            if (ProductCodeEnum.SFS2.getProductCode().equals(type)) {
                behalfUserSid = this.replacementUser(userSid);

                ResShare resShare = shareRemoteService.selectByPrimaryKey(id);
                if (!userSid.equals(Long.parseLong(resShare.getOwnerId()))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if (!ShareStatus.ERROR_DELETING.equals(resShare.getStatus())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00004));
                }
                cn.com.cloudstar.rightcloud.common.pojo.RestResult restResult = shareRemoteService.deleteShareByIdAndOrderId(
                        id, null);
                ServiceOrder serviceOrder = serviceOrderMapper.selectOrderDetailByResourceId(String.valueOf(id), type);
                List<User> users = userMapper.selectUserByDataScope();
                Map<String, String> map = new HashMap<>();
                map.put("orgName", resShare.getOrgName());
                map.put("productName", ProductCodeEnum.SFS2.getProductName());
                map.put("orderSn", serviceOrder.getOrderSn());
                this.assembleBaseMessageContent(map);
                if (!restResult.getStatus()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1524362507));
                }
            } else if (ProductCodeEnum.HPC.getProductCode().equals(type) || ProductCodeEnum.HPC_SAAS.getProductCode()
                                                                                                    .equals(type)) {
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(id);
                String errorCode = null;
                if (!userSid.equals(resHpcClusterRemoteModule.getOwnerId())) {
                    errorCode = MsgCd.PARAM_NOT_VALID_ERROR;
                }
                if (!ResHpcClusterStatus.UNSUBSCRIBE_ERROR.equals(resHpcClusterRemoteModule.getStatus())) {
                    errorCode = MsgCd.ERROR_MSG_00004;
                }
                if (errorCode != null) {
                    List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectOrderByCluster(id, type);
                    ServiceOrderVo serviceOrderVo = serviceOrderVos.get(0);
                    Org org = orgMapper.selectById(resHpcClusterRemoteModule.getOrgSid());
                    List<User> users = userMapper.selectUserByDataScope();
                    Map<String, String> map = new HashMap<>();
                    map.put("orgName", org.getOrgName());
                    map.put("productName", ProductCodeEnum.HPC_DRP.getProductName());
                    map.put("orderSn", serviceOrderVo.getOrderSn());
                    this.assembleBaseMessageContent(map);
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_FAIL);
                    baseNotificationMqBean.setMap(map);
                    baseNotificationMqBean.setEntityId(serviceOrderVo.getEntityId());
                    baseNotificationMqBean.getImsgUserIds()
                                          .addAll(users.stream().map(User::getUserSid).collect(Collectors.toSet()));
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                                  baseNotificationMqBean);
                    throw new BizException(WebUtil.getMessage(errorCode));
                }
                //获取共享资源池账号和密码
                String poolUuid = resHpcClusterRemoteModule.getPoolUuid();
                ResHpcClusterPool resHpcClusterPool = null;
                String password = resHpcClusterPool.getPassword();
                String username = resHpcClusterPool.getUsername();
                ReleaseHPCSharePool releaseHPCSharePool = new ReleaseHPCSharePool();
                releaseHPCSharePool.setHPCClusterID(resHpcClusterRemoteModule.getId());
                // 设置共享资源池密码
                releaseHPCSharePool.setCcpPassword(CrytoUtilSimple.decrypt(password));
                releaseHPCSharePool.setCcpUser(CrytoUtilSimple.decrypt(username));
                releaseHPCSharePool.setStatus(ResHpcClusterStatus.UNSUBSCRIBING_RETRY);
                //调用ccm
                BasicInfoUtil.replaceUserToInvoke(() -> hpcRemoteService.releaseShareHPC(releaseHPCSharePool), userSid);
                if (ProductCodeEnum.HPC.getProductCode().equals(type)) {
                    List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectOrderByCluster(id, type);
                    ServiceOrderVo serviceOrderVo = serviceOrderVos.get(0);
                    Org org = orgMapper.selectById(resHpcClusterRemoteModule.getOrgSid());
                    List<User> users = userMapper.findAdminstratorsByEntityId(serviceOrderVo.getEntityId());

                    Map<String, String> map = new HashMap<>();
                    map.put("orgName", org.getOrgName());
                    map.put("productName", ProductCodeEnum.HPC.getProductName());
                    map.put("orderSn", serviceOrderVo.getOrderSn());
                    map.put("userAccount", serviceOrderVo.getCreatedBy());
                    this.assembleBaseMessageContent(map);
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(id);
                    if (ResHpcClusterStatus.UNSUBSCRIBE_ERROR.equals(resHpcClusterRemoteModule.getStatus())) {
                        baseNotificationMqBean.setMsgId(
                                NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_FAIL);
                    } else {
                        baseNotificationMqBean.setMsgId(
                                NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_SUCCESS);
                    }
                    baseNotificationMqBean.setMap(map);
                    baseNotificationMqBean.setEntityId(serviceOrderVo.getEntityId());
                    baseNotificationMqBean.getImsgUserIds()
                                          .addAll(users.stream().map(User::getUserSid).collect(Collectors.toSet()));
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                                  baseNotificationMqBean);
                }
            } else if (ProductCodeEnum.HPC_DRP.getProductCode().equals(type)) {
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(id);
                if (!userSid.equals(resHpcClusterRemoteModule.getOwnerId())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if (!ResHpcClusterStatus.UNSUBSCRIBE_ERROR.equals(resHpcClusterRemoteModule.getStatus())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00004));
                }
                HPCClusterDeleteResult hpcClusterDeleteResult = hpcRemoteService.releaseDrpHPC(id);
                String taskID = hpcClusterDeleteResult.getTaskID();
                taskID = StringUtils.isNotEmpty(taskID)? taskID:hpcClusterDeleteResult.getTaskId();
                resHpcClusterRemoteModule.setUnsubscribeTaskId(taskID);
                resHpcClusterRemoteModule.setStatus(ResHpcClusterStatus.UNSUBSCRIBING_RETRY);
                log.debug("释放HPC专属资源池,更改集群状态：[{}]", JSONUtil.toJsonStr(resHpcClusterRemoteModule));

                hpcRemoteService.updateByPrimaryKeySelective(resHpcClusterRemoteModule);
            } else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_NOT_SUPPORT));
            }
        } finally {
            if (Objects.nonNull(behalfUserSid)) {
                AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(behalfUserSid),
                                                                AuthUser.class);
                AuthUserHolder.setAuthUser(authUserInfo);
            }
        }
        this.orderActionLog(id, type);
    }

    private void orderActionLog(Long id, String type) {
        try {
            StringBuffer actionName = new StringBuffer();
            actionName.append("资源释放：");

            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            String roleName = "运营管理员(内置)";
            assert authUserInfo != null;
            actionName.append("[" + ProductCodeEnum.getByCode(type) + "]，");

            String productName;
            if (ProductCodeEnum.SFS2.getProductCode().equals(type)) {
                ServiceOrder serviceOrder = serviceOrderService.selectOrderDetailByResourceId(String.valueOf(id), type);
                productName = serviceOrder.getName();
            } else {
                List<ServiceOrderVo> serviceOrderVos = serviceOrderMapper.selectOrderByCluster(id, type);
                ServiceOrderVo serviceOrderVo = serviceOrderVos.get(0);
                productName = serviceOrderVo.getName();
            }
            actionName.append("名称：[" + productName + "]");
            HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
            String userClient = request.getHeader("User-Agent");
            String remoteIp = cn.com.cloudstar.rightcloud.oss.common.util.IPAddressUtil.getRemoteHostIp(request);
            String traceId = request.getRequestedSessionId();
            String spanId = "--";
            if (Objects.nonNull(tracer)) {
                traceId = tracer.currentTraceContext().context().traceId();
                spanId = tracer.currentTraceContext().context().spanId();
            }
            mongoTemplate.insert(ActionLog.builder()
                                          .actionName(actionName.toString())
                                          .account(authUserInfo.getAccount())
                                          .actionPath(request.getRequestURI())
                                          .actionTime(new Date())
                                          .httpMethod(request.getMethod())
                                          .lbIp(request.getRemoteAddr())
                                          .remoteIp(remoteIp)
                                          .traceId(traceId)
                                          .spanId(spanId)
                                          .roleName(roleName)
                                          .client(userClient).build()
                    , "action_log");
        } catch (Exception e) {
            log.error("资源释放日志记录-失败-orderActionLog：[{}]", e.getMessage());
        }
    }

    @Override
    public Long queryOwnerId(long id, String type) {
        if (ProductCodeEnum.SFS2.getProductCode().equals(type)) {
            ResShare resShare = shareRemoteService.selectByPrimaryKey(id);
            return Long.parseLong(resShare.getOwnerId());
        } else if (ProductCodeEnum.HPC.getProductCode().equals(type)
                || ProductCodeEnum.HPC_SAAS.getProductCode().equals(type)
                || ProductCodeEnum.HPC_DRP.getProductCode().equals(type)) {
            ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(id);
            return resHpcClusterRemoteModule.getOwnerId();
        }
        return null;
    }

    private Long replacementUser(Long userSid) {
        Long behalfUserSid = null;
        if (Objects.nonNull(userSid)) {
            User authUser = AuthUtil.getAuthUser();
            behalfUserSid = authUser.getUserSid();
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(userSid),
                                                            AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        log.info("当前用户名称：[{}]", authUserInfo.getAccount());
        return behalfUserSid;
    }

    @Override
    public Boolean getCheckHpcCluster(Long userSid, Long  clusterId) {
        User user = userMapper.selectByPrimaryKey(userSid);
        Org org = orgService.selectRootOrg(user.getOrgSid());
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_SEARCH_STATUS);
        queryResHpcClusterRequest.setOrgSid(user.getOrgSid());
        List<Long> hpcCluIdList = policyAssertionService.findResourceRelationByUser("hpc", user.getUserSid(),
                user.getOrgSid(),
                (Function<ResHpcClusterVO, Long>) (a) -> a
                        .getId(), queryResHpcClusterRequest);

        Boolean result;
        //增加企业认证的判断
        if(!"authSucceed".equals(org.getCertificationStatus()) && !"noAuth".equals(org.getCertificationStatus())){
            result=false;
        }

        if("authSucceed".equals(user.getCertificationStatus()) || "authSucceed".equals(org.getCertificationStatus())){
            if (Objects.nonNull(user) && Objects.nonNull(user.getParentSid())) {
                result = getIsExistence(hpcCluIdList, clusterId);
            } else {
                result = true;
            }
        }else {
            result=false;
        }
        return result;
    }

    private String assembleNodeName(NodeDTO dto) {
        String nameTmp = "{}核{}G({})";
        Integer gb = Convert.toInt(dto.getRam(), 0);
        return StrUtil.format(nameTmp, dto.getCpu(), gb, dto.getTypeName());
    }

    private void addPriceVo(List<PriceVO> priceVOList, String serviceConfig, Long serviceOrderId,
                            Map<String, BizBillingSpec> specMap, List<Code> codes, Long size, Long publicSize, BigDecimal usedSize) {
        if (StringUtils.isNotEmpty(serviceConfig)) {
            ServiceConfig config = JSONObject.parseObject(serviceConfig, ServiceConfig.class);
            DataDTO dataDTO = config.getData();
            ProductConfigDescDTO productConfigDesc = config.getProductConfigDesc();
            if (dataDTO == null) {
                return;
            }
            HpcDTO hpc = dataDTO.realHpcDTO();
            //getCurrentConfigDesc 这个参数值为json格式前端约定，与前端确认
            List<HashMap> hashMaps = new ArrayList<>();
            Map<String, HashMap> attrKeyMap = new HashMap<>(5);
            Map<String, HashMap> labelMap = new HashMap<>(5);
            //兼容2.1.3版本CurrentConfigDesc不为数组的情况
            try {
                hashMaps  = JSONObject.parseArray(productConfigDesc.getCurrentConfigDesc(), HashMap.class);
                attrKeyMap = hashMaps.stream().collect(Collectors.toMap(map -> (String) map.get("attrKey"), map -> map));
            } catch (Exception e) {
                log.error("json解析productConfigDesc异常 :",e);
                JSONObject configDesc  = JSONObject.parseObject(productConfigDesc.getCurrentConfigDesc());
                JSONArray currentDescArray = configDesc.getJSONArray("currentDesc");

                for (Object o : currentDescArray) {
                    JSONObject jsonObject = (JSONObject)o;
                    Set<String> keys = jsonObject.keySet();
                    HashMap<String, String> objectObjectHashMap = new HashMap<>(5);
                    for (String key : keys) {
                        objectObjectHashMap.put(key,jsonObject.getString(key));
                    }
                    hashMaps.add(objectObjectHashMap);

                }
                labelMap = hashMaps.stream().collect(Collectors.toMap(map -> (String) map.get("label"), map -> map));
            }

            if (hpc != null) {
                //查询价格
                List<BillingPricesDTO> hpcBillingPrices = getBillingPrices(hpc.getBillingPrices(), dataDTO,
                                                                           serviceOrderId, serviceConfig);
                if (CollectionUtil.isNotEmpty(hpcBillingPrices)) {
                    boolean isNode = false;
                    HpcClusterNodeResponse nodeInfo = new HpcClusterNodeResponse();
                    if (Arrays.asList(ProductCodeEnum.HPC.getProductCode(), ProductCodeEnum.HPC_SAAS.getProductCode()).contains(hpc.getProductCode())) {
                        RestResult pool = hpcClusterService.getResHpcClusterPoolById(Long.parseLong(hpc.getHpcClusterID()));
                        if (Objects.nonNull(pool.getData())){
                            ResHpcClusterPool resHpcClusterPool = BeanConvertUtil.convert(pool.getData(), ResHpcClusterPool.class);
                        RestResult node = hpcFeignService.getNode(GetNodeRequest.builder().serviceId(config.getServiceId()).clusterId(resHpcClusterPool.getClusterId()).build());
                            nodeInfo = BeanConvertUtil.convert(node.getData(), HpcClusterNodeResponse.class);
                            isNode = true;
                        }
                    }
                    for (BillingPricesDTO billingPrice : hpcBillingPrices) {
                        PriceVO priceVO = createPriceVO(specMap, codes, billingPrice);
                        //
                        if ("cpuUsage".equals(billingPrice.getSpecType())) {
                            if (isNode) {
                                priceVO.setSpecValue(nodeInfo.getCpuSpecification());
                            } else {
                            HashMap cpuConfig = attrKeyMap.get("hpc_config");
                            if (cpuConfig != null) {
                                priceVO.setSpecValue(cpuConfig.get("value").toString());
                            }
                            cpuConfig = labelMap.get("CPU规格");
                            if (cpuConfig != null) {
                                priceVO.setSpecValue(cpuConfig.get("value").toString());
                            }
                        }
                        }
                        if ("gpuUsage".equals(billingPrice.getSpecType())) {
                            if (isNode) {
                                priceVO.setSpecValue(nodeInfo.getGpuSpecification());
                            } else {
                            HashMap gpuConfig = attrKeyMap.get("gpu_config");
                            if (gpuConfig != null) {
                                priceVO.setSpecValue(gpuConfig.get("value").toString());
                            }
                            gpuConfig = labelMap.get("GPU规格");
                            if (gpuConfig != null) {
                                if (gpuConfig.get("value") != null) {
                                    priceVO.setSpecValue(gpuConfig.get("value").toString());
                                } else {
                                    priceVO.setSpecValue("");
                                }
                            }
                        }
                        }
                        priceVOList.add(priceVO);
                    }
                }
            }

            SfsDTO sfs = dataDTO.realSfsDTO();
            if (sfs != null) {
                //查询价格
                List<BillingPricesDTO> billingPrices = getBillingPrices(sfs.getBillingPrices(), dataDTO, serviceOrderId,
                                                                        serviceConfig);
                String unit = "GB";
                if (CollectionUtil.isNotEmpty(billingPrices)) {
                    for (BillingPricesDTO billingPrice : billingPrices) {
                        PriceVO priceVO = createPriceVO(specMap, codes, billingPrice);
                        if ("capacity".equals(billingPrice.getSpecType())) {
                            priceVO.setSpecValue(publicSize + unit + "（共享目录）+" + size + unit + "（专属文件系统）");
                        }
                        if ("storageUsage".equals(billingPrice.getSpecType())) {
                            priceVO.setSpecValue(usedSize.intValue() + unit);
                        }
                        priceVOList.add(priceVO);
                    }
                }
            }
        }
    }

    private PriceVO createPriceVO(Map<String, BizBillingSpec> specMap,
                                  List<Code> codes, BillingPricesDTO billingPrice) {

        String fixedPriceTmp = "￥{}+";
        String priceTmp = "￥{}/{}/小时";
        PriceVO priceVO = new PriceVO();
        String category = billingPrice.getCategory();
        String specType = billingPrice.getSpecType();
        String unit = "";
        Map<String, String> categoryMap = codes.stream().collect(Collectors.toMap(code -> code.getCodeValue(), code -> code.getCodeDisplay()));
        priceVO.setCategory(categoryMap.get(category));
        BizBillingSpec bizBillingSpec = specMap.get(category + "." + specType);
        if (bizBillingSpec != null) {
            priceVO.setSpecDesc(bizBillingSpec.getSpecDescription());
            unit = bizBillingSpec.getUnit();
        }
        priceVO.setSpecValue("");
        String fixedHourPrice = billingPrice.getFixedHourPrice();
        StringBuffer originalSb = new StringBuffer();

        if (StringUtils.isNotEmpty(fixedHourPrice) && !"0".equals(fixedHourPrice)) {
            originalSb.append(StrUtil.format(fixedPriceTmp, fixedHourPrice));
        }
        String unitHourPrice = billingPrice.getUnitHourPrice();

        originalSb.append(StrUtil.format(priceTmp, unitHourPrice, unit));
        priceVO.setOriginalPrice(originalSb.toString());

        String tradeFixedHourPrice = billingPrice.getTradeFixedHourPrice();
        StringBuffer tradeSb = new StringBuffer();

        if (StringUtils.isNotEmpty(tradeFixedHourPrice) && !"0".equals(tradeFixedHourPrice)) {
            tradeSb.append(StrUtil.format(fixedPriceTmp, tradeFixedHourPrice));
        }
        String tradeUnitHourPrice = billingPrice.getTradeUnitHourPrice();

        tradeSb.append(StrUtil.format(priceTmp, tradeUnitHourPrice, unit));
        priceVO.setDiscountPrice(tradeSb.toString());
        return priceVO;
    }

    private Integer getGB(String ram) {
        BigDecimal gb = BigDecimal.ZERO;
        if (StringUtil.isNotEmpty(ram)) {
            gb = NumberUtil.div(new BigDecimal(ram), 1024);
        }
        return gb.intValue();
    }

    //查询用户关联的所有HPC集群id
    private List<Long> getHpcCluIdList(Long userSid) {
        // 查找用户所关联的用户组or用户or策略组or策略断言的所有有关系的策略断言信息
        List<PolicyAssertion> policyAssertionList = this.policyAssertionMapper.selectPolicyAssertion(
                userSid, "hpc");
        List<Long> hpcCluIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(policyAssertionList)) {
            for (PolicyAssertion policyAssertion : policyAssertionList) {
                // 查询断言规则
                List<PolicyAssertion> assertionList = policyAssertionService.lambdaQuery()
                                                                            .eq(PolicyAssertion::getPolicySid, policyAssertion.getPolicySid())
                                                                            .like(PolicyAssertion::getAssertionExpression, "%" + HpcConstance.TYPE_HPC + "%")
                                                                            .orderByAsc(PolicyAssertion::getAssertionSid).list();
                List<Long> hpcClusterList = Lists.newArrayList();
                if (CollectionUtil.isNotEmpty(assertionList)) {
                    // 解析关联资源
                    PolicyResourceStrategy strategy = new PolicyHpcResourceStrategy();
                    List<String> assertionExpressionList = assertionList.stream().
                            map(PolicyAssertion::getAssertionExpression).collect(Collectors.toList());
                    List<PolicyHpcResourceVO> resourceList =
                            (List<PolicyHpcResourceVO>) strategy.parseResource(assertionExpressionList);
                    if (CollectionUtil.isNotEmpty(resourceList)) {
                        hpcClusterList =
                                resourceList.stream().map(PolicyHpcResourceVO::getResourceId).collect(Collectors.toList());
                    }
                    hpcCluIdList.addAll(hpcClusterList);
                }
            }
        }
        return hpcCluIdList;
    }

    //通过clusterId判断用户是否关联了该HPC资源
    private Boolean getIsExistence(List<Long> hpcCluIdList, Long clusterId) {
        if (CollectionUtil.isNotEmpty(hpcCluIdList)) {
            for (Long clusterId1 : hpcCluIdList) {
                if (Objects.equals(clusterId1, clusterId)) {
                    return true;
                }
            }
        } else {
            return false;
        }
        return false;
    }

    @Override
    public IPage<DescribeProductResourceResponse> exportListResources(DescribeProductResourceRequest request) {
        request.setMgtConsole(true);
        request.setPagesize((long) this.count(null));
        request.setPagenum(0L);
        IPage<DescribeProductResourceResponse> responsePage = listResources(request);

        //新增开始----------------------------------------------------
        //系统配置表中获取具体的到期时间提醒
        int days= Integer.parseInt(PropertiesUtil.getProperty("upcoming_expired_days"));
        Date now = DateUtil.date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.DATE, + days);
        List<DescribeProductResourceResponse> records = responsePage.getRecords();
        for (DescribeProductResourceResponse resourceResponse : records){
            if (StringUtils.isBlank(resourceResponse.getFreezingStrategy())) {
                if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(resourceResponse.getProductType())) {
                    resourceResponse.setFreezingStrategy(FreezingStrategyEnum.RESERVED_RESOURCE.getCode());
                }else if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(resourceResponse.getProductType())) {
                    resourceResponse.setFreezingStrategy(FreezingStrategyEnum.CONTINUE_OPERATION.getCode());
                }
            }
            if (Objects.isNull(resourceResponse.getStrategyBufferPeriod())) {
                resourceResponse.setStrategyBufferPeriod(0);
            }
            //状态
            if("normal".equalsIgnoreCase(resourceResponse.getStatus())
                    && !StringUtil.isNullOrEmpty(resourceResponse.getEndTime())){
                //时间差
                if (now.getTime() <= resourceResponse.getEndTime().getTime()
                        && cal.getTime().getTime() >= resourceResponse.getEndTime().getTime()) {
                    resourceResponse.setStatus("即将到期");
                }
            }
            if("available".equalsIgnoreCase(resourceResponse.getStatus())||"normal".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("正常");
            }
            if("arrears".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("错误");
            }
            if("pending".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("申请中");
            }
            if("expired".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已到期");
            }
            if("deleting".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("删除中");
            }
            if("deleted".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已删除");
            }
            if("renewing".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("续订中");
            }
            if("unsubscribed".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已退订");
            }
            if("unsubscribing".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("退订中");
            }
            if("frozen".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已冻结");
            }
            if ("upgrading".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("扩容中");
            }
            //计费模式
            if("prepaid".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType(ChargeTypeEnum.PrePaid.getDesc());
            }
            if("postpaid".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType(ChargeTypeEnum.PostPaid.getDesc());
            }
            if("none".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType(ChargeTypeEnum.None.getDesc());
            }
            //时间设置
            if(!StringUtil.isNullOrEmpty(resourceResponse.getStartTime())){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = format.format(resourceResponse.getStartTime());
                try {
                    resourceResponse.setStartTime(format.parse(str));
                } catch (ParseException e) {
                    log.error(e.getMessage());
                }
            }
            if(!StringUtil.isNullOrEmpty(resourceResponse.getEndTime())){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String str = format.format(resourceResponse.getEndTime());
                try {
                    resourceResponse.setEndTime(format.parse(str));
                } catch (ParseException e) {
                    log.error(e.getMessage());
                }
            }

        }
        return responsePage;
    }

    @Override
    public BaseGridReturn exportHpcClusterInstances(DescribeProductResourceRequest request) {
        String status = request.getStatus();
        String chargeType = request.getChargeType();
        String accountNameLike = request.getAccountNameLike();
        String orderSn = request.getOrderSn();
        String distributorName = request.getDistributorName();
        String productNameLike = request.getProductNameLike();
        String productType;
        if (StringUtils.equalsIgnoreCase("HPC", request.getProductType())) {
            productType = "SAASShare";
        } else if (StringUtils.equalsIgnoreCase("HPC-DRP", request.getProductType())) {
            productType = "SAASPrivate";
        } else {
            productType = request.getProductType();
        }
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setFixedSorting(Boolean.FALSE);
        Long pagenum = request.getPagenum();
        Long pagesize = request.getPagesize();
        queryResHpcClusterRequest.setPagenum(pagenum != null ? String.valueOf(pagenum-1): null);
        queryResHpcClusterRequest.setPagesize(pagesize != null ? pagesize.toString() : null);
        if (StringUtils.isNotEmpty(chargeType)) {
            queryResHpcClusterRequest.setChargeType(chargeType);
        }
        if (StringUtils.isNotEmpty(status)) {
            queryResHpcClusterRequest.setStatus(status);
        }
        if(StringUtils.isNotEmpty(productNameLike)){
            queryResHpcClusterRequest.setClusterName(productNameLike);
        }
        if (StringUtils.isNotEmpty(productType)) {
            queryResHpcClusterRequest.setClusterType(productType);
        }
        BaseGridReturn baseGridReturn = new BaseGridReturn();
        RestResult clusterResult = null;
        if (!StringUtil.isNullOrEmpty(accountNameLike)) {
            //查询申请客户
            // Bug:29857 修改为客户管理，客户信息
            List<User> userList = userMapper.selectAllUserByAccountNameLike(accountNameLike);
            List<Long> userSidList = userList.stream().map(User::getUserSid).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(userList)) {
                queryResHpcClusterRequest.setOwnerIdList(userSidList);
                clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
            }

        } else if (!StringUtil.isNullOrEmpty(orderSn)) {
            //查询订单编号
            QueryWrapper<ServiceOrder> tWrapper = new QueryWrapper<>();
            tWrapper.like("order_sn", orderSn);
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(tWrapper);
            List<Long> orderIds = serviceOrders.stream().map(ServiceOrder::getId).collect(Collectors.toList());
            if(orderIds.size()>0){
                QueryWrapper<SfProductResource> sfProductResourceQueryWrapper = new QueryWrapper<>();
                List<Long> clusterIdList = sfProductResourceMapper.selectList(sfProductResourceQueryWrapper.in("service_order_id", orderIds))
                        .stream().map(SfProductResource::getClusterId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(clusterIdList)) {
                    queryResHpcClusterRequest.setClusterIdList(clusterIdList);
                    clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
                }
            }
        } else if (!StringUtil.isNullOrEmpty(distributorName)) {   //查询所属分销商
            List<Long> orgSids = new ArrayList<>();
            //获取所有直营的org集合
            List<Long> directlyOrgList = Lists.newArrayList();
            List<BizBillingAccount> bizBillingAccountList = bizBillingAccountMapper.selectAllDirectlyAccount();
            bizBillingAccountList.forEach(e -> {
                directlyOrgList.add(e.getOrgSid());
                List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e.getOrgSid());
                if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                    directlyOrgList.addAll(childrenOrgIds);
                }
            });
            if ("直营".equals(distributorName) || "直".equals(distributorName) || "营".equals(distributorName)) {
                orgSids.addAll(directlyOrgList);
            } else {
                orgService.selectOrgIdListByOrgNameLike(distributorName).forEach(e -> {
                    orgSids.add(e);
                    List<Long> childrenOrgIds = orgService.selectChildrenOrgIds(e);
                    if (CollectionUtil.isNotEmpty(childrenOrgIds)) {
                        orgSids.addAll(childrenOrgIds);
                    }
                });
                log.info("orgSids————>{}",orgSids);
                //移除直营的Org集合
                orgSids.removeAll(directlyOrgList);
            }
            if (CollectionUtil.isNotEmpty(orgSids)) {
                log.info("orgSids————>{}",orgSids);
                queryResHpcClusterRequest.setOrgSidList(orgSids);
                clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
            }
        } else {
            clusterResult = hpcClusterService.getResHpcCluster(queryResHpcClusterRequest);
        }
        List<ResHpcClusterInstancesResponse> instancesResponses = new ArrayList<>();
        if (clusterResult != null && clusterResult.getStatus()) {
            Object data = clusterResult.getData();
            baseGridReturn = BeanConvertUtil.convert(data, BaseGridReturn.class);
            List list = baseGridReturn.getDataList();
            List<ProductResourceHPCResponse> hpcResponses = BeanConvertUtil.convert(list, ProductResourceHPCResponse.class);
            Map<Long, ProductResourceHPCResponse> hpcResponseMap = hpcResponses.stream()
                    .collect(Collectors.toMap(ProductResourceHPCResponse::getClusterId, prh -> prh));
            Set<Long> clusterIdSet = hpcResponseMap.keySet();
            if (CollectionUtil.isEmpty(clusterIdSet)) {
                baseGridReturn.setDataList(instancesResponses);
                return baseGridReturn;
            }
            QueryWrapper<SfProductResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("cluster_id", clusterIdSet);
            queryWrapper.orderByDesc( "id");
            List<SfProductResource> sfProductResources = this.baseMapper.selectList(queryWrapper);
            Map<Long, SfProductResource> orderIdProductMap = sfProductResources.stream()
                    .filter(sfP->sfP.getServiceOrderId() !=null).collect(Collectors.toMap(SfProductResource::getServiceOrderId, sfp -> sfp));
            Set<Long> orderIdSet = orderIdProductMap.keySet();
            Map<Long, SfProductResource> clusterIdProductMap = sfProductResources.stream()
                    .filter(Objects::nonNull).collect(Collectors.toMap(SfProductResource::getClusterId, sfp -> sfp,(oldValue,newValue)->newValue));

            QueryWrapper<ServiceOrder> tWrapper = new QueryWrapper<>();

            tWrapper.in("cluster_id",clusterIdSet);
            if (CollectionUtil.isNotEmpty(orderIdSet)) {
                tWrapper.or().in("id", orderIdSet);
            }
            tWrapper.orderByDesc("created_dt");
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(tWrapper);
            Map<String, ServiceOrder> orderMap = new HashMap<>();
            Map<String, ServiceOrder> orderClusterMap = new HashMap<>();
            for (ServiceOrder serviceOrder : serviceOrders) {
                orderMap.put(serviceOrder.getId().toString(),serviceOrder);
                if (Objects.nonNull(serviceOrder.getClusterId())) {
                    orderClusterMap.put(serviceOrder.getClusterId().toString(), serviceOrder);
                }
            }
            hpcResponses.stream().forEach(resourceHPCResponse -> {

                ResHpcClusterInstancesResponse instResp = new ResHpcClusterInstancesResponse();
                instancesResponses.add(instResp);
                Long clusterId = resourceHPCResponse.getClusterId();

                SfProductResource sfp = clusterIdProductMap.get(clusterId);
                Long serviceOrderId;
                if (Objects.nonNull(sfp)) {
                    serviceOrderId = sfp.getServiceOrderId();
                } else {
                    ServiceOrder order = orderClusterMap.get(clusterId.toString());
                    if (Objects.nonNull(order)) {
                        serviceOrderId = order.getId();
                    } else {
                        serviceOrderId = null;
                    }

                }
                if (sfp == null) {
                    instResp.setOrgSid(resourceHPCResponse.getOrgSid());
                    instResp.setStatus(resourceHPCResponse.getStatus());
                    instResp.setChargeType(resourceHPCResponse.getChargeType());
                    instResp.setClusterId(clusterId);
                    instResp.setName(resourceHPCResponse.getClusterName());
                }else{
                    BeanUtils.copyProperties(sfp,instResp);
                    instResp.setName(resourceHPCResponse.getClusterName());
                }

                if(serviceOrderId ==null){
                    return;
                }
                ServiceOrder serviceOrder = orderMap.get(serviceOrderId.toString());
                if (serviceOrder == null) {
                    return;
                }
                List<ServiceOrderDetail> serviceOrderDetails = iServiceOrderPriceDetailService.selectOrderDetailByCriteria(new Criteria("orderId",serviceOrderId));
                Optional<ServiceOrderDetail> detailOptional = serviceOrderDetails.stream().filter(detail -> ProductCodeEnum.HPC.getProductCode().equals(detail.getServiceType())
                        || ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())).findFirst();
                if(!detailOptional.isPresent()){
                    return;
                }

                ServiceOrderDetail serviceOrderDetail = detailOptional.get();
                instResp.setProductName(serviceOrder.getProductName());
                instResp.setProductType(serviceOrderDetail.getServiceType());
                instResp.setClusterId(clusterId);
                instResp.setChargeType(serviceOrderDetail.getChargeType());
                iServiceOrderPriceDetailService.setProductConfigDescNodeInfo(serviceOrderDetail);
                instResp.setConfigDesc(serviceOrderDetail.getServiceConfig());
                instResp.setQuantity(serviceOrderDetail.getQuantity());
                if (serviceOrder.getContractId() != null) {
                    BizContract bizContract = bizContractMapper.selectById(serviceOrder.getContractId());
                    instResp.setContractName(bizContract.getContractTitle());
                    instResp.setContractFile(bizContract.getContractFile());
                    instResp.setContractFileName(bizContract.getContractFileName());
                }
                instResp.setApplyOrderSn(String.valueOf(serviceOrderDetail.getOrderId()));
                instResp.setOrderSn(serviceOrder.getOrderSn());
                User user = userMapper.selectByPrimaryKey(Long.valueOf(serviceOrder.getOwnerId()));
                Org org = orgService.getById(serviceOrder.getOrgSid());
                instResp.setOrgName(Objects.isNull(org) ? "" : org.getOrgName());
                instResp.setBehalfUserSid(serviceOrder.getBehalfUserSid());
                instResp.setContractId(serviceOrder.getContractId() == null ? null : serviceOrder.getContractId().toString());

                instResp.setSettlementType(serviceOrder.getSettlementType());
                instResp.setName(serviceOrder.getName());

                instResp.setOriginalCost(serviceOrder.getOriginalCost());
                instResp.setFinalCost(serviceOrder.getFinalCost());
                instResp.setStartTime(serviceOrderDetail.getStartTime());
                User authUser = AuthUtil.getAuthUser();
                if (Objects.nonNull(user)) {

                    instResp.setDistributorName("直营");
                    BizBillingAccount billingAccount = bizBillingAccountMapper.selectByOrgIdAndEntityId(user.getCompanyId(),authUser.getEntityId());
                    if (!Objects.isNull(billingAccount) && !StringUtil.isNullOrEmpty(billingAccount.getDistributorName())) {
                        instResp.setDistributorName(billingAccount.getDistributorName());
                    }
                    if (!Objects.isNull(billingAccount)) {
                        instResp.setAccountName(billingAccount.getAccountName());
                    }
                }

                if (ChargeTypeEnum.PostPaid.getType().equals(instResp.getChargeType())) {
                    instResp.setEndTime(null);
                }

                //查询如果是已退订的共享资源池，则添加结束时间
                if (ProductCodeEnum.HPC.getProductCode().equals(instResp.getProductType()) &&
                        SfProductEnum.UNSUBSCRIBED.getStatus().equals(resourceHPCResponse.getStatus())){
                    instResp.setEndTime(resourceHPCResponse.getEndTime());
                }


                ProductResourceHPCResponse response = hpcResponseMap.get(clusterId);
                if(response !=null){
                    instResp.setStatus(response.getStatus());
                }

            });

        }
        //系统配置表中获取具体的到期时间提醒
        int days= Integer.parseInt(PropertiesUtil.getProperty("upcoming_expired_days"));
        Date date = new Date();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        for (ResHpcClusterInstancesResponse resourceResponse : instancesResponses){
            if("available".equalsIgnoreCase(resourceResponse.getStatus()) && !StringUtil.isNullOrEmpty(resourceResponse.getEndTime())){
                //时间差
                long day = (resourceResponse.getEndTime().getTime()-date.getTime()) / (1000 * 60 * 60 * 24);
                if (0<day && day<=days){
                    resourceResponse.setStatus("即将到期");
                }
            }
            if("available".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("运行中");
            }
            if("apply".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("申请中");
            }
            if("configing".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("配置中");
            }
            if("expired".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已过期");
            }
            if("frozen".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已冻结");
            }
            if("rejected".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已拒绝");
            }
            if("unsubscribing".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("退订中");
            }
            if("unsubscribed".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已退订");
            }
            if("deleting".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("删除中");
            }
            if("deleted".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("已删除");
            }
            if("configed".equalsIgnoreCase(resourceResponse.getStatus())){
                resourceResponse.setStatus("配置完成");
            }
            //计费模式
            if("prepaid".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType("包年包月");
            }
            if("postpaid".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType("按量计费");
            }
            if("none".equalsIgnoreCase(resourceResponse.getChargeType())){
                resourceResponse.setChargeType("不计费");
            }
            if (Objects.isNull(resourceResponse.getEndTime())){
                resourceResponse.setEndTimeForExcel("--");
            }else {
                resourceResponse.setEndTimeForExcel(simpleDateFormat.format(resourceResponse.getEndTime()));
            }
        }
        baseGridReturn.setDataList(instancesResponses);
        return baseGridReturn;
    }


    @Override
    public Boolean checkMaName(String clusterName) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        Criteria criteria = new Criteria();
        criteria.put("created_by", authUser.getAccount());
        criteria.put("product_type", ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
        // 过滤掉已退订资源
        criteria.put("flag","1");
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);

        if (CollectionUtils.isEmpty(sfProductResources)) {
            return false;
        }

        List<Long> clusterIdList = sfProductResources.stream()
                                                     .map(SfProductResource::getClusterId)
                                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clusterIdList)) {
            return false;
        }
        //统计申请单和退订单 条数，如果相等则资源已经退订。
        Map<String, Long> typeCountMap = serviceOrderService.lambdaQuery()
                                                            .in(ServiceOrder::getClusterId, clusterIdList)
                                                            .in(ServiceOrder::getType,
                                                                Arrays.asList(OrderType.APPLY, OrderType.RELEASE))
                                                            .in(ServiceOrder::getStatus,
                                                                Arrays.asList(OrderStatus.COMPLETED,
                                                                              OrderStatus.PENDING))
                                                            .eq(ServiceOrder::getName, clusterName)
                                                            .list()
                                                            .stream()
                                                            .filter(order -> {
                                                                QueryWrapper<ServiceOrderDetail> queryWrapper = new QueryWrapper<>();
                                                                queryWrapper.lambda()
                                                                            .eq(ServiceOrderDetail::getOrderId,
                                                                                order.getId())
                                                                            .eq(ServiceOrderDetail::getServiceType,
                                                                                ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
                                                                return serviceOrderDetailMapper.selectCount(
                                                                        queryWrapper) > 0;
                                                            })
                                                            .collect(Collectors.groupingBy(ServiceOrder::getType,
                                                                                           Collectors.counting()));
        Long applyCount = typeCountMap.get(OrderType.APPLY);
        if (applyCount == null) {
            return false;
        }
        Long releaseCount = Optional.ofNullable(typeCountMap.get(OrderType.RELEASE)).orElse(0L);
        if (applyCount.equals(releaseCount)) {
            return false;
        }
        return true;
    }

    @Override
    public ResHpcClusterDetailVO getHpcClusterDetailByClusterId(Long orgSid, Long clusterId, Long userSid) {
        User user = userMapper.selectByPrimaryKey(userSid);
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_SEARCH_STATUS);
        queryResHpcClusterRequest.setOrgSid(orgSid);
        List<Long> cluIdList = policyAssertionService.findResourceRelationByUser("hpc", user.getUserSid(),
                                                                                 orgSid,
                                                                                 (Function<ResHpcClusterVO, Long>) (a) -> a
                                                                                         .getId(),
                                                                                 queryResHpcClusterRequest);

        RestResult restResult;
        if (!CollectionUtil.isEmpty(cluIdList) && cluIdList.contains(clusterId)) {
            restResult = hpcClusterService.resHpcClusterDetail(orgSid, clusterId);
        } else {
            restResult = null;
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1982535027));
        }

        if (restResult.getStatus()) {
            Object data = restResult.getData();
            ResHpcClusterDetailVO detailVO = BeanConvertUtil.convert(data, ResHpcClusterDetailVO.class);
            if (detailVO != null) {
                Integer vncNodeNum = detailVO.getVNCNodeNum();
                if (Objects.isNull(vncNodeNum) || vncNodeNum < 0) {
                    detailVO.setVNCNodeNum(0);
                }
                ResHpcClusterDetailDTO detailDTO = BeanConvertUtil.convert(data, ResHpcClusterDetailDTO.class);
                List<NodeInfo> nodeList = detailVO.getNodeList();
                List<Code> nodeTypeList = codeMapper.selectByCodeCategory("NODE_TYPE");
                Map<String, String> nodeTypeMap = nodeTypeList.stream()
                                                              .collect(Collectors.toMap(Code::getCodeValue,
                                                                                        Code::getCodeDisplay));
                List<ResBmsDTO> bmsDTOList = detailDTO.getBmsDTOList();
                bmsDTOList.forEach(dto -> {
                    NodeInfo nodeInfo = BeanConvertUtil.convert(dto, NodeInfo.class);
                    nodeInfo.setNodeType(nodeTypeMap.get(dto.getNodeType()));
                    nodeInfo.setName(assembleNodeName(dto));
                    nodeList.add(nodeInfo);
                });
                List<ResEcsDTO> ecsDTOList = detailDTO.getEcsDTOList();
                ecsDTOList.forEach(dto -> {
                    NodeInfo nodeInfo = BeanConvertUtil.convert(dto, NodeInfo.class);
                    nodeInfo.setNodeType(dto.getNodeType());
                    nodeInfo.setName(assembleNodeName(dto));
                    nodeList.add(nodeInfo);
                });
                if (clusterId != null) {
                    Criteria criteria = new Criteria();
                    criteria.put("cluster_id", clusterId);
                    criteria.put("productTypeIn", Arrays.asList(ProductCodeEnum.HPC.getProductCode(),
                                                                ProductCodeEnum.HPC_DRP.getProductCode()));
                    List<SfProductResource> sfProductResources = this.baseMapper.selectByParams(criteria);
                    if (CollectionUtil.isNotEmpty(sfProductResources)) {
                        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);
                        detailVO.setId(sfProductResource.getId());
                        detailVO.setProductType(sfProductResource.getProductType());
                        detailVO.setProductName(sfProductResource.getProductName());
                        if (StringUtils.isEmpty(detailVO.getPoolName())) {
                            detailVO.setPoolName(detailVO.getClusterName());
                            detailVO.setName(detailVO.getClusterName());
                        } else {
                            detailVO.setName(detailVO.getPoolName());
                        }
                        detailVO.setStartTime(sfProductResource.getStartTime());
                        detailVO.setEndTime(sfProductResource.getEndTime());
                        detailVO.setFrozenTime(sfProductResource.getFrozenTime());
                        if (ScenarioEnum.getByCode(detailVO.getScenario()) != null) {
                            detailVO.setScenario(ScenarioEnum.getByCode(detailVO.getScenario()).getMessage());
                        } else {
                            detailVO.setScenario(ScenarioEnum.Unpaired.getMessage());
                        }

                        if (ProductCodeEnum.HPC_DRP.getProductCode().equals(sfProductResource.getProductType())) {
                            return detailVO;
                        }
                        List<PriceVO> priceList = detailVO.getPriceList();
                        Long serviceOrderId = sfProductResource.getServiceOrderId();
                        if (serviceOrderId != null) {
                            QueryWrapper<ServiceOrderDetail> tWrapper = new QueryWrapper<>();
                            tWrapper.eq("order_id", serviceOrderId);
                            List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectList(
                                    tWrapper);
                            if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {
                                //查询规格
                                QueryWrapper<BizBillingSpec> tWrapper1 = new QueryWrapper<>();
                                List<BizBillingSpec> bizBillingSpecs = bizBillingSpecMapper.selectList(tWrapper1);
                                Map<String, BizBillingSpec> specMap = bizBillingSpecs.stream()
                                                                                     .collect(Collectors.toMap(
                                                                                             spec -> spec.getCategory()
                                                                                                     + "."
                                                                                                     + spec.getSpecName(),
                                                                                             spec -> spec));
                                //查询类型
                                List<Code> strategyTypeList = codeMapper.selectByCodeCategory(BILLING_STRATEGY_TYPE);

                                //存储容量、使用量
                                List<HPCResponseShareVO> shareList = detailDTO.getShareList();
                                //公共目录大小
                                Long publicSize = 0L;
                                //专属文件系统大小
                                Long size = 0L;
                                //使用量（超出用量部分）
                                BigDecimal usedSize = BigDecimal.ZERO;
                                if (CollectionUtil.isNotEmpty(shareList)) {
                                    for (HPCResponseShareVO hpcResponseShareVO : shareList) {
                                        Long shareVOSize =
                                                hpcResponseShareVO.getSize() == null ? 0 : hpcResponseShareVO.getSize();
                                        if (Boolean.TRUE.equals(hpcResponseShareVO.getIsVirtual())) {
                                            publicSize = publicSize + shareVOSize;
                                        } else {
                                            size = size + shareVOSize;

                                            BigDecimal shareVOUsedSize = hpcResponseShareVO.getUsedSize();
                                            BigDecimal subSize = NumberUtil.sub(
                                                    shareVOUsedSize == null ? BigDecimal.ZERO : shareVOUsedSize,
                                                    shareVOSize);
                                            if (NumberUtil.isGreater(subSize, BigDecimal.ZERO)) {
                                                usedSize = NumberUtil.add(subSize, usedSize);
                                            }
                                        }

                                    }
                                }
                                if (CollectionUtil.isNotEmpty(serviceOrderDetails)) {
                                    //shareList无数据取订单中的容量
                                    if (CollectionUtil.isEmpty(shareList)) {

                                        for (ServiceOrderDetail ordeDetail : serviceOrderDetails) {
                                            String serviceConfig = ordeDetail.getServiceConfig();
                                            ServiceConfig config = JSONObject.parseObject(serviceConfig,
                                                                                          ServiceConfig.class);
                                            DataDTO configData = config.getData();
                                            HpcDTO hpc = configData.realHpcDTO();
                                            if (hpc != null) {
                                                String hpcVersion = hpc.getHpcVersion();
                                                if ("3".equals(hpcVersion) && hpc.getSize() != null) {
                                                    publicSize = Long.valueOf(hpc.getSize());
                                                }
                                            }
                                            SfsDTO sfs = configData.realSfsDTO();
                                            if (sfs != null && sfs.getSize() != null) {
                                                size = Long.valueOf(sfs.getSize());
                                            }
                                        }
                                    }

                                    for (ServiceOrderDetail ordeDetail : serviceOrderDetails) {
                                        String serviceConfig = ordeDetail.getServiceConfig();
                                        addPriceVo(priceList, serviceConfig, serviceOrderId, specMap, strategyTypeList,
                                                   size, publicSize, usedSize);
                                    }
                                }
                            }

                        }
                    }
                }
            }
            return detailVO;

        } else {
            log.info("call hpcClusterService.resHpcClusterDetail:{}", restResult);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult<BaseGridReturn> editFreezingStrategy(EditFreezingStrategyRequest request) {
        SfProductResource resource = sfProductResourceMapper.selectById(request.getId());
        AuthUser authUser = AuthUserHolder.getAuthUser();
        ExampleFreezeStrategyRecord record = new ExampleFreezeStrategyRecord();
        record.setHandleUserId(authUser.getUserSid());
        record.setHandleUserAccount(authUser.getAccount());
        record.setResourceId(resource.getId());
        record.setProductType(resource.getProductType());
        record.setPrevStrategy(resource.getFreezingStrategy());
        record.setPrevBufferPeriod(resource.getStrategyBufferPeriod());
        if (StringUtils.equals(FreezingStrategyEnum.RELEASE_RESOURCE.getCode(), request.getFreezingStrategy())) {
            if (Objects.isNull(request.getStrategyBufferPeriod())) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if (StringUtils.equals(FreezingStrategyEnum.RESERVED_RESOURCE.getCode(), resource.getFreezingStrategy())) {
                BssTypeEnum bssType;
                if (StringUtils.equals(resource.getProductType(), ProductCodeEnum.HPC_DRP.getProductCode())) {
                    bssType = BssTypeEnum.HPC_DRP_RESOURCE_EXPIRE;
                } else if (StringUtils.equals(resource.getProductType(), ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode())) {
                    bssType = BssTypeEnum.DRP_RESOURCE_EXPIRE;
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_NOT_SUPPORT));
                }
                SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigBssMapper.selectEnableByBssType(bssType.getValue());
                if (Objects.nonNull(sysMNotifyConfig)) {
                    if (StringUtils.equals(sysMNotifyConfig.getExpireStrategy(), ExpireStrategyEnum.NONE.getValue())) {
                        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.FREEZING_STRATEGY_MSG_001));
                    }
                }
            }
            resource.setStrategyBufferPeriod(request.getStrategyBufferPeriod());
        } else {
            resource.setStrategyBufferPeriod(null);
        }
        resource.setFreezingStrategy(request.getFreezingStrategy());
        sfProductResourceMapper.updateByPrimaryKeySelective(resource);
        record.setCurrentStrategy(resource.getFreezingStrategy());
        record.setCurrentBufferPeriod(resource.getStrategyBufferPeriod());
        exampleFreezeStrategyRecordService.insert(record);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    @Override
    public RestResult stopJob(StopJobRequest request) {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        SfProductResource resource = sfProductResourceMapper.selectById(request.getId());
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(resource.getClusterId());
        CloudEnvParams params = new CloudEnvParams();
        params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
        if (org.springframework.util.CollectionUtils.isEmpty(cloudEnvs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
        }
        Org org = orgService.selectRootOrg(resource.getOrgSid());
        LambdaQueryWrapper<cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser> qw = new LambdaQueryWrapper<>();
        qw.eq(cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser::getOrgSid, org.getOrgSid());
        qw.isNull(cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser::getParentSid);
        cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser owner = userMapper.selectOne(qw);
        RestResult result = new RestResult();
        AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(owner.getUserSid()),
                                                        AuthUser.class);
        AuthUserHolder.setAuthUser(authUserInfo);
        //停止资源池作业
        String rootUrl = "";
        if (StringUtils.isNotBlank(resHpcCluster.getCcpInternelAddress())) {
            try {
                URL url = new URL(ActiveDirectory.getCcpInternalAddr(resHpcCluster.getCcpInternelAddress()));
                rootUrl = String.format("%s://%s", url.getProtocol(), url.getAuthority());
            } catch (MalformedURLException e) {
                log.error("解析url异常：[{}]", e.getMessage());
                result.setStatus(RestResult.Status.FAILURE);
                result.setMessage(WebUtil.getMessage(MsgCd.SYSTEM_ERROR));
                return result;
            }
        }
        HPCShareStopJob hpcShareStopJob = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), HPCShareStopJob.class);
        RestResult resHpcNameAndPass = hpcClusterService.getResHpcNameAndPass(resHpcCluster.getPoolUuid());
        if (Objects.isNull(resHpcNameAndPass.getData())){
            log.error("stopJob==共享集群绑定异常：[{}]", resHpcCluster.getPoolUuid());
            result.setStatus(RestResult.Status.FAILURE);
            result.setMessage(WebUtil.getMessage(MsgCd.SYSTEM_ERROR));
            return result;
        }
        ResHpcClusterPool pool = BeanConvertUtil.convert(resHpcNameAndPass.getData(), ResHpcClusterPool.class);
        hpcShareStopJob.setTenantUserName(CrytoUtilSimple.decrypt(pool.getUsername()));
        hpcShareStopJob.setTenantUserPass(CrytoUtilSimple.decrypt(pool.getPassword()));
        hpcShareStopJob.setOptions(MapsKit.of(ROOT_URL, rootUrl));
        hpcShareStopJob.setAccount(org.getLdapOu());
        HPCShareStopJobResult stopJobResult;
        try {
            stopJobResult = (HPCShareStopJobResult) MQHelper.rpc(hpcShareStopJob);
        } catch (MQException e) {
            stopJobResult = new HPCShareStopJobResult();
            log.error("stopJob==MQ异常：", e);
            stopJobResult.setSuccess(false);
            stopJobResult.setMessage(WebUtil.getMessage(MsgCd.SYSTEM_ERROR));
        }
        ServiceOrder serviceOrder = serviceOrderMapper.selectById(resource.getServiceOrderId());
        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("userAccount", owner.getAccount());
        if (stopJobResult.isSuccess() && StringUtils.equals(stopJobResult.getCode(), SUCCESS)) {
            result.setStatus(RestResult.Status.SUCCESS);
            result.setMessage(WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
            if (Objects.nonNull(stopJobResult.getData()) && CollectionUtil.isNotEmpty(stopJobResult.getData().getSuccessData())) {
                //给租户发消息
                String msgId = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_SHARE_STOP_JOB;
                messageContent.put("productName", resource.getProductName());
                messageContent.put("poolName", resource.getProductName());
                if (request.getAutoStop()) {
                    msgId = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_SHARE_AUTO_STOP_JOB;
                    messageContent.put("productName", resource.getProductName());
                    messageContent.put("minFrozenAmount", request.getMinFrozenAmount());
                } else {
                    messageContent.put("poolName", resource.getProductName());
                    if(EXPIRE_TYPE.equals(request.getExpireType())){
                        messageContent.put("adminAccount", "系统");
                    } else{
                        messageContent.put("adminAccount", authUser.getAccount());
                    }
                }
                BaseNotificationMqBean userBaseNotificationMqBean = new BaseNotificationMqBean();
                userBaseNotificationMqBean.setMsgId(msgId);
                userBaseNotificationMqBean.setMap(messageContent);
                userBaseNotificationMqBean.getImsgUserIds().add(resHpcCluster.getOwnerId());
                userBaseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                              userBaseNotificationMqBean);
            }
        } else {
            result.setStatus(RestResult.Status.FAILURE);
            result.setMessage(stopJobResult.getMessage());
            if (Objects.nonNull(stopJobResult.getData()) && CollectionUtil.isNotEmpty(stopJobResult.getData().getErrorData())) {
                result.setMessage(JSON.toJSONString(stopJobResult.getData().getErrorData()));
            }
            log.error("stopJob-共享资源池停止作业失败：[{}]", result.getMessage());
            // 给管理员发消息
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> adminstrators = userMapper.findAdminstrators();
            messageContent.put("poolName", resource.getProductName());
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(
                    NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_SHARE_STOP_JOB_FAIL);
            baseNotificationMqBean.setMap(messageContent);
            LinkedHashSet<Long> toUserIds = new LinkedHashSet<>();
            if (EXPIRE_TYPE.equals(request.getExpireType())){
                toUserIds.addAll(adminstrators.stream()
                                              .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid)
                                              .collect(Collectors.toSet()));
            } else{
                toUserIds.add(authUser.getUserSid());
            }

            baseNotificationMqBean.setImsgUserIds(toUserIds);
            baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                          baseNotificationMqBean);
        }
        return result;
    }



    /**
     * 获取实时价格
     *
     * @param billingPricesDTOList
     * @param dataDTO
     * @param serviceOrderId
     */
    private List<BillingPricesDTO> getBillingPrices(List<BillingPricesDTO> billingPricesDTOList, DataDTO dataDTO,
                                                    Long serviceOrderId, String serviceConfig) {

        if (StringUtils.equals(CommonPropertyKeyEnum.ONE.getCode()
                , cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getPropertyAndCache(
                        SysConfigConstants.BILL_PREPAID_FROM_INQUIRYPRICE
                        , CommonPropertyKeyEnum.ZERO.getCode(), 30))) {

            try {
                JSONObject serviceConfigJOB = JSONObject.parseObject(serviceConfig);
                Long cloudEnvId = serviceConfigJOB.getLong("cloudEnvId");

                ServiceOrder serviceOrder =
                        serviceOrderMapper.selectOne(
                                new QueryWrapper<ServiceOrder>().lambda().eq(ServiceOrder::getId, serviceOrderId));

                String productCode = serviceConfigJOB.getString("productCode");

                ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectOne(
                        new QueryWrapper<ServiceOrderDetail>().lambda()
                                                              .eq(ServiceOrderDetail::getOrderId, serviceOrderId)
                                                              .eq(ServiceOrderDetail::getServiceType, productCode));

                //设置询价请求
                ApplyServiceVO applyServiceRequest = new ApplyServiceVO();

                applyServiceRequest.setCouponId(null);
                applyServiceRequest.setProjectId(serviceOrder.getOrgSid());
                applyServiceRequest.setOrderType("apply-other_service");
                List<ProductInfoVO> productInfoVOList = new ArrayList<>();
                ProductInfoVO productInfoVO = new ProductInfoVO();
                productInfoVOList.add(productInfoVO);
                productInfoVO.setBillingAccountId(serviceOrder.getBizBillingAccountId());
                productInfoVO.setAmount(1);
                productInfoVO.setChargeType(
                        cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum.POSTPAID.getValue());
                productInfoVO.setServiceId(serviceOrderDetail.getServiceId());
                productInfoVO.setOrderId(serviceOrder.getId().toString());
                productInfoVO.setPeriod(BigDecimal.valueOf(1));
                productInfoVO.setPeriodUnit(HOUR);
                productInfoVO.setCloudEnvId(cloudEnvId);
                productInfoVO.setProductCode(productCode);
                applyServiceRequest.setProductInfo(productInfoVOList);
                applyServiceRequest.setEntityId(serviceOrder.getEntityId());
                applyServiceRequest.setAccountId(serviceOrder.getBizBillingAccountId());
                if (StringUtils.isNotEmpty(serviceOrder.getOwnerId())) {
                    applyServiceRequest.setUserSid(Long.valueOf(serviceOrder.getOwnerId()));
                }
                //设置订单价格为NUll
                dataDTO.setBillingPrices(null);
                applyServiceRequest.getProductInfo().get(0).setData(dataDTO);
                log.debug("SfProductResourceServiceImpl.getBillingPrices= {}", JSON.toJSONString(applyServiceRequest));
                List<InquiryPriceResponse> priceResponses = inquiryPriceService.inquiryProductPrice(
                        applyServiceRequest);
                InquiryPriceResponse priceResponse = CollectionUtil.getFirst(priceResponses);
                List<BizBillingPriceVO> billingPrices = priceResponse.getBillingPrices();
                billingPricesDTOList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(billingPrices)) {
                    for (BizBillingPriceVO billingPriceVO : billingPrices) {
                        BillingPricesDTO pricesDTO = BeanConvertUtil.convert(billingPriceVO, BillingPricesDTO.class);
                        //价格转换为字符串
                        pricesDTO.setFixedHourPrice(this.getPriceStr(billingPriceVO.getFixedHourPrice()));
                        pricesDTO.setUnitHourPrice(this.getPriceStr(billingPriceVO.getUnitHourPrice()));
                        pricesDTO.setTradeFixedHourPrice(this.getPriceStr(billingPriceVO.getTradeFixedHourPrice()));
                        pricesDTO.setTradeUnitHourPrice(this.getPriceStr(billingPriceVO.getTradeUnitHourPrice()));
                        billingPricesDTOList.add(pricesDTO);
                    }
                }
            } catch (Exception e) {
                log.error("SfProductResourceServiceImpl.getBillingPrices Exception:", e);
            }
        }
        return billingPricesDTOList;
    }

    private String getPriceStr(BigDecimal price) {
        String priceStr = "0";
        if (price != null && NumberUtil.isGreater(price, BigDecimal.ZERO)) {
            priceStr = price.setScale(5, BigDecimal.ROUND_DOWN).toString();
        }
        return priceStr;
    }

}
