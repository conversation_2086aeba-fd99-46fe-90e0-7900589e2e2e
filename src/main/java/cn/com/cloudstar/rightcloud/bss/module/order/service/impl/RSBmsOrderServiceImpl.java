package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import brave.Tracing;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.CouponService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;

/**
 * <p>
 * 裸金属服务器开通
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
@Service
@Slf4j
public class RSBmsOrderServiceImpl extends AbstractOrderService {


    @Autowired
    private OrderService orderService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private CouponService couponService;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private Tracing tracing;
    @DubboReference
    private ResVmRemoteService resVmRemoteService;
    @Autowired
    private BizInquiryPriceService bizInquiryPriceService;

    /**
     * 分布式链路跟踪，缓存traceId
     */
    private static final String TRACE_ID_KEY = "traceIdKey:";

    @Override
    public String apply(ApplyServiceVO serviceVO) {
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
        Integer amount = productInfoVO.getAmount();

        this.tagProductInfo(serviceVO.getProductInfo(), 0L);
        if (amount > 1 && ProductCodeEnum.RS_BMS.getProductType().equals(productInfoVO.getProductCode())) {
            productInfoVO.setAmount(1);
            List<ProductInfoVO> productInfoVOList = new ArrayList<>();
            for (int i = 1; i < amount; i++) {
                List<ProductInfoVO> convert = BeanConvertUtil.convert(serviceVO.getProductInfo(), ProductInfoVO.class);
                this.tagProductInfo(convert, (long) i);
                productInfoVOList.addAll(convert);
            }
            serviceVO.getProductInfo().addAll(productInfoVOList);
        }
        ApplyEntity applyEntity = before();

        validateSpec(serviceVO);

        execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);
        //保存资源ID.用作计费
        List<ServiceOrderResourceRef> resourceRef = new ArrayList<>();
        for (ServiceOrderDetail orderDetail : applyEntity.getOrderDetails()) {
            List<ServiceOrderResourceRef> orderResourceRefListByDetailId =
                    serviceOrderResourceRefService.getOrderResourceRefListByDetailId(orderDetail.getId());
            if (CollectionUtils.isNotEmpty(orderResourceRefListByDetailId)) {
                resourceRef.addAll(orderResourceRefListByDetailId);
            }
        }
        if (Objects.nonNull(resourceRef)) {
            List<String> resourceIds = resourceRef.stream().map(ServiceOrderResourceRef::getResourceId).collect(Collectors.toList());
            applyEntity.setResourceId(resourceIds);
        }
        after(applyEntity);
        return result;
    }

    private void tagProductInfo(List<ProductInfoVO> productInfo, Long index) {
        for (ProductInfoVO productInfoVO : productInfo) {
            productInfoVO.setAmount(1);
            productInfoVO.setBmsOrderDetailId(index);
        }
    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        List<ProductInfoVO> productInfos = serviceVO.getProductInfo().stream()
                                                    .filter(productInfoVO ->
                                                                    ProductComponentEnum.RS_BMS.getKey()
                                                                                                .contains(productInfoVO.getProductCode().toLowerCase()))
                                                    .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(productInfos)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_VALID_FAILURE));
        }
//        serviceVO.setProductInfo(productInfos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.execute(serviceVO, applyEntity);
    }

    @Override
    public void after(ApplyEntity applyEntity) {
        if (CollectionUtil.isEmpty(applyEntity.getOrderDetails())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2071376150));
        }
        // 分布式链路跟踪，缓存traceId
        if (Objects.nonNull(applyEntity.getOrder())) {
            Long serviceOrderId = applyEntity.getOrder().getId();
            QueryWrapper<SfProductResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("service_order_id", serviceOrderId);
            List<SfProductResource> sfProductResource = sfProductResourceMapper.selectList(queryWrapper);
            if (!sfProductResource.isEmpty()) {
                try {
                    String clusterIds = sfProductResource.stream().map(e -> e.getClusterId().toString()).collect(Collectors.joining(","));

                    long traceId = tracing.currentTraceContext().get().traceId();
                    String cacheKey = TRACE_ID_KEY + sfProductResource.get(0).getProductType() + StrUtil.COLON + clusterIds;
                    JedisUtil.INSTANCE.set(cacheKey, String.valueOf(traceId));
                } catch (Exception e) {
                    log.info("获取当前链路traceId-AbstractOrderService.after-失败:[{}]", e.getMessage());
                }
            }
        }

        if (CollectionUtil.isEmpty(applyEntity.getResourceId())) {
            return;
        }

        for (ServiceOrderDetail orderDetail : applyEntity.getOrderDetails()) {
            ConfigDesc configDesc = new ConfigDesc();
            configDesc.setLabel("ID");
            configDesc.setValue(String.join(StrUtil.COMMA, applyEntity.getResourceId()));
            String productConfigDesc = orderDetail.getProductConfigDesc();

            if (Objects.nonNull(productConfigDesc)) {
                //判断是json对象还是json数组
                boolean isAarray = productConfigDesc.startsWith("{");
                if (isAarray) {
                    JSONObject jsonNode = JSON.parseObject(orderDetail.getProductConfigDesc());
                    JSONArray parent = jsonNode.getJSONArray("currentDesc");
                    if (parent != null) {
                        parent.add(0, configDesc);
                        orderDetail.setProductConfigDesc(JSON.toJSONString(jsonNode));
                        serviceOrderDetailMapper.updateById(orderDetail);
                    }
                }
            }
        }
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ServiceOrder order = applyEntity.getOrder();
        RestResult restResult;
        try {
            restResult = orderService.startProcessByOrder(order.getId());
        } catch (Exception e) {
            e.printStackTrace();
            // 将优惠券状态重置为未使用
            BizCouponResource bizCouponResource = couponService.getCouponResourceByOrderId(order.getId());
            if (Objects.nonNull(bizCouponResource)) {
                couponService.updateCouponStatus(bizCouponResource.getCouponSid(), CouponStatusEnum.UNUSED.getCode());
                couponService.updateCouponUsed(bizCouponResource.getCouponSid());
            }
            serviceOrderMapper.deleteById(order.getId());
            serviceOrderDetailMapper.delete(new LambdaQueryWrapper<ServiceOrderDetail>().eq(ServiceOrderDetail::getOrderId, order.getId()));
            QueryWrapper<SfProductResource> resourceQueryWrapper = new QueryWrapper<>();
            resourceQueryWrapper.eq("service_order_id",order.getId());
            List<SfProductResource> sfProductResources = sfProductResourceMapper.selectList(resourceQueryWrapper);
            try {
                if (CollectionUtils.isNotEmpty(sfProductResources)) {
                    List<Long> rollbackIds =
                            sfProductResources.stream().filter(r -> Objects.nonNull(r.getClusterId())).map(SfProductResource::getClusterId)
                                              .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(rollbackIds)) {
                        resVmRemoteService.deleteBmsByIds(rollbackIds);
                    }
                }
            }catch (Exception e1) {
                log.error("RSBmsOrderServiceImpl-createBms-rollback-error:{}", e1.getMessage());
            }
            sfProductResourceMapper.delete(resourceQueryWrapper);
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        if (Objects.nonNull(restResult)){
            if (!restResult.getStatus()){
                // 将优惠券状态重置为未使用
                BizCouponResource bizCouponResource = couponService.getCouponResourceByOrderId(order.getId());
                if (Objects.nonNull(bizCouponResource)) {
                    couponService.updateCouponStatus(bizCouponResource.getCouponSid(), CouponStatusEnum.UNUSED.getCode());
                    couponService.updateCouponUsed(bizCouponResource.getCouponSid());
                }
                serviceOrderMapper.deleteById(order.getId());
                serviceOrderDetailMapper.delete(new LambdaQueryWrapper<ServiceOrderDetail>().eq(ServiceOrderDetail::getOrderId, order.getId()));
                QueryWrapper<SfProductResource> resourceQueryWrapper = new QueryWrapper<>();
                resourceQueryWrapper.eq("service_order_id",order.getId());
                List<SfProductResource> sfProductResources = sfProductResourceMapper.selectList(resourceQueryWrapper);
                try {
                    if (CollectionUtils.isNotEmpty(sfProductResources)) {
                        List<Long> rollbackIds =
                                sfProductResources.stream().filter(r -> Objects.nonNull(r.getClusterId())).map(SfProductResource::getClusterId)
                                                  .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(rollbackIds)) {
                            resVmRemoteService.deleteBmsByIds(rollbackIds);
                        }
                    }
                }catch (Exception e1) {
                    log.error("RSBmsOrderServiceImpl-createBms-rollback-error:{}", e1.getMessage());
                }
                sfProductResourceMapper.delete(resourceQueryWrapper);
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
            }
        }

        return JSONUtil.toJsonStr(restResult.getData());
    }

    @Data
    private static class ConfigDesc {
        private String label;

        private String value;
    }

    @Override
    public void inquiryPrice(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.inquiryPrice(serviceVO, applyEntity);
        List<InquiryPriceResponse>  inquiryPriceResponses = applyEntity.getPrices();
        if (inquiryPriceResponses == null) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
        }

        inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(
                ProductCodeEnum.RS_BMS.getProductType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(inquiryPriceResponses) || CollectionUtil.isEmpty(inquiryPriceResponses.get(0).getBillingPrices())) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
        }

        Map<String, Object> map = BeanUtil.beanToMap(serviceVO.getProductInfo().get(0).getData());
        if (map.containsKey("dataDisk") && Objects.nonNull(map.get("dataDisk"))) {
            inquiryPriceResponses = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(
                    ProductCodeEnum.DISK.getProductType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(inquiryPriceResponses) || CollectionUtil.isEmpty(inquiryPriceResponses.get(0).getBillingPrices())) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
            }
        }
        if (CollectionUtil.isEmpty(inquiryPriceResponses)) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
        } else {
            for (InquiryPriceResponse response : inquiryPriceResponses) {
                String productCategory = response.getProductCategory();
                if (CollectionUtil.isEmpty(response.getBillingPrices())) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_619263840) + productCategory + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_144599566));
                }
            }
        }
        // 优惠券计算
        if (Objects.nonNull(applyEntity.getBizCoupon())) {
            bizInquiryPriceService.multipleProductCouponPrice(applyEntity.getPrices(), applyEntity.getBizCoupon().getDiscountAmount());
        }
    }
}
