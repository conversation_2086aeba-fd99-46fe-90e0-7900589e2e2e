/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 组织架构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@Service
public class OrgServiceImpl extends ServiceImpl<OrgMapper, Org> implements OrgService {

    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private RoleMapper roleMapper;

    private ExportService exportService;


    @Override
    public Org selectRootOrg(Long orgSid) {
        if (Objects.isNull(orgSid)) {
            return null;
        }
        Org org = this.getById(orgSid);
        if (Objects.isNull(org)) {
            return null;
        }
        if ("company".equals(org.getOrgType())||Objects.isNull(org.getParentId())) {
            return org;
        } else {
            return selectRootOrg(org.getParentId());
        }
    }

    @Override
    public List<Long> selectChildrenOrgIds(Long orgSid) {
        if (Objects.isNull(orgSid)) {
            return null;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.likeRight("tree_path","/"+orgSid+"/");
        return listObjs(queryWrapper);

    }

    @Override
    public List<Long> selectCustomerOrgSids(Long orgSid) {
        return orgMapper.selectCustomerOrgSids(orgSid);
    }

    @Override
    public List<Long> selectOrgIdListByOrgNameLike(String orgNameLike) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("org_name",orgNameLike);
        List<Org> list = this.orgMapper.selectList(queryWrapper);
        return list.stream().map(Org::getOrgSid).collect(Collectors.toList());
    }

    @Override
    public List<Org> getOrgInfo(Map param) {
        return this.orgMapper.getOrgInfo(param);
    }

    @Override
    public List<Org> selectOrgSidBySid(Long companyId) {
        return this.orgMapper.selectOrgSidBySid(companyId,"default");
    }
    @Override
    public List<Org> selectOrgByUserSidAndType(Long orgSid, String type) {
        return this.orgMapper.selectOrgByUserSidAndType(orgSid,type);
    }

    @Override
    public RestResult asynExportCustomer(ImportCustomerRequest request) {
        BizDownload download = new BizDownload();
        download.setOperationType("batchImportCustomer");

        //添加下载任务数据
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        download = getBizDownload(download, request.getType(), authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
        }
        new ExportThreadUtil(exportService, request.getType(), download.getDownloadId(),request.getCustomers(),request.getSubUsers(), authUserInfo).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1601343667), download.getDownloadId());
    }

    @Override
    public Org selectDistributorBy(String orgName) {
        return orgMapper.selectDistributorBy(orgName);
    }

    @Override
    public String selectContactName(Long orgSid) {
        return orgMapper.selectContactName(orgSid);
    }

    @Override
    public boolean updateSolutionById(Org org) {
        return orgMapper.updateSolutionById(org);
    }

    @Override
    public String selectLdapOuByOrgSid(Long orgSid) {
        return orgMapper.selectLdapOuByOrgSid(orgSid);
    }

    @Override
    public List<CurrentOrgVO> findOrgListByIds(Set<Long> orgSids) {
        if (CollectionUtil.isEmpty(orgSids)) {
            return new ArrayList<>();
        }
        List<Org> orgs = orgMapper.selectList(new QueryWrapper<Org>().in("org_sid", orgSids));
        if (CollectionUtil.isNotEmpty(orgs)) {
            orgSids = orgs.stream().filter(e -> !"company".equals(e.getOrgType()) && e.getParentId() != null).map(Org::getParentId).collect(Collectors.toSet());
            orgs =  orgs.stream().filter(e -> "company".equals(e.getOrgType()) || e.getParentId() == null).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orgSids)) {
                List<Org> result = orgMapper.selectList(new QueryWrapper<Org>().in("org_sid", orgSids));
                if (CollectionUtil.isNotEmpty(result)) {
                    orgs.addAll(result);
                }
            }
            return  BeanConvertUtil.convert(orgs, CurrentOrgVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<CurrentOrgVO> findByIds(Set<Long> orgSids) {
        if (CollectionUtil.isEmpty(orgSids)) {
            return new ArrayList<>();
        }
        List<Org> orgs = orgMapper.selectList(new QueryWrapper<Org>().in("org_sid", orgSids));
        if (CollectionUtil.isNotEmpty(orgs)) {
            return  BeanConvertUtil.convert(orgs, CurrentOrgVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public User selectUserByOrgSid(Long orgSid) {
        return orgMapper.selectUserByOrgSid(orgSid);
    }

    @Override
    public List<Org> findAll() {
        return orgMapper.selectAllOrg();
    }

    @Override
    public List<Org> selectOrgsByCustomize(String customizationInfo) {
        Criteria criteria = new Criteria();
        if (StringUtil.isNotEmpty(customizationInfo)) {
            try {
                JSONObject jsonObject = JSON.parseObject(customizationInfo);
                List<String> customizationInfoKey = new ArrayList<>();
                List<String> customizationInfoValue = new ArrayList<>();
                jsonObject.forEach((key, value) -> {
                    customizationInfoKey.add(key);
                    customizationInfoValue.add(String.valueOf(value));
                });
                criteria.put("customizationInfoKey", customizationInfoKey);
                criteria.put("customizationInfoValue", customizationInfoValue);
            }catch (Exception e) {
                throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException("参数不合法");
            }
        }
        return orgMapper.selectOrgsByCustomize(criteria);
    }

    /**
     * 查询所有组织简单信息 org_sid,org_name,org_type,parent_id
     * @return
     */
    @Override
    public List<Org> selectAllOrgSimple() {
        return orgMapper.selectAllOrgSimple();
    }
    @Override
    public void checkDistributorRole(Long orgSid, Long accountId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        if (!UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
            return;
        }
        List<Role> rolesByUserSid = roleMapper.findRolesByUserSid(authUserInfo.getUserSid());
        BizBillingAccount bizBillingAccount = new BizBillingAccount();
        if (Objects.nonNull(orgSid)) {
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.getByOrgSid(orgSid);
            if (bizBillingAccounts.isEmpty()) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
            bizBillingAccount = bizBillingAccounts.stream().filter(t -> authUserInfo.getEntityId()
                            .equals(t.getEntityId()))
                    .findFirst().orElse(new BizBillingAccount());

        } else if (Objects.nonNull(accountId)) {
            bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
            if (Objects.isNull(bizBillingAccount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
        }
        if (!authUserInfo.getOrgSid().equals(bizBillingAccount.getDistributorId()) || (
                rolesByUserSid.get(0).getDataScope().equals("3")
                        && !authUserInfo.getUserSid().equals(bizBillingAccount.getSalesmenId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
    }

    @Override
    public Org selectRootNewOrg( Org org,boolean isCompany) {
        if (Objects.isNull(org)) {
            return null;
        }
        Long parentId = org.getParentId();
        if (parentId == null) {
            return org;
        }
        Org parentOrg = this.getById(parentId);
        if (Objects.isNull(parentOrg)) {
            return null;
        }
        if (isCompany && !StringUtils.equalsIgnoreCase(parentOrg.getOrgType(), "company")) {
            return org;
        }
        return selectRootNewOrg(parentOrg, isCompany);
    }

    @Override
    public Org cmpSelectRootOrg(Long orgSid, String refOrgId, boolean isNeedRoot) {
        if (Objects.isNull(orgSid) && StringUtils.isEmpty(refOrgId)) {
            return null;
        }
        Org org = this.getById(orgSid);
        if (Objects.isNull(org)) {
            List<Org> orgList = this.list(new QueryWrapper<Org>().eq("ref_org_id", refOrgId));
            if (CollectionUtils.isEmpty(orgList)) {
                return null;
            }
            org = orgList.get(0);
        }
        if (Objects.isNull(org.getParentId())) {
            return org;
        } else {
            if (isNeedRoot) {
                return cmpSelectRootOrg(org.getParentId(), null, true);
            } else {
                return org;
            }
        }
    }

    private BizDownload getBizDownload(BizDownload download, String moduleType, AuthUser authUserInfo) {

        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        int insert = bizDownloadMapper.insert(download);
        return download;
    }
}
