/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.request.ProductSpecDefineFeignForm;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.result.ProductSpecDefineFeignResult;

/**
 * <AUTHOR>
 * @date 2020/5/6.
 */
@FeignClient(value = "${feign.url.service:https://cmp-service:6005}", configuration = FeignConfig.class, path = "/api/service/v1")
public interface ServiceFeignService {

    @GetMapping("/product/spec_define/list")
    RestResult<List<ProductSpecDefineFeignResult>> getProductSpecDefine(@SpringQueryMap ProductSpecDefineFeignForm param);

}
