/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 资源－虚拟磁盘
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-08
 */
@Data
public class ResVd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源磁盘ID
     */
    @TableId
    private String resVdSid;

    /**
     * 磁盘名称
     */
    private String vdName;

    /**
     * 占用存储大小
     */
    private Long allocateDiskSize;

    /**
     * 实际使用存储大小
     */
    private Long useDiskSize;

    /**
     * 分配存储资源ID
     */
    private String allocateResStorageSid;

    /**
     * 关联主机实例SID
     */
    private String resVmId;

    /**
     * 块存储用途
     */
    private String storagePurpose;

    private String deviceName;

    private String devicePath;

    /**
     * 磁盘模式
     */
    private String diskMode;

    private String mountPoint;

    private String fileSystemType;

    private String logicVolume;

    /**
     * UUID
     */
    private String uuid;

    private Long cloudEnvId;

    private String releaseMode;

    private String zone;

    private String diskType;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    private Long orgSid;

    /**
     * 部署id
     */
    private Long cloudDeploymentId;

    private Date startTime;

    private Date endTime;

    private Long resVolumeTypeId;

    private String errorMsg;

    private String source;

    /**
     * 描述
     */
    private String description;

    private String projectId;

    private String companyId;

    /**
     * urn
     */
    private String urn;

    /**
     * urn
     */
    private String uri;

    /**
     * 所有者ID
     */
    private String ownerId;

    /**
     * 资源模板id
     */
    private Long resourceTemplateId;

    /**
     * 自服务发布实例id
     */
    private Long serviceDeployInstId;

    private String chargeType;


    @TableField(exist = false)
    private String typeName;

    @TableField(exist = false)
    private String vdTypeUuid;

    @TableField(exist = false)
    private String cloudEnvName;

    @TableField(exist = false)
    private String zoneName;
}
