/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DeleteMessagesRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.MsgReceiveContactRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.MsgUserConfigUpdateRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.UpdateMessageReadRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.ValidateContactrRequest;

import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.request.SysMMsgReceiveContactReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/9.
 */
@FeignClient(value = "${feign.url.oss:https://cmp-oss:8080}", configuration = FeignConfig.class,path = "/api/v1/oss")
public interface MessageService {

    /**
     * 批量删除消息
     **/
    @DeleteMapping("/message")
    RestResult deleteMsgs(DeleteMessagesRequest deleteRequest);

    /**
     * 批量读取消息
     **/
    @PutMapping("/message/read")
    RestResult readMsgs(UpdateMessageReadRequest request);

    /**
     * 显示所有信息
     **/
    @GetMapping("/message/infos")
    RestResult displayAllMessage(@SpringQueryMap Map<String, Object> query);

    /**
     * 查询消息配置
     * @return
     */
    @GetMapping("/msg/rejectCall")
    RestResult getMsgRejectCall();

    /**
     * 修改消息配置
     * @param req
     * @return
     */
    @PutMapping("/msg/rejectCall")
    RestResult updateMsgRejectCall(@RequestBody MsgUserConfigUpdateRequest req);

    /**
     * 查看消息接收人
     * @param configId
     * @return
     */
    @GetMapping("/msg/msgContact/rejectCall")
    RestResult getMsgContactRejectCall(@RequestParam String configId);

    /**
     * 修改消息接收人
     * @param req
     * @return
     */
    @PutMapping("/msg/msgContact/rejectCall")
    RestResult updateMsgContactRejectCall(@RequestBody SysMMsgReceiveContactReq req);

    /**
     * 发送验证
     * @param req
     * @return
     */
    @PostMapping("/msg/sendContact/rejectCall")
    RestResult sendContactRejectCall(@RequestBody ValidateContactrRequest req);

    /**
     * 验证信息
     **/
    @GetMapping("/msg/validateContact")
    RestResult validateContact(@RequestParam String securityKey);
}
