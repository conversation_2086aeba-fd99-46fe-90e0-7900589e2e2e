package cn.com.cloudstar.rightcloud.bss.module.sfs.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ServiceProcessService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.ServiceProcessRequest;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/25 13:45
 */
@RestController
@RequestMapping("/process-apply")
@Validated
@Api(value = "/process-apply", tags = "流程申请管理Ctrl")
@ApiGroup(ApiGroupEnum.AUDIT_GROUP)
@AllArgsConstructor
@Slf4j
public class ServiceProcessCtrl {

    @Autowired
    private ServiceProcessService serviceProcessService;


    /**
     * 【Since v2.5.0】列出服务进程
     *
     * @param request 请求
     * @param status 状态
     *
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = AuthModuleOss.BG.BG03.CB2009)
    public RestResult listServiceProcess(ServiceProcessRequest request, @PathVariable String status) {
        return serviceProcessService.listServiceProcessFeign(request, status);
    }
}
