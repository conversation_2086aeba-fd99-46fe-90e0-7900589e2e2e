/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.controller;


import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.SysBssEntityMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.ResultUtil;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductCategoryCatalogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductCategoryCatalog;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfProductCategoryCatalogService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.IShareService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.COMMON.PRODUCT;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.ActionLogTypeEnum;
import cn.com.cloudstar.rightcloud.core.annotation.log.CustomerActionLog;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SysBssEntity;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD02.BD02;

/**
 * 产品
 *
 * <AUTHOR>
 * @since 2019-11-01
 */
@RestController
@RequestMapping("/products")
@Api("产品")
public class SfProductCategoryCatalogController {

    @Autowired
    private ISfProductCategoryCatalogService productCategoryService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private SysUserService sysUserService;


    @Autowired
    private OrgService orgService;

    @Autowired
    private SysBssEntityMapper sysBssEntityMapper;

    @Autowired
    private SfProductCategoryCatalogMapper sfProductCategoryCatalogMapper;

    @Autowired
    private IShareService iShareService;

    /**
     * 查询产品类别
     *
     * @param request 查询产品类别请求体
     * @return {@code IPage<DescribeProductCategoryResponse>}
     */
    @AuthorizeBss(action = BD02)
    @GetMapping("/categories")
    @ApiOperation("查询产品类别")
    public IPage<DescribeProductCategorySimpleResponse> listCategories(
            DescribeProductCategoryRequest request) {
        IPage<DescribeProductCategoryResponse> describeProductCategoryResponseIPage = productCategoryService.listCategories(request);
        return BeanConvertUtil.convertPage(describeProductCategoryResponseIPage, DescribeProductCategorySimpleResponse.class);
    }

    @AuthorizeBss(action = AuthModuleOss.BD.BD02.BD0201)
    @PostMapping("/categories")
    @ApiOperation("创建产品类别")
    @CustomerActionLog(Type = ActionLogTypeEnum.CREATE_PRODUCT_CATEGORY)
    @Idempotent
    public RestResult createCategory(@Valid @RequestBody CreateProductCategoriesRequest request) {
        if (productCategoryService.CategoryNameVerify(request.getName())) {
            return ResultUtil.ofInsert(
                    productCategoryService.createCategory(request.getName(), request.getProductIds()), null);
        }
        return new RestResult(cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status.FAILURE, "产品名称重复，创建失败");
    }

    /**
     * 删除产品类别
     *
     * @param categoryId 类别id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD.BD02.BD0203)
    @DeleteMapping("/categories/{id}")
    @ApiOperation("删除产品类别")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "产品类别",
            resource = OperationResourceEnum.DELETE_PRODUCT_CATEGORY, bizId = "#categoryId")
    public RestResult deleteCategory(@PathVariable("id") Long categoryId) {
        return ResultUtil.ofOperate(productCategoryService.deleteCategory(categoryId));
    }

    /**
     * 编辑产品类别
     *
     * @param categoryId 类别id
     * @param request    创建产品类别请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD.BD02.BD0202)
    @PutMapping("/categories/{id}")
    @ApiOperation("编辑产品类别")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.name",
            resource = OperationResourceEnum.EDIT_PRODUCT_CATEGORY, bizId = "#id", param = "#request")
    public RestResult updateCategory(@PathVariable("id") Long categoryId
            , @Valid CreateProductCategoriesRequest request) {
        AuthUser currentUserInfo = RequestContextUtil.getAuthUserInfo();
        Assert.notNull(currentUserInfo.getEntityId(), "运营实体ID不能为空");
        boolean b = productCategoryService.updateCategory(categoryId, request.getName(), request.getProductIds(),currentUserInfo.getEntityId().toString());
        if("存储".equals(request.getName()) && request.getProductIds().size()>0){
            //存储需要内置弹性文件
            boolean isDefault = iShareService.addDefaultConfig(categoryId, request.getProductIds());
            if(isDefault){
                return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INNER_MSG_0032));
            }
        }
        return ResultUtil.ofOperate(b);

    }

    /**
     * @param request
     * @return
     */
    @AuthorizeBss(action = PRODUCT.GET_PRODUCTS_CATEGORIES)
    @GetMapping("/categories/isHave")
    @ApiOperation("查询产品类别名称是否重复")
    public boolean isHave(
            DescribeProductCategoryRequest request) {
        List<SfProductCategoryCatalog> list = sfProductCategoryCatalogMapper
                .selectList(new QueryWrapper<SfProductCategoryCatalog>().eq("category_name", request.getCategoryNameLike()));
        if (CollectionUtil.isNotEmpty(list)){
            return  false;
        }
        return  true;
    }

    /**
     * 查询产品
     *
     * @param request 查询产品请求体
     * @return {@code IPage<DescribeProductSimpleResponse>}
     * @since 2.4.1
     */
    @AuthorizeBss(action = CB.CB19)
    @GetMapping
    @ApiOperation("查询产品")
    public IPage<DescribeProductSimpleResponse> listProducts(@Valid  DescribeProductRequest request) {
        IPage<DescribeProductResponse> describeProductResponseIPage = productCategoryService.listProducts(request);
        return BeanConvertUtil.convertPage(describeProductResponseIPage, DescribeProductSimpleResponse.class);
    }

    /**
     * [INNER API] 查询产品
     *
     * @param request 查询产品请求体
     * @return {@code IPage<DescribeProductResponse>}
     * @since 2.4.1
     */
    @RejectCall
    @GetMapping("/feign")
    @ApiOperation("查询产品")
    public IPage<DescribeProductSimpleResponse> listProductsByFeign(DescribeProductRequest request) {
        return listProducts(request);
    }

    /**
     * 创建产品
     *【Since v2.5.0】
     * @param request 创建产品请求体
     * @return {@code RestResult}
     * @since 2.4.1
     */
    @AuthorizeBss(action = BD01.BD0101)
//    @PostMapping("")
    @ApiOperation("创建产品")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'创建产品'", param = "#request", tagNameUs ="'Create a product'",
            resource = OperationResourceEnum.CREAT_PRODUCT,bizId = "#request.productName")
    public RestResult createProcut(@RequestBody @Valid CreateProductRequest request) {

        String productName = request.getProductName();
        if (StringUtil.isEmpty(productName) || productName.length() > 25 || StringUtils.isBlank(productName)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_NAME));
        }

        String productCode = request.getProductCode();
        if (StringUtil.isEmpty(productCode)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE));
        }
        String reg = "^[0-9a-zA-Z._-]{1,50}";
        if (!productCode.matches(reg)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE));
        }

        String productDesc = request.getProductDesc();
        if (StringUtil.isNotEmpty(productDesc) && productCode.length() > 500) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_DESC));
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS), productCategoryService.createProduct(request));
    }

    /**
     * 产品代码是否存在
     * [INNER API] 产品目录-编辑
     *
     * @param productCodeRequest 产品代码请求
     * @return boolean
     * @since 2.4.1
     */
    @AuthorizeBss(action = BD01.BD0102)
    @GetMapping("/productCode/exist")
    @ApiOperation("产品代码是否存在")
    public boolean productCodeExist(@Valid ProductCodeRequest productCodeRequest) {
        return !productCategoryService.checkProductCodeExist(productCodeRequest.getProductCode(), productCodeRequest.getProductId());
    }

    /**
     * 删除产品
     *
     * @param productId 产品id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD01.BD0103)
    @DeleteMapping("/{id}")
    @ApiOperation("删除产品")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'产品名称'", bizId = "#id", tagNameUs ="'Product Name'",
            resource = OperationResourceEnum.DELETE_PRODUCT)
    public RestResult deleteProduct(@PathVariable(value = "id") Long productId) {

        if (productId == null) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        productCategoryService.deleteProduct(productId);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 更新产品
     *
     * @param request 修改产品请求体
     * @return {@code RestResult}
     * @since 2.4.1
     */
    @AuthorizeBss(action = BD01.BD0102)
    @PutMapping("")
    @ApiOperation("更新产品")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.productName", bizId = "#request.id", resource = OperationResourceEnum.UPDATE_PRODUCT)
    public RestResult updateProduct(@RequestBody @Valid UpdateProductRequest request) {
        //status不为空时是上下架

        String productName = request.getProductName();
        if (StringUtil.isEmpty(productName) || productName.length() > 25) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_NAME));
        }

        String productCode = request.getProductCode();
        if (StringUtil.isEmpty(productCode)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE));
        }
        String reg = "^[0-9a-zA-Z._-]{1,50}";
        if (!productCode.matches(reg)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE));
        }

        String productDesc = request.getProductDesc();
        if (StringUtil.isNotEmpty(productDesc) && productCode.length() > 500) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_DESC));
        }

        Pattern pattern = java.util.regex.Pattern.compile("^[A-Za-z]+$");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getShowType())) {
            if (!pattern.matcher(request.getShowType()).matches()) {
                throw new BizException(MsgCd.PARAM_NOT_VALID_ERROR);
            }
        }

        return ResultUtil.ofOperate(productCategoryService.updateProduct(request));
    }

    /**
     * 更新产品状态(上下架)
     *
     * @param request 修改产品状态请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD01.BD0105)
    @PutMapping("status/{id}")
    @ApiOperation("更新产品状态(上下架)")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'产品类别'", tagNameUs ="'Product category'",
            resource = OperationResourceEnum.PRODUCT_CATALOG_LISTING_AND_DELISTING, param = "#request")
    public RestResult updateProductStatus(UpdateProductStatusRequest request) {
        String status = request.getStatus();
        if (request.getId() == null
                || (!ServiceCategoryStatus.NOUSING.equals(status) && !ServiceCategoryStatus.USING.equals(status))) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        return ResultUtil.ofOperate(productCategoryService.updateProductStatus(request));
    }

    /**
     * 关联产品模板
     *
     * @param request 关联产品模板请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD01.BD0104)
    @PutMapping("/related/templates")
    @ApiOperation("关联产品模板")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'关联产品模板'", bizId = "#request.templateIds", param = "#request", tagNameUs ="'Associated Product Templates'",
            resource = OperationResourceEnum.RELATED_PRODUCT_TEMPLATE)
    public RestResult relatedProductTemplate(@RequestBody @Valid RelatedProductTemplatesRequest request) {
        return ResultUtil.ofOperate(productCategoryService.relatedProductTemplate(request));
    }

    /**
     * 查询平台所有上过架产品
     *
     * @param productNameLike 产品名称类似
     * @param entityId        实体ID
     * @param moduleType      模块类型
     * @return {@link List}<{@link DescribeServiceCategory}>
     * @since 2.4.1
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C114 + ",ZL")
    @ApiOperation("查询平台所有上过架产品")
    @GetMapping("/publish/products")
    public List<DescribeServiceCategory> publishProducts(String productNameLike, String entityId,
                                                         @RequestHeader String moduleType) {
        if (StringUtils.isBlank(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_18819451));
        }
        if (!Arrays.asList(Constants.BSS, Constants.CONSOLE).contains(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2038660916));
        }
        if(Constants.BSS.equalsIgnoreCase(moduleType)){
            SysBssEntity entity = sysBssEntityMapper.selectById(entityId);
            if(entity == null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2038660916));
            }
        }
        return productCategoryService.publishProducts(productNameLike, entityId, moduleType);
    }

    /**
     * 查询平台所有上过架产品 【Since v2.5.0】
     *
     * @param productNameLike 产品名称
     * @param entityId 实体ID
     * @param moduleType 模块类型
     *
     * @return {@link List}<{@link DescribeServiceCategory}>
     */
    @AuthorizeBss(action = AuthModule.CD.CD02)
    @ApiOperation("查询平台所有上过架产品")
    @GetMapping("/publish/products/list")
    public List<DescribeServiceCategory> publishProductsList(String productNameLike, String entityId,
                                                             @RequestHeader String moduleType) {
        return productCategoryService.publishProductsList(productNameLike, entityId, moduleType);
    }
    /**
     * 通过账户ID找到对应产品
     *
     * @param accountId 帐户ID
     * @return {@link List}<{@link DescribeServiceCategory}>
     * @since 2.4.1
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C103)
    @ApiOperation("通过账户ID找到对应产品")
    @GetMapping("/product/billingAccount/{accountId}")
    public List<DescribeServiceCategory> findProductByAccountId(@PathVariable("accountId") Long accountId) {
        return productCategoryService.findProductByAccountId(accountId);
    }

    /**
     * 查询产品是否上架
     */
    @GetMapping("/{serviceType}")
    @RejectCall
    public RestResult checkServiceStatus(@PathVariable("serviceType") String serviceType) {
        return productCategoryService.checkServiceStatus(serviceType);
    }

}

