package cn.com.cloudstar.rightcloud.bss.module.intergrate.service.impl;

import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.ResFederationInstMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.ResFederationInst;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryFederationInstRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.FederationInstRequestResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResFederationInstService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.enums.FederationStatusConstants;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@Service
public class ResFederationInstServiceImpl
        extends ServiceImpl<ResFederationInstMapper, ResFederationInst>
        implements ResFederationInstService {

    @Autowired
    private ResFederationInstMapper resFederationInstMapper;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public RestResult list(QueryFederationInstRequest request) {
        QueryWrapper queryWrapper = new QueryWrapper<ResFederationInst>();
        String userSid = request.getUserSid();
        final User user = userMapper.selectByPrimaryKey(Long.parseLong(userSid));
        log.info("查询用户信息:{}", user);
        if (ObjectUtil.isEmpty(user)) {
            throw new BizException("用户不存在");
        }
        if (ObjectUtil.isNotEmpty(user.getParentSid())) {
            userSid = user.getParentSid().toString();
        }
        log.info("查询用户userSid:{}", userSid);
        if (StringUtils.isNotBlank(userSid)) {
            queryWrapper.eq("owner_sid", userSid);
        }

        String productCode = request.getProductCode();
        if (StringUtils.isNotBlank(productCode)) {
            queryWrapper.eq("product_code", productCode);
        }
        queryWrapper.notIn("status", "unsubscribed", "rejected");

        List<ResFederationInst> list = resFederationInstMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return new RestResult();
        }

        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectList(Wrappers.<SfProductResource>lambdaQuery()
                .in(SfProductResource::getId, list.stream().map(ResFederationInst::getResourceId).collect(Collectors.toList()))
        );
        if (CollectionUtils.isNotEmpty(sfProductResources)) {
            for (SfProductResource sfProductResource : sfProductResources) {
                if (Objects.nonNull(sfProductResource.getEndTime()) && new Date().after(sfProductResource.getEndTime())) {
                    if ("normal".equals(sfProductResource.getStatus())) {
                        sfProductResource.setStatus("expired");
                    }
                }
            }
        }
        for (ResFederationInst resFederationInst : list) {
            if (Objects.nonNull(resFederationInst.getEndTime()) && new Date().after(resFederationInst.getEndTime())) {
                if (FederationStatusConstants.AVAILABLE.equals(resFederationInst.getStatus())) {
                    resFederationInst.setStatus(FederationStatusConstants.EXPIRED);
                }
            }
        }
        Map<Long, String> map = sfProductResources.stream().collect(Collectors.toMap(SfProductResource::getId, SfProductResource::getStatus));
        List<FederationInstRequestResponse> convert = BeanConvertUtil.convert(list, FederationInstRequestResponse.class);
        convert.forEach(c -> c.setSecStatus(map.get(Long.valueOf(c.getResourceId()))));
        return new RestResult(convert);
    }


}
