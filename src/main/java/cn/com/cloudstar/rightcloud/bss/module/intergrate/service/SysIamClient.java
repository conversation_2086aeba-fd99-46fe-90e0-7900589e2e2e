package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.common.common.RightCloudResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.cmp.system.form.SysIamAuthFeignForm;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.cmp.system.result.SysIamAuthFeignResult;

/**
 * iam客户端
 *
 * <AUTHOR>
 * @date 2022/10/12 15:13.
 */
@FeignClient(value = "${feign.url.system:https://cmp-system:6001}", configuration = FeignConfig.class, path = "/api/v1/system/iam")
public interface SysIamClient {

    /**
     * 用户接口权限认证
     *
     * @param iamAuthFrom 用户信息
     */
    @PostMapping("/role_auth/group")
    RightCloudResult<SysIamAuthFeignResult> roleAuth(@RequestBody SysIamAuthFeignForm iamAuthFrom);




}
