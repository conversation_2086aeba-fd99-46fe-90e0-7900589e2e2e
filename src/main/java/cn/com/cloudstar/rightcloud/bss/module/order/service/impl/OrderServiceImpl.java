/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ApproveOrderRequest;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.OrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductConfigDesc;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.OrderToChargeingTypeVo;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeShareRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.factory.OrderServiceFactory;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ResourceInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShare;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7.
 */
@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {

    @Autowired
    private OrderServiceFactory orderServiceFactory;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private ServiceCategoryService categoryService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private HpcClusterService hpcClusterService;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * APPLY
     */
    private static final String APPLY = "apply";

    /**
     * APPROVAL
     */
    private static final String APPROVAL = "approval";

    private static final String LDAP_ENABLE = "ldap.enable";

    /**
     *  订单编号对应收费规则KEY
     */
    private static final String ORDERNO_TO_CHARGINGTYPE = "orderNo_to_chargingType";

    private static final String ORDERNO_TO_CHARGINGTYPE_ORDERID = "orderNo_to_chargingType_orderId";



    @Override
    public String orderServiceAction(ApplyServiceVO applyServiceVO) {
        String errorMessage = null;
        String productName=applyServiceVO.getProductName();
        //查询现金余额
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        //根据产品类别查询对应的运营实体
        //根据产品id查询对应运营实体
        EntityDTO entityDTO = categoryService.getEntityByCategoryId(applyServiceVO.getProductInfo().get(0).getServiceId());
        if (Objects.isNull(entityDTO)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1964963679));
        }
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entityDTO.getEntityId(), authUser.getUserSid());
        if (Objects.isNull(account)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
        }
        if(Objects.nonNull(applyServiceVO.getBehalfUserSid()) && !authUser.getEntityId().equals(entityDTO.getEntityId())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_463148427));
        }

        //弹性文件服务只能开通10个
        if (ProductCodeEnum.SFS2.getProductType().equals(applyServiceVO.getProductInfo().get(0).getProductCode())
                || ProductCodeEnum.SFS.getProductType().equals(applyServiceVO.getProductInfo().get(0).getProductCode())) {


            int repeat = countByName(productName, "repeat");

            if (repeat >= 1) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_21710839) + productName + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1625081363));
            }
        }
        //判断产品名称是否含有特殊字符
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        if(StringUtils.isNotEmpty(productName) && Pattern.compile(regEx).matcher(productName).find()){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_21710839)+productName+WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1710045038));
        }


        applyServiceVO.setAccountId(account.getId());
        applyServiceVO.setEntityId(entityDTO.getEntityId());
        applyServiceVO.setEntityName(entityDTO.getEntityName());
        String productCode = applyServiceVO.getProductInfo().get(0).getProductCode();
        String ldapEnable = PropertiesUtil.getProperty(LDAP_ENABLE);

        List<String> share = Arrays.asList(ProductCodeEnum.HPC.getProductType(),
                                             ProductCodeEnum.MODEL_ARTS.getProductType(),
                                             ProductCodeEnum.HPC_SAAS.getProductType());
        if (share.contains(productCode)) {
            List<String> hpcShare = Arrays.asList(ProductCodeEnum.HPC.getProductType(),
                                                  ProductCodeEnum.HPC_SAAS.getProductType());
            if (hpcShare.contains(productCode) && "0".equals(ldapEnable)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_730173332));
            }
        }
        // 针对modelarts专属资源池命名，因自动化创建专属资源池时，华为接口对资源池名字有此限制
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productCode)
                && !Pattern.matches("^[a-z][a-z0-9-]{2,28}[a-z0-9]$", productName)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_210195960));
        }
        if (ProductCodeEnum.HPC.getProductType().equals(productCode)
                || ProductCodeEnum.MODEL_ARTS.getProductType().equals(productCode)
                || ProductCodeEnum.HPC_SAAS.getProductType().equals(productCode)
                || ProductCodeEnum.RDS.getProductType().equals(productCode)) {
            errorMessage = orderServiceFactory.orderService(productCode).apply(applyServiceVO);
        } else if (ProductCodeEnum.HPC_DRP.getProductType().equals(productCode)) {
            errorMessage = orderServiceFactory.orderService(productCode).apply(applyServiceVO);
        } else if (ProductCodeEnum.ECS.getProductType().equals(productCode)
                || ProductCodeEnum.RS_BMS.getProductType().equals(productCode)) {
            errorMessage = orderServiceFactory.orderService(productCode).apply(applyServiceVO);
        } else {
            for (ProductInfoVO productInfoVO : applyServiceVO.getProductInfo()) {
                errorMessage = orderServiceFactory.orderService(productInfoVO.getProductCode()).apply(applyServiceVO);
            }
        }
        return errorMessage;
    }

    public int countByName(String name, String type) {
        DescribeShareRequest request = new DescribeShareRequest();
        request.setNameLike(name);
        request.setIncludeDelete(true);
        RestResult restResult = shareService.getShareList(BasicInfoUtil.getMaxDataScope(), request);
        String jsonStr = JSONUtil.toJsonStr(restResult.getData());
        List<ResShare> shareList = JSONUtil.toList(JSONUtil.parseArray(jsonStr), ResShare.class);
        shareList = shareList.stream().filter(e -> !e.getStatus().equals("deleted")).collect(Collectors.toList());
        List<String> shareNames = shareList.stream()
                                           .map(ResShare::getName)
                                           .sorted(Comparator.naturalOrder())
                                           .collect(Collectors.toList());
        int i = 0;
        boolean isRepeat = "repeat".equals(type);
        for (String sn : shareNames) {
            int endIndex = sn.lastIndexOf('_');
            String s = isRepeat || !sn.contains("_") ? sn : sn.substring(0, endIndex);
            if (s.equals(name)) {
                if (isRepeat) {
                    i++;
                } else {
                    String number = sn.substring(endIndex + 1);
                    i = NumberUtil.isNumber(number) && !number.startsWith("0") ? Math.max(Integer.parseInt(number), i)
                            : i;
                }
            }
        }
        return i;
    }

    @Override
    public String checkProduct(ApplyServiceVO applyServiceVO, Long userSid) {
        ProductInfoVO productInfoVO = CollectionUtil.getFirst(applyServiceVO.getProductInfo());
        String productCode = productInfoVO.getProductCode();
        String result = StrUtil.EMPTY;
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productCode)
                || ProductCodeEnum.IAAS.getProductType().equals(productCode)) {
            ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(
                    ProductCodeEnum.MODEL_ARTS.getProductType(), userSid);
            if (Objects.isNull(productOrderInfo.getId())) {
                result = APPLY;
            } else if (OrderType.APPLY.equals(productOrderInfo.getType())) {
                if (OrderStatus.PENDING.equals(productOrderInfo.getStatus())) {
                    result = APPROVAL;
                } else if (!OrderStatus.COMPLETED.equals(productOrderInfo.getStatus())) {
                    result = APPLY;
                }
            } else {
                if (OrderStatus.COMPLETED.equals(productOrderInfo.getStatus())) {
                    result = APPLY;
                }
                if(OrderType.RELEASE.equalsIgnoreCase(productOrderInfo.getType()) && OrderStatus.PENDING.equals(productOrderInfo.getStatus())){
                    result = SfProductEnum.UNSUBSCRIBING.getStatus();
                }
            }
        }
        return result;
    }

    /**
     * 自动申请ModelArts
     */
    @Override
    public String modelArtsOrder(ApplyServiceVO applyServiceVO) {
        // 环境id
        Long cloudEnvId = CollectionUtil.getFirst(applyServiceVO.getProductInfo()).getCloudEnvId();
        ApplyServiceVO modelArtsApplyServiceVO = applyServiceVO;
        List<ProductInfoVO> productInfoVOList = Lists.newArrayList();
        ProductInfoVO productInfoVO = new ProductInfoVO();
        // 取得modelArts和OBS 产品信息
        QueryWrapper<ServiceCategory> categoryQueryWrapper = new QueryWrapper<>();
        String modelArtsStr = ProductCodeEnum.MODEL_ARTS.getProductType();
        String obsStr = ProductCodeEnum.OBS.getProductType();
        categoryQueryWrapper.eq("publish_status", "succeed");
        List<String> productIds = Lists.newArrayList();
        productIds.add(modelArtsStr);
        productIds.add(obsStr);
        categoryQueryWrapper.in("service_form", productIds);
        List<ServiceCategory> serviceCategories = categoryService.list(categoryQueryWrapper);
        Map<String, Long> serviceCategoryMap = serviceCategories.stream().collect(
                Collectors.toMap(ServiceCategory::getServiceForm, ServiceCategory::getId, (s, a) -> s + a));
        // modelArts
        productInfoVO.setProductCode(modelArtsStr);
        productInfoVO.setCloudEnvId(cloudEnvId);
        productInfoVO.setServiceId(serviceCategoryMap.get(modelArtsStr));
        productInfoVO.setChargeType("PostPaid");
        productInfoVO.setAmount(1);
        productInfoVO.setPeriodUnit("Hour");
        productInfoVO.setPeriod(null);
        productInfoVO.setData("{\"price\":{},\"modelArts\":null}");
        ProductConfigDesc productConfigDesc = new ProductConfigDesc();
        productConfigDesc.setCurrentConfigDesc("[{\"label\":\"备注\",\"attrKey\":\"textarea-\",\"value\":\"--\"}]");
        productInfoVO.setProductConfigDesc(productConfigDesc);
        productInfoVOList.add(productInfoVO);
        // OBS
        productInfoVO = new ProductInfoVO();
        productInfoVO.setProductCode(obsStr);
        productInfoVO.setCloudEnvId(cloudEnvId);
        productInfoVO.setServiceId(serviceCategoryMap.get(obsStr));
        productInfoVO.setChargeType("PostPaid");
        productInfoVO.setAmount(1);
        productInfoVO.setPeriodUnit("Hour");
        productInfoVO.setPeriod(null);
        productInfoVO.setData("{\"obs\":{\"chargeItemCategory\":\"blockStorage\",\"productCode\":\"OBS\",\"unitPriceDisplay\":true,\"size\":1,\"allSpecDisplay\":true}}");
        productInfoVO.setProductConfigDesc(new ProductConfigDesc());
        productInfoVOList.add(productInfoVO);
        modelArtsApplyServiceVO.setProductInfo(productInfoVOList);
        // 自动创建订单
        String errorMessage = orderServiceAction(modelArtsApplyServiceVO);
        if (StrUtil.isNotBlank(errorMessage)) {
            return errorMessage;
        }
        return null;
    }

    /**
     * 自动审核ModelArts
     */
    @Override
    public String modelArtsOpen(ApplyServiceVO applyServiceVO) {

        ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(
                ProductCodeEnum.MODEL_ARTS.getProductType(), applyServiceVO.getUserSid());
        ApproveOrderRequest request = new ApproveOrderRequest();
        request.setId(productOrderInfo.getId());
        request.setUserSid(String.valueOf(applyServiceVO.getBehalfUserSid()));
        request.setApproveType("01");
        request.setApproveAdvice("自动审批ModelArts");
        request.setOrderId(Long.valueOf(productOrderInfo.getId()));
        request.setOrderSn(productOrderInfo.getOrderSn());
        request.setResourceInfo(new ResourceInfo());

        RestResult result = orderService.updateOrder(request);
        if (!result.getStatus()) {
            return "自动审核失败！请确认订单[" + productOrderInfo.getOrderSn() + "]";
        }
        return null;
    }

    @Override
    public void handleOrderToChargeingType() {
        String orderId = JedisUtil.INSTANCE.get(ORDERNO_TO_CHARGINGTYPE_ORDERID, "0");
        orderId = orderId.replaceAll("\"", "");
        List<OrderToChargeingTypeVo> list = orderMapper.selectOrderToChargeingType(Long.valueOf(orderId));
        if (CollectionUtil.isNotEmpty(list)) {
            for (OrderToChargeingTypeVo vo : list) {
                String chargingType = vo.getChargingType();
                if (StringUtils.isBlank(chargingType)) {
                    vo.setChargingType("--");
                } else {
                    if ("1".equals(chargingType)) {
                        vo.setChargingType("正常计费");
                    } else {
                        vo.setChargingType("销售计费");
                    }
                }
            }
            Map<String, String> data = JedisUtil.INSTANCE.hgetall(ORDERNO_TO_CHARGINGTYPE);

            Map<String, Object> map = list.stream().collect(Collectors.toMap(OrderToChargeingTypeVo::getOrderSn, OrderToChargeingTypeVo::getChargingType,
                    (k, v) -> k));
            if (data.size() > 0) {
                map.putAll(data);
            }

            JedisUtil.INSTANCE.hmset(ORDERNO_TO_CHARGINGTYPE, map, -1);
            Long cacheOrderId = list.get(list.size() - 1).getId();
            JedisUtil.INSTANCE.set(ORDERNO_TO_CHARGINGTYPE_ORDERID, String.valueOf(cacheOrderId));
        }

    }

    @Override
    public String orderServiceMarket(ApplyServiceVO applyMarketVo) {
        String errorMessage = null;
        // 查询现金余额
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 根据产品id查询对应运营实体
        EntityDTO entityDTO = categoryService.getEntityByCategoryId(applyMarketVo.getProductInfo().get(0).getServiceId());
        if (Objects.isNull(entityDTO)) {
            throw new BizException("当前产品未关联运营实体");
        }
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entityDTO.getEntityId(), authUser.getUserSid());
        if (Objects.isNull(account)) {
            throw new BizException("当前产品没有所对应的运营实体账户");
        }
        if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("现金余额小于0，不能开通资源！");
        }

        applyMarketVo.setAccountId(account.getId());
        applyMarketVo.setEntityId(entityDTO.getEntityId());
        applyMarketVo.setEntityName(entityDTO.getEntityName());
        String shopId = applyMarketVo.getProductInfo().get(0).getShopId();

        errorMessage = orderServiceFactory.orderService(applyMarketVo.getProductInfo().get(0).getProductCode()).apply(applyMarketVo);
        return errorMessage;
    }

    /**
     * json转换成对象
     * @return Long
     */
    private Long getResShare(ApplyServiceVO applyServiceVO){
        ProductInfoVO productInfo = CollectionUtil.getFirst(applyServiceVO.getProductInfo());
        cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare resShare = BeanConvertUtil.convert(
                JSONUtil.parseObj(productInfo.getData()).getStr(productInfo.getProductCode()),
                cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare.class);
        if(Objects.isNull(resShare)){
            resShare.setSize(0);
        }
        return Long.valueOf(resShare.getSize());

    }
}
