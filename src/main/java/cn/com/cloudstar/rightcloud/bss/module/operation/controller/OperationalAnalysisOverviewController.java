package cn.com.cloudstar.rightcloud.bss.module.operation.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CostBillExport;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CostBillItem;
import cn.com.cloudstar.rightcloud.bss.module.operation.service.OperationalAnalysisOverviewService;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-06 17:03
 * @Desc
 */
@RestController
@RequestMapping("/operational/analysis/overview")
public class OperationalAnalysisOverviewController {

    @Autowired
    private OperationalAnalysisOverviewService operationalAnalysisOverviewService;

    @RejectCall
    @PostMapping(value = "/export/feign")
    public RestResult export(@RequestBody CostBillExport costBillExport) {
        return operationalAnalysisOverviewService.export(costBillExport);
    }

}
