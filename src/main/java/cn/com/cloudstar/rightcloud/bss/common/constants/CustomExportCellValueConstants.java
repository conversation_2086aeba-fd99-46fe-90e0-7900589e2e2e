package cn.com.cloudstar.rightcloud.bss.common.constants;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *    自定义信息导出字典类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
public class CustomExportCellValueConstants {

    /**
     * 客户信息表头
     */
    public static final List<String> ACCOUNT_CELL_VALUE = Arrays.asList("客户名称","组织OU","账户ID","账户名称","联系人","管理员姓名",
                                                                        "用户名","邮箱","电话号码","所属销售组织","注册时间",
                                                                        "业务标识","状态","是否资源冻结","现金余额","信用余额",
                                                                        "现金劵余额","线下开票金额","所属销售","所属行业","应用场景","人员规模","详细地址","备注");

    /**
     * 子用户信息表头
     */
    public static final List<String> RES_USERS_CELL_VALUE = Arrays.asList("客户名称","联系人","管理员姓名","主用户名","子用户名",
                                                                          "子用户姓名","业务标识","邮箱","电话号码");

    /**
     * 客户信息字段名
     */
    public static final List<String> ACCOUNT_CELL_FIELD = Arrays.asList("accountName","ldapOu","accountId","entityName","contactName","adminName",
                                                                        "account","email","mobile","distributorName","createdDt","businessTag",
                                                                        "status","freezeStatusName","balance","creditLine","balanceCash","offlineInvoicedAmount",
                                                                        "salesmen","industryName","scenarioName","sizeName","address","remark");

    /**
     * 子用户信息字段名
     */
    public static final List<String> RES_USERS_CELL_FIELD = Arrays.asList("accountName","contactName","adminName","accountName","account",
                                                                          "realName","businessTag","email","mobile");

    /**
     * 收支明细字段名
     */
    public static final List<String> BSS_ACCOUNT_DEAL_FIELD = Arrays.asList("flowNo","tradeTime","account","entityName","accountName","distributorName",
                                                                            "type","tradeType","tradeChannel","tradeNo","orderNo","billNo","billingCycle",
                                                                            "amount","balance","cashAmount","balanceCash","balanceCreditAmount","balanceCredit",
                                                                            "deductBalanceCash","chargingTypeForExcel","remark");

    /**
     * 收支明细表头
     */
    public static final List<String> BSS_ACCOUNT_DEAL_VALUE = Arrays.asList("交易编号","交易时间","账户ID","账户名称","客户名称","所属分销商","收支类型","交易类型","交易渠道",
                                                                            "交易渠道流水号","订单号","账单号","账期","现金消费金额(元)","现金余额(元)","充值现金券消费金额(元)",
                                                                            "充值现金券余额（元）","信用额度消费金额(元)","信用额度余额(元)","优惠券(元)","收费规则","备注");
}
