package cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 自定义用户信息配置模板
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@ApiModel(description = "自定义用户信息配置模板")
@Data
public class CustomizationInfoTemplate {

    @ApiModelProperty("字段标识")
    private String attrKey;

    @ApiModelProperty("字段名称")
    private String attrName;

    @ApiModelProperty("字段类型")
    private String attrType;

    @ApiModelProperty("是否必填")
    private Boolean isRequired;

    @ApiModelProperty("校验规则正则表达式")
    private String pattern;

    @ApiModelProperty("字段选项")
    private List<String> options;

    @ApiModelProperty("对租户侧是否禁用")
    private Boolean isConsoleDisable = false;

    @ApiModelProperty("对运营侧是否禁用")
    private Boolean isMgtDisable = false;
}
