/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * Created on 2019/11/05
 */
@Data
public class ServerTemplate {

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 云环境名称
     */
    private String cloudEnvNames;

    /**
     * 发布状态
     */
    private String publishStatus;

    /**
     * 发布者
     */
    private String publisher;

    /**
     * api 参考链接
     */
    private String href;

    /**
     * 克隆源id
     */
    private Long cloneId;

    /**
     * 克隆源名称
     */
    private String clonedName;

    /**
     * 克隆时间
     */
    private Date clonedDt;

    /**
     * 云环境配置
     */
    private String envConfig;

    /**
     * 版本列表
     */
    private List<LinkedHashMap<String, Object>> versionList = new ArrayList<>();

    private Integer mainTemplateId;

    private String templateClass;

    private String cloudEnvTypes;

    private Boolean fixed;
}

