/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/05/29 10:03
 */
@ApiModel("续订请求")
@Data
public class RenewRequest {

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    private String projectId;

    /**
     * 优惠券sid
     */
    @ApiModelProperty(value = "优惠券id")
    private Long couponSid;

    /**
     * 产品详情
     */
    @ApiModelProperty("产品详情")
    @Valid
    private List<ProductInfoVO> productInfo;

    @ApiModelProperty("服务数量")
    private BigDecimal serviceAmount;

    @ApiModelProperty("平台折扣")
    private BigDecimal platformDiscount;

    /**
     * 代客下单管理员id
     */
    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    /**
     * 下单用户id
     */
    @ApiModelProperty("下单用户ID")
    private Long userSid;

    /**
     * 下单用户组织id
     */
    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    public BigDecimal getServiceAmount() {
        return Objects.isNull(this.serviceAmount) ? BigDecimal.ZERO : this.serviceAmount;
    }

    public BigDecimal getPlatformDiscount() {
        return Objects.isNull(this.platformDiscount) ? BigDecimal.ONE : this.platformDiscount;
    }


}
