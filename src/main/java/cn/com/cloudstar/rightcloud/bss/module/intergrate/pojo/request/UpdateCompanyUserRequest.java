/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.RoleSidVO;
import cn.com.cloudstar.rightcloud.oss.common.safe.Mobile;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.StartWithWord;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.TypesRange;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Future;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 更新企业用户
 *
 * <AUTHOR>
 */
@ApiModel(description = "更新企业用户")
@Setter
@Getter
public class UpdateCompanyUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户SID
     */
    @ApiModelProperty(value = "用户SID", required = true)
    @NotNull
    private Long userSid;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    @SafeHtml
    @Length(max = 64)
    @NotIllegalString
    private String realName;

    /**
     * 邮箱
     */
    @Email(regexp = "^[a-zA-Z0-9_.-]{4,16}@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$", message = "邮箱格式不正确")
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 手机
     */
    @Mobile
    @ApiModelProperty("手机")
    private String mobile;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    @SafeHtml
    @Length(max = 64)
    @NotIllegalString
    private String account;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    @StartWithWord(message = "公司名称不能已test，admin开头")
    @Length(max = 64, min = 2, message = "公司名称长度为 2-64 个字符")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z()]([\\u4e00-\\u9fa5-_()a-zA-Z0-9])+$" ,message = "公司名称不能以数字、下划线、中划线开头，不支持特殊符号")
    private String orgName;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 短信验证码
     */
    @ApiModelProperty("短信验证码")
    private String smsCode;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "应用场景")
    @TypesRange(type = TypesConstant.APPLICATION_SCENARIO, message = "不合法的应用场景类型")
    private String applicationScenario;

    /**
     * 人员规模
     */
    @ApiModelProperty("人员规模")
    @TypesRange(type = TypesConstant.PERSONNEL_SIZE, message = "不合法的人员规模类型")
    private String personnelSize;

    /**
     * 所属行业
     */
    @ApiModelProperty("所属行业")
    @TypesRange(type = TypesConstant.INDUSTRY_TYPE, message = "不合法的所属行业类型")
    private String industryType;

    /**
     * 账号有效开始时间
     */
    @ApiModelProperty("账号有效开始时间")
    private Date startTime;

    /**startTime
     * 账号有效截止时间
     */
    @ApiModelProperty("账号有效截止时间")
    @Future
    private Date endTime;

    /**
     * 是否管理员
     */
    @ApiModelProperty("是否管理员")
    private Boolean adminFlag;

    /**
     * 用户角色列表
     */
    @ApiModelProperty("用户角色列表")
    @NotNull
    private List<RoleSidVO> userRoles;

    /**
     * 只改真实名称
     */
    @ApiModelProperty("只改真实名称")
    private Boolean justRealName;

    /**
     * 管理类角色id
     */
    @ApiModelProperty("管理类角色id")
    private List<Long> roleIds;

    /**
     * keycloak修改Flg
     */
    @ApiModelProperty("keycloak修改Flg")
    private boolean keycloakFlg = true;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
