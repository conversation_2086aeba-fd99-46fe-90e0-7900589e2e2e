package cn.com.cloudstar.rightcloud.bss.common.aop;

import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Policy;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractTemplateMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractTemplate;
import cn.com.cloudstar.rightcloud.bss.module.distributor.mapper.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoice;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.RenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.UserRoleConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @desc
 * <AUTHOR>
 * @date 2022/12/27 -4:04 下午
 **/
@Aspect
@Component
@Slf4j
public class DataPermissionAspect {

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private BizContractTemplateMapper bizContractTemplateMapper;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;

    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;

    @Autowired
    private BizDistributorMapper bizDistributorMapper;

    @Autowired
    private IPolicyService iPolicyService;


    @Pointcut("@annotation(cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission)")
    public void pointCut() {

    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        User authUser = AuthUtil.getAuthUser();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        Method targetMethod = findTargetMethod(joinPoint);
        StandardEvaluationContext context = new StandardEvaluationContext();
        //注入数据
        registerArgumentsToContext(joinPoint, targetMethod, context);
        DataPermission dataPermission = AnnotationUtils.findAnnotation(targetMethod, DataPermission.class);
        // 运营管理员拥有所有权限
        if(!isHopeRole(authUser,UserRoleConstants.OPERATE_ADMIN)) {
            switch (dataPermission.resource()) {
                // 子用户相关
                case DELETE_SUB_USER:
                    subUser(context, dataPermission, authUser);
                    break;
                // 用户组相关
                case CREATE_USER_GROUP:
                    userGroup(context, dataPermission, authUser);
                    break;
                case REMOVE_USER_PERMISSIONS:
                    removeUserPermission(context, dataPermission, authUser);
                    break;
                case FIND_PROJECT_USER:
                    findProjectUser(context, dataPermission, authUser);
                    break;
                case REMOVE_PROJECT_USER:
                    removeProjectUser(context, dataPermission, authUser);
                    break;
                case GET_BILLING_ACCOUNT_DETAIL:
                    getBillingAccountDetail(context, dataPermission, authUser);
                    break;
                case UPDATE_DISTRIBUTOR_USER:
                    updateDistirbutorUser(context, dataPermission, authUser);
                    break;
                case UPDATE_DISTRIBUTOR_USER_ROLE:
                    updateDistributorUserRole(context, dataPermission, authUser);
                    break;
                case SELECT_INVOICE_DETAIL:
                    selectInvoiceDetail(context, dataPermission,authUser);
                    break;
                case UNSUBSCRIBE:
                    unSubscribe(context, dataPermission, authUser);
                    break;
                case RENEW_RESOURCE:
                    renewResource(context, dataPermission, authUser);
                    break;
                case BILLING_DETAILS_EXPORT:
                    billingDetailsExport(context, dataPermission, authUser);
                    break;
                case OPERATION_CONTRACT:
                    operationContract(context, dataPermission, authUser);
                    break;
                case DELCONTRACTTEMP:
                    deleteResRdsAccount(context, dataPermission, authUser);
                    break;
                case CREATE_INVOICE:
                    createInvice(context, dataPermission, authUser);
                    break;
                case CREDIT:
                    credit(context, dataPermission, authUser);
                    break;
                case CHARGE:
                    charge(context, dataPermission, authUser);
                break;
                case MODIFY_INQUIRY_PRICE:
                    modifyInquiryPrice(context, dataPermission, authUser);
                break;
                case QUERY_RENEW_DETAIL:
                    queryRenewDetail(context, dataPermission, authUser);
                break;
                case IS_NON_BILL_PRODUCT:
                    isNonBillProduct(context, dataPermission, authUser);
                break;
                case UNSUBSCRIBE_AI_INQUIRYPRICE:
                    unsubscribeAIInquiryPrice(context, dataPermission, authUser);
                break;
                case GET_PRODUCT_OPEN_ORDERS:
                    getProductOpenOrders(context, dataPermission, authUser);
                break;
                case FIND_PROJECTS:
                    findProjects(authUser);
                    break;
                case LIST_RESOURCES:
                    listResources(context, dataPermission, authUser);
                break;
                case EXPORT_SUBUSER:
                    exportSubuser(context, dataPermission, authUser);
                    break;
                case CONFIG_POLICY_ASSERTION:
                    configPolicyAssertion(context,dataPermission,authUser);
                    break;
                default:
                    break;
            }
        }

        return joinPoint.proceed();
    }

    private void configPolicyAssertion(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long policySid = resolveExpress(context, dataPermission.bizId(), Long.class);
        Policy policy = iPolicyService.getById(policySid);
        if ("custom".equals(policy.getPolicyType())) {
            List<Long> longs = orgMapper.selectCustomerOrgSids(policy.getOrgSid());
            if (longs.stream().noneMatch(org -> org.equals(authUser.getOrgSid()))){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
    }

    private void findProjects(User authUser) throws NoSuchMethodException {
        //子用户没有权限移除权限
        if (authUser.getParentSid() != null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    private void exportSubuser(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        BizDistributor distributor = bizDistributorMapper.selectById(authUser.getOrgSid());
        if (!ObjectUtils.isEmpty(distributor)){
            QueryWrapper<Org> orgQueryWrapper = new QueryWrapper<>();
            orgQueryWrapper.lambda().eq(Org::getParentId,authUser.getOrgSid());
            List<Long> orgSids = orgMapper.selectList(orgQueryWrapper).stream().map(Org::getOrgSid).collect(Collectors.toList());
            if (orgSids.stream().noneMatch(id->id.equals(bizId))){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }
    }

    private Method findTargetMethod(ProceedingJoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        return ClassUtils.getUserClass(joinPoint.getTarget())
                .getMethod(joinPoint.getSignature().getName(), parameterTypes);
    }


    /**
     * 注册参数到Context
     */
    private void registerArgumentsToContext(JoinPoint point, Method currentMethod, StandardEvaluationContext context) {
        Object[] arguments = point.getArgs();
        DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
        String[] params = discoverer.getParameterNames(currentMethod);
        if (params != null) {
            for (int len = 0; len < params.length; len++) {
                context.setVariable(params[len], arguments[len]);
            }
        }
    }



    /**
     * 参数解析
     * @param data
     * @param clazz
     * @return
     */
    private <T> T resolveExpress(EvaluationContext context,String data,Class<T> clazz){
        if(StringUtils.isEmpty(data)){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        ExpressionParser parser = new SpelExpressionParser();
        Expression dataExpression = parser.parseExpression(data);
        if(null == dataExpression){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        T returnData = dataExpression.getValue(context,clazz);
        if(null == returnData){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return returnData;
    }


    /**
     * 子用户
     *
     * @param authUser
     */
    private void subUser(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        checkUsers((Arrays.asList(bizId)), authUser.getUserSid());
    }

    /**
     * 用户组
     *
     * @param authUser
     */
    private void userGroup(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        checkGroup(ListUtil.of(bizId), authUser.getOrgSid());
    }

    /**
     * 移除用户权限
     *
     * @param authUser
     */
    private void removeUserPermission(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        List<Long> userSids = resolveExpress(context, dataPermission.bizId(), List.class);
        List<Long> orgSids = new ArrayList<>();
        for (Long userSid : userSids) {
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = userMapper.selectById(userSid);
            if(Objects.nonNull(user)){
                orgSids.add(user.getOrgSid());
            }
        }
        if (orgSids.stream().noneMatch(orgSid -> orgSid.equals(authUser.getOrgSid()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 获取项目用户列表
     *
     * @param authUser
     */
    private void findProjectUser(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        // 同组织
        Org org = orgMapper.selectById(bizId);
        if (!(bizId.equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 移除项目用户
     *
     * @param dataPermission
     * @param authUser
     */
    private void removeProjectUser(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);

        Org org = orgMapper.selectById(bizId);
        if (!(bizId.equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 获取账户详情
     *
     * @param dataPermission
     * @param authUser
     */
    private void getBillingAccountDetail(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        BizBillingAccount billingAccount = bizBillingAccountService.getBillingAccountDetail(bizId);
        if (Objects.isNull(billingAccount)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        // 运营管理员 分销商管理员有权限
        if (!(isHopeRole(authUser, UserRoleConstants.OPERATE_ADMIN) || isHopeRole(authUser, 401L))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        // 分销商管理员看本组织
        if (isHopeRole(authUser, UserRoleConstants.DISTRIBUTOR_ADMIN)) {
            Org org = orgMapper.selectById(billingAccount.getOrgSid());
            if (!(billingAccount.getOrgSid().equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

    }

    /**
     * 编辑分销商账户
     *
     * @param dataPermission
     * @param authUser
     */
    private void updateDistirbutorUser(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        // 运营管理员可编辑
        if (isHopeRole(authUser, UserRoleConstants.OPERATE_ADMIN)) {
            return;
        }
        // 分销商管理员
        if ("04".equals(authUser.getUserType())){
            if (!authUser.getOrgSid().equals(bizId)){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }
    }

    /**
     * 编辑分销商账户角色
     *
     * @param dataPermission
     * @param authUser
     */
    private void updateDistributorUserRole(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User sysUser = sysUserService.selectByPrimaryKey(bizId);
        if (ObjectUtil.isNull(sysUser)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        // 同组织
        Org org = orgMapper.selectById(sysUser.getOrgSid());
        if (!(sysUser.getOrgSid().equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 获取发票详情
     *
     * @param
     * @param authUser
     */
    private void selectInvoiceDetail(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        BizInvoice bizInvoice = bizInvoiceMapper.selectById(bizId);
        if (Objects.isNull(bizInvoice)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        BizBillingAccount bizbillingAcoount = bizBillingAccountService.getById(Long.valueOf(bizInvoice.getAccountId()));
        if (Objects.nonNull(authUser.getOrgSid())) {
            if ("03".equals(authUser.getUserType())) {
                if (!Objects.equals(authUser.getOrgSid(), bizbillingAcoount.getOrgSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            } else {
                QueryWrapper<Org> orgQueryWrapper = new QueryWrapper<>();
                orgQueryWrapper.lambda().eq(Org::getOrgSid, bizbillingAcoount.getOrgSid()).eq(Org::getParentId, authUser.getOrgSid());
                if (orgMapper.selectList(orgQueryWrapper).size() == 0) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
        }
    }


    /**
     * 退订
     *
     * @param dataPermission
     * @param authUser
     */
    private void unSubscribe(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context,dataPermission.bizId(), Long.class);
        projectDeal(bizId,authUser);
    }

    /**
     * 续费
     * @param authUser
     */
    private void renewResource(StandardEvaluationContext context,DataPermission dataPermission, User authUser){
        RenewRequest request =resolveExpress(context,dataPermission.param(), RenewRequest.class);
        if(ObjectUtil.isNull(request)){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if(CollectionUtils.isEmpty(request.getProductInfo())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        projectDeal(Long.parseLong(request.getProductInfo().get(0).getId()),authUser);
    }

    private void projectDeal(Long projectResourceId, User authUser){
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(projectResourceId);
        if (ObjectUtil.isNull(sfProductResource)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        QueryWrapper<ServiceOrderResourceRef> refQuery = new QueryWrapper<>();
        refQuery.lambda().eq(ServiceOrderResourceRef::getResourceId,sfProductResource.getId()).eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType());
        ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(refQuery);
        if (ObjectUtil.isNull(serviceOrderResourceRef)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceOrderDetail orderDetail = serviceOrderDetailService.getById(serviceOrderResourceRef.getOrderDetailId());
        if (ObjectUtil.isNull(orderDetail)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceOrderVo serviceOrderVo = serviceOrderMapper.selectOrderDetailById(orderDetail.getOrderId());
        if (ObjectUtil.isNull(serviceOrderVo)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if ("04".equals(authUser.getUserType())){
            if (!serviceOrderVo.getOwnerId().equals(String.valueOf(authUser.getUserSid()))){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
        if (UserType.PLATFORM_USER.equals(authUser.getUserType()) && Objects.nonNull(authUser.getOrgSid())){
            List<Long> allOrg = orgMapper.selectCustomerOrgSids(authUser.getOrgSid());
            if (!allOrg.contains(serviceOrderVo.getOrgSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
    }

    /**
     * 周期明细数据导出
     *
     * @param dataPermission
     * @param authUser
     */
    private void billingDetailsExport(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        List bizId = resolveExpress(context, dataPermission.bizId(), List.class);
        Query queryDetail = new Query(Criteria.where("billBillingCycleId").in(bizId));
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> costs = mongoTemplate.find(queryDetail, cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class);
        if (StringUtils.equals("console", authUser.getRemark())) {
            // 租户侧  同组织
            for(InstanceGaapCost cost:costs){
                Org org = orgMapper.selectById(cost.getOrgSid());
                if(!(cost.getOrgSid().equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }
        // 运营侧：分销商 同组织
        else if (StringUtils.equals(UserTypeConstants.BSS, authUser.getRemark())) {
            BizBillingAccount bizBillingAccount = bizBillingAccountService.getBizBillingAccountByOrgId(authUser.getOrgSid());
            if (ObjectUtil.isNotNull(bizBillingAccount) && null != bizBillingAccount.getDistributorId()) {
                for(InstanceGaapCost cost:costs){
                    Org org = orgMapper.selectById(cost.getOrgSid());
                    if(!(cost.getOrgSid().equals(authUser.getCompanyId()) || (ObjectUtil.isNotEmpty(org) && authUser.getCompanyId().equals(org.getParentId())))){
                        throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                    }
                }
            }
        }
    }

    /**
     * 修改合同相关
     *
     * @param context        上下文
     * @param dataPermission 数据权限
     * @param authUser       身份验证用户
     */
    private void operationContract(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        String bizId = resolveExpress(context,dataPermission.bizId(), String.class);
        if (Objects.nonNull(authUser.getOrgSid())) {
            cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria();
            criteria.put("contractId",bizId);
            criteria.put("orgSid",authUser.getOrgSid());
            if (orgMapper.selectOrg(criteria) == 0) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
    }


    /**
     * 删除合同模板
     *
     * @param context        上下文
     * @param dataPermission 数据权限
     * @param authUser       身份验证用户
     */
    private void deleteResRdsAccount(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        if (Objects.nonNull(authUser.getOrgSid())) {
            String bizId = resolveExpress(context, dataPermission.bizId(), String.class);
            BizContractTemplate bizContractTemplate = bizContractTemplateMapper.selectById(bizId);
            cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria();
            criteria.put("account", authUser.getAccount());
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users = userMapper.selectByParams(criteria);
            if (validUserRole(users)) return;

            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> distributorUsers =
                    users.stream().filter(user -> user.getRoleSid() == 401L)
                            .collect(Collectors.toList());

            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = distributorUsers.get(0);
            if (!Objects.equals(user.getOrgSid(), authUser.getOrgSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }

            if (!authUser.getAccount().equals(bizContractTemplate.getCreatedBy())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
    }

    private boolean validUserRole(List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users) {
        List<Long> roleIds = users.stream()
                .map(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getRoleSid)
                .collect(Collectors.toList());
        if (roleIds.contains(301L)) {
            return true;
        }
        if (!roleIds.contains(401L)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        return false;
    }

    /**
     * 申请发票
     *
     * @param context        上下文
     * @param dataPermission 数据权限
     * @param authUser       身份验证用户
     */
    private void createInvice(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        String bizId = resolveExpress(context, dataPermission.bizId(), String.class);
        BizBillingAccount billingAccountDetail = bizBillingAccountService.getBillingAccountDetail(Long.valueOf(bizId));
        if (billingAccountDetail == null || !billingAccountDetail.getOrgSid().equals(authUser.getOrgSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 更新信用额度
     *
     * @param context        上下文
     * @param dataPermission 数据权限
     * @param authUser       身份验证用户
     */
    private void credit(StandardEvaluationContext context,DataPermission dataPermission, User authUser) {
        String bizId = resolveExpress(context, dataPermission.bizId(), String.class);
        // 根据参数获取数据所属的客户信息
        BizBillingAccount billingAccountDetail = bizBillingAccountService.getBillingAccountDetail(Long.valueOf(bizId));
        if (Objects.nonNull(authUser.getOrgSid())){
            if (Objects.nonNull(billingAccountDetail.getDistributorId())){
                if (!Objects.equals(authUser.getOrgSid(), billingAccountDetail.getDistributorId())){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            } else {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
    }

    /**
     * 判断客户是否是同一组织
     *
     * @param context        上下文
     * @param dataPermission 数据权限
     * @param authUser       身份验证用户
     */
    private void charge(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        try {
            // 这个只针对与组织管理员和租户管理员有用
            if(authUser.getOrgSid() != null){
                String bizId = resolveExpress(context, dataPermission.bizId(), String.class);
                // 根据参数获取数据所属的客户信息
                BizBillingAccount billingAccountDetail = bizBillingAccountService.getBillingAccountDetail(Long.valueOf(bizId));
                if(ObjectUtil.isNull(billingAccountDetail)){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                List<Long> orgSids = orgMapper.selectCustomerOrgSids(authUser.getOrgSid());
                if(CollectionUtils.isEmpty(orgSids)){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                if (orgSids.stream().noneMatch(o->Long.compare(o,billingAccountDetail.getOrgSid())==0)) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }

        } catch (Exception e) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

    }

    /**
     * 判断资源是不是属于该用户
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void modifyInquiryPrice(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        try {
            ModifyInquiryPriceRequest modifyInquiryPriceRequest = (ModifyInquiryPriceRequest) context.lookupVariable("request");
            String targetType = modifyInquiryPriceRequest.getTargetType();
            if (StringUtils.isNotBlank(targetType) && "SFS2.0".equals(targetType)) {
                return;
            }
        } catch (Exception e) {
        }

        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        projectDeal(bizId,authUser);
    }

    /**
     * 判断资源是不是属于该用户
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void queryRenewDetail(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        if (StrUtil.isEmpty(dataPermission.bizId())) {
            return;
        }
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        projectDeal(bizId,authUser);
    }

    /**
     * 判断资源是不是属于该用户
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void isNonBillProduct(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        projectDeal(bizId,authUser);
    }

    /**
     * 判断资源是不是属于该用户
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void unsubscribeAIInquiryPrice(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        projectDeal(bizId,authUser);
    }

    /**
     * 判断该用户是不是属于该上级组织
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void getProductOpenOrders(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        if (!authUser.getOrgSid().equals(bizId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    /**
     * 判断该用户是不是属于该上级组织
     * @param context 上下文
     * @param dataPermission 数据权限
     * @param authUser 身份验证用户
     */
    private void listResources(StandardEvaluationContext context, DataPermission dataPermission, User authUser) {
        Long bizId = resolveExpress(context, dataPermission.bizId(), Long.class);
        if (!authUser.getOrgSid().equals(bizId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }



    private void checkGroup(List<Long> groupIds, Long rootOrg) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        int count = orgMapper.toCheckGroupCount(groupIds, rootOrg);
        if (count != groupIds.size()) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    private void checkUsers(List<Long> userSids, Long rootUser) {
        if (CollectionUtils.isEmpty(userSids)) {
            return;
        }
        int count = userMapper.toCheckUserCount(userSids, rootUser);
        if (count != userSids.size()) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }


    /**
     * 是否是对应角色
     *
     * @param user
     * @return
     */
    private boolean isHopeRole(User user, Long roleSid) {
        List<UserRole> userRoles = userRoleMapper.selectByParams(new cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria("userSid", user.getUserSid()));
        if (CollectionUtil.isEmpty(userRoles)) {
            return false;
        }
        return userRoles.stream().filter(userRole -> userRole.getRoleSid().equals(roleSid)).findAny().isPresent();
    }


}
