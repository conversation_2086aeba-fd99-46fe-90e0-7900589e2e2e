package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst.BizError;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.ResFederationInst;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResFederationInstService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.CouponService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;

/**
 * <p>
 * 联邦集成资源开通
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
@Slf4j
public class FederationProductOrderServiceImpl extends AbstractOrderService {

    /**
     * 联邦集成资源，只能开通一个
     */
    private final List<String> statusInList = Arrays.asList("apply", "renewing", "available", "expired", "freezed");
    @Autowired
    private OrderService orderService;
    @Autowired
    private ResFederationInstService resFederationInstService;
    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;


    @Override
    public String apply(ApplyServiceVO serviceVO) {
        ApplyEntity applyEntity = before();

        if (applyEntity.getAuthUser().getParentSid() != null) {
            return "只有租户管理员可以开通服务";
        }
        ProductInfoVO productInfoVO = CollectionUtil.getFirst(serviceVO.getProductInfo());
        String productCode = productInfoVO.getProductCode();
        Long userSid = applyEntity.getAuthUser().getUserSid();
        Long orgSid = applyEntity.getAuthUser().getOrgSid();

        if (ProductCodeEnum.federationProducts().contains(productCode)) {
            List<ResFederationInst> list = resFederationInstService.list(new LambdaQueryWrapper<ResFederationInst>()
                                                                                 .eq(ResFederationInst::getProductCode,
                                                                                     productCode)
                                                                                 .eq(ResFederationInst::getOrgSid, orgSid)
                                                                                 .in(ResFederationInst::getStatus, statusInList));
            if (CollectionUtils.isNotEmpty(list)) {
                String productName = productCode;
                ProductCodeEnum productCodeEnum = ProductCodeEnum.toEnum(productCode);
                if (Objects.nonNull(productCodeEnum)) {
                    productName = productCodeEnum.getProductName();
                }
                throw new BizException(productName + "已存在，请勿重复申请。");
            }
        }
        // execute重要代码
        ((FederationProductOrderServiceImpl ) AopContext.currentProxy()).execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);

        ServiceOrderDetail orderDetails = applyEntity.getOrderDetails().get(0);
        if (Objects.nonNull(result)) {
            applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(result)));
        } else {
            ServiceOrderResourceRef resourceRef = serviceOrderResourceRefService.getOrderResourceRefByDetailId(
                    orderDetails.getId());
            if (Objects.nonNull(resourceRef)) {
                applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(resourceRef.getResourceId())));
            }

        }

        after(applyEntity);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.execute(serviceVO, applyEntity);
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ServiceOrder order = applyEntity.getOrder();
        RestResult restResult = orderService.startProcessByOrder(order.getId());
        //bss需要对调用oss的结果做处理，否则会出现订单有，审批单没有等情况。
        if (Objects.nonNull(restResult)){
            if (!restResult.getStatus()){
                // 将优惠券状态重置为未使用
                BizCouponResource bizCouponResource = couponService.getCouponResourceByOrderId(order.getId());
                if (Objects.nonNull(bizCouponResource)) {
                    couponService.updateCouponStatus(bizCouponResource.getCouponSid(), CouponStatusEnum.UNUSED.getCode());
                    couponService.updateCouponUsed(bizCouponResource.getCouponSid());
                }
                serviceOrderMapper.deleteById(order.getId());
                QueryWrapper<SfProductResource> resourceQueryWrapper = new QueryWrapper<>();
                resourceQueryWrapper.eq("service_order_id",order.getId());
                List<SfProductResource> resources = sfProductResourceMapper.selectList(resourceQueryWrapper);
                sfProductResourceMapper.delete(resourceQueryWrapper);

                if (CollectionUtils.isNotEmpty(resources)) {
                    LambdaQueryWrapper<ResFederationInst> queryWrapper =
                            new LambdaQueryWrapper<ResFederationInst>().eq(ResFederationInst::getResourceId, resources.get(0).getId());
                    resFederationInstService.remove(queryWrapper);
                }
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(restResult));
                if (BizError.BIZ_ERROR.getCode() == jsonObject.getInteger("code")) {
                    throw new cn.com.cloudstar.rightcloud.common.exception.BizException(restResult.getMessage().toString());
                }

                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
            }
        }

        return JSONUtil.toJsonStr(restResult.getData());
    }
}
