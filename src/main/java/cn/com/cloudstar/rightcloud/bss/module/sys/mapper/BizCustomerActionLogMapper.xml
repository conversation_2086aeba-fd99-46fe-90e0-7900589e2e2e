<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.sys.mapper.BizCustomerActionLogMapper">
    <resultMap id="BaseResultMap"
        type="cn.com.cloudstar.rightcloud.core.pojo.dto.system.BizCustomerActionLog">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="org_sid" property="orgSid" jdbcType="BIGINT" />
        <result column="user_sid" property="userSid" jdbcType="BIGINT" />
        <result column="op_type" property="opType" jdbcType="VARCHAR" />
        <result column="op_detail" property="opDetail" jdbcType="VARCHAR" />
        <result column="op_date" property="opDate" jdbcType="TIMESTAMP" />
        <result column="op_user" property="opUser" jdbcType="BIGINT" />
        <result column="org_Name" property="orgName" jdbcType="VARCHAR" />
        <result column="user_Name" property="userName" jdbcType="VARCHAR" />
        <result column="op_User_Name" property="opUserName" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Example_Where_Clause" >
        <trim prefix="where" prefixOverrides="and|or" >
        <if test="condition.orgSid != null" >
            and A.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.userSid != null" >
            and A.user_sid = #{condition.userSid}
        </if>
        <if test="condition.orgName != null">
            and C.org_name like concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.userName != null">
            and B.real_name like concat('%', #{condition.userName}, '%')
        </if>
        <if test="condition.realNameHash != null">
            and B.real_name_hash = #{condition.realNameHash}
        </if>
        <if test="condition.opType != null">
            and A.op_type = #{condition.opType}
        </if>
        <if test="condition.opUserName != null">
            and D.real_name like concat('%', #{condition.opUserName}, '%')
        </if>
        </trim>
    </sql>
    <select id="selectByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
            A.id,
            A.org_sid,
            A.user_sid,
            A.op_type,
            A.op_detail,
            A.op_date,
            A.op_user,
            B.real_name as user_name,
            C.org_name as org_Name,
            ifnull(D.account, '系统') as op_User_Name
        FROM biz_customer_action_log A
        LEFT JOIN sys_m_user B ON A.user_Sid = B.user_sid
        LEFT JOIN sys_m_org C ON A.org_sid = C.org_sid
        LEFT JOIN sys_m_user D ON A.op_user = D.user_sid
        <include refid="Example_Where_Clause" />
        <if test="condition.orderByClause != null" >
            order by ${condition.orderByClause}
        </if>
    </select>
</mapper>
