/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo;

import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/04/24 17:34
 */
@Data
@ApiModel("密码策略")
public class PasswordPolicyDTO {
    @ApiModelProperty("最小长度")
    private int minLength;

    @ApiModelProperty("密码包含规则")
    private List<String> charactorType;

    @ApiModelProperty("不同字符数限制")
    private int charactorLimit;

    @ApiModelProperty("常用密码剔除")
    private List<String> ruleOut;

    @ApiModelProperty("登陆失败锁定开启状态")
    private boolean loginfailureEnable;

    @ApiModelProperty("最大失败数")
    private int loginfailureCount;

    @ApiModelProperty("账号有效期开启状态")
    private boolean accountValidity;

    @ApiModelProperty("有效天数")
    private int expireTime;

    @ApiModelProperty("密码有效期")
    private Long pwdExpireTime;

    @ApiModelProperty("密码有效期开启状态")
    private Boolean pwdExpireTimeValidity;

    @ApiModelProperty("密码最少使用天数")
    private Long pwdLeastUsedDay;

    @ApiModelProperty("密码不能与前N个历史密码重复")
    private Long pwdRepeatNum;
    private String secAuth;

    public boolean getLoginfailureEnable() {
        if (ObjectUtils.isEmpty(this.loginfailureEnable)) {
            return Constants.ENABLED;
        }
        return this.loginfailureEnable;

    }

    public boolean getAccountValidity() {
        if (ObjectUtils.isEmpty(this.accountValidity)) {
            return Constants.ENABLED;
        }
        return this.accountValidity;

    }

    public int getLoginfailureCount() {
        if (ObjectUtils.isEmpty(this.loginfailureCount)) {
            return Constants.FAILURE_COUNT;
        }

        return this.loginfailureCount;
    }

    public int getExpireTime() {
        if (ObjectUtils.isEmpty(this.expireTime)) {
            return Constants.EXPIRE_TIME;
        }

        return this.expireTime;
    }
}
