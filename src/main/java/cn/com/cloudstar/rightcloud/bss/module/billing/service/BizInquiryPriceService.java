/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service;

import java.math.BigDecimal;
import java.util.List;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.constants.InquiryPriceConstants;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.InquiryRenewPriceRequestDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ModifyInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubscribeInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryBase;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryBaseVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServiceAccountPriceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServicePriceDetailNewVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServicePriceDetailVO;

/**
 * <AUTHOR>
 * Created on 2019/11/5
 */
public interface BizInquiryPriceService extends InquiryPriceConstants {

    /**
     * 计算资源价格
     * @param inquiryBase
     * @return
     */
    @Deprecated
    InquiryPriceResult calculateResourcePrice(InquiryBase inquiryBase);

    /**
     * 具体的询价
     * @param inquiryBase
     * @return
     */
    InquiryPriceResult inquiryPrice(InquiryBaseVO inquiryBase);

    /**
     * 公有云询价
     * @param inquiryPriceResult
     * @param inquiryBase
     * @param cloudEnv
     * @param floatingRatio
     * @return
     */
    InquiryPriceResult inquiryPublicCloudPrice(InquiryPriceResult inquiryPriceResult, InquiryBaseVO inquiryBase,
        CloudEnv cloudEnv, BigDecimal floatingRatio);

    /**
     * 产品询价
     * @param request
     * @return
     */
    List<InquiryPriceResponse> inquiryPrice(ApplyServiceVO request);

    /**
     * 组装价格
     * @param inquiryPriceResult
     * @param instanceChargeType
     * @return
     */
    InquiryPriceResponse convertPriceResponse(InquiryPriceResult inquiryPriceResult, String instanceChargeType);

    /**
     * 服务询价
     * @param serviceId
     * @param period
     * @return
     */
    List<BizBillingPriceVO> calculateServicePrice(Long serviceId, BigDecimal period);

    /**
     * 获取服务价格详情
     * @param serviceId
     * @return
     */
    ServicePriceDetailVO getServicePriceDetail(Long serviceId);

    /**
     * 获取云账号定价详情
     * @param accountId
     * @return
     */
    ServiceAccountPriceDetailVO getAccountPriceDetail(Long accountId);

    /**
     * 获取服务价格
     * @param inquiryPriceResult
     * @param serviceId
     * @return
     */
    InquiryPriceResult generateServicePrice(InquiryPriceResult inquiryPriceResult, Long serviceId, BigDecimal period);

    /**
     * 获取服务价格详情
     * @param serviceId
     * @return
     */
    ServicePriceDetailNewVO getServicePriceDetailNew(Long serviceId, String userSid);

    /**
     * 获取云账号定价详情
     * @param accountId
     * @return
     */
    ServiceAccountPriceDetailVO getAccountPriceDetailNew(Long accountId, String resourceType, String region, String userSid);

    /**
     * 产品计费预览
     */
    ServicePriceDetailNewVO overviewServicePrice(Long serviceId, List<Long> chargeIds, String resourceType);

    UnsubInquiryPriceVO unsubInquiryPrice(String id, String type);

    ModifyInquiryPriceVO modifyInquiryPrice(ModifyInquiryPriceRequest request);

    InquiryPriceResponse inquiryRenewPrice(InquiryRenewPriceRequestDTO request);

    InquiryPriceResponse getInquiryPriceResponse(InquiryRenewPriceRequestDTO request, ProductInfo productInfo);

    /**
     * 多资源计费
     * @param priceRequest
     * @return
     */
    List<InquiryPriceResponse> inquiryRenewPriceList(InquiryRenewPriceRequestDTO priceRequest);

    /**
     * AI专属资源池退订询价
     * @param id
     * @return
     */
    UnsubscribeInquiryPriceVO unsubInquiryAIPrice(String id);

    void inquiryUnsubscribePriceList(String id, UnsubInquiryPriceVO vo);

    /**
     * 查询退订价格包含附属资源
     * @param id
     * @return
     */
    void inquiryUnsubscribePriceContainSubsidiaryResource(String id, UnsubInquiryPriceVO vo);

    void inquiryUnsubscribePriceRdsResource(String id, UnsubInquiryPriceVO vo);

    /**
     * 代客订购询价、ma开通询价、 hpc开通询价
     * @param applyServiceVO 参数
     * @return
     */
    List<InquiryPriceResponse> inquiryProductPrice(ApplyServiceVO applyServiceVO);

    /**
     * 多资源分摊优惠卷
     * @param inquiryPriceResponses
     * @param couponPrice
     * @return
     */
    void multipleProductCouponPrice(List<InquiryPriceResponse> inquiryPriceResponses, BigDecimal couponPrice);
    /**
     * 根据订单，创建询价请求
     * @param serviceOrder
     * @return
     */
    ApplyServiceRequest createPostPaidApplyServiceRequst(ServiceOrder serviceOrder);

    UnsubscribeInquiryPriceVO unsubInquiryResPrice(String id);
}
