/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;

@Data
@TableName("sys_m_user")
@EncryptDecryptClass
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt = true)
public class User extends BaseCCSP implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户SID
     */
    @TableId
    private Long userSid;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    @CCSPIntegralityHashAndVerify(segment = 10)
    private String password;



    /**
     * 真实姓名
     */
    @DesensitizationField(type = DesensitizedType.NAME)
    @CCSPIntegralityHashAndVerify(segment = 5)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String realName;
    private String realNameHash;

    /**
     * 性别 0:男 1:女
     */
    private Integer sex;

    /**
     * 邮箱
     */
    @EncryptDecryptField
    @DesensitizationField(type = DesensitizedType.EMAIL)
    @CCSPIntegralityHashAndVerify(segment = 1)
    @CCSPEncryptDecrypt
    private String email;
    private String emailHash;

    /**
     * 手机
     */
    @EncryptDecryptField
    @DesensitizationField(type = DesensitizedType.PHONE)
    @CCSPIntegralityHashAndVerify(segment = 2)
    @CCSPEncryptDecrypt
    private String mobile;
    private String mobileHash;

    /**
     * 职务头衔
     */
    private String title;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 用户状态（0:禁用，1:有效，2:锁定）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码错误次数
     */
    private Integer errorCount;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 上次登录IP地址
     */
    private String lastLoginIp;

    /**
     * 账号有效开始时间
     */
    private Date startTime;

    /**
     * 账号有效开始时间
     */
    private Date endTime;

    /**
     * 服务限制数量
     */
    private Integer serviceLimitQuantity;

    /**
     * 申请理由
     */
    private String applyReason;
    /**
     * 身份证正面图片路径
     *
     */
    @CCSPIntegralityHashAndVerify(segment = 3)
    private String idCardFront;
    /**
     * 身份证反面图片路径
     */
    @CCSPIntegralityHashAndVerify(segment = 4)
    private String idCardReverse;

    /**
     * 最大短信数
     */
    private Integer smsMax;

    private String uuid;

    /**
     * 用户偏好主题
     */
    private String skinTheme;

    /**
     * 第三方认证绑定ID
     */
    @CCSPIntegralityHashAndVerify(segment = 9)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String authId;

    /**
     * 身份证名称
     */
    private String authName;

    /**
     * 账户认证类型 1. local 2. ad
     */
    private String authType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * open ID
     */
    private String openId;

    /**
     * 微信头像
     */
    private String avatarUrl;

    /**
     * 微信用户所在省
     */
    @CCSPIntegralityHashAndVerify(segment = 6)
    private String province;

    /**
     * 微信用户所在城市
     */
    @CCSPIntegralityHashAndVerify(segment = 7)
    private String city;

    /**
     * 微信用户所在国家
     */
    @CCSPIntegralityHashAndVerify(segment = 8)
    private String country;

    /**
     * 微信账号名称
     */
    private String wechatName;

    /**
     * IAM域ID
     */
    private String domainId;

    /**
     * IAM用户ID
     */
    private String iamId;

    /**
     * IAM密码过期时间
     */
    private String passwordExpiresAt;

    /**
     * IAM是否强制重置密码
     */
    private String forceresetpwd;

    /**
     * IAM默认项目ID
     */
    private String defaultProjectId;

    /**
     * IAM最后访问项目ID
     */
    private String lastProjectId;

    /**
     * IAM密码强度
     */
    private String pwdStrength;


    private Long parentSid;

    /**
     * 分销商ID
     */
    @TableField(exist = false)
    private Long distributorId;

    @TableField(exist = false)
    private Long roleSid;

    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private String originType;

    @TableField(exist = false)
    private String skip2FA;


    /**
     * 冻结状态
     */
    private String freezeStatus;

    /**
     * 认证状态
     */
    private String certificationStatus;

    /**
     * 冻结类型
     */
    private String unfreezeType;

    /**
     * 密码有效结束时间
     */
    private Date pwdEndTime;

    private String businessTag;

    @ApiModelProperty("是否预开通")
    @TableField(exist = false)
    private Boolean isPreOpen;

    /**
     * 隐私确认记录
     */
    private Integer policyAgreeSign;

    /**
     * 隐私同意时间
     */
    private Date policyAgreeTime;

    @TableField(exist = false)
    private List<Long> groupIds;

    private String refUserId;

    /**
     * 映射组织ID
     */
    @TableField(exist = false)
    private String refOrgId;


    @TableField(exist = false)
    private String userPlatform;

}
