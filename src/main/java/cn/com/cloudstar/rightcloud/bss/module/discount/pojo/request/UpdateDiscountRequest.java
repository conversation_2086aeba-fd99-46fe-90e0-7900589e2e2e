/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.TypesRange;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/19.
 */
@ApiModel(description = "编辑折扣")
@Data
public class UpdateDiscountRequest {

    /**
     * 折扣sid
     */
    @ApiModelProperty("折扣SID")
    @NotNull
    private Long discountSid;

    /**
     * 折扣名称
     */
    @ApiModelProperty("名称")
    @NotBlank
    @NotIllegalString
    @SafeHtml
    private String discountName;

    /**
     * 折扣类型
     */
    @ApiModelProperty("折扣类型 platform:平台折扣，customer:客户折扣")
    @NotBlank
    private String discountType;

    /**
     * 用户sid
     */
    @ApiModelProperty("用户SID")
    private Long userSid;

    /**
     * 折扣来源
     */
    @ApiModelProperty("折扣来源 [contract]来源合同 [discount] 来源折扣")
    private String originType;

    /**
     * 状态
     */
    @ApiModelProperty("状态 0 禁用  1 启用")
    private Integer status;

    /**
     * 适用环境
     */
    @ApiModelProperty("适用环境 适用的云环境，多个以，分隔")
    @javax.validation.constraints.NotBlank
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    @EnumValue(strValues = {"HCSO", "FusionDirector", "HPCOffline"}, message = "适用环境参数异常")
    private String cloudEnvScope;

    /**
     * 适用产品
     */
    /**
     * 适用产品
     */
    @ApiModelProperty("适用产品 适用的产品，多个以，分隔。[ecs][disk][floatingip]")
    @javax.validation.constraints.NotBlank
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    @SafeHtml
    @TypesRange(type = TypesConstant.DISCOUNT_PRODUCT_TYPE, message = "该产品不存在")
    private String productScope;

    /**
     * 应用范围
     */
    @ApiModelProperty("应用范围 支持[quantity]按照数量，[money]按照结算时最终金额，[unlimited]无限制，直接享受折扣")
    @javax.validation.constraints.NotBlank
    @EnumValue(strValues = {"quantity", "money", "time", "unlimited"}, message = "应用范围参数异常")
    private String scopeType;

    /**
     * 范围值
     */
    @ApiModelProperty("范围值 1. 支持范围写法，如1-+，1-1000，结束值+代表无限制 2. 支持固定数字值")
    @NotBlank
    private String scopeValue;

    /**
     * 折扣系数
     */
    @ApiModelProperty("折扣系数 0-1，最多两位小数")
    @NotNull
    private BigDecimal discountRatio;

    /**
     * 开始时间
     */
    @ApiModelProperty("生效时间")
    @NotNull
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("失效时间")
    @NotNull
    private Date endTime;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    @SafeHtml
    private String description;
}
