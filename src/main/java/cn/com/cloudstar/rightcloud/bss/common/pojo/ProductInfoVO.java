/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductConfigDesc;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.TypesRange;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/10 22:44
 */
@Data
@ApiModel("产品详情")
public class ProductInfoVO {
    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    @TypesRange(type = TypesConstant.CHARGE_RESOURCE_TYPE)
    private String productCode;

    /**
     * 云环境id
     */
    @ApiModelProperty("云环境ID")
    private String cloudEnvId;

    /**
     * 资源id
     */
    @ApiModelProperty("资源ID")
    private String id;

    /**
     * 计费类型
     */
    @ApiModelProperty("计费类型")
    @TypesRange(type = TypesConstant.INSTANCE_CHARGE_TYPE)
    private String chargeType;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @Min(0)
    private Integer quantity;

    /**
     * 时长单位
     */
    @ApiModelProperty("时长单位")
    private String priceUnit;

    /**
     * 时长
     */
    @ApiModelProperty("时长")
    @Min(0)
    private BigDecimal period;

    /**
     * 产品描述
     */
    @ApiModelProperty("产品描述")
    @JsonDeserialize(using = ProductConfigDescDeserializer.class)
    private ProductConfigDesc productConfigDesc;

    /**
     * 资源id
     */
    @ApiModelProperty("资源ID")
    private String resourceId;

    /**
     * 产品id
     */
    @ApiModelProperty("产品ID")
    private String serviceId;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal price;


    /**
     * 统一结束时间
     */
    @ApiModelProperty("统一结束时间")
    @Min(1)
    @Max(28)
    private Integer unifyDate;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

    /**
     * 订单名称
     */
    @ApiModelProperty("订单名称")
    private String name;

    public BigDecimal getPrice() {
        return Objects.isNull(period) ? BigDecimal.ZERO : this.price;
    }
}
