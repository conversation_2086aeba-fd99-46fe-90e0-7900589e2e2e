/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.controller;


import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeAccountCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeAccountCouponResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeStatisticsCouponResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.ResultUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BL.BL01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 账户下的优惠劵
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@RestController
@RequestMapping("/coupons/accounts")
@Api("账户下的优惠劵")
public class BizCouponAccountController {

    @Autowired
    private IBizCouponAccountService bizCouponAccountService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private OrgService orgService;

    /**
     * 查询账户下的优惠劵(云资源)
     *
     * @param request 查询账户下优惠劵请求体
     * @return {@code IPage<DescribeAccountCouponResponse>}
     */
    @AuthorizeBss(action = AuthModule.CB.CB19)
    @GetMapping
    @ApiOperation("查询账户下的优惠劵")
    public IPage<DescribeAccountCouponResponse> listCoupons(DescribeAccountCouponRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo.getParentSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (Objects.isNull(request.getStartTimeLe()) && Objects.isNull(request.getEndTimeGe())) {
            request.setStartTimeLe(new Date());
            request.setEndTimeGe(new Date());
        }

        return bizCouponAccountService.listCoupons(request);
    }

    /**
     * 查询账户下的优惠劵,低代码不能接收page(云资源)
     *
     * @param request 查询账户下优惠劵请求体
     * @return {@code IPage<DescribeAccountCouponResponse>}
     */
    @GetMapping("/myList")
    @ApiOperation("查询账户下的优惠劵")
    public List<DescribeAccountCouponResponse> couponlist(DescribeAccountCouponRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo.getParentSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (Objects.isNull(request.getStartTimeLe()) && Objects.isNull(request.getEndTimeGe())) {
            request.setStartTimeLe(new Date());
            request.setEndTimeGe(new Date());
        }

        return bizCouponAccountService.couponList(request);
    }

    /**
     * 查询账户下的优惠劵(优惠管理)
     *
     * @param request 查询账户下优惠劵请求体
     * @return {@code IPage<DescribeAccountCouponResponse>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC02)
    @GetMapping("/pt")
    @ApiOperation("查询账户下的优惠劵")
    @ListenExpireBack
    public IPage<DescribeAccountCouponResponse> preferentialTreatmentListCoupons(DescribeAccountCouponRequest request) {
        return bizCouponAccountService.listCoupons(request);
    }

    /**
     * 【Since v2.5.0】代客订购下查询账户下的优惠劵
     *
     * @param request 请求
     *
     * @return {@link IPage}<{@link DescribeAccountCouponResponse}>
     */
    @AuthorizeBss(action = AuthModuleOss.BQ.BQ010702)
    @GetMapping("/list")
    @ApiOperation("代客订购下查询账户下的优惠劵")
    public IPage<DescribeAccountCouponResponse> listCouponsForOss(DescribeAccountCouponRequest request) {
     return bizCouponAccountService.listCoupons(request);
    }
    /**
     * [INNER API] 查询账户下的优惠劵
     *
     * @param request 查询账户下优惠劵请求体
     * @return {@code IPage<DescribeAccountCouponResponse>}
     */
    @RejectCall
    @GetMapping("/feign")
    @ApiOperation("查询账户下的优惠劵")
    public IPage<DescribeAccountCouponResponse> listCouponsByFeign(DescribeAccountCouponRequest request) {
        if (ObjectUtils.isEmpty(request.getAccountId())){
            BizBillingAccount account =
                    bizBillingAccountService.getByEntityIdAndUserId(RequestContextUtil.getEntityId(), request.getUserSid());
            if (ObjectUtils.isEmpty(account)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1757129758));
            }
            request.setAccountId(account.getId());
            request.setUserSid(null);
        }
        // 分销商权限检查
        if (Objects.nonNull(request.getAccountId())) {
            orgService.checkDistributorRole(null, request.getAccountId());
        }
        return bizCouponAccountService.listCoupons(request);
    }


    /**
     * 删除账户下的优惠劵
     *【Since v2.5.0】
     * @param id id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BL01.BL01_COUPON_COMMON + "," + BL01.BL0103 + "," + BQ.BQ_COMMON)
    @Deprecated
    @ApiOperation("删除账户下的优惠劵")
    public RestResult deleteCoupon(@PathVariable("id") Long id) {
        return ResultUtil.ofDelete(bizCouponAccountService.removeById(id));
    }

    /**
     * 统计账户下优惠劵的有效数量和优惠金额
     *
     * @return {@code DescribeStatisticsCouponResponse}
     */
    @AuthorizeBss(action = AuthModule.CC.CC02)
    @GetMapping("/statistics")
    @ApiOperation("统计账户下优惠劵的有效数量和优惠金额")
    public DescribeStatisticsCouponResponse statisticsCoupons() {
        return bizCouponAccountService.statisticsCoupons();
    }
}

