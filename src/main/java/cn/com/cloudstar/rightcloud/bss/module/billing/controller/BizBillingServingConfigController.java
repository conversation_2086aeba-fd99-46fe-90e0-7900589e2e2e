/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategy;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyServing;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.DescribeBizBillingStrategyServingRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpdateBizBillingStrategyServingRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBillingStrategyServingResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBillingStrategyServingSimpleResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD04;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务计费配置管理
 *
 * <AUTHOR>
 * Created on 2019/10/30
 */
@Api(tags = "服务计费配置管理")
@Slf4j
@RestController
@RequestMapping("/billing/strategy")
public class BizBillingServingConfigController {

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private BizBillingStrategyServingService servingService;

    @Autowired
    private BizBillingStrategyOrgService strategyOrgService;

    @Autowired
    private BizBillingStrategyService strategyService;

    @Autowired
    private BizBillingServingConfigService bizBillingServingConfigService;

    @Autowired
    private BizBillingTariffSpecChargeService chargeService;

    /**
     * 查询服务计费策略配置
     *
     * @param request 服务名称检索条件请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD0402)
    @ApiOperation("查询服务计费策略配置")
    @GetMapping("/serving")
    public RestResult<List<DescribeBillingStrategyServingSimpleResponse>> queryBillingServingConfig(DescribeBizBillingStrategyServingRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }
        QueryWrapper<ServiceCategory> categoryQueryWrapper = new QueryWrapper<>();
        categoryQueryWrapper.eq("publish_status", "succeed");
        categoryQueryWrapper.orderByDesc("created_dt");
        if (!StringUtils.isEmpty(request.getNameLike())) {
            categoryQueryWrapper.like("product_name", request.getNameLike());
        }
        if (!StringUtils.isEmpty(request.getServiceType())) {
            categoryQueryWrapper.eq("service_type", request.getServiceType());
        }

        List<DescribeBillingStrategyServingResponse> strategyServingResponses =
                bizBillingServingConfigService.queryBillingServingConfig(categoryQueryWrapper, request.getCategoryId());
        List<DescribeBillingStrategyServingResponse> collect = strategyServingResponses.stream()
                                                                                       .filter(item -> "DRP".equals(
                                                                                               item.getServiceType())
                                                                                               || "ModelArts".equals(
                                                                                               item.getServiceType())
                                                                                               || "OBS".equals(
                                                                                               item.getServiceType()))
                                                                                       .collect(
                                                                                               Collectors.toList());

        return new RestResult(BeanConvertUtil.convert(strategyServingResponses, DescribeBillingStrategyServingSimpleResponse.class));
    }

    /**
     * 保存服务计费策略配置
     *
     * @param request 服务计费策略配置请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD040202)
    @ApiOperation("保存服务计费策略配置")
    @PutMapping("/serving")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.resourceType",
            resource = OperationResourceEnum.PRODUCT_BILLING_CONFIGURATION_PRICING,bizId = "#request.serviceId",param = "#request")
    public RestResult saveBillingServingConfig(@Validated @RequestBody UpdateBizBillingStrategyServingRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getServiceId());
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1776479847));
        }
        if (!serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        // 修改定价信息时，检查产品上线
        String productCode = bizBillingServingConfigService.checkServingUsingStatus(serviceCategory);
        if (Strings.isNotBlank(productCode)) {
            log.info("该资源计费所属产品:[{}],正在被使用，请先下架产品！",ProductCodeEnum.keyFromDesc(productCode));
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2000872691));
        }
        // 计费资源检查
        if (!Objects.equals(serviceCategory.getServiceType(), request.getResourceType()) && !"none".equals(request.getResourceType())) {
            log.error("当前产品：[{}]，配置资源[{}]", serviceCategory.getServiceType(), request.getResourceType());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_857666303));
        }

        // 服务计费策略有效性验证
        if (null != request.getBillingStrategyId()) {
            BizBillingStrategy bizBillingStrategy = strategyService.getById(request.getBillingStrategyId());
            if (null == bizBillingStrategy) {
                log.error("不合法的服务计费策略");
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }

        // 规格定价有效性验证
        if (StringUtil.isNotEmpty(request.getSpecChargeIds())) {
            Arrays.stream(request.getSpecChargeIds().split(","));
            BizBillingStrategy bizBillingStrategy = strategyService.getById(request.getBillingStrategyId());
            if (null == bizBillingStrategy) {
                log.error("不合法的服务计费策略");
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }


        QueryWrapper<BizBillingStrategyServing> servingQueryWrapper = new QueryWrapper<>();
        servingQueryWrapper.eq("service_id", request.getServiceId());
        List<BizBillingStrategyServing> strategyServings = servingService.list(servingQueryWrapper);
        if (CollectionUtils.isEmpty(strategyServings)) {
            BizBillingStrategyServing strategyServing = BeanConvertUtil.convert(request, BizBillingStrategyServing.class);
            WebUserUtil.prepareInsertParams(strategyServing);
            strategyServing.setBillingStrategyId(request.getBillingStrategyId());
            servingService.save(strategyServing);
        } else {
            BizBillingStrategyServing strategyServing = strategyServings.get(0);
            WebUserUtil.prepareUpdateParams(strategyServing);
            BeanUtils.copyProperties(request, strategyServing);
            // SFS 不存在额外计费和产品服务费
            if (ProductCodeEnum.SFS.getProductType().equals(serviceCategory.getServiceType())) {
                strategyServing.setSpecChargeIds(null);
                strategyServing.setExtraCharge(false);
            }
            // 是否需要额外费用为空时插入false
            if (Objects.isNull(strategyServing.getExtraCharge())) {
                strategyServing.setExtraCharge(false);
            }
            servingService.update(strategyServing);
        }

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }
}
