/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import lombok.Data;

/**
 * <p>
 * 订单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
@Data
public class ServiceOrderDetail  extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 申请单ID
     */
    private Long orderId;

    /**
     * 计费类型：包年包月、按量付费
     */
    private String chargeType;

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 服务属性
     */
    private String serviceAttr;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务配置
     */
    private String serviceConfig;

    private Long orgSid;

    /**
     * 状态
     */
    private String status;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 申请时长
     */
    private Integer duration;

    @CCSPIntegralityHashAndVerify(segment = 1,defineBigDecimalScale = true, bigDecimalScale = 5)
    private BigDecimal price;

    @CCSPIntegralityHashAndVerify(segment = 2,defineBigDecimalScale = true, bigDecimalScale = 5)
    private BigDecimal amount;

    @CCSPIntegralityHashAndVerify(segment = 3,defineBigDecimalScale = true, bigDecimalScale = 5)
    private BigDecimal originalCost;

    @CCSPIntegralityHashAndVerify(segment = 4,defineBigDecimalScale = true, bigDecimalScale = 5)
    private BigDecimal discount;

    /**
     * 变更服务实例ID
     */
    private Long serviceInstanceId;

    /**
     * 退订时间
     */
    private Date endTime;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 配置展示
     */
    private String productConfigDesc;


    /**
     * 订单资源开始时间
     */
    private Date startTime;

    /**
     * 折扣系数
     */
    private BigDecimal discountRatio;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;

    /**
     * 资源价格
     */
    private BigDecimal resourcePrice;

    /**
     * 产品服务价格
     */
    private BigDecimal servicePrice;

    /**
     * 额外配置费用
     */
    private BigDecimal oncePrice;

    /**
     * 审批类型 开发训练,上线部署
     */
    private String applyType;
    /**
     * 客户自定义标志
     */
    private Boolean customFlag;

}
