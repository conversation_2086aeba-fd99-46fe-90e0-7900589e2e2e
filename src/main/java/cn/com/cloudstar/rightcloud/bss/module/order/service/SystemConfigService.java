package cn.com.cloudstar.rightcloud.bss.module.order.service;

import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeSysConfigByTypeListRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;

import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;

/**
 * <AUTHOR>
 * @date 2022/6/21 15:45
 */
@FeignClient(value = "https://cmp-oss:8080", configuration = FeignConfig.class,path = "/api/v1/oss")
public interface SystemConfigService {

    @GetMapping("/configs/multi/feign")
    RestResult getConfigsByTypeList(@SpringQueryMap DescribeSysConfigByTypeListRequest request);

}
