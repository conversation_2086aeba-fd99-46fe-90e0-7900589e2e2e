/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;

/**
 * DESC:云环境
 *
 * <AUTHOR>
 * @date 2020/3/19 14:47
 */
public enum CloudEnvEnum {
    // 公有云
    /**
     * 全部
     */
    ALL("全部", "all"),
    /**
     * 阿里云
     */
    ALIYUN("阿里云", "<PERSON>yun"),
    /**
     * 腾讯云
     */
    QCLOUD("腾讯云", "Qcloud"),

    /**
     * AWS
     */
    AWS("AWS", "Aws"),

    /**
     * 华为云
     */
    HUAWEICLOUD("华为云", "HuaweiCloud"),

    /**
     * 微软云
     */
    AZURE("微软云", "Azure"),

    // 私有云
    /**
     * VMWARE
     */
    VMWARE("VMWARE", "VMware"),

    /**
     * OpenStack
     */
    OPEN_STACK("OpenStack", "OpenStack"),

    /**
     * OpenStack
     */
    ESCLOUD("ESCloud", "ESCloud"),

    /**
     * 华为私有云
     */
    FUSIONCOMPUTE("华为私有云", "FusionCompute"),
    /**
     * HCSO
     */
    HCSO("HCSO", "HCSO");
    private String desc;
    private String key;

    CloudEnvEnum(String desc, String key) {
        this.desc = desc;
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static List<String> transformDesc(String... keys) {
        if (keys == null) {
            return Collections.emptyList();
        }
        CloudEnvEnum[] components = CloudEnvEnum.values();
        List<String> descList = Lists.newArrayList();
        for (String s : keys) {
            for (CloudEnvEnum component : components) {
                if (Objects.equals(s, component.key)) {
                    descList.add(component.desc);
                }
            }
        }
        return descList.stream().distinct().collect(Collectors.toList());
    }

    public static String keyFromDesc(String key) {
        if (key == null) {
            return StrUtil.EMPTY;
        }

        for (CloudEnvEnum value : CloudEnvEnum.values()) {
            if (value.key.equals(key)) {
                return value.desc;
            }
        }

        return StrUtil.EMPTY;
    }
}
