/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.mapper;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DrpProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
@Repository
public interface ServiceOrderMapper extends BaseMapper<ServiceOrder> {

    List<ServiceOrderVo> selectCloudProductInfo(@Param(value="accountIds") List<Long> accountIds);

    List<ServiceOrderVo> selectCloudProductTotal();

    List<ServiceOrderVo> selectReleaseIngDetail(@Param(value="orderIds")List<String> orderIds);

    /**
     * 根据条件查询 订单和订单明细
     * @param criteria
     * @return
     */
    List<ServiceOrderVo> selectServiceOrderAndDetail(Criteria criteria);

    List<Long> selectProductResourceIdListByChargeType(String chargeType);

    List<Long> selectProductResourceIdListByOrderSn(String orderSn);

    List<String> selectOrderSnByEntityId(@Param("entityId") Long entityId);

    List<ServiceOrderVo> selectReleaseByOrgSid(@Param(value="orgSidList")List<Long> orgSidList);

    List<Long> selectProductResourceIdListByOwnerId(@Param(value="ownerIdList")List<Long> ownerIdList);

    /**查询portal的服务分类*/
    List<MarketCatalogVO> selectProductCatalogWithService();

    /**
     * 查询订单详情
     * @param orderId
     * @return
     */
    ServiceOrderVo selectOrderDetailById(@Param(value = "orderId")Long orderId);

    /**
     * 根据资源查询订单详情
     * @param id
     * @return
     */
    List<ServiceOrderDetail> selectDetailByResId(String id);

    /**
     * 根据实例名称模糊查询
     */
    List<Long> selectProductResourceIdListBypProductNameLike(String productNameLike);

    List<ServiceOrderVo> selectOrderByName(String name);

    List<ServiceOrderDetail> selectOrderDetailByClusterId(@Param("productType")String productType, @Param("clusterId")Long clusterId);
    /**
     * 根据实例id查询申请订单
     */
    List<ServiceOrderVo> selectByInstanceId(String refInstanceId);

    /**
     * 根据orderSn查询订单账户信息
     */
    Long findAccountIdByOrderSn(@Param("orderSn")Long orderSn);

    List<ServiceOrderDetail> selectProductOrderByWeek(@Param("productCode")String productCode,@Param("resourceId") Long resourceId);

    List<Long> selectForColleter(@Param("id")Long id);

    /**
     * 根据adminSid查询按量付费订单
     */
    List<ServiceOrder> selectOrderByAdminSid(@Param("adminSid")Long adminSid,@Param("entityId")Long entityId);

    List<ServiceOrderVo> selectOrderByCluster(Long clusterId, String productCode);


    /**
     * 根据实例名称模糊查询
     */
    ServiceOrder selectOrderDetailByResourceId(@Param("id")String id,@Param("mainProductCode")String mainProductCode);

    Integer selectPendingOrder(@Param("id") String id, @Param("productCode") String productCode);

    @Select("select distinct o.name from service_order o " +
            "left join sf_product_resource  pr on o.cluster_id = pr.cluster_id " +
            "where o.owner_id =#{userSid} and pr.product_type =#{productType} and pr.`status` !='unsubscribed'")
    List<String> selectResourceName(@Param("userSid") Long userSid, @Param("productType") String productType);

    /**
     * 根据id查询专属资源池使用信息
     *
     * @param userSid
     *
     * @return
     */
    List<DrpProductResponse> selectDrpList(Long userSid);
    /**
     * 查询配置
     * @param key
     * @return
     */
    String  selectConfig(String key);
}
