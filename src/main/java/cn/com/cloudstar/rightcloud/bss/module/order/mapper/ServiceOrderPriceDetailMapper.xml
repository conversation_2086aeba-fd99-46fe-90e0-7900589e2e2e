<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderPriceDetailMapper">
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.orderId != null">
                and B.id = #{condition.orderId}
            </if>
            <if test="condition.orderType != null">
                and B.type = #{condition.orderType}
            </if>
            <if test="condition.orderStatus != null">
                and B.status = #{condition.orderStatus}
            </if>
            <if test="condition.orderTypes != null">
                and B.type in
                <foreach collection="condition.orderTypes" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.types != null">
                and A.type in
                <foreach collection="condition.types" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.productCodeIn != null">
                and A.product_code in
                <foreach collection="condition.productCodeIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.endTime != null">
                and A.end_time &gt;= #{condition.endTime}
            </if>
            <if test="condition.refInstanceIdLike != null">
                and A.ref_instance_id like concat('%', #{condition.refInstanceIdLike}, '%')
            </if>
            <if test="condition.priceType != null">
                and A.price_type = #{condition.priceType}
            </if>
            <if test="condition.productCode != null">
                and A.product_code = #{condition.productCode}
            </if>
            <if test="condition.isValid != null and condition.isValid">
                and A.end_time &gt;= A.start_time
            </if>
            <if test="condition.orderDetailIdIn != null">
                and A.order_detail_id in
                <foreach collection="condition.orderDetailIdIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.productCodeIn != null">
                and A.product_code in
                <foreach collection="condition.productCodeIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orderSnIn != null and condition.orderSnIn.size() > 0">
                and A.order_sn in
                <foreach collection="condition.orderSnIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.contractSids != null and condition.contractSids.size() > 0">
                and B.contract_id in
                <foreach collection="condition.contractSids" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>
    <select id="selectByCriteria" resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail">
        select A.*, B.type as order_type, B.status as order_status, C.service_id, C.order_id,
        A.pay_balance,
        A.pay_balance_cash,
        A.pay_credit_line,
        B.charging_type
        from service_order_price_detail A
        left join service_order B on A.order_sn = B.order_sn
        left join service_order_detail C on A.order_detail_id = C.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectOrderInfo"
            resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail">
        select C.expired_used_amount,B.original_cost,B.final_cost as amount,C.start_time,C.end_time, B.type as order_type, B.status as order_status, C.service_id, C.order_id,C.expired_used_amount,C.duration,
               B.org_discount,
               A.product_code,
               A.trade_price,
               A.pay_balance,
               A.pay_balance_cash,
               A.pay_credit_line,
               A.price,
               A.quantity,
               A.coupon_amount,
               A.discount,
               A.order_sn
        from service_order_price_detail A
        left join service_order B on A.order_sn = B.order_sn
        left join service_order_detail C on A.order_detail_id = C.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by B.id
    </select>
    <select id="selectOrderDetailByCriteria"
            resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail">

        select B.cluster_id, c.* from service_order_price_detail A
        left join service_order B on A.order_sn = B.order_sn
        left join service_order_detail C on A.order_detail_id = C.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by c.id
        order by c.id desc
        limit 50
    </select>

    <select id="selectOrderInfoByParam"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail">
        select
        DISTINCT
        A.order_detail_id, B.original_cost,B.org_discount,B.coupon_discount,B.final_cost as amount,A.start_time,A.end_time, B.type as order_type, B.status as order_status,
        C.service_id, C.order_id, C.expired_used_amount,A.ref_instance_id
        from service_order_price_detail A
        left join service_order B on A.order_sn = B.order_sn
        left join service_order_detail C on A.order_detail_id = C.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by A.order_detail_id
    </select>

    <select id="selectOrderDetailByType" parameterType="map"
        resultType="cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail">
        select
        A.*
        from service_order_price_detail A

        where A.order_sn=#{orderSn} and A.type=#{type} ;
    </select>

</mapper>
