/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Policy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.UserGroupMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.*;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.NotificationUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;


/**
 * <p>
 * 用户组与用户关联表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
@Slf4j
public class UserGroupServiceImpl extends ServiceImpl<UserGroupMapper, UserGroup> implements
        IUserGroupService {

    @Autowired
    private IPolicyGroupService policyGroupService;

    @Autowired
    private IPolicyUserService policyUserService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private IPolicyService policyService;
    @Autowired
    private PolicyMapper policyMapper;
    @Autowired
    private HPCService hpcService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private SysGroupService sysGroupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUser(List<Long> userSids, List<Long> groupIds, Long orgSid) {
        if (CollectionUtils.isEmpty(userSids) || CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());
        if (Objects.nonNull(org)) {
        policyService.checkGroup(groupIds, org.getOrgSid());
        }
        AtomicLong createDt = new AtomicLong(System.currentTimeMillis());
        userSids.forEach(userSid -> {
            groupIds.forEach(groupId -> {
                // 是否可以插入
                QueryWrapper<UserGroup> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("group_sid", groupId);
                queryWrapper.eq("user_sid", userSid);
                queryWrapper.eq("org_sid", orgSid);
                List<UserGroup> list = this.list(queryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    createDt.addAndGet(1000L);
                    UserGroup userGroup = new UserGroup();
                    userGroup.setCreatedDt(new Date(createDt.get()));
                    userGroup.setGroupSid(groupId);
                    userGroup.setUserSid(userSid);
                    userGroup.setOrgSid(orgSid);
                    resetSysGroupMacByUserSid(userSid, groupId, false);
                    this.save(userGroup);
                }
            });
            // 清除权限缓存
            JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + userSid, 0);
        });
    }


    /**
     * 用户组关联表插入或删除之前用重置用户组mac
     * @param groupSid groupSid
     * @param userSid userSid
     * @param del del
     */
    public void resetSysGroupMacByUserSid(Long groupSid, Long userSid, boolean del) {
        List<SysGroup> sysGroups = sysGroupService.selectByParams(null);
        boolean updateFlag = false;
        for (SysGroup sysGroup : sysGroups) {
            StringBuilder currentGroupUsers = new StringBuilder();

            if (groupSid == null) {
                if (del) {
                    if (sysGroup.getGroupUsers() != null) {
                        updateFlag = true;
                        currentGroupUsers.append(Arrays.stream(sysGroup.getGroupUsers().split(",")).filter(s -> !s.equals(userSid.toString())).collect(Collectors.joining(",")));
                    }
                }
            } else {
                if (del) {
                    if (sysGroup.getGroupUsers() != null) {
                        updateFlag = true;
                        currentGroupUsers.append(Arrays.stream(sysGroup.getGroupUsers().split(",")).filter(s -> !s.equals(userSid.toString())).collect(Collectors.joining(",")));
                    }
                } else {
                    if (groupSid.equals(sysGroup.getGroupSid())) {
                        if (StrUtil.isNotBlank(sysGroup.getGroupUsers())) {
                            currentGroupUsers.append(sysGroup.getGroupUsers());
                        }
                        updateFlag = true;
                        if (StrUtil.isNotBlank(currentGroupUsers.toString())) {
                            currentGroupUsers.append(",").append(userSid);
                        }else{
                            currentGroupUsers.append(userSid);
                        }
                    }
                }
            }
            sysGroup.setGroupUsers(currentGroupUsers.toString());
            sysGroup.setSkipCCSPHandle(true);
            if (updateFlag) {
                sysGroupService.updateByPrimaryKey(sysGroup);
                updateFlag = false;
            }
        }
    }


    /**
     * 递归查询子用户的父id，并获取最顶级的用户信息
     *
     * @param userSid
     */
    public User getParentSidBySid(Long userSid) {
        User user = userMapper.selectByPrimaryKey(userSid);
        User rootUser = user;

        //递归出口 parentSid为空
        if (Objects.nonNull(user) && Objects.nonNull(user.getParentSid())) {
            User user1 = userMapper.selectByPrimaryKey(user.getParentSid());
            getParentSidBySid(user1.getUserSid());
            rootUser = user1;
        }
        return rootUser;
    }

    /**
     * 把这些用户组的用户更新为userSids
     *
     * @param userSids
     * @param groupIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void multiAddUser(List<Long> userSids, List<Long> groupIds) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());

        //临时方案：子用户变更权限组用户时，只能变更自己
        if (userSids.contains(authUserInfo.getUserSid()) || Objects.isNull(authUserInfo.getParentSid())){
            policyService.checkUsers(userSids, authUserInfo.getUserSid());
            policyService.checkUserSelf(groupIds, userSids, authUserInfo.getUserSid());
            policyService.checkGroup(groupIds, org.getOrgSid());
        }
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("orgSid", authUserInfo.getOrgSid());
        List<User> users = sysUserService.selectByParams(criteria);
        List<Long> subUserSidList = users.stream().mapToLong(User::getUserSid).boxed().collect(Collectors.toList());
        for (Long userSid : userSids) {
            if (!subUserSidList.contains(userSid)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_803013550));
            }
        }

        List<Long> toChekcJob = Lists.newArrayList();
        //对用户组来说 是将用户组成员更新为userSids 存在新增 和 删除两种情况
        //所有可能被操作到的用户
        List<Long> allUserIds = Lists.newArrayList();
        groupIds.forEach(groupId -> {
            //判断需要被移除的用户
            List<UserGroup> list = this.lambdaQuery()
                                       .eq(UserGroup::getGroupSid, groupId)
                                       .eq(UserGroup::getOrgSid, org.getOrgSid())
                                       .list();
            List<Long> oldUserIds = list.stream().mapToLong(UserGroup::getUserSid).boxed().collect(Collectors.toList());
            allUserIds.addAll(oldUserIds);
        });

        allUserIds.addAll(userSids);
        if (!CollectionUtils.isEmpty(allUserIds)) {
            List<Long> collect = allUserIds.stream().distinct().collect(Collectors.toList());
            collect.forEach(c -> {
                // 清除权限缓存
                JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + c, 0);
            });
        }

        Criteria criteria1 = new Criteria();
        criteria1.put("parentSid", authUserInfo.getUserSid());
        List<Policy> oldPolicy = policyMapper.selectHpcByGroupSids(criteria1);

        //落库
        this.lambdaUpdate().in(UserGroup::getGroupSid, groupIds).eq(UserGroup::getOrgSid, org.getOrgSid()).remove();
        ArrayList<UserGroup> toAdd = Lists.newArrayList();
        AtomicLong createDt = new AtomicLong(System.currentTimeMillis());

        groupIds.forEach(g -> {
            userSids.forEach(u -> {
                createDt.addAndGet(1000L);
                UserGroup userGroup = new UserGroup();
                userGroup.setOrgSid(org.getOrgSid());
                userGroup.setGroupSid(g);
                userGroup.setUserSid(u);
                userGroup.setCreatedDt(new Date(createDt.get()));
                toAdd.add(userGroup);
            });
        });

        updateGroupMac(userSids, groupIds);
        //落库
        this.lambdaUpdate().in(UserGroup::getGroupSid, groupIds).eq(UserGroup::getOrgSid, org.getOrgSid()).remove();
        this.saveBatch(toAdd);


        List<Policy> newPolicy = policyMapper.selectHpcByGroupSids(criteria1);
        this.sendAuthorityChangeMsg(oldPolicy, newPolicy, authUserInfo);
    }

    /**
     *  发送权限变更消息
     */
    @Override
    @Async
    public void sendAuthorityChangeMsg(List<Policy> oldPolicy, List<Policy> newPolicy, AuthUser authUserInfo) {
        if (oldPolicy == null) {
            oldPolicy = new ArrayList<>();
        }
        if (newPolicy == null){
            newPolicy = new ArrayList<>();
        }
        Map<Long, List<Policy>> oldUserIdToMap = new HashMap<>();
        Map<Long, List<Policy>> newUserIdToMap = new HashMap<>();
        if (oldPolicy.size() > 0) {
            oldUserIdToMap = oldPolicy.stream().collect(Collectors.groupingBy(Policy::getUserSid));
        }
        if (newPolicy.size() > 0) {
            newUserIdToMap = newPolicy.stream().collect(Collectors.groupingBy(Policy::getUserSid));
        }

        Set<Long> userSids = new HashSet<>();
        userSids.addAll(oldUserIdToMap.keySet());
        userSids.addAll(newUserIdToMap.keySet());

        for (Long userSid : userSids) {
            this.sendMessage(oldUserIdToMap.get(userSid), newUserIdToMap.get(userSid), userSid,  authUserInfo);
        }

    }

    private void sendMessage(List<Policy> oldPolicy, List<Policy> newPolicy, Long userSid, AuthUser authUserInfo) {
        if (oldPolicy == null) {
            oldPolicy = new ArrayList<>();
        }
        if (newPolicy == null){
            newPolicy = new ArrayList<>();
        }

        // 模板内容
        Map<String, Object> messageContent = NotificationUtil.getBaseMessageContent(authUserInfo.getRealName(), authUserInfo.getAccount());
        messageContent.put("adminUserName", authUserInfo.getAccount());

        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.getImsgUserIds().add(userSid);
        baseNotificationMqBean.getImsgUserIds().add(authUserInfo.getUserSid());
        baseNotificationMqBean.setMap(messageContent);
        User user = userMapper.selectByPrimaryKey(userSid);
        if (Objects.isNull(user)) {
            return;
        }
        messageContent.put("user", user.getAccount());

        List<String> newPolicyNames = newPolicy.stream()
                                               .map(policy -> StringUtils.isEmpty(policy.getDisplayName()) ? policy.getPolicyName() : policy.getDisplayName())
                                               .collect(Collectors.toList());
        List<String> oldPolicyNames = oldPolicy.stream()
                                               .map(policy -> StringUtils.isEmpty(policy.getDisplayName()) ? policy.getPolicyName() : policy.getDisplayName())
                                               .collect(Collectors.toList());
        List<String> newNames = newPolicyNames.stream().filter(name -> !oldPolicyNames.contains(name)).collect(Collectors.toList());
        List<String> oldNames = oldPolicyNames.stream().filter(name -> !newPolicyNames.contains(name)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oldNames)) {
            // 发送取消权限消息
            baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_AUTHORITY_REMOVE);
            messageContent.put("policyName", StringUtils.join(oldNames, "，"));
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
        }

        if (!CollectionUtils.isEmpty(newNames)){
            // 发送授权权限消息
            baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_AUTHORITY_ADD);
            messageContent.put("policyName", StringUtils.join(newNames, "，"));
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
        }

    }

    private void updateGroupMac(List<Long> userSids, List<Long> groupIds) {

        //这里通过修改触发国密mac更新
        List<SysGroup> sysGroups = new ArrayList<>();
        String groupUsers = userSids.stream().map(String::valueOf).collect(Collectors.joining(","));
        for (Long groupId : groupIds) {
            SysGroup sysGroup = sysGroupService.selectByPrimaryKey(groupId);
            sysGroup.setGroupSid(groupId);
            sysGroup.setGroupUsers(groupUsers);
            sysGroup.setSkipCCSPHandle(true);
            sysGroups.add(sysGroup);
        }
        if (groupIds.isEmpty()) {
            return;
        }
        sysGroupService.updateBatchById(sysGroups);
    }


    @Override
    public List<Long> getDistinctUserSidByGroupIds(List<Long> oldGroup) {
        return baseMapper.getDistinctUserSidByGroupIds(oldGroup);
    }

    /**
     * 将这些用户的用户组设为 groupIds 可能有增加 和删除
     *
     * @param userSids
     * @param groupIds
     */
    @Override
    public void joinGroup(List<Long> userSids, List<Long> groupIds) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        groupIds.forEach(groupId -> {
            SysGroup sysGroup = sysGroupService.selectByPrimaryKey(groupId);
            if (Objects.isNull(sysGroup)) {
                // 指定id的用户组不存在
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }else {
                Long orgSid = sysGroup.getOrgSid();
                if (Objects.nonNull(orgSid) && !orgSid.equals(authUserInfo.getOrgSid())) {
                    // 指定id的用户组为自定义用户组，但与当前用户组织不匹配
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
        });
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(User::getOrgSid,authUserInfo.getOrgSid());
        List<Long> userSidList = userMapper.selectList(userQueryWrapper).stream()
                .map(User::getUserSid)
                .collect(Collectors.toList());
        userSids.forEach(id -> {
            if (userSidList.stream().noneMatch(id::equals)) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        });
        if (userSids.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1131742719));
        }
        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());

        // 根据用户ID查询出组ID
        Criteria criteria = new Criteria();
        criteria.put("userIds", userSids);
        List<Policy> oldPolicy = policyMapper.selectHpcByGroupSids(criteria);

        //落库
        this.remove(new QueryWrapper<UserGroup>().in("user_sid", userSids));
        List<UserGroup> toAdd = Lists.newArrayList();
        AtomicLong createDt = new AtomicLong(System.currentTimeMillis());
        userSids.forEach(u -> {
            groupIds.forEach(g -> {
                createDt.addAndGet(1000L);
                UserGroup userGroup = new UserGroup();
                userGroup.setOrgSid(org.getOrgSid());
                userGroup.setGroupSid(g);
                userGroup.setUserSid(u);
                userGroup.setCreatedDt(new Date(createDt.get()));
                toAdd.add(userGroup);
            });
        });

        if (!CollectionUtils.isEmpty(userSids)) {
            List<Long> collect = userSids.stream().distinct().collect(Collectors.toList());
            collect.forEach(c -> {
                // 清除权限缓存
                JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + c, 0);
            });
            resetSysGroupMacByUserSid(CollectionUtils.isEmpty(groupIds) ? null : groupIds.get(0), userSids.get(0), false);
        }

        saveBatch(toAdd);

        List<Policy> newPolicy = policyMapper.selectHpcByGroupSids(criteria);

        this.sendAuthorityChangeMsg(oldPolicy, newPolicy, authUserInfo);
    }


    @Override
    public void removeUsers(List<Long> userSids, List<Long> groupIds) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());
        if (Objects.nonNull(authUserInfo.getParentSid())
                || !ObjectUtils.isEmpty(org)
                || "03".equals(authUserInfo.getUserType())){
            policyService.checkUsers(userSids, authUserInfo.getUserSid());
            policyService.checkGroup(groupIds, org.getOrgSid());
        }

        Criteria criteria = new Criteria();
        criteria.put("parentSid", authUserInfo.getUserSid());
        criteria.put("userIds", userSids);
        criteria.put("groupSids", groupIds);
        List<Policy> oldPolicy = policyMapper.selectHpcByGroupSids(criteria);

        userSids.forEach(userSid -> this.remove(Wrappers.<UserGroup>lambdaQuery().eq(UserGroup::getUserSid, userSid)
                                                        .in(UserGroup::getGroupSid, groupIds)));

        updateGroupMac(userSids, groupIds);
        List<Policy> newPolicy = policyMapper.selectHpcByGroupSids(criteria);
        this.sendAuthorityChangeMsg(oldPolicy, newPolicy, authUserInfo);
    }

}
