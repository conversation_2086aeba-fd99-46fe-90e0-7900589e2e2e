/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.SidMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Sid;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SidService;

/**
 * <p>
 * 系统流水号管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Service
public class SidServiceImpl extends ServiceImpl<SidMapper, Sid> implements SidService {

    @Override
    public String getMaxSid(String sidCategory) {
        // 最大流水号
        String maxSid = "";
        // 获取当前流水类别记录
        QueryWrapper<Sid> params = new QueryWrapper();
        params.lambda().eq(Sid::getSidCategory, sidCategory);

        List<Sid> sidList = this.baseMapper.selectList(params);
        // 如果没有找到流水类别，返回一个空的流水号
        if (sidList == null || sidList.size() == 0) {
            return null;
        }

        Sid sysSid = sidList.get(0);

        // 获取前缀
        String frefix = (sysSid.getSidFrefix() == null) ? "" : sysSid.getSidFrefix();
        // 流水号长度
        int sidLength = sysSid.getSidLength();
        // 流水号记录番号
        Long curNo = 0L;
        if (sysSid.getCurNo() != null) {
            curNo = sysSid.getCurNo();
        }

        Long maxNo = curNo + 1;
        // 处理流水号长度
        String strMaxNo = StringUtil.formatIntToString(maxNo, sidLength);

        // 判断是否跟日期有关
        String isDate = sysSid.getIsDate();
        if ("true".equalsIgnoreCase(isDate)) {
            // 比较当前日期和流水记录日期是否相同，如果不同需要重新计数
            String curDateDB = sysSid.getCurDate();
            String dateFormat = sysSid.getDateFormat();
            String curDate = StringUtil.dateFormat(new Date(), dateFormat);
            if (!curDate.equalsIgnoreCase(curDateDB)) {
                maxNo = 1L;
                strMaxNo = StringUtil.formatIntToString(maxNo, sidLength);
            }
            strMaxNo = curDate + strMaxNo;
            sysSid.setCurDate(curDate);
        }
        sysSid.setCurNo(maxNo);

        // 记录当前最大流水及日期
        this.baseMapper.updateById(sysSid);

        maxSid = frefix + strMaxNo;

        return maxSid;
    }
}
