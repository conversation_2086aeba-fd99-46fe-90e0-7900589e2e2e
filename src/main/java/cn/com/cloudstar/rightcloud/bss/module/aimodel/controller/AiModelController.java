package cn.com.cloudstar.rightcloud.bss.module.aimodel.controller;


import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.aimodel.pojo.request.CreateConsumerReq;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Api(tags = "AI大模型")
@RestController
@RequestMapping("/ai_model")
@Slf4j
public class AiModelController {


    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ConfigMapper configMapper;

    /**
     * 获取AI供应商信息
     */
    @GetMapping("/provider/{name}")
    public RestResult getAiProvider(@PathVariable String name,
                                    @RequestHeader("Higress-Cookie") String cookie) {
        String url = configMapper.selectConfigValue("higress_url") + "/v1/ai/providers/";
        Map<String, Object> map = new HashMap<>();
        map.put("ts", Instant.now().toEpochMilli());
        String s = splitPath(url, map);
        HttpEntity<String> requestEntity = new HttpEntity<>(tokenHeader(cookie));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        String body = response.getBody();
        Object data = JSONUtil.parseObj(body).get("data");
        return new RestResult(data);
    }

    /**
     * 创建API key
     */
    @PostMapping("/consumer")
    public RestResult createConsumer(@RequestBody CreateConsumerReq req,
                                     @RequestHeader("Higress-Cookie") String cookie) {
        String url = configMapper.selectConfigValue("higress_url") + ""

    }


    public static HttpHeaders tokenHeader(String cookie) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", "application/json");
        headers.add("Content-Encoding", "UTF-8");
        headers.add("Content-Type", "application/json; charset=UTF-8");
        if (StrUtil.isNotBlank(cookie)) {
            headers.add("Cookie", cookie);
        }
        return headers;
    }

    public static String splitPath(String url, Map<String, Object> source) {
        Map<String, Object> params = new HashMap<>(source);
        Iterator<String> it = params.keySet().iterator();

        StringBuilder paramStr = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            if (StrUtil.isBlank(String.valueOf(params.get(key)))) {
                continue;
            }
            paramStr.append("&").append(key).append("=").append(params.get(key));
            it.remove(); // 移除已处理的参数
        }
        if (paramStr.length() == 0) {
            return url;
        }

        String sub = paramStr.substring(1);
        if (url.contains("?")) {
            url += ("&" + sub);
        } else {
            url += ("?" + sub);
        }
        return url;
    }

}
