/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/6/15
 */
@ApiModel(description = "产品申请")
@Data
public class ApplyServiceVO {

    @ApiModelProperty("订单类型,如购买（apply），续订（renew）")
    private String orderType;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("优惠券ID")
    private Long couponId;

    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    @ApiModelProperty("下单用户ID")
    private Long userSid;

    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("相同产品是否汇总")
    private boolean summaryFlg = false;

    @ApiModelProperty("多产品优惠分摊")
    private boolean shareFlg = false;

    private List<ProductInfoVO> productInfo;

    /**
     * 资源信息
     */
    private ActionParam resourceInfo;

    private Long accountId;

    private String entityName;

    private Long entityId;

    private String clusterId;

    private String clusterUuid;
    private String clusterName;

    private String freezingStrategy;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 询价时间点
     */
    private Date pointInTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 模型集市的规格id
     */
    private Long priceJoinId;
}
