/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response;

import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class InquiryPriceResult {

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountPrice;

    /**
     * 最终价，为原价减去折扣
     */
    private BigDecimal tradePrice;
    /**
     * 单次收费原价
     */
    private BigDecimal originalOncePrice = BigDecimal.ZERO;
    /**
     * 单次收费折扣价
     */
    private BigDecimal discountOncePrice = BigDecimal.ZERO;
    /**
     * 单次收费交易价
     */
    private BigDecimal tradeOncePrice = BigDecimal.ZERO;

    /**
     * 配置费用单位
     */
    private String chargeUnit;

    /**
     * 带宽价格单位
     */
    private String bandwidthPriceChargeUnit;

    /**
     * 带宽价格原价
     */
    private BigDecimal bandwidthOriginalPrice;

    /**
     * 带宽价格折扣价
     */
    private BigDecimal bandwidthDiscountPrice;

    /**
     * 几折
     */
    private BigDecimal platformDiscount;

    /**
     * 平台折扣价
     */
    private BigDecimal platformDiscountPrice;

    /**
     * 小时价格
     */
    private BigDecimal originalHourPrice = BigDecimal.ZERO;

    /**
     * 月价格
     */
    private BigDecimal originalMonthPrice = BigDecimal.ZERO;

    /**
     * 折后小时价格
     */
    private BigDecimal discountHourPrice = BigDecimal.ZERO;

    /**
     * 折后月价格
     */
    private BigDecimal discountMonthPrice = BigDecimal.ZERO;

    /**
     * 最终价(小时)，为原价减去折扣
     */
    private BigDecimal tradeHourPrice = BigDecimal.ZERO;

    /**
     * 最终价(月)，为原价减去折扣
     */
    private BigDecimal tradeMonthPrice = BigDecimal.ZERO;

    /**
     * 计费详情
     */
    private List<BizBillingPriceVO> billingPrices = Lists.newArrayList();

    /**
     * 折扣详情
     */
    private List<DiscountDetailVO> discounts;

    /**
     * 是否指定月数
     */
    private Boolean appoint = Boolean.FALSE;

    /**
     *  服务价格
     */
    private BigDecimal serviceHourAmount = BigDecimal.ZERO;

    /**
     *  服务价格
     */
    private BigDecimal serviceMonthAmount = BigDecimal.ZERO;

    /**
     *  单次服务价格
     */
    private BigDecimal serviceOnceAmount = BigDecimal.ZERO;

    /**
     * 资源价格
     */
    private BigDecimal resourceHourAmount = BigDecimal.ZERO;

    /**
     * 资源价格
     */
    private BigDecimal resourceMonthAmount = BigDecimal.ZERO;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;

    private String productCode;

    private BigDecimal couponAmount = BigDecimal.ZERO;

    /**
     * 合同ID
     */
    private Long contractId;
    /**
     * 产品id
     */
    private Long serviceId;

    private String productCategory;

    private BigDecimal periodTime;
    private Date pointInTime;
}
