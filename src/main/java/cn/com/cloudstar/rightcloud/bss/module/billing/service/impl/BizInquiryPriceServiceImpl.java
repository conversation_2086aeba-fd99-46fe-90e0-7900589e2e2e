/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResDcs;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.dcs.ResDcsRemoteService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.SafeConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConfigObjType;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants.BillingChargeType;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingStrategyType;
import cn.com.cloudstar.rightcloud.bss.common.constants.CloudEnvType;
import cn.com.cloudstar.rightcloud.bss.common.constants.PriceType;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.ServiceManage;
import cn.com.cloudstar.rightcloud.bss.common.constants.VolumeType;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.DescribePriceTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ComputeUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingCustomRegionChargeMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingCustomRegionResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingSpecRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BillingConfigCategory;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingChargeMonth;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingCustomRegionCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingCustomRegionResource;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingRegionCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingRegionResource;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecGroup;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategy;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccount;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccountConfig;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccountMatch;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyOrg;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyServing;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.DiskSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.InquiryRenewPriceRequestDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ModifyInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourceInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourcePayPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubscribeInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryBase;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryBaseVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryDiskPrice;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryFloatingIpPrice;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryInstancePrice;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.InquiryInstancePriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.SpecConfigVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceCategoryDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceGroupDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceSpecDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceWayDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingStrategyServingVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ComponentPriceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DiscountDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServiceAccountPriceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServicePriceDetailNewVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.ServicePriceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingSpecService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyAccountConfigService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyAccountMatchService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyOrgService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyServingService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingTariffSpecChargeService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingTariffSpecService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingChargeMonthService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingCustomRegionChargeService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingCustomRegionResourceService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingRegionChargeService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingRegionResourceService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingSpecGroupService;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResourceCompensation;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.EcsDcService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceCompensationService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.InquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaPoolService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServicePrice;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.ResRenewRefService;
import cn.com.cloudstar.rightcloud.bss.module.resource.mapper.ServerTemplateMapper;
import cn.com.cloudstar.rightcloud.bss.module.resource.pojo.entity.ResZone;
import cn.com.cloudstar.rightcloud.bss.module.resource.pojo.entity.ServerTemplate;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.ResourceConfigService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.CustomRegionResourceStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.dto.ResVdDto;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.request.ResVmInfoListRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.result.ResVmInfoListResult;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVolumeType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVolumeTypeRemoteService;


/**
 * <AUTHOR>
 * Created on 2019/11/5
 */
@Slf4j
@Service
public class BizInquiryPriceServiceImpl implements BizInquiryPriceService {
    private static final Map<String, String> RESOURCE_MAP = Maps.newHashMap();
    private static final Map<String, String> CATEGROY_MAP = Maps.newHashMap();
    private static final Map<String, String> BILLINGCHARGE_MAP = Maps.newHashMap();
    private static final Map<String, String> CHARGE_CYCLE_MAP = Maps.newHashMap();
    private static final Map<String, String> GROUP_MAP = Maps.newHashMap();
    private static final Map<String, String> RESOURCE_NAME_MAP = Maps.newHashMap();



    /**
     * 字符串"-"
     */
    private static final String HYPHEN_STR = "-";

    static {
        RESOURCE_MAP.put(ServiceManage.COMPUTE, "res_vm");
        RESOURCE_MAP.put(ServiceManage.FLOATING_IP, "floating_ip");
        RESOURCE_MAP.put(ServiceManage.DISK, "disk");

        CATEGROY_MAP.put(BillingConfigCategory.COMPUTER, "计算");
        CATEGROY_MAP.put(BillingConfigCategory.BLOCK_STORAGE, "存储");
        CATEGROY_MAP.put(BillingConfigCategory.NETWORK, "网络");
        CATEGROY_MAP.put(BillingConfigCategory.ENTERPRISE_INTELLIGENCE, "EI企业智能");

        BILLINGCHARGE_MAP.put(BillingConstants.BillingChargeType.INC_TYPE, "按量计费");
        BILLINGCHARGE_MAP.put(BillingConstants.BillingChargeType.STATIC_TYPE, "固定计费");

        CHARGE_CYCLE_MAP.put(BillingConstants.BillingTrafficSpecChargeType.PERIOD, "周期计费");
        CHARGE_CYCLE_MAP.put(BillingConstants.BillingTrafficSpecChargeType.ONCE, "单次计费");

        GROUP_MAP.put("vdSize", "volumeType");
        GROUP_MAP.put("cpu", "region");
        GROUP_MAP.put("memory", "region");

        RESOURCE_NAME_MAP.put("cpu", "CPU");
        RESOURCE_NAME_MAP.put("memory", "内存");
        RESOURCE_NAME_MAP.put("ipBandWidth", "带宽");
        RESOURCE_NAME_MAP.put("vdSize", "存储");
        RESOURCE_NAME_MAP.put("floatingIp", "弹性IP");
    }

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @DubboReference
    private CloudEnvAccountRemoteService cloudEnvAccountService;

    @Autowired
    private BizBillingStrategyAccountConfigService strategyAccountConfigService;

    @Autowired
    private BizBillingTariffSpecService tariffSpecService;

    @Autowired
    private BizBillingTariffSpecChargeService chargeService;

    @Autowired
    private BizBillingStrategyAccountService strategyAccountService;


    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;
    @DubboReference
    private ResVmRemoteService resVmRemoteService;
    @Autowired
    private BizBillingStrategyService strategyService;

    @DubboReference
    private ResVmTypeRemoteService vmTypeService;

    @DubboReference
    private ResVolumeTypeRemoteService volumeTypeService;

    @Autowired
    private BizBillingSpecService specService;

    @Autowired
    private BizBillingSpecRefMapper specRefMapper;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private ServerTemplateMapper serverTemplateMapper;

    @Autowired
    private BizBillingStrategyOrgService strategyOrgService;

    @Autowired
    private BizBillingStrategyServingService servingService;

    @Autowired
    private BizBillingStrategyAccountMatchService accountMatchService;

    @Autowired
    private IBizDiscountService bizDiscountService;

    @Autowired
    private IBizDiscountPolicyService bizDiscountPolicyService;
    @Autowired
    private IBizBillingRegionChargeService bizBillingRegionChargeService;

    @Autowired
    private BizBillingCustomRegionChargeMapper bizBillingCustomRegionChargeMapper;
    @Autowired
    private IBizBillingRegionResourceService bizBillingRegionResourceService;
    @Autowired
    private BizBillingCustomRegionResourceMapper bizBillingCustomRegionResourceMapper;

    @Autowired
    private IBizBillingCustomRegionChargeService bizBillingCustomRegionChargeService;

    @Autowired
    private IBizBillingCustomRegionResourceService bizBillingCustomRegionResourceService;

    @Autowired
    private IBizBillingSpecGroupService bizBillingSpecGroupService;

    @Autowired
    private IBizBillingChargeMonthService bizBillingChargeMonthService;

    @Autowired
    private InquiryPriceService inquiryPriceService;

    @Autowired
    private ResourceConfigService resourceConfigService;

    @Autowired
    private IServiceOrderDetailService iServiceOrderDetailService;

    @Autowired
    @Lazy
    private ResRenewRefService resRenewRefService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private IServiceOrderPriceDetailService iServiceOrderPriceDetailService;

    @Autowired
    private BizCouponMapper bizCouponMapper;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;

    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @DubboReference
    private ResAllRemoteService resAllRemoteService;
    @DubboReference
    private ShareRemoteService shareRemoteService;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private ISfProductResourceCompensationService sfProductResourceCompensationService;

    @Autowired
    private ResMaPoolService resMaPoolService;

    @Autowired
    private ServiceCategoryService categoryService;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;
    @Autowired
    private BizContractService bizContractService;
    @DubboReference
    private ResDcsRemoteService resDcsRemoteService;

    @Autowired
    private EcsDcService ecsDcService;

    @Override
    public InquiryPriceResult calculateResourcePrice(InquiryBase inquiryBase) {
        Long serviceId = inquiryBase.getServiceId();
        //购买时长
        BigDecimal period = inquiryBase.getPeriod();
        String chargeType = inquiryBase.getInstanceChargeType();
        List<String> products = inquiryBase.getProducts();
        Long envId = inquiryBase.getEnvId();
        //购买数量
        Integer amount = inquiryBase.getAmount();
        //账户ID
        Long accountId = inquiryBase.getBillingAccountId();
        // 服务计费（第三方服务没有云环境，直接走服务计费）
        if (Objects.isNull(inquiryBase.getEnvId()) && Objects.nonNull(serviceId)) {
            return operateResult(generateServicePrice(new InquiryPriceResult(), serviceId, period),
                                 chargeType, products, envId, period, amount, accountId, false);
        }

        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(inquiryBase.getEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1810809622));
        }

        CloudEnvAccount account = cloudEnvAccountService.selectByPrimaryKey(cloudEnv.getCloudEnvAccountId());
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_935946425));
        }

        InquiryPriceResult inquiryPriceResult = new InquiryPriceResult();
        // 获取云账号是否计费配置信息
        QueryWrapper<BizBillingStrategyAccountConfig> accountConfigQueryWrapper = new QueryWrapper<>();
        accountConfigQueryWrapper.eq("account_id", account.getId());
        List<BizBillingStrategyAccountConfig> accountConfigs = strategyAccountConfigService.list(accountConfigQueryWrapper);
        if (CollectionUtils.isEmpty(accountConfigs)) {
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }

        BizBillingStrategyAccountConfig strategyAccountConfig = accountConfigs.get(0);

        if (PUBLIC_ENV.equals(account.getEnvTypeCategory())) {
            // 公有云只计算服务价格，不根据计费策略计算底层资源价格
            generateServicePrice(inquiryPriceResult, inquiryBase.getServiceId(), inquiryBase.getPeriod());
            if (BillingStrategyType.CHARGED_BY_NONE.equals(strategyAccountConfig.getBillingStrategy())) {
                return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, true);
            }
            try {
                InquiryInstancePriceRequest request = BeanConvertUtil.convert(inquiryBase, InquiryInstancePriceRequest.class);
                RestResult response = inquiryPriceService.invoke(request);
                if (response != null && response.getData() != null) {
                    InquiryPriceResponse priceResponse = BeanConvertUtil.convert(response.getData(), InquiryPriceResponse.class);
                    //价格 * 上浮点数
                    priceResponse.setTradePrice(NumberUtil.mul(priceResponse.getTradePrice(), strategyAccountConfig.getFloatingRatio()));

                    if (HOUR.equals(priceResponse.getChargeUnit())) {
                        inquiryPriceResult.setOriginalHourPrice(inquiryPriceResult.getOriginalHourPrice().add(priceResponse.getOriginalPrice()));
                        inquiryPriceResult.setTradeHourPrice(inquiryPriceResult.getTradeHourPrice().add(priceResponse.getTradePrice()));
                    } else {
                        inquiryPriceResult.setOriginalMonthPrice(inquiryPriceResult.getOriginalHourPrice().add(priceResponse.getOriginalPrice()));
                        inquiryPriceResult.setTradeMonthPrice(inquiryPriceResult.getTradeMonthPrice().add(priceResponse.getTradePrice()));
                    }
                }
            } catch (Exception e) {
                log.error("公有云询价失败！", e.getMessage());
            }

            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, true);
        }

        // 获取需要计费的原始数据
        Map<String, Object> billingMap = generateBillingMap(inquiryBase, account, cloudEnv);
        if (!BillingConstants.ObjType.CLOUD_ENV.equals(
                billingMap.get(DescribePriceTypeEnum.OBJ_TYPE.getProp()).toString())) {
            // 计算服务价格
            generateServicePrice(inquiryPriceResult, inquiryBase.getServiceId(), inquiryBase.getPeriod());
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }
        // 计算服务价格
        generateServicePrice(inquiryPriceResult, inquiryBase.getServiceId(), inquiryBase.getPeriod());

        if (!BillingStrategyType.CHARGED_BY_CUSTOM.equals(strategyAccountConfig.getBillingStrategy())) {
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }

        // 计算底层资源价格
        generateAccountPrice((InquiryInstancePrice) inquiryBase, inquiryPriceResult, billingMap, cloudEnv);

        return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
    }

    @Override
    public InquiryPriceResult inquiryPrice(InquiryBaseVO inquiryBase) {
        Long serviceId = inquiryBase.getServiceId();
        //购买时长
        BigDecimal period = inquiryBase.getPeriod();
        String chargeType = inquiryBase.getChargeType();
        List<String> products = inquiryBase.getProducts();
        products.add(inquiryBase.getProductCode());
        Long envId = inquiryBase.getCloudEnvId();
        //购买数量
        Integer amount = inquiryBase.getAmount();
        //账户ID
        Long accountId = inquiryBase.getBillingAccountId();

        User authUser = AuthUtil.getAuthUser();
        if (inquiryBase.isConsoleUserFlg() && accountId == null) {
            BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(inquiryBase.getEntityId(), authUser.getUserSid());
            AssertUtil.requireNonBlank(account, "登录用户权限错误.未关联对应的账户.");
            accountId = account.getId();
        }

        InquiryPriceResult inquiryPriceResult = new InquiryPriceResult();
        inquiryPriceResult.setProductCode(inquiryBase.getProductCode());
        inquiryPriceResult.setPeriodTime(inquiryBase.getPeriodTime());
        inquiryPriceResult.setPointInTime(inquiryBase.getPointInTime());
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(envId);
        if (cloudEnv == null) {
            ResCloudEnv resCloudEnv = cloudEnvService.selectResCloudEnvById(envId);
            if (resCloudEnv != null && resCloudEnv.getCloudEnvId() != null) {
                cloudEnv = cloudEnvService.selectByPrimaryKey(resCloudEnv.getCloudEnvId());
            }
        }

        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1810809622));
        }

        CloudEnvAccount account = cloudEnvAccountService.selectByPrimaryKey(cloudEnv.getCloudEnvAccountId());
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_935946425));
        }

        // 产品上架检查
        ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
        if (Objects.isNull(serviceCategory)) {
            List<ServiceCategory> categoryList = serviceCategoryService.getServiceCategoryByServiceType(inquiryBase.getProductCode());
            if (CollectionUtils.isNotEmpty(categoryList)) {
                serviceCategory = categoryList.get(0);
                serviceId = serviceCategory.getId();
                inquiryBase.setServiceId(serviceId);
            }
        }
        // 产品开通的状态和续订的情况，不判断产品上下架
        boolean b = false;
        if (Objects.nonNull(serviceCategory)) {
            b = serviceCategory.getEditable() != 0 && !"SFS".equals(serviceCategory.getServiceType())
                    && !"OBS".equals(serviceCategory.getServiceType())
                    && !CouponStatusEnum.USING.getCode().equals(serviceCategory.getStatus());
        }

        if (!inquiryBase.isOpenFlg() && !inquiryBase.isRenewFlg() && (Objects.isNull(serviceCategory) || b)) {
           if (StringUtil.isEmpty(inquiryBase.getOrderId())) {
               if (CouponStatusEnum.UNAUTH.getCode().equals(serviceCategory.getStatus())) {
                   throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_162401584));
               }
               throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_162401583));
           }
        }

        // 合同价
        if (Objects.nonNull(inquiryBase.getContractId())) {
            BigDecimal discount = BigDecimal.ONE;
            // 32305 取消合同折扣

            BigDecimal contractPrice = Objects.isNull(inquiryBase.getContractPrice()) ? BigDecimal.ZERO : inquiryBase.getContractPrice();
            inquiryPriceResult.setOriginalPrice(contractPrice);
            inquiryPriceResult.setTradePrice(contractPrice.multiply(discount));
            inquiryPriceResult.setDiscountPrice(inquiryPriceResult.getOriginalPrice().subtract(inquiryPriceResult.getTradePrice()));
            inquiryPriceResult.setPlatformDiscount(discount);
            inquiryPriceResult.setContractId(inquiryBase.getContractId());
            List<BizBillingPriceVO> billingPrices = Lists.newArrayList();
            BizBillingPriceVO billingPriceVO = new BizBillingPriceVO();
            billingPriceVO.setPriceType(PriceType.RESOURCE);
            //获取计费项
            JsonNode jsonNode = JsonUtil.fromJson(JsonUtil.toJson(((ProductInfoVO) inquiryBase).getData()));
            List<Map<String, Object>> specMapping = specMapping(jsonNode, cloudEnv);
            billingPriceVO.setCategory(Convert.toStr(specMapping.get(0).get("category")));
            billingPriceVO.setResourceType("contract");
            billingPriceVO.setPriceDesc(ProductCodeEnum.toDesc(inquiryBase.getProductCode()));
            billingPriceVO.setBillingSpec(StrUtil.concat(true, Convert.toStr(specMapping.get(0).get("spec")), "、",
                    Convert.toStr(specMapping.get(0).get("businessType"))));
            billingPriceVO.setProductCode(inquiryBase.getProductCode());
            billingPriceVO.setResourceConfig(JSON.toJSONString(specMapping.get(0)));
            billingPriceVO.setIdentification(Convert.toStr(specMapping.get(0).get("identification")) + Convert.toStr(specMapping.get(0).get("spec")));
            billingPriceVO.setMonthPrice(contractPrice);
            billingPriceVO.setPlatformDiscount(discount);
            billingPrices.add(billingPriceVO);
            inquiryPriceResult.setBillingPrices(billingPrices);
            return inquiryPriceResult;
        }
        //设置优惠券金额
        if (inquiryBase.getCouponId() != null) {
            BizCoupon bizCoupon = bizCouponMapper.selectById(inquiryBase.getCouponId());
            if (bizCoupon != null) {
                inquiryPriceResult.setCouponAmount(bizCoupon.getDiscountAmount());
            }
        }

        // 服务计费（第三方服务没有云环境，直接走服务计费）
        if (Objects.isNull(inquiryBase.getCloudEnvId()) && Objects.nonNull(inquiryBase.getServiceId())) {
            return operateResult(generateServicePrice(inquiryPriceResult, serviceId, period),
                    chargeType, products, envId, period, amount, accountId, false);
        }
        //产品配置为不计费，返回
        if (ChargeTypeEnum.None.getType().equals(chargeType)
                || !serviceCategoryService.checkProductIsCharge(cloudEnv.getCloudEnvType(), inquiryBase.getProductCode())) {
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }
        // 产品关联资源检查
        QueryWrapper<BizBillingStrategyServing> specQuery = new QueryWrapper<>();
        specQuery.lambda().in(BizBillingStrategyServing::getServiceId, serviceId);
        List<BizBillingStrategyServing> bizBillingStrategy = servingService.list(specQuery);
        if (CollectionUtil.isEmpty(bizBillingStrategy) || Strings.isNullOrEmpty(bizBillingStrategy.get(0).getResourceType())) {
            String product = Objects.isNull(ProductCodeEnum.toEnum(inquiryBase.getProductCode())) ? "" : ProductCodeEnum.toEnum(inquiryBase.getProductCode()).getProductName();

            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1523413729));
        } else if (!Objects.equals(bizBillingStrategy.get(0).getResourceType(), inquiryBase.getProductCode())) {

            String product = Objects.isNull(ProductCodeEnum.toEnum(inquiryBase.getProductCode())) ? "" : ProductCodeEnum.toEnum(inquiryBase.getProductCode()).getProductName();

            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1899767177));
        }

        // 获取云账号是否计费配置信息
        QueryWrapper<BizBillingStrategyAccountConfig> accountConfigQueryWrapper = new QueryWrapper<>();
        accountConfigQueryWrapper.eq("account_id", account.getId());
        List<BizBillingStrategyAccountConfig> accountConfigs = strategyAccountConfigService.list(accountConfigQueryWrapper);
        if (CollectionUtils.isEmpty(accountConfigs)) {
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }

        BizBillingStrategyAccountConfig strategyAccountConfig = accountConfigs.get(0);
        BigDecimal floatingRatio = Objects.isNull(strategyAccountConfig.getFloatingRatio())
            ? BigDecimal.ONE : strategyAccountConfig.getFloatingRatio();
        inquiryPriceResult.setFloatingRatio(floatingRatio);
        if (PUBLIC_ENV.equals(account.getEnvTypeCategory())) {
            if (CloudEnvType.ALIYUN.valueContains(cloudEnv.getCloudEnvType())
                    && ChargeTypeEnum.PrePaid.getType().equals(chargeType)
                    && ProductCodeEnum.DISK.getProductType().equals(inquiryBase.getProductCode())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1653647754));
            }
            // 公有云只计算服务价格，不根据计费策略计算底层资源价格
            generateServicePrice(inquiryPriceResult, serviceId, period);
            if (BillingStrategyType.CHARGED_BY_NONE.equals(strategyAccountConfig.getBillingStrategy())) {
                return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, true);
            }
            inquiryPriceResult = inquiryPublicCloudPrice(inquiryPriceResult, inquiryBase, cloudEnv, floatingRatio);
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, true);
        }

        // 计算服务价格
        generateServicePrice(inquiryPriceResult, serviceId, period);

        if (!BillingStrategyType.CHARGED_BY_CUSTOM.equals(strategyAccountConfig.getBillingStrategy())) {
            return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
        }
        // 计算底层资源价格
        generateAccountPrice((ProductInfoVO) inquiryBase, inquiryPriceResult, cloudEnv);

        // 获取order_detail中资源规格配置，针对VNC节点：amount为零，表示缩容为零，不再计费
        boolean billing = Arrays.asList(
                "ECS",
                ProductCodeEnum.FLOATING_IP.getProductType(),
                "CCE",
                ProductCodeEnum.CBR.getProductType(),
                ProductCodeEnum.RDS.getProductType(),
                ProductCodeEnum.SFS_TURBO.getProductType(),
                ProductCodeEnum.DISK.getProductType(),
                ProductCodeEnum.DCS.getProductType(),
                "ELB",
                ProductCodeEnum.RS_BMS.getProductType()
        ).contains(inquiryBase.getProductCode());
        if (Objects.nonNull(((ProductInfoVO) inquiryBase).getProductConfigDesc()) && !billing) {
            String currentConfigDesc = ((ProductInfoVO) inquiryBase).getProductConfigDesc().getCurrentConfigDesc();
            JSONArray configDescArray = JSON.parseArray(currentConfigDesc);
            if (configDescArray != null) {
                for (Object object : configDescArray) {
                    if (ObjectUtils.isEmpty(object)) {
                        continue;
                    }
                    JSONObject jsonObject = (JSONObject) object;
                    if (Objects.equals(jsonObject.get(Constants.ATTR_KEY), Constants.VNC_NODE_INFO) && amount == 0) {
                        return inquiryPriceResult;
                    }
                }
            }
        }

        return operateResult(inquiryPriceResult, chargeType, products, envId, period, amount, accountId, false);
    }

    /**
     * 价格详情汇总
     * @param inquiryPriceResult
     * @param isPublic
     */
    public void amountSummary(InquiryPriceResult inquiryPriceResult, BigDecimal period, Integer amount, boolean isPublic) {
        if (isPublic) {
            inquiryPriceResult.setOriginalHourPrice(
                BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
                    .filter(price -> !PriceType.RESOURCE.equals(price.getPriceType()))
                    .mapToDouble(
                        value1 -> value1.getHourPrice()
                            .doubleValue())
                    .summaryStatistics().getSum())
                    .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP));

            inquiryPriceResult.setOriginalMonthPrice(
                BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
                    .filter(price -> !PriceType.RESOURCE.equals(price.getPriceType()))
                    .mapToDouble(
                        value1 -> value1.getMonthPrice()
                            .doubleValue())
                    .summaryStatistics()
                    .getSum())
                    .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP));

        } else {
            inquiryPriceResult.setOriginalHourPrice(BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
                .mapToDouble(
                    value1 -> value1.getHourPrice().multiply(new BigDecimal(amount))
                        .doubleValue())
                .summaryStatistics()
                .getSum())
                .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP));

            inquiryPriceResult.setOriginalMonthPrice(BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
                .mapToDouble(
                        value1 -> {
                            if (value1.getAppoint()) {
                                return value1.getMonthPrice().multiply(new BigDecimal(amount)).doubleValue();
                            } else {
                                return value1.getMonthPrice().multiply(new BigDecimal(amount)).multiply(period).doubleValue();
                            }
                        })
                .summaryStatistics()
                .getSum())
                .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP));
        }

        inquiryPriceResult.setOriginalOncePrice(BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
            .mapToDouble(
                value1 -> value1.getOncePrice()
                    .doubleValue())
            .summaryStatistics()
            .getSum())
            .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP));

        BigDecimal publicHourAmount = BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
            .filter(price -> PriceType.RESOURCE.equals(price.getPriceType()))
            .mapToDouble(
                value1 -> value1.getHourPrice()
                    .doubleValue())
            .summaryStatistics()
            .getSum())
            .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP);
        BigDecimal publicMonthAmount = BigDecimal.valueOf(inquiryPriceResult.getBillingPrices().stream()
            .filter(price -> PriceType.RESOURCE.equals(price.getPriceType()))
            .mapToDouble(
                value1 -> value1.getMonthPrice()
                    .doubleValue())
            .summaryStatistics()
            .getSum())
            .setScale(DECIMAL_FIX, BigDecimal.ROUND_HALF_UP);
        inquiryPriceResult.setResourceHourAmount(publicHourAmount);

        inquiryPriceResult.setResourceMonthAmount(publicMonthAmount);
    }

    @Override
    public InquiryPriceResult inquiryPublicCloudPrice(InquiryPriceResult inquiryPriceResult, InquiryBaseVO inquiryBase,
        CloudEnv cloudEnv, BigDecimal floatingRatio) {
        try {
            JsonNode jsonNode = JsonUtil.fromJson(JsonUtil.toJson(((ProductInfoVO) inquiryBase).getData()));
            InquiryInstancePriceRequest request = BeanConvertUtil.convert(jsonNode.toString(), InquiryInstancePriceRequest.class);
            Iterator<Entry<String, JsonNode>> iterator = jsonNode.fields();
            while (iterator.hasNext()) {
                JsonNode metadata = iterator.next().getValue();
                if (metadata.isArray()) {
                    continue;
                }
                InquiryInstancePriceRequest copy = BeanConvertUtil.convert(metadata.toString(), InquiryInstancePriceRequest.class);
                BeanUtil.copyProperties(copy, request, CopyOptions.create().ignoreNullValue());
            }
            BeanUtil.copyProperties(inquiryBase, request, CopyOptions.create().ignoreNullValue());
            request.setEnvId(cloudEnv.getId());
            request.setInstanceChargeType(inquiryBase.getChargeType());
            ProductCodeEnum productCodeEnum = ProductCodeEnum.toEnum(inquiryBase.getProductCode());
            request.setResourceType(Objects.isNull(productCodeEnum) ? "" : productCodeEnum.getPriceKey());
            if (!inquiryBase.getProductCode().equalsIgnoreCase(ProductCodeEnum.ECS.getProductType())) {
                request.setInstanceType(jsonNode.findValue("category") == null
                    ? null : jsonNode.findValue("category").asText());
            }

            RestResult response = inquiryPriceService.invoke(request);
            if (response != null && response.getData() != null) {
                InquiryPriceResponse priceResponse = BeanConvertUtil.convert(response.getData(), InquiryPriceResponse.class);
                //价格 * 上浮点数
                inquiryPriceResult.setFloatingRatio(floatingRatio);

                BizBillingPriceVO priceVO = new BizBillingPriceVO();
                priceVO.setCategory(PUBLIC_ENV);
                priceVO.setPriceType(PriceType.RESOURCE);
                priceVO.setFloatingRatio(floatingRatio);
                priceVO.setResourceType(inquiryBase.getProductCode());
                priceVO.setPriceDesc(ProductCodeEnum.toDesc(inquiryBase.getProductCode()));
                priceVO.setOriginalPrice(priceResponse.getOriginalPrice());
                if (HOUR.equalsIgnoreCase(priceResponse.getChargeUnit())) {
                    priceVO.setHourPrice(NumberUtil.mul(priceResponse.getTradePrice(), floatingRatio));
                } else {
                    priceVO.setMonthPrice(NumberUtil.mul(priceResponse.getTradePrice(), floatingRatio));
                }
                inquiryPriceResult.getBillingPrices().add(priceVO);
            }
        } catch (Exception e) {
            log.error("公有云询价失败！", e.getMessage());
        }
        return inquiryPriceResult;
    }

    @Override
    public List<InquiryPriceResponse> inquiryPrice(ApplyServiceVO applyServiceVO) {
        List<InquiryPriceResponse> result = Lists.newArrayList();
        if (CollectionUtil.isEmpty(applyServiceVO.getProductInfo())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        applyServiceVO.getProductInfo().forEach(productInfoVO -> {
            convertProductInfoVO(productInfoVO);
            String productCode = productInfoVO.getProductCode();
            productInfoVO.setBillingAccountId(applyServiceVO.getAccountId());
            productInfoVO.setEntityId(applyServiceVO.getEntityId());
            productInfoVO.setEntityName(applyServiceVO.getEntityName());
            result.add(
                convertPriceResponse(inquiryPrice(productInfoVO), productInfoVO.getChargeType()));
            productInfoVO.setProductCode(productCode);
        });
        if (applyServiceVO.getCouponId() != null) {
            BizCoupon bizCoupon = bizCouponMapper.selectById(applyServiceVO.getCouponId());
            if (bizCoupon != null && Objects.nonNull(bizCoupon.getReductionCondition())) {
                BigDecimal tradePrice = result.stream().map(InquiryPriceResponse::getTradePrice).filter(Objects::nonNull).reduce(BigDecimal::add)
                                              .orElse(BigDecimal.ZERO);
                if (tradePrice.add(bizCoupon.getDiscountAmount()).compareTo(bizCoupon.getReductionCondition()) < 0) {
                    throw new BizException("未满足所选优惠券使用条件.");
                }
            }
        }
        return result;
    }

    private void convertProductInfoVO(ProductInfoVO productInfoVO) {
        //传入参数与申请前的询价接口参数不匹配 所以这里没做处理的情况下订单没有价格
        //即使有多条数据 只要类型是指定类型就添加data
        //页面集成类计费为固定规格，从页面配置的与代码中硬编码相对应
        if (StrUtil.equalsIgnoreCase("dws", productInfoVO.getProductCode())
                ||StrUtil.equalsIgnoreCase("mrs", productInfoVO.getProductCode())
                ||StrUtil.equalsIgnoreCase("cbr", productInfoVO.getProductCode())) {
            Map<String, Object> map;
            if (Objects.isNull(productInfoVO.getData())) {
                map = new HashMap<>();
            } else {
                map = BeanUtil.beanToMap(productInfoVO.getData());
            }
            if (map.containsKey("DWS")||map.containsKey("MRS")) {
                return;
            }
            if(StrUtil.equalsIgnoreCase("cbr", productInfoVO.getProductCode())){
                final Object cbr = map.get("CBR");
                Map<String, Object> m = BeanUtil.beanToMap(cbr);
                if("gb".equals(m.get("sizeType"))){
                    m.put("size",m.get("sizeGB"));
                }else{
                    if(ObjectUtil.isEmpty(m.get("sizeTB"))){
                        throw new BizException("参数错误");
                    }
                    m.put("size",Integer.parseInt(m.get("sizeTB").toString())*1024);
                }
                map.put("MRS",m);
                productInfoVO.setData(map);
            }
            if(StrUtil.equalsIgnoreCase("dws", productInfoVO.getProductCode())){
                Map<Object, Object> build = MapUtil.builder()
                                                   .put("productCode", "DWS")
                                                   .put("chargeItemCategory", "compute")
                                                   .put("category", "dws")
                                                   .put("spec", "dws").build();
                map.put("DWS", build);
                productInfoVO.setData(map);
            }
            if(StrUtil.equalsIgnoreCase("mrs", productInfoVO.getProductCode())){
                Map<Object, Object> build = MapUtil.builder()
                                                   .put("productCode", "MRS")
                                                   .put("chargeItemCategory", "compute")
                                                   .put("category", "mrs")
                                                   .put("spec", "mrs").build();
                map.put("MRS", build);
                productInfoVO.setData(map);
            }
        }
    }

    @Override
    public InquiryPriceResponse convertPriceResponse(InquiryPriceResult inquiryPriceResult, String instanceChargeType) {
        InquiryPriceResponse response = new InquiryPriceResponse();
        if (Objects.nonNull(inquiryPriceResult.getContractId())) {
            response = BeanConvertUtil.convert(inquiryPriceResult, InquiryPriceResponse.class);
            response.setServiceAmount(BigDecimal.ZERO);
            response.setResourceAmount(BigDecimal.ZERO);
            return response;
        }
        response.setCurrency("CNY");
        response.setPlatformDiscount(inquiryPriceResult.getPlatformDiscount());
        response.setDiscountDetails(inquiryPriceResult.getDiscounts());
        response.setBillingPrices(inquiryPriceResult.getBillingPrices());
        response.setProductCode(inquiryPriceResult.getProductCode());
        response.setCouponAmount(inquiryPriceResult.getCouponAmount());
        response.setFloatingRatio(inquiryPriceResult.getFloatingRatio());
        // 固定价格，需要乘以购买份数
        response.setOriginalOncePrice(inquiryPriceResult.getOriginalOncePrice());
        response.setTradeOncePrice(inquiryPriceResult.getTradeOncePrice());
        response.setDiscountOncePrice(response.getOriginalOncePrice().subtract(response.getTradeOncePrice()));
        if (BillingConstants.ChargeType.PRE_PAID.equals(instanceChargeType)) {
            response.setOriginalPrice(inquiryPriceResult.getOriginalMonthPrice());
            response.setTradePrice(NumberUtil.sub(inquiryPriceResult.getTradeMonthPrice(), inquiryPriceResult.getCouponAmount()));
            response.setDiscountPrice(response.getOriginalPrice().subtract(response.getTradePrice()));
            response.setChargeUnit("month");
            response.setResourceAmount(inquiryPriceResult.getResourceMonthAmount());
            response.setServiceAmount(inquiryPriceResult.getServiceMonthAmount());
        } else {
            response.setOriginalPrice(inquiryPriceResult.getOriginalHourPrice());
            response.setTradePrice(NumberUtil.sub(inquiryPriceResult.getTradeHourPrice(), inquiryPriceResult.getCouponAmount()));
            response.setDiscountPrice(response.getOriginalPrice().subtract(response.getTradePrice()));
            response.setChargeUnit("hour");
            response.setResourceAmount(inquiryPriceResult.getResourceHourAmount());
            response.setServiceAmount(inquiryPriceResult.getServiceHourAmount());
        }
        if (response.getTradePrice().compareTo(BigDecimal.ZERO)< 0){
            response.setCouponAmount(NumberUtil.add(inquiryPriceResult.getCouponAmount(),response.getTradePrice()));
            response.setTradePrice(BigDecimal.ZERO);
        }
        response.setProductCategory(inquiryPriceResult.getProductCategory());
        return response;
    }
    /**
     * 最终返回价
     * @param inquiryPriceResult
     * @param chargeType
     * @param products
     * @param envId
     * @param period
     * @param amount
     * @param accountId
     * @return
     */
    private InquiryPriceResult operateResult(InquiryPriceResult inquiryPriceResult, String chargeType, List<String> products,
        Long envId, BigDecimal period, Integer amount,
        Long accountId, boolean isPublic) {
        // 汇总价格
        BigDecimal periodValue = BigDecimal.ONE;
        int amountValue = 1;
        if (Objects.nonNull(period) && period.compareTo(BigDecimal.ONE) == 1) {
            periodValue = period;
        }
        if (Objects.nonNull(amount) && amount > 0) {
            amountValue = amount;
        }
        amountSummary(inquiryPriceResult, periodValue, amountValue, isPublic);

        inquiryPriceResult.setDiscountHourPrice(inquiryPriceResult.getOriginalHourPrice());
        inquiryPriceResult.setDiscountMonthPrice(inquiryPriceResult.getOriginalMonthPrice());
        inquiryPriceResult.setTradeHourPrice(inquiryPriceResult.getOriginalHourPrice());
        inquiryPriceResult.setTradeMonthPrice(inquiryPriceResult.getOriginalMonthPrice());
        inquiryPriceResult.setOriginalOncePrice(NumberUtil.mul(inquiryPriceResult.getOriginalOncePrice(), amount));
        inquiryPriceResult.setDiscountOncePrice(NumberUtil.mul(inquiryPriceResult.getOriginalOncePrice(), amount));
        inquiryPriceResult.setTradeOncePrice(inquiryPriceResult.getOriginalOncePrice());
        inquiryPriceResult.setCurrency("CNY");
        inquiryPriceResult.setPlatformDiscount(BigDecimal.ONE);

        boolean appoint = false;


        inquiryPriceResult = convertPriceResult(inquiryPriceResult, period, amount, appoint, isPublic);
        products = products.stream().distinct().collect(Collectors.toList());
        return setDiscountsAmount(chargeType, products, envId, period, amount, accountId, inquiryPriceResult);
    }

    private InquiryPriceResult convertPriceResult(
            InquiryPriceResult inquiryPriceResult, BigDecimal period, Integer amount, boolean appoint, boolean isPublic) {


        //公有云在服务及配置的价格上，加上底层返回的资源价格
        if (isPublic) {
            inquiryPriceResult.setOriginalHourPrice(inquiryPriceResult.getOriginalHourPrice().setScale(DECIMAL_FIX, BigDecimal.ROUND_UP)
                .add(inquiryPriceResult.getResourceHourAmount()));
            inquiryPriceResult.setTradeHourPrice(inquiryPriceResult.getTradeHourPrice().setScale(DECIMAL_FIX, BigDecimal.ROUND_UP)
                .add(inquiryPriceResult.getResourceHourAmount()));

            inquiryPriceResult.setOriginalMonthPrice(inquiryPriceResult.getOriginalMonthPrice().setScale(DECIMAL_FIX, BigDecimal.ROUND_UP)
                .add(inquiryPriceResult.getResourceMonthAmount()));
            inquiryPriceResult.setTradeMonthPrice(inquiryPriceResult.getTradeMonthPrice().setScale(DECIMAL_FIX, BigDecimal.ROUND_UP)
                .add(inquiryPriceResult.getResourceMonthAmount()));
        }

        return inquiryPriceResult;
    }

    /**
     * 计算资源价格
     * @param inquiryPriceResult
     * @param billingMap
     */
    private void generateAccountPrice(InquiryInstancePrice inquiryInstancePrice, InquiryPriceResult inquiryPriceResult,
        Map<String, Object> billingMap, CloudEnv cloudEnv) {
        // 获取资源计费配置信息
        QueryWrapper<BizBillingRegionResource> resourceQueryWrapper = new QueryWrapper<>();
        resourceQueryWrapper.lambda().eq(BizBillingRegionResource::getResourceType, billingMap.get(DescribePriceTypeEnum.PRODUCT_TYPE.getProp()))
            .eq(BizBillingRegionResource::getRegion, cloudEnv.getRegion()).eq(BizBillingRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId());

        List<BizBillingRegionResource> regionResources = bizBillingRegionResourceService.list(resourceQueryWrapper);
        if (CollectionUtil.isEmpty(regionResources)) {
            // 查询所有区域的资源类型配置
            QueryWrapper<BizBillingRegionResource> allQuery = new QueryWrapper<>();
            allQuery.lambda().eq(BizBillingRegionResource::getResourceType, billingMap.get(DescribePriceTypeEnum.PRODUCT_TYPE.getProp()))
                .eq(BizBillingRegionResource::getRegion, ALL_REGION).eq(BizBillingRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId());
            regionResources = bizBillingRegionResourceService.list(allQuery);
            if (CollectionUtil.isEmpty(regionResources)) {
                // 区域资源计费未配置
                return;
            }
        }
        BizBillingRegionResource regionResource = regionResources.get(0);

        // 获取区域资源类型计费配置
        QueryWrapper<BizBillingRegionCharge> regionChargeQuery = new QueryWrapper<>();
        regionChargeQuery.lambda().eq(BizBillingRegionCharge::getRegionResourceId, regionResource.getId());
        List<BizBillingRegionCharge> regionCharges = bizBillingRegionChargeService.list(regionChargeQuery);

        Map<String, List<BizBillingRegionCharge>> regionChargeMap = regionCharges.stream().collect(Collectors.groupingBy(BizBillingRegionCharge::getCategory));

        // 计算资源询价
        generateComputePrice(inquiryPriceResult, regionChargeMap.get(ResourceTypeEnum.COMPUTE.getCode()), inquiryInstancePrice, billingMap);

        // 存储资源询价
        generateStoragePrice(inquiryPriceResult, regionChargeMap.get(ResourceTypeEnum.STORAGE.getCode()), inquiryInstancePrice, billingMap);

        //网络资源询价
        generateNetworkPrice(inquiryPriceResult, regionChargeMap.get(ResourceTypeEnum.NETWORK.getCode()), inquiryInstancePrice, billingMap);

        // 汇总价格
        inquiryPriceResult.setOriginalHourPrice(inquiryPriceResult.getBillingPrices().stream()
            .map(BizBillingPriceVO::getHourPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        inquiryPriceResult.setOriginalMonthPrice(inquiryPriceResult.getBillingPrices().stream()
            .map(BizBillingPriceVO::getMonthPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        inquiryPriceResult.setOriginalOncePrice(inquiryPriceResult.getBillingPrices().stream()
            .map(BizBillingPriceVO::getOncePrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

    }

    private void generateAccountPrice(ProductInfoVO productInfoVO, InquiryPriceResult inquiryPriceResult
        , CloudEnv cloudEnv) {
        //获取计费项
        JsonNode jsonNode = JsonUtil.fromJson(JsonUtil.toJson(productInfoVO.getData()));

        List<Map<String, Object>> specMapping = specMapping(jsonNode, cloudEnv);
        // 获取资源计费配置信息
        QueryWrapper<BizBillingRegionResource> resourceQueryWrapper = new QueryWrapper<>();
        resourceQueryWrapper.lambda().eq(BizBillingRegionResource::getResourceType, productInfoVO.getProductCode())
                            .eq(BizBillingRegionResource::getRegion, cloudEnv.getRegion()).eq(BizBillingRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId());

        List<BizBillingRegionResource> regionResources = bizBillingRegionResourceService.list(resourceQueryWrapper);
        if (CollectionUtil.isEmpty(regionResources)) {
            // 查询所有区域的资源类型配置
            QueryWrapper<BizBillingRegionResource> allQuery = new QueryWrapper<>();
            allQuery.lambda().eq(BizBillingRegionResource::getResourceType, productInfoVO.getProductCode())
                    .eq(BizBillingRegionResource::getRegion, ALL_REGION).eq(BizBillingRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId());
            regionResources = bizBillingRegionResourceService.list(allQuery);
            if (CollectionUtil.isEmpty(regionResources)) {
                // 区域资源计费未配置
                return;
            }
        }

        BizBillingRegionResource regionResource = regionResources.get(0);

        // 获取区域资源类型计费配置
        QueryWrapper<BizBillingRegionCharge> regionChargeQuery = new QueryWrapper<>();
        regionChargeQuery.lambda().eq(BizBillingRegionCharge::getRegionResourceId, regionResource.getId());
        List<BizBillingRegionCharge> regionCharges = bizBillingRegionChargeService.list(regionChargeQuery);

        Map<String, List<BizBillingRegionCharge>> regionChargeMap = regionCharges.stream().collect(Collectors.groupingBy(BizBillingRegionCharge::getCategory));

        //替换客户定价
        Map<String, List<BizBillingRegionCharge>> finalRegionChargeMap = replaceCustomerPricing(regionChargeMap, productInfoVO, cloudEnv);
        //价格匹配计算
        specMapping.forEach(billingMap -> {
            // 产品规格
            if (Objects.nonNull(billingMap.get("aloneCategory"))) {
                inquiryPriceResult.setProductCategory(Convert.toStr(billingMap.get("aloneCategory")));
            }
            List<BizBillingRegionCharge> billings = finalRegionChargeMap.get(Convert.toStr(billingMap.get(CHARGE_ITEM_CATEGORY)));
            if (CollectionUtil.isEmpty(billings)) {
                return;
            }
            generatePrice(inquiryPriceResult, billings, billingMap, productInfoVO);
        });
    }

    /**
     * 代替客户定价;有责替换，无责返回
     *
     * @param regionChargeMap 地区收费地图
     */
    private Map<String, List<BizBillingRegionCharge>> replaceCustomerPricing(Map<String, List<BizBillingRegionCharge>> regionChargeMap, ProductInfoVO productInfoVO, CloudEnv cloudEnv) {
        productInfoVO.setCustomFlag(Boolean.FALSE);
        User authUser = AuthUtil.getAuthUser();
        // 获取资源计费配置信息
        QueryWrapper<BizBillingCustomRegionResource> resourceQueryWrapper = new QueryWrapper<>();
        resourceQueryWrapper.lambda().eq(BizBillingCustomRegionResource::getResourceType, productInfoVO.getProductCode())
                .eq(BizBillingCustomRegionResource::getRegion, cloudEnv.getRegion()).eq(BizBillingCustomRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId())
                .eq(BizBillingCustomRegionResource::getStatus, CustomRegionResourceStatus.enable)
                .eq(BizBillingCustomRegionResource::getOrgSid, authUser.getOrgSid());

        List<BizBillingCustomRegionResource> regionResources = bizBillingCustomRegionResourceService.list(resourceQueryWrapper);
        if (CollectionUtil.isEmpty(regionResources)) {
            // 查询所有区域的资源类型配置
            QueryWrapper<BizBillingCustomRegionResource> allQuery = new QueryWrapper<>();
            allQuery.lambda().eq(BizBillingCustomRegionResource::getResourceType, productInfoVO.getProductCode())
                    .eq(BizBillingCustomRegionResource::getRegion, ALL_REGION).eq(BizBillingCustomRegionResource::getEnvAccountId, cloudEnv.getCloudEnvAccountId())
                    .eq(BizBillingCustomRegionResource::getStatus, CustomRegionResourceStatus.enable)
                    .eq(BizBillingCustomRegionResource::getOrgSid, authUser.getOrgSid());
            regionResources = bizBillingCustomRegionResourceService.list(allQuery);
            if (CollectionUtil.isEmpty(regionResources)) {
                // 区域资源计费未配置
                return regionChargeMap;
            }
        }

        BizBillingCustomRegionResource regionResource = regionResources.get(0);

        // 获取区域资源类型计费配置
        QueryWrapper<BizBillingCustomRegionCharge> regionChargeQuery = new QueryWrapper<>();
        regionChargeQuery.lambda().eq(BizBillingCustomRegionCharge::getRegionResourceId, regionResource.getId())
                .eq(BizBillingCustomRegionCharge::getOrgSid, authUser.getOrgSid());
        List<BizBillingCustomRegionCharge> regionCharges = bizBillingCustomRegionChargeService.list(regionChargeQuery);
        if (CollectionUtil.isNotEmpty(regionCharges)) {
            productInfoVO.setCustomFlag(Boolean.TRUE);
            List<BizBillingRegionCharge> customRegionCharges = BeanUtil.copyToList(regionCharges, BizBillingRegionCharge.class);
            return customRegionCharges.stream().collect(Collectors.groupingBy(BizBillingRegionCharge::getCategory));
        }
        return regionChargeMap;
    }
    /**
     * 资源询价
     */
    private void generatePrice(InquiryPriceResult inquiryPriceResult, List<BizBillingRegionCharge> regionCharges,
        Map<String, Object> billingMap, ProductInfoVO productInfoVO) {
        BigDecimal period = productInfoVO.getPeriod();
        if (CollectionUtil.isEmpty(regionCharges)) {
            return;
        }
        regionCharges.forEach(regionCharge -> {
            BizBillingStrategy strategy = strategyService.getById(regionCharge.getStrategyId());
            // 策略未启用
            if (strategy == null || !strategy.getStatus()) {
                return;
            }
            //运营实体所对应的计费策略
            if (!Objects.equals(strategy.getEntityId(),productInfoVO.getEntityId())){
                return;
            }
            String specConfig = regionCharge.getSpecConfig();
            if (StrUtil.isEmpty(specConfig)) {
                return;
            }
            // 获取类型族和 规格族配置
            List<SpecConfigVO> specConfigs = JSONArray.parseArray(specConfig, SpecConfigVO.class);
            if (CollectionUtil.isEmpty(specConfigs)) {
                return;
            }
            if (Objects.isNull(billingMap.get("allSpecDisplay"))) {
                specConfigs = specConfigs.stream().filter(spec -> spec.getType()
                        .equals(Convert.toStr(billingMap.get("category")))
                        || CollectionUtil.contains(DEFAULT, spec.getType())
                        || spec.getTypeName().equals(Convert.toStr(billingMap.get("category")))).collect(Collectors.toList());
            }
            List<Long> specGroupIds = specConfigs.stream().map(SpecConfigVO::getSpecGroupId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(specGroupIds)) {
                return;
            }

            Collection<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.listByIds(specGroupIds);
            Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
            if (CollectionUtil.isEmpty(specGroups)) {
                return;
            }
            specConfigs.forEach(specConfigVO -> {
                //类型族名称
                Long specGroupId = specConfigVO.getSpecGroupId();
                BizBillingSpecGroup specGroup = specGroupMap.get(specGroupId);
                if (specGroup == null) {
                    return;
                }

                // 计费规格项
                Set<String> specIdSet = specGroups.stream().map(BizBillingSpecGroup::getSpecId).collect(Collectors.toSet());
                List<String> specIds = Lists.newArrayList();
                specIdSet.forEach(str -> {
                    specIds.addAll(Arrays.asList(StrUtil.splitToArray(str, StrUtil.COMMA)));
                });
                Collection<BizBillingSpec> specs = specService.listByIds(specIds);
                if (CollectionUtil.isEmpty(specs)) {
                    return;
                }

                Map<Long, BizBillingSpec> specMap = specs.stream()
                        .collect(Collectors.toMap(BizBillingSpec::getId, o -> o, (key1, key2) -> key1));

                Map<String, BizBillingSpec> specStrMap = specs.stream()
                        .collect(Collectors.toMap(BizBillingSpec::getSpecName, o -> o, (key1, key2) -> key1));

                QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
                specChargeQuery.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
                List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
                if (CollectionUtil.isEmpty(specCharges)) {
                    return;
                }
                String category = Convert.toStr(billingMap.get("category"));
                // 检查计费原始数据是否与规格定价匹配
                for (BizBillingTariffSpecCharge specCharge : specCharges) {
                    if (BillingChargeType.STATIC_TYPE.equals(specGroup.getBillingMode())) {
                        String billingConfig = specCharge.getBillingConfig();

                        // 解析规格配置信息
                        Map<String, String> specChargeMap = JsonUtil
                                .fromJson(billingConfig, new TypeReference<Map<String, String>>() {
                                });

                        boolean specMatched = matchPriceSpec(specChargeMap, billingMap);
                        // 计费规格能与计费元数据匹配，计算对应价格
                        if (specMatched) {
                            BizBillingPriceVO priceVO = new BizBillingPriceVO();
                            priceVO.setCustomFlag(productInfoVO.getCustomFlag());
                            priceVO.setCategory(regionCharge.getCategory());
                            priceVO.setPriceType(PriceType.RESOURCE);
                            priceVO.setResourceType(billingMap.get("uuid") != null
                                    ? Convert.toStr(billingMap.get("uuid")) : category);
                            priceVO.setPriceDesc(ProductCodeEnum.toDesc(Convert.toStr(billingMap.get("productCode"))));
                            priceVO.setBillingSpec(specService.packageSpecDesc(billingConfig, specStrMap, specGroup.getSpec()));
                            priceVO.setResourceConfig(JSON.toJSONString(billingMap));
                            priceVO.setIdentification(Convert.toStr(billingMap.get("identification") + priceVO.getResourceType()));
                            priceVO.setProductCode(Convert.toStr(billingMap.get("productCode")));

                            // 需要取得单价信息
                            if (Objects.nonNull(billingMap.get("unitPriceDisplay"))) {
                                // 按量计费单价
                                // 资源单价+周期服务费用+周期额外费用
                                BigDecimal unitPrice = priceVO.getUnitHourPrice().add(specCharge.getHourPrice())
                                        .add(inquiryPriceResult.getServiceHourAmount());
                                priceVO.setUnitHourPrice(unitPrice);
                                // 单次额外费用
                                priceVO.setUnitOncePrice(inquiryPriceResult.getServiceOnceAmount());

                                // 单价折扣计算
                                BigDecimal discount = unitPriceDiscount(productInfoVO.getCloudEnvId(), productInfoVO.getBillingAccountId(),
                                        unitPrice, inquiryPriceResult.getProductCode(),productInfoVO.getChargeType(), productInfoVO.getPointInTime());
                                priceVO.setPlatformDiscount(discount);
                                priceVO.setTradeUnitHourPrice(unitPrice.multiply(discount));
                            }

                            // 类型全表示的场合，显示类型信息
                            if (Objects.nonNull(billingMap.get("allSpecDisplay"))) {
                                priceVO.setProductType(specConfigVO.getType());
                                priceVO.setProductTypeName(specConfigVO.getTypeName());
                            }

                            // 获取匹配到的计费项
                            calculatePrice(period, priceVO, specCharge);
                            inquiryPriceResult.getBillingPrices().add(priceVO);
                            break;
                        }
                    } else {
                        Map<Long, BizBillingSpec> specMapResult = specMap;
                        if (Objects.nonNull(specCharge.getSpecType())) {
                            // 取得规格类型一致的规格（针对使用量【CPU，GPU等】）
                            specMapResult = specMap.entrySet().stream()
                                    .filter(s -> s.getValue().getSpecName().equals(specCharge.getSpecType()))
                                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                        }
                        specMapResult.forEach((id, spec) -> {
                            //获取实际规格key，前后端 定义key不同
                            Object chargeSpec = billingMap.get(spec.getDataType()) == null
                                    ? billingMap.get(spec.getSpecName()) : billingMap.get(spec.getDataType());
                            if (chargeSpec == null) {
                                return;
                            }
                            BizBillingPriceVO priceVO = new BizBillingPriceVO();
                            priceVO.setCustomFlag(productInfoVO.getCustomFlag());
                            priceVO.setCategory(regionCharge.getCategory());
                            priceVO.setPriceType(PriceType.RESOURCE);
                            priceVO.setResourceType(category);
                            priceVO.setPriceDesc(ProductCodeEnum.toDesc(Convert.toStr(billingMap.get("productCode"))));
                            priceVO.setBillingSpec(StrUtil.concat(true, category, "(",
                                    Convert.toStr(chargeSpec), spec.getUnit(), ")"));
                            priceVO.setProductCode(Convert.toStr(billingMap.get("productCode")));
                            priceVO.setResourceConfig(JSON.toJSONString(billingMap));
                            priceVO.setIdentification(Convert.toStr(billingMap.get("identification")) + chargeSpec);
                            priceVO.setSpecType(spec.getSpecName());
                            priceVO.setConvertRatio(specCharge.getConvertRatio());

                            // 需要取得单价信息
                            if (Objects.nonNull(billingMap.get("unitPriceDisplay"))) {
                                // 按量计费单价
                                // 资源单价+周期服务费用+周期额外费用
                                BigDecimal unitPrice = specCharge.getUnitHourPrice().add(inquiryPriceResult.getServiceHourAmount());
                                priceVO.setUnitHourPrice(unitPrice);
                                // 按量计费基础价格
                                BigDecimal fixedPrice = specCharge.getFixedHourPrice();
                                priceVO.setFixedHourPrice(fixedPrice);
                                // 单次额外费用
                                priceVO.setUnitOncePrice(inquiryPriceResult.getServiceOnceAmount());

                                // 单价折扣计算
                                BigDecimal discount = unitPriceDiscount(productInfoVO.getCloudEnvId(), productInfoVO.getBillingAccountId(),
                                        unitPrice.add(fixedPrice), inquiryPriceResult.getProductCode(),productInfoVO.getChargeType(), productInfoVO.getPointInTime());
                                priceVO.setPlatformDiscount(discount);
                                priceVO.setTradeUnitHourPrice(unitPrice.multiply(discount));
                                priceVO.setTradeFixedHourPrice(fixedPrice.multiply(discount));
                                // 类型全表示的场合，显示类型信息
                                if (Objects.nonNull(billingMap.get("allSpecDisplay"))) {
                                    priceVO.setProductType(specConfigVO.getType());
                                    priceVO.setProductTypeName(specConfigVO.getTypeName());
                                }
                            }
                            computePrice(specCharge, chargeSpec, priceVO, inquiryPriceResult,
                                    spec.getSpecExpression());

                            List<BizBillingPriceVO> priceVOS = inquiryPriceResult.getBillingPrices().stream()
                                    .filter(BizBillingPriceVO::getAppoint).collect(Collectors.toList());
                            // 存在指定包年包月计费配置， 存储和网络单独乘月数，后面处理价格不再乘以月数
                            if (CollectionUtil.isNotEmpty(priceVOS)) {
                                priceVO.setMonthPrice(NumberUtil.mul(priceVO.getMonthPrice(), period));
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 计算资源询价
     *
     * @param inquiryPriceResult
     * @param regionCharges
     * @param inquiryInstancePrice
     * @param billingMap
     */
    private void generateComputePrice(InquiryPriceResult inquiryPriceResult, List<BizBillingRegionCharge> regionCharges,
                                      InquiryInstancePrice inquiryInstancePrice, Map<String, Object> billingMap) {
        if (CollectionUtil.isEmpty(regionCharges)) {
            return;
        }
        regionCharges.forEach(regionCharge -> {
            BizBillingStrategy strategy = strategyService.getById(regionCharge.getStrategyId());
            // 策略未启用
            if (strategy == null || !strategy.getStatus()) {
                return;
            }
            String specConfig = regionCharge.getSpecConfig();
            if (StrUtil.isEmpty(specConfig)) {
                return;
            }
            // 获取类型族和 规格族配置
            List<SpecConfigVO> specConfigs = JSONArray.parseArray(specConfig, SpecConfigVO.class);
            if (CollectionUtil.isEmpty(specConfigs)) {
                return;
            }
            specConfigs = specConfigs.stream().filter(spec -> spec.getType()
                    .equals(billingMap.get(DescribePriceTypeEnum.FAMILY.getProp()))).collect(Collectors.toList());
            List<Long> specGroupIds = specConfigs.stream().map(SpecConfigVO::getSpecGroupId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(specGroupIds)) {
                return;
            }
            Collection<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.listByIds(specGroupIds);
            Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                    .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
            if (CollectionUtil.isEmpty(specGroups)) {
                return;
            }
            specConfigs.forEach(specConfigVO -> {
                //类型族名称
                Long specGroupId = specConfigVO.getSpecGroupId();
                BizBillingSpecGroup specGroup = specGroupMap.get(specGroupId);
                if (specGroup == null) {
                    return;
                }

                QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
                specChargeQuery.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
                List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
                if (CollectionUtil.isEmpty(specCharges)) {
                    return;
                }
                specCharges.forEach(specCharge -> {
                    BizBillingPriceVO priceVO = new BizBillingPriceVO();
                    priceVO.setCategory(regionCharge.getCategory());

                    // 检查计费原始数据是否与规格定价匹配
                    String billingConfig = specCharge.getBillingConfig();
                    // 解析规格配置信息
                    Map<String, String> specChargeMap = JsonUtil.fromJson(billingConfig, new TypeReference<Map<String, String>>() {
                    });

                    boolean specMatched = matchPriceSpec(specChargeMap, billingMap);
                    // 计费规格能与计费元数据匹配，计算对应价格
                    if (specMatched) {
                        // 获取匹配到的计费项
                        calculateComputePrice(inquiryInstancePrice, priceVO, specCharge);
                        inquiryPriceResult.setResourceHourAmount(inquiryPriceResult.getResourceHourAmount().add(priceVO.getHourPrice()));
                        inquiryPriceResult.setResourceMonthAmount(inquiryPriceResult.getResourceMonthAmount().add(priceVO.getMonthPrice()));
                        inquiryPriceResult.getBillingPrices().add(priceVO);
                    }
                });
            });
        });
    }

    /**
     * 存储资源询价
     *
     * @param inquiryPriceResult
     * @param regionCharges
     * @param inquiryInstancePrice
     * @param billingMap
     */
    private void generateStoragePrice(InquiryPriceResult inquiryPriceResult, List<BizBillingRegionCharge> regionCharges,
                                      InquiryInstancePrice inquiryInstancePrice, Map<String, Object> billingMap) {
        if (CollectionUtil.isEmpty(regionCharges)) {
            return;
        }
        regionCharges.forEach(regionCharge -> {
            BizBillingStrategy strategy = strategyService.getById(regionCharge.getStrategyId());
            // 策略未启用
            if (strategy == null || !strategy.getStatus()) {
                return;
            }
            String specConfig = regionCharge.getSpecConfig();
            if (StrUtil.isEmpty(specConfig)) {
                return;
            }
            // 获取类型族和 规格族配置
            List<SpecConfigVO> specConfigs = JSONArray.parseArray(specConfig, SpecConfigVO.class);
            if (CollectionUtil.isEmpty(specConfigs)) {
                return;
            }
            List<Long> specGroupIds = specConfigs.stream().map(SpecConfigVO::getSpecGroupId).collect(Collectors.toList());

            QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
            specChargeQuery.lambda().in(BizBillingTariffSpecCharge::getSpecGroupId, specGroupIds);
            List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
            if (CollectionUtil.isEmpty(specCharges)) {
                return;
            }
            // 增量模式规格族对应的规格定价目前设计只有一条记录
            Map<Long, BizBillingTariffSpecCharge> specChargeMap = specCharges.stream()
                    .collect(Collectors.toMap(BizBillingTariffSpecCharge::getSpecGroupId, o -> o, (key1, key2) -> key1));

            // 类型族-规格族
            Map<String, Long> familyGroupMap = specConfigs.stream()
                    .collect(Collectors.toMap(SpecConfigVO::getType, SpecConfigVO::getSpecGroupId, (key1, key2) -> key1));

            // 规格族
            Collection<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.listByIds(specGroupIds);
            Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                    .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
            if (CollectionUtil.isEmpty(specGroups)) {
                return;
            }
            // 计费规格项
            Set<String> specIdSet = specGroups.stream().map(BizBillingSpecGroup::getSpecId).collect(Collectors.toSet());
            List<String> specIds = Lists.newArrayList();
            specIdSet.forEach(str -> {
                specIds.addAll(Arrays.asList(StrUtil.splitToArray(str, StrUtil.COMMA)));
            });
            Collection<BizBillingSpec> specs = specService.listByIds(specIds);
            if (CollectionUtil.isEmpty(specs)) {
                return;
            }
            Map<Long, BizBillingSpec> specMap = specs.stream()
                    .collect(Collectors.toMap(BizBillingSpec::getId, o -> o, (key1, key2) -> key1));

            int n = 1;
            while (billingMap.get(DescribePriceTypeEnum.VOLUME_TYPE.getProp(n)) != null) {
                BizBillingPriceVO priceVO = new BizBillingPriceVO();
                priceVO.setCategory(regionCharge.getCategory());
                Long specGroupId = familyGroupMap.get(billingMap.get(DescribePriceTypeEnum.VOLUME_TYPE.getProp(n)));
                BizBillingSpecGroup specGroup = specGroupMap.get(specGroupId);
                if (specGroup == null) {
                    continue;
                }
                BizBillingSpec spec = specMap.get(specGroup.getSpecId());
                String expression = spec.getSpecExpression();
                String[] express = StrUtil.splitToArray(expression, StrUtil.COMMA);
                if (express.length >= 1) {
                    computePrice(specChargeMap.get(specGroupId), billingMap.get(DescribePriceTypeEnum.DISK_SIZE.getProp(n)),
                            priceVO, inquiryPriceResult, express[0]);
                    inquiryPriceResult.setResourceHourAmount(inquiryPriceResult.getResourceHourAmount().add(priceVO.getHourPrice()));
                    inquiryPriceResult.setResourceMonthAmount(inquiryPriceResult.getResourceMonthAmount().add(priceVO.getMonthPrice()));
                    List<BizBillingPriceVO> priceVOS = inquiryPriceResult.getBillingPrices().stream()
                            .filter(BizBillingPriceVO::getAppoint).collect(Collectors.toList());
                    // 存在指定包年包月计费配置， 存储和网络单独乘月数，后面处理价格不再乘以月数
                    if (CollectionUtil.isNotEmpty(priceVOS)) {
                        priceVO.setMonthPrice(NumberUtil.mul(priceVO.getMonthPrice(), inquiryInstancePrice.getPeriod()));
                    }
                }
                if (express.length >= 2 && inquiryInstancePrice.getUsage() != null) {
                    computeUsagePrice(specChargeMap.get(specGroupId), inquiryInstancePrice.getUsage(),
                            priceVO, inquiryPriceResult, express[1]);
                    inquiryPriceResult.setResourceHourAmount(inquiryPriceResult.getResourceHourAmount().add(priceVO.getHourPrice()));
                    inquiryPriceResult.setResourceMonthAmount(inquiryPriceResult.getResourceMonthAmount().add(priceVO.getMonthPrice()));
                }
                n++;
            }

        });
    }

    /**
     * 网络资源询价
     *
     * @param inquiryPriceResult
     * @param regionCharges
     * @param inquiryInstancePrice
     * @param billingMap
     */
    private void generateNetworkPrice(InquiryPriceResult inquiryPriceResult, List<BizBillingRegionCharge> regionCharges,
                                      InquiryInstancePrice inquiryInstancePrice, Map<String, Object> billingMap) {
        if (CollectionUtil.isEmpty(regionCharges)) {
            return;
        }
        regionCharges.forEach(regionCharge -> {
            BizBillingStrategy strategy = strategyService.getById(regionCharge.getStrategyId());
            // 策略未启用
            if (strategy == null || !strategy.getStatus()) {
                return;
            }
            String specConfig = regionCharge.getSpecConfig();
            if (StrUtil.isEmpty(specConfig)) {
                return;
            }
            // 获取类型族和 规格族配置
            List<SpecConfigVO> specConfigs = JSONArray.parseArray(specConfig, SpecConfigVO.class);
            if (CollectionUtil.isEmpty(specConfigs)) {
                return;
            }
            if (billingMap.get(DescribePriceTypeEnum.NETWORK_TYPE.getProp()) != null) {
                specConfigs = specConfigs.stream().filter(spec -> spec.getType()
                        .equals(billingMap.get(DescribePriceTypeEnum.NETWORK_TYPE.getProp())))
                        .collect(Collectors.toList());
            } else {
                specConfigs = specConfigs.stream().filter(spec -> PUBLIC.equals(spec.getTypeName()))
                        .collect(Collectors.toList());
            }

            List<Long> specGroupIds = specConfigs.stream().map(SpecConfigVO::getSpecGroupId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(specGroupIds)) {
                return;
            }
            Collection<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.listByIds(specGroupIds);
            Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                    .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
            if (CollectionUtil.isEmpty(specGroups)) {
                return;
            }
            Set<String> specIdSet = specGroups.stream().map(BizBillingSpecGroup::getSpecId).collect(Collectors.toSet());
            List<String> specIds = Lists.newArrayList();
            specIdSet.forEach(str -> {
                specIds.addAll(Arrays.asList(StrUtil.splitToArray(str, StrUtil.COMMA)));
            });
            Collection<BizBillingSpec> specs = specService.listByIds(specIds);
            if (CollectionUtil.isEmpty(specs)) {
                return;
            }
            Map<Long, BizBillingSpec> specMap = specs.stream()
                    .collect(Collectors.toMap(BizBillingSpec::getId, o -> o, (key1, key2) -> key1));

            specConfigs.forEach(specConfigVO -> {
                //类型族名称
                Long specGroupId = specConfigVO.getSpecGroupId();
                BizBillingSpecGroup specGroup = specGroupMap.get(specGroupId);
                if (specGroup == null) {
                    return;
                }

                QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
                specChargeQuery.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
                List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
                if (CollectionUtil.isEmpty(specCharges)) {
                    return;
                }

                BizBillingSpec spec = specMap.get(specGroup.getSpecId());
                String expression = spec.getSpecExpression();
                specCharges.forEach(specCharge -> {
                    BizBillingPriceVO priceVO = new BizBillingPriceVO();
                    priceVO.setCategory(regionCharge.getCategory());
                    Object paramValue = null;
                    if (inquiryInstancePrice.getUsage() != null) {
                        paramValue = inquiryInstancePrice.getUsage();
                    } else if (billingMap.get(DescribePriceTypeEnum.IP_BAND_WIDTH.getProp()) != null) {
                        paramValue = billingMap.get(DescribePriceTypeEnum.IP_BAND_WIDTH.getProp());
                    }
                    if (Convert.toInt(paramValue) > 0) {
                        computePrice(specCharge, paramValue, priceVO, inquiryPriceResult, expression);
                        inquiryPriceResult.setResourceHourAmount(inquiryPriceResult.getResourceHourAmount().add(priceVO.getHourPrice()));
                        inquiryPriceResult.setResourceMonthAmount(inquiryPriceResult.getResourceMonthAmount().add(priceVO.getMonthPrice()));
                        List<BizBillingPriceVO> priceVOS = inquiryPriceResult.getBillingPrices().stream()
                                .filter(BizBillingPriceVO::getAppoint).collect(Collectors.toList());
                        // 存在指定包年包月计费配置， 存储和网络单独乘月数，后面处理价格不再乘以月数
                        if (CollectionUtil.isNotEmpty(priceVOS)) {
                            priceVO.setMonthPrice(NumberUtil.mul(priceVO.getMonthPrice(), inquiryInstancePrice.getPeriod()));
                            inquiryPriceResult.setResourceMonthAmount(priceVO.getMonthPrice());
                        }
                    }
                });
            });
        });
    }

    /**
     * 计算组织折扣
     *
     * @param inquiryPriceResult
     * @param billingAccount
     */
    private void processOrgDiscount(InquiryPriceResult inquiryPriceResult, BizBillingAccount billingAccount) {
        if (Objects.isNull(billingAccount)) {
            return;
        }

        if (Objects.nonNull(billingAccount.getDiscount())
                && billingAccount.getDiscount().doubleValue() > 0.0
                && billingAccount.getDiscount().doubleValue() <= 1.0) {
            inquiryPriceResult.setPlatformDiscount(billingAccount.getDiscount());
            inquiryPriceResult.setTradeHourPrice(inquiryPriceResult.getOriginalHourPrice().multiply(billingAccount.getDiscount()));
            inquiryPriceResult.setTradeMonthPrice(inquiryPriceResult.getOriginalMonthPrice().multiply(billingAccount.getDiscount()));
            inquiryPriceResult.setTradeOncePrice(inquiryPriceResult.getOriginalOncePrice().multiply(billingAccount.getDiscount()));

            inquiryPriceResult.setDiscountHourPrice(inquiryPriceResult.getOriginalHourPrice()
                    .subtract(inquiryPriceResult.getTradeHourPrice()).setScale(DECIMAL_FIX, BigDecimal.ROUND_UP));
            inquiryPriceResult.setDiscountMonthPrice(inquiryPriceResult.getOriginalMonthPrice()
                    .subtract(inquiryPriceResult.getTradeMonthPrice()).setScale(DECIMAL_FIX, BigDecimal.ROUND_UP));
            inquiryPriceResult.setDiscountOncePrice(inquiryPriceResult.getOriginalOncePrice()
                    .subtract(inquiryPriceResult.getTradeOncePrice()).setScale(DECIMAL_FIX, BigDecimal.ROUND_UP));
        }
    }

    /**
     * 计算服务价格
     *
     * @param inquiryPriceResult
     * @param serviceId
     * @return
     */
    @Override
    public InquiryPriceResult generateServicePrice(InquiryPriceResult inquiryPriceResult, Long serviceId, BigDecimal period) {
        if (Objects.isNull(serviceId)) {
            return inquiryPriceResult;
        }

        List<BizBillingPriceVO> priceVOs = calculateServicePrice(serviceId, period);
        if (CollectionUtil.isNotEmpty(priceVOs)) {
            priceVOs.forEach(priceVO -> {
                inquiryPriceResult.setOriginalHourPrice(inquiryPriceResult.getOriginalHourPrice().add(priceVO.getHourPrice()));
                inquiryPriceResult.setOriginalMonthPrice(inquiryPriceResult.getOriginalMonthPrice().add(priceVO.getMonthPrice()));
                inquiryPriceResult.setOriginalOncePrice(inquiryPriceResult.getOriginalOncePrice().add(priceVO.getOncePrice()));
                inquiryPriceResult.setServiceHourAmount(inquiryPriceResult.getServiceHourAmount().add(priceVO.getHourPrice()));
                inquiryPriceResult.setServiceMonthAmount(inquiryPriceResult.getServiceMonthAmount().add(priceVO.getMonthPrice()));
                inquiryPriceResult.setServiceOnceAmount(inquiryPriceResult.getServiceOnceAmount().add(priceVO.getOncePrice()));
                inquiryPriceResult.getBillingPrices().add(priceVO);
            });
        }
        return inquiryPriceResult;
    }

    @Override
    public ServicePriceDetailNewVO getServicePriceDetailNew(Long serviceId, String userSid) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296864863));
        }

        ServicePriceDetailNewVO response = new ServicePriceDetailNewVO();

        // 获取产品配置
        QueryWrapper<BizBillingStrategyServing> servingQueryWrapper = new QueryWrapper<>();
        servingQueryWrapper.eq("service_id", serviceId);
        List<BizBillingStrategyServing> strategyServings = servingService.list(servingQueryWrapper);
        String resourceType = "";
        if (CollectionUtils.isEmpty(strategyServings)) {
            response.setServicePriced(false);
        } else {
            response.setServicePriced(true);
            BizBillingStrategyServing strategyServing = strategyServings.get(0);
            BizBillingStrategyServingVO strategyServingVO = BeanConvertUtil.convert(
                    strategyServing, BizBillingStrategyServingVO.class);
            strategyServingVO.setExtraHourPrice(keepThreeDecimals(strategyServingVO.getExtraHourPrice()));
            strategyServingVO.setExtraMonthPrice(keepThreeDecimals(strategyServingVO.getExtraMonthPrice()));
            strategyServingVO.setExtraOncePrice(keepThreeDecimals(strategyServingVO.getExtraOncePrice()));
            response.setStrategyServing(strategyServingVO);
            response.setResourceType(strategyServing.getResourceType());
            if (!StringUtils.isEmpty(strategyServing.getSpecChargeIds())) {
                String[] specChargeIdArray = strategyServing.getSpecChargeIds().split(",");
                List<String> specChargeIds = Arrays.asList(specChargeIdArray);
                if (CollectionUtils.isNotEmpty(specChargeIds)) {
                    QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
                    chargeQueryWrapper.in("id", specChargeIds);
                    List<BizBillingTariffSpecCharge> charges = chargeService.list(chargeQueryWrapper);
                    if (CollectionUtils.isNotEmpty(charges)) {
                        for (BizBillingTariffSpecCharge specCharge : charges) {
                            generateServiceSpecChargeDetailNew(response, specCharge);
                        }
                    }
                }
            }
            if (CHARGE_NONE.equals(strategyServing.getResourceType())) {
                response.setResourceCharged(false);
                return response;
            }
            //是否无资源计费
            response.setResourceCharged(true);
            resourceType = strategyServing.getResourceType();
        }

        return getResouecePriceConfig(response, resourceType, userSid);
    }

    /**
     * 获取资源价格配置
     *
     * @param response
     * @param resourceType
     * @return
     */
    private ServicePriceDetailNewVO getResouecePriceConfig(ServicePriceDetailNewVO response, String resourceType, String userSid) {
        // endregion
        // region 直接从环境表里取得环境信息
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType("HCSO");
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(cloudEnvParams);
        CloudEnv cloudEnv = Objects.isNull(cloudEnvs) ? null : cloudEnvs.get(0);
        if (cloudEnv == null) {
            return response;
        }
        ServiceAccountPriceDetailVO accountPriceDetail = getAccountPriceDetailNew(cloudEnv.getCloudEnvAccountId(), resourceType,
                cloudEnv.getRegion(), userSid);
        if (Objects.nonNull(accountPriceDetail)) {
            if (accountPriceDetail.getPriced()) {
                response.setAccountPricedStatus("all");
            }
            response.getAccountDetails().add(accountPriceDetail);
        }
        // endregion
        return response;
    }

    /**
     * 获取询价元数据信息
     *
     * @param inquiryBase
     * @param account
     * @param cloudEnv
     * @return
     */
    private Map<String, Object> generateBillingMap(InquiryBase inquiryBase, CloudEnvAccount account, CloudEnv cloudEnv) {
        Map<String, Object> billingMap = Maps.newHashMap();
        billingMap.put(DescribePriceTypeEnum.OBJ_ID.getProp(), inquiryBase.getEnvId());
        billingMap.put(DescribePriceTypeEnum.OBJ_TYPE.getProp(), BillingConfigObjType.CLOUD_ENV);
        if (inquiryBase instanceof InquiryInstancePrice) {
            InquiryInstancePrice inquiryInstancePrice = (InquiryInstancePrice) inquiryBase;
            if (billingMap.get(DescribePriceTypeEnum.PRODUCT_TYPE.getProp()) == null) {
                if (StrUtil.isNotEmpty(inquiryInstancePrice.getInstanceType())) {
                    billingMap.put(DescribePriceTypeEnum.PRODUCT_TYPE.getProp(), ECS);
                } else if (inquiryInstancePrice.getInternetMaxBandwidthOut() != null) {
                    billingMap.put(DescribePriceTypeEnum.PRODUCT_TYPE.getProp(), EIP);
                } else if (StrUtil.isEmpty(inquiryInstancePrice.getInstanceType())
                        && (CollectionUtil.isNotEmpty(inquiryInstancePrice.getDataDisks())
                        || inquiryInstancePrice.getSystemDisk() != null)) {
                    billingMap.put(DescribePriceTypeEnum.PRODUCT_TYPE.getProp(), EBS);
                } else {
                    billingMap.put(DescribePriceTypeEnum.PRODUCT_TYPE.getProp(), inquiryBase.getResourceType());
                }
            }

            // 分区
            billingMap.put(DescribePriceTypeEnum.REGION.getProp(), inquiryInstancePrice.getZone());
            // 实例类型
            billingMap.put(DescribePriceTypeEnum.INSTANCE_TYPE.getProp(), inquiryInstancePrice.getInstanceType());
            // 付费类型
            billingMap.put(DescribePriceTypeEnum.CHARGE_TYPE.getProp(), inquiryInstancePrice.getInstanceChargeType());

            ResVmType resVmType = null;

            QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
            queryResVmTypeByParamsRequest.setCloudEnvId(cloudEnv.getId());
            queryResVmTypeByParamsRequest.setUuid(inquiryInstancePrice.getInstanceType());
            List<ResVmType> resVmTypes = vmTypeService.selectByParams(queryResVmTypeByParamsRequest);
            if (CollectionUtils.isNotEmpty(resVmTypes)) {
                resVmType = resVmTypes.get(0);
                billingMap.put(DescribePriceTypeEnum.CPU.getProp(), resVmType.getCpu());
                // 内存单位MB转化为GB
                billingMap.put(DescribePriceTypeEnum.MEMORY.getProp(), resVmType.getRam().intValue() / GB_TO_MB_SIZE);

                billingMap.put(DescribePriceTypeEnum.FAMILY.getProp(), resVmType.getFamily());
            } else {
                try {
                    resVmType = vmTypeService.selectByPrimaryKey(Long.parseLong(inquiryInstancePrice.getInstanceType()));
                    if (Objects.nonNull(resVmType)) {
                        billingMap.put(DescribePriceTypeEnum.CPU.getProp(), resVmType.getCpu());
                        // 内存单位MB转化为GB
                        billingMap.put(DescribePriceTypeEnum.MEMORY.getProp(), resVmType.getRam().intValue() / GB_TO_MB_SIZE);

                        billingMap.put(DescribePriceTypeEnum.INSTANCE_TYPE.getProp(), resVmType.getUuid());
                        billingMap.put(DescribePriceTypeEnum.FAMILY.getProp(), resVmType.getFamily());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e.getMessage());
                }
            }

            int index = 1;
            if (Objects.nonNull(inquiryInstancePrice.getSystemDisk())) {
                DiskSpec diskSpec = inquiryInstancePrice.getSystemDisk();
                if (CloudEnvType.FUSIONCOMPUTE.valueContains(cloudEnv.getCloudEnvType())) {
                    billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index, diskSpec.getCategory());
                } else if (CloudEnvType.HCSO.valueContains(cloudEnv.getCloudEnvType())) {
                    billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index, diskSpec.getCategory());
                } else {
                    ResVolumeType resVolumeType = volumeTypeService.selectByPrimaryKey(Long.valueOf(diskSpec.getCategory()));
                    if (Objects.nonNull(resVolumeType)) {
                        billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index, resVolumeType.getUuid());
                    }
                }
                billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                index++;
            } else if (CloudEnvType.OPEN_STACK.valueContains(cloudEnv.getCloudEnvType())) {
                if (Objects.nonNull(resVmType)) {

                    billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                            VolumeType.DEFAULT_VOLUME_UUID);

                }
            }

            if (CollectionUtils.isNotEmpty(inquiryInstancePrice.getDataDisks())) {
                for (DiskSpec diskSpec : inquiryInstancePrice.getDataDisks()) {
                    //存储类型 uuid
                    if (CloudEnvType.FUSIONCOMPUTE.valueContains(cloudEnv.getCloudEnvType())) {
                        billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                                diskSpec.getCategory());
                        billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                    } else if (CloudEnvType.HCSO.valueContains(cloudEnv.getCloudEnvType())) {
                        billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                                diskSpec.getCategory());
                        billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                    } else {
                        ResVolumeType resVolumeType = volumeTypeService.selectByPrimaryKey(Long.valueOf(diskSpec.getCategory()));
                        if (Objects.nonNull(resVolumeType)) {
                            billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                                    resVolumeType.getUuid());
                            billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                        }
                    }
                    index++;
                }
            }

            billingMap.put(DescribePriceTypeEnum.FLOATING_IP.getProp(), inquiryInstancePrice.getFloatingIpNum());
            billingMap.put(DescribePriceTypeEnum.NETWORK_TYPE.getProp(), inquiryInstancePrice.getNetworkType());
            billingMap.put(DescribePriceTypeEnum.IP_BAND_WIDTH.getProp(),
                    inquiryInstancePrice.getInternetMaxBandwidthOut());
            billingMap.put(DescribePriceTypeEnum.IP.getProp(), inquiryInstancePrice.getIpNum());
            billingMap.put(DescribePriceTypeEnum.CLOUID_SERVICE.getProp(), inquiryInstancePrice.getServiceId());
            billingMap.put(DescribePriceTypeEnum.USAGE.getProp(), inquiryInstancePrice.getUsage());
        } else if (inquiryBase instanceof InquiryDiskPrice) {
            InquiryDiskPrice inquiryDiskPrice = (InquiryDiskPrice) inquiryBase;
            if (CollectionUtils.isNotEmpty(inquiryDiskPrice.getDataDisks())) {
                int index = 1;
                for (DiskSpec diskSpec : inquiryDiskPrice.getDataDisks()) {
                    //存储类型 uuid
                    if (CloudEnvType.FUSIONCOMPUTE.valueContains(cloudEnv.getCloudEnvType())) {
                        billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                                diskSpec.getCategory());
                        billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                    } else {
                        ResVolumeType resVolumeType = volumeTypeService.selectByPrimaryKey(Long.valueOf(diskSpec.getCategory()));
                        if (Objects.nonNull(resVolumeType)) {
                            billingMap.put(DescribePriceTypeEnum.VOLUME_TYPE.getProp() + "." + index,
                                    resVolumeType.getUuid());
                            billingMap.put(DescribePriceTypeEnum.DISK_SIZE.getProp() + "." + index, diskSpec.getSize());
                        }
                    }
                    index++;
                }
            }
        } else if (inquiryBase instanceof InquiryFloatingIpPrice) {
            InquiryFloatingIpPrice inquiryFloatingIpPrice = (InquiryFloatingIpPrice) inquiryBase;
            billingMap.put(DescribePriceTypeEnum.FLOATING_IP.getProp(), inquiryFloatingIpPrice.getFloatingIpNum());
            billingMap.put(DescribePriceTypeEnum.IP_BAND_WIDTH.getProp(),
                    inquiryFloatingIpPrice.getInternetMaxBandwidthOut());
        }

        return billingMap;
    }

    /**
     * 询价元数据映射
     *
     * @param jsonNode
     * @param cloudEnv
     * @return
     */
    private List<Map<String, Object>> specMapping(JsonNode jsonNode, CloudEnv cloudEnv) {

        List<Map<String, Object>> specMapping = Lists.newArrayList();
        Iterator<Map.Entry<String, JsonNode>> iterator = jsonNode.fields();

        while (iterator.hasNext()) {
            Map.Entry<String, JsonNode> currNode = iterator.next();
            String key = currNode.getKey();
            JsonNode chargeNode = currNode.getValue();
            List<JsonNode> billingNodes = chargeNode.findParents(CHARGE_ITEM_CATEGORY);
            int n = 0;
            for (JsonNode billingNode : billingNodes) {
                Map<String, Object> billingMap = JsonUtil.fromJson(billingNode.toString(), new TypeReference<Map<String, Object>>() {
                });
                ResVmType resVmType = null;
                if (billingNode.findValue("instanceType") != null) {
                    QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
                    queryResVmTypeByParamsRequest.setUuid(jsonNode.findValue("instanceType").asText());
                    List<ResVmType> resVmTypes = vmTypeService.selectByParams(queryResVmTypeByParamsRequest);
                    if (CollectionUtils.isNotEmpty(resVmTypes)) {
                        resVmType = resVmTypes.get(0);
                        billingMap.put(DescribePriceTypeEnum.CPU.getProp(), resVmType.getCpu());
                        // 内存单位MB转化为GB
                        billingMap
                                .put(DescribePriceTypeEnum.MEMORY.getProp(), Convert.toBigDecimal(resVmType.getRam().intValue() / GB_TO_MB_SIZE));

                        billingMap.put(DescribePriceTypeEnum.FAMILY.getProp(), resVmType.getFamily());
                        billingMap.put("category", StrUtil.isEmpty(resVmType.getFamily())
                                ? resVmType.getUuid() : resVmType.getFamily());
                        billingMap.put("uuid", resVmType.getUuid());
                    } else {
                        try {
                            resVmType = vmTypeService.selectByPrimaryKey(Convert.toLong(jsonNode.findValue("instanceType").asText()));
                            if (Objects.nonNull(resVmType)) {
                                billingMap.put(DescribePriceTypeEnum.CPU.getProp(), resVmType.getCpu());
                                // 内存单位MB转化为GB
                                billingMap.put(DescribePriceTypeEnum.MEMORY.getProp(),
                                        Convert.toBigDecimal(resVmType.getRam().intValue() / GB_TO_MB_SIZE));

                                billingMap.put(DescribePriceTypeEnum.INSTANCE_TYPE.getProp(), resVmType.getUuid());
                                billingMap.put(DescribePriceTypeEnum.FAMILY.getProp(), resVmType.getFamily());
                                billingMap.put("category", StrUtil.isEmpty(resVmType.getFamily())
                                        ? resVmType.getUuid() : resVmType.getFamily());
                                billingMap.put("uuid", resVmType.getUuid());
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e.getMessage());
                        }
                    }
                    if (billingMap.get("category") == null) {
                        billingMap.put("category", billingNode.findValue("instanceType").textValue());
                    }
                }
                if (billingMap.get("instanceType") == null && billingMap.get("internetMaxBandwidthOut") == null) {
                    ResVolumeType resVolumeType = volumeTypeService.selectByPrimaryKey(Convert.toLong(billingMap.get("category"), 0L));
                    if (Objects.nonNull(resVolumeType)) {
                        billingMap.put("category", resVolumeType.getUuid());
                        // SFS/HPC-DRP 类型= ShareType + 协议类型
                    } else if ((ProductCodeEnum.SFS.getProductType().equals(billingMap.get("productCode"))
                            || ProductCodeEnum.SFS2.getProductType().equals(billingMap.get("productCode"))
                            || ProductCodeEnum.HPC_DRP.getProductType().equals(billingMap.get("productCode")))
                            && Objects.nonNull(billingMap.get("shareType"))) {
                        billingMap.put("aloneCategory",  billingMap.get("category"));
                        billingMap.put("category",
                                       billingMap.get("shareType") + HYPHEN_STR + billingMap.get("category"));
                    } else {
                        billingMap.put("aloneCategory",  billingMap.get("category"));
                        billingMap.put("category", billingMap.get("category"));
                    }
                }
                if (billingMap.get("productCode") == null) {
                    billingMap.put("productCode", META_MAP.get(key));
                }
                billingMap.put("identification", key + HYPHEN_STR + Convert.toStr(billingMap.get("category")) + HYPHEN_STR);
                specMapping.add(billingMap);
                n++;
            }
        }

        return specMapping;
    }


    /**
     * 根据策略计算价格
     *
     * @param billingMap
     * @param strategyAccount
     * @param strategy
     * @param tariffSpecs
     * @param tariffSpecCharges
     * @return
     */
    private BizBillingPriceVO describeBillingStrategyPrice(
            Map<String, Object> billingMap,
            BizBillingStrategyAccount strategyAccount,
            Map<String, List<ResZone>> zoneMap,
            List<BizBillingSpec> originalSpecs,
            BizBillingStrategy strategy,
            List<BizBillingTariffSpec> tariffSpecs,
            List<BizBillingTariffSpecCharge> tariffSpecCharges,
            List<BizBillingStrategyAccountMatch> accountMatches) {
        BizBillingPriceVO priceVO = new BizBillingPriceVO();
        priceVO.setCategory(strategy.getCategory());

        if (CollectionUtils.isEmpty(tariffSpecs) || CollectionUtils.isEmpty(tariffSpecCharges)) {
            return priceVO;
        }

        // 获取计费规格ID列表
        String specIdStr = tariffSpecs.stream().map(BizBillingTariffSpec::getSpecIds).collect(Collectors.joining(","));
        String[] specIdArray = specIdStr.split(",");
        List<String> specIds = Arrays.asList(specIdArray);
        List<BizBillingSpec> specs = originalSpecs.stream()
                .filter(originalSpec -> specIds.contains(originalSpec.getId().toString()))
                .collect(Collectors.toList());

        // 对计费规格进行分组（方便根据ID获取）
        Map<Long, List<BizBillingSpec>> specMap = specs.stream().collect(Collectors.groupingBy(BizBillingSpec::getId));

        // 对需要计费的原始数据进行分组

        Multimap<String, String> billSpecMap = HashMultimap.create();
        billingMap.keySet().forEach(s -> {
            String[] keys = StringUtil.split(s, ".");
            billSpecMap.put(keys[0], s);
        });
        Set<String> billSpecSet = billSpecMap.keySet();

        // 使用规格对数据进行匹配
        for (BizBillingTariffSpec tariffSpec : tariffSpecs) {
            String tariffSpecSpecIds = tariffSpec.getSpecIds();
            String[] tariffSpecArray = tariffSpecSpecIds.split(",");
            // 获取计费项名称列表，计费元数据需要全部包含才计费
            Set<String> specItemSet = Sets.newHashSet();
            // 计费规格非计费项
            Set<String> specNotPriceItemSet = Sets.newHashSet();
            for (String tariffSpecStr : tariffSpecArray) {
                Long specId = Long.parseLong(tariffSpecStr);
                if (Objects.nonNull(specMap.get(specId))) {
                    BizBillingSpec spec = specMap.get(specId).get(0);
                    if (spec.getIsCharge()) {
                        specItemSet.add(spec.getSpecName());
                    } else {
                        specNotPriceItemSet.add(spec.getSpecName());
                    }
                }
            }
            // 检查计费信息里是否包含计费规格的所有计费项
            boolean specPricedItemMatched = billSpecSet.containsAll(specItemSet);
            if (!specPricedItemMatched) {
                continue;
            }

            // 获取规格对应的计费项
            List<BizBillingTariffSpecCharge> specCharges = tariffSpecCharges
                    .stream()
                    .filter(charge -> charge.getBillingTariffSpecId().equals(tariffSpec.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(specCharges)) {
                continue;
            }

            // 根据规格组过滤需要匹配的项
            List<BizBillingStrategyAccountMatch> matches = accountMatches.stream()
                    .filter(match -> match.getBillingStrategyId().equals(strategy.getId())
                            && match.getTariffSpecId().equals(tariffSpec.getId()))
                    .collect(Collectors.toList());

            // 匹配规格定价
            for (BizBillingTariffSpecCharge specCharge : specCharges) {
                String billingConfig = specCharge.getBillingConfig();
                // 解析规格配置信息 specChargeMap = {cpu: "1-100", group:"test"}
                Map<String, Object> specChargeMap = JsonUtil.fromJson(
                        billingConfig, new TypeReference<Map<String, Object>>() {
                        });
                if (!specChargeMap.keySet().containsAll(specItemSet)) {
                    continue;
                }

                // 匹配非计费项
                Map<String, Boolean> groupMatchedMap = Maps.newHashMap();
                boolean groupMatched = matchNoPriceSpecItem(groupMatchedMap, specNotPriceItemSet,
                        specChargeMap, matches, specMap, billSpecMap, billingMap, zoneMap);
                if (!groupMatched) {
                    continue;
                }

                // 检查计费原始数据是否与规格定价匹配
                Map<String, Boolean> matchedMap = Maps.newHashMap();
                boolean specMathed = matchPriceSpecItem(matchedMap, specItemSet, tariffSpec, specChargeMap, billSpecMap, billingMap);

                // 计费规格能与计费元数据匹配，计算对应价格
                if (specMathed) {
                    // 获取匹配到的计费项
                    calculateSpecPrice(priceVO, matchedMap, billingMap, specItemSet, tariffSpec, specCharge, groupMatchedMap);
                }
            }
        }

        return priceVO;
    }

    /**
     * 检查计费项是否匹配
     *
     * @param matchedMap
     * @param specItemSet
     * @param tariffSpec
     * @param specChargeMap
     * @param billSpecMap
     * @param billingMap
     * @return
     */
    private boolean matchPriceSpecItem(
            Map<String, Boolean> matchedMap, Set<String> specItemSet, BizBillingTariffSpec tariffSpec,
            Map<String, Object> specChargeMap, Multimap<String, String> billSpecMap, Map<String, Object> billingMap) {
        boolean specMathed = true;
        for (String specName : specItemSet) {
            if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                // 固定计费
                String specItemValue = specChargeMap.get(specName).toString();
                Collection<String> billSpecNames = billSpecMap.get(specName);
                if (CollectionUtils.isEmpty(billSpecNames)) {
                    specMathed = false;
                    continue;
                }

                boolean itemMatched = false;
                for (String billSpecName : billSpecNames) {
                    Object o = billingMap.get(billSpecName);
                    String billSpecValue = Objects.nonNull(o) ? o.toString() : null;
                    if (doubleStringCompare(billSpecValue, specItemValue)) {
                        matchedMap.put(billSpecName, Boolean.TRUE);
                        itemMatched = true;
                    } else {
                        matchedMap.put(billSpecName, Boolean.FALSE);
                    }
                }
                if (!itemMatched) {
                    specMathed = false;
                }
            } else if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                // 增量计费
                String specItemValueRange = specChargeMap.get(specName).toString();
                Collection<String> billSpecNames = billSpecMap.get(specName);
                if (CollectionUtils.isEmpty(billSpecNames)) {
                    specMathed = false;
                    continue;
                }

                String[] specRangeArray = specItemValueRange.split(HYPHEN_STR);
                Double min = Double.parseDouble(specRangeArray[0]);
                Double max = Double.parseDouble(specRangeArray[1]);

                boolean itemMatched = false;
                for (String billSpecName : billSpecNames) {
                    Object o = billingMap.get(billSpecName);
                    if (Objects.isNull(o)) {
                        continue;
                    }
                    String billSpecValueStr = o.toString();
                    Double billSpecValue = Double.parseDouble(billSpecValueStr);
                    if (billSpecValue >= min && billSpecValue <= max) {
                        matchedMap.put(billSpecName, Boolean.TRUE);
                        itemMatched = true;
                    } else {
                        matchedMap.put(billSpecName, Boolean.FALSE);
                    }
                }
                if (!itemMatched) {
                    specMathed = false;
                }
            }
        }

        return specMathed;
    }

    /**
     * 检查非计费项是否匹配
     *
     * @param groupMatchedMap
     * @param specNotPriceItemSet
     * @param specChargeMap
     * @param matches
     * @param specMap
     * @param billSpecMap
     * @param billingMap
     * @param zoneMap
     * @return
     */
    private boolean matchNoPriceSpecItem(
            Map<String, Boolean> groupMatchedMap, Set<String> specNotPriceItemSet, Map<String, Object> specChargeMap,
            List<BizBillingStrategyAccountMatch> matches, Map<Long, List<BizBillingSpec>> specMap,
            Multimap<String, String> billSpecMap, Map<String, Object> billingMap, Map<String, List<ResZone>> zoneMap) {
        if (CollectionUtils.isEmpty(specNotPriceItemSet)) {
            return true;
        }

        Object group = specChargeMap.get("group");
        List<BizBillingStrategyAccountMatch> currentMatchs = matches.stream()
                .filter(match -> match.getGroupName().equals(group))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentMatchs)) {
            return false;
        }

        boolean groupMatched = true;
        Map<Long, List<BizBillingStrategyAccountMatch>> matchMap = currentMatchs.stream()
                .collect(Collectors.groupingBy(BizBillingStrategyAccountMatch::getSpecId));

        Set<Map.Entry<Long, List<BizBillingStrategyAccountMatch>>> entries = matchMap.entrySet();
        for (Map.Entry<Long, List<BizBillingStrategyAccountMatch>> entry : entries) {
            List<BizBillingStrategyAccountMatch> matchList = entry.getValue();
            boolean itemMatched = false;
            for (BizBillingStrategyAccountMatch accountMatch : matchList) {
                if (Objects.isNull(specMap.get(accountMatch.getSpecId()))) {
                    continue;
                }
                BizBillingSpec spec = specMap.get(accountMatch.getSpecId()).get(0);
                Collection<String> specKeys = billSpecMap.get(spec.getSpecName());
                for (String specKey : specKeys) {
                    Object specValue = billingMap.get(specKey);
                    if (Objects.isNull(specValue)) {
                        if (!groupMatchedMap.containsKey(specKey)) {
                            groupMatchedMap.put(specKey, false);
                        }
                        continue;
                    }

                    if (accountMatch.getTargetId().equals(specValue.toString())) {
                        groupMatchedMap.put(specKey, true);
                        itemMatched = true;
                    } else {
                        if (zoneMap.containsKey(accountMatch.getTargetId())
                                && zoneMap.get(accountMatch.getTargetId()).get(0).getUuid().equals(specValue.toString())) {
                            groupMatchedMap.put(specKey, true);
                            itemMatched = true;
                        } else {
                            if (!groupMatchedMap.containsKey(specKey)) {
                                groupMatchedMap.put(specKey, false);
                            }
                        }
                    }
                }
            }
            if (!itemMatched) {
                groupMatched = false;
            }
        }

        return groupMatched;
    }

    /**
     * 计算规格项价格
     *
     * @param priceVO
     * @param matchedMap
     * @param billingMap
     * @param specItemSet
     * @param tariffSpec
     * @param specCharge
     * @param groupMatchedMap
     */
    private void calculateSpecPrice(BizBillingPriceVO priceVO, Map<String, Boolean> matchedMap, Map<String, Object> billingMap,
                                    Set<String> specItemSet, BizBillingTariffSpec tariffSpec,
                                    BizBillingTariffSpecCharge specCharge, Map<String, Boolean> groupMatchedMap) {
        // 获取匹配到的计费项
        Set<Map.Entry<String, Boolean>> entries = matchedMap.entrySet();
        Map<String, Set<String>> priceGroup = Maps.newHashMap();
        for (Map.Entry<String, Boolean> entry : entries) {
            if (entry.getValue()) {
                String key = entry.getKey();
                if (key.indexOf('.') > 0) {
                    String[] split = key.split("\\.");
                    if (!priceGroup.containsKey(split[1])) {
                        priceGroup.put(split[1], Sets.newHashSet());
                    }
                    priceGroup.get(split[1]).add(split[0]);
                } else {
                    if (!priceGroup.containsKey(SINGLE_SPEC_ITEM)) {
                        priceGroup.put(SINGLE_SPEC_ITEM, Sets.newHashSet());
                    }
                    priceGroup.get(SINGLE_SPEC_ITEM).add(key);
                }
            }
        }
        Set<Map.Entry<String, Set<String>>> groupEntrySet = priceGroup.entrySet();
        for (Map.Entry<String, Set<String>> groupEntry : groupEntrySet) {
            Set<String> priceItem = groupEntry.getValue();
            if (priceItem.containsAll(specItemSet)) {
                if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                    Optional<String> first = priceItem.stream().findFirst();
                    first.ifPresent(s ->
                            calculateStaticSpecPrice(priceVO, s, tariffSpec, specCharge, groupMatchedMap, groupEntry.getKey()));
                } else if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                    Optional<String> first = priceItem.stream().findFirst();
                    first.ifPresent(s ->
                            calculateIncSpecPrice(priceVO, s, tariffSpec, specCharge, groupMatchedMap, groupEntry.getKey(), billingMap));
                }
            }
        }
    }

    /**
     * 计算按量计费项价格
     *
     * @param priceVO
     * @param specName
     * @param tariffSpec
     * @param specCharge
     * @param groupMatchedMap
     * @param groupKey
     * @param billingMap
     */
    private void calculateIncSpecPrice(BizBillingPriceVO priceVO, String specName,
                                       BizBillingTariffSpec tariffSpec, BizBillingTariffSpecCharge specCharge,
                                       Map<String, Boolean> groupMatchedMap, String groupKey, Map<String, Object> billingMap) {
        String relatedName = GROUP_MAP.get(specName);
        if (StringUtils.isEmpty(relatedName)) {
            BigDecimal count = new BigDecimal(billingMap.get(getSpecItemKey(specName, groupKey)).toString());
            if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
                priceVO.setHourPrice(priceVO.getHourPrice().add(specCharge.getFixedHourPrice()).add(count.multiply(specCharge.getHourPrice())));
                priceVO.setMonthPrice(priceVO.getMonthPrice().add(specCharge.getFixedMonthPrice()).add(count.multiply(specCharge.getMonthPrice())));
            } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
                priceVO.setOncePrice(priceVO.getOncePrice().add(specCharge.getOncePrice()));
            }
            return;
        }
        String relatedKey = getSpecItemKey(relatedName, groupKey);
        Boolean groupMathed = groupMatchedMap.get(relatedKey);
        if (Objects.isNull(groupMathed) || groupMathed) {
            BigDecimal count = new BigDecimal(billingMap.get(getSpecItemKey(specName, groupKey)).toString());
            if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
                priceVO.setHourPrice(priceVO.getHourPrice().add(specCharge.getFixedHourPrice()).add(count.multiply(specCharge.getHourPrice())));
                priceVO.setMonthPrice(priceVO.getMonthPrice().add(specCharge.getFixedMonthPrice()).add(count.multiply(specCharge.getMonthPrice())));
            } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
                priceVO.setOncePrice(priceVO.getOncePrice().add(specCharge.getOncePrice() == null ? BigDecimal.ZERO : specCharge.getOncePrice()));
            }
        }
    }

    /**
     * 计算固定计费项价格
     *
     * @param priceVO
     * @param specName
     * @param tariffSpec
     * @param specCharge
     * @param groupMatchedMap
     * @param groupKey
     */
    private void calculateStaticSpecPrice(BizBillingPriceVO priceVO, String specName,
                                          BizBillingTariffSpec tariffSpec, BizBillingTariffSpecCharge specCharge,
                                          Map<String, Boolean> groupMatchedMap, String groupKey) {
        String relatedName = GROUP_MAP.get(specName);
        if (StringUtils.isEmpty(relatedName)) {
            if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
                priceVO.setHourPrice(priceVO.getHourPrice().add(specCharge.getHourPrice()));
                priceVO.setMonthPrice(priceVO.getMonthPrice().add(specCharge.getMonthPrice()));
            } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
                priceVO.setOncePrice(priceVO.getOncePrice().add(specCharge.getOncePrice()));
            }
            return;
        }
        String relatedKey = getSpecItemKey(relatedName, groupKey);
        Boolean groupMathed = groupMatchedMap.get(relatedKey);
        if (Objects.isNull(groupMathed) || groupMathed) {
            if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
                priceVO.setHourPrice(priceVO.getHourPrice().add(specCharge.getHourPrice()));
                priceVO.setMonthPrice(priceVO.getMonthPrice().add(specCharge.getMonthPrice()));
            } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
                priceVO.setOncePrice(priceVO.getOncePrice().add(specCharge.getOncePrice() == null ? BigDecimal.ZERO : specCharge.getOncePrice()));
            }
        }
    }

    private boolean doubleStringCompare(String a, String b) {
        if (StringUtils.isEmpty(a) || StringUtils.isEmpty(b)) {
            return false;
        }
        double aValue = Double.parseDouble(a);
        double bValue = Double.parseDouble(b);
        if (Double.doubleToLongBits(aValue) == Double.doubleToLongBits(bValue)) {
            return true;
        }
        return false;
    }

    private String getSpecItemKey(String specName, String key) {
        if (SINGLE_SPEC_ITEM.equals(key)) {
            return specName;
        } else {
            return specName + "." + key;
        }
    }

    /**
     * 计算服务价格
     *
     * @param serviceId
     * @return
     */
    @Override
    public List<BizBillingPriceVO> calculateServicePrice(Long serviceId, BigDecimal period) {
        List<BizBillingPriceVO> priceVOs = Lists.newArrayList();
        ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
        if (Objects.isNull(serviceCategory)) {
            return priceVOs;
        }

        // 获取服务价格配置信息
        QueryWrapper<BizBillingStrategyServing> servingQueryWrapper = new QueryWrapper<>();
        servingQueryWrapper.eq("service_id", serviceId);
        List<BizBillingStrategyServing> strategyServings = servingService.list(servingQueryWrapper);
        if (CollectionUtils.isEmpty(strategyServings)) {
            return priceVOs;
        }
        BizBillingStrategyServing strategyServing = strategyServings.get(0);
        if (Objects.nonNull(strategyServing.getExtraCharge()) && strategyServing.getExtraCharge()) {
            BizBillingPriceVO priceVO = new BizBillingPriceVO();
            priceVO.setCategory(PriceType.EXTRA_CONFIG);
            priceVO.setPriceType(PriceType.EXTRA_CONFIG);
            priceVO.setExtraType(strategyServing.getExtraChargeStrategy());
            priceVO.setResourceType(PriceType.EXTRA_CONFIG);
            priceVO.setBillingSpec(serviceCategory.getProductName());
            priceVO.setIdentification(StrUtil.concat(true, Convert.toStr(serviceCategory.getId()), HYPHEN_STR,
                    serviceCategory.getShowType()));
            // 计算服务额外计费项
            if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(strategyServing.getExtraChargeStrategy())) {
                if (Objects.nonNull(strategyServing.getExtraOncePrice())) {
                    priceVO.setOncePrice(priceVO.getOncePrice().add(strategyServing.getExtraOncePrice()));
                }

            } else {
                if (Objects.nonNull(strategyServing.getExtraHourPrice())) {
                    priceVO.setHourPrice(priceVO.getHourPrice().add(strategyServing.getExtraHourPrice()));
                }
                if (Objects.nonNull(strategyServing.getExtraMonthPrice())) {
                    priceVO.setMonthPrice(priceVO.getMonthPrice().add(strategyServing.getExtraMonthPrice()));
                }
            }
            priceVOs.add(priceVO);
        }

        // 获取服务计费策略
        BizBillingStrategy strategy = strategyService.getById(strategyServing.getBillingStrategyId());
        if (Objects.isNull(strategy)) {
            return priceVOs;
        }

        // 获取规格定价
        if (StrUtil.isNotEmpty(strategyServing.getSpecChargeIds())) {
            List<Long> specChargeIdList = Arrays.stream(StrUtil.splitToArray(strategyServing.getSpecChargeIds(), StrUtil.COMMA))
                    .map(Long::parseLong).collect(Collectors.toList());

            QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
            chargeQueryWrapper.lambda().in(BizBillingTariffSpecCharge::getId, specChargeIdList);
            List<BizBillingTariffSpecCharge> charges = chargeService.list(chargeQueryWrapper);
            if (CollectionUtils.isEmpty(charges)) {
                return priceVOs;
            }
            charges.forEach(specCharge -> {
                BizBillingPriceVO priceVO = new BizBillingPriceVO();
                priceVO.setCategory(strategy.getCategory());
                priceVO.setPriceType(PriceType.SERVICE);
                priceVO.setResourceType(PriceType.SERVICE);
                JSONObject billingConfig = JSON.parseObject(specCharge.getBillingConfig());
                priceVO.setPriceDesc(billingConfig.getString("cloudService"));
                priceVO.setBillingSpec(billingConfig.getString("cloudService"));
                priceVO.setIdentification(billingConfig.getString("cloudService"));
                if (Objects.nonNull(specCharge.getHourPrice())) {
                    priceVO.setHourPrice(priceVO.getHourPrice().add(specCharge.getHourPrice()));
                }
                if (Objects.nonNull(specCharge.getMonthPrice())) {
                    priceVO.setMonthPrice(priceVO.getMonthPrice().add(specCharge.getMonthPrice()));
                }

                QueryWrapper<BizBillingChargeMonth> chargeMonthQuery = new QueryWrapper<>();
                chargeMonthQuery.lambda().eq(BizBillingChargeMonth::getSpecChargeId, specCharge.getId());
                List<BizBillingChargeMonth> chargeMonths = bizBillingChargeMonthService.list(chargeMonthQuery);
                if (CollectionUtil.isNotEmpty(chargeMonths)) {
                    chargeMonths.forEach(chargeMonth -> {
                        if (Objects.equals(chargeMonth.getDuration(), period.intValue())) {
                            priceVO.setAppoint(Boolean.TRUE);
                            priceVO.setMonthPrice(chargeMonth.getPrice());
                        }
                    });
                }
                priceVOs.add(priceVO);
            });
        }
        return priceVOs;
    }

    @Override
    public ServicePriceDetailVO getServicePriceDetail(Long serviceId) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296864863));
        }

        ServicePriceDetailVO response = new ServicePriceDetailVO();

        QueryWrapper<BizBillingStrategyOrg> strategyOrgQueryWrapper = new QueryWrapper<>();
        List<BizBillingStrategyOrg> strategyOrgs = strategyOrgService.list(strategyOrgQueryWrapper);
        if (CollectionUtils.isEmpty(strategyOrgs)) {
            response.setServicePriced(false);
        } else {
            BizBillingStrategyOrg strategyOrg = strategyOrgs.get(0);
            QueryWrapper<BizBillingStrategyServing> servingQueryWrapper = new QueryWrapper<>();
            servingQueryWrapper.eq("strategy_org_id", strategyOrg.getId());
            servingQueryWrapper.eq("billing_strategy_id", strategyOrg.getBillingStrategyId());
            servingQueryWrapper.eq("service_id", serviceId);
            List<BizBillingStrategyServing> strategyServings = servingService.list(servingQueryWrapper);
            if (CollectionUtils.isEmpty(strategyServings)) {
                response.setServicePriced(false);
            } else {
                response.setServicePriced(true);
                BizBillingStrategyServing strategyServing = strategyServings.get(0);
                BizBillingStrategyServingVO strategyServingVO = BeanConvertUtil.convert(
                        strategyServing, BizBillingStrategyServingVO.class);
                response.setStrategyServing(strategyServingVO);

                String[] specChargeIdArray = strategyServing.getSpecChargeIds().split(",");
                List<String> specChargeIds = Arrays.asList(specChargeIdArray);
                if (CollectionUtils.isNotEmpty(specChargeIds)) {
                    QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
                    chargeQueryWrapper.in("id", specChargeIds);
                    List<BizBillingTariffSpecCharge> charges = chargeService.list(chargeQueryWrapper);
                    if (CollectionUtils.isNotEmpty(charges)) {
                        BizBillingTariffSpec tariffSpec = tariffSpecService.getById(charges.get(0).getBillingTariffSpecId());
                        String priceWay = BILLINGCHARGE_MAP.get(tariffSpec.getChargeStrategy())
                                + HYPHEN_STR + CHARGE_CYCLE_MAP.get(tariffSpec.getChargeCycle());

                        for (BizBillingTariffSpecCharge specCharge : charges) {
                            generateServiceSpecChargeDetail(response, priceWay, specCharge, tariffSpec);
                        }
                    }
                }
            }
        }

        Long serverTemplateId = getServerTemplateId(serviceCategory);
        if (Objects.nonNull(serverTemplateId)) {
            ServerTemplate serverTemplate = serverTemplateMapper.getServerTemplateById(serverTemplateId, false);
            if (Objects.isNull(serverTemplate)) {
                return response;
            }

            String envConfig = serverTemplate.getEnvConfig();
            JSONArray envConfigArray = JSON.parseArray(envConfig);

            int pricedCount = 0;

            for (Object o : envConfigArray) {
                JSONObject config = (JSONObject) o;
                Long cloudEnvId = config.getLong("cloudEnvId");
                if (Objects.isNull(cloudEnvId)) {
                    continue;
                }

                CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);
                if (Objects.isNull(cloudEnv)) {
                    continue;
                }

                ServiceAccountPriceDetailVO accountPriceDetail = getAccountPriceDetail(cloudEnv.getCloudEnvAccountId());
                if (Objects.nonNull(accountPriceDetail)) {
                    if (accountPriceDetail.getPriced()) {
                        pricedCount++;
                    }

                    response.getAccountDetails().add(accountPriceDetail);
                }
            }

            if (envConfigArray.size() == pricedCount) {
                response.setAccountPricedStatus("all");
            } else if (pricedCount > 0 && pricedCount < envConfigArray.size()) {
                response.setAccountPricedStatus("part");
            } else {
                response.setAccountPricedStatus("no");
            }
        }

        return response;
    }

    private void generateServiceSpecChargeDetail(ServicePriceDetailVO response, String priceWay,
                                                 BizBillingTariffSpecCharge specCharge, BizBillingTariffSpec tariffSpec) {
        ComponentPriceDetailVO componentPriceDetailVO = new ComponentPriceDetailVO();
        componentPriceDetailVO.setPriceWay(priceWay);
        JSONObject billingConfig = JSON.parseObject(specCharge.getBillingConfig());
        componentPriceDetailVO.setComponentName(billingConfig.getString("serviceComponent"));

        if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
            if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                componentPriceDetailVO.getPriceDetails().add(String.format(
                        "按量计费：%s+(N*%s)元/小时", keepFourDecimals(specCharge.getFixedHourPrice()),
                        keepFourDecimals(specCharge.getHourPrice())));
                componentPriceDetailVO.getPriceDetails().add(String.format(
                        "包年包月：%s+(N*%s)元/月", keepFourDecimals(specCharge.getFixedMonthPrice()),
                        keepFourDecimals(specCharge.getMonthPrice())));
            } else if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                componentPriceDetailVO.getPriceDetails().add(String.format("按量计费：%s元/小时",
                        keepFourDecimals(specCharge.getHourPrice())));
                componentPriceDetailVO.getPriceDetails().add(String.format("包年包月：%s元/月",
                        keepFourDecimals(specCharge.getMonthPrice())));
            }
        } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
            if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                componentPriceDetailVO.getPriceDetails().add(String.format(
                        "按量计费：%s+(N*%s)元", keepFourDecimals(specCharge.getFixedOncePrice()),
                        keepFourDecimals(specCharge.getOncePrice())));
            } else if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                componentPriceDetailVO.getPriceDetails().add(String.format("固定计费：%s元",
                        keepFourDecimals(specCharge.getOncePrice())));
            }
        }

        response.getComponentPriceDetails().add(componentPriceDetailVO);
    }

    private void generateServiceSpecChargeDetailNew(ServicePriceDetailNewVO response, BizBillingTariffSpecCharge specCharge) {
        ComponentPriceDetailVO componentPriceDetailVO = new ComponentPriceDetailVO();
        JSONObject billingConfig = JSON.parseObject(specCharge.getBillingConfig());
        componentPriceDetailVO.setComponentName(billingConfig.getString("cloudService"));
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        String detail = isUs ? "Pay by volume" : "按量计费";
        componentPriceDetailVO.getPriceDetails().add(String.format(detail + "：￥%s/小时;",
                keepThreeDecimals(specCharge.getHourPrice())));
        // 是否存在指定月数的计费
        QueryWrapper<BizBillingChargeMonth> chargeMonthQuery = new QueryWrapper<>();
        chargeMonthQuery.lambda().eq(BizBillingChargeMonth::getSpecChargeId, specCharge.getId());
        List<BizBillingChargeMonth> chargeMonths = bizBillingChargeMonthService.list(chargeMonthQuery);
        StringBuilder sb = new StringBuilder();
        if (CollectionUtil.isNotEmpty(chargeMonths)) {
            // 设置指定月数的计费配置
            chargeMonths.forEach(chargeMonth -> {
                sb.append(String.format("[%s个月：￥%s];",
                        chargeMonth.getDuration(), keepThreeDecimals(chargeMonth.getPrice())));
            });
        }
        detail = isUs ? "Monthly and yearly packages" : "包年包月";
        componentPriceDetailVO.getPriceDetails().add(String.format(detail + "：￥%s/月;%s",
                keepThreeDecimals(specCharge.getMonthPrice()), sb.toString()));

        response.getComponentPriceDetails().add(componentPriceDetailVO);
    }

    @Override
    public ServiceAccountPriceDetailVO getAccountPriceDetail(Long accountId) {
        CloudEnvAccount account = cloudEnvAccountService.selectByPrimaryKey(accountId);
        if (Objects.isNull(account)) {
            return null;
        }

        ServiceAccountPriceDetailVO accountPrice = new ServiceAccountPriceDetailVO();
        accountPrice.setPriced(Boolean.FALSE);
        accountPrice.setAccountName(account.getEnvAccountName());


        accountPrice.setPriced(Boolean.TRUE);
        QueryWrapper<BizBillingStrategyAccount> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.eq("account_id", account.getId());
        List<BizBillingStrategyAccount> strategyAccounts = strategyAccountService.list(accountQueryWrapper);
        if (CollectionUtils.isEmpty(strategyAccounts)) {
            return accountPrice;
        }

        List<BizBillingSpec> specs = specService.list();
        Map<String, List<BizBillingSpec>> specIdMap = specs.stream().collect(Collectors.groupingBy(spec -> spec.getId().toString()));

        for (BizBillingStrategyAccount strategyAccount : strategyAccounts) {
            AccountPriceCategoryDetailVO priceCategory = new AccountPriceCategoryDetailVO();
            accountPrice.getCategorys().add(priceCategory);
            priceCategory.setCategoryName(this.categoryNameToUs(CATEGROY_MAP.get(strategyAccount.getCategory())));

            QueryWrapper<BizBillingStrategyAccountMatch> matchQueryWrapper = new QueryWrapper<>();
            matchQueryWrapper.eq("strategy_account_id", strategyAccount.getId());
            List<BizBillingStrategyAccountMatch> accountMatches = accountMatchService.list(matchQueryWrapper);

            Long billingStrategyId = strategyAccount.getBillingStrategyId();
            BizBillingStrategy strategy = strategyService.getById(billingStrategyId);
            QueryWrapper<BizBillingTariffSpec> tariffSpecQueryWrapper = new QueryWrapper<>();
            tariffSpecQueryWrapper.eq("billing_strategy_id", strategy.getId());
            List<BizBillingTariffSpec> tariffSpecs = tariffSpecService.list(tariffSpecQueryWrapper);

            for (BizBillingTariffSpec tariffSpec : tariffSpecs) {
                AccountPriceWayDetailVO priceWay = new AccountPriceWayDetailVO();
                priceCategory.getWays().add(priceWay);
                priceWay.setWayName(BILLINGCHARGE_MAP.get(tariffSpec.getChargeStrategy())
                        + HYPHEN_STR + CHARGE_CYCLE_MAP.get(tariffSpec.getChargeCycle()));

                QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
                chargeQueryWrapper.eq("billing_tariff_spec_id", tariffSpec.getId());
                List<BizBillingTariffSpecCharge> charges = chargeService.list(chargeQueryWrapper);

                Map<String, List<BizBillingTariffSpecCharge>> chargeGoup = charges
                        .stream().collect(Collectors.groupingBy(charge -> {
                            JSONObject jsonObject = JSON.parseObject(charge.getBillingConfig());
                            String group = jsonObject.getString("group");
                            if (StringUtils.isEmpty(group)) {
                                return "默认";
                            }
                            return group;
                        }));

                Set<Map.Entry<String, List<BizBillingTariffSpecCharge>>> entries = chargeGoup.entrySet();
                for (Map.Entry<String, List<BizBillingTariffSpecCharge>> entry : entries) {
                    if (CollectionUtils.isEmpty(accountMatches)) {
                        AccountPriceGroupDetailVO priceGroup = new AccountPriceGroupDetailVO();
                        priceWay.getGroups().add(priceGroup);
                        priceGroup.setGroupName(entry.getKey());

                        List<BizBillingTariffSpecCharge> groupedCharges = entry.getValue();

                        for (BizBillingTariffSpecCharge specCharge : groupedCharges) {
                            generateAccountSpecChargeDetail(priceGroup, specIdMap, specCharge, tariffSpec);
                        }
                    } else {
                        for (BizBillingStrategyAccountMatch accountMatch : accountMatches) {
                            if (entry.getKey().equals(accountMatch.getGroupName())) {
                                AccountPriceGroupDetailVO priceGroup = new AccountPriceGroupDetailVO();
                                priceWay.getGroups().add(priceGroup);
                                if (!StringUtils.isEmpty(accountMatch.getSpecName())) {
                                    priceGroup.setGroupName(entry.getKey() + "[" + accountMatch.getSpecName() + "]");
                                } else {
                                    priceGroup.setGroupName(entry.getKey());
                                }

                                List<BizBillingTariffSpecCharge> groupedCharges = entry.getValue();

                                for (BizBillingTariffSpecCharge specCharge : groupedCharges) {
                                    generateAccountSpecChargeDetail(priceGroup, specIdMap, specCharge, tariffSpec);
                                }
                            }
                        }
                    }
                }
            }
        }

        return accountPrice;
    }

    @Override
    public ServiceAccountPriceDetailVO getAccountPriceDetailNew(Long accountId, String resourceType, String region, String userSid) {
        CloudEnvAccount account = cloudEnvAccountService.selectByPrimaryKey(accountId);
        if (Objects.isNull(account)) {
            return null;
        }

        ServiceAccountPriceDetailVO accountPrice = new ServiceAccountPriceDetailVO();
        accountPrice.setPriced(Boolean.FALSE);
        accountPrice.setAccountName(account.getEnvAccountName());

        QueryWrapper<BizBillingStrategyAccountConfig> accountConfigQueryWrapper = new QueryWrapper<>();
        accountConfigQueryWrapper.eq("account_id", account.getId());
        List<BizBillingStrategyAccountConfig> accountConfigs = strategyAccountConfigService.list(accountConfigQueryWrapper);
        if (CollectionUtils.isEmpty(accountConfigs)) {
            return accountPrice;
        }

        BizBillingStrategyAccountConfig strategyAccountConfig = accountConfigs.get(0);
        if (BillingStrategyType.CHARGED_BY_CUSTOM.equals(strategyAccountConfig.getBillingStrategy())) {
            // 获取云账号区域资源类型配置
            List<BizBillingRegionResource> regionResources = this.getBizBillingRegionResourceList(region, resourceType, accountId, userSid);

                if (CollectionUtil.isEmpty(regionResources)) {
                    return accountPrice;
                }
            BizBillingRegionResource regionResource = regionResources.get(0);
            // 获取区域资源类型计费配置
            List<BizBillingRegionCharge> regionCharges = getBizBillingRegionChargeList(regionResource.getId(), userSid);
            if (CollectionUtil.isEmpty(regionCharges)) {
                return accountPrice;
            }
            for (BizBillingRegionCharge regionCharge : regionCharges) {
                AccountPriceCategoryDetailVO priceCategory = new AccountPriceCategoryDetailVO();
                accountPrice.getCategorys().add(priceCategory);
                priceCategory.setCategoryName(this.categoryNameToUs(CATEGROY_MAP.get(regionCharge.getCategory())));
                String specConfig = regionCharge.getSpecConfig();
                if (StrUtil.isEmpty(specConfig)) {
                    continue;
                }
                // 获取类型族和 规格族配置
                List<SpecConfigVO> specConfigs = JSONArray.parseArray(specConfig, SpecConfigVO.class);
                if (CollectionUtil.isEmpty(specConfigs)) {
                    continue;
                }
                List<Long> specGroupIds = specConfigs.stream().map(SpecConfigVO::getSpecGroupId).collect(Collectors.toList());

                Collection<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.listByIds(specGroupIds);
                Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                        .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
                if (CollectionUtil.isEmpty(specGroups)) {
                    continue;
                }
                Set<String> specIdSet = specGroups.stream().map(BizBillingSpecGroup::getSpecId).collect(Collectors.toSet());
                List<String> specIds = Lists.newArrayList();
                specIdSet.forEach(str -> {
                    specIds.addAll(Arrays.asList(StrUtil.splitToArray(str, StrUtil.COMMA)));
                });
                Collection<BizBillingSpec> specs = specService.listByIds(specIds);
                if (CollectionUtil.isEmpty(specs)) {
                    continue;
                }
                Map<String, BizBillingSpec> specMap = specs.stream()
                        .collect(Collectors.toMap(BizBillingSpec::getSpecName, o -> o, (key1, key2) -> key1));
                for (SpecConfigVO specConfigVO : specConfigs) {
                    AccountPriceGroupDetailVO groupDetailVO = new AccountPriceGroupDetailVO();
                    priceCategory.getGroups().add(groupDetailVO);
                    //类型族名称
                    groupDetailVO.setGroupName(StrUtil.isEmpty(specConfigVO.getTypeName()) ?
                            specConfigVO.getType() : specConfigVO.getTypeName());
                    Long specGroupId = specConfigVO.getSpecGroupId();

                    QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
                    specChargeQuery.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
                    List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
                    if (CollectionUtil.isEmpty(specCharges)) {
                        continue;
                    }

                    accountPrice.setPriced(Boolean.TRUE);
                    for (BizBillingTariffSpecCharge specCharge : specCharges) {
                        AccountPriceSpecDetailVO specDetailVO = new AccountPriceSpecDetailVO();
                        groupDetailVO.getSpecs().add(specDetailVO);
                        specDetailVO.setSpecName(
                                specService.packageSpecName(specGroupMap.get(specGroupId), specCharge, specDetailVO, specMap));
                    }
                }
            }
        }

        return accountPrice;
    }

    private String categoryNameToUs(String categoryName) {
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        if (isUs && org.apache.commons.lang3.StringUtils.isNotBlank(categoryName)) {
            categoryName = categoryName.replaceAll("计算", "compute")
                    .replaceAll("存储", "storage")
                    .replaceAll("网络", "network")
                    .replaceAll("EI企业智能", "EI Enterprise Intelligence");
        }

        return categoryName;
    }

    private List<BizBillingRegionResource>  getBizBillingRegionResourceList(String region, String resourceType, Long accountId, String userId) {
        List<BizBillingRegionResource> list = null;
        if (org.apache.commons.lang3.StringUtils.isBlank(userId)) {
            QueryWrapper<BizBillingRegionResource> resourceQueryWrapper = new QueryWrapper<>();
            resourceQueryWrapper.lambda().eq(BizBillingRegionResource::getResourceType, resourceType)
                    .eq(BizBillingRegionResource::getEntityId, RequestContextUtil.getEntityId())
                    .eq(BizBillingRegionResource::getRegion, region).eq(BizBillingRegionResource::getEnvAccountId, accountId);

            list = bizBillingRegionResourceService.list(resourceQueryWrapper);

            if (CollectionUtil.isEmpty(list)) {
                // 查询所有区域的资源类型配置
                QueryWrapper<BizBillingRegionResource> allQuery = new QueryWrapper<>();
                allQuery.lambda().eq(BizBillingRegionResource::getResourceType, resourceType)
                        .eq(BizBillingRegionResource::getRegion, ALL_REGION)
                        .eq(BizBillingRegionResource::getEntityId, RequestContextUtil.getEntityId())
                        .eq(BizBillingRegionResource::getEnvAccountId, accountId);
                list = bizBillingRegionResourceService.list(allQuery);
            }

        } else {
            QueryWrapper<BizBillingCustomRegionResource> resourceQueryWrapper = new QueryWrapper<>();
            resourceQueryWrapper.lambda().eq(BizBillingCustomRegionResource::getResourceType, resourceType)
                    .eq(BizBillingCustomRegionResource::getEntityId, RequestContextUtil.getEntityId()).eq(BizBillingCustomRegionResource::getOwnerId, userId)
                    .eq(BizBillingCustomRegionResource::getRegion, region).eq(BizBillingCustomRegionResource::getEnvAccountId, accountId);
            List<BizBillingCustomRegionResource> customRegionResources = bizBillingCustomRegionResourceMapper.selectList(resourceQueryWrapper);

            if (CollectionUtil.isEmpty(customRegionResources)) {
                // 查询所有区域的资源类型配置
                QueryWrapper<BizBillingCustomRegionResource> allQuery = new QueryWrapper<>();
                allQuery.lambda().eq(BizBillingCustomRegionResource::getResourceType, resourceType)
                        .eq(BizBillingCustomRegionResource::getRegion, ALL_REGION)
                        .eq(BizBillingCustomRegionResource::getEntityId, RequestContextUtil.getEntityId())
                        .eq(BizBillingCustomRegionResource::getOwnerId, userId)
                        .eq(BizBillingCustomRegionResource::getEnvAccountId, accountId);
                customRegionResources = bizBillingCustomRegionResourceMapper.selectList(allQuery);
            }

            list = BeanConvertUtil.convert(customRegionResources, BizBillingRegionResource.class);
        }

        return list;
    }

    private List<BizBillingRegionCharge> getBizBillingRegionChargeList(Long resourceId, String userId) {
        List<BizBillingRegionCharge> regionCharges = null;
        if (org.apache.commons.lang3.StringUtils.isBlank(userId)) {
            QueryWrapper<BizBillingRegionCharge> regionChargeQuery = new QueryWrapper<>();
            regionChargeQuery.lambda().eq(BizBillingRegionCharge::getRegionResourceId, resourceId);
            regionCharges = bizBillingRegionChargeService.list(regionChargeQuery);
        } else {
            QueryWrapper<BizBillingCustomRegionCharge> regionChargeQuery = new QueryWrapper<>();
            regionChargeQuery.lambda().eq(BizBillingCustomRegionCharge::getRegionResourceId, resourceId);
            List<BizBillingCustomRegionCharge> list = bizBillingCustomRegionChargeMapper.selectList(regionChargeQuery);
            regionCharges = BeanConvertUtil.convert(list, BizBillingRegionCharge.class);
        }

        return regionCharges;
    }
    @Override
    public ServicePriceDetailNewVO overviewServicePrice(Long serviceId, List<Long> chargeIds, String resourceType) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296864863));
        }

        ServicePriceDetailNewVO response = new ServicePriceDetailNewVO();

        // 获取产品配置
        response.setResourceType(resourceType);
        if (CollectionUtils.isNotEmpty(chargeIds)) {
            QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
            chargeQueryWrapper.in("id", chargeIds);
            List<BizBillingTariffSpecCharge> charges = chargeService.list(chargeQueryWrapper);
            if (CollectionUtils.isNotEmpty(charges)) {
                for (BizBillingTariffSpecCharge specCharge : charges) {
                    generateServiceSpecChargeDetailNew(response, specCharge);
                }
            }
        }
        if (CHARGE_NONE.equals(resourceType)) {
            response.setResourceCharged(false);
            return response;
        }
        //是否无资源计费
        response.setResourceCharged(true);

        return getResouecePriceConfig(response, resourceType, null);
    }

    @Override
    public UnsubInquiryPriceVO unsubInquiryPrice(String id, String type) {
        UnsubInquiryPriceVO response = new UnsubInquiryPriceVO();
        boolean innerProduct =
                ProductCodeEnum.isInnerProduct(type) || ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(type)
                        || ProductCodeEnum.federationProducts().contains(type)
                        || ProductCodeEnum.isCmpApiProduct().contains(type);


        if (!innerProduct) {
        List<ResourceDetailVO> resourceDetailVOS = resourceConfigService.generateResourceDtail(id);

        AtomicReference<Boolean> inquiryTag = new AtomicReference<>(false);


        if (CollectionUtil.isNotEmpty(resourceDetailVOS)) {
            resourceDetailVOS.forEach(r -> {
                r.setServices(Lists.newArrayList());
                r.setOrderType(OrderType.RELEASE);
                iServiceOrderDetailService.completeResourceDetail(r);
                ServicePrice serviceAmount = r.getServiceAmount();
                ServicePrice extraConfigAmount = r.getExtraConfigAmount();
                // 总实付金额 = 资源 + 服务 + 额外
                response.setTotalPayment(response.getTotalPayment().add(
                        r.getTotalPayment().add(serviceAmount.getTotalPayment())
                                .add(extraConfigAmount.getTotalPayment())));
                response.setTotalOriginalCost(
                        NumberUtil.add(r.getTotalOriginalCost(), serviceAmount.getTotalOriginalCost(),
                                       extraConfigAmount.getTotalOriginalCost()));
                response.setTotalOrgDiscount(
                        NumberUtil.add(r.getTotalOrgDiscount(), serviceAmount.getTotalOrgDiscount(),
                                       extraConfigAmount.getTotalOrgDiscount()));
                response.setTotalCouponDiscount(
                        NumberUtil.add(r.getTotalCouponDiscount(), serviceAmount.getTotalCouponDiscount(),
                                       extraConfigAmount.getTotalCouponDiscount()));
                response.setTotalCouponAmount(
                        NumberUtil.add(r.getTotalCouponAmount(), serviceAmount.getTotalCouponAmount(),
                                       extraConfigAmount.getTotalCouponAmount()));
                response.setTotalCashAmount(NumberUtil.add(r.getTotalCashAmount(), serviceAmount.getTotalCashAmount(),
                                                           extraConfigAmount.getTotalCashAmount()));
                response.setTotalCreditAmount(
                        NumberUtil.add(r.getTotalCreditAmount(), serviceAmount.getTotalCreditAmount(),
                                       extraConfigAmount.getTotalCreditAmount()));
                response.setUsedAmount(response.getUsedAmount()
                        .add(r.getTotalUsedAmount())
                        .add(serviceAmount.getTotalUsed())
                        .add(extraConfigAmount.getTotalUsed()));
                response.setTotalUsedCouponAmount(
                        NumberUtil.add(r.getTotalUsedCouponAmount(), serviceAmount.getTotalUsedCouponAmount(),
                                       extraConfigAmount.getTotalUsedCouponAmount()));
                response.setTotalUsedCashAmount(
                        NumberUtil.add(r.getTotalUsedCashAmount(), serviceAmount.getTotalUsedCashAmount(),
                                       extraConfigAmount.getTotalUsedCashAmount()));
                response.setTotalUsedCreditAmount(
                        NumberUtil.add(r.getTotalUsedCreditAmount(), serviceAmount.getTotalUsedCreditAmount(),
                                       extraConfigAmount.getTotalUsedCreditAmount()));
                if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(r.getProductCode())) {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setId(id);
                    productInfo.setProductCode(r.getProductCode());
                    resRenewRefService.completeProductInfo(productInfo);
                    boolean hasNow = Objects.nonNull(productInfo.getNow());

                    Criteria criteria = new Criteria();
                    criteria.put("refInstanceIdLike", "\"" + productInfo.getId() + "\"");
                    criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
                    criteria.put("orderStatus", "completed");
                    criteria.put("productCode", productInfo.getProductCode());
                    List<ServiceOrderPriceDetail> serviceOrderPriceDetails = iServiceOrderPriceDetailService.selectByCriteria(
                            criteria);
                    //获取所有因负数补差金额产生的不退款金额
                    if (CollectionUtils.isNotEmpty(serviceOrderPriceDetails)){
                        for (ServiceOrderPriceDetail serviceOrderPriceDetail:serviceOrderPriceDetails) {
                            if (Objects.nonNull(serviceOrderPriceDetail.getNegativeAmount())
                                    && BigDecimal.ZERO.compareTo(serviceOrderPriceDetail.getNegativeAmount()) != 0){
                                //数据库记录为负数，添加用减法
                                response.setUsedAmount(response.getUsedAmount().subtract(serviceOrderPriceDetail.getNegativeAmount()));
                                r.setTotalUsedAmount(r.getTotalUsedAmount().subtract(serviceOrderPriceDetail.getNegativeAmount()));
                            }
                        }
                    }


                    r.setUnsubAmount(NumberUtil.add(r.getUnsubCashAmount(), serviceAmount.getUnsubCashAmount(),
                                                    extraConfigAmount.getUnsubCashAmount()));
                    if (hasNow) {
                        response.setNow(productInfo.getNow());
                        r.setNow(productInfo.getNow());

                        // if (!"frozen".equalsIgnoreCase(productInfo.getStatus())) {
//                            Criteria criteria = new Criteria();
//                            criteria.put("refInstanceIdLike", "\"" + productInfo.getId() + "\"");
//                            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
//                            criteria.put("orderStatus", "completed");
//                            criteria.put("productCode", productInfo.getProductCode());
                        criteria.put("isValid", true);
                        serviceOrderPriceDetails = iServiceOrderPriceDetailService.selectByCriteria(
                                criteria);
                            Set<Long> orderDetailIds = serviceOrderPriceDetails.stream()
                                    .map(ServiceOrderPriceDetail::getOrderDetailId)
                                    .collect(Collectors.toCollection(LinkedHashSet::new));
                            Long maxOrderDetailId = orderDetailIds.stream().max(Long::compare).orElse(-1L);
                            List<ServiceOrderPriceDetail> lastOrderPriceDetails = serviceOrderPriceDetails.stream()
                                                                                                          .filter(d -> Objects.equals(
                                                                                                                  d.getOrderDetailId(),
                                                                                                                  maxOrderDetailId))
                                                                                                          .collect(
                                                                                                                  Collectors.toList());
                            Integer offDay = DateUtil.calculateOffDay(productInfo.getEndTime(),
                                                                      !"frozen".equalsIgnoreCase(
                                                                              productInfo.getStatus())
                                                                              ? productInfo.getNow()
                                                                              : productInfo.getFrozenTime());
                            for (ServiceOrderPriceDetail priceDetail : lastOrderPriceDetails) {
                                BigDecimal tradePrice = priceDetail.getPrice();
                                Calendar calendar = Calendar.getInstance();
                                Date endTime = priceDetail.getEndTime();
                                Date lastMonthStart = DateUtils.addMonths(endTime, -1);
                                calendar.setTime(lastMonthStart);
                                BigDecimal finalCost = NumberUtil.div(tradePrice,
                                                                      calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                                                 .multiply(BigDecimal.valueOf(offDay)).setScale(5, RoundingMode.HALF_UP);
                                response.setExpiredUsedAmount(response.getExpiredUsedAmount().add(finalCost));
                            }
                            // }
                        }
                    }
                });
            }
            response.setDetail(resourceDetailVOS);
            BigDecimal finalUsed = response.getUsedAmount();
            response.setUsedAmount(finalUsed);
            if (!inquiryTag.get()) {
                response.setUnsubAmount(response.getTotalPayment().subtract(finalUsed));
            }
            // 需要退订的金额=总支付-已使用
            response.setUnsubCouponAmount(
                    NumberUtil.sub(response.getTotalCouponAmount(), response.getTotalUsedCouponAmount()));
            response.setUnsubCashAmount(
                    NumberUtil.sub(response.getTotalCashAmount(), response.getTotalUsedCashAmount()));
            response.setUnsubCreditAmount(
                    NumberUtil.sub(response.getTotalCreditAmount(), response.getTotalUsedCreditAmount()));
            response.setUnsubAmount(response.getUnsubCashAmount());
        }else if(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(type)){
            UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = unsubInquiryAIPrice(id);
            ResourcePayPriceVO unsubscribeAmountVo = unsubscribeInquiryPriceVO.getUnsubscribeAmount();
            //退订金额需要加上补扣费用-退订金额
            response.setUnsubAmount(NumberUtil.sub(unsubscribeAmountVo.getAmount(),
                                                   unsubscribeInquiryPriceVO.getExpiredUsedAmount()));

            //退订现金
            response.setUnsubCashAmount(unsubscribeAmountVo.getCashAmount());
            //退订信用额度
            response.setUnsubCreditAmount(unsubscribeAmountVo.getCreditAmount());
            //退订现金支付券
            response.setUnsubCouponAmount(unsubscribeAmountVo.getCouponAmount());

        }else if(ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(type)
                || ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(type)){
            inquiryUnsubscribePriceContainSubsidiaryResource(id, response);
        }else if(!ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(type)
                && !ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(type)
                && (ProductCodeEnum.federationProducts().contains(type)
                || ProductCodeEnum.isCmpApiProduct().contains(type))){
            UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = unsubInquiryResPrice(id);
            ResourcePayPriceVO unsubscribeAmountVo = unsubscribeInquiryPriceVO.getUnsubscribeAmount();
            //退订金额需要加上补扣费用-退订金额
            response.setUnsubAmount(NumberUtil.sub(unsubscribeAmountVo.getAmount(),
                                                   unsubscribeInquiryPriceVO.getExpiredUsedAmount()));

            //退订现金
            response.setUnsubCashAmount(unsubscribeAmountVo.getCashAmount());
            //退订信用额度
            response.setUnsubCreditAmount(unsubscribeAmountVo.getCreditAmount());
            //退订现金支付券
            response.setUnsubCouponAmount(unsubscribeAmountVo.getCouponAmount());
            if(ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(type)){
                response.setDetail(unsubscribeInquiryPriceVO.getDetail());
            }

        } else {
            SfProductResource sfProductResource = sfProductResourceService.getById(id);
            ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(
                    Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                            .eq(ServiceOrderResourceRef::getResourceId, id)
                            .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType()));
            Assert.notNull(serviceOrderResourceRef);
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailService
                    .getById(serviceOrderResourceRef.getOrderDetailId());
            computeInnerProject(serviceOrderDetail, id, response);
        }
        return response;
    }

    private void computeInnerProject(ServiceOrderDetail serviceOrderDetail, String id, UnsubInquiryPriceVO response) {
        SfProductResource sfProductResource = sfProductResourceService.getById(id);

        if (Objects.nonNull(sfProductResource)) {
            if (ProductCodeEnum.HPC_DRP.getProductType()
                    .equalsIgnoreCase(sfProductResource.getProductType())) {
                inquiryUnsubscribePriceList(id, response);
                return;
            } else if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(sfProductResource.getProductType())
                    || ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(sfProductResource.getProductType())) {
                inquiryUnsubscribePriceContainSubsidiaryResource(id, response);
                return;
            }
       /*     ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(
                    Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                            .eq(ServiceOrderResourceRef::getResourceId, sfProductResource.getId())
                            .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType()));
            if (ObjectUtils.isEmpty(serviceOrderResourceRef)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailService
                    .getById(serviceOrderResourceRef.getOrderDetailId());*/
            // 订单询价，DRP退订询价，只算现金支付，amount含有信用额度及充值现金券金额
            Criteria criteria = new Criteria();
            //申请订单的
            criteria.put("orderDetailIdIn", Lists.newArrayList(serviceOrderDetail.getId()));
            List<ServiceOrderPriceDetail> applyOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(criteria);
            BigDecimal price = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(applyOrderPriceDetails)) {
                //求和
                price = applyOrderPriceDetails.stream()
                                              .map(ServiceOrderPriceDetail::getPayBalance).reduce(BigDecimal::add)
                                              .orElse(BigDecimal.ZERO);
            }
            // 续订询价
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + id + "\"");
            param.put("orderTypes", Lists.newArrayList("renew"));
            param.put("orderStatus", "completed");
            param.put("productCode",sfProductResource.getProductType());
            List<ServiceOrderPriceDetail> renewOrderPriceDetails = serviceOrderPriceDetailMapper
                    .selectOrderInfo(param);
            BigDecimal renewTotalCost = BigDecimal.ZERO;
            //已使用的现金支付金额
            BigDecimal used = price;
            //预付费计算退费
            if(ChargeTypeEnum.PrePaid.getType().equalsIgnoreCase(serviceOrderDetail.getChargeType())) {
                if (CollectionUtil.isNotEmpty(renewOrderPriceDetails)) {
                    List<String> orderIdList = renewOrderPriceDetails.stream()
                                                                     .map(t -> t.getOrderId().toString())
                                                                     .collect(Collectors.toList());
                    org.springframework.data.mongodb.core.query.Criteria criteria1 = org.springframework.data.mongodb.core.query.Criteria
                            .where("orderId").in(orderIdList);
                    List<InstanceGaapCost> renewCosts = mongoTemplate
                            .find(Query.query(criteria1), InstanceGaapCost.class, "biz_bill_usage_item");
                    if (CollectionUtils.isNotEmpty(renewCosts)) {
                        renewTotalCost = renewCosts.stream().map(InstanceGaapCost::getCashAmount)
                                                   .filter(Objects::nonNull)
                                                   .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                //资源结束时间
                Date resEndTime = Objects.isNull(sfProductResource.getEndTime()) ? new Date() : sfProductResource.getEndTime();
                //订单开始时间
                Date startTime = serviceOrderDetail.getStartTime();
                //订单结束时间
                Date endTime = Optional.ofNullable(serviceOrderDetail.getEndTime()).orElse(new Date());
                //当前时间
                Date currentDate = new Date();
                if (resEndTime.after(currentDate)) {
                    Date st = cn.hutool.core.date.DateUtil.date(startTime);
                    int month = 0;
                    log.info("...,{},....{}",DateUtil.dateFormat(st),DateUtil.dateFormat(currentDate));
                    Date temp = st;
                    while (temp.before(currentDate)) {
                        month++;
                        temp = cn.hutool.core.date.DateUtil.offsetMonth(st, month);
                        log.info("...,{},....{}",DateUtil.dateFormat(temp));
                    }
                    st = temp;
                    currentDate = st.after(resEndTime) ? resEndTime : st;
                    log.info("...,{},....{}",DateUtil.dateFormat(currentDate),DateUtil.dateFormat(resEndTime));
                }
                log.info("...,{},....{}",DateUtil.dateFormat(endTime),DateUtil.dateFormat(currentDate));
                //如果当前时间未到购买订单结束时间
                if (!endTime.before(currentDate)) {
                    long offMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, currentDate);
                    Integer duration = serviceOrderDetail.getDuration();
                    if("0".equals(String.valueOf(serviceOrderDetail.getDuration()))){
                        duration=1;
                    }
                    used = NumberUtil.div(price.multiply(BigDecimal.valueOf(offMonths)),duration);
                } else {
                    String discountSwitch = configMapper.selectConfigValue(CommonPropertyKeyEnum.EXPIRE_DRP_DISCOUNT_SWITCH.getCode());
                    //当前时间在订单结束时间后面，查看续订订单时间
                    if (CollectionUtil.isNotEmpty(renewOrderPriceDetails)) {
                        for (ServiceOrderPriceDetail renewOrderPriceDetail : renewOrderPriceDetails) {
                            //续订结束时间
                            Date renewEndTime = renewOrderPriceDetail.getEndTime();
                            //续订开始时间
                            Date renewStartTime = renewOrderPriceDetail.getStartTime();
                            //续订现金支付基恩
                            BigDecimal amount = renewOrderPriceDetail.getPayBalance();
                            BigDecimal deductionAmount = BigDecimal.ZERO;
                            //初始化超期补扣金额
                            renewOrderPriceDetail.setExpiredUsedAmount(renewOrderPriceDetail.getExpiredUsedAmount() != null ? renewOrderPriceDetail.getExpiredUsedAmount():BigDecimal.ZERO);
                            // 该续订是否含有补扣金额(现金支付)
                            if ( BigDecimal.ZERO.compareTo(renewOrderPriceDetail.getExpiredUsedAmount()) != 0) {
                                // 存在补扣金额
                                Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("orderId")
                                                                                                            .is(String.valueOf(
                                                                                                                    renewOrderPriceDetail
                                                                                                                            .getOrderId()))
                                                                                                            .and("pretaxGrossAmount")
                                                                                                            .regex(renewOrderPriceDetail
                                                                                                                           .getExpiredUsedAmount()
                                                                                                                           .stripTrailingZeros()
                                                                                                                           .toPlainString()));
                                //补扣账单
                                InstanceGaapCost makeUpCosts = mongoTemplate.findOne(query, InstanceGaapCost.class,
                                                                                     "biz_bill_usage_item");
                                if (Objects.nonNull(makeUpCosts)) {
                                    deductionAmount = makeUpCosts.getCashAmount();
                                }
                            }

                            // 补扣金额记入已使用金额
                            used = used.add(deductionAmount);

                            if (renewEndTime.before(currentDate)) {
                                used = used.add(amount);
                                if (renewEndTime.equals(resEndTime)) {
                                    long offsetDays=0;
                                    BigDecimal originalCost = renewOrderPriceDetail.getPrice();
                                    if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(renewOrderPriceDetail.getProductCode())
                                            && CommonPropertyKeyEnum.ONE.getCode().equals(discountSwitch)) {
                                        // 武汉定制化
                                        originalCost = renewOrderPriceDetail.getTradePrice();
                                    }
                                    //开发训练获取当前的最大节点数，计算补扣费用
                                    if(Objects.nonNull(sfProductResource.getClusterId())
                                            && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))){
                                        Integer availableCount = Integer.valueOf(resMaPoolService.getResMaPoolAvailableCount(sfProductResource.getClusterId())
                                                .getData().toString());
                                        originalCost=originalCost.multiply(BigDecimal.valueOf(availableCount));
                                    }
                                    if("frozen".equals(sfProductResource.getStatus())){
                                        Date frozenTime = sfProductResource.getFrozenTime();
                                        offsetDays =DateUtil.calculateOffDay(renewEndTime,frozenTime);
                                    }else{
                                        offsetDays = DateUtil.calculateOffDay(renewEndTime, currentDate);
                                    }
                                    BigDecimal allDays = NumberUtil.div(BigDecimal.valueOf(
                                                                           renewEndTime.getTime() - renewStartTime.getTime()), BigDecimal.valueOf(1000 * 60 * 60 * 24L))
                                                                   .setScale(0, BigDecimal.ROUND_CEILING);
                                    used = used.add(NumberUtil.div(originalCost.multiply(BigDecimal.valueOf(offsetDays)),
                                                                   allDays));
                                }

                            } else if (renewStartTime.before(currentDate)) {
                                long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime,
                                                                                                                         renewEndTime);
                                long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime,
                                                                                                                          currentDate);
                                // 如果未到续订服务开始时间，则只计算补扣部分费用，整月的购买费用退还
                                if (renewStartTime.before(new Date()) && totalMonth>0) {
                                    if (amount.compareTo(BigDecimal.ZERO) < 0) {
                                        amount = BigDecimal.ZERO;
                                    }
                                    // 按比例计算
                                    used = used.add(NumberUtil.div(amount.multiply(BigDecimal.valueOf(offsetMonth)),
                                                                   totalMonth));
                                }
                            }
                        }
                    } else {
                        ServiceOrder serviceOrder = serviceOrderMapper.selectById(serviceOrderDetail.getOrderId());
                        BigDecimal originalCost = serviceOrder.getOriginalCost();
                        //开发训练获取当前的最大节点数，计算补扣费用
                        if(Objects.nonNull(sfProductResource.getClusterId())
                                && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))){
                            Integer availableCount = Integer.valueOf(resMaPoolService.getResMaPoolAvailableCount(sfProductResource.getClusterId())
                                    .getData().toString());

                            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(serviceOrderDetail.getServiceType())
                                    && CommonPropertyKeyEnum.ONE.getCode().equals(discountSwitch)) {
                                originalCost = serviceOrderDetail.getPrice();
                            }
                            originalCost=originalCost.multiply(BigDecimal.valueOf(availableCount));
                        }else if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(serviceOrderDetail.getServiceType())
                                && CommonPropertyKeyEnum.ONE.getCode().equals(discountSwitch)) {
                            // 武汉定制化
                            originalCost = serviceOrder.getFinalCost();
                        }
                        //判断是否冻结,进行补缴费用
                        long offsetDays=0;
                        if("frozen".equals(sfProductResource.getStatus())){
                            Date frozenTime = sfProductResource.getFrozenTime();
                            offsetDays =DateUtil.calculateOffDay(endTime,frozenTime);
                        }else{
                            offsetDays = DateUtil.calculateOffDay(endTime, currentDate);
                        }
                        BigDecimal allDays = NumberUtil.div(BigDecimal.valueOf(
                                                               endTime.getTime() - startTime.getTime()), BigDecimal.valueOf(1000 * 60 * 60 * 24L))
                                                       .setScale(0, BigDecimal.ROUND_CEILING);
                        used = used.add(NumberUtil.div(originalCost.multiply(BigDecimal.valueOf(offsetDays)), allDays));

                    }
                }
            }
            response.setTotalPayment(NumberUtil.add(price, renewTotalCost));
            response.setUsedAmount(used.setScale(5, BigDecimal.ROUND_HALF_UP));
            // 补扣金额
            response.setUnsubAmount(NumberUtil.sub(response.getTotalPayment(),
                                                   used.setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        }
    }

    @Override
    public ModifyInquiryPriceVO modifyInquiryPrice(ModifyInquiryPriceRequest request) {
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        ModifyInquiryPriceVO modifyInquiryPriceVO = new ModifyInquiryPriceVO();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setId(request.getId());
        productInfo.setProductCode(request.getTargetType());
        productInfo.setTargetSize(request.getTargetSize());
        productInfo.setTargetType(request.getTargetType());
        resRenewRefService.completeProductInfo(productInfo);
        if (productInfo.isPublic()) {
            request.setHasSystemDisk(true);

        }
        modifyInquiryPriceVO.setChargeUnit("month");
        if (ChargeTypeEnum.PostPaid.getType().equals(productInfo.getChargeType())) {
            modifyInquiryPriceVO.setChargeUnit("hour");
        }

        // 涉及到AI按量折扣策略，先处理目标数量
        SfProductResource sfProductResource = sfProductResourceService.getById(request.getId());
        ResMaPoolVO resMaPoolVO = new ResMaPoolVO();
        ResourceDetailVO resourceDetailVO = new ResourceDetailVO();
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(request.getTargetType())) {
            RestResult result = resMaPoolService.getResMaPoolInfo(sfProductResource.getClusterId());
            resMaPoolVO = BeanConvertUtil.convert(result.getData(), ResMaPoolVO.class);
            if (Objects.isNull(resMaPoolVO) || io.seata.common.util.StringUtils.isEmpty(resMaPoolVO.getName())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1055800560));
            }
            if (request.getTargetSize().compareTo(resMaPoolVO.getAvailableCount()) < 0) {
                resourceDetailVO.setOrderType(OrderType.DEGRADE);
            }else {
                resourceDetailVO.setOrderType(OrderType.UPGRADE);

                int changeCount = request.getTargetSize() - resMaPoolVO.getAvailableCount();
                request.setTargetSize(changeCount);
            }
        } else if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(request.getTargetType())) {
            ResShare resShare = shareRemoteService.selectByPrimaryKey(Long.parseLong(request.getId()));
            resourceDetailVO.setScaleUp(request.getTargetSize() > resShare.getSize());
            serviceOrderService.checkPendingOrder(productInfo.getId(), productInfo.getProductCode());
        }
        Boolean isRds=sfProductResource.getProductType().equals(ProductCodeEnum.RDS.getProductType());
        if(isRds){
            request.setTargetType(ProductCodeEnum.RDS.getProductType());
        }
        ProductInfoVO productInfoVO = resourceConfigService.generateModifyInquiryBody(request);
        List<EntityDTO> entityDTOs = categoryService.getByServiceType(request.getTargetType());

        if (CollectionUtil.isEmpty(entityDTOs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1964963679));
        }

        EntityDTO entityDTO = entityDTOs.get(0);
        User authUser = AuthUtil.getAuthUser();
        Long userSid = authUser.getUserSid();
        if (Objects.nonNull(authUser.getParentSid())) {
            userSid = authUser.getParentSid();
        }
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entityDTO.getEntityId(), userSid);
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
        }

        productInfoVO.setEntityId(entityDTO.getEntityId());
        productInfoVO.setEntityName(entityDTO.getEntityName());
        productInfoVO.setBillingAccountId(account.getId());
        BigDecimal applyPeriod = productInfoVO.getPeriod();
        //AI扩容按照日期折扣
        if (OrderType.UPGRADE.equals(resourceDetailVO.getOrderType())) {
            //计算当前时间扩容是否享受折扣
            if (Objects.nonNull(sfProductResource)) {
                int monthDiff = DateUtil.getMonthDiff(new Date(), sfProductResource.getEndTime());
                productInfoVO.setPeriodTime(BigDecimal.valueOf(monthDiff));
            }

        } else {
            productInfoVO.setPeriodTime(BigDecimal.ONE);
        }
        productInfoVO.setPeriod(BigDecimal.ONE);
        // 询价
        InquiryPriceResponse inquiryPriceResponse = this.convertPriceResponse(this.inquiryPrice(productInfoVO),
                                                                              productInfoVO.getChargeType());
        modifyInquiryPriceVO.setPrice(inquiryPriceResponse.getOriginalPrice());
        modifyInquiryPriceVO.setPlatformDiscount(inquiryPriceResponse.getPlatformDiscount());
        modifyInquiryPriceVO.setBillingPrices(inquiryPriceResponse.getBillingPrices());
        if (!Objects.isNull(productInfo.getCurrentType()) && compareConfig(productInfo, request)) {
            return modifyInquiryPriceVO;
        }

        if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(request.getTargetType())
                || ProductCodeEnum.DCS.getProductType().equals(request.getTargetType())
        ) {
            BigDecimal tradePrice = inquiryPriceResponse.getTradePrice();
            if (tradePrice.compareTo(productInfoVO.getOldPrice()) == 0) {
                request.setTargetSize(0);
                resourceDetailVO.setScaleUp(true);
            } else {
                resourceDetailVO.setScaleUp(tradePrice.compareTo(productInfoVO.getOldPrice()) > 0);
            }
        } else if (ProductCodeEnum.CCE.getProductType().equals(request.getTargetType())) {
            resourceDetailVO.setScaleUp(true);
        } else if (ProductCodeEnum.FLOATING_IP.getProductType().equals(request.getTargetType())) {
            resourceDetailVO.setScaleUp(request.getTargetSize() > productInfoVO.getSize());
        } else if (ProductCodeEnum.DISK.getProductType().equals(request.getTargetType())) {
            modifyInquiryPriceVO.setChargeUnit("month");
            if (ChargeTypeEnum.PostPaid.getType().equals(productInfo.getChargeType())) {
                modifyInquiryPriceVO.setChargeUnit("hour");
                modifyInquiryPriceVO.setShowAmount(false);
            }
            if (Objects.equals(Long.valueOf(request.getTargetSize()), productInfoVO.getSize())) {
                modifyInquiryPriceVO.setAmount(BigDecimal.ZERO);
                return modifyInquiryPriceVO;
            }
            if (request.getTargetSize() < productInfoVO.getSize()) {
                //云硬盘不支持缩容
                throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUPPORT_CAPACITY_REDUCTION));
            }

            resourceDetailVO.setScaleUp(true);
        } else if (ProductCodeEnum.SFS_TURBO.getProductType().equals(request.getTargetType())) {
            resourceDetailVO.setScaleUp(true);
        } else if (ProductCodeEnum.RDS.getProductType().equals(request.getTargetType())) {
            resourceDetailVO.setScaleUp(true);
        }

        if (ChargeTypeEnum.PrePaid.getType().equals(productInfo.getChargeType()) ||
                ProductCodeEnum.DISK.getProductType().equals(request.getTargetType())
        ) {
            resourceDetailVO.setId(request.getId());
            resourceDetailVO.setProductCode(productInfo.getProductCode());
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(request.getTargetType())) {
                if (OrderType.UPGRADE.equalsIgnoreCase(resourceDetailVO.getOrderType())) {
                    if (0 == request.getTargetSize()) {
                        inquiryPriceResponse.setTradePrice(BigDecimal.ZERO);
                        modifyInquiryPriceVO.setPrice(BigDecimal.ZERO);
                    } else {
                        modifyInquiryPriceVO.setPrice(NumberUtil.div(inquiryPriceResponse.getOriginalPrice(), request.getTargetSize()));
                    }
                }
            } else {
                resourceDetailVO.setOrderType(OrderType.MODIFY);
            }
            iServiceOrderDetailService.completeResourceDetail(resourceDetailVO);
            BigDecimal totalUsed = NumberUtil.add(resourceDetailVO.getTotalUsedAmount(),
                                                  resourceDetailVO.getServiceAmount().getTotalUsed(),
                                                  resourceDetailVO.getExtraConfigAmount().getTotalUsed());
            BigDecimal totalPayment = NumberUtil.add(resourceDetailVO.getTotalPayment(),
                                                     resourceDetailVO.getServiceAmount().getTotalPayment(),
                                                     resourceDetailVO.getTotalCouponDiscount(),
                                                     resourceDetailVO.getExtraConfigAmount().getTotalPayment());
            BigDecimal giveBack = totalUsed.subtract(totalPayment);
            log.info("resource totalPayment:{}. totalUsed:{}", totalPayment, totalUsed);

            // 变配价格（差价） = （新规格价格（天） X 剩余时常）- 原剩余时常总价格
            Date startTime = productInfo.getStartTime();
            Date endTime = productInfo.getEndTime();
            Date modifyTime = Convert.toDate(resourceDetailVO.getComputeDate(), new Date());
            // 获取从开始到当前周期结束的月差
            Date tmpDate = startTime;
            int offsetMonth = 0;
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(request.getTargetType())) {
                while (tmpDate.before(modifyTime)) {
                    tmpDate = cn.hutool.core.date.DateUtil.offsetMonth(startTime, ++offsetMonth);
                }
            } else {
                while (tmpDate.before(resourceDetailVO.getCurrentDate())) {
                    tmpDate = cn.hutool.core.date.DateUtil.offsetMonth(startTime, ++offsetMonth);
                }
            }

            // 当前周期开始时间
            Date currentStartTime = cn.hutool.core.date.DateUtil.offsetMonth(startTime, offsetMonth - 1);
            // 当前周期结束时间
            Date currentEndTime = cn.hutool.core.date.DateUtil.offsetMonth(startTime, offsetMonth);
            // 当前周期需要补缴天数
            Integer days = DateUtil.calculateOffDay(modifyTime, currentEndTime);
            // 当前周期总天数
            Integer allDays = DateUtil.calculateOffDay(currentStartTime, currentEndTime);
            // 兼容旧代码
            startTime = currentEndTime;
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(cn.hutool.core.date.DateUtil.offsetMonth(startTime, -1));
            BigDecimal newPrice;
            boolean isExitBk = false;
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
                //modelarts扩缩容，计费按月算，不满一月按一月计算,如果存在续订过期得时候，在补扣期间扩容得不计费补扣期间得费用
                //判断当前时间是否在不扣区间
                Date date = new Date();
                ServiceOrderPriceDetail orderPriceDetail = this.findExpiredOrderPriceDetail(date, request.getId(),
                                                                                            ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
                if (orderPriceDetail != null) {
                    isExitBk = true;
                    date = cn.hutool.core.date.DateUtil.offsetDay(orderPriceDetail.getEndTime(), 1);
                }
                Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(date, endTime);
                newPrice = NumberUtil.mul(inquiryPriceResponse.getTradePrice(), offsetMonths);
            } else {
                //扩容剩余天数向上取整，缩容剩余天数向下取整
                days = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(resourceDetailVO.getCurrentDate(), currentEndTime,
                                                                                            resourceDetailVO.getScaleUp());
                newPrice = NumberUtil.mul(
                        NumberUtil.div(inquiryPriceResponse.getTradePrice(), allDays), days);
                newPrice = newPrice.add(NumberUtil.mul(inquiryPriceResponse.getTradePrice(),
                                                       cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime)));
            }

            /*if (ProductCodeEnum.DCS.getProductType().equals(request.getTargetType()) && OrderType.MODIFY.equals(resourceDetailVO.getOrderType())) {
                Long clusterId = sfProductResource.getClusterId();
                log.info("dcs变更目标:{}", clusterId);
                ResDcs dcs = resDcsRemoteService.selectByPrimaryKey(clusterId);
                BigDecimal num = BigDecimal.ONE;
                if (Objects.nonNull(dcs.getDataCopyNum()) && dcs.getDataCopyNum() > 1) {
                    num = BigDecimal.valueOf(dcs.getDataCopyNum());
                }
                inquiryPriceResponse.setTradePrice(inquiryPriceResponse.getTradePrice().multiply(num));
                //扩容剩余天数向上取整，缩容剩余天数向下取整
                days = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(resourceDetailVO.getCurrentDate(), currentEndTime,resourceDetailVO.getScaleUp());
                newPrice = NumberUtil.mul(
                        NumberUtil.div(inquiryPriceResponse.getTradePrice(), allDays), days);
                newPrice = newPrice.add(NumberUtil.mul(inquiryPriceResponse.getTradePrice(),
                        cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime)));
            }*/


            newPrice = newPrice.setScale(5, RoundingMode.HALF_UP);
            giveBack = giveBack.compareTo(BigDecimal.ZERO) == 1 ? BigDecimal.ZERO : giveBack;
            // 资源
            modifyInquiryPriceVO.setGiveBack(giveBack);
            switch (resourceDetailVO.getOrderType()) {
                case OrderType.UPGRADE:
                    // AI扩容直接收取新增节点当前价格配置的费用
                    modifyInquiryPriceVO.setAmount(newPrice);
                    break;
                case OrderType.DEGRADE:
                    // AI缩容根据剩余金额、节点数量、剩余时间计算平均单价，以此计算退费
                    // 判断是否存在补扣，存在补扣则重置开始时间的天为结束时间的天
                    if (isExitBk) {
                        int day = cn.hutool.core.date.DateUtil.dayOfMonth(endTime);
                        Calendar calendar2 = Calendar.getInstance();
                        calendar2.setTime(startTime);
                        calendar2.set(Calendar.DATE, day);
                        startTime = calendar2.getTime();
                    }

                    Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime);
                    if (BigDecimal.ZERO.equals(BigDecimal.valueOf(offsetMonths))) {
                        // 未使用月数为0，不退款
                        modifyInquiryPriceVO.setAmount(BigDecimal.ZERO);
                        modifyInquiryPriceVO.setPrice(BigDecimal.ZERO);
                    } else {
                        // 未使用月数不为0，根据平均单价与未使用月数退款
                        BigDecimal totalPrice = NumberUtil.div(giveBack, offsetMonths);
                        newPrice = NumberUtil.div(totalPrice, resMaPoolVO.getAvailableCount());
                        BigDecimal singleNodePrice = BigDecimal.ZERO.subtract(newPrice);
                        modifyInquiryPriceVO.getBillingPrices().get(0).setMonthPrice(singleNodePrice);
                        //退款得时候只退现金支付部分
                        //公式： 用总支付单价(包含现金支付，信用额度，充值现金券) * 现金支付占比
                        modifyInquiryPriceVO.setPrice(NumberUtil.mul(singleNodePrice, resourceDetailVO.getUnsubAmount())
                                                                .setScale(5, RoundingMode.HALF_UP));
                        modifyInquiryPriceVO.setPlatformDiscount(BigDecimal.ONE);
                        int changeCount = resMaPoolVO.getAvailableCount() - request.getTargetSize();
                        //总退金额 = 单价*退订月份*退订节点 ，根据金额取反显示负数得退订金额
                        modifyInquiryPriceVO.setAmount(
                                BigDecimal.ZERO.subtract(NumberUtil.mul(modifyInquiryPriceVO.getPrice(), changeCount, offsetMonths)
                                                                   .setScale(2, RoundingMode.HALF_UP)));
                        modifyInquiryPriceVO.setGiveBack(modifyInquiryPriceVO.getAmount());
                    }
                    break;
                default:
                    modifyInquiryPriceVO.setAmount(newPrice.add(giveBack));
                    if (BigDecimal.ZERO.compareTo(modifyInquiryPriceVO.getAmount()) > 0 && !BigDecimal.ZERO.equals(totalPayment)) {
                        // 退款的场景下，计算现金支付比例，按比例退款,当信用额度或充值现金券不为0时
                        if (NumberUtil.isGreater(resourceDetailVO.getTotalCreditAmount(), BigDecimal.ZERO) || NumberUtil.isGreater(
                                resourceDetailVO.getTotalCouponAmount(), BigDecimal.ZERO)) {
                            BigDecimal amount = modifyInquiryPriceVO.getAmount().multiply(resourceDetailVO.getTotalCashAmount())
                                                                    .divide(totalPayment, 5, RoundingMode.HALF_UP);
                            modifyInquiryPriceVO.setAmount(amount);
                        }
                    }
            }
            modifyInquiryPriceVO.setComputeDate(modifyTime);
            modifyInquiryPriceVO.setComputeEndDate(startTime);
            // 服务
            BigDecimal serviceGiveBack = resourceDetailVO.getServiceAmount().getTotalUsed()
                                                         .subtract(resourceDetailVO.getServiceAmount().getTotalPayment());
            BigDecimal newServicePrice;
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
                Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime);
                newServicePrice = NumberUtil.mul(
                        inquiryPriceResponse.getServiceAmount()
                                            .multiply(inquiryPriceResponse.getPlatformDiscount()), offsetMonths);
            } else {
                newServicePrice = NumberUtil.mul(NumberUtil.div(
                        inquiryPriceResponse.getServiceAmount()
                                            .multiply(inquiryPriceResponse.getPlatformDiscount()),
                        calendar.getActualMaximum(Calendar.DAY_OF_MONTH)), days);
                newServicePrice = newServicePrice.add(NumberUtil.mul(
                        inquiryPriceResponse.getServiceAmount()
                                            .multiply(inquiryPriceResponse.getPlatformDiscount()),
                        cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime)));
            }
            newServicePrice = newServicePrice.setScale(5, RoundingMode.HALF_UP);
            serviceGiveBack = serviceGiveBack.compareTo(BigDecimal.ZERO) == 1 ? BigDecimal.ZERO : serviceGiveBack;
            modifyInquiryPriceVO.setServiceGiveBack(serviceGiveBack);
            modifyInquiryPriceVO.setServiceAmount(newServicePrice.add(serviceGiveBack));
        } else {
            modifyInquiryPriceVO.setAmount(inquiryPriceResponse.getTradePrice());
        }

        ServiceOrderPriceDetail serviceOrderPriceDetail
                = iServiceOrderPriceDetailService.selectServiceId(request.getId(), productInfo.getProductCode());
        if (Objects.nonNull(serviceOrderPriceDetail)) {
            modifyInquiryPriceVO.setProjectId(serviceOrderPriceDetail.getOrgSid());
        }
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            modifyInquiryPriceVO.setProjectId(productInfo.getProjectId());
        }

        //扩容时补差金额不能小于0
        if (Objects.nonNull(modifyInquiryPriceVO.getAmount()) && BigDecimal.ZERO.compareTo(modifyInquiryPriceVO.getAmount()) > 0){
            Integer newValue = 0;
            Integer oldValue = 0;
            if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
                ResShare resShare = shareRemoteService.selectByPrimaryKey(Long.parseLong(request.getId()));
                newValue = request.getTargetSize();
                oldValue = resShare.getSize();
            } else if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
                cn.hutool.json.JSONObject productConfigDesc = JSONUtil.parseObj(productInfo.getProductConfigDesc());
                cn.hutool.json.JSONArray jsonArray = productConfigDesc.getJSONArray("changeDesc");
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(jsonArray)) {
                    cn.hutool.json.JSONObject changeDesc = (cn.hutool.json.JSONObject) jsonArray.get(0);
                    newValue = changeDesc.getInt("newValue");
                    oldValue = changeDesc.getInt("oldValue");
                } else {
                    //如果走这条分支，说明询价时当前配置与目标配置一致，但是有退款
                    newValue = 1;
                    oldValue = 0;
                }
            }
            if (newValue > oldValue) {
                //如果补差金额为负，记录负值，这部分退订时不退款
                modifyInquiryPriceVO.setNegativeAmount(modifyInquiryPriceVO.getAmount());
                modifyInquiryPriceVO.setAmount(BigDecimal.ZERO);
            }
        }

        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(
                    BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()),
                    cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
        }
        return modifyInquiryPriceVO;

    }


    public ServiceOrderPriceDetail findExpiredOrderPriceDetail(Date date, String refInstanceId, String productCode) {
        return iServiceOrderPriceDetailService.findExpiredOrderPriceDetail(date, refInstanceId, productCode);
    }

    @Override
    public InquiryPriceResponse inquiryRenewPrice(InquiryRenewPriceRequestDTO request) {
        ProductInfo productInfo = new ProductInfo();
        productInfo.setId(request.getId());
        productInfo.setProductCode(request.getProductCode());
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            serviceOrderService.checkPendingOrder(productInfo.getId(), productInfo.getProductCode());
        }
        resRenewRefService.completeProductInfo(productInfo);

        /*if (ProductCodeEnum.DCS.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            productInfo.setQuantity(request.getQuantity());
        }*/

        InquiryPriceResponse inquiryPriceResponse = getInquiryPriceResponse(request, productInfo);
        return inquiryPriceResponse;

    }

    @Override
    public InquiryPriceResponse getInquiryPriceResponse(InquiryRenewPriceRequestDTO request, ProductInfo productInfo) {
        String config = productInfo.getConfig();
        if (Strings.isNullOrEmpty(config)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_431199933));
        }
        if (ChargeTypeEnum.PostPaid.getType().equals(productInfo.getChargeType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1852197570));
        }

        JSONObject jsonObject = JSONObject.parseObject(config);

        boolean hasNow = Objects.nonNull(productInfo.getNow());
        DateTime beforeRenewDate = DateTime.of(
                hasNow ? productInfo.getNow() : productInfo.getEndTime());
        DateTime offset = beforeRenewDate.offset(DateField.MONTH, request.getPeriod().intValue());
        DateTime clone = (DateTime) offset.clone();

        if (Objects.nonNull(request.getUnifyDate())) {
            // 如果设置了统一到期时间，计算新的续费时长
            int i = clone.dayOfMonth();
            if (i > request.getUnifyDate()) {
                clone.offset(DateField.MONTH, 1);
            }
            clone.setField(DateField.DAY_OF_MONTH, request.getUnifyDate());
            clone.setField(DateField.HOUR_OF_DAY, 23);
            clone.setField(DateField.MINUTE, 59);
            clone.setField(DateField.SECOND, 59);
        }
        //下次结束时间到上次结束时间总天数
        double days = ((double) (clone.getTime() - offset.getTime())) / (1000 * 60 * 60 * 24);
        double addPoint = days / 30;
        BigDecimal point = request.getPeriod().add(BigDecimal.valueOf(addPoint).setScale(4, RoundingMode.UP));
        ProductInfoVO productInfoVO = BeanConvertUtil.convert(jsonObject, ProductInfoVO.class);
        //设置申请时长
        productInfoVO.setPeriod(point);
        if (!ProductCodeEnum.innerServiceProducts().contains(productInfo.getProductCode())) {
            productInfoVO.setAmount(productInfo.getQuantity());
        }
        productInfoVO.setAmount(productInfo.getQuantity());
        if (ProductCodeEnum.DCS.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            log.info("申请数量:{}", productInfoVO.getAmount());
        }
        //设置账户id
        productInfoVO.setBillingAccountId(request.getAccountId());
        // 优惠卷设置
        productInfoVO.setCouponId(request.getCouponId());
        productInfoVO.setContractId(request.getContractId());
        productInfoVO.setContractPrice(request.getContractPrice());
        if (Strings.isNullOrEmpty(request.getServiceId())) {
            ServiceOrderPriceDetail serviceOrderPriceDetail
                    = iServiceOrderPriceDetailService.selectServiceId(request.getId(), productInfo.getProductCode());
            String serviceId = "";
            if (Objects.nonNull(serviceOrderPriceDetail)) {
                serviceId = serviceOrderPriceDetail.getServiceId();
            }
            if (!Strings.isNullOrEmpty(serviceId)) {
                productInfoVO.setServiceId(Long.valueOf(serviceId));
            }
        } else {
            productInfoVO.setServiceId(Long.valueOf(request.getServiceId()));
        }

        if (Objects.isNull(productInfoVO.getEntityId())) {
            //根据续订产品查询对应运营实体
            EntityDTO entity = categoryService.getEntityByCategoryId(productInfoVO.getServiceId());
            productInfoVO.setEntityId(entity.getEntityId());
            productInfoVO.setEntityName(entity.getEntityName());
        }

        // 续订设置Flg 修改：续订的场合，也需要判断产品上下架
        InquiryPriceResponse inquiryPriceResponse = this.convertPriceResponse(this.inquiryPrice(productInfoVO), productInfoVO.getChargeType());

        Criteria criteria = new Criteria();
        String resourceId = productInfo.getId();
        if (StringUtil.isEmpty(resourceId)) {
            resourceId = productInfo.getResourceId();
        }
        criteria.put("refInstanceIdLike", "\"" + resourceId + "\"");
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", OrderType.UPGRADE, OrderType.DEGRADE));
        } else {
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
        }

        criteria.put("orderStatus", "completed");
        criteria.put("productCode", productInfo.getProductCode());
        if (productInfo.getMakeUpOrderDetailId() != null) {
            criteria.put("orderDetailIdIn", Lists.newArrayList(productInfo.getMakeUpOrderDetailId()));
        }

        // 此标识成立条件A.end_time >= A.start_time，会导致开通、扩容、超期续订场景下，补扣金额按照申请单价计算
        //criteria.put("isValid", true);
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = iServiceOrderPriceDetailService.selectByCriteria(
                criteria);

        SfProductResource sfProductResource = sfProductResourceService.getById(resourceId);
        if (Objects.isNull(sfProductResource)) {
            // 弹性文件没有sfProductResource，为了避免下面判断的地方出现空指针
            sfProductResource = new SfProductResource();
        }
        //租户越权判断
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (!ProductCodeEnum.SFS2.getProductType().equals(productInfo.getProductCode()) || ProductCodeEnum.SFS.getProductType()
                                                                                                              .equals(productInfo.getProductCode())) {
            if (!authUser.getAccount().equals(sfProductResource.getCreatedBy())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
        //如果是AI专属资源池，查询申请单对应的serviceOrderPriceDetail
        boolean noApply = serviceOrderPriceDetails.stream().noneMatch(priceDetail -> OrderType.APPLY.equals(priceDetail.getType()));
        if (noApply && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            Optional<ServiceOrderResourceRef> resourceRefOptional =
                    serviceOrderResourceRefService.lambdaQuery().eq(ServiceOrderResourceRef::getResourceId, resourceId)
                                                  .eq(ServiceOrderResourceRef::getType, productInfo.getProductCode())
                                                  .list().stream().findFirst();
            if (resourceRefOptional.isPresent()) {
                Long orderDetailId = resourceRefOptional.get().getOrderDetailId();
                List<ServiceOrderPriceDetail> applyPriceDetailList = iServiceOrderPriceDetailService.lambdaQuery()
                                                                                                    .eq(ServiceOrderPriceDetail::getOrderDetailId,
                                                                                                        orderDetailId)
                                                                                                    .eq(ServiceOrderPriceDetail::getType, "apply")
                                                                                                    .list();
                serviceOrderPriceDetails.addAll(applyPriceDetailList);
            }
        }
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            // AI专属资源池申请单serviceOrderPriceDetail中的quantity为1。
            serviceOrderPriceDetails.stream()
                                    .filter(priceDetail -> OrderType.APPLY.equals(priceDetail.getType()))
                                    .forEach(priceDetail -> priceDetail.setQuantity(productInfoVO.getAmount()));
        }

        Set<Long> orderDetailIds = serviceOrderPriceDetails.stream()
                                                           .map(ServiceOrderPriceDetail::getOrderDetailId)
                                                           .collect(Collectors.toCollection(LinkedHashSet::new));
        Long maxOrderDetailId = orderDetailIds.stream().max(Long::compare).orElse(-1L);
        List<ServiceOrderPriceDetail> lastOrderPriceDetails = serviceOrderPriceDetails.stream()
                                                                                      .filter(d -> Objects.equals(d.getOrderDetailId(),
                                                                                                                  maxOrderDetailId))
                                                                                      .collect(Collectors.toList());

        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                && Objects.nonNull(sfProductResource.getClusterId())) {
            RestResult resMaPoolInfo = resMaPoolService.getResMaPoolInfo(sfProductResource.getClusterId());
            ResMaPoolVO resMaPoolVO = BeanConvertUtil.convert(resMaPoolInfo.getData(), ResMaPoolVO.class);
            // 自动化的AI专属资源池直接设置为当前节点数，因为不同的订单类型记录的数量不一致
            if (!StringUtils.isEmpty(resMaPoolVO.getName())) {
                lastOrderPriceDetails.stream().forEach(priceDetail -> priceDetail.setQuantity(resMaPoolVO.getAvailableCount()));
            }
        }

        //当前订单补偿的天数
        Integer compenstionDays = null;
        //是否在补偿天数内
        boolean isCompensation = false;
        if (ProductCodeEnum.HPC_DRP.getProductType().equals(sfProductResource.getProductType())) {
            compenstionDays = this.statisticalCompensationDays(sfProductResource.getId(), productInfo.getEndTime(), null);
            isCompensation = isCompensation(resourceId, productInfo.getNow());
        }
        compenstionDays = Optional.ofNullable(compenstionDays).orElse(0);
        Date realEndTime = DateUtils.addDays(productInfo.getEndTime(), compenstionDays);
        //是否需要补扣
        if (hasNow && !isCompensation) {
            productInfo.getExpiredUsedPriceDetails().addAll(lastOrderPriceDetails);
            Integer newQuantity = productInfo.getQuantity();
            String discountSwitch = configMapper.selectConfigValue(CommonPropertyKeyEnum.EXPIRE_DRP_DISCOUNT_SWITCH.getCode());
            if (!"frozen".equalsIgnoreCase(productInfo.getStatus())) {
                inquiryPriceResponse.setNow(productInfo.getNow());

                Integer offDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(realEndTime, productInfo.getNow(), true);

                lastOrderPriceDetails.stream().forEach(priceDetail -> {

                    if (StringUtils.endsWithIgnoreCase("expired", priceDetail.getType())) {
                        return;
                    }
                    Integer oldQuantity = priceDetail.getQuantity();
                    //获取缩容率
                    BigDecimal degradeRate = this.getDegradeRate(newQuantity, oldQuantity, priceDetail.getProductCode());

                    // 续订与退订补扣金额逻辑保持一直
                    Calendar calendar = Calendar.getInstance();
                    Date endTime = priceDetail.getEndTime();
                    Date startTime = priceDetail.getStartTime();
                    Date lastMonthStart = DateUtils.addMonths(endTime, -1);
                    calendar.setTime(lastMonthStart);
                    int actualMaximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

                    // 开通了几个月
                    long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime);
                    BigDecimal price = priceDetail.getPrice();

                    // 武汉定制化
                    if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                            && CommonPropertyKeyEnum.ONE.getCode().equals(discountSwitch)) {
                        price = priceDetail.getTradePrice();
                    }
                    BigDecimal tradePrice = price.multiply(BigDecimal.valueOf(oldQuantity))
                                                 .multiply(degradeRate);

                    BigDecimal expiredUsedAmount;

                        //AI专属资源池扩容单需要除以数量
                        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
                            expiredUsedAmount = NumberUtil.mul(NumberUtil.div(priceDetail.getPrice(), newQuantity * actualMaximum), oldQuantity, offDay);
                        } else {
                            expiredUsedAmount = NumberUtil.div(tradePrice, actualMaximum).multiply(BigDecimal.valueOf(offDay));
                        }
                        inquiryPriceResponse.setExpiredUsedAmount(inquiryPriceResponse.getExpiredUsedAmount().add(expiredUsedAmount));
                        priceDetail.setExpiredUsedAmount(expiredUsedAmount);

                    priceDetail.setStartTime(endTime);
                    priceDetail.setEndTime(DateUtils.addDays(realEndTime, offDay));


                });

                inquiryPriceResponse.setExpiredUsedAmount(
                        inquiryPriceResponse.getExpiredUsedAmount().setScale(5, BigDecimal.ROUND_HALF_UP)
                );

                productInfo.setRenewStartTime(DateUtils.addDays(realEndTime, offDay));
            } else {

                inquiryPriceResponse.setNow(productInfo.getNow());
                Integer offDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(realEndTime,
                                                                                                      productInfo.getFrozenTime(), true);

                lastOrderPriceDetails.stream().forEach(priceDetail -> {
                    if (StringUtils.endsWithIgnoreCase("expired", priceDetail.getType())) {
                        return;
                    }
                    Integer oldQuantity = priceDetail.getQuantity();
                    //获取缩容率
                    BigDecimal degradeRate = this.getDegradeRate(newQuantity, oldQuantity, priceDetail.getProductCode());

                    BigDecimal price = priceDetail.getPrice();

                    // 武汉定制化
                    if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                            && CommonPropertyKeyEnum.ONE.getCode().equals(discountSwitch)) {
                        price = priceDetail.getTradePrice();
                    }
                    BigDecimal tradePrice = price.multiply(BigDecimal.valueOf(oldQuantity)).multiply(degradeRate);
                    Calendar calendar = Calendar.getInstance();
                    //上月实际天数
                    Date endTime = priceDetail.getEndTime();
                    Date lastMonthStart = DateUtils.addMonths(endTime, -1);
                    calendar.setTime(lastMonthStart);
                    int monthDays = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

                    BigDecimal finalCost;
                    //AI专属资源池扩容单需要除以数量
                    if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                            && OrderType.UPGRADE.equals(priceDetail.getType())) {
                        finalCost = NumberUtil.mul(NumberUtil.div(price, newQuantity * monthDays), oldQuantity, offDay);
                    } else {
                        finalCost = NumberUtil.div(tradePrice, monthDays)
                                              .multiply(BigDecimal.valueOf(offDay));
                    }
                    inquiryPriceResponse.setExpiredUsedAmount(inquiryPriceResponse.getExpiredUsedAmount().add(finalCost));
                    priceDetail.setExpiredUsedAmount(finalCost);
                    priceDetail.setStartTime(priceDetail.getEndTime());
                    priceDetail.setEndTime(DateUtils.addDays(realEndTime, offDay));
                });
                inquiryPriceResponse.setExpiredUsedAmount(
                        inquiryPriceResponse.getExpiredUsedAmount().setScale(5, BigDecimal.ROUND_HALF_UP)
                );

                //补扣天数大于或等于资源到期时间到续订时间的天数
                Integer renewOffDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(realEndTime, productInfo.getNow(), true);
                if (offDay >= renewOffDay) {
                    productInfo.setRenewStartTime(DateUtils.addDays(realEndTime, offDay));
                } else {
                    productInfo.setRenewStartTime(productInfo.getCurrentDate());
                }
            }
        } else {
            productInfo.setRenewStartTime(realEndTime);
        }
        return inquiryPriceResponse;
    }

    /**
     * 是否在补偿时间内
     * @param resourceId
     * @param now
     */
    private boolean isCompensation(String resourceId, Date now) {
        Integer count = sfProductResourceCompensationService.lambdaQuery()
                                                            .eq(SfProductResourceCompensation::getSfProductResourceId, resourceId)
                                                            .le(SfProductResourceCompensation::getStartTime, now)
                                                            .ge(SfProductResourceCompensation::getEndTime, now)
                                                            .count();
        return count > 0;
    }

    @Override
    public List<InquiryPriceResponse> inquiryRenewPriceList(InquiryRenewPriceRequestDTO priceRequest) {

        List<InquiryPriceResponse> list = new ArrayList<>();
        String id = priceRequest.getId();

        QueryWrapper<SfProductResource> tWrapper = new QueryWrapper<>();
        tWrapper.eq("id", id);
        SfProductResource sfProductResource = sfProductResourceService.getOne(tWrapper);
        if (ObjectUtils.isEmpty(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        //查询集群扩容相关的合同
        List<HpcBizContractDTO> hpcBizContractDTOS = serviceOrderService.getHpcBizContractDTOList(sfProductResource.getClusterId());
        //获取资源对应的最新的订单详情
        Criteria criteria = new Criteria();
        criteria.put("refInstanceIdLike", "\"" + id + "\"");
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew"));
        criteria.put("orderStatus", "completed");
        ArrayList<String> productCodeIn = Lists.newArrayList(ProductCodeEnum.BMS.getProductType(), ProductCodeEnum.HPC_DRP.getProductType(),
                                                             ProductCodeEnum.ECS.getProductType(), ProductCodeEnum.SFS2.getProductType());
        criteria.put("productCodeIn", productCodeIn);
        criteria.put("isValid", true);
        List<ServiceOrderDetail> serviceOrderDetails = iServiceOrderPriceDetailService.selectOrderDetailByCriteria(criteria);
        if (CollectionUtil.isEmpty(serviceOrderDetails)) {
            return list;
        }
        ServiceOrderDetail lastOrderDetail = CollectionUtil.getFirst(serviceOrderDetails);
        Long orderId = lastOrderDetail.getOrderId();
        List<ServiceOrderDetail> lastOrderDetailList = serviceOrderDetails.stream()
                                                                          .filter(od -> orderId.equals(od.getOrderId()))
                                                                          .collect(Collectors.toList());
        Long couponId = null;
        boolean isHpcDrp = isHpcDrpFromDetail(lastOrderDetailList);
        for (ServiceOrderDetail serviceOrderDetail : lastOrderDetailList) {
            String serviceType = serviceOrderDetail.getServiceType();
            priceRequest.setServiceId(serviceOrderDetail.getServiceId().toString());
            priceRequest.setProductCode(serviceType);
            //产品ID
            ProductInfo productInfo = resRenewRefService.buildProductInfo(serviceOrderDetail);
            resRenewRefService.changeNodeNum(sfProductResource, serviceOrderDetail, productInfo, false, hpcBizContractDTOS);
            Integer quantity = productInfo.getQuantity();
            if (quantity == 0) {
                continue;
            }
            productInfo.setStatus(sfProductResource.getStatus());
            productInfo.setResourceId(id);
            productInfo.setFrozenTime(sfProductResource.getFrozenTime());
            productInfo.setMakeUpOrderDetailId(serviceOrderDetail.getId());

            // HPC专属资源池优惠卷特殊处理
            if (isHpcDrp && Objects.isNull(couponId)) {
                couponId = priceRequest.getCouponId();
                priceRequest.setCouponId(null);
            }
            list.add(getInquiryPriceResponse(priceRequest, productInfo));
        }
        // HPC专属资源池汇总
        if (isHpcDrp) {
            if (Objects.nonNull(couponId)) {
                // 多产品优惠卷分摊
                multipleProductCouponPrice(list, bizCouponMapper.selectById(couponId).getDiscountAmount());
            }
            // 同类型产品汇总
            summaryProductPrice(list);
        }
        return list;
    }

    @Override
    public UnsubscribeInquiryPriceVO unsubInquiryAIPrice(String id) {
        UnsubscribeInquiryPriceVO response = new UnsubscribeInquiryPriceVO();
        SfProductResource sfProductResource = sfProductResourceService.getById(id);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(sfProductResource.getProductType());
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
        if (Objects.isNull(authUserInfo.getOrgSid()) && !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<EntityDTO> serviceTypes = serviceCategoryService.getByServiceType(sfProductResource.getProductType());
        if (Constants.BSS.equals(authUser.getRemark()) && CollectionUtil.isNotEmpty(serviceTypes) && !serviceTypes.get(0)
                                                                                                                  .getEntityId()
                                                                                                                  .equals(authUser.getEntityId())) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        if (Objects.isNull(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1493182351));
        }
        //部署上线走以前的询价
        if (ApplyTypeEnum.MA_VERSION_DEPONLINE.getType().equals(String.valueOf(sfProductResource.getMaVersion()))) {
            response = getDepline(id, sfProductResource, response);
        } else {
            //查询开通的订单
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + id + "\"");
            param.put("orderTypes", Lists.newArrayList("apply"));
            param.put("orderStatus", "completed");
            param.put("productCode", sfProductResource.getProductType());
            List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                    .selectOrderInfoByParam(param);
            ServiceOrderDetail serviceOrderDetail = new ServiceOrderDetail();
            if (orderPriceDetails.size() > 0) {
                //查询开通订单
                if (Objects.isNull(orderPriceDetails.get(0).getOrderDetailId())) {
                    List<ServiceOrderDetail> serviceOrderDetails = serviceOrderMapper.selectOrderDetailByClusterId(sfProductResource.getProductType(),
                                                                                                                   sfProductResource.getClusterId());
                    serviceOrderDetail = serviceOrderDetails.get(0);
                } else {
                    serviceOrderDetail = serviceOrderDetailService
                            .getById(orderPriceDetails.get(0).getOrderDetailId());
                }
            } else {
                List<ServiceOrderDetail> serviceOrderDetails = serviceOrderMapper.selectOrderDetailByClusterId(sfProductResource.getProductType(),
                                                                                                               sfProductResource.getClusterId());
                if (CollectionUtils.isEmpty(serviceOrderDetails)) {
                    serviceOrderDetails = serviceOrderMapper.selectDetailByResId(String.valueOf(sfProductResource.getId()));
                }
                serviceOrderDetail = serviceOrderDetails.get(0);
            }
            //开发训练走新的询价方式
            response = getTrain(id, sfProductResource, response, serviceOrderDetail);
        }
        return response;
    }


    /**
     * 部署上线询价逻辑
     *
     * @param id
     * @param sfProductResource
     * @param response
     *
     * @return UnsubscribeInquiryPriceVO
     */
    private UnsubscribeInquiryPriceVO getDepline(String id,
                                                 SfProductResource sfProductResource,
                                                 UnsubscribeInquiryPriceVO response) {
        // 查询订购订单
        ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getResourceId, sfProductResource.getId())
                        .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType()));
        if (ObjectUtils.isEmpty(serviceOrderResourceRef)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        //查询开通订单
        ServiceOrderDetail serviceOrderDetail = serviceOrderDetailService
                .getById(serviceOrderResourceRef.getOrderDetailId());

        // 查询续订订单
        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + id + "\"");
        param.put("orderTypes", Lists.newArrayList("renew"));
        param.put("orderStatus", "completed");
        param.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfo(param);
        // 实付金额
        ResourceInquiryPriceVO payAmount = computePayPrice(serviceOrderDetail, orderPriceDetails);
        // 直接用退订询价接口
        UnsubInquiryPriceVO unsubInquiryPriceVO = new UnsubInquiryPriceVO();
        this.computeInnerProject(serviceOrderDetail, id, unsubInquiryPriceVO);
        // 已使用的金额
        ResourcePayPriceVO usedAmount = computeUsedPrice(payAmount, serviceOrderDetail, sfProductResource);
        // 退订金额
        ResourcePayPriceVO unsubAmount = new ResourcePayPriceVO();
        unsubAmount.setCashAmount(NumberUtil.sub(payAmount.getCashAmount(),
                                                 usedAmount.getCashAmount().setScale(3, BigDecimal.ROUND_HALF_UP))
                                            .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCreditAmount(NumberUtil.sub(payAmount.getCreditAmount(),
                                                   usedAmount.getCreditAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCouponAmount(NumberUtil.sub(payAmount.getCouponAmount(),
                                                   usedAmount.getCouponAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setAmount(unsubAmount.getCashAmount());

        response.setPayAmount(payAmount);
        response.setUsedAmount(usedAmount);
        response.setUnsubscribeAmount(unsubAmount);
        response.setExpiredUsedAmount(
                unsubInquiryPriceVO.getUnsubAmount().compareTo(BigDecimal.ZERO) < 0
                        ? unsubInquiryPriceVO.getUnsubAmount().abs()
                        : BigDecimal.ZERO);
        return response;
    }

    /**
     * 退订询价查询实付金额
     */
    private ResourceInquiryPriceVO computePayPrice(ServiceOrderDetail serviceOrderDetail,
                                                   List<ServiceOrderPriceDetail> renewOrderPriceDetails) {
        Query query = new Query();
        String orderId = serviceOrderDetail.getOrderId().toString();
        org.springframework.data.mongodb.core.query.Criteria c = new org.springframework.data.mongodb.core.query.Criteria();
        c.and("orderId").is(orderId);

        if (ProductCodeEnum.RS_BMS.getProductType().equals(serviceOrderDetail.getServiceType())) {
            Long productResourceId = JSON.parseObject(serviceOrderDetail.getServiceConfig()).getLong("productResourceId");
            if (Objects.nonNull(productResourceId)) {
                c.and("instanceId").is(productResourceId.toString());
            }
        }
        query.addCriteria(c);
        List<InstanceGaapCost> costs = mongoTemplate.find(query, InstanceGaapCost.class, "biz_bill_usage_item");

        //通过资源id查询订单
        ResourceInquiryPriceVO payAmount = new ResourceInquiryPriceVO();
        ServiceOrder serviceOrder = serviceOrderMapper.selectById(serviceOrderDetail.getOrderId());
        BigDecimal originalCost;
        BigDecimal orgDiscount;
        BigDecimal couponDiscount;
        if (ProductCodeEnum.RS_BMS.getProductType().equals(serviceOrderDetail.getServiceType())) {
            List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectList(
                    new LambdaQueryWrapper<ServiceOrderPriceDetail>()
                            .eq(ServiceOrderPriceDetail::getOrderDetailId, serviceOrderDetail.getId()));
            ServiceOrderPriceDetail serviceOrderPriceDetail = serviceOrderPriceDetails.get(0);
            originalCost = scaleThree(serviceOrderPriceDetail.getOriginalCost().multiply(BigDecimal.valueOf(serviceOrderDetail.getDuration())));
            orgDiscount = scaleThree(serviceOrderPriceDetail.getDiscount());
            couponDiscount = scaleThree(serviceOrderPriceDetail.getCouponAmount());
        }else {
            originalCost = scaleThree(serviceOrder.getOriginalCost());
            orgDiscount = scaleThree(serviceOrder.getOrgDiscount());
            couponDiscount = scaleThree(serviceOrder.getCouponDiscount());
        }
        ResourcePayPriceVO resourcePayPriceVO = getPaymentField(costs);
        payAmount.setCashAmount(resourcePayPriceVO.getCashAmount());
        payAmount.setCreditAmount(resourcePayPriceVO.getCreditAmount());
        payAmount.setCouponAmount(resourcePayPriceVO.getCouponAmount());
        if (ProductCodeEnum.ECS.getProductType().equals(serviceOrderDetail.getServiceType())) {
            payAmount.setCashAmount(resourcePayPriceVO.getCashAmount().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
            payAmount.setCreditAmount(resourcePayPriceVO.getCreditAmount().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
            payAmount.setCouponAmount(resourcePayPriceVO.getCouponAmount().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
            originalCost = scaleThree(serviceOrder.getOriginalCost().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
            orgDiscount = scaleThree(serviceOrder.getOrgDiscount().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
            couponDiscount = scaleThree(serviceOrder.getCouponDiscount().divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity())));
        }

        if (StringUtil.equals("apply", serviceOrder.getType()) && CollectionUtil.isEmpty(renewOrderPriceDetails)) {
            payAmount.setOriginalCost(originalCost);
            payAmount.setOrgDiscount(orgDiscount);
            payAmount.setCouponDiscount(couponDiscount);
            return payAmount;
        }
        //处理续订的订单

        for (int i = 0; i < renewOrderPriceDetails.size(); i++) {
            if ("renew".equals(renewOrderPriceDetails.get(i).getOrderType())) {
                //处理续订的账单
                String orderIds = renewOrderPriceDetails.get(i).getOrderId().toString();
                org.springframework.data.mongodb.core.query.Criteria c1 = new org.springframework.data.mongodb.core.query.Criteria();
                c1.and("orderId").is(orderIds);
                List<InstanceGaapCost> renewCosts = mongoTemplate
                        .find(Query.query(c1), InstanceGaapCost.class, "biz_bill_usage_item");

                couponDiscount = couponDiscount.add(scaleThree(renewOrderPriceDetails.get(i).getCouponAmount().
                        multiply(BigDecimal.valueOf(renewOrderPriceDetails.get(i).getQuantity()))));
                orgDiscount = orgDiscount.add(scaleThree(renewOrderPriceDetails.get(i).getOrgDiscount()));
                originalCost = originalCost.add(scaleThree(renewOrderPriceDetails.get(i).getOriginalCost()));
                ResourcePayPriceVO renewPayPrice = getPaymentField(renewCosts);
                payAmount.setCashAmount(payAmount.getCashAmount().add(renewPayPrice.getCashAmount()));
                payAmount.setCreditAmount(payAmount.getCreditAmount().add(renewPayPrice.getCreditAmount()));
                payAmount.setCouponAmount(payAmount.getCouponAmount().add(renewPayPrice.getCouponAmount()));
            }
        }
        renewOrderPriceDetails.removeIf(serviceOrderPriceDetail -> "renew".equals(serviceOrderPriceDetail.getOrderType()));

        //查询扩缩容帐单
        List<String> orderIdss = renewOrderPriceDetails.stream()
                                                       .map(t -> t.getOrderId().toString())
                                                       .collect(Collectors.toList());
        List<String> orderIdLists = new ArrayList<>();
        orderIdLists.addAll(orderIdss);
        org.springframework.data.mongodb.core.query.Criteria criteria2 = org.springframework.data.mongodb.core.query.Criteria
                .where("orderId").in(orderIdLists);
        List<InstanceGaapCost> reCosts = mongoTemplate
                .find(Query.query(criteria2), InstanceGaapCost.class, "biz_bill_usage_item");
        //只有资源开通订单的实付金额
        if (CollectionUtil.isNotEmpty(renewOrderPriceDetails)) {

            //存在续订单
            BigDecimal renewOriginalCost = renewOrderPriceDetails.stream()
                                                                 .map(ServiceOrderPriceDetail::getOriginalCost)
                                                                 .filter(Objects::nonNull)
                                                                 .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal renewOrgDiscount = renewOrderPriceDetails.stream()
                                                                .map(ServiceOrderPriceDetail::getOrgDiscount)
                                                                .filter(Objects::nonNull)
                                                                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal renewCouponDiscount = renewOrderPriceDetails.stream()
                                                                   .map(ServiceOrderPriceDetail::getCouponAmount)
                                                                   .filter(Objects::nonNull)
                                                                   .reduce(BigDecimal.ZERO, BigDecimal::add);

            payAmount.setOriginalCost(originalCost.add(renewOriginalCost));
            payAmount.setOrgDiscount(orgDiscount.add(renewOrgDiscount));
            payAmount.setCouponDiscount(couponDiscount.add(renewCouponDiscount));
            ResourcePayPriceVO renewPayPrice = getPaymentField(reCosts);
            payAmount.setCashAmount(payAmount.getCashAmount().add(renewPayPrice.getCashAmount()));
            payAmount.setCreditAmount(payAmount.getCreditAmount().add(renewPayPrice.getCreditAmount()));
            payAmount.setCouponAmount(payAmount.getCouponAmount().add(renewPayPrice.getCouponAmount()));
        } else {
            payAmount.setOriginalCost(originalCost);
            payAmount.setOrgDiscount(orgDiscount);
            payAmount.setCouponDiscount(couponDiscount);
            payAmount.setCreditAmount(payAmount.getCreditAmount());
            payAmount.setCouponAmount(payAmount.getCouponAmount());
            payAmount.setCashAmount(payAmount.getCashAmount());
        }
        return payAmount;
    }

    /**
     * 退订查询已使用的金额
     */
    private ResourcePayPriceVO computeUsedPrice(ResourceInquiryPriceVO payAmount,
                                                ServiceOrderDetail serviceOrderDetail,
                                                SfProductResource sfProductResource) {

        // 订单询价，DRP退订询价，只算现金支付，amount含有信用额度及充值现金券金额
        Criteria criteria = new Criteria();
        criteria.put("orderDetailIdIn", Lists.newArrayList(serviceOrderDetail.getId()));
        List<ServiceOrderPriceDetail> appleOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(
                criteria);

        BigDecimal applyCouponAmount = BigDecimal.ZERO;
        BigDecimal applyCreditAmount = BigDecimal.ZERO;
        BigDecimal applyCashAmount = BigDecimal.ZERO;
        BigDecimal usedCouponAmount;
        BigDecimal usedCreditAmount;
        BigDecimal usedCashAmount;

        if (CollectionUtil.isNotEmpty(appleOrderPriceDetails)) {
            applyCouponAmount = appleOrderPriceDetails.stream()
                                                      .map(ServiceOrderPriceDetail::getPayBalanceCash)
                                                      .reduce(BigDecimal::add)
                                                      .orElse(BigDecimal.ZERO);
            applyCreditAmount = appleOrderPriceDetails.stream()
                                                      .map(ServiceOrderPriceDetail::getPayCreditLine)
                                                      .reduce(BigDecimal::add)
                                                      .orElse(BigDecimal.ZERO);
            applyCashAmount = appleOrderPriceDetails.stream()
                                                    .map(ServiceOrderPriceDetail::getPayBalance)
                                                    .reduce(BigDecimal::add)
                                                    .orElse(BigDecimal.ZERO);
            if (ProductCodeEnum.ECS.getProductType().equals(serviceOrderDetail.getServiceType())) {
                applyCouponAmount.divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity()));
                applyCreditAmount.divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity()));
                applyCashAmount.divide(BigDecimal.valueOf(serviceOrderDetail.getQuantity()));
            }
        }

        usedCouponAmount = applyCouponAmount;
        usedCreditAmount = applyCreditAmount;
        usedCashAmount = applyCashAmount;

        Date resEndTime = Objects.isNull(sfProductResource.getEndTime()) ? new Date() : sfProductResource.getEndTime();
        Date startTime = serviceOrderDetail.getStartTime();
        Date endTime = Optional.ofNullable(serviceOrderDetail.getEndTime()).orElse(new Date());
        Date currentDate = new Date();
        if (resEndTime.after(currentDate)) {
            Date st = cn.hutool.core.date.DateUtil.date(startTime);
            while (st.before(currentDate)) {
                st = cn.hutool.core.date.DateUtil.offsetMonth(st, 1);
            }
            currentDate = st.after(resEndTime) ? resEndTime : st;
        }

        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + sfProductResource.getId() + "\"");
        param.put("types", Lists.newArrayList("renew", "upgrade", "degrade", "modify"));
        param.put("orderStatus", "completed");
        param.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfo(param);
        Integer duration = serviceOrderDetail.getDuration();
        if ("0".equals(String.valueOf(duration))) {
            duration = 1;
        }
        if (!endTime.before(currentDate)) {
            long offMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, currentDate);
            usedCouponAmount = NumberUtil.div(usedCouponAmount.multiply(BigDecimal.valueOf(offMonths)),
                                              duration);
            usedCreditAmount = NumberUtil.div(usedCreditAmount.multiply(BigDecimal.valueOf(offMonths)),
                                              duration);
            usedCashAmount = NumberUtil.div(usedCashAmount.multiply(BigDecimal.valueOf(offMonths)),
                                            duration);
        } else {
            if (CollectionUtil.isNotEmpty(orderPriceDetails)) {
                for (ServiceOrderPriceDetail renewOrderPriceDetail : orderPriceDetails) {
                    //操作续订的金额
                    if ("renew".equals(renewOrderPriceDetail.getOrderType())) {
                        Date renewEndTime = renewOrderPriceDetail.getEndTime();
                        Date renewStartTime = renewOrderPriceDetail.getStartTime();
                        BigDecimal payBalanceCash = renewOrderPriceDetail.getPayBalanceCash();
                        BigDecimal payCreditLine = renewOrderPriceDetail.getPayCreditLine();
                        BigDecimal payCashAmount = renewOrderPriceDetail.getPayBalance();
                        log.info("payCashAmount..{}", payCashAmount.toString());
                        if (renewEndTime.before(currentDate)) {
                            usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                            usedCreditAmount = usedCreditAmount.add(payCreditLine);
                            usedCashAmount = usedCashAmount.add(payCashAmount);
                        } else if (renewStartTime.before(currentDate)) {
                            long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime, renewEndTime);
                            long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime, currentDate);

                            //如果未到续订服务开始时间，整月的购买费用退还
                            if (renewStartTime.before(new Date()) && totalMonth > 0) {
                                usedCouponAmount = usedCouponAmount.add(
                                        NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)),
                                                       totalMonth));
                                usedCreditAmount = usedCreditAmount.add(
                                        NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)),
                                                       totalMonth));
                                usedCashAmount = usedCashAmount.add(
                                        NumberUtil.div(payCashAmount.multiply(BigDecimal.valueOf(offsetMonth)),
                                                       totalMonth));
                            }
                        }
                        //续订询价存在补扣
                        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectOrderDetailByType(
                                renewOrderPriceDetail.getOrderSn(), "expired");
                        if (serviceOrderPriceDetails.size() > 0) {
                            for (ServiceOrderPriceDetail expiredOrderPriceDetail : serviceOrderPriceDetails) {
                                if ("expired".equals(expiredOrderPriceDetail.getType())) {
                                    usedCouponAmount = usedCouponAmount.add(expiredOrderPriceDetail.getPayBalanceCash());
                                    usedCreditAmount = usedCreditAmount.add(expiredOrderPriceDetail.getPayCreditLine());
                                    usedCashAmount = usedCashAmount.add(expiredOrderPriceDetail.getPayBalance());
                                }
                            }
                        }
                    }
                }
            } else {
                usedCouponAmount = payAmount.getCouponAmount();
                usedCreditAmount = payAmount.getCreditAmount();
                usedCashAmount = payAmount.getCashAmount();
            }
        }
        //扩容操作
        for (ServiceOrderPriceDetail renewOrderPriceDetail : orderPriceDetails) {
            if ("upgrade".equals(renewOrderPriceDetail.getOrderType())) {
                Date upgradeEndTime = renewOrderPriceDetail.getEndTime();
                Date upgradeStartTime = renewOrderPriceDetail.getStartTime();
                BigDecimal payBalanceCash = renewOrderPriceDetail.getPayBalanceCash();
                BigDecimal payCreditLine = renewOrderPriceDetail.getPayCreditLine();
                BigDecimal payCashAmount = renewOrderPriceDetail.getPayBalance();
                log.info("payCashAmount..{}", payCashAmount.toString());
                if (upgradeEndTime.before(currentDate)) {
                    usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                    usedCreditAmount = usedCreditAmount.add(payCreditLine);
                    usedCashAmount = usedCashAmount.add(payCashAmount);
                } else if (upgradeStartTime.before(currentDate)) {
                    long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, upgradeEndTime);
                    long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, currentDate);

                    if (totalMonth == 0L) {
                        totalMonth = 1L;
                    }
                    //如果未到续订服务开始时间，整月的购买费用退还
                    if (upgradeStartTime.before(new Date())) {
                        usedCouponAmount = usedCouponAmount.add(
                                NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCreditAmount = usedCreditAmount.add(
                                NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCashAmount = usedCashAmount.add(
                                NumberUtil.div(payCashAmount.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                    }
                }
            } else if ("modify".equals(renewOrderPriceDetail.getOrderType())) {
                Date upgradeEndTime = renewOrderPriceDetail.getEndTime();
                Date upgradeStartTime = renewOrderPriceDetail.getStartTime();
                BigDecimal payBalanceCash = renewOrderPriceDetail.getPayBalanceCash();
                BigDecimal payCreditLine = renewOrderPriceDetail.getPayCreditLine();
                BigDecimal payCashAmount = renewOrderPriceDetail.getPayBalance();
                log.info("payCashAmount..{}", payCashAmount.toString());
                if (upgradeEndTime.before(currentDate)) {
                    usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                    usedCreditAmount = usedCreditAmount.add(payCreditLine);
                    usedCashAmount = usedCashAmount.add(payCashAmount);
                } else if (upgradeStartTime.before(currentDate)) {
                    long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, upgradeEndTime);
                    long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, currentDate);

                    if (totalMonth == 0L) {
                        totalMonth = 1L;
                    }
                    //如果未到续订服务开始时间，整月的购买费用退还
                    if (upgradeStartTime.before(new Date())) {
                        usedCouponAmount = usedCouponAmount.add(
                                NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCreditAmount = usedCreditAmount.add(
                                NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCashAmount = usedCashAmount.add(
                                NumberUtil.div(payCashAmount.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        if (renewOrderPriceDetail.getPayBalance().compareTo(BigDecimal.ZERO) < 0) {
                            BigDecimal pay = renewOrderPriceDetail.getPayBalance();
                            BigDecimal tradePrice = renewOrderPriceDetail.getTradePrice();
                            while (pay.compareTo(BigDecimal.ZERO) < 0) {
                                pay = pay.add(tradePrice);
                            }
                            usedCashAmount = usedCashAmount.add(NumberUtil.div(pay.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                        }
                    }
                }
            }
        }
        //缩容操作
        for (ServiceOrderPriceDetail renewOrderPriceDetail : orderPriceDetails) {
            if ("degrade".equals(renewOrderPriceDetail.getOrderType())) {
                Date upgradeEndTime = renewOrderPriceDetail.getEndTime();
                Date upgradeStartTime = renewOrderPriceDetail.getStartTime();
                //判断缩容是否计费,如果在同一个计费周期里面，不计算缩容
                Date currDate = new Date();
                long degradeMonth = monthCompare(startTime, upgradeStartTime);
                log.info("..,{}..,{},...{}", DateUtil.dateFormat(startTime),
                         DateUtil.dateFormat(upgradeStartTime), DateUtil.dateFormat(currDate));
                long currentMonth = monthCompare(startTime, currDate);
                if (currentMonth - degradeMonth == 0) {
                    continue;
                }
                BigDecimal payBalanceCash = renewOrderPriceDetail.getPayBalanceCash();
                BigDecimal payCreditLine = renewOrderPriceDetail.getPayCreditLine();
                BigDecimal payCashAmount = renewOrderPriceDetail.getPayBalance();
                log.info("payCashAmount..{}", payCashAmount.toString());
                if (upgradeEndTime.before(currentDate)) {
                    usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                    usedCreditAmount = usedCreditAmount.add(payCreditLine);
                    usedCashAmount = usedCashAmount.add(payCashAmount);
                } else if (upgradeStartTime.before(currentDate)) {
                    upgradeStartTime = cn.hutool.core.date.DateUtil.offsetMonth(upgradeStartTime, 1);
                    long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, upgradeEndTime);
                    if (totalMonth == 0) {
                        totalMonth = 1;
                    }
                    log.info("..{},..{}", DateUtil.dateFormat(currentDate), DateUtil.dateFormat(upgradeEndTime));
                    long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, currentDate);

                    log.info("..{},..{}", DateUtil.dateFormat(currentDate), DateUtil.dateFormat(upgradeStartTime));
                    //如果未到续订服务开始时间，整月的购买费用退还
                    if (upgradeStartTime.before(new Date())) {
                        usedCouponAmount = usedCouponAmount.add(
                                NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCreditAmount = usedCreditAmount.add(
                                NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                        usedCashAmount = usedCashAmount.add(
                                NumberUtil.div(payCashAmount.multiply(BigDecimal.valueOf(offsetMonth)),
                                               totalMonth));
                    }
                }
            }
        }
        ResourcePayPriceVO resourcePayPriceVO = new ResourcePayPriceVO();
        resourcePayPriceVO.setCashAmount(usedCashAmount.setScale(3, BigDecimal.ROUND_HALF_UP));
        resourcePayPriceVO.setCouponAmount(usedCouponAmount.setScale(3, BigDecimal.ROUND_HALF_UP));
        resourcePayPriceVO.setCreditAmount(usedCreditAmount.setScale(3, BigDecimal.ROUND_HALF_UP));
        BigDecimal amount = usedCouponAmount.add(usedCreditAmount).add(resourcePayPriceVO.getCashAmount());
        resourcePayPriceVO.setAmount(amount.setScale(3, BigDecimal.ROUND_HALF_UP));
        return resourcePayPriceVO;
    }


    private long monthCompare(Date fromDate, Date toDate) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(fromDate);
        c2.setTime(toDate);
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 || month1 == month2 && day1 < day2) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) {
            monthInterval--;
        }
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
        return monthsDiff;
    }


    /**
     * 开发训练询价
     *
     * @param id
     * @param sfProductResource
     * @param response
     * @param serviceOrderDetail
     *
     * @return UnsubscribeInquiryPriceVO
     */
    private UnsubscribeInquiryPriceVO getTrain(String id,
                                               SfProductResource sfProductResource,
                                               UnsubscribeInquiryPriceVO response,
                                               ServiceOrderDetail serviceOrderDetail) {
        // 查询所有订单
        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + id + "\"");
        param.put("types", Lists.newArrayList("renew", "degrade", "upgrade"));
        param.put("orderStatus", "completed");
        param.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfo(param);
        //查询开通订单
        // 实付金额
        ResourceInquiryPriceVO payAmount = computePayPrice(serviceOrderDetail, orderPriceDetails);
        // 直接用退订询价接口
        UnsubInquiryPriceVO unsubInquiryPriceVO = new UnsubInquiryPriceVO();
        this.computeInnerProject(serviceOrderDetail, id, unsubInquiryPriceVO);
        // 已使用的金额
        ResourcePayPriceVO usedAmount = computeUsedPrice(payAmount, serviceOrderDetail, sfProductResource);
        // 退订金额
        ResourcePayPriceVO unsubAmount = new ResourcePayPriceVO();
        unsubAmount.setCashAmount(NumberUtil.sub(payAmount.getCashAmount(),
                                                 usedAmount.getCashAmount().setScale(5, BigDecimal.ROUND_HALF_UP))
                                            .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCreditAmount(NumberUtil.sub(payAmount.getCreditAmount(),
                                                   usedAmount.getCreditAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCouponAmount(NumberUtil.sub(payAmount.getCouponAmount(),
                                                   usedAmount.getCouponAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setAmount(unsubAmount.getCashAmount());
        response.setPayAmount(payAmount);
        response.setUsedAmount(usedAmount);
        response.setUnsubscribeAmount(unsubAmount);
        response.setExpiredUsedAmount(
                unsubInquiryPriceVO.getUnsubAmount().compareTo(BigDecimal.ZERO) < 0
                        ? unsubInquiryPriceVO.getUnsubAmount().abs()
                        : BigDecimal.ZERO);
        return response;
    }

    /**
     * 退订查询已使用的金额
     */
    private ResourcePayPriceVO computeUsedPrice(UnsubInquiryPriceVO unsubInquiryPriceVO,
                                                ResourceInquiryPriceVO payAmount,
                                                ServiceOrderDetail serviceOrderDetail,
                                                SfProductResource sfProductResource) {

        // 订单询价，DRP退订询价，只算现金支付，amount含有信用额度及充值现金券金额
        Criteria criteria = new Criteria();
        criteria.put("orderDetailIdIn", Lists.newArrayList(serviceOrderDetail.getId()));
        List<ServiceOrderPriceDetail> appleOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(
                criteria);

        BigDecimal applyCouponAmount = BigDecimal.ZERO;
        BigDecimal applyCreditAmount = BigDecimal.ZERO;
        BigDecimal usedCouponAmount;
        BigDecimal usedCreditAmount;

        if (CollectionUtil.isNotEmpty(appleOrderPriceDetails)) {
            applyCouponAmount = appleOrderPriceDetails.stream()
                                                      .map(ServiceOrderPriceDetail::getPayBalanceCash)
                                                      .reduce(BigDecimal::add)
                                                      .orElse(BigDecimal.ZERO);
            applyCreditAmount = appleOrderPriceDetails.stream()
                                                      .map(ServiceOrderPriceDetail::getPayCreditLine)
                                                      .reduce(BigDecimal::add)
                                                      .orElse(BigDecimal.ZERO);
        }

        // 续订询价`
        //查询续订订单
        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + sfProductResource.getId() + "\"");
        param.put("orderTypes", Lists.newArrayList("renew"));
        param.put("orderStatus", "completed");
        param.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> renewOrderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfo(param);

        usedCouponAmount = applyCouponAmount;
        usedCreditAmount = applyCreditAmount;

        Date resEndTime = Objects.isNull(sfProductResource.getEndTime()) ? new Date() : sfProductResource.getEndTime();
        Date startTime = serviceOrderDetail.getStartTime();
        Date endTime = Optional.ofNullable(serviceOrderDetail.getEndTime()).orElse(new Date());
        Date currentDate = new Date();
        if (resEndTime.after(currentDate)) {
            Date st = cn.hutool.core.date.DateUtil.date(startTime);
            while (st.before(currentDate)) {
                st = cn.hutool.core.date.DateUtil.offsetMonth(st, 1);
            }
            currentDate = st.after(resEndTime) ? resEndTime : st;
        }

        if (!endTime.before(currentDate)) {
            long offMonths = cn.hutool.core.date.DateUtil.betweenMonth(startTime, currentDate, true);
            usedCouponAmount = NumberUtil.div(applyCouponAmount.multiply(BigDecimal.valueOf(offMonths)),
                                              serviceOrderDetail.getDuration());
            usedCreditAmount = NumberUtil.div(applyCreditAmount.multiply(BigDecimal.valueOf(offMonths)),
                                              serviceOrderDetail.getDuration());
        } else {
            if (CollectionUtil.isNotEmpty(renewOrderPriceDetails)) {
                for (ServiceOrderPriceDetail renewOrderPriceDetail : renewOrderPriceDetails) {
                    Date renewEndTime = renewOrderPriceDetail.getEndTime();
                    Date renewStartTime = renewOrderPriceDetail.getStartTime();
                    BigDecimal payBalanceCash = renewOrderPriceDetail.getPayBalanceCash();
                    BigDecimal payCreditLine = renewOrderPriceDetail.getPayCreditLine();

                    if (renewEndTime.before(currentDate)) {
                        usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                        usedCreditAmount = usedCreditAmount.add(payCreditLine);
                    } else if (renewStartTime.before(currentDate)) {
                        long totalMonth = cn.hutool.core.date.DateUtil.betweenMonth(renewStartTime, renewEndTime, true);
                        long offsetMonth = cn.hutool.core.date.DateUtil.betweenMonth(renewStartTime, currentDate, true);

                        //如果未到续订服务开始时间，整月的购买费用退还
                        if (renewStartTime.before(new Date())) {
                            usedCouponAmount = usedCouponAmount.add(
                                    NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)),
                                                   totalMonth));
                            usedCreditAmount = usedCreditAmount.add(
                                    NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)),
                                                   totalMonth));
                        }
                    }
                }
            } else {
                usedCouponAmount = payAmount.getCouponAmount();
                usedCreditAmount = payAmount.getCreditAmount();
            }
        }
        ResourcePayPriceVO resourcePayPriceVO = new ResourcePayPriceVO();
        resourcePayPriceVO.setCashAmount(
                unsubInquiryPriceVO.getUsedAmount().compareTo(unsubInquiryPriceVO.getTotalPayment()) > 0 ? unsubInquiryPriceVO.getTotalPayment()
                        : unsubInquiryPriceVO.getUsedAmount());
        resourcePayPriceVO.setCouponAmount(usedCouponAmount);
        resourcePayPriceVO.setCreditAmount(usedCreditAmount);
        resourcePayPriceVO.setAmount(usedCouponAmount.add(usedCreditAmount).add(resourcePayPriceVO.getCashAmount()));
        return resourcePayPriceVO;
    }

    private BigDecimal scaleThree(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(5, BigDecimal.ROUND_HALF_UP);
    }

    private ResourcePayPriceVO getPaymentField(List<InstanceGaapCost> costs) {
        ResourcePayPriceVO amount = new ResourcePayPriceVO();
        if (costs.size() > 0) {
            if (costs.size() == 1) {
                BigDecimal creditAmount = scaleThree(costs.get(0).getCreditAmount());
                amount.setCreditAmount(creditAmount);
                BigDecimal couponAmount = scaleThree(costs.get(0).getCouponAmount());
                amount.setCouponAmount(couponAmount);
                BigDecimal cashAmount = scaleThree(costs.get(0).getCashAmount());
                amount.setCashAmount(cashAmount);
            } else {
                BigDecimal creditAmount = costs.stream()
                                               .filter(t -> t.getCreditAmount() != null)
                                               .map(InstanceGaapCost::getCreditAmount)
                                               .reduce(BigDecimal.ZERO, BigDecimal::add);
                amount.setCreditAmount(creditAmount);
                BigDecimal couponAmount = costs.stream()
                                               .filter(t -> t.getCouponAmount() != null)
                                               .map(InstanceGaapCost::getCouponAmount)
                                               .reduce(BigDecimal.ZERO, BigDecimal::add);
                amount.setCouponAmount(couponAmount);
                BigDecimal cashAmount = costs.stream()
                                             .filter(t -> t.getCashAmount() != null)
                                             .map(InstanceGaapCost::getCashAmount)
                                             .reduce(BigDecimal.ZERO, BigDecimal::add);
                amount.setCashAmount(cashAmount);
            }
        } else {
            amount.setCreditAmount(BigDecimal.ZERO);
            amount.setCouponAmount(BigDecimal.ZERO);
            amount.setCashAmount(BigDecimal.ZERO);
        }
        return amount;
    }

    @Override
    public void inquiryUnsubscribePriceList(String id, UnsubInquiryPriceVO vo) {
        SfProductResource hpcDrp = sfProductResourceService.getById(id);
        // apply
        List<ServiceOrderDetail> serviceOrderDetails = serviceOrderService.selectDetailByResId(id);
        if (CollectionUtil.isEmpty(serviceOrderDetails)) {
            return;
        }
        List<Long> applyDetailIds = serviceOrderDetails.stream().map(ServiceOrderDetail::getId)
                                                       .collect(Collectors.toList());
        Criteria criteria = new Criteria();
        criteria.put("orderDetailIdIn", applyDetailIds);
        List<ServiceOrderPriceDetail> applyPrice = serviceOrderPriceDetailMapper.selectByCriteria(
                criteria);
        // renew
        criteria.clear();
        criteria.put("refInstanceIdLike", "\"" + id + "\"");
        criteria.put("types", Lists.newArrayList("renew", "expired"));
        criteria.put("orderStatus", "completed");
        criteria.put("productCodeIn", Lists.newArrayList(ProductCodeEnum.HPC_DRP.getProductType(),
                                                         ProductCodeEnum.ECS.getProductType(), ProductCodeEnum.BMS.getProductType()));
        List<ServiceOrderPriceDetail> renewPrice = serviceOrderPriceDetailMapper.selectByCriteria(
                criteria);
        //通过OrderSn查询对应的弹性文件2.0的续订或过期单
        List<String> orderSnList = renewPrice.stream().map(ServiceOrderPriceDetail::getOrderSn).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(orderSnList)) {
            Criteria sfsCri = new Criteria();
            sfsCri.put("productCode", ProductCodeEnum.SFS2.getProductType());
            sfsCri.put("orderSnIn", orderSnList);
            List<ServiceOrderPriceDetail> sfs2PriceDetailList = serviceOrderPriceDetailMapper.selectByCriteria(
                    sfsCri);

            renewPrice.addAll(sfs2PriceDetailList);
        }

        List<ServiceOrderPriceDetail> allPrice = Lists.newArrayList();
        allPrice.addAll(applyPrice);
        allPrice.addAll(renewPrice);
        /*Map<String, List<ServiceOrderPriceDetail>> groupByProductCode = allPrice.stream()
                                                                                .collect(Collectors.groupingBy(
                                                                                        ServiceOrderPriceDetail::getResourceConfig));
        // serviceType对应productCode的Map
        Map<String, String> typeCodeMap = new HashMap<>();
        if (!ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(serviceOrderDetails.get(0).getApplyType())) {
            groupByProductCode = allPrice.stream()
                                         .collect(Collectors.groupingBy(ServiceOrderPriceDetail::getServiceType));
            typeCodeMap = applyPrice.stream()
                                    .collect(Collectors.toMap(ServiceOrderPriceDetail::getServiceType,
                                                              ServiceOrderPriceDetail::getProductCode));
        }*/
        Map<String, String> typeCodeMap = new HashMap<>();
        Map<String, List<ServiceOrderPriceDetail>> groupByProductCode = allPrice.stream()
                                                                                .collect(Collectors.groupingBy(
                                                                                        ServiceOrderPriceDetail::getResourceConfigDesc));
        typeCodeMap = applyPrice.stream()
                                .collect(Collectors.toMap(ServiceOrderPriceDetail::getResourceConfigDesc,
                                                          ServiceOrderPriceDetail::getProductCode));
        Date now = cn.hutool.core.date.DateUtil.date();
        Date endTime = hpcDrp.getEndTime();
        boolean isExpired = endTime.before(now);
        boolean nonFrozen = !"frozen".equalsIgnoreCase(hpcDrp.getStatus());
        Date usedTime = hpcDrp.getStartTime();

        while (usedTime.before(now)) {
            usedTime = cn.hutool.core.date.DateUtil.offsetMonth(usedTime, 1);
        }
        usedTime = usedTime.after(endTime) ? endTime : now;

        //当前订单补偿的天数
        Integer compenstionDays = this.statisticalCompensationDays(Long.valueOf(id), now, null);
        if (compenstionDays == null) {
            compenstionDays = 0;
        }
        List<ResourceDetailVO> details = Lists.newArrayList();
        for (Entry<String, List<ServiceOrderPriceDetail>> entry : groupByProductCode.entrySet()) {
           /* String code = entry.getKey();
            if(!ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(serviceOrderDetails.get(0).getApplyType())){
                code = typeCodeMap.get(entry.getKey());
            }*/
            String code = typeCodeMap.get(entry.getKey());
            //获取当前节点数据
            List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> nodeInfoList = getNodeInfos(hpcDrp.getClusterId(), code);
            List<ServiceOrderPriceDetail> price = entry.getValue();
            price.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
            ResourceDetailVO detailVO = new ResourceDetailVO();
            detailVO.init();
            // 该时间为退订时间点（为和之前业务兼容，所以没有修改字段名）
            detailVO.setComputeDate(now);
            // 该时间为原产品到期时间（为和之前业务兼容，所以没有修改字段名）
            detailVO.setNow(hpcDrp.getEndTime());
       /*     if (!ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(serviceOrderDetails.get(0).getApplyType())) {
                applyPrice.stream().filter(d -> Objects.equals(d.getServiceType(), entry.getKey()))
                          .findFirst().ifPresent(d -> {
                    detailVO.setOrderDetailId(d.getOrderDetailId());
                    detailVO.setProjectId(d.getOrgSid());
                    detailVO.setChargeType(d.getChargeType());
                });
            } else {
                serviceOrderDetails.stream().filter(d -> Objects.equals(d.getServiceType(), entry.getKey()))
                                   .findFirst().ifPresent(d -> {
                    detailVO.setOrderDetailId(d.getId());
                    detailVO.setProjectId(d.getOrgSid());
                    detailVO.setChargeType(d.getChargeType());
                });
            }*/
            applyPrice.stream().filter(d -> Objects.equals(d.getResourceConfigDesc(), entry.getKey()))
                      .findFirst().ifPresent(d -> {
                detailVO.setOrderDetailId(d.getOrderDetailId());
                detailVO.setProjectId(d.getOrgSid());
                detailVO.setChargeType(d.getChargeType());
            });
            if (code.equalsIgnoreCase(hpcDrp.getProductType())) {
                detailVO.setId(id);
            }
            detailVO.setProductCode(code);
            // 资源
            BigDecimal totalPayment = detailVO.getTotalPayment();
            BigDecimal totalOriginalCost = Convert.toBigDecimal(detailVO.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal totalOrgDiscount = Convert.toBigDecimal(detailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
            BigDecimal totalCouponDiscount = Convert.toBigDecimal(detailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal totalCouponAmount = Convert.toBigDecimal(detailVO.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal totalCashAmount = Convert.toBigDecimal(detailVO.getTotalCashAmount(), BigDecimal.ZERO);
            BigDecimal totalCreditAmount = Convert.toBigDecimal(detailVO.getTotalCreditAmount(), BigDecimal.ZERO);
            BigDecimal usedAmount = detailVO.getTotalUsedAmount();
            BigDecimal totalUsedCouponAmount = Convert.toBigDecimal(detailVO.getTotalUsedCouponAmount(), BigDecimal.ZERO);
            BigDecimal totalUsedCashAmount = Convert.toBigDecimal(detailVO.getTotalUsedCashAmount(), BigDecimal.ZERO);
            BigDecimal totalUsedCreditAmount = Convert.toBigDecimal(detailVO.getTotalUsedCreditAmount(), BigDecimal.ZERO);
            // 服务
            ServicePrice servicePrice = new ServicePrice();
            BigDecimal serviceTotalPayment = servicePrice.getTotalPayment();
            BigDecimal serviceOriginalCost = Convert.toBigDecimal(servicePrice.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal serviceOrgDiscount = Convert.toBigDecimal(servicePrice.getTotalOrgDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponDiscount = Convert.toBigDecimal(servicePrice.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponAmount = Convert.toBigDecimal(servicePrice.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceCashAmount = Convert.toBigDecimal(servicePrice.getTotalCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceCreditAmount = Convert.toBigDecimal(servicePrice.getTotalCreditAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedAmount = servicePrice.getTotalUsed();
            BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCreditAmount(), BigDecimal.ZERO);
            //下个订单开始时间集合
            // LinkedHashMap<Date,Date> nextPriceStartDateMap = new LinkedHashMap<>();
            // for (int i = 0; i < price.size()-1; i++) {
            //     ServiceOrderPriceDetail serviceOrderPriceDetail = price.get(i);
            //     if(!nextPriceStartDateMap.containsKey(serviceOrderPriceDetail.getEndTime()) &&
            //             !Objects.equals(serviceOrderPriceDetail.getOrderSn(),price.get(i+1).getOrderSn())){
            //         nextPriceStartDateMap.put(serviceOrderPriceDetail.getEndTime(),price.get(i+1).getStartTime());
            //     }
            // }
            //补偿天数集合
            // Map<Date,Integer> daysMap = new HashMap<>();

            Optional<ServiceOrderPriceDetail> priceDetail = price.stream().min(Comparator.comparing(ServiceOrderPriceDetail::getQuantity));
            ServiceOrderPriceDetail minQuantityPriceDetail = new ServiceOrderPriceDetail();
            if (priceDetail.isPresent()) {
                minQuantityPriceDetail = priceDetail.get();
            }
            for (ServiceOrderPriceDetail p : price) {
                BigDecimal amount = p.getAmount();
                BigDecimal originalCost = p.getOriginalCost();
                BigDecimal discount = p.getDiscount();
                BigDecimal couponDiscount = p.getCouponAmount();
                BigDecimal degradeRate = BigDecimal.ONE;
                if (!ProductCodeEnum.SFS2.getProductType().equals(p.getProductCode())) {
                    degradeRate = this.calcDegradeRate(nodeInfoList, hpcDrp.getClusterId(), p);

                }
                //实际支付的余额
                BigDecimal payBalance = Optional.ofNullable(p.getPayBalance()).orElse(BigDecimal.ZERO);
                BigDecimal payBalanceCash = Optional.ofNullable(p.getPayBalanceCash()).orElse(BigDecimal.ZERO);
                BigDecimal payCreditLine = Optional.ofNullable(p.getPayCreditLine()).orElse(BigDecimal.ZERO);

                if (PriceType.RESOURCE.equals(p.getPriceType())) {
                    totalPayment = totalPayment.add(amount);
                    totalOriginalCost = NumberUtil.add(totalOriginalCost, originalCost);
                    totalOrgDiscount = NumberUtil.add(totalOrgDiscount, discount);
                    totalCouponDiscount = NumberUtil.add(totalCouponDiscount, couponDiscount);
                    totalCouponAmount = NumberUtil.add(totalCouponAmount, payBalanceCash);
                    totalCashAmount = NumberUtil.add(totalCashAmount, payBalance);
                    totalCreditAmount = NumberUtil.add(totalCreditAmount, payCreditLine);
                }
                if (PriceType.SERVICE.equals(p.getPriceType())) {
                    serviceTotalPayment = serviceTotalPayment.add(amount);
                    serviceOriginalCost = NumberUtil.add(serviceOriginalCost, originalCost);
                    serviceOrgDiscount = NumberUtil.add(serviceOrgDiscount, discount);
                    serviceCouponDiscount = NumberUtil.add(serviceCouponDiscount, couponDiscount);
                    serviceCouponAmount = NumberUtil.add(serviceCouponAmount, payBalanceCash);
                    serviceCashAmount = NumberUtil.add(serviceCashAmount, payBalance);
                    serviceCreditAmount = NumberUtil.add(serviceCreditAmount, payCreditLine);
                }
                //最小节点数为0，则全部节点金额使用
                if (!ProductCodeEnum.SFS2.getProductType().equals(p.getProductCode()) && minQuantityPriceDetail.getQuantity() == 0) {
                    if (PriceType.RESOURCE.equals(p.getPriceType())) {
                        usedAmount = usedAmount.add(amount);
                        totalUsedCouponAmount = totalUsedCouponAmount.add(payBalanceCash);
                        totalUsedCashAmount = totalUsedCashAmount.add(payBalance);
                        totalUsedCreditAmount = totalUsedCreditAmount.add(payCreditLine);
                    }
                    if (PriceType.SERVICE.equals(p.getPriceType())) {
                        serviceUsedAmount = serviceUsedAmount.add(amount);
                        serviceUsedCouponAmount = serviceUsedCouponAmount.add(payBalanceCash);
                        serviceUsedCashAmount = serviceUsedCashAmount.add(payBalance);
                        serviceUsedCreditAmount = serviceUsedCreditAmount.add(payCreditLine);
                    }
                    continue;
                }
                Date priceDetailEndDate = p.getEndTime();
                //订单明细到当前时间的的补偿天数
                Integer priceDetailNextCompenstionDays = 0;
                if (usedTime.after(priceDetailEndDate)) {
                    //当前订单补偿的天数
                    Integer days = this.statisticalCompensationDays(Long.valueOf(id), priceDetailEndDate, now);
                    if (days != null) {
                        priceDetailNextCompenstionDays = days;
                    }
                }
                //时间已经过了的订单或超期补扣的订单全额计算已使用金额
                if (usedTime.after(DateUtils.addDays(priceDetailEndDate, priceDetailNextCompenstionDays)) || OrderType.EXPIRED.equals(p.getType())) {
                    if (PriceType.RESOURCE.equals(p.getPriceType())) {
                        usedAmount = usedAmount.add(amount);
                        totalUsedCouponAmount = totalUsedCouponAmount.add(payBalanceCash);
                        totalUsedCashAmount = totalUsedCashAmount.add(payBalance);
                        totalUsedCreditAmount = totalUsedCreditAmount.add(payCreditLine);
                    }
                    if (PriceType.SERVICE.equals(p.getPriceType())) {
                        serviceUsedAmount = serviceUsedAmount.add(amount);
                        serviceUsedCouponAmount = serviceUsedCouponAmount.add(payBalanceCash);
                        serviceUsedCashAmount = serviceUsedCashAmount.add(payBalance);
                        serviceUsedCreditAmount = serviceUsedCreditAmount.add(payCreditLine);
                    }
                } else {
                    //
                    if (!now.before(p.getStartTime())) {
                        //当前priceDetail月数
                        int month = 0;
                        Date tempStartDate = p.getStartTime();
                        while (tempStartDate.before(priceDetailEndDate)) {
                            tempStartDate = cn.hutool.core.date.DateUtil.offsetMonth(tempStartDate, 1);
                            month++;
                        }
                        //实际使用月数
                        int usedMonth = 0;
                        Date tempDate = p.getStartTime();
                        //先使用补偿天数，再计算实际使用月数
                        tempDate = DateUtils.addDays(tempDate, compenstionDays);
                        while (tempDate.before(now)) {
                            tempDate = cn.hutool.core.date.DateUtil.offsetMonth(tempDate, 1);
                            usedMonth++;
                        }
                        if (usedMonth == 0) {
                            if (NumberUtil.isGreater(BigDecimal.ONE, degradeRate) && NumberUtil.isLess(BigDecimal.ZERO, degradeRate)) {
                                if (PriceType.RESOURCE.equals(p.getPriceType())) {
                                    usedAmount = usedAmount.add(NumberUtil.mul(amount, degradeRate));
                                    totalUsedCouponAmount = totalUsedCouponAmount.add(NumberUtil.mul(payBalanceCash, degradeRate));
                                    totalUsedCashAmount = totalUsedCashAmount.add(NumberUtil.mul(payBalance, degradeRate));
                                    totalUsedCreditAmount = totalUsedCreditAmount.add(NumberUtil.mul(payCreditLine, degradeRate));
                                }
                                if (PriceType.SERVICE.equals(p.getPriceType())) {
                                    serviceUsedAmount = serviceUsedAmount.add(NumberUtil.mul(amount, degradeRate));
                                    serviceUsedCouponAmount = serviceUsedCouponAmount.add(NumberUtil.mul(payBalanceCash, degradeRate));
                                    serviceUsedCashAmount = serviceUsedCashAmount.add(NumberUtil.mul(payBalance, degradeRate));
                                    serviceUsedCreditAmount = serviceUsedCreditAmount.add(NumberUtil.mul(payCreditLine, degradeRate));
                                }
                            }
                            continue;
                        }
                        if (usedMonth > month) {
                            usedMonth = month;
                        }
                        // Calendar calendar = Calendar.getInstance();
                        // calendar.setTime(p.getStartTime());

                        //实际使用金额为支付金额减去退订的余额
                        BigDecimal backBanlance = NumberUtil.div(payBalance.multiply(degradeRate), month)
                                                            .multiply(new BigDecimal(month - usedMonth));
                        BigDecimal used = BigDecimalUtil.scaleAndRoundHalfUp(amount.subtract(backBanlance));

                        BigDecimal usedCash = BigDecimalUtil.scaleAndRoundHalfUp(payBalance.subtract(backBanlance));
                        BigDecimal backBanlanceCash = NumberUtil.div(payBalanceCash.multiply(degradeRate), month)
                                                                .multiply(new BigDecimal(month - usedMonth));
                        BigDecimal usedBanlanceCash = BigDecimalUtil.scaleAndRoundHalfUp(
                                payBalanceCash.subtract(backBanlanceCash));

                        BigDecimal backCreditLine = NumberUtil.div(payCreditLine.multiply(degradeRate), month)
                                                              .multiply(new BigDecimal(month - usedMonth));
                        BigDecimal usedCreditLine = BigDecimalUtil.scaleAndRoundHalfUp(
                                payCreditLine.subtract(backCreditLine));

                        used = NumberUtil.isGreater(used, amount) ? amount : used;
                        if (PriceType.RESOURCE.equals(p.getPriceType())) {
                            usedAmount = usedAmount.add(used);
                            totalUsedCouponAmount = totalUsedCouponAmount.add(usedBanlanceCash);
                            totalUsedCashAmount = totalUsedCashAmount.add(usedCash);
                            totalUsedCreditAmount = totalUsedCreditAmount.add(usedCreditLine);
                        }
                        if (PriceType.SERVICE.equals(p.getPriceType())) {
                            serviceUsedAmount = serviceUsedAmount.add(used);
                            serviceUsedCouponAmount = serviceUsedCouponAmount.add(usedBanlanceCash);
                            serviceUsedCashAmount = serviceUsedCashAmount.add(usedCash);
                            serviceUsedCreditAmount = serviceUsedCreditAmount.add(usedCreditLine);
                        }
                    } else if (NumberUtil.isGreater(BigDecimal.ONE, degradeRate) && NumberUtil.isLess(BigDecimal.ZERO, degradeRate)) {
                        if (PriceType.RESOURCE.equals(p.getPriceType())) {
                            usedAmount = usedAmount.add(NumberUtil.mul(amount, degradeRate));
                            totalUsedCouponAmount = totalUsedCouponAmount.add(NumberUtil.mul(payBalanceCash, degradeRate));
                            totalUsedCashAmount = totalUsedCashAmount.add(NumberUtil.mul(payBalance, degradeRate));
                            totalUsedCreditAmount = totalUsedCreditAmount.add(NumberUtil.mul(payCreditLine, degradeRate));
                        }
                        if (PriceType.SERVICE.equals(p.getPriceType())) {
                            serviceUsedAmount = serviceUsedAmount.add(NumberUtil.mul(amount, degradeRate));
                            serviceUsedCouponAmount = serviceUsedCouponAmount.add(NumberUtil.mul(payBalanceCash, degradeRate));
                            serviceUsedCashAmount = serviceUsedCashAmount.add(NumberUtil.mul(payBalance, degradeRate));
                            serviceUsedCreditAmount = serviceUsedCreditAmount.add(NumberUtil.mul(payCreditLine, degradeRate));
                        }
                    }
                }
            }
            if (isExpired) {
                Long maxOrderId = price.stream()
                                       .map(ServiceOrderPriceDetail::getOrderId).max(Long::compare).orElse(-1L);
                List<ServiceOrderPriceDetail> lastPrice = price.stream()
                                                               .filter(p -> Objects.equals(maxOrderId, p.getOrderId()) && !OrderType.EXPIRED.equals(
                                                                       p.getType()))
                                                               .collect(Collectors.toList());
                BigDecimal expiredUsedAmount = BigDecimal.ZERO;
                BigDecimal serviceExpiredUsedAmount = BigDecimal.ZERO;
                for (ServiceOrderPriceDetail p : lastPrice) {
                    BigDecimal degradeRate = BigDecimal.ONE;
                    if (!ProductCodeEnum.SFS2.getProductType().equals(p.getProductCode())) {
                        degradeRate = this.calcDegradeRate(nodeInfoList, hpcDrp.getClusterId(), p);

                    }
                    //  BigDecimal degradeRate = this.calcDegradeRate(nodeInfoList, p);
                    Integer offDay = DateUtil.calculateOffDay(endTime, nonFrozen ? now : hpcDrp.getFrozenTime());
                    Calendar calendar = Calendar.getInstance();
                    Date priceEndTime = p.getEndTime();
                    Date lastMonthStart = DateUtils.addMonths(priceEndTime, -1);
                    calendar.setTime(lastMonthStart);
                    BigDecimal used = NumberUtil.div(p.getPrice(), calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                                .multiply(BigDecimal.valueOf(offDay))
                                                .multiply(BigDecimal.valueOf(p.getQuantity()))
                                                .multiply(degradeRate)
                                                .setScale(5, RoundingMode.FLOOR);
                    if (PriceType.RESOURCE.equals(p.getPriceType())) {
                        expiredUsedAmount = expiredUsedAmount.add(used);
                    }
                    if (PriceType.SERVICE.equals(p.getPriceType())) {
                        serviceExpiredUsedAmount = serviceExpiredUsedAmount.add(used);
                    }
                }
                detailVO.setExpiredUsedAmount(expiredUsedAmount);
                servicePrice.setExpiredUsedAmount(serviceExpiredUsedAmount);
            }
            detailVO.setTotalPayment(totalPayment);
            detailVO.setTotalOriginalCost(totalOriginalCost);
            detailVO.setTotalOrgDiscount(totalOrgDiscount);
            detailVO.setTotalCouponDiscount(totalCouponDiscount);
            detailVO.setTotalCouponAmount(totalCouponAmount);
            detailVO.setTotalCashAmount(totalCashAmount);
            detailVO.setTotalCreditAmount(totalCreditAmount);
            detailVO.setTotalUsedAmount(usedAmount);
            detailVO.setTotalUsedCouponAmount(totalUsedCouponAmount);
            detailVO.setTotalUsedCashAmount(totalUsedCashAmount);
            detailVO.setTotalUsedCreditAmount(totalUsedCreditAmount);
            detailVO.setUnsubAmount(totalPayment.subtract(usedAmount));
            detailVO.setUnsubCouponAmount(totalCouponAmount.subtract(totalUsedCouponAmount));
            detailVO.setUnsubCashAmount(totalCashAmount.subtract(totalUsedCashAmount));
            detailVO.setUnsubCreditAmount(totalCreditAmount.subtract(totalUsedCreditAmount));
            servicePrice.setTotalPayment(serviceTotalPayment);
            servicePrice.setTotalOriginalCost(serviceOriginalCost);
            servicePrice.setTotalOrgDiscount(serviceOrgDiscount);
            servicePrice.setTotalCouponDiscount(serviceCouponDiscount);
            servicePrice.setTotalCouponAmount(serviceCouponAmount);
            servicePrice.setTotalCashAmount(serviceCashAmount);
            servicePrice.setTotalCreditAmount(serviceCreditAmount);
            servicePrice.setTotalUsed(serviceUsedAmount);
            servicePrice.setTotalUsedCouponAmount(serviceUsedCouponAmount);
            servicePrice.setTotalUsedCashAmount(serviceUsedCashAmount);
            servicePrice.setTotalUsedCreditAmount(serviceUsedCreditAmount);
            servicePrice.setUnsubAmount(serviceTotalPayment.subtract(serviceUsedAmount));
            servicePrice.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
            servicePrice.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
            servicePrice.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
            detailVO.setServiceAmount(servicePrice);
            details.add(detailVO);
        }
        Comparator<ResourceDetailVO> comparator = Comparator.comparingInt(o -> o.getProductCode().length());
        details.sort(comparator.reversed());
        vo.setDetail(details);
        vo.setTotalPayment(details.stream()
                                  .map(d -> d.getTotalPayment().add(d.getServiceAmount().getTotalPayment()))
                                  .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOriginalCost(details.stream()
                                       .map(d -> d.getTotalOriginalCost()
                                                  .add(d.getServiceAmount().getTotalOriginalCost()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOrgDiscount(details.stream()
                                      .map(d -> d.getTotalOrgDiscount()
                                                 .add(d.getServiceAmount().getTotalOrgDiscount()))
                                      .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponDiscount(details.stream()
                                         .map(d -> d.getTotalCouponDiscount()
                                                    .add(d.getServiceAmount().getTotalCouponDiscount()))
                                         .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponAmount(details.stream()
                                       .map(d -> d.getTotalCouponAmount()
                                                  .add(d.getServiceAmount().getTotalCouponAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCashAmount(details.stream()
                                     .map(d -> d.getTotalCashAmount()
                                                .add(d.getServiceAmount().getTotalCashAmount()))
                                     .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCreditAmount(details.stream()
                                       .map(d -> d.getTotalCreditAmount()
                                                  .add(d.getServiceAmount().getTotalCreditAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUsedAmount(details.stream()
                                .map(d -> d.getTotalUsedAmount().add(d.getServiceAmount().getTotalUsed()))
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCouponAmount(details.stream()
                                           .map(d -> d.getTotalUsedCouponAmount()
                                                      .add(d.getServiceAmount().getTotalUsedCouponAmount()))
                                           .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCashAmount(details.stream()
                                         .map(d -> d.getTotalUsedCashAmount()
                                                    .add(d.getServiceAmount().getTotalUsedCashAmount()))
                                         .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCreditAmount(details.stream()
                                           .map(d -> d.getTotalUsedCreditAmount()
                                                      .add(d.getServiceAmount().getTotalUsedCreditAmount()))
                                           .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUnsubAmount(vo.getTotalCashAmount().subtract(vo.getTotalUsedCashAmount()));
        vo.setUnsubCouponAmount(vo.getTotalCouponAmount().subtract(vo.getTotalUsedCouponAmount()));
        vo.setUnsubCashAmount(vo.getTotalCashAmount().subtract(vo.getTotalUsedCashAmount()));
        vo.setUnsubCreditAmount(vo.getTotalCreditAmount().subtract(vo.getTotalUsedCreditAmount()));
        vo.setExpiredUsedAmount(details.stream()
                                       .map(d -> d.getExpiredUsedAmount().add(d.getServiceAmount().getExpiredUsedAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    @Override
    public void inquiryUnsubscribePriceContainSubsidiaryResource(String id, UnsubInquiryPriceVO vo) {
        SfProductResource mainResource = sfProductResourceService.getById(id);
        List<SfProductResource> resources = new ArrayList<>();
        resources.add(mainResource);
        Boolean isRds = ProductCodeEnum.RDS.getProductType().equals(mainResource.getProductType());
        //查询资源绑定资源
        if (ProductCodeEnum.ECS.getProductType().equals(mainResource.getProductType())) {
            ResVmInfoListRequest request = new ResVmInfoListRequest();
            request.setIdIn(Arrays.asList(mainResource.getClusterId()));
            RestResult<List<ResVmInfoListResult>> vmInfoResult = ecsDcService.getVmInfoByIds(request);
            if (Objects.nonNull(vmInfoResult) && vmInfoResult.getStatus() && Objects.nonNull(vmInfoResult.getData())) {
                List<ResVmInfoListResult> vmInfo = JSON.parseArray(JSON.toJSONString(vmInfoResult.getData()), ResVmInfoListResult.class);
                Map<Long, ResVmInfoListResult> collect = vmInfo.stream().collect(Collectors.toMap(ResVmInfoListResult::getId, Function.identity()));
                ResVmInfoListResult resVmInfoListResult = collect.get(mainResource.getClusterId());
                List<ResVdDto> vds = resVmInfoListResult.getVds();
                List<Long> vdIds = vds.stream()
                                      .filter(e -> "withInstance".equals(e.getReleaseMode()))
                                      .map(ResVdDto::getId)
                                      .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(vdIds)) {
                    List<SfProductResource> list = sfProductResourceService.list(
                            new LambdaQueryWrapper<SfProductResource>().in(SfProductResource::getClusterId, vdIds));
                    for (SfProductResource resource : list) {
                        if (!SfProductEnum.NORMAL.getStatus().equals(resource.getStatus())
                                && !SfProductEnum.EXPIRED.getStatus().equals(resource.getStatus())
                                && !SfProductEnum.FROZEN.getStatus().equals(resource.getStatus())) {
                            String statusByI18n = SfProductEnum.getNameByI18n(resource.getStatus(), WebUtil.getHeaderAcceptLanguage());
                            String nameByI18n = ProductCodeEnum.getNameByI18n(resource.getProductType(), WebUtil.getHeaderAcceptLanguage());
                            throw new BizException(WebUtil.getMessage(MsgCd.CURRENT_CONTAIN_OPERATION_NOT_SUPPORTED, new Object[]{nameByI18n, statusByI18n}));
                        }
                    }
                    resources.addAll(list);
                }
            }
        }else if (ProductCodeEnum.RS_BMS.getProductType().equals(mainResource.getProductType())) {
            ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(new LambdaQueryWrapper<ServiceOrderResourceRef>()
                                                                                        .eq(ServiceOrderResourceRef::getResourceId, id)
                                                                                        .eq(ServiceOrderResourceRef::getType,
                                                                                            ProductCodeEnum.RS_BMS.getProductType()));
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectById(serviceOrderResourceRef.getOrderDetailId());
            JSONArray volumeOrderDetailIds = JSON.parseObject(serviceOrderDetail.getServiceConfig()).getJSONArray("volumeOrderDetailIds");
            if (CollectionUtils.isNotEmpty(volumeOrderDetailIds)) {
                List<Long> longs = volumeOrderDetailIds.toJavaList(Long.class);
                List<ServiceOrderResourceRef> diskList = serviceOrderResourceRefService.list(new LambdaQueryWrapper<ServiceOrderResourceRef>()
                                                                                                 .eq(ServiceOrderResourceRef::getType,
                                                                                                     ProductCodeEnum.DISK.getProductType())
                                                                                                 .in(ServiceOrderResourceRef::getOrderDetailId,
                                                                                                     longs));
                if (CollectionUtils.isNotEmpty(diskList)) {
                    List<Long> collect = diskList.stream().map(ref -> Long.parseLong(ref.getResourceId())).collect(Collectors.toList());
                    List<SfProductResource> list =
                            sfProductResourceService.list(new LambdaQueryWrapper<SfProductResource>().in(SfProductResource::getId, collect));
                    resources.addAll(list);
                }
            }
        }
        List<Long> orderIds = resources.stream().map(SfProductResource::getServiceOrderId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<ServiceOrderDetail> qw = new LambdaQueryWrapper<>();
        qw.in(ServiceOrderDetail::getOrderId, orderIds);
        List<ServiceOrderDetail> list = iServiceOrderDetailService.list(qw);
        Map<Long, String> orderMap = list.stream().collect(Collectors.toMap(ServiceOrderDetail::getOrderId, ServiceOrderDetail::getChargeType,  (k, v) -> k));

        //循环每个资源的订单价格详情
        List<ResourceDetailVO> details = Lists.newArrayList();
        for (SfProductResource resource : resources) {
            ResourceDetailVO detailVO = new ResourceDetailVO();
            detailVO.init();
            String chargeType = orderMap.get(resource.getServiceOrderId());
            detailVO.setChargeType(chargeType);
            if (!ChargeTypeEnum.PrePaid.getType().equals(chargeType)) {
                postPaidInit(resource, detailVO, details);
                continue;
            }

            Date now = cn.hutool.core.date.DateUtil.date();
            Date resourceEndTime = resource.getEndTime();
            boolean isExpired = resourceEndTime.before(now);
            boolean nonFrozen = !"frozen".equalsIgnoreCase(resource.getStatus());

            //查询每个资源各自的订单价格详情,存在多实例共用一个PriceDetail的情况，需要每个资源独立查询
            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + resource.getId() + "\"");
            criteria.put("types", Lists.newArrayList("apply", "renew", "modify"));
            criteria.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> prices = serviceOrderPriceDetailMapper.selectByCriteria(criteria);
            prices.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
            // 该时间为退订时间点（为和之前业务兼容，所以没有修改字段名）
            detailVO.setComputeDate(now);
            // 该时间为原产品到期时间（为和之前业务兼容，所以没有修改字段名）
            detailVO.setNow(resource.getEndTime());
            prices.stream().filter(p -> OrderType.APPLY.equals(p.getType())).findFirst().ifPresent(d -> {
                          detailVO.setOrderDetailId(d.getOrderDetailId());
                          detailVO.setProjectId(d.getOrgSid());
                          detailVO.setChargeType(d.getChargeType());
                      });
            detailVO.setId(resource.getId().toString());
            detailVO.setProductCode(prices.get(0).getProductCode());

            // 服务
            ServicePrice servicePrice = new ServicePrice();
            BigDecimal serviceTotalPayment = servicePrice.getTotalPayment();
            BigDecimal serviceOriginalCost = Convert.toBigDecimal(servicePrice.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal serviceOrgDiscount = Convert.toBigDecimal(servicePrice.getTotalOrgDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponDiscount = Convert.toBigDecimal(servicePrice.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponAmount = Convert.toBigDecimal(servicePrice.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceCashAmount = Convert.toBigDecimal(servicePrice.getTotalCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceCreditAmount = Convert.toBigDecimal(servicePrice.getTotalCreditAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedAmount = servicePrice.getTotalUsed();
            BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCreditAmount(), BigDecimal.ZERO);

            //总支付
            BigDecimal totalOriginalCost = Convert.toBigDecimal(detailVO.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal totalOrgDiscount = Convert.toBigDecimal(detailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
            //优惠券
            BigDecimal totalCouponDiscount = Convert.toBigDecimal(detailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal totalCashAmount = Convert.toBigDecimal(detailVO.getTotalCashAmount(), BigDecimal.ZERO);
            //充值现金券
            BigDecimal totalCouponAmount = Convert.toBigDecimal(detailVO.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal totalCreditAmount = Convert.toBigDecimal(detailVO.getTotalCreditAmount(), BigDecimal.ZERO);
            //已使用充值优惠券
            BigDecimal usedCouponAmount = BigDecimal.ZERO;
            //已使用优惠券
            BigDecimal usedCouponDiscount = BigDecimal.ZERO;
            BigDecimal usedCreditAmount = BigDecimal.ZERO;
            BigDecimal usedCashAmount = BigDecimal.ZERO;

            Date applyCurrentDate = now;
            Date resEndTime = Objects.isNull(resource.getEndTime()) ? new Date() : resource.getEndTime();
            for (ServiceOrderPriceDetail p : prices) {
                if("apply".equals(p.getOrderType())) {
                    ServiceOrderDetail detail = iServiceOrderDetailService.getById(p.getOrderDetailId());
                    Query query = new Query();
                    String orderId = detail.getOrderId().toString();
                    org.springframework.data.mongodb.core.query.Criteria c = new org.springframework.data.mongodb.core.query.Criteria();
                    c.and("orderId").is(orderId);
                    c.and("instanceId").is(resource.getId().toString());
                    query.addCriteria(c);
                    List<InstanceGaapCost> costs = mongoTemplate.find(query, InstanceGaapCost.class, "biz_bill_usage_item");
                    InstanceGaapCost applyCost = costs.get(0);

                    Date priceDetailStartTime = p.getStartTime();
                    Date priceDetailEndTime = Optional.ofNullable(p.getEndTime()).orElse(new Date());
                    Date nowDate = cn.hutool.core.date.DateUtil.date();
                    if (resEndTime.after(nowDate)) {
                        Date resCurrentMonthEndTime = cn.hutool.core.date.DateUtil.date(priceDetailStartTime);
                        while (resCurrentMonthEndTime.before(nowDate)) {
                            resCurrentMonthEndTime = cn.hutool.core.date.DateUtil.offsetMonth(resCurrentMonthEndTime, 1);
                        }
                        applyCurrentDate = resCurrentMonthEndTime.after(resEndTime) ? resEndTime : resCurrentMonthEndTime;
                    }

                    if (!priceDetailEndTime.before(applyCurrentDate)) {
                        Integer duration = detail.getDuration();
                        if("0".equals(String.valueOf(duration))){
                            duration = 1;
                        }
                        long offMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(priceDetailStartTime, applyCurrentDate);
                        usedCouponAmount = usedCouponAmount.add(NumberUtil.div(scaleThree(applyCost.getCouponAmount()).multiply(BigDecimal.valueOf(offMonths)), duration));
                        usedCouponDiscount = usedCouponDiscount.add(NumberUtil.div(scaleThree(applyCost.getCouponDiscount()).multiply(BigDecimal.valueOf(offMonths)), duration));
                        usedCreditAmount = usedCreditAmount.add(NumberUtil.div(applyCost.getCreditAmount().multiply(BigDecimal.valueOf(offMonths)), duration));
                        usedCashAmount = usedCashAmount.add(NumberUtil.div(applyCost.getCashAmount().multiply(BigDecimal.valueOf(offMonths)),duration));//
                    } else {
                        //价格详情结束时间已经结束，该周期已使用完
                        usedCouponAmount = usedCouponAmount.add(scaleThree(applyCost.getCouponAmount()));
                        usedCouponDiscount = usedCouponDiscount.add(scaleThree(applyCost.getCouponDiscount()));
                        usedCreditAmount = usedCreditAmount.add(applyCost.getCreditAmount());
                        usedCashAmount = usedCashAmount.add(applyCost.getCashAmount());
                    }

                    //支付信息
                    totalOriginalCost = totalOriginalCost.add(scaleThree(applyCost.getPretaxGrossAmount()));
                    totalOrgDiscount = totalOrgDiscount.add(scaleThree(applyCost.getPricingDiscount()));
                    totalCouponDiscount = totalCouponDiscount.add(scaleThree(applyCost.getCouponDiscount()));
                    totalCreditAmount = totalCreditAmount.add(scaleThree(applyCost.getCreditAmount()));
                    totalCouponAmount = totalCouponAmount.add(scaleThree(applyCost.getCouponAmount()));
                    totalCashAmount = totalCashAmount.add(scaleThree(applyCost.getCashAmount()));
                }
                else if("renew".equals(p.getOrderType())) {
                    //处理续订的账单
                    totalOriginalCost = totalOriginalCost.add(scaleThree(p.getOriginalCost()));
                    totalOrgDiscount = totalOrgDiscount.add(scaleThree(p.getDiscount()));
                    totalCouponDiscount = totalCouponDiscount.add(scaleThree(p.getCouponAmount()));
                    totalCreditAmount = totalCreditAmount.add(scaleThree(p.getPayCreditLine()));
                    totalCashAmount = totalCashAmount.add(scaleThree(p.getPayBalance()));
                    totalCouponAmount = totalCouponAmount.add(scaleThree(p.getCouponDiscount()));

                    Date renewEndTime = p.getEndTime();
                    Date renewStartTime = p.getStartTime();
                    BigDecimal payBalanceCash = p.getPayBalanceCash();
                    BigDecimal payCreditLine = p.getPayCreditLine();
                    BigDecimal payCashAmount = p.getPayBalance();
                    BigDecimal couponAmount = p.getCouponAmount();
                    log.info("payCashAmount..{}",payCashAmount.toString());
                    if (renewStartTime.after(applyCurrentDate)) {

                    }
                    else if (renewEndTime.before(applyCurrentDate)) {
                        usedCouponDiscount = usedCouponDiscount.add(couponAmount);
                        usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                        usedCreditAmount = usedCreditAmount.add(payCreditLine);
                        //已使用
                        usedCashAmount = usedCashAmount.add(p.getPayBalance());


                        if (renewEndTime.equals(resEndTime)) {
                            long offsetDays= 0 ;
                            BigDecimal originalCost = p.getPrice();
                            if("frozen".equals(resource.getStatus())){
                                Date frozenTime = resource.getFrozenTime();
                                offsetDays =DateUtil.calculateOffDay(renewEndTime,frozenTime);
                            }else{
                                offsetDays = DateUtil.calculateOffDay(renewEndTime, applyCurrentDate);
                            }
                            BigDecimal allDays = NumberUtil.div(BigDecimal.valueOf(renewEndTime.getTime() - renewStartTime.getTime()), BigDecimal.valueOf(1000 * 60 * 60 * 24L)).setScale(0, BigDecimal.ROUND_CEILING);
                            usedCashAmount = usedCashAmount.add(NumberUtil.div(originalCost.multiply(BigDecimal.valueOf(offsetDays)), allDays));
                        }
                    }
                    else if (renewStartTime.before(applyCurrentDate)) {
                        long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime, renewEndTime);
                        long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(renewStartTime, applyCurrentDate);
                        //如果未到续订服务开始时间，整月的购买费用退还
                        if (renewStartTime.before(new Date()) && totalMonth>0) {
                            usedCouponAmount = usedCouponAmount.add(NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            usedCouponDiscount = usedCouponDiscount.add(NumberUtil.div(couponAmount.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            usedCreditAmount = usedCreditAmount.add(NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            //按比例计算
                            if (p.getPayBalance().compareTo(BigDecimal.ZERO) >= 0) {
                                usedCashAmount = usedCashAmount.add(NumberUtil.div(p.getPayBalance().multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            }
                        }
                    }
                    //续订询价存在补扣
                    List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectOrderDetailByType(p.getOrderSn(),"expired");
                    if(serviceOrderPriceDetails.size() > 0){
                        for(ServiceOrderPriceDetail expiredOrderPriceDetail : serviceOrderPriceDetails){
                            if("expired".equals(expiredOrderPriceDetail.getType()) && expiredOrderPriceDetail.getRefInstanceId().contains(resource.getId().toString())){
                                totalOriginalCost = totalOriginalCost.add(scaleThree(expiredOrderPriceDetail.getOriginalCost()));
                                totalOrgDiscount = totalOrgDiscount.add(scaleThree(expiredOrderPriceDetail.getOrgDiscount()));
                                totalCouponDiscount = totalCouponDiscount.add(scaleThree(expiredOrderPriceDetail.getCouponAmount()));
                                totalCreditAmount = totalCreditAmount.add(scaleThree(expiredOrderPriceDetail.getPayCreditLine()));
                                totalCashAmount = totalCashAmount.add(scaleThree(expiredOrderPriceDetail.getPayBalance()));
                                totalCouponAmount = totalCouponAmount.add(scaleThree(expiredOrderPriceDetail.getCouponDiscount()));

                                usedCouponAmount = usedCouponAmount.add(expiredOrderPriceDetail.getPayBalanceCash());
                                usedCouponDiscount = usedCouponAmount.add(expiredOrderPriceDetail.getCouponAmount());
                                usedCreditAmount = usedCreditAmount.add(expiredOrderPriceDetail.getPayCreditLine());
                                usedCashAmount = usedCashAmount.add(expiredOrderPriceDetail.getPayBalance());
                            }
                        }
                    }
                }else if("modify".equals(p.getOrderType())) {
                    //变更原价定义模糊，先取变更未优惠时的价格
                    totalOriginalCost = totalOriginalCost.add(scaleThree(p.getAmount()).add(scaleThree(p.getDiscount())).add(p.getCouponAmount()));
                    totalOrgDiscount = totalOrgDiscount.add(scaleThree(p.getOrgDiscount()));
                    totalCouponDiscount = totalCouponDiscount.add(scaleThree(p.getCouponAmount().multiply(BigDecimal.valueOf(p.getQuantity()))));
                    totalCreditAmount = totalCreditAmount.add(p.getPayCreditLine());
                    totalCashAmount = totalCashAmount.add(p.getPayBalance());
                    totalCouponAmount = totalCouponAmount.add(scaleThree(p.getCouponDiscount()));

                    Date upgradeEndTime = p.getEndTime();
                    Date upgradeStartTime = p.getStartTime();
                    BigDecimal payBalanceCash = p.getPayBalanceCash();
                    BigDecimal payCreditLine = p.getPayCreditLine();
                    BigDecimal payCashAmount = p.getPayBalance();
                    BigDecimal couponAmount = p.getCouponAmount();
                    log.info("payCashAmount..{}",payCashAmount.toString());
                    if (upgradeEndTime.before(applyCurrentDate)) {
                        usedCouponAmount = usedCouponAmount.add(payBalanceCash);
                        usedCouponDiscount = usedCouponAmount.add(couponAmount);
                        usedCreditAmount = usedCreditAmount.add(payCreditLine);
                        usedCashAmount = usedCashAmount.add(payCashAmount);
                    } else if (upgradeStartTime.before(applyCurrentDate)) {
                        long totalMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, upgradeEndTime);
                        long offsetMonth = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(upgradeStartTime, applyCurrentDate);

                        if(totalMonth == 0L) {
                            totalMonth = 1L;
                        }
                        //如果未到续订服务开始时间，整月的购买费用退还
                        if (upgradeStartTime.before(new Date())) {
                            usedCouponAmount = usedCouponAmount.add(NumberUtil.div(payBalanceCash.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            usedCouponDiscount = usedCouponDiscount.add(NumberUtil.div(couponAmount.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            usedCreditAmount = usedCreditAmount.add(NumberUtil.div(payCreditLine.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            if (p.getPayBalance().compareTo(BigDecimal.ZERO) < 0) {
                                BigDecimal pay = p.getPayBalance();
                                BigDecimal tradePrice = p.getTradePrice();
                                while (pay.compareTo(BigDecimal.ZERO) < 0) {
                                    pay = pay.add(tradePrice);
                                }
                                usedCashAmount = usedCashAmount.add(NumberUtil.div(pay.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            } else {
                                usedCashAmount = usedCashAmount.add(NumberUtil.div(payCashAmount.multiply(BigDecimal.valueOf(offsetMonth)), totalMonth));
                            }
                        }
                    }
                }
            }

            if (isExpired) {
                Long maxOrderId = prices.stream().map(ServiceOrderPriceDetail::getOrderId).max(Long::compare).orElse(-1L);
                List<ServiceOrderPriceDetail> lastPrice = prices.stream().filter(p -> Objects.equals(maxOrderId, p.getOrderId()) && !OrderType.EXPIRED.equals(p.getType())).collect(Collectors.toList());
                BigDecimal expiredUsedAmount = BigDecimal.ZERO;
                BigDecimal serviceExpiredUsedAmount = BigDecimal.ZERO;
                for (ServiceOrderPriceDetail p : lastPrice) {
                    BigDecimal degradeRate=BigDecimal.ONE;
                    Integer offDay = DateUtil.calculateOffDay(resourceEndTime, nonFrozen ? now : resource.getFrozenTime());
                    Calendar calendar = Calendar.getInstance();
                    Date priceEndTime = p.getEndTime();
                    Date lastMonthStart = DateUtils.addMonths(priceEndTime, -1);
                    calendar.setTime(lastMonthStart);
                    BigDecimal used = NumberUtil.div(p.getPrice(), calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                                .multiply(BigDecimal.valueOf(offDay))
                                                .multiply(BigDecimal.valueOf(p.getQuantity()))
                                                .multiply(degradeRate)
                                                .setScale(5, BigDecimal.ROUND_HALF_UP);
                    if (PriceType.RESOURCE.equals(p.getPriceType())) {
                        expiredUsedAmount = expiredUsedAmount.add(used);
                    }
                    if (PriceType.SERVICE.equals(p.getPriceType())) {
                        serviceExpiredUsedAmount = serviceExpiredUsedAmount.add(used);
                    }
                }
                detailVO.setExpiredUsedAmount(expiredUsedAmount);
                servicePrice.setExpiredUsedAmount(serviceExpiredUsedAmount);






            }
            BigDecimal totalPayment = totalCashAmount.add(totalCreditAmount).add(totalCouponAmount);
            detailVO.setTotalPayment(totalPayment);
            detailVO.setTotalOriginalCost(totalOriginalCost);
            detailVO.setTotalOrgDiscount(totalOrgDiscount);
            detailVO.setTotalCouponDiscount(totalCouponDiscount);
            detailVO.setTotalCouponAmount(totalCouponAmount);
            detailVO.setTotalCashAmount(totalCashAmount);
            detailVO.setTotalCreditAmount(totalCreditAmount);

            BigDecimal usedAmount = usedCashAmount.add(usedCreditAmount).add(usedCouponAmount).add(usedCouponDiscount);
            detailVO.setTotalUsedAmount(usedAmount);
            detailVO.setTotalUsedCouponAmount(usedCouponAmount);
            detailVO.setTotalUsedCashAmount(usedCashAmount);
            detailVO.setTotalUsedCreditAmount(usedCreditAmount);

            detailVO.setUnsubAmount(totalPayment.subtract(usedAmount));
            detailVO.setUnsubCouponAmount(totalCouponAmount.subtract(usedCouponAmount));
            detailVO.setUnsubCashAmount(totalCashAmount.subtract(usedCashAmount));
            detailVO.setUnsubCreditAmount(totalCreditAmount.subtract(usedCreditAmount));

            servicePrice.setTotalPayment(serviceTotalPayment);
            servicePrice.setTotalOriginalCost(serviceOriginalCost);
            servicePrice.setTotalOrgDiscount(serviceOrgDiscount);
            servicePrice.setTotalCouponDiscount(serviceCouponDiscount);
            servicePrice.setTotalCouponAmount(serviceCouponAmount);
            servicePrice.setTotalCashAmount(serviceCashAmount);
            servicePrice.setTotalCreditAmount(serviceCreditAmount);
            servicePrice.setTotalUsed(serviceUsedAmount);
            servicePrice.setTotalUsedCouponAmount(serviceUsedCouponAmount);
            servicePrice.setTotalUsedCashAmount(serviceUsedCashAmount);
            servicePrice.setTotalUsedCreditAmount(serviceUsedCreditAmount);
            servicePrice.setUnsubAmount(serviceTotalPayment.subtract(serviceUsedAmount));
            servicePrice.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
            servicePrice.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
            servicePrice.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
            detailVO.setServiceAmount(servicePrice);
            details.add(detailVO);
        }
        //汇总资源的价格
        Comparator<ResourceDetailVO> comparator = Comparator.comparingInt(o -> o.getProductCode().length());
        details.sort(comparator.reversed());
        vo.setDetail(details);

        vo.setTotalPayment(details.stream().map(d -> d.getTotalPayment().add(d.getServiceAmount().getTotalPayment())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOriginalCost(details.stream().map(d -> d.getTotalOriginalCost().add(d.getServiceAmount().getTotalOriginalCost())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOrgDiscount(details.stream().map(d -> d.getTotalOrgDiscount().add(d.getServiceAmount().getTotalOrgDiscount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponDiscount(details.stream().map(d -> d.getTotalCouponDiscount().add(d.getServiceAmount().getTotalCouponDiscount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponAmount(details.stream().map(d -> d.getTotalCouponAmount().add(d.getServiceAmount().getTotalCouponAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCashAmount(details.stream().map(d -> d.getTotalCashAmount().add(d.getServiceAmount().getTotalCashAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCreditAmount(details.stream().map(d -> d.getTotalCreditAmount().add(d.getServiceAmount().getTotalCreditAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUsedAmount(details.stream().map(d -> d.getTotalUsedAmount().add(d.getServiceAmount().getTotalUsed())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCouponAmount(details.stream().map(d -> d.getTotalUsedCouponAmount().add(d.getServiceAmount().getTotalUsedCouponAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCashAmount(details.stream().map(d -> d.getTotalUsedCashAmount().add(d.getServiceAmount().getTotalUsedCashAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCreditAmount(details.stream().map(d -> d.getTotalUsedCreditAmount().add(d.getServiceAmount().getTotalUsedCreditAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUnsubAmount(details.stream().map(ResourceDetailVO::getUnsubCashAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUnsubCouponAmount(vo.getTotalCouponAmount().subtract(vo.getTotalUsedCouponAmount()));
        vo.setUnsubCashAmount(vo.getTotalCashAmount().subtract(vo.getTotalUsedCashAmount()));
        vo.setUnsubCreditAmount(vo.getTotalCreditAmount().subtract(vo.getTotalUsedCreditAmount()));
        vo.setExpiredUsedAmount(details.stream().map(d -> d.getExpiredUsedAmount().add(d.getServiceAmount().getExpiredUsedAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    @Override
    public void inquiryUnsubscribePriceRdsResource(String id, UnsubInquiryPriceVO vo) {
        SfProductResource mainResource = sfProductResourceService.getById(id);
        List<SfProductResource> resources = new ArrayList<>();
        resources.add(mainResource);
        Boolean isRds = ProductCodeEnum.RDS.getProductType().equals(mainResource.getProductType());
        List<Long> orderIds = resources.stream().map(SfProductResource::getServiceOrderId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<ServiceOrderDetail> qw = new LambdaQueryWrapper<>();
        qw.in(ServiceOrderDetail::getOrderId, orderIds);
        List<ServiceOrderDetail> list = iServiceOrderDetailService.list(qw);
        Map<Long, String> orderMap = list.stream().collect(Collectors.toMap(ServiceOrderDetail::getOrderId, ServiceOrderDetail::getChargeType,  (k, v) -> k));
        //循环每个资源的订单价格详情
        List<ResourceDetailVO> details = Lists.newArrayList();
        for (SfProductResource resource : resources) {
            String chargeType = orderMap.get(resource.getServiceOrderId());
            ResourceDetailVO detailVO = new ResourceDetailVO();
            detailVO.init();
            detailVO.setChargeType(chargeType);
            if (!ChargeTypeEnum.PrePaid.getType().equals(chargeType)) {
                detailVO.setComputeDate(cn.hutool.core.date.DateUtil.date());
                detailVO.setId(resource.getId().toString());
                detailVO.setProductCode(resource.getProductType());
                detailVO.setTotalPayment(BigDecimal.ZERO);
                detailVO.setTotalOriginalCost(BigDecimal.ZERO);
                detailVO.setTotalOrgDiscount(BigDecimal.ZERO);
                detailVO.setTotalCouponDiscount(BigDecimal.ZERO);
                detailVO.setTotalCouponAmount(BigDecimal.ZERO);
                detailVO.setTotalCashAmount(BigDecimal.ZERO);
                detailVO.setTotalCreditAmount(BigDecimal.ZERO);
                detailVO.setTotalUsedAmount(BigDecimal.ZERO);
                detailVO.setTotalUsedCouponAmount(BigDecimal.ZERO);
                detailVO.setTotalUsedCashAmount(BigDecimal.ZERO);
                detailVO.setTotalUsedCreditAmount(BigDecimal.ZERO);
                detailVO.setUnsubAmount(BigDecimal.ZERO);
                detailVO.setUnsubCouponAmount(BigDecimal.ZERO);
                detailVO.setUnsubCashAmount(BigDecimal.ZERO);
                detailVO.setUnsubCreditAmount(BigDecimal.ZERO);
                detailVO.setExpiredUsedAmount(BigDecimal.ZERO);
                ServicePrice servicePrice = new ServicePrice();
                servicePrice.setExpiredUsedAmount(BigDecimal.ZERO);
                servicePrice.setTotalPayment(BigDecimal.ZERO);
                servicePrice.setTotalOriginalCost(BigDecimal.ZERO);
                servicePrice.setTotalOrgDiscount(BigDecimal.ZERO);
                servicePrice.setTotalCouponDiscount(BigDecimal.ZERO);
                servicePrice.setTotalCouponAmount(BigDecimal.ZERO);
                servicePrice.setTotalCashAmount(BigDecimal.ZERO);
                servicePrice.setTotalCreditAmount(BigDecimal.ZERO);
                servicePrice.setTotalUsed(BigDecimal.ZERO);
                servicePrice.setTotalUsedCouponAmount(BigDecimal.ZERO);
                servicePrice.setTotalUsedCashAmount(BigDecimal.ZERO);
                servicePrice.setTotalUsedCreditAmount(BigDecimal.ZERO);
                servicePrice.setUnsubAmount(BigDecimal.ZERO);
                servicePrice.setUnsubCouponAmount(BigDecimal.ZERO);
                servicePrice.setUnsubCashAmount(BigDecimal.ZERO);
                servicePrice.setUnsubCreditAmount(BigDecimal.ZERO);
                detailVO.setServiceAmount(servicePrice);
                details.add(detailVO);
                continue;
            }

            Date now = cn.hutool.core.date.DateUtil.date();
            Date endTime = resource.getEndTime();
            boolean isExpired = endTime.before(now);
            boolean nonFrozen = !"frozen".equalsIgnoreCase(resource.getStatus());
            Date usedTime = resource.getStartTime();

            while (usedTime.before(now)) {
                usedTime = cn.hutool.core.date.DateUtil.offsetMonth(usedTime, 1);
            }
            usedTime = usedTime.after(endTime) ? endTime : now;

            //当前订单补偿的天数
            Integer compenstionDays = this.statisticalCompensationDays(Long.valueOf(id), now, null);
            if (compenstionDays == null) {
                compenstionDays = 0;
            }

            //查询每个资源各自的订单价格详情,存在多实例共用一个PriceDetail的情况，需要每个资源独立查询
            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + resource.getId() + "\"");
            criteria.put("types", Lists.newArrayList("apply", "renew", "modify", "expired"));
            criteria.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> prices = serviceOrderPriceDetailMapper.selectByCriteria(criteria);
            prices.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
            // 该时间为退订时间点（为和之前业务兼容，所以没有修改字段名）
            detailVO.setComputeDate(now);
            // 该时间为原产品到期时间（为和之前业务兼容，所以没有修改字段名）
            detailVO.setNow(resource.getEndTime());
            prices.stream().filter(p -> OrderType.APPLY.equals(p.getType())).findFirst()
                  .ifPresent(d -> {
                      detailVO.setOrderDetailId(d.getOrderDetailId());
                      detailVO.setProjectId(d.getOrgSid());
                      detailVO.setChargeType(d.getChargeType());
                  });
            detailVO.setId(resource.getId().toString());
            detailVO.setProductCode(prices.get(0).getProductCode());
            // 资源
            BigDecimal totalPayment = detailVO.getTotalPayment();
            BigDecimal totalOriginalCost = Convert.toBigDecimal(detailVO.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal totalOrgDiscount = Convert.toBigDecimal(detailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
            BigDecimal totalCouponDiscount = Convert.toBigDecimal(detailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal totalCouponAmount = Convert.toBigDecimal(detailVO.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal totalCashAmount = Convert.toBigDecimal(detailVO.getTotalCashAmount(), BigDecimal.ZERO);
            BigDecimal totalCreditAmount = Convert.toBigDecimal(detailVO.getTotalCreditAmount(), BigDecimal.ZERO);
            BigDecimal usedAmount = detailVO.getTotalUsedAmount();
            BigDecimal totalUsedCouponAmount = Convert.toBigDecimal(detailVO.getTotalUsedCouponAmount(), BigDecimal.ZERO);
            BigDecimal totalUsedCashAmount = Convert.toBigDecimal(detailVO.getTotalUsedCashAmount(), BigDecimal.ZERO);
            BigDecimal totalUsedCreditAmount = Convert.toBigDecimal(detailVO.getTotalUsedCreditAmount(), BigDecimal.ZERO);
            // 服务
            ServicePrice servicePrice = new ServicePrice();
            BigDecimal serviceTotalPayment = servicePrice.getTotalPayment();
            BigDecimal serviceOriginalCost = Convert.toBigDecimal(servicePrice.getTotalOriginalCost(), BigDecimal.ZERO);
            BigDecimal serviceOrgDiscount = Convert.toBigDecimal(servicePrice.getTotalOrgDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponDiscount = Convert.toBigDecimal(servicePrice.getTotalCouponDiscount(), BigDecimal.ZERO);
            BigDecimal serviceCouponAmount = Convert.toBigDecimal(servicePrice.getTotalCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceCashAmount = Convert.toBigDecimal(servicePrice.getTotalCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceCreditAmount = Convert.toBigDecimal(servicePrice.getTotalCreditAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedAmount = servicePrice.getTotalUsed();
            BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCouponAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCashAmount(), BigDecimal.ZERO);
            BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCreditAmount(), BigDecimal.ZERO);

            final Map<String, List<ServiceOrderPriceDetail>> map = prices.stream()
                                                                         .collect(Collectors.groupingBy(
                                                                                 ServiceOrderPriceDetail::getProductCode));

            for (List<ServiceOrderPriceDetail> value : map.values()) {
                ResourceDetailVO detailVO1 = new ResourceDetailVO();
                detailVO1.init();
                // 该时间为退订时间点（为和之前业务兼容，所以没有修改字段名）
                detailVO1.setComputeDate(now);
                // 该时间为原产品到期时间（为和之前业务兼容，所以没有修改字段名）
                detailVO1.setNow(resource.getEndTime());
                value.stream().filter(p -> OrderType.APPLY.equals(p.getType())).findFirst()
                     .ifPresent(d -> {
                         detailVO1.setOrderDetailId(d.getOrderDetailId());
                         detailVO1.setProjectId(d.getOrgSid());
                         detailVO1.setChargeType(d.getChargeType());
                     });
                detailVO1.setId(resource.getId().toString());
                detailVO1.setProductCode(value.get(0).getProductCode());
                // 资源
                totalPayment = detailVO1.getTotalPayment();
                totalOriginalCost = Convert.toBigDecimal(detailVO1.getTotalOriginalCost(), BigDecimal.ZERO);
                totalOrgDiscount = Convert.toBigDecimal(detailVO1.getTotalOrgDiscount(), BigDecimal.ZERO);
                totalCouponDiscount = Convert.toBigDecimal(detailVO1.getTotalCouponDiscount(), BigDecimal.ZERO);
                totalCouponAmount = Convert.toBigDecimal(detailVO1.getTotalCouponAmount(), BigDecimal.ZERO);
                totalCashAmount = Convert.toBigDecimal(detailVO1.getTotalCashAmount(), BigDecimal.ZERO);
                totalCreditAmount = Convert.toBigDecimal(detailVO1.getTotalCreditAmount(), BigDecimal.ZERO);
                usedAmount = detailVO1.getTotalUsedAmount();
                totalUsedCouponAmount = Convert.toBigDecimal(detailVO1.getTotalUsedCouponAmount(), BigDecimal.ZERO);
                totalUsedCashAmount = Convert.toBigDecimal(detailVO1.getTotalUsedCashAmount(), BigDecimal.ZERO);
                totalUsedCreditAmount = Convert.toBigDecimal(detailVO1.getTotalUsedCreditAmount(), BigDecimal.ZERO);
                // 服务
                servicePrice = new ServicePrice();
                serviceTotalPayment = servicePrice.getTotalPayment();
                serviceOriginalCost = Convert.toBigDecimal(servicePrice.getTotalOriginalCost(), BigDecimal.ZERO);
                serviceOrgDiscount = Convert.toBigDecimal(servicePrice.getTotalOrgDiscount(), BigDecimal.ZERO);
                serviceCouponDiscount = Convert.toBigDecimal(servicePrice.getTotalCouponDiscount(), BigDecimal.ZERO);
                serviceCouponAmount = Convert.toBigDecimal(servicePrice.getTotalCouponAmount(), BigDecimal.ZERO);
                serviceCashAmount = Convert.toBigDecimal(servicePrice.getTotalCashAmount(), BigDecimal.ZERO);
                serviceCreditAmount = Convert.toBigDecimal(servicePrice.getTotalCreditAmount(), BigDecimal.ZERO);
                serviceUsedAmount = servicePrice.getTotalUsed();
                serviceUsedCouponAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCouponAmount(), BigDecimal.ZERO);
                serviceUsedCashAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCashAmount(), BigDecimal.ZERO);
                serviceUsedCreditAmount = Convert.toBigDecimal(servicePrice.getTotalUsedCreditAmount(), BigDecimal.ZERO);

                for (ServiceOrderPriceDetail p : value) {
                    boolean modify = OrderType.MODIFY.equals(p.getType());
                    BigDecimal amount = p.getAmount();
                    BigDecimal originalCost = modify ? p.getAmount() : NumberUtil.div(p.getOriginalCost(), p.getQuantity());
                    BigDecimal discount = p.getDiscount();
                    BigDecimal couponDiscount = p.getCouponAmount();
                    //实际支付的余额
                    BigDecimal payBalance = Optional.ofNullable(p.getPayBalance()).orElse(BigDecimal.ZERO);
                    BigDecimal payBalanceCash = Optional.ofNullable(p.getPayBalanceCash()).orElse(BigDecimal.ZERO);
                    BigDecimal payCreditLine = Optional.ofNullable(p.getPayCreditLine()).orElse(BigDecimal.ZERO);

                    if (PriceType.RESOURCE.equals(p.getPriceType())) {
                        totalPayment = totalPayment.add(amount);
                        totalOriginalCost = NumberUtil.add(totalOriginalCost, originalCost);
                        totalOrgDiscount = NumberUtil.add(totalOrgDiscount, discount);
                        totalCouponDiscount = NumberUtil.add(totalCouponDiscount, couponDiscount);
                        totalCouponAmount = NumberUtil.add(totalCouponAmount, payBalanceCash);
                        totalCashAmount = NumberUtil.add(totalCashAmount, payBalance);
                        totalCreditAmount = NumberUtil.add(totalCreditAmount, payCreditLine);
                    }
                    if (PriceType.SERVICE.equals(p.getPriceType())) {
                        serviceTotalPayment = serviceTotalPayment.add(amount);
                        serviceOriginalCost = NumberUtil.add(serviceOriginalCost, originalCost);
                        serviceOrgDiscount = NumberUtil.add(serviceOrgDiscount, discount);
                        serviceCouponDiscount = NumberUtil.add(serviceCouponDiscount, couponDiscount);
                        serviceCouponAmount = NumberUtil.add(serviceCouponAmount, payBalanceCash);
                        serviceCashAmount = NumberUtil.add(serviceCashAmount, payBalance);
                        serviceCreditAmount = NumberUtil.add(serviceCreditAmount, payCreditLine);
                    }
                    Date priceDetailEndDate = p.getEndTime();
                    //订单明细到当前时间的的补偿天数
                    Integer priceDetailNextCompenstionDays = 0;
                    if (usedTime.after(priceDetailEndDate)) {
                        //当前订单补偿的天数
                        Integer days = this.statisticalCompensationDays(Long.valueOf(id), priceDetailEndDate, now);
                        if (days != null) {
                            priceDetailNextCompenstionDays = days;
                        }
                    }
                    //时间已经过了的订单或超期补扣的订单全额计算已使用金额
                    if (usedTime.after(DateUtils.addDays(priceDetailEndDate, priceDetailNextCompenstionDays)) || OrderType.EXPIRED.equals(
                            p.getType())) {
                        if (PriceType.RESOURCE.equals(p.getPriceType())) {
                            usedAmount = usedAmount.add(amount);
                            totalUsedCouponAmount = totalUsedCouponAmount.add(payBalanceCash);
                            totalUsedCashAmount = totalUsedCashAmount.add(payBalance);
                            totalUsedCreditAmount = totalUsedCreditAmount.add(payCreditLine);
                        }
                        if (PriceType.SERVICE.equals(p.getPriceType())) {
                            serviceUsedAmount = serviceUsedAmount.add(amount);
                            serviceUsedCouponAmount = serviceUsedCouponAmount.add(payBalanceCash);
                            serviceUsedCashAmount = serviceUsedCashAmount.add(payBalance);
                            serviceUsedCreditAmount = serviceUsedCreditAmount.add(payCreditLine);
                        }
                    } else {
                        //
                        if (!now.before(p.getStartTime())) {
                            //当前priceDetail月数
                            int month = 0;
                            Date tempStartDate = p.getStartTime();
                            while (tempStartDate.before(priceDetailEndDate)) {
                                tempStartDate = cn.hutool.core.date.DateUtil.offsetMonth(tempStartDate, 1);
                                month++;
                            }
                            //实际使用月数
                            int usedMonth = 0;
                            Date tempDate = p.getStartTime();
                            //先使用补偿天数，再计算实际使用月数
                            tempDate = DateUtils.addDays(tempDate, compenstionDays);
                            while (tempDate.before(now)) {
                                tempDate = cn.hutool.core.date.DateUtil.offsetMonth(tempDate, 1);
                                usedMonth++;
                            }
                            if (usedMonth == 0) {
                                if (PriceType.RESOURCE.equals(p.getPriceType())) {
                                    usedAmount = usedAmount.add(amount);
                                    totalUsedCouponAmount = totalUsedCouponAmount.add(payBalanceCash);
                                    totalUsedCashAmount = totalUsedCashAmount.add(payBalance);
                                    totalUsedCreditAmount = totalUsedCreditAmount.add(payCreditLine);
                                }
                                if (PriceType.SERVICE.equals(p.getPriceType())) {
                                    serviceUsedAmount = serviceUsedAmount.add(amount);
                                    serviceUsedCouponAmount = serviceUsedCouponAmount.add(payBalanceCash);
                                    serviceUsedCashAmount = serviceUsedCashAmount.add(payBalance);
                                    serviceUsedCreditAmount = serviceUsedCreditAmount.add(payCreditLine);
                                }
                                continue;
                            }
                            if (usedMonth > month) {
                                usedMonth = month;
                            }
                            // Calendar calendar = Calendar.getInstance();
                            // calendar.setTime(p.getStartTime());

                            //实际使用金额为支付金额减去退订的余额
                            BigDecimal backBanlance = NumberUtil.div(payBalance, month)
                                                                .multiply(new BigDecimal(month - usedMonth));
                            BigDecimal used = BigDecimalUtil.scaleAndRoundHalfUp(amount.subtract(backBanlance));

                            BigDecimal usedCash = BigDecimalUtil.scaleAndRoundHalfUp(payBalance.subtract(backBanlance));
                            BigDecimal backBanlanceCash = NumberUtil.div(payBalanceCash, month)
                                                                    .multiply(new BigDecimal(month - usedMonth));
                            BigDecimal usedBanlanceCash = BigDecimalUtil.scaleAndRoundHalfUp(
                                    payBalanceCash.subtract(backBanlanceCash));

                            BigDecimal backCreditLine = NumberUtil.div(payCreditLine, month)
                                                                  .multiply(new BigDecimal(month - usedMonth));
                            BigDecimal usedCreditLine = BigDecimalUtil.scaleAndRoundHalfUp(
                                    payCreditLine.subtract(backCreditLine));

                            used = NumberUtil.isGreater(used, amount) ? amount : used;
                            if (PriceType.RESOURCE.equals(p.getPriceType())) {
                                usedAmount = usedAmount.add(used);
                                totalUsedCouponAmount = totalUsedCouponAmount.add(usedBanlanceCash);
                                totalUsedCashAmount = totalUsedCashAmount.add(usedCash);
                                totalUsedCreditAmount = totalUsedCreditAmount.add(usedCreditLine);
                            }
                            if (PriceType.SERVICE.equals(p.getPriceType())) {
                                serviceUsedAmount = serviceUsedAmount.add(used);
                                serviceUsedCouponAmount = serviceUsedCouponAmount.add(usedBanlanceCash);
                                serviceUsedCashAmount = serviceUsedCashAmount.add(usedCash);
                                serviceUsedCreditAmount = serviceUsedCreditAmount.add(usedCreditLine);
                            }
                        } else {
//                        if (PriceType.RESOURCE.equals(p.getPriceType())) {
//                            usedAmount = usedAmount.add(amount);
//                            totalUsedCouponAmount = totalUsedCouponAmount.add(payBalanceCash);
//                            totalUsedCashAmount = totalUsedCashAmount.add(payBalance);
//                            totalUsedCreditAmount = totalUsedCreditAmount.add(payCreditLine);
//                        }
//                        if (PriceType.SERVICE.equals(p.getPriceType())) {
//                            serviceUsedAmount = serviceUsedAmount.add(amount);
//                            serviceUsedCouponAmount = serviceUsedCouponAmount.add(payBalanceCash);
//                            serviceUsedCashAmount = serviceUsedCashAmount.add(payBalance);
//                            serviceUsedCreditAmount = serviceUsedCreditAmount.add(payCreditLine);
//                        }
                        }
                    }
                }
                if (isExpired) {
                    Long maxOrderId = value.stream().map(ServiceOrderPriceDetail::getOrderId).max(Long::compare).orElse(-1L);
                    List<ServiceOrderPriceDetail> lastPrice = value.stream()
                                                                   .filter(p -> Objects.equals(maxOrderId, p.getOrderId())
                                                                           && !OrderType.EXPIRED.equals(p.getType()))
                                                                   .collect(Collectors.toList());
                    BigDecimal expiredUsedAmount = BigDecimal.ZERO;
                    BigDecimal serviceExpiredUsedAmount = BigDecimal.ZERO;
                    for (ServiceOrderPriceDetail p : lastPrice) {
                        BigDecimal degradeRate = BigDecimal.ONE;
                        Integer offDay = DateUtil.calculateOffDay(endTime, nonFrozen ? now : resource.getFrozenTime());
                        Calendar calendar = Calendar.getInstance();
                        Date priceEndTime = p.getEndTime();
                        Date lastMonthStart = DateUtils.addMonths(priceEndTime, -1);
                        calendar.setTime(lastMonthStart);
                        BigDecimal used = NumberUtil.div(p.getPrice(), calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                                    .multiply(BigDecimal.valueOf(offDay))
                                                    .multiply(BigDecimal.valueOf(p.getQuantity()))
                                                    .multiply(degradeRate)
                                                    .setScale(5, RoundingMode.FLOOR);
                        if (PriceType.RESOURCE.equals(p.getPriceType())) {
                            expiredUsedAmount = expiredUsedAmount.add(used);
                        }
                        if (PriceType.SERVICE.equals(p.getPriceType())) {
                            serviceExpiredUsedAmount = serviceExpiredUsedAmount.add(used);
                        }
                    }
                    detailVO1.setExpiredUsedAmount(expiredUsedAmount);
                    servicePrice.setExpiredUsedAmount(serviceExpiredUsedAmount);
                }
                detailVO1.setTotalPayment(totalPayment);
                detailVO1.setTotalOriginalCost(totalOriginalCost);
                detailVO1.setTotalOrgDiscount(totalOrgDiscount);
                detailVO1.setTotalCouponDiscount(totalCouponDiscount);
                detailVO1.setTotalCouponAmount(totalCouponAmount);
                detailVO1.setTotalCashAmount(totalCashAmount);
                detailVO1.setTotalCreditAmount(totalCreditAmount);
                detailVO1.setTotalUsedAmount(usedAmount);
                detailVO1.setTotalUsedCouponAmount(totalUsedCouponAmount);
                detailVO1.setTotalUsedCashAmount(totalUsedCashAmount);
                detailVO1.setTotalUsedCreditAmount(totalUsedCreditAmount);
                detailVO1.setUnsubAmount(totalPayment.subtract(usedAmount));
                detailVO1.setUnsubCouponAmount(totalCouponAmount.subtract(totalUsedCouponAmount));
                detailVO1.setUnsubCashAmount(totalCashAmount.subtract(totalUsedCashAmount));
                detailVO1.setUnsubCreditAmount(totalCreditAmount.subtract(totalUsedCreditAmount));
                servicePrice.setTotalPayment(serviceTotalPayment);
                servicePrice.setTotalOriginalCost(serviceOriginalCost);
                servicePrice.setTotalOrgDiscount(serviceOrgDiscount);
                servicePrice.setTotalCouponDiscount(serviceCouponDiscount);
                servicePrice.setTotalCouponAmount(serviceCouponAmount);
                servicePrice.setTotalCashAmount(serviceCashAmount);
                servicePrice.setTotalCreditAmount(serviceCreditAmount);
                servicePrice.setTotalUsed(serviceUsedAmount);
                servicePrice.setTotalUsedCouponAmount(serviceUsedCouponAmount);
                servicePrice.setTotalUsedCashAmount(serviceUsedCashAmount);
                servicePrice.setTotalUsedCreditAmount(serviceUsedCreditAmount);
                servicePrice.setUnsubAmount(serviceTotalPayment.subtract(serviceUsedAmount));
                servicePrice.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
                servicePrice.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
                servicePrice.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
                detailVO1.setServiceAmount(servicePrice);
                details.add(detailVO1);
            }
        }
        //汇总资源的价格
        Comparator<ResourceDetailVO> comparator = Comparator.comparingInt(o -> o.getProductCode().length());
        details.sort(comparator.reversed());
        vo.setDetail(details);
        vo.setTotalPayment(details.stream()
                                  .map(d -> d.getTotalPayment().add(d.getServiceAmount().getTotalPayment()))
                                  .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOriginalCost(details.stream()
                                       .map(d -> d.getTotalOriginalCost()
                                                  .add(d.getServiceAmount().getTotalOriginalCost()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalOrgDiscount(details.stream()
                                      .map(d -> d.getTotalOrgDiscount()
                                                 .add(d.getServiceAmount().getTotalOrgDiscount()))
                                      .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponDiscount(details.stream()
                                         .map(d -> d.getTotalCouponDiscount()
                                                    .add(d.getServiceAmount().getTotalCouponDiscount()))
                                         .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCouponAmount(details.stream()
                                       .map(d -> d.getTotalCouponAmount()
                                                  .add(d.getServiceAmount().getTotalCouponAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCashAmount(details.stream()
                                     .map(d -> d.getTotalCashAmount()
                                                .add(d.getServiceAmount().getTotalCashAmount()))
                                     .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalCreditAmount(details.stream()
                                       .map(d -> d.getTotalCreditAmount()
                                                  .add(d.getServiceAmount().getTotalCreditAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUsedAmount(details.stream()
                                .map(d -> d.getTotalUsedAmount().add(d.getServiceAmount().getTotalUsed()))
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCouponAmount(details.stream()
                                           .map(d -> d.getTotalUsedCouponAmount()
                                                      .add(d.getServiceAmount().getTotalUsedCouponAmount()))
                                           .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCashAmount(details.stream()
                                         .map(d -> d.getTotalUsedCashAmount()
                                                    .add(d.getServiceAmount().getTotalUsedCashAmount()))
                                         .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setTotalUsedCreditAmount(details.stream()
                                           .map(d -> d.getTotalUsedCreditAmount()
                                                      .add(d.getServiceAmount().getTotalUsedCreditAmount()))
                                           .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUnsubAmount(vo.getTotalCashAmount().subtract(vo.getTotalUsedCashAmount()));
        vo.setUnsubCouponAmount(vo.getTotalCouponAmount().subtract(vo.getTotalUsedCouponAmount()));
        vo.setUnsubCashAmount(vo.getTotalCashAmount().subtract(vo.getTotalUsedCashAmount()));
        vo.setUnsubCreditAmount(vo.getTotalCreditAmount().subtract(vo.getTotalUsedCreditAmount()));
        vo.setExpiredUsedAmount(details.stream()
                                       .map(d -> d.getExpiredUsedAmount().add(d.getServiceAmount().getExpiredUsedAmount()))
                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    /**
     * 获取补偿天数
     *
     * @param id
     * @param priceDetailEndDate 当前订单结束时间
     * @param nextPriceDetailStartDate 下个订单开始时间
     */
    private Integer statisticalCompensationDays(Long id, Date priceDetailEndDate, Date nextPriceDetailStartDate) {
        Integer resultDays = 0;
        LambdaQueryChainWrapper<SfProductResourceCompensation> chainWrapper = sfProductResourceCompensationService.lambdaQuery()
                                                                                                                  .eq(SfProductResourceCompensation::getSfProductResourceId,
                                                                                                                      id);
        if (priceDetailEndDate != null) {
            chainWrapper.ge(SfProductResourceCompensation::getStartTime, priceDetailEndDate);
        }
        if (nextPriceDetailStartDate != null) {
            chainWrapper.le(SfProductResourceCompensation::getEndTime, nextPriceDetailStartDate);
        }

        List<Integer> daysList = chainWrapper.list()
                                             .stream()
                                             .filter(sfProductResourceCompensation -> Objects.nonNull(
                                                     sfProductResourceCompensation.getCompensationDays()))
                                             .map(SfProductResourceCompensation::getCompensationDays)
                                             .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(daysList)) {
            for (Integer compesationDays : daysList) {
                resultDays += compesationDays;
            }
        }
        return resultDays;
    }

    private List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> getNodeInfos(Long clustarId, String code) {
        List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> nodeInfoList = new ArrayList<>();
        if (ProductCodeEnum.BMS.getProductType().equalsIgnoreCase(code)) {
            List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> collect =
                    Optional.ofNullable(resBmsRemoteService.getNodeInfoListByClusterId(clustarId)).orElseGet(ArrayList::new).stream()
                            .map(cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo.class::cast).collect(
                            Collectors.toList());
            nodeInfoList.addAll(collect);
        }
        if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(code)) {
            List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> collect =
                    Optional.ofNullable(resVmRemoteService.getNodeInfoListByClusterId(clustarId)).orElseGet(ArrayList::new).stream()
                            .map(cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo.class::cast).collect(
                            Collectors.toList());
            nodeInfoList.addAll(collect);
        }
        return nodeInfoList;
    }

    private BigDecimal calcDegradeRate(List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> nodeInfoList, Long clusterId,
                                       ServiceOrderPriceDetail priceDtail) {

        List<HpcBizContractDTO> hpcBizContractDTOList = serviceOrderService.getHpcBizContractDTOList(clusterId);
        nodeInfoList = removeContractNode(nodeInfoList, hpcBizContractDTOList, priceDtail);

        // 实际节点数
        int count = Math.toIntExact(
                nodeInfoList.stream().filter(nodeInfo -> StringUtil.equals(priceDtail.getServiceType(), nodeInfo.getTypeName())).count());
        Integer quantity = priceDtail.getQuantity();
        String productCode = priceDtail.getProductCode();
        // 原始节点缩容率
        BigDecimal degradeRate = getDegradeRate(count, quantity, productCode);
        return degradeRate;
    }

    private List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> removeContractNode(
            List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> nodeInfoList, List<HpcBizContractDTO> hpcBizContractDTOS,
            ServiceOrderPriceDetail priceDetail) {
        if (priceDetail.getProductCode().equals(ProductCodeEnum.BMS.getProductType())) {
            hpcBizContractDTOS = hpcBizContractDTOS.stream().filter(dto -> !dto.isZeroNode()).collect(Collectors.toList());
            int allContractAgentNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 0);
            //当合同中剩余节点数大于0时减去对应的节点数，
            if (allContractAgentNodeNum > 0) {
                nodeInfoList = nodeInfoList.stream().limit(nodeInfoList.size() - allContractAgentNodeNum).collect(Collectors.toList());
            }
        }
        if (priceDetail.getProductCode().equals(ProductCodeEnum.ECS.getProductType())) {
            int allContractCliNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 1);
            if (allContractCliNodeNum > 0) {
                List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> cliNodeInfoList = nodeInfoList.stream()
                                                                                                                      .filter(hpcNode -> HpcPointType.CCS_CLI
                                                                                                                              .equals(hpcNode.getHpcPointType()))
                                                                                                                      .collect(Collectors.toList());
                int remainNodeNum = cliNodeInfoList.size() - allContractCliNodeNum;
                //清空登陆节点
                nodeInfoList.removeIf(nodeInfo -> HpcPointType.CCS_CLI.equals(nodeInfo.getHpcPointType()));
                if (remainNodeNum > 0) {
                    cliNodeInfoList = cliNodeInfoList.stream().limit(remainNodeNum).collect(Collectors.toList());
                    nodeInfoList.addAll(cliNodeInfoList);
                }
            }
            int allContractVNCNodeNum = serviceOrderService.getAllContractNode(hpcBizContractDTOS, 2);
            if (allContractVNCNodeNum > 0) {
                List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo> vncNodeInfoList = nodeInfoList.stream()
                                                                                                                      .filter(hpcNode -> HpcPointType.VNC
                                                                                                                              .equals(hpcNode.getHpcPointType()))
                                                                                                                      .collect(Collectors.toList());
                int remainNodeNum = vncNodeInfoList.size() - allContractVNCNodeNum;
                nodeInfoList.removeIf(nodeInfo -> HpcPointType.VNC.equals(nodeInfo.getHpcPointType()));
                //清空VNC节点
                if (remainNodeNum > 0) {
                    vncNodeInfoList = vncNodeInfoList.stream().limit(remainNodeNum).collect(Collectors.toList());
                    nodeInfoList.addAll(vncNodeInfoList);
                }
            }
        }
        return nodeInfoList;
    }

    private BigDecimal getDegradeRate(Integer count, Integer quantity, String productCode) {
        BigDecimal degradeRate = BigDecimal.ONE;
        if (!StringUtil.equalsIgnoreCase(productCode, ProductCodeEnum.HPC_DRP.getProductType())) {
            if (Objects.isNull(count)) {
                return degradeRate;
            }
            if (count < quantity) {
                degradeRate = NumberUtil.div(BigDecimal.valueOf(count), quantity);
            }
        }
        return degradeRate;
    }

    private void generateAccountSpecChargeDetail(AccountPriceGroupDetailVO priceGroup, Map<String, List<BizBillingSpec>> specIdMap,
                                                 BizBillingTariffSpecCharge specCharge, BizBillingTariffSpec tariffSpec) {
        AccountPriceSpecDetailVO priceSpec = new AccountPriceSpecDetailVO();
        priceGroup.getSpecs().add(priceSpec);

        String[] specIds = tariffSpec.getSpecIds().split(",");
        JSONObject billingConfig = JSON.parseObject(specCharge.getBillingConfig());

        if (BillingConstants.BillingTrafficSpecChargeType.PERIOD.equals(tariffSpec.getChargeCycle())) {
            // 周期计费
            if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                // 增量计费
                List<String> specItems = Lists.newArrayListWithCapacity(1);
                for (String specId : specIds) {
                    if (Objects.isNull(specIdMap.get(specId))) {
                        continue;
                    }
                    BizBillingSpec spec = specIdMap.get(specId).get(0);
                    if (spec.getIsCharge()) {
                        String specValue = billingConfig.getString(spec.getSpecName());
                        specItems.add(String.format("%s: %s%s", nameMap(spec.getSpecName()), specValue, spec.getUnit()));
                    }
                }
                priceSpec.setSpecName(String.join("、", specItems));
                priceSpec.getItems().add(String.format(
                        "按量计费：%s+(N*%s)元/小时", keepFourDecimals(specCharge.getFixedHourPrice()),
                        keepFourDecimals(specCharge.getHourPrice())));
                priceSpec.getItems().add(String.format(
                        "包年包月：%s+(N*%s)元/月", keepFourDecimals(specCharge.getFixedMonthPrice()),
                        keepFourDecimals(specCharge.getMonthPrice())));
            } else if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                // 固定计费
                List<String> specItems = Lists.newArrayListWithCapacity(1);
                for (String specId : specIds) {
                    if (Objects.isNull(specIdMap.get(specId))) {
                        continue;
                    }
                    BizBillingSpec spec = specIdMap.get(specId).get(0);
                    if (spec.getIsCharge()) {
                        String specValue = billingConfig.getString(spec.getSpecName());
                        specItems.add(String.format("%s: %s%s", nameMap(spec.getSpecName()), specValue, spec.getUnit()));
                    }
                }
                priceSpec.setSpecName(String.join("、", specItems));
                priceSpec.getItems().add(String.format("按量计费：%s元/小时",
                                                       keepFourDecimals(specCharge.getHourPrice())));
                priceSpec.getItems().add(String.format("包年包月：%s元/月",
                                                       keepFourDecimals(specCharge.getMonthPrice())));
            }
        } else if (BillingConstants.BillingTrafficSpecChargeType.ONCE.equals(tariffSpec.getChargeCycle())) {
            if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                List<String> specItems = Lists.newArrayListWithCapacity(1);
                for (String specId : specIds) {
                    if (Objects.isNull(specIdMap.get(specId))) {
                        continue;
                    }
                    BizBillingSpec spec = specIdMap.get(specId).get(0);
                    if (spec.getIsCharge()) {
                        String specValue = billingConfig.getString(spec.getSpecName());
                        specItems.add(String.format("%s: %s%s", nameMap(spec.getSpecName()), specValue, spec.getUnit()));
                    }
                }
                priceSpec.setSpecName(String.join("、", specItems));
                priceSpec.getItems().add(String.format(
                        "按量计费：%s+(N*%s)元", keepFourDecimals(specCharge.getFixedOncePrice()),
                        keepFourDecimals(specCharge.getOncePrice())));
            } else if (BillingConstants.BillingChargeType.STATIC_TYPE.equals(tariffSpec.getChargeStrategy())) {
                List<String> specItems = Lists.newArrayListWithCapacity(1);
                for (String specId : specIds) {
                    if (Objects.isNull(specIdMap.get(specId))) {
                        continue;
                    }
                    BizBillingSpec spec = specIdMap.get(specId).get(0);
                    if (spec.getIsCharge()) {
                        String specValue = billingConfig.getString(spec.getSpecName());
                        specItems.add(String.format("%s: %s%s", nameMap(spec.getSpecName()), specValue, spec.getUnit()));
                    }
                }
                priceSpec.setSpecName(String.join("、", specItems));
                priceSpec.getItems().add(String.format("固定计费：%s元", keepFourDecimals(specCharge.getOncePrice())));
            }
        }
    }

    private String nameMap(String name) {
        String value = RESOURCE_NAME_MAP.get(name);
        if (Objects.isNull(value)) {
            return name;
        }
        return value;
    }

    /**
     * 保留4位小树
     *
     * @param price
     */
    public static double keepFourDecimals(BigDecimal price) {
        if (Objects.isNull(price)) {
            return 0.0;
        }
        return (double) Math.round(price.doubleValue() * KEEP_FOUR_DECIMALS) / KEEP_FOUR_DECIMALS;
    }

    /**
     * 保留3位小树
     *
     * @param price
     */
    public static BigDecimal keepThreeDecimals(BigDecimal price) {
        if (Objects.isNull(price)) {
            // BUG:29903 金额精确到小数点后5位数
            return BigDecimal.valueOf(0.00000D);
        }
        return price.setScale(DECIMAL_FIX, RoundingMode.HALF_UP);
    }

    /**
     * 根据服务获取主机模板ID
     *
     * @param serviceCategory
     */
    private Long getServerTemplateId(ServiceCategory serviceCategory) {
        if (RESOURCE_MAP.containsKey(serviceCategory.getServiceType())) {
            Yaml yaml = new Yaml(new SafeConstructor(new LoaderOptions()));
            Map<String, Object> config = yaml.load(serviceCategory.getServiceConfig());
            Map<String, Object> topologyTemplate = (Map<String, Object>) config.get("topology_template");
            if (Objects.isNull(topologyTemplate)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_661138946));
            }

            Map<String, Object> nodeTemplates = (Map<String, Object>) topologyTemplate.get("node_templates");
            if (Objects.isNull(nodeTemplates)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_661138946));
            }

            Map<String, Object> resVm = (Map<String, Object>) nodeTemplates.get(RESOURCE_MAP.get(serviceCategory.getServiceType()));
            if (Objects.isNull(resVm)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_661138946));
            }

            List<Object> templates = (List<Object>) resVm.get("templates");
            if (CollectionUtils.isEmpty(templates)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_661138946));
            }

            Map<String, Object> template = (Map<String, Object>) templates.get(0);

            Integer id = (Integer) template.get("id");

            if (Objects.nonNull(id)) {
                return id.longValue();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 计算折扣价 折扣策略和具体的折扣
     *
     * @param result
     */
    private InquiryPriceResult setDiscountsAmount(String chargeType, List<String> products, Long envId, BigDecimal period, Integer amount,
                                                  Long accountId, InquiryPriceResult result) {
        result.setPlatformDiscount(BigDecimal.ONE);
        // 判断应用范围为金额时，价格是否在应用范围内
        BigDecimal price;
        if (BillingConstants.ChargeType.PRE_PAID.equals(chargeType)) {
            price = result.getTradeMonthPrice();
        } else {
            price = result.getTradeHourPrice();
        }
        if (Objects.nonNull(result.getPeriodTime())) {
            period = result.getPeriodTime();
        }
        // getPortalDiscount方法主要计算折扣
        List<DiscountDetailVO> detailVOS = bizDiscountService.getPortalDiscount(products, envId, period, amount, accountId, price,
                                                                                result.getPointInTime());
        if (CollectionUtil.isNotEmpty(detailVOS)) {
            // 费用计算折扣
            if (Objects.nonNull(detailVOS.get(0).getDiscount())
                    && detailVOS.get(0).getDiscount().doubleValue() > 0.0
                    && detailVOS.get(0).getDiscount().doubleValue() <= 1.0) {
                result.setPlatformDiscount(detailVOS.get(0).getDiscount());
                result.setTradeMonthPrice(
                        result.getTradeMonthPrice().multiply(detailVOS.get(0).getDiscount()).setScale(DECIMAL_FIX, BigDecimal.ROUND_UP));
                result.setTradeHourPrice(
                        result.getTradeHourPrice().multiply(detailVOS.get(0).getDiscount()).setScale(DECIMAL_FIX, BigDecimal.ROUND_UP));
            }
        }

        List<BizBillingPriceVO> billingPrices = result.getBillingPrices();
        for (BizBillingPriceVO priceVO : billingPrices) {
            priceVO.setPlatformDiscount(result.getPlatformDiscount());
        }
        // 当金额大于0时 设置折扣返回
        if (result.getOriginalMonthPrice().compareTo(BigDecimal.ZERO) > 0
                || result.getOriginalHourPrice().compareTo(BigDecimal.ZERO) > 0) {
            result.setDiscounts(detailVOS.stream().filter(detail -> detail.getDiscount() != null && detail.getDiscount().doubleValue() < 1.0)
                                         .collect(Collectors.toList()));
        }

        return result;

    }

    private void calculatePrice(BigDecimal period, BizBillingPriceVO priceVO,
                                BizBillingTariffSpecCharge specConfigCharge) {
        priceVO.setHourPrice(priceVO.getHourPrice().add(specConfigCharge.getHourPrice()));
        priceVO.setMonthPrice(priceVO.getMonthPrice().add(specConfigCharge.getMonthPrice()));

        if (StrUtil.containsIgnoreCase(priceVO.getPriceDesc(), "dcs")
                || StrUtil.startWith(priceVO.getBillingSpec(), "redis")
                || StrUtil.startWith(priceVO.getResourceType(), "redis")
        ) {
            log.info("获取dcs规格,billingSpec:{},resourceType:{}", priceVO.getBillingSpec(), priceVO.getResourceType());
            String spec = StrUtil.isNotBlank(priceVO.getBillingSpec()) ? priceVO.getBillingSpec() : priceVO.getResourceType();
            log.info("获取dcs规格{}", spec);
            String[] parts = spec.split("\\.");
            String part1 = parts[parts.length - 2];

            if (part1.startsWith("r") || part1.startsWith("p")) {
                String number = part1.substring(1);
                if (StrUtil.isNotBlank(number)) {
                    BigDecimal bigDecimal = new BigDecimal(number);
                    priceVO.setHourPrice(NumberUtil.mul(priceVO.getHourPrice(), bigDecimal));
                    priceVO.setMonthPrice(NumberUtil.mul(priceVO.getMonthPrice(), bigDecimal));
                }
            }
        }

        // 如果包年包月存在指定月数价格
        QueryWrapper<BizBillingChargeMonth> chargeMonthQuery = new QueryWrapper<>();
        chargeMonthQuery.lambda().eq(BizBillingChargeMonth::getSpecChargeId, specConfigCharge.getId());
        List<BizBillingChargeMonth> chargeMonths = bizBillingChargeMonthService.list(chargeMonthQuery);
        if (CollectionUtil.isNotEmpty(chargeMonths)) {
            chargeMonths.forEach(chargeMonth -> {
                if (Objects.nonNull(period) && chargeMonth.getDuration().equals(period.intValue())) {
                    priceVO.setAppoint(Boolean.TRUE);
                    priceVO.setMonthPrice(chargeMonth.getPrice());
                }
            });
        }
    }

    private void calculateComputePrice(InquiryInstancePrice inquiryInstancePrice, BizBillingPriceVO priceVO,
                                       BizBillingTariffSpecCharge specConfigCharge) {
        priceVO.setHourPrice(priceVO.getHourPrice().add(specConfigCharge.getHourPrice()));
        priceVO.setMonthPrice(priceVO.getMonthPrice().add(specConfigCharge.getMonthPrice()));
        // 如果包年包月存在指定月数价格
        QueryWrapper<BizBillingChargeMonth> chargeMonthQuery = new QueryWrapper<>();
        chargeMonthQuery.lambda().eq(BizBillingChargeMonth::getSpecChargeId, specConfigCharge.getId());
        List<BizBillingChargeMonth> chargeMonths = bizBillingChargeMonthService.list(chargeMonthQuery);
        if (CollectionUtil.isNotEmpty(chargeMonths)) {
            chargeMonths.forEach(chargeMonth -> {
                if (inquiryInstancePrice.getPeriod() != null && chargeMonth.getDuration().equals(inquiryInstancePrice.getPeriod().intValue())) {
                    priceVO.setAppoint(Boolean.TRUE);
                    priceVO.setMonthPrice(chargeMonth.getPrice());
                }
            });
        }
    }


    /**
     * 匹配规格
     *
     * @param specChargeMap
     * @param billingMap
     */
    private boolean matchPriceSpec(Map<String, String> specChargeMap, Map<String, Object> billingMap) {
        String productCode = Convert.toStr(billingMap.get("productCode"));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(productCode) && ProductCodeEnum.MA_BMS.getProductType().equals(productCode)) {
            return true;
        }

        if (Boolean.TRUE.equals(billingMap.get("allSpecDisplay")) && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productCode)
                && billingMap.get("spec") == null) {
            return true;
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(productCode) && ProductCodeEnum.CSS.getProductType().equals(productCode)) {
            return true;
        }

        /*if (org.apache.commons.lang3.StringUtils.isNotBlank(productCode) && ProductCodeEnum.DCS.getProductType().equals(productCode)) {
            return true;
        }*/

        Set<String> keySet = specChargeMap.keySet();
        int size = keySet.size();
        for (Entry<String, Object> entry : billingMap.entrySet()) {
            String spec = entry.getKey();
            Object value = entry.getValue();
            if (Objects.isNull(value)) {
                continue;
            }
            String v1 = value.toString();
            String v2 = specChargeMap.get(spec);
            if (Objects.equals(v1, v2)) {
                size--;
            }
        }

        return size == 0;
    }

    /**
     * 设置存储、网络按量、包月公式参数
     *
     * @param specCharge
     * @param paramValue
     * @param priceVO
     * @param inquiryPriceResult
     * @param expression
     */
    private void computePrice(BizBillingTariffSpecCharge specCharge, Object paramValue,
                              BizBillingPriceVO priceVO, InquiryPriceResult inquiryPriceResult, String expression) {
        BigDecimal hourPrice = BigDecimal.ZERO;
        BigDecimal monthPrice = BigDecimal.ZERO;
        Map<String, Object> hourMap = Maps.newHashMap();
        hourMap.put(X, specCharge.getFixedHourPrice());
        hourMap.put(N, paramValue);
        hourMap.put(Y, specCharge.getUnitHourPrice());

        Map<String, Object> monthMap = Maps.newHashMap();
        monthMap.put(X, specCharge.getFixedMonthPrice());

        monthMap.put(N, paramValue);
        monthMap.put(Y, specCharge.getUnitMonthPrice());

        hourPrice = hourPrice.add(ComputeUtil.computeExpress(expression, hourMap).setScale(DECIMAL_FIX, RoundingMode.HALF_UP));
        monthPrice = monthPrice.add(ComputeUtil.computeExpress(expression, monthMap).setScale(DECIMAL_FIX, RoundingMode.HALF_UP));

        priceVO.setHourPrice(priceVO.getHourPrice().add(hourPrice));
        priceVO.setMonthPrice(priceVO.getMonthPrice().add(monthPrice));
        inquiryPriceResult.getBillingPrices().add(priceVO);
    }

    /**
     * 设置存储、网络用量、计量公式参数
     *
     * @param specCharge
     * @param paramValue
     * @param priceVO
     * @param inquiryPriceResult
     * @param expression
     */
    private void computeUsagePrice(BizBillingTariffSpecCharge specCharge, Object paramValue,
                                   BizBillingPriceVO priceVO, InquiryPriceResult inquiryPriceResult, String expression) {
        Map<String, Object> valueMap = Maps.newHashMap();
        valueMap.put(X, specCharge.getUsageCount());
        valueMap.put(N, paramValue);
        valueMap.put(Y, specCharge.getUsageCountPrice());

        BigDecimal price = ComputeUtil.computeExpress(expression, valueMap).setScale(DECIMAL_FIX, RoundingMode.HALF_UP);
        priceVO.setOncePrice(priceVO.getOncePrice().add(price));
        inquiryPriceResult.getBillingPrices().add(priceVO);
    }

    private boolean compareConfig(ProductInfo productInfo, ModifyInquiryPriceRequest request) {

        boolean flag = true;
        if (Objects.nonNull(request.getTargetSize())) {
            return Objects.equals(productInfo.getCurrentSize(), request.getTargetSize());
        }
        if (!Strings.isNullOrEmpty(request.getTargetType())) {
            return Objects.equals(productInfo.getCurrentType(), request.getTargetType());
        }
        return flag;
    }

    /**
     * 单价折扣计算
     *
     * @param envId
     * @param accountId
     * @param unitPrice
     * @param productCode
     */
    private BigDecimal unitPriceDiscount(Long envId, Long accountId, BigDecimal unitPrice, String productCode, String chargeType, Date pointInTime) {
        BigDecimal discount = BigDecimal.ONE;
        List<String> products = new ArrayList<String>() {
            {
                this.add(productCode);
            }
        };
        List<DiscountDetailVO> detailVOS = bizDiscountService.getPortalDiscount(
                products, envId, BigDecimal.ONE, 1, accountId, unitPrice, pointInTime);
        if (CollectionUtil.isNotEmpty(detailVOS)) {
            // 费用计算折扣
            if (Objects.nonNull(detailVOS.get(0).getDiscount())
                    && detailVOS.get(0).getDiscount().doubleValue() > 0.0
                    && detailVOS.get(0).getDiscount().doubleValue() <= 1.0) {
                discount = detailVOS.get(0).getDiscount();
            }
        }
        return discount;
    }

    @Override
    public List<InquiryPriceResponse> inquiryProductPrice(ApplyServiceVO applyServiceVO) {
        // 询价
        List<InquiryPriceResponse> result = Lists.newArrayList();
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        boolean multiProduct = 1 < applyServiceVO.getProductInfo().size();
        applyServiceVO.getProductInfo().forEach(productInfoVO -> {
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productInfoVO.getProductCode())) {
                if (!ObjectUtils.isEmpty(authUser.getOrgSid()) && !authUser.getOrgSid().equals(applyServiceVO.getProjectId())) {
                    Org org = orgMapper.selectById(applyServiceVO.getProjectId());
                    if (Objects.nonNull(org)) {
                        if (!ObjectUtils.isEmpty(authUser.getOrgSid()) && !authUser.getOrgSid().equals(org.getParentId())) {
                            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                        }
                    }
                }
            }

            if (!applyServiceVO.isSummaryFlg() && !multiProduct) {
                productInfoVO.setCouponId(applyServiceVO.getCouponId());
            }
            productInfoVO.setBillingAccountId(applyServiceVO.getAccountId());
            productInfoVO.setEntityId(applyServiceVO.getEntityId());
            productInfoVO.setEntityName(applyServiceVO.getEntityName());
            productInfoVO.setClusterId(applyServiceVO.getClusterId());

            productInfoVO.setPointInTime(applyServiceVO.getPointInTime());
            //SFS2.0 dpc&nfs 默认替换为 nfs&dpc
            Object data = productInfoVO.getData();
            if (data != null) {
                String jsonString = JSON.toJSONString(data);
                String shareProto = "dpc&nfs";
                if (StringUtil.isNotBlank(jsonString) && (jsonString.contains(shareProto))) {
                    jsonString = jsonString.replaceAll(shareProto, "nfs&dpc");
                    productInfoVO.setData(JSON.parseObject(jsonString));
                }
            }

            // 询价关键代码
            result.add(convertPriceResponse(inquiryPrice(productInfoVO), productInfoVO.getChargeType()));
        });
        if (applyServiceVO.getCouponId() != null) {
            BizCoupon bizCoupon = bizCouponMapper.selectById(applyServiceVO.getCouponId());
            if (bizCoupon != null && Objects.nonNull(bizCoupon.getReductionCondition())) {
                BigDecimal tradePrice = result.stream().map(InquiryPriceResponse::getTradePrice).filter(Objects::nonNull).reduce(BigDecimal::add)
                                              .orElse(BigDecimal.ZERO);
                if (tradePrice.add(bizCoupon.getDiscountAmount()).compareTo(bizCoupon.getReductionCondition()) < 0) {
                    throw new BizException("未满足所选优惠券使用条件.");
                }
            }
        }

        if (applyServiceVO.isSummaryFlg()) {
            if (Objects.nonNull(applyServiceVO.getCouponId())) {
                // 多产品优惠卷分摊
                multipleProductCouponPrice(result, bizCouponMapper.selectById(applyServiceVO.getCouponId()).getDiscountAmount());
            }
            // 同类型产品汇总
            summaryProductPrice(result);
        } else if (multiProduct) {
            if (Objects.nonNull(applyServiceVO.getCouponId())) {
                // 多产品优惠卷分摊
                multipleProductCouponPrice(result, bizCouponMapper.selectById(applyServiceVO.getCouponId()).getDiscountAmount());
            }
        }

        return result;
    }

    @Override
    public void multipleProductCouponPrice(List<InquiryPriceResponse> inquiryPriceResponses, BigDecimal couponPrice) {

        BigDecimal couponDisPrice = BigDecimal.ZERO;
        // 折后总金额
        BigDecimal tradePrice = inquiryPriceResponses.stream().map(InquiryPriceResponse::getTradePrice).reduce(BigDecimal::add)
                                                     .orElse(BigDecimal.ZERO);
        for (InquiryPriceResponse priceResponse : inquiryPriceResponses) {
            BigDecimal currentCouponPrice = BigDecimal.ZERO;
            if (couponPrice.compareTo(tradePrice) >= 0) {
                // 优惠券金额大于总金额的时候，减去全部金额
                couponPrice = tradePrice;
                currentCouponPrice = priceResponse.getTradePrice();
                couponDisPrice = NumberUtil.add(couponDisPrice, currentCouponPrice);
            } else if (couponPrice.compareTo(BigDecimal.ZERO) > 0) {
                currentCouponPrice = NumberUtil.mul(
                        NumberUtil.div(priceResponse.getTradePrice(), tradePrice), couponPrice)
                                               .setScale(2, BigDecimal.ROUND_DOWN);
                couponDisPrice = NumberUtil.add(couponDisPrice, currentCouponPrice);
            }

            priceResponse.setTradePrice(NumberUtil.sub(priceResponse.getTradePrice(), currentCouponPrice));
            priceResponse.setDiscountPrice(NumberUtil.add(priceResponse.getDiscountPrice(), currentCouponPrice));
            priceResponse.setCouponAmount(currentCouponPrice);
        }

        if (couponPrice.compareTo(couponDisPrice) > 0) {
            // 补足没有扣除的优惠卷(指定一个产品补足)
            supplementCouponAmount(inquiryPriceResponses, inquiryPriceResponses.get(0).getProductCode(), NumberUtil.sub(couponPrice, couponDisPrice));
        }
    }

    /**
     * 补足没有扣除的优惠卷
     *
     * @param result
     * @param productCode
     * @param disCouponAmount
     */
    private void supplementCouponAmount(List<InquiryPriceResponse> result, String productCode, BigDecimal disCouponAmount) {
        boolean skipFlg = false;
        for (InquiryPriceResponse inquiryPriceResponse : result) {
            if (skipFlg || Objects.equals(inquiryPriceResponse.getProductCode(), productCode)) {
                if (inquiryPriceResponse.getTradePrice().compareTo(disCouponAmount) < 0) {
                    skipFlg = true;
                    continue;
                }
                inquiryPriceResponse.setTradePrice(NumberUtil.sub(inquiryPriceResponse.getTradePrice(), disCouponAmount));
                inquiryPriceResponse.setDiscountPrice(NumberUtil.add(inquiryPriceResponse.getDiscountPrice(), disCouponAmount));
                inquiryPriceResponse.setCouponAmount(NumberUtil.add(inquiryPriceResponse.getCouponAmount(), disCouponAmount));
                return;
            }
        }
    }

    /**
     * 相同产品价格汇总
     *
     * @param inquiryPriceResponses
     */
    private void summaryProductPrice(List<InquiryPriceResponse> inquiryPriceResponses) {
        Map<String, List<InquiryPriceResponse>> inquiryPriceResponseMap = inquiryPriceResponses.stream()
                                                                                               .collect(Collectors.groupingBy(
                                                                                                       InquiryPriceResponse::getProductCode));

        List<InquiryPriceResponse> summaryInquiryPriceResponses = Lists.newArrayList();
        inquiryPriceResponseMap.forEach((key, value) -> {
            InquiryPriceResponse inquiryPriceResponse = null;
            for (InquiryPriceResponse priceResponse : value) {
                if (Objects.isNull(inquiryPriceResponse)) {
                    inquiryPriceResponse = priceResponse;
                } else {
                    inquiryPriceResponse.setOriginalPrice(NumberUtil.add(inquiryPriceResponse.getOriginalPrice(), priceResponse.getOriginalPrice()));
                    inquiryPriceResponse.setResourceAmount(
                            NumberUtil.add(inquiryPriceResponse.getResourceAmount(), priceResponse.getResourceAmount()));
                    inquiryPriceResponse.setServiceAmount(NumberUtil.add(inquiryPriceResponse.getServiceAmount(), priceResponse.getServiceAmount()));
                    inquiryPriceResponse.setTradePrice(NumberUtil.add(inquiryPriceResponse.getTradePrice(), priceResponse.getTradePrice()));
                    inquiryPriceResponse.setDiscountPrice(NumberUtil.add(inquiryPriceResponse.getDiscountPrice(), priceResponse.getDiscountPrice()));
                    inquiryPriceResponse.setExpiredUsedAmount(
                            NumberUtil.add(inquiryPriceResponse.getExpiredUsedAmount(), priceResponse.getExpiredUsedAmount()));
                }
            }
            summaryInquiryPriceResponses.add(inquiryPriceResponse);
        });
        inquiryPriceResponses.clear();
        inquiryPriceResponses.addAll(summaryInquiryPriceResponses);
    }

    private boolean isHpcDrpFromDetail(List<ServiceOrderDetail> details) {
        Boolean hpcDrpFlg = false;
        List<String> productInfos = details.stream().map(ServiceOrderDetail::getServiceType).collect(Collectors.toList());
        if (productInfos.contains(cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.HPC_DRP.getProductCode())) {
            hpcDrpFlg = true;
        }
        return hpcDrpFlg;
    }

    @Override
    public ApplyServiceRequest createPostPaidApplyServiceRequst(ServiceOrder serviceOrder) {
        Long bizBillingAccountId = serviceOrder.getBizBillingAccountId();
        Assert.notNull(bizBillingAccountId);

        BizBillingAccount account = bizBillingAccountService.getById(bizBillingAccountId);

        ApplyServiceRequest applyServiceRequest = new ApplyServiceRequest();

        applyServiceRequest.setCouponId(null);
        applyServiceRequest.setProjectId(serviceOrder.getOrgSid());
        applyServiceRequest.setOrderType("apply-other_service");
        applyServiceRequest.setEntityId(account.getEntityId());

        List<ProductInfoVO> productInfoVOList = new ArrayList<>();
        serviceOrder.getOrderDetails().stream().forEach(orderDetail -> {
            String serviceConfig = orderDetail.getServiceConfig();
            JSONObject data = JSONObject.parseObject(serviceConfig);
            Long cloudEnvId = data.getLong("cloudEnvId");
            ProductInfoVO productInfoVO = JSONObject.parseObject(serviceConfig, ProductInfoVO.class);
//            ProductInfoVO productInfoVO = new ProductInfoVO();
            productInfoVOList.add(productInfoVO);
//            productInfoVO.setBillingAccountId(account.getId());
//            productInfoVO.setAmount(1);
//            productInfoVO.setChargeType(cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum.POSTPAID.getValue());
//            productInfoVO.setServiceId(orderDetail.getServiceId());
//            productInfoVO.setOrderId(serviceOrder.getId().toString());
//            productInfoVO.setPeriod(BigDecimal.ONE);
//            productInfoVO.setPeriodUnit(HOUR);
//            productInfoVO.setCloudEnvId(cloudEnvId);
//            productInfoVO.setProductCode(orderDetail.getServiceType());
        });
        applyServiceRequest.setProductInfo(productInfoVOList);
        applyServiceRequest.setUserSid(account.getAdminSid());
        return applyServiceRequest;
    }


    @Override
    public UnsubscribeInquiryPriceVO unsubInquiryResPrice(String id) {
        SfProductResource sfProductResource = sfProductResourceService.getById(id);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(sfProductResource.getProductType());
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
        if (Objects.isNull(authUserInfo.getOrgSid()) && !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<EntityDTO> serviceTypes = serviceCategoryService.getByServiceType(sfProductResource.getProductType());
        if (Constants.BSS.equals(authUser.getRemark()) && CollectionUtil.isNotEmpty(serviceTypes) && !serviceTypes.get(0)
                                                                                                                  .getEntityId()
                                                                                                                  .equals(authUser.getEntityId())) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
        }
        if (Objects.isNull(sfProductResource)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1493182351));
        }

        if (ProductCodeEnum.ECS.getProductType().equals(sfProductResource.getProductType())
                || ProductCodeEnum.RDS.getProductType().equals(sfProductResource.getProductType())
                || ProductCodeEnum.RS_BMS.getProductType().equals(sfProductResource.getProductType())) {
            return computeUnsubPriceWithResource(sfProductResource);
        }

        //查询开通的订单
        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + id + "\"");
        param.put("orderTypes", Lists.newArrayList("apply"));
        param.put("orderStatus", "completed");
        param.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfoByParam(param);
        ServiceOrderDetail serviceOrderDetail = new ServiceOrderDetail();
        if (orderPriceDetails.size() > 0) {
            //查询开通订单
            if (Objects.isNull(orderPriceDetails.get(0).getOrderDetailId())) {
                List<ServiceOrderDetail> serviceOrderDetails = serviceOrderMapper.selectOrderDetailByClusterId(sfProductResource.getProductType(),
                                                                                                               sfProductResource.getClusterId());
                serviceOrderDetail = serviceOrderDetails.get(0);
            } else {
                serviceOrderDetail = serviceOrderDetailService
                        .getById(orderPriceDetails.get(0).getOrderDetailId());
            }
        } else {
            List<ServiceOrderDetail> serviceOrderDetails = serviceOrderMapper.selectOrderDetailByClusterId(sfProductResource.getProductType(),
                                                                                                           sfProductResource.getClusterId());
            if (CollectionUtils.isEmpty(serviceOrderDetails)) {
                serviceOrderDetails = serviceOrderMapper.selectDetailByResId(String.valueOf(sfProductResource.getId()));
            }
            serviceOrderDetail = serviceOrderDetails.get(0);
        }

        // 查询所有订单
        Criteria other = new Criteria();
        other.put("refInstanceIdLike", "\"" + id + "\"");
//        other.put("types", Lists.newArrayList("renew","degrade","upgrade"));
        other.put("types", Lists.newArrayList("renew", "degrade", "upgrade", "modify"));
        other.put("orderStatus", "completed");
        other.put("productCode", sfProductResource.getProductType());
        List<ServiceOrderPriceDetail> otherOrderPriceDetails = serviceOrderPriceDetailMapper
                .selectOrderInfo(other);
        // 实付金额
        ResourceInquiryPriceVO payAmount = computePayPrice(serviceOrderDetail, otherOrderPriceDetails);
        // 直接用退订询价接口
        UnsubInquiryPriceVO unsubInquiryPriceVO = new UnsubInquiryPriceVO();
        this.computeInnerProject(serviceOrderDetail, id, unsubInquiryPriceVO);
        // 已使用的金额
        ResourcePayPriceVO usedAmount = computeUsedPrice(payAmount, serviceOrderDetail, sfProductResource);
        // 退订金额
        ResourcePayPriceVO unsubAmount = new ResourcePayPriceVO();
        unsubAmount.setCashAmount(NumberUtil.sub(payAmount.getCashAmount(),
                                                 usedAmount.getCashAmount().setScale(5, BigDecimal.ROUND_HALF_UP))
                                            .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCreditAmount(NumberUtil.sub(payAmount.getCreditAmount(),
                                                   usedAmount.getCreditAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCouponAmount(NumberUtil.sub(payAmount.getCouponAmount(),
                                                   usedAmount.getCouponAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setAmount(unsubAmount.getCashAmount());
        //金额统计
        UnsubscribeInquiryPriceVO response = new UnsubscribeInquiryPriceVO();
        response.setPayAmount(payAmount);
        response.setUsedAmount(usedAmount);
        response.setUnsubscribeAmount(unsubAmount);
        response.setExpiredUsedAmount(
                unsubInquiryPriceVO.getUnsubAmount().compareTo(BigDecimal.ZERO) < 0
                        ? unsubInquiryPriceVO.getUnsubAmount().abs()
                        : BigDecimal.ZERO);
        return response;
    }

    private UnsubscribeInquiryPriceVO computeUnsubPriceWithResource(SfProductResource sfProductResource) {
        UnsubInquiryPriceVO unsubInquiryPriceVO = new UnsubInquiryPriceVO();
        if(sfProductResource.getProductType().equals(ProductCodeEnum.RDS.getProductType())){
            inquiryUnsubscribePriceRdsResource(sfProductResource.getId().toString(), unsubInquiryPriceVO);
        }else{
            inquiryUnsubscribePriceContainSubsidiaryResource(sfProductResource.getId().toString(), unsubInquiryPriceVO);
        }
        ResourceInquiryPriceVO payAmount = new ResourceInquiryPriceVO();
        payAmount.setOriginalCost(unsubInquiryPriceVO.getTotalOriginalCost());
        payAmount.setOrgDiscount(unsubInquiryPriceVO.getTotalOrgDiscount());
        payAmount.setCouponAmount(unsubInquiryPriceVO.getTotalCouponAmount());
        payAmount.setCashAmount(unsubInquiryPriceVO.getTotalCashAmount());
        payAmount.setCreditAmount(unsubInquiryPriceVO.getTotalCreditAmount());
        payAmount.setCouponDiscount(unsubInquiryPriceVO.getTotalCouponDiscount());

        ResourcePayPriceVO usedAmount = new ResourcePayPriceVO();
        usedAmount.setCashAmount(unsubInquiryPriceVO.getTotalUsedCashAmount().setScale(3, BigDecimal.ROUND_HALF_UP));
        usedAmount.setCouponAmount(unsubInquiryPriceVO.getTotalUsedCouponAmount().setScale(3, BigDecimal.ROUND_HALF_UP));
        usedAmount.setCreditAmount(unsubInquiryPriceVO.getTotalUsedCreditAmount().setScale(3, BigDecimal.ROUND_HALF_UP));
        usedAmount.setAmount(unsubInquiryPriceVO.getUsedAmount().setScale(3, BigDecimal.ROUND_HALF_UP));

        ResourcePayPriceVO unsubAmount = new ResourcePayPriceVO();
        unsubAmount.setCashAmount(NumberUtil.sub(payAmount.getCashAmount(),
                                                 usedAmount.getCashAmount().setScale(3, BigDecimal.ROUND_HALF_UP))
                                            .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCreditAmount(NumberUtil.sub(payAmount.getCreditAmount(),
                                                   usedAmount.getCreditAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setCouponAmount(NumberUtil.sub(payAmount.getCouponAmount(),
                                                   usedAmount.getCouponAmount()
                                                             .setScale(5, BigDecimal.ROUND_HALF_UP))
                                              .setScale(5, BigDecimal.ROUND_HALF_UP));
        unsubAmount.setAmount(unsubAmount.getCashAmount());

        UnsubscribeInquiryPriceVO response = new UnsubscribeInquiryPriceVO();
        response.setPayAmount(payAmount);
        response.setUsedAmount(usedAmount);
        response.setUnsubscribeAmount(unsubAmount);
        response.setExpiredUsedAmount(unsubInquiryPriceVO.getExpiredUsedAmount());
        if(ProductCodeEnum.RDS.getProductType().equals(sfProductResource.getProductType())){
            response.setDetail(unsubInquiryPriceVO.getDetail());
        }
        return response;
    }


    private static void postPaidInit(SfProductResource resource, ResourceDetailVO detailVO, List<ResourceDetailVO> details) {
        detailVO.setComputeDate(cn.hutool.core.date.DateUtil.date());
        detailVO.setId(resource.getId().toString());
        detailVO.setProductCode(resource.getProductType());
        detailVO.setTotalPayment(BigDecimal.ZERO);
        detailVO.setTotalOriginalCost(BigDecimal.ZERO);
        detailVO.setTotalOrgDiscount(BigDecimal.ZERO);
        detailVO.setTotalCouponDiscount(BigDecimal.ZERO);
        detailVO.setTotalCouponAmount(BigDecimal.ZERO);
        detailVO.setTotalCashAmount(BigDecimal.ZERO);
        detailVO.setTotalCreditAmount(BigDecimal.ZERO);
        detailVO.setTotalUsedAmount(BigDecimal.ZERO);
        detailVO.setTotalUsedCouponAmount(BigDecimal.ZERO);
        detailVO.setTotalUsedCashAmount(BigDecimal.ZERO);
        detailVO.setTotalUsedCreditAmount(BigDecimal.ZERO);
        detailVO.setUnsubAmount(BigDecimal.ZERO);
        detailVO.setUnsubCouponAmount(BigDecimal.ZERO);
        detailVO.setUnsubCashAmount(BigDecimal.ZERO);
        detailVO.setUnsubCreditAmount(BigDecimal.ZERO);
        detailVO.setExpiredUsedAmount(BigDecimal.ZERO);
        ServicePrice servicePrice = new ServicePrice();
        servicePrice.setExpiredUsedAmount(BigDecimal.ZERO);
        servicePrice.setTotalPayment(BigDecimal.ZERO);
        servicePrice.setTotalOriginalCost(BigDecimal.ZERO);
        servicePrice.setTotalOrgDiscount(BigDecimal.ZERO);
        servicePrice.setTotalCouponDiscount(BigDecimal.ZERO);
        servicePrice.setTotalCouponAmount(BigDecimal.ZERO);
        servicePrice.setTotalCashAmount(BigDecimal.ZERO);
        servicePrice.setTotalCreditAmount(BigDecimal.ZERO);
        servicePrice.setTotalUsed(BigDecimal.ZERO);
        servicePrice.setTotalUsedCouponAmount(BigDecimal.ZERO);
        servicePrice.setTotalUsedCashAmount(BigDecimal.ZERO);
        servicePrice.setTotalUsedCreditAmount(BigDecimal.ZERO);
        servicePrice.setUnsubAmount(BigDecimal.ZERO);
        servicePrice.setUnsubCouponAmount(BigDecimal.ZERO);
        servicePrice.setUnsubCashAmount(BigDecimal.ZERO);
        servicePrice.setUnsubCreditAmount(BigDecimal.ZERO);
        detailVO.setServiceAmount(servicePrice);
        details.add(detailVO);
    }


}
