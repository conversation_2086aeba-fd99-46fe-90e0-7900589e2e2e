package cn.com.cloudstar.rightcloud.bss.module.access.bean.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cn.com.cloudstar.rightcloud.common.constraint.Number;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;

/**
 * <Description>
 *
 * <AUTHOR>
 * @createDate 2022/12/20 15:30
 */
@Data
public class UserSensitiveRequest {

    /**
     * 用户id或发票id
     */
    @NotNull
    @Number
    private Long userId;
    /**
     * 个人隐私数据信息类型
     * 取值范围为email，mobile，realName,idCard,bankAccount,address,
     */
    @NotBlank
    @EnumValue(strValues = {"email", "mobile","realName","idCard","bankAccount","address","bank_account","register_address","register_mobile","id_card_front","id_card_reverse","account","legalPerson","legalPersonCard"})
    private String type;

    /**
     * 敏感信息类型（user或者account)
     * 若类型为user，则sensitiveData取值范围为email，mobile，realName,idCard;
     * 若类型为account，则sensitiveData取值范围为bankAccount,address,email，mobile
     */
    @NotBlank
    @EnumValue(strValues = {"user", "account","invoiceSetting","invoiceDetail", "org","provider"})
    private String sensitiveType;

}
