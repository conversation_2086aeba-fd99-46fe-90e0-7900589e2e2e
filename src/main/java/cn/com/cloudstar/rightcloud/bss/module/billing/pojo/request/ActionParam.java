package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 资源操作参数
 *
 * @author: chengpeng
 * @date: 2023/4/3 20:29
 */
@Data
public class ActionParam {
    /**
     * 订单资源ID
     */
    private Long orderResId;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;
    /**
     * 组织ID
     */
    private Long orgId;
    /**
     * sub组织ID
     */
    private Long subOrgId;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 资源类型编码
     */
    private String resTypeCode;
    /**
     * 资源操纵编码
     */
    private String action;
    /**
     * 资源属性信息
     */
    @NotNull
    private Object data;
    //======= 自动化共通参数 =======//
    /**
     * 执行对象清单（管理凭证id和实例基础信息）
     */
    private List<Map<String, List<Map<String, Object>>>> inventory;

    /**
     * 编排任务id
     */
    private Long flowTaskId;

    /**
     * 下一步操作
     */
    private ActionParam nextAction;

    private BigDecimal fileSize;

    private Long obsHcsoUserId;

    private String region;
}
