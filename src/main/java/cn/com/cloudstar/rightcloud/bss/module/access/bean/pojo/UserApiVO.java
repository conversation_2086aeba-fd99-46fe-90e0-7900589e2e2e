/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;


/**
 * <Description> <br>users/registe
 *
 * <AUTHOR>
 * @createDate 2020/04/24 10:27
 */
@Data
@ApiModel("简单用户数据")
public class UserApiVO {

    /**
     * 用户名
     */
    @NotBlank
    // @StartWithWord(message = "姓名不能已test，admin开头")
    @ApiModelProperty("用户名")
    @SafeHtml
    private String account;

    /**
     * 电话
     */
    @NotBlank
    @ApiModelProperty("电话")
    /*@Pattern(regexp = "^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$"
            , message = "请输入正确的手机号码")*/
    private String mobile;

    /**
     * 邮箱
     */
  //  @Email(regexp = "^[a-zA-Z0-9_.-]{3,16}@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$", message = "邮箱格式不正确")
    @NotBlank
  //  @StartWithWord(message = "邮箱不能已test，admin开头")
    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("是否预开通")
    private Boolean isPreOpen;

    @ApiModelProperty("映射用户ID")
    @NotBlank(message = "映射用户ID不能为空")
    private String refUserId;

    private String realName;

    /**
     * 用户标识
     */
    @ApiModelProperty(value = "用户类型", required = true)
    @NotBlank(message = "用户类型不能为空")
    @Length(max = 2)
    @Pattern(regexp = "^0([12])$"
            , message = "请输入正确的用户类型")
    private String userType;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    @Encrypt
    private String password;

    private Long userSid;

    private String status;

    private String ecryptMobile;
}
