/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.task;

import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.AiModelCollector;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.AiModelCollectorArchived;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.Collector;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IAiModelGaapCostService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: CalculateAiModelBillsTask.java
 * @Description： AiModel定时计费任务
 * @Author: system
 * @Date: 2025/01/21
 * @Version: 1.0.0
 **/
@Component
@Slf4j
public class CalculateAiModelBillsTask {
    
    private static final String AIMODEL_LOCK_KEY = "bill:task:aimodel:calculating";
    private static final String POST_PAID = "PostPaid";
    private static final String AIMODEL_RECORD_KEY = "AIMODEL_RECORD";
    
    @Autowired
    private IAiModelGaapCostService aiModelGaapCostService;
    
    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;
    
    @Autowired
    private TransactionDefinition transactionDefinition;
    
    @Autowired
    private IBizAccountDealService bizAccountDealService;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    /**
     * 计算AiModel账单
     */
    public void calculateAiModelBills() {
        calculateByType();
    }
    
    /**
     * 按类型计算账单
     */
    public void calculateByType() {
        log.info("AiModel入账开始");
        boolean lock = JedisUtil.INSTANCE.setnx(AIMODEL_LOCK_KEY);
        if (lock) {
            JedisUtil.INSTANCE.expire(AIMODEL_LOCK_KEY, 1800); // 30分钟超时
            TransactionStatus transactionStatus = null;
            try {
                transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
                doCalculate();
                dataSourceTransactionManager.commit(transactionStatus);
            } catch (Exception e) {
                log.error("AiModel入账失败,回滚数据,原因{}", e.getMessage(), e);
                if (transactionStatus != null) {
                    dataSourceTransactionManager.rollback(transactionStatus);
                }
            } finally {
                JedisUtil.INSTANCE.del(AIMODEL_LOCK_KEY);
            }
            log.info("AiModel入账结束");
        } else {
            log.info("已有AiModel任务正在入账中,退出本次任务");
        }
    }
    
    /**
     * 执行计算
     */
    private void doCalculate() {
        // 查询所有AiModel话单
        List<AiModelCollector> collectorList = getAiModelCollectorOnMongo();
        
        // 账单集合
        List<InstanceGaapCost> costs = Lists.newArrayList();
        // 收支明细集合
        List<BizAccountDeal> deals = Lists.newLinkedList();
        // 归档集合
        List<Collector> archRecords = Lists.newArrayList();
        
        if (CollectionUtil.isNotEmpty(collectorList)) {
            log.info("找到{}条AiModel话单待处理", collectorList.size());
            
            for (AiModelCollector detail : collectorList) {
                try {
                    aiModelGaapCostService.handleAiModelBill(archRecords, detail, POST_PAID, costs, deals);
                } catch (Exception e) {
                    log.error("生成AiModel账单信息失败！资源ID: {}, 错误: {}", detail.getResource_id(), e.getMessage(), e);
                }
            }
        }
        
        // 新增收支明细
        if (CollectionUtil.isNotEmpty(deals)) {
            bizAccountDealService.saveBatch(deals);
            log.info("保存{}条收支明细", deals.size());
        }
        
        // 将账单信息插入mongodb
        if (CollectionUtil.isNotEmpty(costs)) {
            // 保存账单到mongo
            mongoTemplate.insertAll(costs);
            log.info("保存{}条账单记录", costs.size());
            
            // 归档已出账话单
            handleArchRecords(archRecords);
        }
    }
    
    /**
     * 查询所有AiModel话单
     */
    private List<AiModelCollector> getAiModelCollectorOnMongo() {
        List<AiModelCollector> collectorList = new ArrayList<>();
        
        // 查询上次出账记录
        String lastRecord = JedisUtil.INSTANCE.get(AIMODEL_RECORD_KEY);
        if (lastRecord == null) {
            // 第一次出单出账，拉取全部数据
            Query query = new Query();
            collectorList = mongoTemplate.find(query, AiModelCollector.class);
            log.info("首次处理AiModel话单，查询到{}条记录", collectorList.size());
        } else {
            // TODO: 实现增量查询逻辑
            // 根据上次处理的时间戳进行增量查询
            Query query = new Query();
            collectorList = mongoTemplate.find(query, AiModelCollector.class);
            log.info("增量处理AiModel话单，查询到{}条记录", collectorList.size());
        }
        
        return collectorList;
    }
    
    /**
     * 归档已出账话单记录
     */
    private void handleArchRecords(List<Collector> archivalRecords) {
        if (CollectionUtil.isNotEmpty(archivalRecords)) {
            log.info("开始归档{}条AiModel话单", archivalRecords.size());
            
            archivalRecords.forEach(c -> {
                if (c instanceof AiModelCollector) {
                    // 归档AiModel类型话单
                    AiModelCollectorArchived aiModelCollectorArchived = new AiModelCollectorArchived();
                    BeanUtils.copyProperties(c, aiModelCollectorArchived);
                    mongoTemplate.insert(aiModelCollectorArchived);
                    
                    // 删除原始话单
                    mongoTemplate.remove(c);
                }
            });
            
            log.info("AiModel话单归档完成");
        }
    }
}
