/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.response;

import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/23.
 */
@Data
@ApiModel(description = "账户管理-账户列表")
public class DescribeBillingAccountResponse {

    @ApiModelProperty("账户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("账户名称")
    private String accountName;

    @ApiModelProperty("管理员名称")
    @DesensitizationField(type = DesensitizedType.NAME)
    private String adminName;

    @ApiModelProperty("联系人")
    @DesensitizationField(type = DesensitizedType.NAME)
    private String contactName;

    @ApiModelProperty("邮箱")
    @DesensitizationField(type = DesensitizedType.EMAIL)
    private String email;

    @ApiModelProperty("电话号码")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String mobile;

    /**
     * 管理员电话号码
     */
    @ApiModelProperty("管理员电话号码")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String adminmobileName;

    /**
     * 余额
     */
    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("折扣系数")
    private BigDecimal discount;

    @ApiModelProperty("创建时间")
    private Date createdDt;

    @ApiModelProperty("机构")
    private Long orgSid;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("信用额度")
    private BigDecimal creditLine;

    @ApiModelProperty("信用额度到期时间")
    private String creditLineDt;

    /**
     * 关联销售
     */
    @ApiModelProperty("关联销售")
    @DesensitizationField
    private String salesmen;

    @ApiModelProperty("关联销售Id")
    private String salesmenId;

    @ApiModelProperty("账户状态 正常：normal冻结：freeze")
    private String status;

    @ApiModelProperty("user状态")
    private String userStatus;

    @ApiModelProperty("现金券余额")
    private BigDecimal balanceCash;

    @ApiModelProperty("分销商名称")
    private String distributorName;

    @ApiModelProperty("分销商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long distributorId;

    @ApiModelProperty("资源冻结状态(0:禁用，1：启用)")
    private String freezeStatus;

    @ApiModelProperty("UserID")
    private Long userSid;

    @ApiModelProperty("账户登录名")
    private String account;

    @ApiModelProperty("用户身份认证状态")
    private String certificationStatus;

    @ApiModelProperty("所属行业")
    private String industry;
    private String industryName;

    @ApiModelProperty("应用场景")
    private String applicationScenario;
    /**
     * 应用程序场景名称
     */
    private String applicationScenarioName;
    /**
     * 人员规模
     */
    @ApiModelProperty("人员规模")
    private String personnelSize;
    /**
     * 人员规模名字
     */
    private String personnelSizeName;
    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String address;
    @ApiModelProperty("解决方案")
    private String solution;

    /**
     * 当前组织
     */
    @ApiModelProperty("当前组织")
    private CurrentOrgVO currentOrg;

    /**
     * 业务标识tag
     */
    @ApiModelProperty("业务标识tag，多个以;分隔，拓展中：expansion，已备案：recorded，试算中：trial，已签单：signed，商用中：commercial，欠费中：arrearage，已注销：cancelleda")
    private String businessTag;

    /**
     * 账户名称/实体名称
     */
    @ApiModelProperty("账户名称/实体名称")
    private String entityName;

    /**
     * 实体ID
     */
    @ApiModelProperty("实体ID")
    private String entityId;

    /**
     * 真实名称
     */
    @ApiModelProperty("真实名称")
    private String realName;

    /**
     * 是否展示启用按钮：0不展示，1展示
     */
    @ApiModelProperty("是否展示启用按钮：0不展示，1展示")
    private String showFlag;

    /**
     * LDAP 对应的 OU
     */
    @ApiModelProperty("LDAP 对应的 OU")
    private String ldapOu;

    /**
     * 账号到期时间
     */
    @ApiModelProperty("账号到期时间")
    private String endTime;

    /**
     * 处理策略
     */
    @ApiModelProperty("处理策略")
    private String actionStrategy;

    /**
     * 手动录入的线下开票金额
     */
    private BigDecimal offlineInvoicedAmount;


    /**
     * bsm是否开启
     */
    private Long bmsEnable;

    /**
     * 策略保留周期
     */
    private Long strategyBufferPeriod;

    /**
     * reservedResource 保留资源；releaseResource 释放资源；
     */
    private String freezingStrategy;

    /**
     *  是否隐藏弹性裸金属配置信息
     */
    private boolean bmsIsHide = false;

    /**
     * 开通产品信息
     */
    private String openProductInformation;


    @ApiModelProperty("用户免费OBS容量")
    private BigDecimal obsFreeCapacity;
    /**
     * 供应商ID
     */
    private String providerId;
    /**
     * 供应商状态
     * UNENTERED
     * APPLYING
     * ENTERED
     * APPLY_FAILED
     * CANCELLING
     * CANCELLED
     */
    private String providerStatus;

    /**
     * 供应商公司名称
     */
    private String providerCompanyName;


    public String getUserSid() {
        return this.userSid == null ? null : String.valueOf(this.userSid);
    }
}
