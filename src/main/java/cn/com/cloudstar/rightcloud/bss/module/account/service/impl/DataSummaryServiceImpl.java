package cn.com.cloudstar.rightcloud.bss.module.account.service.impl;


import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizCustomizationEntityTotal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.CustomizationEntity;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.EvsCollectorArchived;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.ObsCollectorArchived;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DateSummerRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.Element;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.ProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.SummaryResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.service.BizCustomizationEntityTotalService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.DataSummaryService;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.PageDTO;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;

import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname DataSummaryServiceImpl
 * @Description:
 * @Date 2023/9/20 18:30
 * @Author:mwy
 */
@Service
@Slf4j
public class DataSummaryServiceImpl implements DataSummaryService {

    private static final String OBS = "obsUsed";

    private static final String EVS = "evsUsed";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ISysMNoticeService sysMNoticeService;

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @Autowired
    private BizCustomizationEntityTotalService bizCustomizationEntityTotalService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    @Lazy
    private ExportService exportService;


    @Override
    public RestResult getPageList(DateSummerRequest request) {
        if (StrUtil.isNotBlank(request.getStarTime()) && StrUtil.isNotBlank(request.getEndTime())) {
            Criteria criteria = new Criteria();
            Long pageSize = request.getPagesize();
            int pageNum = (int) (request.getPagenum() * pageSize);
            int startRows = (int) ((request.getPagenum() - 1) * pageSize);

            //集合数据
            this.doCriteria(request, criteria, true);

            //分页
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("accountName").first("accountName").as("accountName"),
                    Aggregation.sort(Sort.Direction.DESC, "accountName")
            );
            List<CustomizationEntity> accountNameList =
                    mongoTemplate.aggregate(aggregation, "biz_customization_entity", CustomizationEntity.class).getMappedResults();
            int size = accountNameList.size();

            if (pageNum > size) {
                pageNum = size;
            }
            List<CustomizationEntity> customizationEntities = accountNameList.subList(startRows, pageNum);
            List<String> strings = customizationEntities.stream().map(CustomizationEntity::getAccountName).collect(Collectors.toList());

            Criteria criteria2 = new Criteria();
            //集合数据
            this.doCriteria(request, criteria2, false);

            criteria2.and("accountName").in(strings);
            List<ProductResponse> productResponses = preparationList(criteria2, customizationEntities);
            return new RestResult(PageDTO.setPageDataList(productResponses.size(), productResponses));
        } else {
            Page<BizCustomizationEntityTotal> page = new Page<>(request.getPagenum(), request.getPagesize());
            QueryWrapper<BizCustomizationEntityTotal> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("account_name");
            if(StrUtil.isNotBlank(request.getAccountName())){
                queryWrapper.lambda().like(BizCustomizationEntityTotal::getAccountName, request.getAccountName());
            }
            List<ProductResponse> productResponses = new ArrayList<>();
            IPage<BizCustomizationEntityTotal> totalPage = bizCustomizationEntityTotalService.page(page, queryWrapper);
            totalPage.getRecords().forEach(it->{
                ProductResponse productResponse = new ProductResponse();
                productResponse.setAccountName(it.getAccountName());
                productResponse.setDrpUsed(it.getDrpUsage());
                productResponse.setShareUsed(it.getModelartsUsage());
                String key = it.getAccountName() + OBS;
                String value = redisTemplate.opsForValue().get(key);
                if(StrUtil.isNotBlank(value)){
                    productResponse.setObsUsed(value);
                }else{
                    productResponse.setObsUsed(it.getObsUsage().setScale(2).toString());
                }
                String evsKey = it.getAccountName() + EVS;
                String evsValue = redisTemplate.opsForValue().get(evsKey);
                if(StrUtil.isNotBlank(evsValue)){
                    productResponse.setEvsUsed(evsValue);
                }else{
                    productResponse.setEvsUsed(it.getEvsUsage().setScale(2).toString());
                }
                productResponse.setWorkUsed(BigDecimal.valueOf(it.getWork()));
                productResponse.setBillingUsed(it.getBilling());
                productResponse.setDrpBillingUsed(it.getBillingDrp());
                productResponse.setShareBillingUsed(it.getBillingModelarts());
                productResponse.setObsBillingUsed(it.getBillingObs());
                productResponse.setEvsBillingUsed(it.getBillingEvs());
                productResponses.add(productResponse);
            });
            return new RestResult(PageDTO.setPageDataList(Math.toIntExact(totalPage.getTotal()), productResponses));
        }

    }


    @Override
    public List<SummaryResponse> summaryList(DateSummerRequest request) {
        ArrayList<String> types = new ArrayList<>(Arrays.asList("DRP", "MODELARTS", "OBS", "EVS", "work", "billing"));
        List<SummaryResponse> responseList = new LinkedList<>();
        for (String type : types) {
            responseList.addAll(doException(request, type));
        }
        return responseList;
    }



    @Override
    public RestResult expertRegisterUserInfo(DateSummerRequest request, String moduleType) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.RESOURCE_ANALYSIS.getCode());
        //添加下载任务数据
        if (authUserInfo == null){
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }

        getBizDownload(download, request, moduleType, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.RESOURCE_ANALYSIS_DOWNLOAD_ERROR));
        }
        //采用线程池
        new ExportThreadUtil(exportService, request, moduleType, ExportTypeEnum.RESOURCE_ANALYSIS.getCode(),
                             download.getDownloadId(), AuthUtil.getAuthUser(), RequestContextUtil.getAuthUserInfo())
                .submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.RESOURCE_ANALYSIS_DOWNLOAD_NOW));
    }

    private void getBizDownload(BizDownload download, DateSummerRequest request, String moduleType,
                                AuthUser authUserInfo) {
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setCreatedBy(authUserInfo.getAccount());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        bizDownloadMapper.insert(download);
    }

    private List<ProductResponse> preparationList(Criteria criteria, List<CustomizationEntity> accountNameList) {
        //集合数据
        List<CustomizationEntity> customizationEntities = mongoTemplate.find(Query.query(criteria), CustomizationEntity.class);
        //根据名称分组
        Map<String, List<CustomizationEntity>> collect =
                customizationEntities.stream().collect(Collectors.groupingBy(CustomizationEntity::getAccountName));
        List<ProductResponse> finalList = new ArrayList<>();
        Date before = DateUtils.addHours(new Date(), -1);
        for (CustomizationEntity name : accountNameList) {
            Long userSid = userMapper.selectIdByUserAccount(name.getAccountName());
            //根据客户名称查询obs最新数据
            ProductResponse productResponse = new ProductResponse();
            String accountName = name.getAccountName();
            List<CustomizationEntity> typeList = collect.get(accountName);
            if (CollectionUtil.isEmpty(typeList)) {
                continue;
            }
            Map<String, BigDecimal> decimalMap =
                    typeList.stream().collect(Collectors.groupingBy(CustomizationEntity::getType, Collectors
                            .mapping(CustomizationEntity::getValue, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            //拼装返回
            productResponse.setAccountName(accountName);
            if (Objects.nonNull(decimalMap.get("DRP"))) {
                productResponse.setDrpUsed(decimalMap.get("DRP").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setDrpUsed(BigDecimal.ZERO.setScale(2));
            }
            if (Objects.nonNull(decimalMap.get("MODELARTS"))) {
                productResponse.setShareUsed(decimalMap.get("MODELARTS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setShareUsed(BigDecimal.ZERO.setScale(2));
            }
            String key = accountName + OBS;
            String value = redisTemplate.opsForValue().get(key);
            if (Objects.nonNull(value)) {
                productResponse.setObsUsed(value);
            } else {
                doList(userSid, before, productResponse);
            }

            String evskey = accountName + EVS;
            String evsValue = redisTemplate.opsForValue().get(evskey);
            if (Objects.nonNull(evsValue)) {
                productResponse.setEvsUsed(evsValue);
            } else {
                doEvsList(userSid, before, productResponse);
            }

            if (Objects.nonNull(decimalMap.get("work"))) {
                productResponse.setWorkUsed(decimalMap.get("work").setScale(0, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setWorkUsed(BigDecimal.ZERO);
            }
            if (Objects.nonNull(decimalMap.get("billing"))) {
                productResponse.setBillingUsed(decimalMap.get("billing").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_DRP"))) {
                productResponse.setDrpBillingUsed(decimalMap.get("billing_DRP").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setDrpBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_MODELARTS"))) {
                productResponse.setShareBillingUsed(decimalMap.get("billing_MODELARTS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setShareBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_OBS"))) {
                productResponse.setObsBillingUsed(decimalMap.get("billing_OBS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setObsBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_EVS"))) {
                productResponse.setEvsBillingUsed(decimalMap.get("billing_EVS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setEvsBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }

            finalList.add(productResponse);
        }
        return finalList;
    }

    /**
     * 查询obs当前使用量
     *
     * @param userSid
     * @param before
     * @param productResponse
     */
    private void doList(Long userSid, Date before, ProductResponse productResponse) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria1 =
                new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria1.put("status", "used");
        List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria1);
        List<HcsoUser> hcsoUserList = hcsoUsers.stream().filter(h -> Objects.nonNull(h.getRefUserId()) && h.getRefUserId().equals(userSid)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(hcsoUserList)) {
            HcsoUser hcsoUser = hcsoUserList.get(0);
            Query query = new Query();
            Criteria criteria2 = new Criteria();
            criteria2.and("user_id").is(hcsoUser.getObsProjectId())
                     .and("billing_success").is("YES")
                     .and("archived_time").gte(before);
            query.addCriteria(criteria2);
            List<ObsCollectorArchived> obsCollectorArchiveds = mongoTemplate.find(query, ObsCollectorArchived.class);
            //每小时的obs使用量
            Long aLong = 0L;
            if (CollectionUtil.isNotEmpty(obsCollectorArchiveds)) {
                for (ObsCollectorArchived collectorArchived : obsCollectorArchiveds) {
                    String value = collectorArchived.getAccumulate_factor_value();
                    aLong = aLong + Long.valueOf(value);
                }
            }
            BigDecimal bigDecimal = bytes2Gb(aLong, 2);
            productResponse.setObsUsed(bigDecimal.setScale(2).toString());
        } else {
            productResponse.setObsUsed(BigDecimal.ZERO.setScale(2).toString());
        }
    }

    /**
     * 查询evs当前使用量
     *
     * @param userSid
     * @param before
     * @param productResponse
     */
    private void doEvsList(Long userSid, Date before, ProductResponse productResponse) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria1 =
                new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria1.put("status", "used");
        List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria1);
        List<HcsoUser> hcsoUserList = hcsoUsers.stream().filter(h -> Objects.nonNull(h.getRefUserId()) && h.getRefUserId().equals(userSid)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(hcsoUserList)) {
            HcsoUser hcsoUser = hcsoUserList.get(0);
            Query query = new Query();
            Criteria criteria2 = new Criteria();
            criteria2.and("user_id").is(hcsoUser.getObsProjectId())
                     .and("billing_success").is("YES")
                     .and("archived_time").gte(before);
            query.addCriteria(criteria2);
            List<EvsCollectorArchived> evsCollectorArchiveds = mongoTemplate.find(query, EvsCollectorArchived.class);
            //每小时得obs使用量
            Long aLong = 0L;
            if (CollectionUtil.isNotEmpty(evsCollectorArchiveds)) {
                for (EvsCollectorArchived collectorArchived : evsCollectorArchiveds) {
                    String value = collectorArchived.getAccumulate_factor_value();
                    aLong = aLong + Long.valueOf(value);
                }
            }
            BigDecimal bigDecimal = bytes2Gb(aLong, 2);
            productResponse.setEvsUsed(bigDecimal.setScale(2).toString());
        } else {
            productResponse.setEvsUsed(BigDecimal.ZERO.setScale(2).toString());
        }
    }

    /**
     * 字节转GB
     *
     * @param bytes
     *
     * @return
     */
    private BigDecimal bytes2Gb(long bytes, int scale) {
        BigDecimal filesize = new BigDecimal(bytes);
        BigDecimal gibibyte = new BigDecimal(1024 * 1024 * 1024);
        /*divide(BigDecimal divisor, int scale, int roundingMode) 返回一个BigDecimal，其值为（this/divisor），其标度为指定标度*/
        return filesize.divide(gibibyte, scale, BigDecimal.ROUND_HALF_UP);
    }

    private List<SummaryResponse> doException(DateSummerRequest request, String type) {
        Criteria criteria = new Criteria();
        this.doCriteria(request, criteria, true);
        criteria.and("type").is(type);
        Query query = new Query();
        query.addCriteria(criteria)
             .with(Sort.by(Sort.Order.desc("dateFormat")));
        //集合数据
        List<CustomizationEntity> customizationEntities = mongoTemplate.find(query, CustomizationEntity.class);
        List<SummaryResponse> responseList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(customizationEntities)) {
            SummaryResponse response = new SummaryResponse();
            List<Element> elementList = new ArrayList<>();
            for (CustomizationEntity entity : customizationEntities) {
                String dateFormat = entity.getDateFormat().toString();
                Date parse = null;
                try {
                    parse = new SimpleDateFormat("yyyyMMdd").parse(dateFormat);
                } catch (ParseException e) {
                    log.error("资源分析-时间转换失败！");
                }
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                BigDecimal value = entity.getValue();
                if ("OBS".equals(entity.getType())) {
                    value = bytes2Gb(value.longValue(), 2);
                }
                if ("EVS".equals(entity.getType())) {
                    value = bytes2Gb(value.longValue(), 2);
                }
                String format = simpleDateFormat.format(parse);
                Element element = Element.builder()
                                         .time(format)
                                         .value(value).build();
                elementList.add(element);
            }
            response.setType(type);
            Collections.reverse(elementList);
            response.setList(elementList);
            responseList.add(response);
        }
        return responseList;
    }

    private void doCriteria(DateSummerRequest request, Criteria criteria, boolean accountName) {
        if (Objects.nonNull(request.getAccountName()) && accountName) {
            criteria.and("accountName").is(request.getAccountName());
        }
        if (Objects.nonNull(request.getStarTime())) {
            String starTime = request.getStarTime().replace("-", "");
            String endTime = request.getEndTime().replace("-", "");
            criteria.and("dateFormat").gte(starTime).lte(endTime);
        }
//        if (Objects.nonNull(request.getEndTime())) {
//            String endTime = request.getEndTime().replace("-", "");
//            criteria.and("dateFormat").lte(endTime);
//        }
    }
}
