/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.service;

import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONArray;

import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/10 14:18
 */
public interface ResourceConfigService {

    Map<String, Object> select4Vm(String id);

    Map<String, Object> select4Vd(String id);

    Map<String, Object> select4EIP(String id);

    Map<String, Object> select4VBSVD(String id);

    Map<String, Object> select4RDS(String id);

    Map<String, Object> selectResource(String id);

    List<ResourceDetailVO> generateResourceDtail(String id);

    JSONArray selectVmChange(String id, String target);

    JSONArray selectDcsChange(String id, String target);

    String selectVdChange(String id, String target);

    JSONArray selectEIPChange(String id, ProductInfo productInfo);

    ProductInfoVO generateModifyInquiryBody(ModifyInquiryPriceRequest request);

    Map<String, Object> select4SFS(String id);

    JSONArray selectSfsChange(String id, ProductInfo productInfo);

    JSONArray selectDrpChange(ProductInfo productInfo);

    JSONArray selectEbsChange(String id, ProductInfo productInfo);

    String updateCurrentSpec(String serviceConfig, String productType, Long resourceId);

}
