/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 组织架构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface OrgService extends IService<Org> {
    /**
     * 查询根组织
     */
    Org selectRootOrg(Long orgSid);

    /**
     * 查询下级组织
     * <AUTHOR>
     * @Date 2021/3/13 11:45
     *
     * @param orgSid
     * @return
     */
    List<Long> selectChildrenOrgIds(Long orgSid);

    /**
     * 查询当前组织下所有客户的组织(包括客户的项目)
     */
    List<Long> selectCustomerOrgSids(Long orgSid);

    /**
     * 模糊查询组织ID集合
     * @param orgNameLike 模糊组织名
     * @return 组织ID集合
     * */
    List<Long> selectOrgIdListByOrgNameLike(String orgNameLike);

    /**
     * 查询org和分销商信息
     * @param param
     * @return
     */
    List<Org> getOrgInfo(Map param);

    List<Org> selectOrgSidBySid(Long companyId);


    /**
     * 查询企业是否认证中
     * @param orgSid 用户ID
     * @param type 企业类型
     * @return  List<Org>
     */
    List<Org> selectOrgByUserSidAndType(Long orgSid, String type);

    RestResult asynExportCustomer(ImportCustomerRequest request);

    /**
     * 根据公司名称查询分销商
     * @param orgName 公司名称
     * @return
     */
    Org selectDistributorBy(String orgName);

    String selectContactName(Long orgSid);

    boolean updateSolutionById(Org org);

    /**
     * 根据orgSid查询ldapOu
     * @param orgSid orgSid
     * @return ldapOu
     */
    String selectLdapOuByOrgSid(Long orgSid);

    /**
     *  根基id查询组织
     * @param orgSids id集合
     * @return 组织列表
     */
    List<CurrentOrgVO> findOrgListByIds(Set<Long> orgSids);


    List<CurrentOrgVO> findByIds(Set<Long> orgSids);

    User selectUserByOrgSid(Long valueOf);

    /**
     * 查询所有组织信息
     */
    List<Org> findAll();
    /**
     * 根据自定义信息条件查询组织
     * @param customizationInfo
     * @return
     */
    List<Org> selectOrgsByCustomize(String customizationInfo);

    List<Org> selectAllOrgSimple();

    /**
     * 分销商权限校验
     * @param orgSid orgSid
     * @param accountId accountId
     */
    void checkDistributorRole(Long orgSid, Long accountId);

    Org selectRootNewOrg(Org org,boolean isCompany);

    Org cmpSelectRootOrg(Long orgSid, String refOrgId, boolean isNeedRoot);
}
