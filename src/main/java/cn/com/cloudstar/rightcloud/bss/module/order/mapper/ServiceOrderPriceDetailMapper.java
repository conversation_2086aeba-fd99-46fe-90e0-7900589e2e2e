/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 订单价格详情  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@Repository
public interface ServiceOrderPriceDetailMapper extends BaseMapper<ServiceOrderPriceDetail> {


    List<ServiceOrderPriceDetail> selectByCriteria(Criteria criteria);


    List<ServiceOrderPriceDetail> selectOrderInfo(Criteria criteria);

    List<ServiceOrderDetail> selectOrderDetailByCriteria(Criteria criteria);

    List<ServiceOrderPriceDetail> selectOrderInfoByParam(Criteria criteria);

    List<ServiceOrderPriceDetail> selectOrderDetailByType(String orderSn, String type);
}
