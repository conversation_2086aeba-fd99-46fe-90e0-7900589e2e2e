/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.*;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.enums.ProductTemplateStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateRelationMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplateRelation;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.CreateProductTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.DescribeProductTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductTemplateStatusRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductTemplateResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfProductTemplateService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ServiceManage;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品模板信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-25
 */
@Service
public class SfProductTemplateServiceImpl extends
        ServiceImpl<SfProductTemplateMapper, SfProductTemplate> implements ISfProductTemplateService {

    @Autowired
    private SfProductTemplateRelationMapper productTemplateRelationMapper;

    @Autowired
    private SfProductTemplateMapper sfProductTemplateMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private CodeMapper codeMapper;

    @Override
    public boolean createTemplate(CreateProductTemplateRequest request) {
        SfProductTemplate template = BeanConvertUtil.convert(request, SfProductTemplate.class);
        if (ObjectUtils.isEmpty(template)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1474238722));
        }
        // 产品模板名称唯一性验证
        if (checkTemplateName(template.getTemplateName(),template.getId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.TEMPLATE_NAME_REPEAT));
        }
        if (StringUtil.isNotBlank(request.getTemplateType()) &&!ServiceManage.list.contains(request.getTemplateType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_986078609));
        }
        if (Objects.isNull(request.getIsCopy())) {
            request.setIsCopy(false);
        }
        if (request.getIsCopy()) {
            template.setStatus(ProductTemplateStatusEnum.DISABLE.getCode());
        } else {
            template.setStatus(ProductTemplateStatusEnum.ENABLE.getCode());
        }
        WebUserUtil.prepareInsertParams(template);
        WebUserUtil.prepareUpdateParams(template);
        //添加运营实体字段
        template.setEntityId(RequestContextUtil.getEntityId());
        return this.save(template);
    }

    @Override
    public IPage<DescribeProductTemplateResponse> listTemplates(
            DescribeProductTemplateRequest request) {
        // 过滤掉除ModelArts和OBS相关业务
        List<String> typeList = Arrays.asList("DRP", "ModelArts", "OBS");
        /* 将请求条件转化为查询条件类*/
        QueryWrapper<SfProductTemplate> queryWrapper = WrapperUtil.wrapQuery(request);
        if(Objects.nonNull(RequestContextUtil.getEntityId())){
            queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
        }
        queryWrapper.in("template_type", typeList);
        boolean isPageQuery = PageUtil.isPageQuery(request);

        /* 是否分页处理*/
        if (isPageQuery) {
            Page<SfProductTemplate> pageParams = PageUtil
                    .preparePageParams(request, "createdDt", "desc");
            // 产品模板仅展示允许产品
            Criteria criteriaCode = new Criteria();
            criteriaCode.put("codeCategory", "PRODUCT_CODE");
            criteriaCode.put("enabled", "1");
            List<Code> codes = codeMapper.selectByParams(criteriaCode);
            queryWrapper.in("template_type", codes.stream().map(Code::getCodeValue).collect(Collectors.toList()));
            IPage<SfProductTemplate> templates = this.page(pageParams, queryWrapper);

            IPage<DescribeProductTemplateResponse> responsePage = BeanConvertUtil
                    .convertPage(templates, DescribeProductTemplateResponse.class);
            responsePage.getRecords().forEach(this::setInfo);

            return responsePage;
        }

        IPage<SfProductTemplate> emptyPage = PageUtil.emptyPage();
        emptyPage.setRecords(this.list(queryWrapper));
        return BeanConvertUtil.convertPage(emptyPage, DescribeProductTemplateResponse.class);
    }

    private void setInfo(DescribeProductTemplateResponse response) {
        response.setStatusName(
                EnumUtil.likeValueOf(ProductTemplateStatusEnum.class, response.getStatus()).getName());
        List<SfProductTemplateRelation> sfProductTemplateRelations = productTemplateRelationMapper
                .selectList(Wrappers.<SfProductTemplateRelation>lambdaQuery()
                        .eq(SfProductTemplateRelation::getTemplateId, response.getId()));
        if (CollectionUtil.isNotEmpty(sfProductTemplateRelations)) {
            List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectList(
                    Wrappers.<ServiceCategory>lambdaQuery().in(ServiceCategory::getId,
                            sfProductTemplateRelations.stream().map(SfProductTemplateRelation::getProductId)
                                    .collect(Collectors.toList())).ne(ServiceCategory::getStatus, "delete"));
            response.setProduct(serviceCategories.stream().map(ServiceCategory::getProductName)
                    .collect(Collectors.joining(StrUtil.COMMA)));
        }
    }

    @Override
    public boolean updateTemplate(UpdateProductTemplateRequest request) {
        SfProductTemplate sfProductTemplate = sfProductTemplateMapper.selectById(request.getId());
        if (Objects.nonNull(sfProductTemplate) && !sfProductTemplate.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        SfProductTemplate template = BeanConvertUtil.convert(request, SfProductTemplate.class);
        if (ObjectUtils.isEmpty(template)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1474238722));
        }
        // 产品模板名称唯一性验证
        if (checkTemplateName(template.getTemplateName(), template.getId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.TEMPLATE_NAME_REPEAT));
        }
        String status = template.getStatus();
        if (ProductTemplateStatusEnum.DISABLE.getCode().equals(status) && isRelationProductInUsing(
                template)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1259909588));
        }
        WebUserUtil.prepareUpdateParams(template);
        return this.updateById(template);
    }

    @Override
    public boolean updateTemplateStatus(UpdateProductTemplateStatusRequest request) {
        SfProductTemplate sfProductTemplate = sfProductTemplateMapper.selectById(request.getId());
        if(ObjectUtils.isEmpty(sfProductTemplate)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1165028263));
        }
        if (!sfProductTemplate.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        SfProductTemplate template = BeanConvertUtil.convert(request, SfProductTemplate.class);
        if (ObjectUtils.isEmpty(template)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1474238722));
        }
        String status = template.getStatus();
        if(ProductTemplateStatusEnum.DISABLE.getCode().equals(status) && ProductTemplateStatusEnum.DISABLE.getCode().equals(sfProductTemplate.getStatus())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_534297092));
        }
        if(ProductTemplateStatusEnum.ENABLE.getCode().equals(status) && ProductTemplateStatusEnum.ENABLE.getCode().equals(sfProductTemplate.getStatus())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_45839932));
        }
        if (ProductTemplateStatusEnum.DISABLE.getCode().equals(status) && isRelationProductInUsing(
                template)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1259909588));
        }
        WebUserUtil.prepareUpdateParams(template);
        return this.updateById(template);
    }

    private boolean isRelationProductInUsing(SfProductTemplate template) {
        List<SfProductTemplateRelation> sfProductTemplateRelations = productTemplateRelationMapper
                .selectList(Wrappers.<SfProductTemplateRelation>lambdaQuery()
                        .eq(SfProductTemplateRelation::getTemplateId, template.getId()));
        if (CollectionUtil.isNotEmpty(sfProductTemplateRelations)) {
            List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectList(
                    Wrappers.<ServiceCategory>lambdaQuery().in(ServiceCategory::getId,
                            sfProductTemplateRelations.stream().map(SfProductTemplateRelation::getProductId)
                                    .collect(Collectors.toList())));
            return serviceCategories.stream()
                    .anyMatch(serviceCategory -> "using".equals(serviceCategory.getStatus()));
        }
        return false;
    }

    @Override
    public DescribeProductTemplateResponse getDetail(Long id) {
        SfProductTemplate productTemplate = this.getById(id);
        DescribeProductTemplateResponse response = BeanConvertUtil
                .convert(productTemplate, DescribeProductTemplateResponse.class);
        if (Objects.nonNull(response)) {
            setInfo(response);
        }
        return response;
    }

    @Override
    public List<DescribeProductTemplateResponse> searchTemplates(Long productId, String templateType) {

        if (StringUtils.isEmpty(templateType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        List<SfProductTemplate> templates = this.baseMapper.findByTemplateType(productId, templateType);
        return BeanConvertUtil.convert(templates, DescribeProductTemplateResponse.class);
    }

    /**
     * 判断是否有重复的产品模板名称
     * @param templateName
     * @param templateId
     * @return
     */
    private boolean checkTemplateName(String templateName, Long templateId) {

        LambdaQueryChainWrapper<SfProductTemplate> queryChainWrapper = this.lambdaQuery()
                                                                           .eq(SfProductTemplate::getTemplateName, templateName.trim());
        if(templateId !=null){
            queryChainWrapper.ne(SfProductTemplate::getId,templateId);
        }
        List<SfProductTemplate> result = queryChainWrapper.list();
        return !CollectionUtils.isEmpty(result);
    }
}
