/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产品对应资源信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SfProductResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 企业名称
     */
    @TableField(exist = false)
    private String companyName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品状态
     */
    private String status;

    /**
     * 资源信息
     */
    private String resourceInfo;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 组织id
     */
    private Long orgSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 乐观锁
     */
    private Long version;


    private Long cloudEnvId;
    /**
     * 有效算力值
     */
    private Long cueValue;
    /**
     * 集群ID
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 订单ID
     */
    private Long serviceOrderId;

    private Integer hpcVersion;
    /**
     * 冻结时间
     */
    private Date frozenTime;
    /**
     *
     */
    private String preStatus;

    private Integer maVersion;

    /**
     * reservedResource 保留资源；releaseResource 释放资源；continueOperation 继续作业；stopOperation 停止作业
     */
    private String freezingStrategy;

    /**
     * 冻结策略缓冲期。释放资源时配置
     */
    private Integer strategyBufferPeriod;

    private String poolType;

    private String cpuArch;

    private String drpFrozenStatus;



    private String instanceUuid;
}
