/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.mapper;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketCatalogVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.annotation.BusinessEntityPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 账户  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-22
 */
@Repository
public interface BizBillingAccountMapper extends BaseMapper<BizBillingAccount> {

    /**
     * 查询账户列表分页
     * @param page
     * @param condition
     * @return
     */
    IPage<BizBillingAccount> selectByParams(IPage<BizBillingAccount> page, @Param("condition") Map<String, Object> condition);

    /**
     * 查询账户列表分页(客户管理列表)
     * @param page
     * @param condition
     * @return
     */
    IPage<BizBillingAccount> selectByParam(IPage<BizBillingAccount> page, @Param("condition") Map<String, Object> condition);


    /**
     * 查询账户列表分页(按照所属分销商模糊查询"直"或"营")
     * @param page
     * @param condition
     * @return
     */
    IPage<BizBillingAccount> selectByLikeDistributorName(IPage<BizBillingAccount> page, @Param("condition") Map<String, Object> condition);
    /**
     * 查询账户折扣列表
     * @param page
     * @param condition
     * @return
     */
    @BusinessEntityPermission(cloumn = "id")
    IPage<BizBillingAccount> selectDiscountByParams(IPage<BizBillingAccount> page, @Param("condition") Map<String, Object> condition);

    void selectDiscountForCount();

    /**
     *  查询账户列表
     * @param condition
     * @return
     */
    List<BizBillingAccount> selectByList(@Param("condition") Map<String, Object> condition);

    /**
     * 查询账户详情
     * @param id
     * @return
     */
    BizBillingAccount selectByPrimaryKey(Long id);

    BizBillingAccount selectByPrimaryKeys(Long id);

    /**
     * 根据id和版本号查询账户
     * @param id
     * @param version
     * @return
     */
    BizBillingAccount selectByIdAndVersion(@Param("id") Long id, @Param("version") Long version);

    /**
     * 通过组织ID查询账户
     * @param orgId
     * @return
     */
    BizBillingAccount selectByOrgId(@Param("orgId") Long orgId, @Param("entityId") Long entityId);


    /**
     * 通过组织查询账户id
     * @param orgId
     * @return
     */
    List<Long> selectAccountIdByOrgId(@Param("orgId") Long orgId);


    /**
     * 通过组织ID和多运营实体id查询账户
     * @param orgId
     * @return
     */
    List<BizBillingAccount>  selectAccountsByOrgId(@Param("orgId") Long orgId);

    /**
     * 查询portal的服务分类
     */
    List<MarketCatalogVO> selectProductCatalogWithService();

    List<MarketServiceVO> selectAvailableServiceByCatalogId(Long catalogId);


    void reduceBalance(@Param("bizBillingAccountId") Long bizBillingAccountId,
        @Param("subtractAmount") BigDecimal subtractAmount);

    void increaseBalance(@Param("bizBillingAccountId") Long bizBillingAccountId,
        @Param("addAmount") BigDecimal addAmount);

    Long countCustomers(Long userSid);


    BizBillingAccount getBillingAccountByUserIdAndEntitytId(@Param("userSid") Long userSid, @Param("entityId") Long entityId);

    /**
     * 获取账户信息和分销商信息`
     * @param param
     * @return
     */
    List<BizBillingAccount> getSimpleAccountAndDistributorInfo(Map param);

    List<BizBillingAccount> getByDistributorNameAndIds(@Param("distributorName") String distributorName,@Param("ids") Set<Long> ids);
    /**
     * 根据请求参数获取账号信息
     */
    List<BizBillingAccount> selectAccountInfoByCustomerName(DescribeGaapCostRequest request);

    /**
     * 获取所有直营账户
     * */
    List<BizBillingAccount> selectAllDirectlyAccount();

    int updateSalesmenAccount(@Param("salesmenId") Long salesmenId, @Param("billingAccountId") Long billingAccountId);

    /**
     * 导出 客户信息
     */
    List<BizBillingAccount> selectByExport(Criteria criteria);

    Integer getSalesmenAccountBySalesmenId(@Param("salesmenIds") List<Long> salesmenIds);

    BizBillingAccount getBillingAccountByIdAndSalesmenId(@Param("accountId")Long accountId,@Param("salesmenId") Long salesmenId,@Param("distributorId")Long distributorId);

    /**
     * 查询自己及自己关联的客户账户
     */
    List<BizBillingAccount> findSelfAndSelfCustomer(@Param("userSid") Long userSid, @Param("entityId") Long entityId);


    List<BizBillingAccount> getByAdminSid(@Param("userSid") Long userSid);


    /**
     * 解冻账户
     */
    int updateFreezeAccount(@Param("accountId") Long accountId);

    /**
     * 冻结账户
     * unfreezeType冻结类型（0为自动1为手动）
     */
    int updateFrozenAccount(@Param("accountId") Long accountId, @Param("unfreezeType") String unfreezeType);

    BizBillingAccount getByCategoryId(@Param("categoryId") Long categoryId, @Param("userSid") Long userSid);

    List<BizBillingAccount> getByOrgSid(@Param("orgSid") Long orgSid);

    List<BizBillingAccount> getByOrgSids(@Param("orgSids")  List<Long> orgSids);

    /**
     * 根据当前运营实体查询当前运营下的账户
     *
     * @param entityId
     * @return
     */
    List<BizBillingAccount> getBillingAccountByEntityId(@Param("entityId") Long entityId);

    /**
     * 根据当前运营实体查询当前运营下的账户
     *
     * @param entityId
     * @return
     */
    List<BizBillingAccount> getAccountIdByEntityId(@Param("entityId") Long entityId);


    /**
     * 根据当前运营实体查询当前运营实体下当前组织及当前组织下的客户账户数据
     *
     * @param entityId
     * @return
     */
    Set<Long> getBillingAccountByEntityIdAndOrgSid(@Param("entityId") Long entityId, @Param("orgSid") Long orgSid);

    /**
     *
     * @param userSid
     * @param entityId
     * @return
     */
    Set<Long> findSelfAndSelfCustomerAccountId(@Param("userSid") Long userSid, @Param("entityId") Long entityId);
    /**
     * 根据当前运营实体查询当前运营实体下当前组织及当前组织下的客户账户数据
     *
     * @param entityId
     * @return
     */
    List<BizBillingAccount> getAccountByEntityId(@Param("entityId") Long entityId, @Param("orgSid") Long orgSid);
    /**
     * 通过组织和运营实体ID查询账户
     */
    BizBillingAccount selectByOrgIdAndEntityId(@Param("orgId") Long orgId, @Param("entityId") Long entityId);


    BizBillingAccount getByEntityIdAndUserId(@Param("entitySid") Long entitySid, @Param("userSid") Long userSid);

    BizBillingAccount getFirst();


    /**
     * 根据分销商orgId查询租户账户号是否存在
     *
     * @param orgSid 分销商orgId
     * @param tenant 账户号
     * @return
     */
    Integer selectAccountIsExist(String tenant, Long orgSid);



    BizBillingAccount getByEntityIdAndOrgSid(@Param("entityId") Long entityId, @Param("orgSid") Long orgSid);


    List<BizBillingAccount> selectAccountByIds(@Param("ids") Set<Long> userAccountIds);

    List<cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount> selectByParamsForCollector(cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria accountCri);

    /**
     * 设置到期时间
     * @param bizBillingAccount
     * @param ids
     * @return void
     */
    boolean updateEndTime(@Param("bizBillingAccount") BizBillingAccount bizBillingAccount, @Param("ids") List<Long> ids);

    /**
     * 按组织id和实体id查询
     *
     * @param entityId 实体id
     * @param orgId    组织id
     * @return {@link List}<{@link BizBillingAccount}>
     */
    List<BizBillingAccount> selectListByOrgIdAndEntityId(@Param("orgId") Long orgId, @Param("entityId") Long entityId);

    @BusinessEntityPermission
    List<BizBillingAccount> selectDiscountByParamsExport(@Param("condition") Map<String, Object> condition);

    BizBillingAccount selectByOrgIdAndEntity(@Param("orgId") Long orgId);
}
