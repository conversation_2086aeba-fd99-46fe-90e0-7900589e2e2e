/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity;

import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServicePrice;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/16 17:08
 */
@Data
@ApiModel("资源组件详情")
public class ResourceDetailVO {

    /**
     * 资源组件id
     */
    @ApiModelProperty("资源组件id")
    private String id;

    /**
     * 底层id
     */
    @ApiModelProperty("底层id")
    private String resourceId;

    /**
     * 订单详情
     */
    @ApiModelProperty("订单详情")
    private List<ServiceOrderDetailVO> orderDetails;

    /**
     * 资源组件实付总金额
     */
    @ApiModelProperty("资源组件实付总金额")
    private BigDecimal totalPayment;

    /**
     * 资源组件原价
     */
    @ApiModelProperty("资源组件原价")
    private BigDecimal totalOriginalCost;

    /**
     * 资源组件折扣优惠金额
     */
    @ApiModelProperty("资源组件折扣优惠金额")
    private BigDecimal totalOrgDiscount;

    /**
     * 资源组件优惠券抵扣金额
     */
    @ApiModelProperty("资源组件优惠券抵扣金额")
    private BigDecimal totalCouponDiscount;

    /**
     * 资源组件充值现金券支付金额
     */
    @ApiModelProperty("资源组件充值现金券支付金额")
    private BigDecimal totalCouponAmount;

    /**
     * 资源组件现金支付金额
     */
    @ApiModelProperty("资源组件现金支付金额")
    private BigDecimal totalCashAmount;

    /**
     * 资源组件信用额度支付金额
     */
    @ApiModelProperty("资源组件信用额度支付金额")
    private BigDecimal totalCreditAmount;

    /**
     * 组件总使用金额
     */
    @ApiModelProperty("组件总使用金额")
    private BigDecimal totalUsedAmount;

    /**
     * 组件总使用充值现金券金额
     */
    @ApiModelProperty("组件总使用充值现金券金额")
    private BigDecimal totalUsedCouponAmount;

    /**
     * 组件总使用现金金额
     */
    @ApiModelProperty("组件总使用现金金额")
    private BigDecimal totalUsedCashAmount;

    /**
     * 组件总使用信用额度金额
     */
    @ApiModelProperty("组件总使用信用额度金额")
    private BigDecimal totalUsedCreditAmount;

    /**
     * 按量付费下单价
     */
    @ApiModelProperty("按量付费下单价")
    private BigDecimal hourPrice;


    /**
     * 额外配置计费
     */
    @ApiModelProperty("额外配置计费")
    private ServicePrice extraConfigAmount;

    /**
     * 服务计费
     */
    @ApiModelProperty("服务计费")
    private ServicePrice serviceAmount;

    /**
     * 计费类型
     */
    @ApiModelProperty("计费类型")
    private String chargeType;

    /**
     * 产品服务列表
     */
    @ApiModelProperty("产品服务")
    private List<ProductService> services;

    /**
     * 状态
     */
    @ApiModelProperty("资源状态")
    private String status;

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    private Long projectId;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品code")
    private String productCode;

    /**
     * 申请单类型
     */
    @ApiModelProperty("申请单类型")
    private String orderType;

    /**
     * 计算时间
     */
    @ApiModelProperty("计算时间")
    private Date computeDate;

    /**
     * 当前时间
     */
    @ApiModelProperty("当前时间")
    private Date now;

    /**
     * 过期使用金额
     */
    @ApiModelProperty("过期使用金额")
    private BigDecimal expiredUsedAmount = BigDecimal.ZERO;

    /**
     * 使用优惠券到期金额
     */
    @ApiModelProperty("组件过期使用充值现金券金额")
    private BigDecimal expiredUsedCouponAmount;

    /**
     * 组件过期使用现金金额
     */
    @ApiModelProperty("组件过期使用现金金额")
    private BigDecimal expiredUsedCashAmount;

    /**
     * 组件过期使用信用额度金额
     */
    @ApiModelProperty("组件过期使用信用额度金额")
    private BigDecimal expiredUsedCreditAmount;

    /**
     * 退订金额
     */
    @ApiModelProperty("退订金额")
    private BigDecimal unsubAmount = BigDecimal.ZERO;

    /**
     * 使用时间实际跨了多少个月
     */
    @ApiModelProperty("使用时间实际跨了多少个月")
    private Integer actualMonth;

    /**
     * 买了几个月
     */
    @ApiModelProperty("买了几个月")
    private Integer buyMonth;

    /**
     * 组件退订充值现金券金额
     */
    @ApiModelProperty("组件退订充值现金券金额")
    private BigDecimal unsubCouponAmount;

    /**
     * 组件退订现金金额
     */
    @ApiModelProperty("组件退订现金金额")
    private BigDecimal unsubCashAmount;

    /**
     * 组件退订信用额度金额
     */
    @ApiModelProperty("组件退订信用额度金额")
    private BigDecimal unsubCreditAmount;

    /**
     * 订单细节id
     */
    private Long orderDetailId;
    /**
     * 是否扩容，true（扩容），false（缩容）
     */
    private Boolean scaleUp;
    /**
     * 真实当前时间
     */
    private Date currentDate;

    public BigDecimal getTotalPayment() {
        return Objects.isNull(totalPayment) ? BigDecimal.ZERO : totalPayment;
    }

    public BigDecimal getTotalUsedAmount() {
        return Objects.isNull(totalUsedAmount) ? BigDecimal.ZERO : totalUsedAmount;
    }

    public List<ProductService> getServices() {
        return Objects.isNull(this.services) ? Lists.newArrayList() : this.services;
    }

    public void init() {
        this.setServices(Lists.newArrayList());
        this.setServiceAmount(new ServicePrice());
        this.setExtraConfigAmount(new ServicePrice());
    }
}
