/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import cn.com.cloudstar.rightcloud.oss.common.util.annotation.StartWithWord;

/**
 * 询价基类
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "询价基础信息")
public class InquiryBaseVO {
    /**
     * 产品类型
     */
    @ApiModelProperty(notes = "产品类型，如ECS，EBS")
    @StartWithWord(words = {"SFS","HPC"}, message = "该产品不存在")
    private String productCode;

    /**
     * 名称
     */
    @ApiModelProperty(value = "申请单名称数据库中订单名称会加一个时间后缀")
    private String name;

    /**
     * 云环境id
     */
    @ApiModelProperty(notes = "云环境ID", required = true)
    private Long cloudEnvId;

    /**
     * 云环境id
     */
    @ApiModelProperty(notes = "云环境ID", required = true)
    private Long envId;

    /**
     * 资源id
     */
    @ApiModelProperty(notes = "资源ID")
    private String resourceId;

    /**
     * 资源类型
     */
    @ApiModelProperty(notes = "资源类型")
    private String resourceType;

    /**
     * 实例计费类型
     */
    @ApiModelProperty(notes = "实例计费类型")
    private String instanceChargeType;

    /**
     * 申请时长
     */
    @ApiModelProperty(notes = "申请时长")
    private BigDecimal period;

    /**
     * 申请数量
     */
    @ApiModelProperty(notes = "申请数量")
    private Integer amount;

    /**
     * 可用区
     */
    @ApiModelProperty(notes = "可用区")
    private String zone;

    /**
     * 服务id
     */
    @ApiModelProperty(notes = "服务ID")
    private Long serviceId;

    /**
     * 计费账户id
     */
    @ApiModelProperty(notes = "计费账户ID")
    private Long billingAccountId;

    /**
     * 产品类别列表
     */
    @ApiModelProperty(notes = "产品类别列表")
    private List<String> products = Lists.newArrayList();

    /**
     * 区域
     */
    @ApiModelProperty(notes = "区域")
    private String regionId;

    /**
     * 付费类型
     */
    @ApiModelProperty(notes = "付费类型")
    private String chargeType;

    /**
     * 周期单位
     */
    @ApiModelProperty(notes = "按月：Month， 按小时：Hour")
    private String periodUnit;

    /**
     * 优惠券
     */
    @ApiModelProperty(notes = "优惠券")
    private Long couponId;

    /**
     * 订单id
     */
    @ApiModelProperty(notes = "订单ID")
    private String orderId;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;
    /**
     * 续订Flg
     */
    @ApiModelProperty(notes = "续订Flg")
    private boolean renewFlg = false;

    /**
     * 开通Flg
     */
    @ApiModelProperty(notes = "开通Flg")
    private boolean openFlg = false;

    /**
     * 常规用户账号判断Flg
     */
    @ApiModelProperty(notes = "常规用户账号判断Flg false：管理员账号， true：非管理员账号")
    private boolean consoleUserFlg = true;

    private String entityName;

    private Long entityId;

    @ApiModelProperty(notes = "申请旧时长")
    private BigDecimal periodTime;
    /**
     * 询价时间点
     */
    private Date pointInTime;


    /**
     * 商品id
     */
    private String shopId;

    /**
     * 询价类型：year:按月;month按年;one-time按次;
     */
    private String periodType;

    /**
     * 商品是否免费：0免费，1按月，2按年,3按次
     */
    private Integer sellType;

    /**
     * 选中的规格版本
     */
    private List<SkuEnumDto> skuInfo;
}
