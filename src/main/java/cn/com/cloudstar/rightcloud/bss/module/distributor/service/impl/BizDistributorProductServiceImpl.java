package cn.com.cloudstar.rightcloud.bss.module.distributor.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.distributor.mapper.BizDistributorProductMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributorProduct;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request.DescribeProductInfoResponse;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 不计费产品信息表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since  2022-05-18
 */
@Service
public class BizDistributorProductServiceImpl extends ServiceImpl<BizDistributorProductMapper, BizDistributorProduct> implements
        IBizDistributorProductService{
    @Autowired
    private BizDistributorProductMapper bizDistributorProductMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private IBizDistributorService distributorService;

    @Autowired
    private UserMapper userMapper;

    @Override
    public IPage<BizDistributorProduct> selectDistributorProduct(Page<BizDistributorProduct> page, Criteria criteria) {
        return this.baseMapper.selectByParams(page,criteria.getCondition());
    }

    @Override
    public List<BizDistributorProduct> selectList(Criteria criteria) {
        return this.baseMapper.selectByList(criteria);
    }

    @Override
    public List<DescribeProductInfoResponse> selectUseService() {
        User authUser = AuthUtil.getAuthUser();
        if ("04".equals(authUser.getUserType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        List<DescribeProductInfoResponse> responses = this.baseMapper.selectService(RequestContextUtil.getEntityId(),ProductCodeEnum.MIRROR_CENTER.getProductCode());
        //查询产品付费情况
        for (DescribeProductInfoResponse res : responses) {
            String templateContent = res.getTemplateContent();
            List<String> list = new ArrayList<>();
            if (ProductCodeEnum.ECS.getProductCode().equals(res.getShowType())) {
                list.add(ChargeTypeEnum.PostPaid.getType());
                list.add(ChargeTypeEnum.PrePaid.getType());
                res.setChargeType(list);
                continue;
            } else if (ProductCodeEnum.CCE.getProductCode().equals(res.getShowType())) {
                list.add(ChargeTypeEnum.PrePaid.getType());
                res.setChargeType(list);
                continue;
            }
            if (Objects.isNull(templateContent)){
                continue;
            }
            // ModelArts AI开发平台共享资源池 特殊处理
            if (templateContent.contains("公共资源池")) {
                list.add(ChargeTypeEnum.PostPaid.getType());
                //数据过大置空
                res.setChargeType(list);
                res.setTemplateContent(null);
                continue;
            }
            if (templateContent.contains(ChargeTypeEnum.PostPaid.getType())){
                list.add(ChargeTypeEnum.PostPaid.getType());
            }
            if (templateContent.contains(ChargeTypeEnum.PrePaid.getType())){
                list.add(ChargeTypeEnum.PrePaid.getType());
            }
            res.setChargeType(list);
            //数据过大置空
            res.setTemplateContent(null);
        }
        return responses;
    }

    @Override
    public Boolean selectIsNonBillProduct(IsNonBillProductRequest request) {
        User authUser = AuthUtil.getAuthUser();
        //资源退订,查询申请时的购买标识
        if(Objects.nonNull(request.getResourceId())){
            //拼接id查询
            String refInstanceId = "[\"" + request.getResourceId() + "\"]";
            List<ServiceOrderVo> orderList = serviceOrderMapper.selectByInstanceId(refInstanceId);
            if (CollectionUtil.isNotEmpty(orderList)){
                ServiceOrderVo serviceOrder = CollectionUtil.getFirst(orderList);
                return BillingConstants.ChargingType.SALE_TYPE.equals(serviceOrder.getChargingType());
            }
            return false;
        }

        //资源购买、续订、变配
        BizBillingAccount account = bizBillingAccountMapper.getByCategoryId(request.getSfServiceId(), authUser.getUserSid());
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        if (Objects.isNull(account) || Objects.isNull(account.getDistributorId())) {
            return false;
        }
        criteria.put("distributorId",account.getDistributorId());

        List<BizDistributorProduct> bizDistributorProducts = bizDistributorProductMapper.selectByParam(criteria);
        if (!CollectionUtils.isEmpty(bizDistributorProducts)){
            return true;
        }else {
        return false;
    }
    }

    @Override
    public void validRequest(BizDistributorProduct distributorProduct) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(distributorProduct.getSfServiceId());
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizDistributor bizDistributor = distributorService.getById(distributorProduct.getDistributorId());
        if (Objects.isNull(bizDistributor)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!bizDistributor.getEntityId().equals(serviceCategory.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
    }

}
