/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants.BillingChargeType;
import cn.com.cloudstar.rightcloud.bss.common.constants.CloudEnvType;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingSpecMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingSpecReferMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingChargeMonth;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecGroup;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecRefer;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.SpecVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceSpecDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingSpecService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingChargeMonthService;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.UnitsEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;

import static cn.com.cloudstar.rightcloud.bss.module.billing.service.impl.BizInquiryPriceServiceImpl.keepThreeDecimals;

/**
 * <AUTHOR>
 * Created on 2019/10/24
 */
@Slf4j
@Service
public class BizBillingSpecServiceImpl
        extends ServiceImpl<BizBillingSpecMapper, BizBillingSpec>
        implements BizBillingSpecService {

    /**
     * 下拉框类型的数据
     */
    private static final String INPUT_TYPE_SELECT = "select";

    private static final String  ZONE_TAG = "res_pool";

    private static final String CLOUD_SERVICE = "cloudService";

    @Autowired
    private BizBillingSpecReferMapper specReferMapper;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @Autowired
    private IBizBillingChargeMonthService bizBillingChargeMonthService;

    @Override
    public List<BizBillingSpecRefer> getCloudBillingSpecRefers(BizBillingSpec billingSpec, Long cloudEnvId) {
        if (Strings.isNullOrEmpty(billingSpec.getRefer()) || !INPUT_TYPE_SELECT.equals(billingSpec.getInputType())) {
            return Lists.newArrayListWithCapacity(0);
        }

        Map map = JsonUtil.fromJson(billingSpec.getRefer(), Map.class);
        Object table = map.get("table");
        if (Objects.isNull(table)) {
            return Lists.newArrayListWithCapacity(0);
        }

        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);
        if (Objects.isNull(cloudEnv)) {
            return Lists.newArrayListWithCapacity(0);
        }

        if (ZONE_TAG.equals(table)) {
            if (!CloudEnvType.VMWARE.valueContains(cloudEnv.getCloudEnvType())) {
                map.put("table", "res_zone");
            }
            if (CloudEnvType.OPEN_STACK.valueContains(cloudEnv.getCloudEnvType())) {
                String condition = map.get("condition").toString();
                condition = condition + " and zone_type = 'compute'";
                map.put("condition", condition);
            }
        }

        return specReferMapper.selectByConfig(map, cloudEnvId);
    }

    @Override
    public String packageSpecDesc(String billingConfig, Map<String, BizBillingSpec> specMap, String specKey) {
        if (StrUtil.isEmpty(billingConfig) && CollectionUtil.isEmpty(specMap)) {
            return StrUtil.EMPTY;
        }
        String result = "";
        if (StrUtil.isEmpty(billingConfig)) {
            //start 增量显示
            int m = 1;
            int incrSize = specMap.size();
            for (Entry<String, BizBillingSpec> e : specMap.entrySet()) {
                BizBillingSpec billingSpec = e.getValue();
                if (specKey.contains(billingSpec.getSpecName()) && billingSpec.getBillingMode().equals(BillingChargeType.INC_TYPE)) {
                    result = StrUtil.concat(true, result, Convert.toStr(billingSpec.getSpecDescription()), billingSpec.getUnit());
                    if (m < incrSize) {
                        result = StrUtil.concat(true, result, "、");
                    }
                }
                m++;
            }
            if (StrUtil.isEmpty(result)) {
                return StrUtil.EMPTY;
            }
            return result;
        }

        //start 固定计费显示
        Map<String, Object> configMap = JSONObject.parseObject(billingConfig, LinkedHashMap.class);
        if (configMap.get(CLOUD_SERVICE) != null) {
            return Convert.toStr(configMap.get(CLOUD_SERVICE));
        }

        int n = 1;
        int size = configMap.size();
        for (Entry<String, Object> entry : configMap.entrySet()) {
            String specName = entry.getKey();
            Object specValue = entry.getValue();
            BizBillingSpec spec = specMap.get(specName);
            if (spec == null) {
                continue;
            }
            result = StrUtil.concat(true, result, Convert.toStr(specValue), spec.getUnit());
            if (n < size) {
                result = StrUtil.concat(true, result, "、");
            }
            n++;
        }
        return result;
    }

    @Override
    public String packageSpecName(BizBillingSpecGroup specGroup, BizBillingTariffSpecCharge specCharge,
        AccountPriceSpecDetailVO specDetailVO, Map<String, BizBillingSpec> specMap) {
        if (specGroup == null) {
            return StrUtil.EMPTY;
        }

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        String anl = isUs ? "Pay by volume" : "按量计费";
        String baon = isUs ? "Monthly and yearly packages" : "包年包月";

        // 基础配置
        String hourDisplay = "/小时";
        String monthDisplay = "/月";
        if (BillingChargeType.INC_TYPE.equals(specGroup.getBillingMode())) {
            // 特殊使用量处理 (capacity:容量，storageUsage:使用量，cpuUsage:CPU使用量，gpuUsage:GPU使用量，resourceUsage:资源用量)
            String specType = specCharge.getSpecType();
            if (Objects.nonNull(specType)) {
                switch (specType) {
                    // HPC基础单位判定
                    case "cpuUsage":
                        hourDisplay = UnitsEnum.CPUUSAGE.getUnit();
                        break;
                    case "gpuUsage":
                        hourDisplay = UnitsEnum.GPUUSAGE.getUnit();
                        break;
                    // ModelArts共享资源池基础单位判定
                    case "resourceUsage":
                        hourDisplay = UnitsEnum.RESOURCEUSAGE.getUnit();

                        break;
                    // 存储使用量基础单位判定
                    case "storageUsage":
                    case "capacity":
                        hourDisplay = UnitsEnum.STORAGEUSAGE.getUnit();
                        monthDisplay = "/GB/月";
                        break;
                    case "pflopsResourceUsage":
                        hourDisplay = UnitsEnum.PFLOPSRESOURCEUSAGE.getUnit();
                        break;
                    default:
                        break;
                }
                if (NumberUtil.isGreaterOrEqual(specCharge.getFixedHourPrice(), BigDecimal.ZERO)
                        && NumberUtil.isGreaterOrEqual(specCharge.getUnitHourPrice(), BigDecimal.ZERO)) {
                    //为 0 则不展示
                    if (NumberUtil.equals(specCharge.getFixedHourPrice(), BigDecimal.ZERO)) {
                        specDetailVO.getItems().add(String.format(anl + "：N × ￥%s" + hourDisplay, keepThreeDecimals(specCharge.getUnitHourPrice())));
                    } else {
                        specDetailVO.getItems().add(String.format(anl + "：(￥%s + N × ￥%s)" + hourDisplay, keepThreeDecimals(specCharge.getFixedHourPrice()),
                                keepThreeDecimals(specCharge.getUnitHourPrice())));
                    }
                }
                if (Objects.equals("capacity", specType)) {
                    if (NumberUtil.isGreaterOrEqual(specCharge.getFixedMonthPrice(), BigDecimal.ZERO)
                            && NumberUtil.isGreaterOrEqual(specCharge.getUnitMonthPrice(), BigDecimal.ZERO)) {
                        if (NumberUtil.equals(specCharge.getFixedMonthPrice(), BigDecimal.ZERO)) {
                            specDetailVO.getItems().add(String.format(
                                    baon + "：N × ￥%s" + monthDisplay, BigDecimalUtil.remainTwoPointAmount(specCharge.getUnitMonthPrice())));
                        } else {
                            specDetailVO.getItems().add(String.format(
                                    baon + "：(￥%s + N × ￥%s)" + monthDisplay, BigDecimalUtil.remainTwoPointAmount(specCharge.getFixedMonthPrice()),
                                    BigDecimalUtil.remainTwoPointAmount(specCharge.getUnitMonthPrice())));
                        }
                    }
                }
                return Objects.nonNull(specMap.get(specType)) ? specMap.get(specType).getSpecDescription() : StrUtil.EMPTY;
            }
            if (NumberUtil.isGreaterOrEqual(specCharge.getFixedHourPrice(), BigDecimal.ZERO)
                    && NumberUtil.isGreaterOrEqual(specCharge.getUnitHourPrice(), BigDecimal.ZERO)) {
                //固定收费为 0 则不展示
                if (NumberUtil.equals(specCharge.getFixedHourPrice(), BigDecimal.ZERO)) {
                    specDetailVO.getItems()
                            .add(String.format(anl + "：N × ￥%s" + hourDisplay, keepThreeDecimals(specCharge.getUnitHourPrice())));
                } else {
                    specDetailVO.getItems().add(String.format(anl +  "：(￥%s + N × ￥%s)" + hourDisplay, keepThreeDecimals(specCharge.getFixedHourPrice()),
                            keepThreeDecimals(specCharge.getUnitHourPrice())));
                }
            }
            if (NumberUtil.isGreaterOrEqual(specCharge.getFixedMonthPrice(), BigDecimal.ZERO)
                    && NumberUtil.isGreaterOrEqual(specCharge.getUnitMonthPrice(), BigDecimal.ZERO)) {
                if (NumberUtil.equals(specCharge.getFixedMonthPrice(), BigDecimal.ZERO)) {
                    specDetailVO.getItems().add(String.format(
                            baon + "：N × ￥%s" + monthDisplay, BigDecimalUtil.remainTwoPointAmount(specCharge.getUnitMonthPrice())));
                } else {
                    specDetailVO.getItems().add(String.format(
                            baon + "：(￥%s + N × ￥%s)" + monthDisplay, BigDecimalUtil.remainTwoPointAmount(specCharge.getFixedMonthPrice()),
                            BigDecimalUtil.remainTwoPointAmount(specCharge.getUnitMonthPrice())));
                }
            }

            if (NumberUtil.isGreater(specCharge.getUsageCountPrice(), BigDecimal.ZERO)) {
                specDetailVO.getItems().add(String.format("每%s:￥%s", specCharge.getUsageCount(), specCharge.getUsageCountPrice()));
            }
            return packageSpecDesc(specCharge.getBillingConfig(), specMap, specGroup.getSpec());
        }

        // ModelArts专属和IAAS单位判定
        if (StrUtil.isNotBlank(specCharge.getBillingConfig())
                && specCharge.getBillingConfig().contains("\"businessType\":")
                && specCharge.getBillingConfig().contains("\"spec\":")) {
            hourDisplay = "/个/小时";
        }
        // 是否存在指定月数的计费
        QueryWrapper<BizBillingChargeMonth> chargeMonthQuery = new QueryWrapper<>();
        chargeMonthQuery.lambda().eq(BizBillingChargeMonth::getSpecChargeId, specCharge.getId());
        List<BizBillingChargeMonth> chargeMonths = bizBillingChargeMonthService.list(chargeMonthQuery);
        StringBuilder sb = new StringBuilder();
        if (CollectionUtil.isNotEmpty(chargeMonths)) {
            // 设置指定月数的计费配置
            chargeMonths.forEach(chargeMonth -> {
                sb.append(String.format("[%s个月：￥%s];",
                        chargeMonth.getDuration(), BigDecimalUtil.remainTwoPointAmount(chargeMonth.getPrice())));
            });
        }
        specDetailVO.getItems().add(String.format(anl + "：￥%s" + hourDisplay,
                keepThreeDecimals(specCharge.getHourPrice())));

        specDetailVO.getItems().add(String.format(baon + "：￥%s" + monthDisplay + ";%s",
                                                  BigDecimalUtil.remainTwoPointAmount(specCharge.getMonthPrice()), sb.toString()));

        return packageSpecDesc(specCharge.getBillingConfig(), specMap, specGroup.getSpec());
    }

    @Override
    public Map<String, Object> packageSpecNameOfGroup(List<String> specNames, Map<Long, BizBillingSpec> specMap) {
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtil.isEmpty(specNames) || CollectionUtil.isEmpty(specMap)) {
            return Maps.newHashMap();
        }
        String specName = "";
        int n = 1;
        int size = specNames.size();
        List<SpecVO> specVOS = Lists.newArrayList();
        for (String spec : specNames) {
            BizBillingSpec bizBillingSpec = specMap.get(Long.valueOf(spec));
            if (bizBillingSpec != null) {
                boolean isUs = WebUtil.getHeaderAcceptLanguage();
                String specNameResult = (isUs && StringUtils.isNotBlank(bizBillingSpec.getSpecDescriptionUs())) ?
                        bizBillingSpec.getSpecDescriptionUs() : bizBillingSpec.getSpecDescription();
                SpecVO specVO = new SpecVO();
                specVO.setSpec(bizBillingSpec.getSpecName());
                specVO.setSpecName(specNameResult);
                specVO.setInputType(bizBillingSpec.getInputType());
                specVO.setValueDomain(JSONArray.parseArray(bizBillingSpec.getValueDomain(), String.class));
                if (StrUtil.isNotBlank(bizBillingSpec.getUnit())) {
                    specName = StrUtil.concat(true, specName, specNameResult, "(",
                        bizBillingSpec.getUnit(), ")");
                } else {
                    specName = StrUtil.concat(true, specName, specNameResult);
                }
                if (n < size) {
                    specName = StrUtil.concat(true, specName, StrUtil.COMMA);
                }
                n++;
                specVOS.add(specVO);
            }

        }
        result.put("name", specName);
        result.put("specVO", specVOS);
        return result;
    }
}
