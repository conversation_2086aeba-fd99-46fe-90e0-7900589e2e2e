/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2019-10-24
 */
@Data
@ApiModel(description = "成本明细返回值")
public class DescribeGaapCostResponse {

    /**
     * 云平台资源类型（华为云）
     */
    @ApiModelProperty("云平台资源类型")
    @JsonIgnore
    private String cloudServiceCode;

    /**
     * 产品代码 disk,ecs
     */
    @ApiModelProperty(value = "产品代码", notes = "disk,ecs")
    private String productCode;

    /**
     * 订阅类型 Subscription／PayAsYouGo
     */
    @ApiModelProperty(value = "订阅类型", notes = "Subscription／PayAsYouGo")
    private String subscriptionType;

    /**
     * 账期YYYY－MM
     */
    @ApiModelProperty(value = "账期", notes = "YYYY－MM")
    private String billingCycle;

    /**
     * 订单类型：New(新购)，Renewal(续费)，Upgrade(升级)，Degrade(降级)，BillType非SubscriptionOrder时为空
     */
    @ApiModelProperty(value = "订单类型", notes = "New(新购)，Renewal(续费)，Upgrade(升级)，Degrade(降级)，BillType非SubscriptionOrder时为空")
    @JsonIgnore
    private String orderType;
    private String type;

    /**
     * 原始金额
     */
    @ApiModelProperty(value = "原始金额")
    private BigDecimal pretaxGrossAmount;

    /**
     * 询价优惠
     */
    @ApiModelProperty(value = "询价优惠")
    private BigDecimal pricingDiscount;

    /**
     * 优惠后金额
     */
    @ApiModelProperty(value = "优惠后金额")
    private BigDecimal pretaxAmount;

    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private String instanceId;

    /**
     * 实例名称
     */
    @ApiModelProperty(value = "实例名称")
    private String instanceName;

    /**
     * 规格项
     */
    @ApiModelProperty(value = "规格项")
    private String configuration;

    /**
     * Region
     */
    @ApiModelProperty(value = "Region")
    private String region;

    /**
     * 账单类型：SubscriptionOrder (预付订单)， PayAsYouGoBill (后付账单)， Refund (退款)， Adjustment (调账)
     */
    @ApiModelProperty(value = "账单类型", notes = "SubscriptionOrder (预付订单)， PayAsYouGoBill (后付账单)， Refund (退款)， Adjustment (调账)")
    private String billType;

    /**
     * 费用产生时间:支付时间
     */
    @ApiModelProperty(value = "费用产生时间:支付时间")
    private Date payTime;

    /**
     * 服务起始时间
     */
    @ApiModelProperty(value = "服务起始时间")
    private Date usageStartDate;

    /**
     * 服务结束时间
     */
    @ApiModelProperty(value = "服务结束时间")
    private Date usageEndDate;

    /**
     * 支付币种(国际)
     */
    @ApiModelProperty(value = "支付币种(国际)")
    private String paymentCurrency;

    /**
     * 云环境类型
     */
    @ApiModelProperty(value = "云环境类型")
    private String cloudEnvType;

    /**
     * 云环境名称
     */
    @ApiModelProperty(value = "云环境名称")
    private String cloudEnvName;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderId;

    /**
     * 优惠劵折扣
     */
    @ApiModelProperty(value = "优惠劵折扣")
    private BigDecimal couponDiscount;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 账单号
     */
    @ApiModelProperty(value = "账单号")
    private String billNo;

    /**
     * 组织折扣
     */
    @ApiModelProperty(value = "组织折扣")
    @JsonIgnore
    private BigDecimal orgDiscount;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 账单来源
     */
    @ApiModelProperty("账单来源，平台出账(platform)；账单同步(sync) ")
    private String billSource;

    /**
     * 账单类型
     */
    @ApiModelProperty("账单类型，资源计费（resource）;服务计费(service)，配置计费(extraConfig)")
    private String priceType;


    /**
     * 状态
     */
    @ApiModelProperty("0 未分摊， 1已分摊")
    @JsonIgnore
    private String status;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    @JsonIgnore
    private String description;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 资源配置
     */
    @ApiModelProperty("资源配置")
    @JsonIgnore
    private String resourceConfig;

    /**
     * 原始平台金额
     */
    @ApiModelProperty(value = "原始平台金额")
    private BigDecimal officialAmount;
    /**
     * 官方字符串数量
     */
    @JsonIgnore
    private String officialAmountString;

    /**
     * 使用量
     */
    @ApiModelProperty(value = "使用量")
    private String usageCount;

    /**
     * 所属分销商名称
     */
    @ApiModelProperty("所属分销商名称")
    private String distributorName;

    /**
     * 计费周期归档表关联id
     */
    @ApiModelProperty("计费周期归档表关联id")
    @JsonIgnore
    private String billBillingCycleId;

    /**
     * 开票状态
     */
    @ApiModelProperty("开票状态")
    private String invoiceStatus;


    /**
     * 折扣金额
     */
    @ApiModelProperty("折扣金额")
    @JsonIgnore
    private BigDecimal discountAmount;

    /**
     * 现金账户支付金额
     */
    @ApiModelProperty(name = "cashAmount", value = "现金账户支付金额")
    private BigDecimal cashAmount;

    /**
     * 信用额度支付金额
     */
    @ApiModelProperty(name = "creditAmount", value = "信用额度支付金额")
    private BigDecimal creditAmount;

    /**
     * 代金卷支付金额
     */
    @ApiModelProperty(name = "voucherAmount", value = "代金卷支付金额")
    @JsonIgnore
    private BigDecimal voucherAmount;

    /**
     * 可开票金额
     */
    @ApiModelProperty(name = "invoiceAmount", value = "可开票金额")
    @JsonIgnore
    private BigDecimal invoiceAmount;

    /**
     * 代金券支付金额
     */
    @ApiModelProperty(name = "couponAmount", value = "代金券支付金额")
    private BigDecimal couponAmount;

    /**
     * 抵扣现金券支付金额
     */
    @ApiModelProperty(name = "deductCouponDiscount", value = "抵扣现金券支付金额")
    private BigDecimal deductCouponDiscount;

    /**
     * 所有折扣
     */
    @JsonIgnore
    private BigDecimal allDiscount;

    /**
     * 支付时间str
     */
    @JsonIgnore
    private String payTimeStr;

    /**
     * 使用结束日期str
     */
    @JsonIgnore
    private String usageEndDateStr;

    /**
     * 使用起始日期str
     */
    private String usageStartDateStr;

    /**
     * accountID-用户处理逻辑数据
     */
    private String userAccountId;

    private String userAccountIdStr;

    /**
     * 用户名
     */
    private String userAccountName;

    /**
     * 组织sid
     */
    private Long orgSid;
    /**
     * 有效算力（CUE）
     */
    private String cueValue;

    /**
     *  算力
     */
    private String computingPower;

    /**
     * 是否可开票
     */
    @JsonIgnore
    private String invoicable;
    /**
     * 抵扣信用余额
     */
    private BigDecimal rechargeCreditAmount;


    /**
     * 套餐包产品ID
     */
    @JsonIgnore
    private Long bagId;
    /**
     * 用户套餐折扣包订购实例ID
     */
    @JsonIgnore
    private String bagDiscountInstId;
    /**
     * 套餐包折扣 如0.88==88折
     */
    private Double bagDiscount;
    /**
     * 套餐优惠价格
     */
    private BigDecimal bagDiscountAmount;
    /**
     * 套餐包抵扣之前价格
     */
    private BigDecimal beforeBagDiscountAmount;
    /**
     * 套餐包规格
     */
    private String bagSpecName;
    /**
     * 卡时包使用数量
     */
    private BigDecimal cardHourUsageAmount;

    /**
     * 卡时包剩余数量
     */
    private BigDecimal cardHourRemainingAmount;
    /**
     * 作业ID
     */
    @JsonIgnore
    private String jobId;
    /**
     * 作业名称
     */
    @JsonIgnore
    private String jobName;
    /**
     * 集群名称
     */
    @JsonIgnore
    private String clusterName;
    /**
     *  租户
     */
    @JsonIgnore
    private String userGroup1;
    /**
     * Account （话单中tag字段的数据）
     */
    @JsonIgnore
    private String account;
    /**
     * 作业提交时间
     */
    @JsonIgnore
    private Date submitTime;
    /**
     * 作业开始时间
     */
    @JsonIgnore
    private Date startTime;
    /**
     * 作业完成时间
     */
    @JsonIgnore
    private Date endTime;
    /**
     * 作业使用CPU总核数/GPU总卡数
     */
    @JsonIgnore
    private String count;

    /**
     * 提交时间str
     */
    @JsonIgnore
    private String submitTimeStr;

    /**
     * 开始时间str
     */
    @JsonIgnore
    private String startTimeStr;

    /**
     * 结束时间str
     */
    @JsonIgnore
    private String endTimeStr;

    /**
     * 抹零列金额
     */
    @ApiModelProperty("抹零金额")
    private BigDecimal eraseZeroAmount;
    /**
     * 收费规则：02:销售计费、01:正常计费
     */
    private String chargingType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 实体名称
     */
    private String entityName;

    /**
     *  算力
     */
    private String cueShowOrHide;

    /**
     * 集群ID
     */
    private Long clusterId;

    /**
     * 使用的卡时
     */
    private BigDecimal usedCardHourAmount;

    /**
     * 使用的卡时String
     */
    private String usedCardHourAmountString;

    /**
     * 订单来源
     */
    private String orderSourceSn;

    /**
     * 统计小时数
     */
    private BigDecimal statisticHours;

    /**
     * 统计天数
     */
    private BigDecimal statisticDays;


    @ApiModelProperty(value = "计费用量和免费用量")
    private String billedAndFree;
}
