/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.MongoCursor;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bson.Document;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.OrgType;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.HCSOTimeService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ActionParam;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.OperateNodeRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductConfigDesc;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpgradeServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.UpgradeDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeSysConfigByTypeListRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MessageService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ServiceFeignService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ModelArtsPoolAllocate;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.request.ResizeResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.response.DescribeSysConfigResponse;
import cn.com.cloudstar.rightcloud.bss.module.order.factory.OrderServiceFactory;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IOrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.SystemConfigService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizAccountProductQuota;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizProductQuota;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizProductQuotaWhiteList;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ProductBaseConfig;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizAccountProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaWhiteListService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.SysMNotifyConfigBssMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.common.enums.FreezingStrategyEnum;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.tracelog.TraceUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.pojo.common.SampleNodeInfo;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.BssTypeEnum;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.StatusEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMNotifyConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMNotifyConfigExample;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants.Sfs2Message;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ServiceConfigArrKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.MaPoolTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.request.ProductSpecDefineFeignForm;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.result.ProductSpecDefineFeignResult;
import cn.com.cloudstar.rightcloud.oss.common.util.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * 产品服务
 *
 * <AUTHOR>
 * @date 2020/6/15.
 */
@Api(tags = "产品服务")
@Slf4j
@RestController
@RequestMapping("/service")
@Validated
public class ServiceController {

    @Autowired
    private OrderServiceFactory orderServiceFactory;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @Autowired
    private ServiceCategoryService serviceCategoryService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IOrderService orderService;
    @Autowired
    private OrderService ossOrderService;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private BizProductQuotaService bizProductQuotaService;

    @Autowired
    private IBizDistributorProductService bizDistributorProductService;

    @Autowired
    private BizAccountProductQuotaService bizAccountProductQuotaService;
    @Autowired
    private HpcClusterService hpcClusterService;
    @Autowired
    private BizProductQuotaWhiteListService bizProductQuotaWhiteListService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Resource
    private SysMNotifyConfigBssMapper notifyConfigBssMapper;
    @Autowired
    HCSOTimeService hcsoTimeService;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    StorageService storageService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SysMNotifyConfigBssMapper sysMNotifyConfigBssMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private ServiceFeignService serviceFeignService;



    /**
     * APPLY
     */
    private static final String APPLY = "apply";

    /**
     * APPROVAL
     */
    private static final String APPROVAL = "approval";

    private static final String  ACCOUNT_EXPIRE = "ACCOUNT_EXPIRE";


    private static final String ACTIVE = "ACTIVE";

    /**
     * 申请服务
     *
     * @param request 服务申请请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CB.CB1901)
    @ApiOperation(httpMethod = "POST", value = "申请服务")
    @PostMapping("/apply")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.productName",
            resource = OperationResourceEnum.OPENSFS,bizId = "#request.projectId",param = "#request")
    @Idempotent
    public RestResult apply(@Valid @RequestBody ApplyServiceRequest request) {
        List<ProductInfoVO> productInfo = request.getProductInfo();
        request.setProductInfo(productInfo.stream()
                                          .filter(t -> Objects.nonNull(t.getServiceId()))
                                          .collect(Collectors.toList()));
        checkSFSCapacity(request);
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (sfProductResourceService.checkMaName(request.getProductName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_157327773));
        }
        if (StringUtils.isNotBlank(request.getProductInfo().get(0).getApplyType())
                && !Stream.of(ApplyTypeEnum.DEPTRAIN.getType(), ApplyTypeEnum.DEPONLINE.getType())
                .collect(Collectors.toList())
                .contains(request.getProductInfo().get(0).getApplyType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_196979320));
        }
        //校验上传文件
        for (ProductInfoVO productInfoVO : request.getProductInfo()) {
            String currentDesc = Optional.ofNullable(productInfoVO.getProductConfigDesc())
                    .map(ProductConfigDesc::getCurrentConfigDesc)
                    .orElse(null);
            if (ObjectUtils.isEmpty(currentDesc)) {
                continue;
            }
            JsonNode jsonNode = JsonUtil.fromJson(currentDesc);
            for (JsonNode node : jsonNode) {
                String value = Optional.ofNullable(node.get("value"))
                        .map(JsonNode::toString)
                        .orElse(null);
                if (ObjectUtils.isEmpty(value)) {
                    continue;
                }
                //校验文件路径，如果文件路径包含minio的nginx拦截路径
                if (value.contains(storageService.getConfig().getInterceptUrl())) {
                    List<String> paths = StrUtil.split(value, ",");
                    paths.forEach(val -> storageService.validateFilePath(val.replace("\"", ""), StoragePathEnum.GENERAL, null));
                }
            }
        }
        ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);
        ApplyServiceVO backUpApplyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);

        RestResult errorMessage1 = this.checkParam(request, applyServiceVO, backUpApplyServiceVO);
        if (errorMessage1 != null) {
            return errorMessage1;
        }
        String errorMessage;

        // 执行申请服务
        errorMessage = orderService.orderServiceAction(backUpApplyServiceVO);
        if (StrUtil.isNotBlank(errorMessage)) {
            return new RestResult(Status.FAILURE, errorMessage);
        }

        // 重新获取当前管理员信息
        if (Objects.nonNull(backUpApplyServiceVO.getBehalfUserSid())) {
            AuthUser authUserInfo =
                    BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(backUpApplyServiceVO.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }

        return new RestResult(RestResult.Status.SUCCESS,
                              WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS)
        );
    }



    /**
     * 申请服务
     *
     * @param request 服务申请请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CB.CB1901)
    @ApiOperation(httpMethod = "POST", value = "申请服务")
    @PostMapping("/res_apply")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.productName",
            resource = OperationResourceEnum.OPENSFS,bizId = "#request.projectId",param = "#request")
    @Idempotent
    public RestResult resApply(@Valid @RequestBody ApplyServiceRequest request) {
        convertReq(request);
        List<ProductInfoVO> productInfo = request.getProductInfo();
        request.setProductInfo(productInfo.stream().map(e -> {
            List<ServiceCategory> categoryList = serviceCategoryService.getServiceCategoryByServiceType(e.getProductCode());
            if (CollectionUtils.isNotEmpty(categoryList)) {
                e.setServiceId(categoryList.get(0).getId());
            }
            return e;
        }).filter(t -> Objects.nonNull(t.getServiceId())).collect(Collectors.toList()));
        ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);

        RestResult restResult = resCheckParam(applyServiceVO);
        if (!restResult.getStatus()) {
            return restResult;
        }
        // 执行申请服务
        String errorMessage = orderService.orderServiceAction(applyServiceVO);
        if (StrUtil.isNotBlank(errorMessage)) {
            return new RestResult(Status.FAILURE, errorMessage);
        }
        if (Objects.nonNull(applyServiceVO.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return restResult;
    }

    private void convertReq(ApplyServiceRequest request) {
        if (CollUtil.isEmpty(request.getProductInfo())) {
            return;
        }
        ProductInfoVO productInfoVO = request.getProductInfo().get(0);
        if (request.getProductInfo().get(0).getProductCode().equalsIgnoreCase("DCS")) {
            request.getProductInfo().get(0).setAmount(1);
        }
        Map<String, Object> map = BeanUtil.beanToMap(request.getProductInfo().get(0).getData());
        if (ProductCodeEnum.CSS.getProductCode().equals(productInfoVO.getProductCode())) {
            map = new HashMap<>(1);
            Map<String, Object> m = new HashMap<>(4);
            m.put("chargeItemCategory","compute");
            m.put("category","css");
            m.put("spec","css");
            m.put("productCode", ProductCodeEnum.CSS.getProductCode());
            map.put(ProductCodeEnum.CSS.getProductCode(), m);
            request.getProductInfo().get(0).setData(map);
        }

        if (ProductCodeEnum.ECS.getProductCode().equals(request.getProductInfo().get(0).getProductCode())
                || ProductCodeEnum.RS_BMS.getProductCode().equals(request.getProductInfo().get(0).getProductCode())) {
            ProductSpecDefineFeignForm form = new ProductSpecDefineFeignForm();
            form.setProductCode("EBS");
            form.setStatus("enable");
            form.setRequiredAttr(true);
            cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult<List<ProductSpecDefineFeignResult>> productSpecDefine = serviceFeignService.getProductSpecDefine(form);
            Object specList = productSpecDefine.getData();
            Map<Long, String> specMap = new HashMap<>();
            if (Objects.nonNull(specList)) {
                List<ProductSpecDefineFeignResult> productSpecDefines = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
                specMap = productSpecDefines.stream().collect(Collectors.toMap(ProductSpecDefineFeignResult::getSpecTypeId, ProductSpecDefineFeignResult::getProductSpecCode));
            }
            if (map.containsKey("sysDisk") && Objects.nonNull(map.get("sysDisk"))) {
                Object disk = map.get("sysDisk");
                map.remove("sysDisk");
                request.getProductInfo().add(packegeDisk(true, disk, specMap, request.getProductInfo().get(0)));
            }
            if (map.containsKey("dataDisk") && Objects.nonNull(map.get("dataDisk"))) {
                Object diskObj = map.get("dataDisk");
                map.remove("dataDisk");
                JSONArray disks = JSON.parseArray(JSON.toJSONString(diskObj));
                for (Object disk : disks) {
                    request.getProductInfo().add(packegeDisk(false, disk, specMap, request.getProductInfo().get(0)));
                }
            }
            request.getProductInfo().get(0).setData(map);
        }
        if (ProductCodeEnum.RDS.getProductCode().equals(productInfoVO.getProductCode())) {
            final String currentConfigDesc = productInfoVO.getProductConfigDesc().getCurrentConfigDesc();
            if(ObjectUtil.isNotEmpty(currentConfigDesc)){
                final JSONObject object = JSONObject.parseObject(currentConfigDesc);
                JSONArray configDescJSONArray = object.getJSONArray("productConfigDesc");
                Set<Object> jsonObjects = new HashSet<>();
                for (Object obj : configDescJSONArray) {
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
                    if (jsonObject.get("label").equals("存储类型")) {
                        jsonObjects.add(jsonObject);
                    }
                    if (jsonObject.get("label").equals("存储空间(GB)")) {
                        jsonObjects.add(jsonObject);
                    }
                }
                configDescJSONArray.removeAll(jsonObjects);
                object.put("productConfigDesc",configDescJSONArray);
                request.getProductInfo().get(0).getProductConfigDesc().setCurrentConfigDesc(JSONObject.toJSONString(object));
            }
        }
    }


    /**
     * 验证开通参数
     * @param request
     * @param applyServiceVO
     * @param backUpApplyServiceVO
     * @return
     */
    private RestResult checkParam(@RequestBody @Valid ApplyServiceRequest request, ApplyServiceVO applyServiceVO, ApplyServiceVO backUpApplyServiceVO) {
        /*
         * 许可证相关判断
         * 1.判断是否过期
         * 2.标准版许可证只能开通部署上线类型和非逻辑资源池
         * 3.判断ma和hpc的使用量是否达到百分之95，超过发送扩容通知
         *
         */
        if (LicenseUtil.isExpireLicense()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1985111101));
        }

        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        String versionType = licenseVo.getVersionType();
        boolean allowDrpType = licenseVo.isAllowDrpType();

        // 检查开通产品
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (StringUtils.equals(user.getUserType(), UserType.PLATFORM_USER) && Objects.nonNull(user.getParentSid())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_185982018));
        }
        Long userSid = Objects.nonNull(applyServiceVO.getBehalfUserSid()) ? applyServiceVO.getUserSid() : user.getUserSid();
        String orderStatus = orderService.checkProduct(applyServiceVO, userSid);
        if (APPLY.equals(request.getOrderType())) {
            if (LicenseUtil.STAND.equals(versionType) && !allowDrpType) {
                // 是开通操作&&是标准许可证&&许可证是标准的：可以开通ma共享资源池,和ma的部署上线类型和物理资源池可以开通 其他都不行;
                boolean anyMatch = request.getProductInfo().stream().anyMatch(a -> {
                    boolean result = false;
                    if (ProductCodeEnum.MODEL_ARTS.getProductCode().equalsIgnoreCase(a.getProductCode())) {
                        result = true;
                    }
                    if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(a.getProductCode())
                            && !MaPoolTypeEnum.LOGICAL.getType().equalsIgnoreCase(a.getPoolType())
                            && !ApplyTypeEnum.DEPTRAIN.getType().equalsIgnoreCase(a.getApplyType())) {
                        result = true;
                    }
                    // 返回true是放行
                    return result;
                });

                if (!anyMatch) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_613976347));
                }
            }


            // ma所有节点
            List<ModelArtsPoolAllocate> modelArts = this.getModelArts();
            // hpc所有节点
            List<SampleNodeInfo> hpcNodeInfo = mongoTemplate.findAll(SampleNodeInfo.class);
            Integer aiNodeNumber = licenseVo.getAiNodeNumber();
            Integer hpcNodeNumber = licenseVo.getHpcNodeNumber();
            // 过滤使用量 只验证ma和hpc专属池
            boolean maFlag = request.getProductInfo().stream().anyMatch(a -> ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equalsIgnoreCase(a.getProductCode()));
            int multiple = 100;
            int flag = 95;
            int scale = 2;

            if (maFlag) {
                int sum= modelArts.size() + request.getProductInfo().stream().filter(f -> ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equalsIgnoreCase(f.getProductCode())).mapToInt(ProductInfoVO::getAmount).sum();
                double rate = NumberUtil.div(sum,aiNodeNumber.doubleValue(),scale);
                if (NumberUtil.mul(rate,multiple) >= flag) {
                    this.sendMessage(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductName(),NumberUtil.mul(rate,multiple));
                }
                if (sum > aiNodeNumber) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1112833614));
                }

            }

            boolean hpcFlag = request.getProductInfo().stream().anyMatch(a -> ProductCodeEnum.HPC_DRP_STANDARD.getProductCode().equalsIgnoreCase(a.getProductCode()) || ProductCodeEnum.HPC_DRP.getProductCode().equalsIgnoreCase(a.getProductCode()));
            if (hpcFlag) {
                int sum = hpcNodeInfo.size() + request.getProductInfo().stream().mapToInt(ProductInfoVO::getAmount).sum();
                double rate = NumberUtil.div(sum, hpcNodeNumber.doubleValue(),scale);
                if (NumberUtil.mul(rate,multiple) >= flag) {
                    this.sendMessage("HPC专属资源池",NumberUtil.mul(rate,multiple));
                }
                if (sum > hpcNodeNumber) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1590100583));
                }
            }
        }

        String errorMessage = StrUtil.EMPTY;

        //校验上传文件
        for (ProductInfoVO productInfoVO : request.getProductInfo()) {
            String currentDesc = Optional.ofNullable(productInfoVO.getProductConfigDesc())
                    .map(ProductConfigDesc::getCurrentConfigDesc)
                    .orElse(null);
            if (ObjectUtils.isEmpty(currentDesc)) {
                continue;
            }
            JsonNode jsonNode = JsonUtil.fromJson(currentDesc);
            for (JsonNode node : jsonNode) {
                String value = Optional.ofNullable(node.get("value"))
                        .map(JsonNode::toString)
                        .orElse(null);
                if (ObjectUtils.isEmpty(value)) {
                    continue;
                }
                //校验文件路径，如果文件路径包含minio的nginx拦截路径
                if (value.contains(storageService.getConfig().getInterceptUrl())) {
                    List<String> paths = StrUtil.split(value, ",");
                    paths.forEach(val -> storageService.validateFilePath(val.replace("\"", ""), StoragePathEnum.GENERAL, null));
                }
            }
        }
        //校验开通参数 订购时常和弹性文件名称
        //校验开通参数 订购时常和弹性文件名称
        List<String> chargetType = request.getProductInfo()
                                          .stream()
                                          .map(ProductInfoVO::getChargeType)
                                          .collect(Collectors.toList());
        if (chargetType.size() > 0) {
            if (ChargeTypeEnum.PREPAID.getValue().equals(chargetType.get(0))) {
                List<BigDecimal> periods = request.getProductInfo()
                                                  .stream()
                                                  .map(ProductInfoVO::getPeriod)
                                                  .collect(Collectors.toList());
                if (periods.size() > 0) {
                    if (new BigDecimal(periods.get(0).intValue()).compareTo(periods.get(0)) != 0 || (
                            periods.get(0).intValue() < 1) || (periods.get(0).intValue() > 36)) {
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_244609625) + periods.get(0) + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
                //产品名称不能为空
                if (StrUtil.isEmpty(request.getProductName())) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }
            }
            //购买数量进行校验
            List<Integer> amounts = request.getProductInfo()
                                           .stream()
                                           .map(ProductInfoVO::getAmount)
                                           .collect(Collectors.toList());
            if (amounts.size() > 0) {
                for (int i = 0; i < amounts.size(); i++) {
                    if (amounts.get(i) < 0) {
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_358450119) + amounts.get(i) + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
            }
        }


        if (SfProductEnum.UNSUBSCRIBING.getStatus().equalsIgnoreCase(orderStatus)) {
            return new RestResult(Status.FAILURE, ProductCodeEnum.MODEL_ARTS.getProductName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_36554443));
        }

        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(applyServiceVO.getBehalfUserSid())) {
            // 开通产品需用户账户
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(applyServiceVO.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
            user = authUserInfo;
            if (APPLY.equals(orderStatus)) {
                // 自动申请产品
                applyServiceVO.setProductName(null);
                errorMessage = orderService.modelArtsOrder(applyServiceVO);
                if (StrUtil.isNotBlank(errorMessage)) {
                    return new RestResult(Status.FAILURE, errorMessage);
                }
            }
            if (APPROVAL.equals(orderStatus) || APPLY.equals(orderStatus)) {
                // 审核产品需管理员用户
                authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getBehalfUserSid()), AuthUser.class);
                AuthUserHolder.setAuthUser(authUserInfo);
                // 自动审核订单
                errorMessage = orderService.modelArtsOpen(applyServiceVO);
                if (StrUtil.isNotBlank(errorMessage)) {
                    return new RestResult(Status.FAILURE, errorMessage);
                }
                authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getUserSid()), AuthUser.class);
                AuthUserHolder.setAuthUser(authUserInfo);
                AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(applyServiceVO.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
            }
        }

        // 产品限额
        List<Long> serviceIdList = request.getProductInfo()
                                          .stream()
                                          .map(ProductInfoVO::getServiceId)
                                          .collect(Collectors.toList());
        String minAmount = productLimit(userSid, serviceIdList, request.getProductInfo().get(0));
        if (Strings.isNotEmpty(minAmount)) {
            log.info("ServiceController.checkParam 产品限额： 【{}】", WebUtil.getMessage(MsgCd.LIMIT_MIN_AMOUNT, new Object[]{minAmount}));
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.LIMIT_MIN_AMOUNT, new Object[]{minAmount}));
        }

        List<Org> orgs = orgService.selectOrgSidBySid(user.getCompanyId());
        if (orgs.size() > 0) {
            if (orgs.stream().noneMatch(orgSid -> orgSid.getOrgSid().equals(request.getProjectId()))) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1156967109));
            }
        }

        //校验企业是否认证中
        List<Org> orgList = orgService.selectOrgByUserSidAndType(user.getOrgSid(), OrgType.COMPANY);
        if (orgList.size() > 0) {
            List<String> certificationStatusList = orgList.stream()
                                                          .map(Org::getCertificationStatus)
                                                          .collect(Collectors.toList());
            if (certificationStatusList.size() > 0) {
                if ("authing".equals(certificationStatusList.get(0))) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1323473283));
                }
            }
        }

        // AI精细化冻结
        for (Long serviceId : serviceIdList) {
            if (licenseVo.isHpcAndAiFineControl()) {
                continue;
            }
            ServiceCategory serviceCategory = serviceCategoryService.getById(serviceId);
            String serviceType = serviceCategory.getServiceType();
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(serviceType)
                    || ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceType)) {
                String freezingStrategy = request.getFreezingStrategy();
                if (Objects.isNull(freezingStrategy)) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.INNER_MSG_0033));
                }
                if (!FreezingStrategyEnum.RELEASE_RESOURCE.getCode().equals(freezingStrategy)
                        && !FreezingStrategyEnum.RESERVED_RESOURCE.getCode().equals(freezingStrategy)) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.INNER_MSG_0034));
                }
                if (FreezingStrategyEnum.RELEASE_RESOURCE.getCode().equals(freezingStrategy)) {
                    String bssType = BssTypeEnum.DRP_RESOURCE_EXPIRE.getValue();
                    if (ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceType)) {
                        bssType = BssTypeEnum.HPC_DRP_RESOURCE_EXPIRE.getValue();
                    }
                    List<SysMNotifyConfig> sysMNotifyConfigs = sysMNotifyConfigBssMapper.selectByBssTypeAndStatus(
                            bssType, StatusEnum.ENABLE.getValue());
                    if (CollectionUtil.isEmpty(sysMNotifyConfigs) || Constants.NONE.equals(
                            CollectionUtil.getFirst(sysMNotifyConfigs).getExpireStrategy())) {
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.INNER_MSG_0034));
                    }
                }
            }
            boolean b = ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(serviceType)
                    || ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceType)
                    || ProductCodeEnum.HPC.getProductCode().equals(serviceType);
            if (b || ProductCodeEnum.HPC_SAAS.getProductCode().equals(serviceType)
                    || ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceType)) {
                String currentConfigDesc = backUpApplyServiceVO.getProductInfo()
                                                               .get(0)
                                                               .getProductConfigDesc()
                                                               .getCurrentConfigDesc();
                cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(currentConfigDesc);
                JSONObject item = new JSONObject();
                String label = "资源池到期策略";
                if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceType)
                        || ProductCodeEnum.HPC.getProductCode().equals(serviceType)
                        || ProductCodeEnum.HPC_SAAS.getProductCode().equals(serviceType)) {
                    label = "资源池冻结策略";
                }
                item.put("label" , label);
                item.put("attrKey" , "freezingStrategy");
                item.put("value" , FreezingStrategyEnum.findByCode(request.getFreezingStrategy()).getName());
                if (ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceType)) {
                    JSONObject desc = new JSONObject();
                    desc.put("label" , "资源池到期策略");
                    desc.put("value" , FreezingStrategyEnum.findByCode(request.getFreezingStrategy()).getName());
                    desc.put("isShow" , true);
                    item.put("desc", desc);
                }
                jsonArray.add(item);
                backUpApplyServiceVO.getProductInfo()
                                    .get(0)
                                    .getProductConfigDesc()
                                    .setCurrentConfigDesc(jsonArray.toString());
            }
        }

        // 执行申请服务
        errorMessage = orderService.orderServiceAction(backUpApplyServiceVO);
        if (StrUtil.isNotBlank(errorMessage)) {
            return new RestResult(Status.FAILURE, errorMessage);
        }

        // 重新获取当前管理员信息
        if (Objects.nonNull(backUpApplyServiceVO.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(backUpApplyServiceVO.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }

        return new RestResult(RestResult.Status.SUCCESS,
                              WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS)
        );
    }


    /**
     * 验证开通参数
     * @param applyServiceVO
     * @return
     */
    private RestResult resCheckParam(ApplyServiceVO applyServiceVO) {
        if (LicenseUtil.isExpireLicense()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1985111101));
        }
//        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();

        // 检查开通产品
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (StringUtils.equals(user.getUserType(), UserType.PLATFORM_USER) && Objects.nonNull(user.getParentSid())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_185982018));
        }

        //校验开通参数 订购时常和弹性文件名称
        List<ProductInfoVO> productInfo = applyServiceVO.getProductInfo();
        List<String> chargetType = productInfo.stream().map(ProductInfoVO::getChargeType).collect(Collectors.toList());
        if (chargetType.size() > 0) {
            if (ChargeTypeEnum.PREPAID.getValue().equals(chargetType.get(0))) {
                List<BigDecimal> periods = applyServiceVO.getProductInfo()
                        .stream()
                        .map(ProductInfoVO::getPeriod)
                        .collect(Collectors.toList());
                if (periods.size() > 0) {
                    if (new BigDecimal(periods.get(0).intValue()).compareTo(periods.get(0)) != 0 || (
                            periods.get(0).intValue() < 1) || (periods.get(0).intValue() > 36)) {
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_244609625) + periods.get(0) + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
                //产品名称不能为空
                if (StrUtil.isEmpty(applyServiceVO.getProductName())) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361916813));
                }
            }
            //购买数量进行校验
            List<Integer> amounts = productInfo.stream().map(ProductInfoVO::getAmount).collect(Collectors.toList());
            if (amounts.size() > 0) {
                for (int i = 0; i < amounts.size(); i++) {
                    if (amounts.get(i) < 0) {
                        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_358450119) + amounts.get(i) + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
                    }
                }
            }
        }

        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(applyServiceVO.getBehalfUserSid())) {
            // 开通产品需用户账户
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(applyServiceVO.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
            user = authUserInfo;
        }

//        List<Org> orgs = orgService.selectOrgSidBySid(user.getCompanyId());
//        if (orgs.size() > 0) {
//            if (orgs.stream().noneMatch(orgSid -> orgSid.getOrgSid().equals(applyServiceVO.getProjectId()))) {
//                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1156967109));
//            }
//        }
        // 产品限额
        Long userSid = Objects.nonNull(applyServiceVO.getBehalfUserSid()) ? applyServiceVO.getUserSid() : user.getUserSid();
        List<Long> serviceIdList = applyServiceVO.getProductInfo()
                                                 .stream()
                                                 .map(ProductInfoVO::getServiceId)
                                                 .collect(Collectors.toList());
        String minAmount = productLimit(userSid, serviceIdList, applyServiceVO.getProductInfo().get(0));
        if (Strings.isNotEmpty(minAmount)) {
            log.info("ServiceController.resCheckParam 产品限额： 【{}】", WebUtil.getMessage(MsgCd.LIMIT_MIN_AMOUNT, new Object[]{minAmount}));
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.LIMIT_MIN_AMOUNT, new Object[]{minAmount}));
        }

        //校验企业是否认证中
        List<Org> orgList = orgService.selectOrgByUserSidAndType(user.getOrgSid(), OrgType.COMPANY);
        boolean orgAuth = false;
        if (orgList.size() > 0) {
            List<String> certificationStatusList = orgList.stream().map(Org::getCertificationStatus).collect(Collectors.toList());
            if (certificationStatusList.size() > 0) {
                if ("authing".equals(certificationStatusList.get(0))) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1323473283));
                }
                orgAuth = certificationStatusList.get(0).contains(CertificationStatus.AUTHSUCCEED);
            }
        }
        if (!CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus()) && !orgAuth) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2102672431));
        }
        // AI精细化冻结
//        List<Long> serviceIdList = productInfo.stream().map(ProductInfoVO::getServiceId).collect(Collectors.toList());

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * [INNER API] 申请服务
     *
     * @param request 服务申请请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("申请服务")
    @PostMapping("/apply/feign")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.productName",
            resource = OperationResourceEnum.OPENSFS,bizId = "#request.projectId",param = "#request")
    @Idempotent
    public RestResult applyFeign(@Valid @RequestBody ApplyServiceRequest request) {
        if (!ObjectUtils.isEmpty(request.getUserSid())) {
            AuthUser user = BasicInfoUtil.getUserInfoByUserSid(request.getUserSid());
            if (ObjectUtils.isEmpty(user) || "8".equals(user.getStatus())) {
                BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_867857480));
            }
            if ("0".equals(user.getStatus())) {
                BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_735250124));
            }
            if ("2".equals(user.getStatus())) {
                BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_146081217));
            }
        }
        checkSFSCapacity(request);
        if (sfProductResourceService.checkMaName(request.getProductName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_157327773));
        }
        if (StringUtils.isNotBlank(request.getProductInfo().get(0).getApplyType())
                && !Stream.of(ApplyTypeEnum.DEPTRAIN.getType(), ApplyTypeEnum.DEPONLINE.getType())
                .collect(Collectors.toList())
                .contains(request.getProductInfo().get(0).getApplyType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_196979320));
        }
        ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);
        ApplyServiceVO backUpApplyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);

        RestResult errorMessage1 = this.checkParam(request, applyServiceVO, backUpApplyServiceVO);
        if (errorMessage1 != null) {
            return errorMessage1;
            }
        String errorMessage;

        // 执行申请服务
        errorMessage = orderService.orderServiceAction(backUpApplyServiceVO);
        if (StrUtil.isNotBlank(errorMessage)) {
            return new RestResult(Status.FAILURE, errorMessage);
        }

        // 重新获取当前管理员信息
        if (Objects.nonNull(backUpApplyServiceVO.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(backUpApplyServiceVO.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }

        return new RestResult(RestResult.Status.SUCCESS,
                WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS)
        );
    }

    /**
     * 申请模型集市产品
     *
     * @param request 服务申请请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("申请模型集市服务")
    @PostMapping("/apply/market")
    @Idempotent
    public RestResult applyMarket(@Valid @RequestBody ApplyServiceRequest request) {
        ApplyServiceVO applyMarketVo = BeanConvertUtil.convert(request, ApplyServiceVO.class);

        try {
            // 执行申请服务
            String errorMessage = orderService.orderServiceMarket(applyMarketVo);
            if (StrUtil.isNotBlank(errorMessage)) {
                return new RestResult(Status.FAILURE, errorMessage);
            }

        }catch (BizException e){
            log.error("申请模型集市服务-订阅失败：{}", e.getMessage());
            return new RestResult(Status.FAILURE, e.getMessage());
        }

        return new RestResult(RestResult.Status.SUCCESS,
                              WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS)
        );
    }


    /**
     * 使用量超过95% 消息通知
     * @param productCode 产品
     * @param rate 使用量
     */
    private void sendMessage(String productCode, double rate) {
        Map<String, Object> messageContent = new HashMap<>(2);
        messageContent.put("productName", productCode);
        messageContent.put("useCount", ((int)rate)+"%");
        List<User> users = userMapper.selectUserByDataScope();
        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_LICENSE_SCALE_UP);
        baseNotificationMqBean.getToUserIds().addAll(users.stream().map(User::getUserSid).collect(Collectors.toList()));
        baseNotificationMqBean.setMap(messageContent);
        baseNotificationMqBean.setBssSendCount(1);
        baseNotificationMqBean.setBssType(SysMNotifyConfigConstant.BssTypeEnum.LICENSE_EXPIRE.getValue());
        baseNotificationMqBean.setSnapshotValue(-1L);
        baseNotificationMqBean.setEntityId(1L);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, baseNotificationMqBean);
    }


    private void checkSFSCapacity(ApplyServiceRequest applyServiceRequest) {
        List<ProductInfoVO> SFSInfo = applyServiceRequest.getProductInfo()
                                                         .stream()
                                                         .filter(info -> StringUtils.equalsIgnoreCase(
                                                                 info.getProductCode(),
                                                                 ProductCodeEnum.SFS.getProductCode()))
                                                         .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(SFSInfo)) {
            //获取服务开通中的SFS信息
            ProductInfoVO infoVO = SFSInfo.get(0);
            ProductConfigDesc productConfigDesc = infoVO.getProductConfigDesc();
            String currentConfigDesc = productConfigDesc.getCurrentConfigDesc();
            JSONArray jsonArray = JSONObject.parseArray(currentConfigDesc);

            if (CollectionUtil.isNotEmpty(jsonArray)) {
                for (Object jsonO : jsonArray) {
                    JSONObject jsonObject = (JSONObject) jsonO;
                    if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                       ServiceConfigArrKey.CAPACITY)) {
                        String value = (String) jsonObject.get(ServiceConfigArrKey.JSON_VALUE);
                        String number = StringUtil.getNumberFromString(value);
                        long apply = Long.parseLong(number);

                        List<String> configTypes = new ArrayList<>();
                        DescribeSysConfigByTypeListRequest req = new DescribeSysConfigByTypeListRequest();
                        configTypes.add("sfs_config");
                        req.setConfigTypes(configTypes);
                        cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult restResult =
                                systemConfigService.getConfigsByTypeList(req);

                        String jsonString = JSON.toJSONString(restResult.getData());
                        List<DescribeSysConfigResponse> responses = JSON.parseArray(jsonString,
                                                                                    DescribeSysConfigResponse.class);
                        for (DescribeSysConfigResponse respons : responses) {
                            if ("hpc.sfs.special.storage.interval".equalsIgnoreCase(respons.getConfigKey())) {
                                String configValue = respons.getConfigValue();
                                String[] split = configValue.split("-");
                                long min = Long.parseLong(split[0]);
                                long max = Long.parseLong(split[1]);
                                if (apply >= min && apply <= max) {
                                    return;
                                } else {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1077740230));
                                }
                            }
                        }
                        break;
                    }
                }
            }

        }

    }

    /**
     * 查询该用户的产品受限
     *
     * @param userSid 用户Id
     * @param serviceIdList 服务id
     *
     * @return 最小受限金额
     */
    public String productLimit(Long userSid, List<Long> serviceIdList,ProductInfoVO productInfo) {
        Long categoryId = productInfo.getServiceId();
        BizBillingAccount account = bizBillingAccountMapper.getByCategoryId(categoryId, userSid);
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
        }
        if (Constants.FREEZE.equals(account.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ACCOUNT_FREEZE));
        }
        IsNonBillProductRequest isNonBillProductRequest = new IsNonBillProductRequest();
        isNonBillProductRequest.setSfServiceId(categoryId);
        isNonBillProductRequest.setChargeType(productInfo.getChargeType());
        Boolean isNonBillProduct = bizDistributorProductService.selectIsNonBillProduct(isNonBillProductRequest);
        if (isNonBillProduct) {
            log.info("不计费产品跳过产品限额");
            return null;
        }
        for (Long serviceId : serviceIdList) {
            // 客户限额
            QueryWrapper<BizAccountProductQuota> queryAccount = new QueryWrapper<>();
            queryAccount.eq("user_sid", userSid)
                    .in("service_id", Lists.newArrayList(0L, serviceId))
                    .eq("status", "1")
                    .eq("entity_id", account.getEntityId());
            BizAccountProductQuota accountProductQuota = bizAccountProductQuotaService.getOne(queryAccount);
            if (Objects.nonNull(accountProductQuota)) {
                if (account.getBalance().compareTo(accountProductQuota.getMinAmount()) < 0) {
                    return String.valueOf(accountProductQuota.getMinAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    return null;
                }
            }
            // 产品限额
            QueryWrapper<BizProductQuota> queryProduct = new QueryWrapper<>();
            queryProduct.eq("service_id", serviceId).eq("status", Constants.ONE);
            BizProductQuota productQuota = bizProductQuotaService.getOne(queryProduct);
            if (Objects.nonNull(productQuota)) {
                if (account.getBalance().compareTo(productQuota.getMinAmount()) < 0) {
                    return String.valueOf(productQuota.getMinAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    return null;
                }
            }
            // 平台限额
            QueryWrapper<BizProductQuotaWhiteList> queryWhiteList = new QueryWrapper<>();
            BizProductQuotaWhiteList productQuotaWhiteList = bizProductQuotaWhiteListService.getOne(
                    queryWhiteList.eq("user_sid", userSid).eq("entity_id", account.getEntityId()));
            if (Constants.TRUE.equals(
                    PropertiesUtil.getProperty(SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT_OPEN + account.getEntityId()))
                    && Objects.isNull(
                    productQuotaWhiteList)) {
                if (account.getBalance().compareTo(new BigDecimal(
                        PropertiesUtil.getProperty(SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + account.getEntityId())))
                        < 0) {
                    return PropertiesUtil.getProperty(
                            SysConfigConstants.ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + account.getEntityId());
                } else {
                    return null;
                }

            }
        }
        return null;
    }

    /**
     * 【Since v2.5.0】
     * 检查是否支持冻结策略：释放资源
     * @param bssType bssType
     * @return Boolean
     */
    @AuthorizeBss(action = AuthModule.BQ.BQ010701 + "," + AuthModule.CB.CB1901)
    @GetMapping("/checkNotifyConfig/{bssType}")
    public HashMap<String, Object> checkNotifyConfig(@PathVariable String bssType) {
        List<SysMNotifyConfig> sysMNotifyConfigEnadble = notifyConfigBssMapper.selectByBssTypeAndStatus(bssType,
                                                                                                            StatusEnum.ENABLE
                                                                                                                    .getValue());
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("status", !CollectionUtil.isEmpty(sysMNotifyConfigEnadble) && !Constants.NONE.equals(
                CollectionUtil.getFirst(sysMNotifyConfigEnadble).getExpireStrategy()));
        hashMap.put("strategyBufferPeriod", 0);
        SysMNotifyConfigExample example = new SysMNotifyConfigExample();
        example.createCriteria().andBssTypeEqualTo(bssType);
        List<SysMNotifyConfig> sysMNotifyConfigs = notifyConfigBssMapper.selectByExample(example);
        if (!CollectionUtil.isEmpty(sysMNotifyConfigs)) {
            hashMap.put("strategyBufferPeriod", CollectionUtil.getFirst(sysMNotifyConfigs).getStrategyBufferPeriod());
        }
        return hashMap;
    }

    /**
     * 验证代客订购名称不重复
     *
     * @param orgSid 组织sid
     * @param name   名称
     * @return {@code Boolean}
     */
    @AuthorizeBss(action = AuthModule.BQ.BQ010701)
    @ApiOperation(httpMethod = "GET", value = " 验证代客订购名称不重复", notes = "通过提交orgSid、name验证名称是否存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgSid", value = "orgSid", paramType = "query", dataType = "Long", required = true),
            @ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string", required = true)})
    @GetMapping("/checkDuplicateNames")
    @ResponseBody
    public Boolean checkDuplicateNames(@NotNull(message = "验证orgSid不能为空！") Long orgSid,
                                       @NotNull(message = "验证名称不能为空！") String name) {
        BizBillingAccount bizBillingAccountByOrgId = bizBillingAccountService.getBizBillingAccountByOrgId(orgSid);
        if (Objects.nonNull(bizBillingAccountByOrgId) && Objects.nonNull(bizBillingAccountByOrgId.getAdminSid())) {
            Criteria criteria = new Criteria();
            criteria.put("orgSid", bizBillingAccountByOrgId.getAdminSid());
            criteria.put("name", name);
            List<ServiceOrderVo> serviceOrderVos = serviceOrderService.checkDuplicateNames(criteria);
            return CollectionUtils.isEmpty(serviceOrderVos);
        }
        return Boolean.FALSE;
    }

    /**
     * 退订
     *
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @return {@code RestResult}
     */
    @ApiOperation("退订")
    @DeleteMapping("/unsubscribe/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName ="'退订'", bizId = "#id", resource = OperationResourceEnum.UNSUBSCRIBE, tagNameUs ="'Unsubscribe'")
    @GlobalTransactional
    @AuthorizeBss(action = AuthModule.CB.CB1908)
    @Idempotent
    @DataPermission(resource = OperationResourceEnum.UNSUBSCRIBE,bizId = "#id")
    public RestResult unsubscribe(@PathVariable String id,
                                  @RequestParam(value = "userSid", required = false) Long userSid,
                                  @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                  @RequestParam(required = false) String type,
                                  @RequestParam(required = false) String originStatus,
                                  @RequestParam(required = false) String applyType
                                  ) {
        return unsubscribe(id,null, userSid, unsubAmount, type, originStatus, applyType, false,null);
    }


    /**
     * 资源退订（增加了审批单参数）
     *
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @param actionParam 审批单
     * @return {@code RestResult}
     */
    @ApiOperation("退订")
    @DeleteMapping("/unsubscribe/res/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName ="'退订'", bizId = "#id", resource = OperationResourceEnum.UNSUBSCRIBE, tagNameUs ="'Unsubscribe'")
    @GlobalTransactional
    @AuthorizeBss(action = AuthModule.CB.CB1908)
    @Idempotent
    @DataPermission(resource = OperationResourceEnum.UNSUBSCRIBE,bizId = "#id")
    public RestResult unsubscribeRes(@PathVariable String id,
                                  @RequestParam(value = "userSid", required = false) Long userSid,
                                  @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                  @RequestParam(required = false) String type,
                                  @RequestParam(required = false) String originStatus,
                                  @RequestParam(required = false) String applyType,
                                  @RequestBody ActionParam actionParam) {
        return unsubscribe(id,null, userSid, unsubAmount, type, originStatus, applyType, false,actionParam);
    }

    /**
     * 管理员退订
     *【Since v2.5.0】
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @return {@code RestResult}
     */
    @ApiOperation("管理员退订")
    @DeleteMapping("/mgt_unsubscribe/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'管理员退订'", bizId = "#id", resource = OperationResourceEnum.UNSUBSCRIBE, tagNameUs ="'Admin Unsubscribe'")
    @GlobalTransactional
    @AuthorizeBss(action = AuthModuleOss.BD.BD06.BD06_RELEASE_RESOURCE)
    public RestResult mgtUnsubscribe(@PathVariable String id,
                                         @RequestParam(value = "userSid", required = false) Long userSid,
                                         @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                         @RequestParam(required = false) @EnumValue(strValues = {"HPC","HPC-SAAS","HPC-DRP", "SFS2.0", "SFS", "DRP", "ModelArts"}) String type,
                                         @RequestParam(required = false) @EnumValue(strValues = {"available", "soonExpire", "frozen", "expired"}) String originStatus,
                                         @RequestParam(required = false) @EnumValue(strValues = {"hpc-drp-standard", "hpc-drp-custom", "depTrain", "depOnline"}) String applyType) {
        return unsubscribe(id,null, userSid, unsubAmount, type, originStatus, applyType, false,null);
    }
    /**
     * [INNER API] 退订
     *【Since v2.5.0】
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @return {@code RestResult}
     */
    @ApiOperation("退订")
    @DeleteMapping("/unsubscribe/feign/{id}")
    @RejectCall
    public RestResult unsubscribeByFeign(@PathVariable String id,
                                         @RequestParam(value = "userSid", required = false) Long userSid,
                                         @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                         @RequestParam(required = false) String type,
                                         @RequestParam(required = false) String originStatus,
                                         @RequestParam(required = false) String applyType,
                                         @RequestParam(required = false) boolean auto) {
        return unsubscribe(id,null, userSid, unsubAmount, type, originStatus, applyType, auto,null);
    }

    /**
     * [INNER API] 退订
     *【Since v2.5.0】
     * @param id           id
     * @param accountId    账户id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @return {@code RestResult}
     * @apiNote Since v2.5.0
     */
    @ApiOperation("退订")
    @RejectCall
    public RestResult unsubscribeByFeign(@PathVariable String id,
                                         @RequestParam(value = "accountId",required = false) Long accountId,
                                         @RequestParam(value = "userSid", required = false) Long userSid,
                                         @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                         @RequestParam(required = false) String type,
                                  @RequestParam(required = false) String originStatus,
                                  @RequestParam(required = false) String applyType ) {

        return unsubscribe(id,accountId, userSid, unsubAmount, type, originStatus, applyType, false,null);
    }

    /**
     * 退订公共方法
     *
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @param auto         是否自动退订
     * @return {@code RestResult}
     */
    private RestResult unsubscribe(@PathVariable String id,
                                   @RequestParam(value = "accountId",required = false) Long accountId,
                                  @RequestParam(value = "userSid", required = false) Long userSid,
                                  @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                  @RequestParam(required = false) String type,
                                  @RequestParam(required = false) String originStatus,
                                  @RequestParam(required = false) String applyType,
                                  @RequestParam(required = false) boolean auto,
                                   ActionParam actionParam) {

        boolean expireTag = false;
        if (accountId != null) {
            String expire = JedisUtil.INSTANCE.get(accountId.toString());
            log.info("试用期账号的标识[{}]", expire);
            expireTag = ACCOUNT_EXPIRE.equals(expire);
        }
        AuthUser user = null;
        if(expireTag){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
            user = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(bizBillingAccount.getAdminSid()), AuthUser.class);
        }else{
            user = RequestContextUtil.getAuthUserInfo();
        }
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        if(user.getOrgSid() != null){
            String expire = JedisUtil.INSTANCE.get(user.getOrgSid().toString()+"_expire");
            if (StringUtils.isNotEmpty(expire)) {
                log.info("试用期账号的标识[{}]", expire);
                expireTag = ACCOUNT_EXPIRE.equals(expire);
            }
        }

        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(type);
        if (Objects.nonNull(serviceCategory)) {
            List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
            if (Objects.isNull(user.getOrgSid()) && !longs.contains(user.getUserSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
        ServiceOrder serviceOrder;
        SfProductResource sfProductResource = null;
        if (ProductCodeEnum.SFS2.getProductCode().equals(type)) {
            serviceOrderService.checkPendingOrder(id, type);
            serviceOrder = serviceOrderService.selectOrderDetailByResourceId(id, type);
        cacheTraceId(id, type);
        } else {

            sfProductResource = sfProductResourceMapper.selectById(id);
            if (sfProductResource == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
            }
            cacheTraceId(String.valueOf(sfProductResource.getClusterId()), sfProductResource.getProductType());

            serviceOrder = serviceOrderMapper.selectById(sfProductResource.getServiceOrderId());
        }
        boolean admin = false;
        if (sysUserService.checkOperationAdmin(user.getUserSid())) {
            admin = true;
            // 运营管理员退订，需要赋值租户id
            userSid = Long.parseLong(serviceOrder.getOwnerId());
        } else {
            if (!Objects.equals((user.getUserSid().toString()), serviceOrder.getOwnerId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if (serviceOrder.getBehalfUserSid() != null && !auto) {
                    Criteria cw = new Criteria();
                    cw.put("userSid",user.getUserSid());
                    cw.put("roleSid",301);
                    List<UserRole> roles = userRoleMapper.selectByParams(cw);
                    if(CollectionUtil.isEmpty(roles)){
                        throw new BizException(WebUtil.getMessage(MsgCd.NO_PERMISSION_OPERATION));
                    }
                }
            BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(serviceOrder.getEntityId(), user.getUserSid());
            if (sfProductResource != null) {
                String status = sfProductResource.getStatus();
                if(!expireTag){
                    if (!SfProductEnum.NORMAL.getStatus().equals(status) && !SfProductEnum.EXPIRED.getStatus().equals(status) &&
                            !SfProductEnum.FROZEN.getStatus().equals(status)) {
                        String nameByI18n = SfProductEnum.getNameByI18n(sfProductResource.getStatus(), WebUtil.getHeaderAcceptLanguage());
                        throw new BizException(WebUtil.getMessage(MsgCd.CURRENT_OPERATION_NOT_SUPPORTED, new Object[]{nameByI18n}));
                    }
                }
            }
        }
        if(!expireTag){
        //校验企业是否认证中
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User userAuth
                    = sysUserService.selectByPrimaryKey(admin ? userSid : user.getUserSid());
            //用户是否可操作
            Long serviceOrderOrgSid = serviceOrder.getOrgSid();
            List<Long> defaultPrejectList = orgService.selectOrgSidBySid(userAuth.getOrgSid()).stream().map(Org::getOrgSid).collect(Collectors.toList());
            if(!userAuth.getOrgSid().equals(serviceOrderOrgSid) && ! defaultPrejectList.contains(serviceOrderOrgSid)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
            }
            List<Org> orgList = orgService.selectOrgByUserSidAndType(admin ? userSid : user.getUserSid(), OrgType.COMPANY);
            if (!CertificationStatus.AUTHSUCCEED.equalsIgnoreCase(user.getCertificationStatus())) {
            if(orgList.size()>0){
                List<String> certificationStatusList = orgList.stream().map(Org::getCertificationStatus).collect(Collectors.toList());
                if(certificationStatusList.size()>0){
                        if (!CertificationStatus.AUTHSUCCEED.equals(certificationStatusList.get(0))) {
                        throw new BizException(WebUtil.getMessage(Sfs2Message.USER_AUTH));

                    }
                }
            }
        }
        if(orgList.size()>0){
            List<String> certificationStatusList = orgList.stream().map(Org::getCertificationStatus).collect(Collectors.toList());
            if(certificationStatusList.size()>0){
                    if (CertificationStatus.AUTHING.equals(certificationStatusList.get(0))) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1360067202));
                }
            }
        }
        }

        String key = "unsubscribe_lock_" + user.getUserSid() + "_" + id;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, 1, 5, TimeUnit.MINUTES);
        try {
            if (lock) {
                Long behalfUserSid = null;
                if (Objects.nonNull(userSid)) {
                    User authUser = cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil.getAuthUser();
                    behalfUserSid = authUser.getUserSid();
                    AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(userSid), AuthUser.class);
                    AuthUserHolder.setAuthUser(authUserInfo);
                    user = authUserInfo;
                }
                BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(serviceOrder.getEntityId(), user.getUserSid());
                // 已冻结账户不支持租户退订，但是运营管理员可以退订
                if(!expireTag){
                    if (Constants.FREEZE.equals(account.getStatus()) && Objects.isNull(behalfUserSid)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1341441118));
                }
                }
                RestResult result =  cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil.replaceUserToInvoke(
                        () -> serviceOrderService.unsubscribe(id, accountId,null, unsubAmount, type, originStatus, applyType, serviceOrder.getEntityId(),actionParam),user.getUserSid());
                if (Objects.nonNull(userSid)) {
                    AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(behalfUserSid), AuthUser.class);
                    AuthUserHolder.setAuthUser(authUserInfo);
                }
                return result;
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_313131619));
            }
        } finally {
            redisTemplate.delete(key);
        }
    }

    /**
     * 缓存traceId
     * @param id
     * @param type
     */
    private void cacheTraceId(String id, String type) {
        try {
            long traceId = TraceUtil.tracingInstance().currentTraceContext().get().traceId();
            String cacheKey = TraceUtil.TRACE_ID_KEY + type +StrUtil.COLON + id;
            JedisUtil.INSTANCE.set(cacheKey, String.valueOf(traceId));
        } catch (Exception e) {
            log.info("设置当前链路traceId-ServiceOrderServiceImpl.deleteNode-失败:[{}]", e.getMessage());
        }
    }

    /**
     * 【Since v2.5.0】查询变更支持类型
     *
     * @param id           id
     * @return {@code RestResult}
     */
    @ApiOperation("查询变更支持类型")
    public RestResult queryVmChargeType(@PathVariable String id) {
        return this.serviceOrderService.queryVmType(id);
        }


    /**
     * 变更配置
     *
     * @param request 变更资源配置请求体
     * @return {@code RestResult}
     */
    @PostMapping("/resize_resource")
    @ApiOperation("变更配置")
    @AuthorizeBss(action = AuthModule.CB.CB19 + "," + AuthModule.BQ.BQ010701)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'变更配置'", bizId = "#request.projectId", resource = OperationResourceEnum.RESIZE_RESOURCE, tagNameUs ="'Change configuration'")
    public RestResult changeVmType(@Valid @RequestBody ResizeResourceRequest request) {
        if (Objects.nonNull(request.getBehalfUserSid())) {
            List<ServiceCategory> serviceCategory = serviceCategoryService.getServiceCategoryByServiceType(request.getProductInfo().getResourceType());
            if (serviceCategory.stream().noneMatch(e -> Objects.equals(e.getEntityId(), RequestContextUtil.getEntityId()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2036580347));
            }
        }
        return serviceOrderService.resizeResource(request);
    }

    /**
     * [INNER API] 更改VM类型
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @PostMapping("/resize_resource/feign")
    @ApiOperation("变更配置")
    @RejectCall
    public RestResult changeVmTypeFeign(@Valid @RequestBody ResizeResourceRequest request) {
        return serviceOrderService.resizeResource(request);
    }

    /**
     * 【Since v2.5.0】查询产品支持计费方式
     *
     * @param cloudEnvType 云环境类型
     * @param productCode  产品代码
     * @return {@code RestResult}
     */
    @ApiOperation("查询产品支持计费方式")
    public RestResult<List<String>> queryChargeType(@ApiParam(value = "云环境ID", type = "String", required = true)
                                                    @RequestParam("cloudEnvType") String cloudEnvType,
                                                    @ApiParam(value = "产品代码", type = "String", required = true)
                                                    @RequestParam("productCode") String productCode) {
        Map<String, Object> query = Maps.newHashMap();
        query.put("codeValue", productCode);
        List<ProductBaseConfig> configs = serviceCategoryService.queryProductBaseConfig(query);
        configs = configs.stream().filter(baseConfig -> baseConfig.getCloudEnvType()
                .contains(cloudEnvType)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(configs)) {
            return new RestResult(configs.get(0).getChargeType());
        }
        return new RestResult();
    }

    /**
     * 【Since v2.5.0】修改节点状态
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("修改节点状态")
    @ListenExpireBack
    @Authorize(action = "hpc:hpc:UpdateNodeStatus")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "修改节点状态",resource = OperationResourceEnum.UPDATE_HPC_NODE_STATUS, tagNameUs ="'Modify node status'")
    public RestResult updateNodeStatus(@Valid @RequestBody OperateNodeRequest request) {
        serviceOrderService.updateNodeStatus(request);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 【Since v2.5.0】删除节点
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("删除节点")
    @ListenExpireBack
    @RejectCall
    public RestResult deleteNode(@Valid @RequestBody OperateNodeRequest request) {
        serviceOrderService.deleteNode(request);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }


    /**
     * 【Since v2.5.0】扩容服务详情
     *
     * @param request 请求
     *
     * @return {@link UpgradeDetailVO}
     */
    @ApiOperation("扩容服务详情")
    @RejectCall
    public UpgradeDetailVO upgradeDetail(UpgradeServiceRequest request) {
        String productType = request.getProductType();
        if (StringUtils.isEmpty(productType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        UpgradeDetailVO upgradeDetailVO = orderServiceFactory.orderService(productType).upgradeDetail(request);
        return upgradeDetailVO;
    }

    /**
     * 【Since v2.5.0】扩容服务
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("hpc扩容服务")
    @RejectCall
    public RestResult upgrade(@Valid @RequestBody UpgradeServiceRequest request) {
        RestResult FAILURE = serviceOrderService.executeUpgrade(request);
        if (FAILURE != null) {
            return FAILURE;
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }


    /**
     * 【Since v2.5.0】退订失败资源释放
     *
     * @param id ID
     * @param userSid 用户ID
     * @param type 类型
     *
     * @return {@link RestResult}
     */
    @ApiOperation("退订失败资源释放")
    @AuthorizeBss(action = AuthModuleOss.BD.BD06.BD06_RELEASE_RESOURCE)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'退订失败资源释放'", tagNameUs ="'Unsubscribe failed resource release'",
            resource = OperationResourceEnum.RELEASE,param = "#type" ,bizId = "#id")
    @GlobalTransactional
    public RestResult releaseUnsubErrorRes(@PathVariable String id,
                                          @RequestParam(value = "userSid", required = false) Long userSid,
                                          @RequestParam(value = "type") String type) {
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        if (!sysUserService.checkOperationAdmin(user.getUserSid())) {
            // 此操作只支持运营管理员执行
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        userSid = sfProductResourceService.queryOwnerId(Long.parseLong(id), type);
        String key = "unsubscribe_lock_" + userSid + "_" + id;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, 1, 1, TimeUnit.MINUTES);
        try {
            if (lock) {
                sfProductResourceService.releasUnsubErrorRes(Long.parseLong(id), userSid, type);
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_313131619));
            }
        } finally {
            redisTemplate.delete(key);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }



    /**
     * 【Since v2.5.0】
     * 获取ma所有节点
     * @return ma所有节点
     */
    private List<ModelArtsPoolAllocate> getModelArts() {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60L * 1000L * 5L;
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        org.springframework.data.mongodb.core.query.Criteria criteria1 = org.springframework.data.mongodb.core.query.Criteria.where("createDt").gt(startDate);
        org.springframework.data.mongodb.core.query.Criteria criteria2 = org.springframework.data.mongodb.core.query.Criteria.where("createDt").lt(endDate);
        org.springframework.data.mongodb.core.query.Criteria criteria3 = new org.springframework.data.mongodb.core.query.Criteria();
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria().andOperator(criteria1, criteria2, criteria3);
        Query query = new Query().addCriteria(criteria);
        List<ModelArtsPoolAllocate> modelArtsPoolAllocates = new ArrayList<>();
        MongoCursor<Document> modelArtsCursor = mongoTemplate.getCollection("modelarts_pool_allocate")
                .find(query.getQueryObject())
                .noCursorTimeout(true)
                .cursor();
        while (modelArtsCursor.hasNext()) {
            ModelArtsPoolAllocate modelArtsPoolAllocate = cn.hutool.core.bean.BeanUtil.toBean(modelArtsCursor.next(),
                    ModelArtsPoolAllocate.class);
            modelArtsPoolAllocates.add(modelArtsPoolAllocate);
        }
        modelArtsCursor.close();
        return modelArtsPoolAllocates;
    }

    private static ProductInfoVO packegeDisk(boolean sys, Object disk, Map<Long, String> specMap, ProductInfoVO template) {
        ProductInfoVO addProductInfo =  BeanConvertUtil.convert(template, ProductInfoVO.class);
        addProductInfo.setProductCode(cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum.DISK.getProductType());
        Map<String, Object> m = BeanUtil.beanToMap(disk);
        Map<String, Object> productInfo = new HashMap<>();
        Object volumeType = m.get("volumeType");
        if (Objects.isNull(volumeType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String name = specMap.get(Long.valueOf(volumeType.toString()));
        if (StringUtils.isBlank(name)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        productInfo.put("chargeItemCategory", "blockStorage");
        productInfo.put("category",  name);
        productInfo.put("spec", name);
        productInfo.put("size", m.get("volumeSize"));
        productInfo.put("productCode", cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum.DISK.getProductType());
        Map<String, Object> data = new HashMap<>();
        data.put(cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum.DISK.getProductType(), productInfo);
        addProductInfo.setData(data);
        ProductConfigDesc productConfigDesc = addProductInfo.getProductConfigDesc();
        JSONObject configDesc  = JSONObject.parseObject(productConfigDesc.getCurrentConfigDesc());
        JSONArray currentDescArray = configDesc.getJSONArray("productConfigDesc");
        List<JSONObject> configs = new ArrayList<>();
        List<String> list = Arrays.asList("计费类型", "可用区");
        for (Object o : currentDescArray) {
            JSONObject jsonObject = (JSONObject)o;
            if (list.contains(jsonObject.getString("label"))) {
                configs.add(jsonObject);
            }
        }
        JSONObject diskObj = new JSONObject();
        diskObj.put("label", sys ? "系统盘" : "数据盘");
        diskObj.put("value", String.format("（%s）%sGB | 随实例释放", name, m.get("volumeSize")));
        configs.add(diskObj);
        configDesc.put("productConfigDesc", configs);
        productConfigDesc.setCurrentConfigDesc(JSON.toJSONString(configDesc));

        if (sys) {
            addProductInfo.setStoragePurpose("sysDisk");
        }else {
            addProductInfo.setStoragePurpose("dataDisk");
        }
        return addProductInfo;
    }
}
