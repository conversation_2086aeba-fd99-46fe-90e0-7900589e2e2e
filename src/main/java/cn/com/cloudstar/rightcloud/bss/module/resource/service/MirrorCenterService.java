/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.MirrorCenterRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2023/4/23.
 */
@FeignClient(value = "https://cmp-oss:8080", configuration = FeignConfig.class, path = "/api/v1/oss")
public interface MirrorCenterService {


    /**
     * 镜像中心
     */
    @GetMapping("/mirrorCenter/listImagesFeign")
    RestResult listImages(@SpringQueryMap MirrorCenterRequest request);


    /**
     * 镜像中心
     */
    @GetMapping("/mirrorCenter/createTempLoginCodeFeign")
    RestResult createTempLoginCode();

    /**
     * 镜像中心
     */
    @GetMapping("/mirrorCenter/getCloudEnvFeign")
    RestResult getCloudEnv();


    /**
     * 组织跳转
     */
    @GetMapping("/mirrorCenter/getOrgUrl")
    RestResult getOrgUrl();


    /**
     * 获取组织
     */
    @GetMapping("/mirrorCenter/getOrgName")
    RestResult getOrgName();


}
