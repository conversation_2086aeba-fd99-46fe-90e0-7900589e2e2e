package cn.com.cloudstar.rightcloud.bss.module.market.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName market_shop_supervise_record
 */
@TableName(value ="market_shop_supervise_record")
@Data
public class MarketShopSuperviseRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 订阅id
     */
    private String subscribeId;

    /**
     * 原始状态
     */
    private String originalStatus;

    /**
     * 更新状态
     */
    private String updateStatus;

    /**
     * 创建人id
     */
    private Long createdUserId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MarketShopSuperviseRecord other = (MarketShopSuperviseRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSubscribeId() == null ? other.getSubscribeId() == null : this.getSubscribeId().equals(other.getSubscribeId()))
            && (this.getOriginalStatus() == null ? other.getOriginalStatus() == null : this.getOriginalStatus().equals(other.getOriginalStatus()))
            && (this.getUpdateStatus() == null ? other.getUpdateStatus() == null : this.getUpdateStatus().equals(other.getUpdateStatus()))
            && (this.getCreatedUserId() == null ? other.getCreatedUserId() == null : this.getCreatedUserId().equals(other.getCreatedUserId()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSubscribeId() == null) ? 0 : getSubscribeId().hashCode());
        result = prime * result + ((getOriginalStatus() == null) ? 0 : getOriginalStatus().hashCode());
        result = prime * result + ((getUpdateStatus() == null) ? 0 : getUpdateStatus().hashCode());
        result = prime * result + ((getCreatedUserId() == null) ? 0 : getCreatedUserId().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(subscribeId);
        sb.append(", originalStatus=").append(originalStatus);
        sb.append(", updateStatus=").append(updateStatus);
        sb.append(", createdUserId=").append(createdUserId);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}