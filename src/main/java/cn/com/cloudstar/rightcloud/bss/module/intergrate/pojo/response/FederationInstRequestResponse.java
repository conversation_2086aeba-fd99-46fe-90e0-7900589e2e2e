package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-10-09 16:07
 * @Desc
 */
@Data
public class FederationInstRequestResponse {
    private Long id;

    /**
     * 资源实例ID
     */
    private String resourceId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 联邦跳转地址
     */
    private String federationUrl;

    /**
     * 计费类型，PrePaid:包年包月, PostPaid:按量计费
     */
    private String chargeType;

    /**
     * 状态：apply申请中，renewing续订中，available正常，expired已过期，freezed已冻结，
     * rejected已拒绝,unsubscribing退订中,unsubscribed 已退订
     */
    private String status;
    private String secStatus;

    /**
     * 计费开始时间
     */
    private Date startTime;

    /**
     * 计费结束时间
     */
    private Date endTime;

    /**
     * 冻结时间
     */
    private Date freezedTime;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 创建人id
     */
    private Long ownerSid;

    private Date createdDt;
    private String createdBy;
    private Date updatedDt;
    private String updatedBy;
    private Long version;
}
