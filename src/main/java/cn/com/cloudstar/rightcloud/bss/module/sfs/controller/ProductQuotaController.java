package cn.com.cloudstar.rightcloud.bss.module.sfs.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.SysMsgEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.message.service.ISysMsgService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeAccountProductQuotaResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductQuotaResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductQuotaWhiteListResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductsResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizAccountProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaWhiteListService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.MessageMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SysTMsg;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD07;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD07.BD0702;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD07.BD0703;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.ProductQuotaConfig;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceRunnable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 产品限额
 *
 * @Auther: 张淇囿
 * @Date: 2022/04/25
 */
@Api(tags = "产品限额")
@RestController
@RequestMapping("/products/quota")
@Slf4j
public class ProductQuotaController {

    /**
     * "productsQuotaAccount:"
     */
    private static final String PRODUCTS_QUOTA_ACCOUNT_KEY = "productsQuotaAccount:";

    /**
     * "productsQuota:"
     */
    private static final String PRODUCTS_QUOTA_KEY = "productsQuota:";

    /**
     * "productsQuotaWhiteList:"
     */
    private static final String PRODUCTS_QUOTA_WHITE_LIST_KEY = "productsQuotaWhiteList:";

    /**
     * 状态：禁用 产品：全部
     */
    private static final String ZERO = "0";

    /**
     * 状态：启用
     */
    private static final String ONE = "1";
    private static final String TWO = "2";

    /**
     * 产品全部类型
     */
    private static final String ALL = "ALL";

    /**
     * 全部
     */
    private static final String EVERYTHING = "全部";

    /**
     * 产品限额异步锁
     */
    private static final String FREEZE_PRODUCT_KEY = "lock:freeze_product:1";
    private static final String CONTENT = "冻结/解冻";

    @Autowired
    private BizAccountProductQuotaService bizAccountProductQuotaService;

    @Autowired
    private BizProductQuotaService bizProductQuotaService;

    @Autowired
    private BizProductQuotaWhiteListService bizProductQuotaWhiteListService;

    @Autowired
    private IBizBillingAccountService iBizBillingAccountService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    MessageMapper messageMapper;

    @Autowired
    EntityUserMapper entityUserMapper;

    @Autowired
    private ThreadPoolTaskExecutor cloudExecutor;

    @Autowired
    private ISysMsgService sysMsgService;
    @Autowired
    private Tracer tracer;
    @Autowired
    private SpanNamer spanNamer;


    /**
     * 获取平台限额白名单列表
     * @param request
     * @return IPage<DescribeProductQuotaWhiteListResponse>
     */
    @AuthorizeBss(action = BD07.BD070104)
    @ApiOperation(httpMethod = "GET", value = "获取平台限额白名单列表")
    @GetMapping("/whiteList")
    @ListenExpireBack
    public IPage<DescribeProductQuotaWhiteListResponse> findBizAccountProductQuotasWhiteList(
            @Valid DescribeProductQuotaWhiteListRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("entityId", getEntityId());
        IPage<BizProductQuotaWhiteList> page = PageUtil.preparePageParams(request, "created_dt", "desc");
        IPage<BizProductQuotaWhiteList> result = bizProductQuotaWhiteListService.selectByParams(page, criteria);
        return BeanConvertUtil.convertPage(result, DescribeProductQuotaWhiteListResponse.class);
    }

    /**
     * 添加平台限额白名单
     * @param idsRequest
     * @return RestResult
     */
    @AuthorizeBss(action = BD07.BD070102)
    @ApiOperation(httpMethod = "POST", value = "添加平台限额白名单")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'产品限额白名单用户'", resource = OperationResourceEnum.PRODUCTS_QUOTA_WHITELIST_ADD, tagNameUs ="'Product quota whitelist users'")
    @PostMapping("/whiteList")
    @ListenExpireBack
    public RestResult addBizAccountProductQuotasWhiteList(@RequestBody @Valid IdsRequest idsRequest) {
        List<Long> userIdList = idsRequest.getIds();
        if (CollectionUtils.isEmpty(userIdList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00023));
        }
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        // 删除数据，以传参数据为准
        QueryWrapper<BizProductQuotaWhiteList> removeWrapper = new QueryWrapper<>();
        removeWrapper.eq("entity_id", getEntityId());
        List<BizProductQuotaWhiteList> oldList = bizProductQuotaWhiteListService.list(removeWrapper);
        bizProductQuotaWhiteListService.remove(removeWrapper);

        List<BizProductQuotaWhiteList> bizProductQuotaWhiteLists = Lists.newArrayList();
        List<Long> accountIds = Lists.newArrayList();
        for (Long sid : userIdList) {
            // 判断sid是否有效
            BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(getEntityId(), sid);
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.CUSTOMER_DOES_NOT_EXIST));
            }

            Optional<BizProductQuotaWhiteList> optional = oldList.stream().filter(e -> e.getUserSid() - sid == 0).findFirst();
            if (optional.isPresent()) {
                bizProductQuotaWhiteLists.add(optional.get());
                continue;
            }

            BizProductQuotaWhiteList bizProductQuotaWhiteList = new BizProductQuotaWhiteList();
            bizProductQuotaWhiteList.setUserSid(sid);
            bizProductQuotaWhiteList.setOrgSid(account.getOrgSid());
            bizProductQuotaWhiteList.setEntityId(getEntityId());
            cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil.prepareInsertParamsWithNoVersion(bizProductQuotaWhiteList);
            bizProductQuotaWhiteLists.add(bizProductQuotaWhiteList);
            accountIds.add(account.getId());
        }
        bizProductQuotaWhiteListService.saveBatch(bizProductQuotaWhiteLists);
        List<String> strings = accountIds.stream().map(x -> x + "").collect(Collectors.toList());
        JedisUtil.INSTANCE.del(getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY);
        JedisUtil.INSTANCE.addList(getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY, strings);
        // 解冻/冻结账户
        for (Long data : accountIds) {
            CompletableFuture.runAsync(new TraceRunnable(tracer,spanNamer,()-> {
                iBizBillingAccountService.doUnfreezeUser(data);
            }),cloudExecutor);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
    }


    /**
     * 删除产品限额白名单
     * @param idsRequest
     * @return RestResult
     */
    @AuthorizeBss(action = BD07.BD070103)
    @ApiOperation(httpMethod = "DELETE", value = "删除产品限额白名单")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'产品限额白名单用户'", resource = OperationResourceEnum.PRODUCTS_QUOTA_WHITELIST_DELETE, bizId = "#idsRequest.ids", tagNameUs ="'Product quota whitelist users'")
    @DeleteMapping("/whiteList")
    @ListenExpireBack
    public RestResult deleteBizAccountProductQuotasWhiteList(@RequestBody @Valid IdsRequest idsRequest) {
        List<Long> ids = idsRequest.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WHITELIST_LIMIT_ID_CANNOT_BE_EMPTY));
        }
        Long entityId = getEntityId();
        // 校验ids是否重复
        if (ids.stream().distinct().count() != ids.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DUPLICATE_SELECTED_ID));
        }
        List<String> accountIds = Lists.newArrayList();
        for (Long sid : ids) {
            // 判断sid是否已存在
            BizProductQuotaWhiteList bizProductQuotaWhiteList = bizProductQuotaWhiteListService.getById(sid);
            if (Objects.isNull(bizProductQuotaWhiteList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
            }
            if(!entityId.equals(bizProductQuotaWhiteList.getEntityId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
            BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(getEntityId(),
                                                                                       bizProductQuotaWhiteList.getUserSid());
            accountIds.add(String.valueOf(account.getId()));
        }
        bizProductQuotaWhiteListService.removeByIds(ids);
        // redis缓存
        List<String> list = JedisUtil.INSTANCE.getList(getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY);
        JedisUtil.INSTANCE.del(getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY);
        accountIds.forEach(list::remove);
        JedisUtil.INSTANCE.addList(getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY, list);
        // 解冻/冻结账户
        for (String data : accountIds) {
            CompletableFuture.runAsync(new TraceRunnable(tracer,spanNamer,()-> {
                iBizBillingAccountService.doUnfreezeUser(Long.valueOf(data));
            }),cloudExecutor);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 获取产品限额列表
     * @param request
     * @return IPage<DescribeProductQuotaResponse>
     */
    @AuthorizeBss(action = BD0702.BD070205)
    @ApiOperation(httpMethod = "GET", value = "获取产品限额列表")
    @GetMapping("")
    @ListenExpireBack
    public IPage<DescribeProductQuotaResponse> findProductQuotas(@Valid DescribeProductQuotaRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("entityId", getEntityId());
        IPage<BizProductQuota> page = PageUtil.preparePageParams(request, "created_dt", "desc");
        IPage<BizProductQuota> result = bizProductQuotaService.selectByParams(page, criteria);
        result.getRecords().forEach(t -> {
            if (ALL.equals(t.getServiceType())) {
                t.setProductName(EVERYTHING);
            }
            if (ProductEnum.HPC.getProductType().equals(t.getServiceType())) {
                SfServiceCategoryHpcClusterPool hpcClusterPool = serviceCategoryMapper.selectServiceCategoryHPCByClusterId(
                        t.getClusterId());
                t.setProductName(hpcClusterPool.getClusterName());
            }
            if (ALL.equals(t.getServiceType())) {
                t.setProductName(EVERYTHING);
            }
        });
        return BeanConvertUtil.convertPage(result, DescribeProductQuotaResponse.class);
    }


    /**
     * 增加产品限额
     * @param request
     * @return RestResult
     */
    @AuthorizeBss(action = BD0702.BD070202)
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'产品限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_ADD, tagNameUs ="'Product limit'")
    @ApiOperation(httpMethod = "POST", value = "增加产品限额")
    @PostMapping("")
    @ListenExpireBack
    public RestResult addProductQuota(@RequestBody @Validated CreateBizProductQuotaRequest request) {
        // 最小购买金额大于最小冻结金额
        Long entityId = getEntityId();
        if (Objects.nonNull(request.getMinFrozenAmount()) && !(
                request.getMinAmount().compareTo(request.getMinFrozenAmount()) > 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.MINIMUM_PURCHASE_FROZEN));
        }
        // 校验serviceId是否有效
        ServiceCategory serviceCategory = serviceCategoryMapper.selectById(request.getServiceId());
        if (Objects.isNull(serviceCategory) || !getEntityId().equals(serviceCategory.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_SELECTED_PRODUCT_DOES_NOT_EXIST));
        }
        // 校验最小冻结金额是否可设置
        if (Objects.nonNull(request.getMinFrozenAmount()) && !bizProductQuotaService.isMinFrozenAmount(
                request.getServiceId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00035));
        }
        List<DescribeProductsResponse> products = findProducts(null);
        if (products.stream().noneMatch(e -> Objects.equals(e.getId(), request.getServiceId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if(!entityId.equals(serviceCategory.getEntityId())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        // 校验serviceId是否存在
        QueryWrapper<BizProductQuota> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_id", request.getServiceId());
        BizProductQuota one = bizProductQuotaService.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_PRODUCT_QUOTA_CONFIGURATION_ALREADY_EXISTS));
        }
        BizProductQuota convert = BeanConvertUtil.convert(request, BizProductQuota.class);
        cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil.prepareInsertParamsWithNoVersion(convert);
        if (ObjectUtils.isEmpty(convert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        convert.setEntityId(getEntityId());
        convert.setServiceType(serviceCategory.getShowType());
        convert.setStatus(ZERO);
        bizProductQuotaService.save(convert);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
    }

    /**
     * 删除产品限额
     * @param idsRequest
     * @return RestResult
     */
    @AuthorizeBss(action = BD0702.BD070203)
    @ApiOperation(httpMethod = "DELETE", value = "删除产品限额")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'产品限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_DELETE, bizId = "#idsRequest.ids", tagNameUs ="'Product limit'")
    @DeleteMapping("")
    @ListenExpireBack
    public RestResult deleteProductQuotas(@RequestBody @Valid IdsRequest idsRequest) {
        List<Long> ids = idsRequest.getIds();
        Long entityId = getEntityId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY));
        }
        // 校验ids是否重复
        if (ids.stream().distinct().count() != ids.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DUPLICATE_SELECTED_ID));
        }
        for (Long sid : ids) {
            // 判断sid是否存在
            BizProductQuota bizProductQuota = bizProductQuotaService.getById(sid);
            if (Objects.isNull(bizProductQuota)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
            }
            if(!entityId.equals(bizProductQuota.getEntityId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }


        }
        bizProductQuotaService.removeByIds(ids);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }


    /**
     * 编辑产品限额
     * @param request
     * @return RestResult
     */
    @AuthorizeBss(action = BD0702.BD070201)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'产品限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_EDIT, bizId = "#request.id", tagNameUs ="'Product limit'")
    @ApiOperation(httpMethod = "PUT", value = "编辑产品限额")
    @PutMapping("")
    @ListenExpireBack
    public RestResult editProductQuotas(@RequestBody @Validated UpdateBizProductQuotaRequest request) {
        // 最小购买金额大于最小冻结金额
        Long entityId = getEntityId();
        if (Objects.nonNull(request.getMinFrozenAmount()) && !(
                request.getMinAmount().compareTo(request.getMinFrozenAmount()) > 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.MINIMUM_PURCHASE_FROZEN));
        }
        // 校验serviceId是否有效
        ServiceCategory serviceCategory = queryProduct(request.getServiceId());
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_SELECTED_PRODUCT_DOES_NOT_EXIST));
        }
        if(!entityId.equals(serviceCategory.getEntityId())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        // 校验serviceId是否存在
        QueryWrapper<BizProductQuota> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_id", request.getServiceId());
        BizProductQuota one = bizProductQuotaService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        }

        // 校验 BizProductQuota 是否存在
        one = bizProductQuotaService.getById(request.getId());
        if (Objects.isNull(one)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        }

        BizProductQuota convert = BeanConvertUtil.convert(request, BizProductQuota.class);
        WebUserUtil.prepareInsertParams(convert);
        if (ObjectUtils.isEmpty(convert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        convert.setServiceType(serviceCategory.getShowType());
        convert.setStatus(ZERO);
        bizProductQuotaService.updateById(convert);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 启用/禁用产品限额
     * @param operateProductQuotasList
     * @return RestResult
     */
    @AuthorizeBss(action = BD0702.BD070204)
    @ApiOperation(httpMethod = "PUT", value = "启用/禁用产品限额")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'产品限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_OPERATE, tagNameUs ="'Product limit'")
    @PutMapping("/operate")
    @ListenExpireBack
    @Transactional(rollbackFor = Exception.class)
    public RestResult operateProductQuotas(
            @RequestBody @Validated List<UpdateOperateProductQuotaRequest> operateProductQuotasList) {
        Long entityId = getEntityId();
        if (CollectionUtils.isEmpty(operateProductQuotasList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY));
        }
        List<String> statusList = Stream.of(Constants.ZERO, Constants.ONE).collect(Collectors.toList());
        for (UpdateOperateProductQuotaRequest data : operateProductQuotasList) {
            if (!statusList.contains(data.getStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            // 判断data是否存在
            BizProductQuota bizProductQuota = bizProductQuotaService.getById(data.getId());
            if (Objects.isNull(bizProductQuota)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
            if(!entityId.equals(bizProductQuota.getEntityId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }
        for (UpdateOperateProductQuotaRequest data : operateProductQuotasList) {
            BizProductQuota convert = BeanConvertUtil.convert(data, BizProductQuota.class);
            WebUserUtil.prepareInsertParams(convert);
            bizProductQuotaService.updateById(convert);
            // 存入缓存
            BizProductQuota bizProductQuota = bizProductQuotaService.getById(data.getId());
            if (Objects.nonNull(bizProductQuota.getMinFrozenAmount())) {
                String serviceId = String.valueOf(bizProductQuota.getServiceId());
                BigDecimal minFrozenAmount = bizProductQuota.getMinFrozenAmount();
                String frozenAmountStatus = bizProductQuota.getFrozenAmountStatus();
                if (ONE.equals(data.getStatus())) {
                    redisProductQuota(serviceId, minFrozenAmount, frozenAmountStatus);
                } else {
                    JedisUtil.INSTANCE.del(PRODUCTS_QUOTA_KEY + serviceId);
                }
                Criteria criteria = new Criteria();
                criteria.put("entityId", getEntityId());
                criteria.put("minFrozenAmount", bizProductQuota.getMinFrozenAmount());
                criteria.put("notStatus", Collections.singletonList(TWO));
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByList(criteria.getCondition());

                //平台限额 > 三金之和 > 产品限额
                //1、没有产品限额，冻结状态(平台限额)         启动产品限额，没有解冻
                //1、有平台限额、产品限额，正常状态(产品限额)  禁用产品限额，没有冻结
                if ("true".equals(PropertiesUtil.getProperty(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT_OPEN + entityId))) {
                    BigDecimal platformMinFrozenAmount = new BigDecimal(
                            PropertiesUtil.getProperty(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + entityId));
                    if (platformMinFrozenAmount.compareTo(minFrozenAmount) > 0) {
                        Criteria criteria1 = new Criteria();
                        criteria1.put("entityId", getEntityId());
                        criteria1.put("minFrozenAmount", platformMinFrozenAmount);
                        criteria1.put("notStatus", Collections.singletonList(TWO));
                        criteria1.put("accountStatus", ONE.equals(data.getStatus()) ? Constants.FREEZE : Constants.NORMAL);
                        List<BizBillingAccount> platform = bizBillingAccountMapper.selectByList(criteria1.getCondition());
                        if (CollectionUtils.isNotEmpty(platform)) {
                            List<Long> accountIds = accounts.stream().map(e -> e.getId()).collect(Collectors.toList());
                            platform.forEach(e -> {
                                if (!accountIds.contains(e.getId())) {
                                    accounts.add(e);
                                }
                            });
                        }
                    }
                }

                log.info("冻结账户异步处理");
                RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(),true);
                boolean lock = JedisUtil.INSTANCE.setnx(FREEZE_PRODUCT_KEY);
                if (lock) {
                    JedisUtil.INSTANCE.expire(FREEZE_PRODUCT_KEY, 60);
                    //异步调用冻结操作
                    try {
                        new Thread(() -> synFreezeProduct(accounts, entityId)).start();
                    } catch (Exception e) {
                        //抛异常释放冻结锁
                        redisTemplate.delete(FREEZE_PRODUCT_KEY);
                    }
                } else {
                    throw new BizException(WebUtil.getMessage(
                            MsgCd.ERROR_ENTITY_00017));
                }
            }
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 产品冻结逻辑
     */
    private void synFreezeProduct(List<BizBillingAccount> accounts, Long entityId) {
        // 异常租户
        List<Long> errorUserSid = new ArrayList<>();
        try {
            for (BizBillingAccount account : accounts) {
                //账户拥有不计费产品，跳过冻结
                if (Constants.NORMAL.equalsIgnoreCase(account.getStatus())
                        && bizProductQuotaService.isHaveNonBillAccount(
                        account)) {
                    continue;
                }
                try {
                    iBizBillingAccountService.doUnfreezeUser(account.getId());
                } catch (Exception e) {
                    errorUserSid.add(account.getUserSid());

                }
            }

            // 站内信通知对应管理员
            errorUserStation(errorUserSid, entityId);

            log.info("产品限额冻结逻辑处理，删除FREEZE_ACCOUNT_KEY锁");
            redisTemplate.delete(FREEZE_PRODUCT_KEY);
        } catch (Exception e) {
            log.info("产品限额异常:{}", e.getMessage());
            log.info("产品冻结异常逻辑处理，删除FREEZE_PRODUCT_KEY锁");
            redisTemplate.delete(FREEZE_PRODUCT_KEY);
        }
    }

    /**
     * 异常租户站内信通知
     */
    private void errorUserStation(List<Long> errorUserSid, Long entityId) {
        if (CollectionUtil.isNotEmpty(errorUserSid)) {
            List<String> strings = userMapper.selectAccountByUserSidList(errorUserSid);
            String userAccount = String.join("，", strings);
            List<Long> userSidByEntityId = entityUserMapper.selectUserSidByEntityId(entityId);
            // 现在时间
            Date nowDate = new Date();
            for (Long userSid : userSidByEntityId) {
                User user = userMapper.selectByPrimaryKey(userSid);
                QueryWrapper<SysTMsg> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("to_user_sid", userSid);
                queryWrapper.like("msg_content", userAccount);
                queryWrapper.like("msg_title", "账户异常");
                queryWrapper.orderByDesc("SEND_DATE");
                List<SysTMsg> messages = messageMapper.selectList(queryWrapper);
                // 5、10、30、60分钟间隔发送
                boolean isSend = false;
                if (CollectionUtil.isEmpty(messages)) {
                    isSend = true;
                } else {
                    Date sendTime = messages.get(0).getSendDate();
                    if (messages.size() == Constants.ONE_INT) {
                        if (DateUtil.offsetMinute(sendTime, 5).isBefore(nowDate)) {
                            isSend = true;
                        }
                    } else if (messages.size() == Constants.TWO_INT) {
                        if (DateUtil.offsetMinute(sendTime, 10).isBefore(nowDate)) {
                            isSend = true;
                        }
                    } else if (messages.size() == Constants.THREE_INT) {
                        if (DateUtil.offsetMinute(sendTime, 30).isBefore(nowDate)) {
                            isSend = true;
                        }
                    } else {
                        if (DateUtil.offsetMinute(sendTime, 60).isBefore(nowDate)) {
                            isSend = true;
                        }
                    }
                }
                if (isSend) {
                    // 站内信
                    String content = String.format(SysMsgEnum.PRODUCT_FROZEN.getContent(),
                                                   CONTENT, userAccount);
                    String title = String.format(SysMsgEnum.MIN_FROZEN.getTitle(), CONTENT);
                    cloudExecutor.execute(
                            () -> sysMsgService.createSysMsg(
                                    BeanConvertUtil.convert(user, AuthUser.class), title, content));
                }
            }
        }
    }

    /**
     * 获取客户产品限额列表
     * @param request
     * @return IPage<DescribeAccountProductQuotaResponse>
     */
    @AuthorizeBss(action = COMMON.GET_QUOTA_ACCOUNT)
    @ApiOperation(httpMethod = "GET", value = "获取客户产品限额列表")
    @GetMapping("/account")
    @ListenExpireBack
    public IPage<DescribeAccountProductQuotaResponse> findBizAccountProductQuotas(
            @Valid DescribeAccountProductQuotaRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("entityId", getEntityId());
        if (Objects.nonNull(request.getProductNameLike()) && EVERYTHING.contains(request.getProductNameLike())) {
            criteria.put("all", ALL);
        }
        IPage<BizAccountProductQuota> page = PageUtil.preparePageParams(request, "created_dt", "desc");
        IPage<BizAccountProductQuota> result = bizAccountProductQuotaService.selectByParams(page, criteria);
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            result.getRecords().forEach(t -> {
                if (ALL.equals(t.getServiceType())) {
                    t.setProductName(EVERYTHING);
                }
                if (ProductEnum.HPC.getProductType().equals(t.getServiceType())) {
                    SfServiceCategoryHpcClusterPool hpcClusterPool = serviceCategoryMapper.selectServiceCategoryHPCByClusterId(
                            t.getClusterId());
                    t.setProductName(hpcClusterPool.getClusterName());
                }
            });
        }
        return BeanConvertUtil.convertPage(result, DescribeAccountProductQuotaResponse.class);
    }

    /**
     * 增加客户产品限额
     * @param request
     * @return RestResult
     */
    @AuthorizeBss(action = BD0703.BD070302)
    @ApiOperation(httpMethod = "POST", value = "增加客户产品限额")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'客户限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_ACCOUNT_ADD, tagNameUs ="'Customer limit'")
    @PostMapping("/account")
    @ListenExpireBack
    public RestResult addBizAccountProductQuota(@RequestBody @Validated CreateBizAccountProductQuotaRequest request) {
        // 最小购买金额大于最小冻结金额
        if (Objects.nonNull(request.getMinFrozenAmount()) && !(
                request.getMinAmount().compareTo(request.getMinFrozenAmount()) > 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.MINIMUM_PURCHASE_FROZEN));
        }
        // 校验userSid是否重复
        if (request.getUserSidList().stream().distinct().count() != request.getUserSidList().size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.SELECTED_CUSTOMER_DUPLICATES));
        }
        // 校验userSid是否有效
        for (Long userSid : request.getUserSidList()) {
            BizBillingAccount bizBillingAccountByAdminSid = iBizBillingAccountService.getByEntityIdAndUserId(
                    getEntityId(),
                    userSid);
            if (Objects.isNull(bizBillingAccountByAdminSid)) {
                throw new BizException(WebUtil.getMessage(MsgCd.CUSTOMER_DOES_NOT_EXIST));
            }
        }
        // 校验serviceId是否有效
        ServiceCategory serviceCategory = queryProduct(request.getServiceId());
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_SELECTED_PRODUCT_DOES_NOT_EXIST));
        }
        if(serviceCategory.getEntityId() != null && !serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_35360333));
        }
        // 校验最小冻结金额是否可设置
        if (Objects.nonNull(request.getMinFrozenAmount()) && !bizProductQuotaService.isMinFrozenAmount(
                request.getServiceId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00035));
        }
        // 校验该用户是否有该serviceId
        for (Long userSid : request.getUserSidList()) {
            QueryWrapper<BizAccountProductQuota> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_sid", userSid);
            queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
            List<BizAccountProductQuota> list = bizAccountProductQuotaService.list(queryWrapper);
            if (Objects.nonNull(list)) {
                List<Long> longList = list.stream()
                                          .map(BizAccountProductQuota::getServiceId)
                                          .collect(Collectors.toList());
                if (longList.contains(request.getServiceId())) {
                    return new RestResult(Status.FAILURE,
                                          WebUtil.getMessage(
                                                  MsgCd.THIS_CUSTOMER_PRODUCT_LIMIT_CONFIGURATION_ALREADY_EXISTS));
                }
                if (longList.contains(0L)) {
                    return new RestResult(Status.FAILURE,
                                          WebUtil.getMessage(
                                                  MsgCd.THE_CUSTOMER_PRODUCT_LIMIT_HSA_BEEN_CONFIGURED_FOR_ALL_PRODUCTS));
                }
            }
        }
        BizAccountProductQuota convert = BeanConvertUtil.convert(request, BizAccountProductQuota.class);
        cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil.prepareInsertParamsWithNoVersion(convert);
        if (ObjectUtils.isEmpty(convert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        convert.setEntityId(getEntityId());
        convert.setServiceType(serviceCategory.getShowType());
        convert.setStatus(ZERO);
        for (Long userSid : request.getUserSidList()) {
            User user = userMapper.selectByPrimaryKey(userSid);
            convert.setOrgSid(user.getOrgSid());
            convert.setUserSid(userSid);
            bizAccountProductQuotaService.save(convert);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
    }

    /**
     * 删除客户产品限额
     * @param idsRequest
     * @return RestResult
     */
    @AuthorizeBss(action = BD0703.BD070303)
    @ApiOperation(httpMethod = "DELETE", value = "删除产品限额")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'客户限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_ACCOUNT_DELETE, bizId = "#idsRequest.ids", tagNameUs ="'Customer limit'")
    @DeleteMapping("/account")
    @ListenExpireBack
    public RestResult deleteBizAccountProductQuota(@RequestBody @Valid IdsRequest idsRequest) {
        List<Long> ids = idsRequest.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(WebUtil.getMessage(MsgCd.CUSTOMER_PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY));
        }
        // 校验ids是否重复
        if (ids.stream().distinct().count() != ids.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DUPLICATE_SELECTED_ID));
        }
        for (Long sid : ids) {
            // 判断sid是否存在
            BizAccountProductQuota bizAccountProductQuota = bizAccountProductQuotaService.getById(sid);
            if (Objects.isNull(bizAccountProductQuota)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
            }
            if(bizAccountProductQuota.getEntityId() != null
                    && !bizAccountProductQuota.getEntityId().equals(RequestContextUtil.getEntityId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_465687352));
            }
        }
        bizAccountProductQuotaService.removeByIds(ids);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 编辑客户产品限额
     * @param request
     * @return RestResult
     */
    @AuthorizeBss(action = BD0703.BD070301)
    @ApiOperation(httpMethod = "PUT", value = "编辑客户产品限额")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_ACCOUNT_EDIT, bizId = "#request.id", tagNameUs ="'Customer limit'")
    @PutMapping("/account")
    public RestResult editBizAccountProductQuota(@RequestBody @Validated UpdateBizAccountProductQuotaRequest request) {
        // 最小购买金额大于最小冻结金额
        if (Objects.nonNull(request.getMinFrozenAmount()) && !(
                request.getMinAmount().compareTo(request.getMinFrozenAmount()) > 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.MINIMUM_PURCHASE_FROZEN));
        }
        // 校验serviceId是否有效
        ServiceCategory serviceCategory = queryProduct(request.getServiceId());
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_SELECTED_PRODUCT_DOES_NOT_EXIST));
        }
        if(serviceCategory.getEntityId() != null && !serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_35360333));
        }

        // 校验serviceId、userSid是否存在
        QueryWrapper<BizAccountProductQuota> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_id", request.getServiceId());
        queryWrapper.eq("user_sid", request.getUserSid());
        BizAccountProductQuota one = bizAccountProductQuotaService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        }

        one = bizAccountProductQuotaService.getById(request.getId());
        if (Objects.isNull(one)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        }

        BizAccountProductQuota convert = BeanConvertUtil.convert(request, BizAccountProductQuota.class);
        WebUserUtil.prepareInsertParams(convert);
        if (ObjectUtils.isEmpty(convert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        convert.setServiceType(serviceCategory.getShowType());
        convert.setStatus(ZERO);
        bizAccountProductQuotaService.updateById(convert);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 启用/禁用客户限额
     * @param operateProductQuotasList
     * @return RestResult
     */
    @AuthorizeBss(action = BD0703.BD070304)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户限额'", resource = OperationResourceEnum.PRODUCTS_QUOTA_ACCOUNT_OPERATE, tagNameUs ="'Customer limit'")
    @ApiOperation(httpMethod = "PUT", value = "启用/禁用客户限额")
    @PutMapping("/account/operate")
    public RestResult operateBizAccountProductQuotas(
            @RequestBody @Validated List<UpdateOperateProductQuotaRequest> operateProductQuotasList) {
        if (CollectionUtils.isEmpty(operateProductQuotasList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.CUSTOMER_PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY));
        }
        List<String> statusList = Stream.of(Constants.ZERO, Constants.ONE).collect(Collectors.toList());
        for (UpdateOperateProductQuotaRequest data : operateProductQuotasList) {
            if (!statusList.contains(data.getStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            // 判断data是否存在
            BizAccountProductQuota bizAccountProductQuota = bizAccountProductQuotaService.getById(data.getId());
            if (Objects.isNull(bizAccountProductQuota)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
            if(bizAccountProductQuota.getEntityId() != null
                    && !bizAccountProductQuota.getEntityId().equals(RequestContextUtil.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_803173586));
            }
            // 客户有产品全部且状态为启用，不允许启用其他客户限制
            if (0L != bizAccountProductQuota.getServiceId() && ONE.equals(data.getStatus())) {
                QueryWrapper<BizAccountProductQuota> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_sid", bizAccountProductQuota.getUserSid())
                            .eq("service_id", ZERO)
                            .eq("entity_id", RequestContextUtil.getEntityId())
                            .eq("status", ONE);
                if (Objects.nonNull(bizAccountProductQuotaService.getOne(queryWrapper))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ACTIVATION_FAILED_THE_CUSTOMER));
                }
            }
        }
        for (UpdateOperateProductQuotaRequest data : operateProductQuotasList) {
            BizAccountProductQuota convert = BeanConvertUtil.convert(data, BizAccountProductQuota.class);
            WebUserUtil.prepareInsertParams(convert);
            bizAccountProductQuotaService.updateById(convert);
            // 存入缓存
            BizAccountProductQuota bizAccountProductQuota = bizAccountProductQuotaService.getById(data.getId());
            if (Objects.nonNull(bizAccountProductQuota.getMinFrozenAmount())) {
                Long userSid = bizAccountProductQuota.getUserSid();
                BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(getEntityId(), userSid);
                //账户拥有不计费产品，跳过冻结
                if ("normal".equalsIgnoreCase(account.getStatus()) && bizProductQuotaService.isHaveNonBillAccount(
                        account)) {
                    continue;
                }
                Long accountId = account.getId();
                String serviceId = String.valueOf(bizAccountProductQuota.getServiceId());
                BigDecimal minFrozenAmount = bizAccountProductQuota.getMinFrozenAmount();
                String frozenAmountStatus = bizAccountProductQuota.getFrozenAmountStatus();
                if (ONE.equals(data.getStatus())) {
                    redisProductQuotaAccount(accountId, serviceId, minFrozenAmount, frozenAmountStatus);
                } else {
                    JedisUtil.INSTANCE.hdel(PRODUCTS_QUOTA_ACCOUNT_KEY + accountId, String.valueOf(serviceId));
                }
                // 解冻/冻结账户
                CompletableFuture.runAsync(new TraceRunnable(tracer,spanNamer,()-> {
                    iBizBillingAccountService.doUnfreezeUser(accountId);
                }), cloudExecutor);
            }
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 获取产品下拉框
     * @param userSids
     * @return List<DescribeProductsResponse>
     */
    @AuthorizeBss(action = BD07.GET_PRODUCT_QUOTA)
    @ApiOperation(httpMethod = "GET", value = "获取产品下拉框")
    @GetMapping("/products")
    public List<DescribeProductsResponse> findProducts(
            @RequestParam(value = "userSids", required = false) List<Long> userSids) {
        QueryWrapper<ServiceCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("entity_id", getEntityId())
                    .eq("editable", ONE)
                    .eq("status", "using")
                    .eq("publish_status", "succeed")
                    .isNotNull("publish_dt");
        List<ServiceCategory> result = serviceCategoryService.list(queryWrapper);
        List<String> excludeServiceTypes = Stream.of(ProductEnum.HPC.getProductType(),
                                                     ProductEnum.HPC_SAAS.getProductType())
                                                 .collect(Collectors.toList());
        result = result.stream()
                       .filter(s -> !"MIRROR_CENTER".equals(s.getProductCode()))
                       .filter(e -> !excludeServiceTypes.contains(e.getServiceType()))
                       .collect(Collectors.toList());
        // 判断用户是否已有客户限额，有则不显示全部
        if (CollectionUtils.isNotEmpty(userSids)) {
            QueryWrapper<BizAccountProductQuota> accountQueryWrapper = new QueryWrapper<>();
            accountQueryWrapper.in("user_sid", userSids);
            accountQueryWrapper.eq("entity_id", getEntityId());
            int count = bizAccountProductQuotaService.count(accountQueryWrapper);
            if (0 == count) {
                ServiceCategory serviceCategory = new ServiceCategory();
                serviceCategory.setId(0L);
                serviceCategory.setProductName(EVERYTHING);
                result.add(serviceCategory);
                Collections.swap(result, result.size() - 1, 0);
            }
        }
        List<DescribeProductsResponse> convert = BeanConvertUtil.convert(result, DescribeProductsResponse.class);
        List<SfServiceCategoryHpcClusterPool> sfServiceCategoryHpcClusterPool = serviceCategoryMapper.selectServiceCategoryHPCByEntityId(
                getEntityId());
        for (SfServiceCategoryHpcClusterPool hpcClusterPool : sfServiceCategoryHpcClusterPool) {
            convert.add(new DescribeProductsResponse(hpcClusterPool.getServiceId(), hpcClusterPool.getClusterName(),
                                                     hpcClusterPool.getClusterId()));
        }
        return convert;
    }


    /**
     * 查询该产品是否支持最小冻结金额
     * @param serviceId
     * @return Boolean
     */
    @AuthorizeBss(action = BD07.BD07)
    @ApiOperation(httpMethod = "GET", value = "查询该产品是否支持最小冻结金额")
    @GetMapping("/isMinFrozenAmount/{serviceId}")
    public Boolean isMinFrozenAmount(
            @ApiParam(value = "产品ID", required = true) @PathVariable("serviceId") Long serviceId) {
        return bizProductQuotaService.isMinFrozenAmount(serviceId);
    }


    /**
     * [INNER API] 检查产品限额冻结解冻
     * @param accountId
     * @return
     */
    @RejectCall
    @ApiOperation(httpMethod = "POST", value = "检查产品限额冻结解冻")
    @PostMapping("/check/{accountId}")
    public void checkProductQuota(
            @ApiParam(value = "产品ID", required = true) @PathVariable("accountId") Long accountId) {
        iBizBillingAccountService.doUnfreezeUser(accountId);
    }


    /**
     * 查询产品
     *
     * @param serviceId id
     */
    public ServiceCategory queryProduct(Long serviceId) {
        if (0 == serviceId) {
            ServiceCategory serviceCategory = new ServiceCategory();
            serviceCategory.setProductName(EVERYTHING);
            serviceCategory.setShowType(ALL);
            return serviceCategory;
        }
        return serviceCategoryService.getById(serviceId);
    }

    /**
     * 启用/禁用客户限额 存入redis
     *
     * @param userSid 客户userSid
     * @param serviceId 服务id
     * @param minFrozenAmount 最小冻结金额
     * @param status 启用/禁用
     */
    public void redisProductQuotaAccount(Long userSid, String serviceId, BigDecimal minFrozenAmount, String status) {
        if (ONE.equals(status)) {
            if (ZERO.equals(serviceId)) {
                JedisUtil.INSTANCE.del(PRODUCTS_QUOTA_ACCOUNT_KEY + userSid);
                JedisUtil.INSTANCE.hset(PRODUCTS_QUOTA_ACCOUNT_KEY + userSid, serviceId,
                                        String.valueOf(minFrozenAmount));
            }
            JedisUtil.INSTANCE.hset(PRODUCTS_QUOTA_ACCOUNT_KEY + userSid, serviceId,
                                    String.valueOf(minFrozenAmount));
        } else if (ZERO.equals(status)) {
            JedisUtil.INSTANCE.hdel(PRODUCTS_QUOTA_ACCOUNT_KEY + userSid, serviceId);
        }
    }

    /**
     * 启用/禁用客户限额 存入redis
     *
     * @param serviceId 服务id
     * @param minFrozenAmount 最小冻结金额
     * @param status 启用/禁用
     */
    public void redisProductQuota(String serviceId, BigDecimal minFrozenAmount, String status) {
        if (ONE.equals(status)) {
            JedisUtil.INSTANCE.set(PRODUCTS_QUOTA_KEY + serviceId, String.valueOf(minFrozenAmount));
        } else if (ZERO.equals(status)) {
            JedisUtil.INSTANCE.del(PRODUCTS_QUOTA_KEY + serviceId);
        }
    }

    /**
     * 获取entityId
     */
    public Long getEntityId() {
        return RequestContextUtil.getEntityId();
    }
}
