/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.oss.common.safe.EnumValue;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * 账户收支明细查询 入参
 *
 * <AUTHOR>
 */
@ApiModel(description = "账户收支明细查询")
@Data
public class DescribeDealsRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易时间开始
     */
    @ApiModelProperty("交易时间开始")
    @SafeHtml
    private String startTime;

    /**
     * 交易时间结束
     */
    @ApiModelProperty("交易时间结束")
    @SafeHtml
    private String endTime;

    /**
     * 充值类型
     */
    @ApiModelProperty("充值类型，alipay,platform,coupon,balance,cashAmount,acctCash,accCredit,balanceCash,deductBalanceCash")
    @EnumValue(strValues = {"alipay","platform","coupon","balance","cashAmount","acctCash","accCredit","balanceCash","deductBalanceCash"})
    @SafeHtml
    private String tradeChannel;

    /**
     * 账户名称
     */
    @ApiModelProperty("账户名称")
    @Length(max = 256, message = "账户名称超过限制")
    @SafeHtml
    private String accountNameLike;

    /**
     * 收支类型
     */
    @ApiModelProperty("收支类型，in（收入） out （支出）")
    @EnumValue(strValues = {"in","out"})
    @Length(max = 8, message = "收支类型超过限制")
    @SafeHtml
    private String type;

    /**
     * 交易类型
     */
    @ApiModelProperty("交易类型 charge（充值）pay（消费）")
    @EnumValue(strValues = {"charge","pay","refund","clearance"})
    @Length(max = 16, message = "交易类型超过限制")
    @SafeHtml
    private String tradeType;

    /**
     * 账单号
     */
    @ApiModelProperty("账单号")
    @Length(max = 32, message = "账单号超过限制")
    @SafeHtml
    private String orderNoLike;

    /**
     * 交易编号
     */
    @ApiModelProperty("交易编号")
    @Length(max = 32, message = "交易编号超过限制")
    @SafeHtml
    private String flowNoLike;

    /**
     * 账单号
     */
    @ApiModelProperty("账单号")
    @Length(max = 32, message = "账单号超过限制")
    @SafeHtml
    private String billNoLike;

    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    @Length(max = 32, message = "交易流水号超过限制")
    @SafeHtml
    private String tradeNoLike;

    /**
     * 账户sid
     */
    @ApiModelProperty("账户")
    @Length(max = 128, message = "账户超过限制")
    @SafeHtml
    private String accountSid;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(max = 128, message = "备注超过限制")
    @SafeHtml
    private String remarkLike;

    /**
     * 运营实体名称
     */
    @ApiModelProperty("运营实体名称")
    @Length(max = 32, message = "运营实体名称超过限制")
    @SafeHtml
    private String entityName;


    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    @SafeHtml
    private String accountLike;

    @ApiModelProperty("自定义信息过滤字段")
    private String customizationInfo;
}
