/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;

import org.apache.commons.compress.utils.Lists;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/03/19 19:18
 */
public enum ProductCodeEnum {
    ECS("ECS", "云主机", "compute", "instance"),
    BMS("BMS", "裸金属服务", "compute", "instance"),
    /**
     * 存储下的云硬盘是EBS，ma的云硬盘是EVS
     */
    DISK("EBS", "云硬盘", "storage", "disk"),
    FLOATING_IP("EIP", "弹性IP", "network", "floatingIp"),
    RDS("RDS", "云数据库", "rds", "rds"),
    VBS_VM("VBS-VM", "主机快照", "vbs", "vbs"),
    VBS_VD("VBS-VD", "硬盘快照", "vbs", "vbs"),
    HPC("HPC", "HPC共享资源池", "hpc", "hpc"),
    HPC_SAAS("HPC-SAAS", "HPC共享资源池-SAAS", "hpc-saas", "hpc-saas"),
    OBS("OBS", "对象存储", "obs", "obs"),
    MODEL_ARTS("ModelArts", "AI开发平台共享资源池", "modelArts", "modelArts"),
    ECS_X86("ECS-X86", "云主机", "ecs-x86", "ecs-x86"),
    EVS("EVS", "云硬盘", "evs", "evs"),
    VPC("VPC", "虚拟私有云", "vpc", "vpc"),
    VPN("VPN", "虚拟专用网络", "vpn", "vpn"),
    EIP_INNER("EIP-INNER", "弹性公网IP", "eip-inner", "eip-inner"),
    DC("DC", "云专线", "dc", "dc"),
    IMS("IMS", "镜像服务", "ims", "ims"),
    CBR("CBR", "云备份", "cbr", "cbr"),
    SFS_TURBO("SFS_TURBO", "弹性文件服务", "SFS_TURBO", "SFS_TURBO"),
    ELB("ELB", "弹性负载均衡", "elb", "elb"),
    CES("CES", "云监控服务", "ces", "ces"),
    SDRS("SDRS", "存储容灾服务", "sdrs", "sdrs"),
    ROMA_CONNECT("ROMA-CONNECT", "应用与数据连接", "roma-connect", "roma-connect"),
    AOM("AOM", "应用运维管理", "aom", "aom"),
    APM("APM", "应用性能管理", "apm", "apm"),
    ER("ER", "企业路由器", "er", "er"),
    VPCEP("VPCEP", "VPC终端节点", "vpcep", "vpcep"),
    CTS("CTS", "云审计服务", "cts", "cts"),
    LTS("LTS", "云日志服务", "lts", "lts"),
    CBH("CBH", "云堡垒机服务", "cbh", "cbh"),
    VSS("VSS", "漏洞管理服务", "vss", "vss"),
    AIkits("AIkits", "盘古大脑服务", "AIkits", "AIkits"),
    Modelarts_Studio("Modelarts_Studio", "ModelartsStudio服务", "Modelarts_Studio", "Modelarts_Studio"),
    APPSTAGE("AppStage", "AppStage服务", "AppStage", "AppStage"),
    CLOUD_SECURITY("CloudSecurity", "云安全服务", "cloudSecurity", "cloudSecurity"),
    SWR("SWR", "容器镜像服务", "swr", "swr"),
    DEDICATED_RESOURCE_POOL("DRP", "AI开发平台专属资源池", "drp", "drp"),
    IAAS("IAAS", "昇腾IAAS专属云服务", "iaas", "iaas"),
    SFS("SFS", "弹性文件服务", "sfs", "sfs"),
    MODELARTS("ModelArts", "AI开发平台共享资源池", "modelarts", "modelarts"),
    SFS2("SFS2.0", "弹性文件服务2.0", "sfs2.0", "sfs2.0"),
    HPC_DRP("HPC-DRP", "HPC专属资源池", "hpc-drp", "hpc-drp"),
    HPC_SRP("HPC-SRP", "HPC共享资源池", "hpc-srp", "hpc-srp"),
    BIZ_BAG("BIZ-BAG", "套餐包", "biz_bag", "biz_bag"),
    MA_BMS("MA-BMS", "弹性裸金属", "ma-bms", "ma-bms"),
    HSS("HSS", "企业主机安全服务", "HSS", "HSS"),
    DWS("DWS", "云数据仓库", "DWS", "DWS"),
    AI_MARKET("AI-MARKET", "模型集市", "ai_market", "ai_market"),
    WAF("WAF", "应用防火墙", "waf", "waf"),
    SEC_MASTER("SEC-MASTER", "安全态势感知", "SEC-MASTER", "SEC-MASTER"),
    DNS("DNS", "内网云解析服务", "dns", "dns"),
    DBSS("DBSS", "数据库安全服务", "DBSS", "DBSS"),
    CFW("CFW", "云防火墙", "CFW", "CFW"),
    DEW("DEW", "数据加密服务", "dew", "dew"),
    DS("DS", "数据治理中心服务", "ds", "ds"),
    CSS("CSS", "云搜索服务", "css", "css"),
    AS_GROUP("AS", "弹性伸缩组", "as-group", "as-group"),
    CCE("CCE", "云容器引擎", "CCE", "CCE"),
    MRS("MRS", "大数据服务", "MRS", "MRS"),
    GIT("GIT", "城市智能中枢", "GIT", "GIT"),
    IIT("IIT", "工业智能中枢", "IIT", "IIT"),
    DCS("DCS", "分布式缓存服务", "DCS", "DCS"),
    RS_BMS("RS-BMS", "裸金属服务器", "RS-BMS", "RS-BMS"),
    NAT("NAT", "网关服务", "NAT", "NAT"),
    DSC("DSC", "数据安全中心DSC服务", "DSC", "DSC"),
    SMN("SMN", "消息通知服务SMN", "SMN", "SMN"),
    ;

    private String productCode;

    private String productName;

    private String classify;

    private String priceKey;

    ProductCodeEnum(String productType, String productName, String classify, String priceKey) {
        this.productCode = productType;
        this.productName = productName;
        this.classify = classify;
        this.priceKey = priceKey;
    }

    public String getProductType() {
        return productCode;
    }

    public String getProductName() {
        return productName;
    }

    public String getClassify() {
        return classify;
    }

    public String getPriceKey() {
        return priceKey;
    }

    public static String tran(String type) {
        if (ProductCodeEnum.ECS.getProductType().equals(type)) {
            return "compute";
        }
        return type;
    }

    public static String keyFromDesc(String key) {
        if (key == null) {
            return StrUtil.EMPTY;
        }

        for (ProductCodeEnum value : ProductCodeEnum.values()) {
            if (value.getProductType().equalsIgnoreCase(key)) {
                return value.getProductName();
            }
        }

        return StrUtil.EMPTY;
    }

    public static ProductCodeEnum toEnum(String key) {
        if (StrUtil.isEmpty(key)) {
            return null;
        }

        for (ProductCodeEnum value : ProductCodeEnum.values()) {
            if (value.getProductType().equalsIgnoreCase(key)) {
                return value;
            }
        }

        return null;
    }

    public static String toDesc(String key) {
        if (key == null) {
            return StrUtil.EMPTY;
        }

        for (ProductCodeEnum value : ProductCodeEnum.values()) {
            if (value.getProductType().equalsIgnoreCase(key)) {
                if (noRequireCodeProducts().contains(key)) {
                    return value.getProductName();
                }
                String productCode = translateProductCode(value.getProductType());
                productCode = productCode.split("-")[0];
                productCode = DEDICATED_RESOURCE_POOL.productCode.equals(productCode)
                        || IAAS.productCode.equals(productCode)
                        || MODEL_ARTS.productCode.equals(productCode)? "" : productCode;
                return StrUtil.concat(true, value.getProductName(), " ", productCode);
            }
        }

        return StrUtil.EMPTY;
    }

    public static boolean isBillingInnerProduct(String productCode) {
        return Stream.of(OBS, MODEL_ARTS, DEDICATED_RESOURCE_POOL)
            .anyMatch(productCodeEnum -> productCodeEnum.getProductType().equals(productCode));
    }

    public static boolean isInnerProduct(String productCode) {
        return Stream.of(OBS, MODEL_ARTS, ECS_X86, EVS, VPC, VPN, EIP_INNER, DC, IMS, ELB,
            CLOUD_SECURITY, SWR, DEDICATED_RESOURCE_POOL,IAAS, AS_GROUP, NAT)
            .anyMatch(productCodeEnum -> productCodeEnum.getProductType().equals(productCode));
    }

    /**
     * 需要检查资源定价的产品
     * @return
     */
    public static List<String> requirePriceProducts() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.HPC.getProductType());
        products.add(ProductCodeEnum.MODELARTS.getProductType());
        products.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
        products.add(ProductCodeEnum.IAAS.getProductType());
        return products;
    }

    /**
     * 产品名称后面不需要跟code的产品
     * @return
     */
    public static List<String> noRequireCodeProducts() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.MODELARTS.getProductType());
        products.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
        products.add(ProductCodeEnum.IAAS.getProductType());
        products.add(ProductCodeEnum.HPC_DRP.getProductType());
        products.add(ProductCodeEnum.HPC.getProductType());
        products.add(ProductCodeEnum.BIZ_BAG.getProductType());
        return products;
    }

    public static String translateProductCode(String code) {
        String result = code;
        if (ProductCodeEnum.RS_BMS.getProductType().equals(code)) {
            result = "BMS";
        }
        return result;
    }
    /**
     * 相关服务  比如HPC关联SFS
     */
    public static final Map<String, List<String>> serviceRelateMap = new HashMap() {
        {
            put(ProductCodeEnum.MODELARTS.getProductType(), Arrays.asList(
                    ProductCodeEnum.MODELARTS.getProductType(),
                    ProductCodeEnum.OBS.getProductType()
            ));
            put(ProductCodeEnum.HPC.getProductType(), Arrays.asList(
                    ProductCodeEnum.HPC.getProductType(),
                    ProductCodeEnum.SFS.getProductType()
            ));
            put(ProductCodeEnum.HPC_DRP.getProductType(), Arrays.asList(
                    ProductCodeEnum.HPC_DRP.getProductType(),
                    ProductCodeEnum.ECS.getProductType(),
                    ProductCodeEnum.ECS_X86.getProductType(),
                    ProductCodeEnum.BMS.getProductType()
            ));
        }
    };


    public static List<String> getRelateServiceType(String productCode) {
        List<String> result = serviceRelateMap.get(productCode);
        if (CollectionUtil.isEmpty(result)) {
            return Arrays.asList(productCode);
        }
        return result;
    }

    /**
     * 平台支持的内置产品
     */
    public static List<String> innerServiceProducts() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.MODELARTS.getProductType());
        products.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
        products.add(ProductCodeEnum.HPC_DRP.getProductType());
        products.add(ProductCodeEnum.HPC.getProductType());
        products.add(ProductCodeEnum.HPC_SAAS.getProductType());
        products.add(ProductCodeEnum.SFS2.getProductType());
        products.add(ProductCodeEnum.SFS.getProductType());
        return products;
    }

    /**
     * 嵌入产品
     * @return
     */
    public static List<String> federationProducts() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.WAF.getProductType());
        products.add(ProductCodeEnum.HSS.getProductType());
        products.add(ProductCodeEnum.DWS.getProductType());
        products.add(ProductCodeEnum.SEC_MASTER.getProductType());
        products.add(ProductCodeEnum.DNS.getProductType());
        products.add(ProductCodeEnum.DBSS.getProductType());
        products.add(ProductCodeEnum.CFW.getProductType());
        products.add(ProductCodeEnum.DEW.getProductType());
        products.add(ProductCodeEnum.DS.getProductType());
        products.add(ProductCodeEnum.CSS.getProductType());
        products.add(ProductCodeEnum.CES.getProductType());
        products.add(ProductCodeEnum.MRS.getProductType());
        products.add(ProductCodeEnum.GIT.getProductType());
        products.add(ProductCodeEnum.IIT.getProductType());
        products.add(ProductCodeEnum.SDRS.getProductType());
        products.add(ProductCodeEnum.ROMA_CONNECT.getProductType());
        products.add(ProductCodeEnum.AOM.getProductType());
        products.add(ProductCodeEnum.APM.getProductType());
        products.add(ProductCodeEnum.ER.getProductType());
        products.add(ProductCodeEnum.DC.getProductType());
        products.add(ProductCodeEnum.VPCEP.getProductType());
        products.add(ProductCodeEnum.CTS.getProductType());
        products.add(ProductCodeEnum.LTS.getProductType());
        products.add(ProductCodeEnum.CBH.getProductType());
        products.add(ProductCodeEnum.VSS.getProductType());
        products.add(ProductCodeEnum.AIkits.getProductType());
        products.add(ProductCodeEnum.APPSTAGE.getProductType());
        products.add(ProductCodeEnum.Modelarts_Studio.getProductType());
        products.add(ProductCodeEnum.SMN.getProductType());
        products.add(ProductCodeEnum.DCS.getProductType());
        return products;
    }

    /**
     * 是否cmp Api集成资源
     * @return
     */
    public static List<String> isCmpApiProduct() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.ECS.getProductType());
        products.add(ProductCodeEnum.FLOATING_IP.getProductType());
        products.add(ProductCodeEnum.DWS.getProductType());
        products.add(ProductCodeEnum.CCE.getProductType());
        products.add(ProductCodeEnum.CBR.getProductType());
        products.add(ProductCodeEnum.DISK.getProductType());
        products.add(ProductCodeEnum.ELB.getProductType());
        products.add(ProductCodeEnum.SFS_TURBO.getProductType());
        products.add(ProductCodeEnum.DCS.getProductType());
        products.add(ProductCodeEnum.RS_BMS.getProductType());
        products.add(ProductCodeEnum.RDS.getProductType());
        return products;
    }


    /**
     *  是否走通用API请求
     * @param productCode
     * @return
     */
    public static boolean isCommonRequest(String productCode) {
        return Stream.of(CCE,CBR,SFS_TURBO,DISK,RDS,DCS)
                .anyMatch(productCodeEnum -> productCodeEnum.getProductType().equals(productCode));
    }

    /**
     * 不包含按量付费的产品
     */
    public static List<String> noPostPaidProduct() {
        List<String> products = Lists.newArrayList();
        products.add(ProductCodeEnum.SFS_TURBO.getProductType());
        products.add(ProductCodeEnum.RDS.getProductType());
        products.add(ProductCodeEnum.CBR.getProductType());
        products.add(ProductCodeEnum.CCE.getProductType());
        products.add(ProductCodeEnum.DCS.getProductType());
        products.add(ProductCodeEnum.DWS.getProductType());
        products.add(ProductCodeEnum.MRS.getProductType());
        products.add(ProductCodeEnum.CSS.getProductType());
        return products;
    }

    public static String getNameByI18n(String type, boolean en) {

        if (StrUtil.isBlank(type)){
            return "";
        }
        for (ProductCodeEnum code : ProductCodeEnum.values()){
            if (type.equals(code.getProductType())){
                return en ? code.getProductType() : code.getProductName();
            }
        }
        return "";
    }

}
