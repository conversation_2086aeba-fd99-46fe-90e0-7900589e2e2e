/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeOrderResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 我的订单
 *
 * <AUTHOR>
 * @date 2020/5/9.
 */
@Api(tags = "我的订单")
@RestController
@RequestMapping("/audit/approval")
@Validated
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private BizInquiryPriceService inquiryPriceService;
    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;
    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private IServiceOrderDetailService orderDetailService;

    /**
     * 查询订单列表
     *
     * @param request 查询订单请求体
     * @return {@code RestResult}
     */
    @GetMapping("/orders")
    @AuthorizeBss(action = CC.CC01)
    @ApiOperation(httpMethod = "GET", value = "查询订单列表", notes = "查询订单列表")
    public RestResult<List<DescribeOrderResponse>> getOrderList(DescribeOrderRequest request) {
        Map<String, Object> query = BeanUtil.beanToMap(request, false, true);
        query.put("hasTenantAccess", sysUserService.checkUserHasSomeAccess(AuthUtil.getAuthUser().getUserSid(),
                AuthConstants.TENANT_ACCESS));
        Long pagesize = request.getPagesize();
        if(pagesize == null ){
            query.put("pagesize",10);
        }
        Long pagenum = request.getPagenum();
        if( pagenum == null){
            query.put("pagenum",0);
        }

        RestResult orderList = orderService.getOrderList(query);
        HashMap data = (HashMap) orderList.getData();
        List<DescribeOrderResponse> orderResponseList = JSONUtil.toList(JSONUtil.parseArray(data.get("dataList")), DescribeOrderResponse.class);
        for (DescribeOrderResponse order : orderResponseList) {
            if ("退订中".equals(order.getStatusName()) && Objects.nonNull(order.getResourceId())) {
                //获取预退订金额
                String type = Optional.ofNullable(sfProductResourceService.getById(order.getResourceId())).map(
                        SfProductResource::getProductType).orElse(null);
                UnsubInquiryPriceVO unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(order.getResourceId(), type);
                if(Objects.nonNull(unsubInquiryPriceVO)){
                    order.setOriginalCost(unsubInquiryPriceVO.getUnsubAmount().negate());
                    order.setFinalCost(unsubInquiryPriceVO.getUnsubAmount().negate());
                }
            }
        }
        if (data.get("dataList") instanceof List){
            ArrayList<HashMap> dataList = (ArrayList<HashMap>) data.get("dataList");
            for (HashMap map:dataList) {
                if ("开通失败".equals(map.get("statusName")) && Objects.isNull(map.get("statusInfo"))){
                    String statusInfo = "未查询到错误信息";
                    //开通失败订单查询失败信息
                    Object orderId = map.get("id");
                    if (Objects.nonNull(orderId)) {
                        LambdaQueryWrapper<ServiceOrderDetail> qw = new LambdaQueryWrapper<>();
                        qw.eq(ServiceOrderDetail::getOrderId, orderId);
                        List<ServiceOrderDetail> list = orderDetailService.list(qw);
                        if (list.stream().anyMatch(e -> StringUtils.containsAnyIgnoreCase(e.getServiceType(), ProductCodeEnum.SFS2.getProductType()
                                , ProductCodeEnum.SFS.getProductType()) && !ProductCodeEnum.SFS_TURBO.getProductType().equals(e.getServiceType()))) {
                            RestResult share = shareService.getShareById(Long.parseLong(map.get("resourceId").toString()));
                            HashMap shareData = (HashMap) share.getData();
                            statusInfo = Objects.isNull(shareData) ? "未查询到错误信息" : shareData.get("errorMsg").toString();
                        }
                    }
                    map.put("statusInfo", statusInfo);
            }
        }
        }
        return orderList;
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单id
     * @return {@code RestResult}
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation(httpMethod = "GET", value = "获取订单详情", notes = "获取订单详情")
    @AuthorizeBss(action = CC.CC0101)
    public RestResult<ServiceOrder> getOrderDetailByOrderId(@PathVariable @ApiParam(name = "orderId", required = true) Long orderId) {
        return orderService.getOrderDetailByOrderId(orderId);
    }

}
