/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * The type LockInstanceRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/9
 */
@Getter
@Setter
@ApiModel(description = "锁定|解锁实例")
public class LockInstanceRequest {

    /**
     * 主机ID列表
     */
    @NotEmpty
    @ApiModelProperty(value = "主机ID列表", required = true)
    private List<String> ids;

    /**
     * 锁定状态
     */
    @NotBlank
    @ApiModelProperty(value = "锁定状态", required = true)
    private String lockStatus;
}
