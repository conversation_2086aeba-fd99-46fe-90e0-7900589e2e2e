/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request;

import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/7  10:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("管理员创建套餐包参数")
public class CreateBizBagRequest  {

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @NotNull(message = "名称不能为空")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,32}$", message = "名称长度为2-32个字符,并且不能包含特殊符号")
    private String name;

    /**
     *适用产品
     */
    @ApiModelProperty("适用产品")
    @NotBlank(message = "适用产品不能为空")
    @EnumValue(strValues = {"HPC", "HPC-SAAS","ModelArts"})
    private String productType;

    /**
     *套餐包计费类型,包年包月：PrePaid，按量付费：PostPaid
     */
    @ApiModelProperty("计费类型")
    @NotBlank(message = "计费类型不能为空")
    @EnumValue(strValues = {"PrePaid"})
    private String billingType;

    /**
     *套餐包类型,折扣包：discount 资源包：resource
     */
    @ApiModelProperty("套餐类型")
    @NotBlank(message = "套餐类型不能为空")
    @EnumValue(strValues = {"discount","card_hour"})
    private String type;

    /**
     *描述
     */
    @ApiModelProperty("描述")
    @Length(max = 256)
    private String description;
    /**
     * 规格列表
     */
    @ApiModelProperty("规格列表")
    @NotNull(message = "规格列表不能为空")
    List<BizBagSpec>  bizBagRequests ;

    /**
     *描述
     */
    @ApiModelProperty("运营实体ID")
    private Long entityId;

    /**
     * HPC共享资源池clusterId
     */
    @ApiModelProperty("HPC共享资源池clusterId")
    private String clusterId;
}
