/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.response;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 企业列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "企业列表")
public class CompanyOpenApiResponse {

    private Long companyId;
    private String refOrgId;
    private Long orgId;
    private String companyName;
    private String contactName;
    private String contactPhone;
    private String contactEmail;
    private String address;
    private String applicationScenario;
    private String applicationScenarioName;
    private String personnelSize;
    private String personnelSizeName;
    private String industry;
    private String industryName;
    private Date createdDt;
}
