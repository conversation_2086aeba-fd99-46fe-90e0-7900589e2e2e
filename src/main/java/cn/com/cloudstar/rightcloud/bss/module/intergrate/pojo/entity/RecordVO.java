/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/9
 */
@Data
@ApiModel(description = "记录对象")
public class RecordVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String username;
    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;
    /**
     * 时间
     */
    @ApiModelProperty("时间")
    private Date time;
    /**
     * 文件记录列表
     */
    @ApiModelProperty("文件记录列表")
    private List<RecordFileVO> recordFiles;
    /**
     * 01 售后工程师回复，02 自己回复
     */
    @ApiModelProperty("1 售后工程师回复，02 自己回复")
    private String type;

}
