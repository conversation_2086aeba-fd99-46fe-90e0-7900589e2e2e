/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceRunnable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import io.seata.common.util.StringUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCUnsubscribeReconciliation;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCUnsubscribeReconciliationResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.NatGateway;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResContainerCluster;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants.ChargeType;
import cn.com.cloudstar.rightcloud.bss.common.constants.CloudEnvType;
import cn.com.cloudstar.rightcloud.bss.common.constants.PriceType;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductOperation;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductService;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.LdapUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ChidrenDescDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.CurrentConfigDesc;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.CurrentConfigDescStr;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.DescDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ModifyInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.NodeValueDTO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.UnsubscribeInquiryPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ActionParam;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.OperateNodeRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductConfigDesc;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpgradeServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.UpgradeDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractDetail;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractDetailService;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.ResFederationInst;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResourceCompensation;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterPoolRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.EcsService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceCompensationService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MailService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResFederationInstService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaPoolService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.HpcDrpDegradeCompensation;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.HpcDrpDeleteNodeTipVO;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServicePrice;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.request.ResizeResourceInfo;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.request.ResizeResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.order.factory.OrderServiceFactory;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.SystemConfigService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.mapper.ResRenewRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.RenewalDTO;
import cn.com.cloudstar.rightcloud.bss.module.renewal.service.ResRenewRefService;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.ResourceConfigService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SidService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.enums.resource.HuaweiCloudDiskModeEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResChangeTypeEnum;
import cn.com.cloudstar.rightcloud.common.tracelog.TraceUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants.Sfs2Message;
import cn.com.cloudstar.rightcloud.oss.common.constants.ServiceConfigArrKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.ClusterNodeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.BillingAccountStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ContractStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BssRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.FederationStatusConstants;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BillingPricesDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.DataDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ServiceConfig;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.CloudClientFactory;
import cn.com.cloudstar.rightcloud.oss.common.util.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.ActiveDirectory;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendNotifyRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResBmsNodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResVmNodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResAllDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResSpecTypeAttr;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResShareParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cce.CceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.CmpResRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.SysHpcPassRemoteService;

/**
 * <p>
 * 申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
@Slf4j
@Service
public class ServiceOrderServiceImpl extends ServiceImpl<ServiceOrderMapper, ServiceOrder> implements
        IServiceOrderService {

    private static final String LOGIN_NODE_INFO = "loginNodeInfo";

    private static final String COMPUTE_NODE_INFO = "computeNodeInfo";

    private static final String  ACCOUNT_EXPIRE = "ACCOUNT_EXPIRE";

    private static final String HPC_SHRINKAGE_MAX_COUNT = "hpc.shrinkage.max.count";
    public static final String VNC = "vnc";

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private ResRenewRefService resRenewRefService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private BizInquiryPriceService inquiryPriceService;

    @Autowired
    private ResRenewRefMapper resRenewRefMapper;

    @Autowired
    private EcsService ecsService;

    @Autowired
    private ResourceConfigService resourceConfigService;

    @Autowired
    private CodeMapper codeMapper;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;

    @DubboReference
    private ResVmRemoteService resVmRemoteService;

    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;


    @DubboReference
    private ResAllRemoteService resAllRemoteService;

    @DubboReference
    private ShareRemoteService shareRemoteService;
    @Autowired
    private HpcClusterService hpcClusterService;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;

    @Autowired
    private IServiceOrderService serviceOrderService;

    @Autowired
    private BizContractDetailMapper contractDetailMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @DubboReference
    private CceRemoteService cceRemoteService;

    @DubboReference
    private ResFloatingIpRemoteService floatingIpRemoteService;

    @DubboReference
    private ResVdRemoteService vdRemoteService;

    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;

    @Autowired
    private LdapUserService ldapUserService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private IBizBillingAccountService accountService;

    @Autowired
    private ResMaPoolService resMaPoolService;
    @Autowired
    private SidService sidService;
    @Autowired
    private BizContractService bizContractService;
    @Autowired
    private BizContractDetailService bizContractDetailService;
    @Autowired
    private HPCDrpOrderServiceImpl hpcDrpOrderService;
    @Autowired
    private ISfProductResourceCompensationService sfProductResourceCompensationService;
    @Autowired
    private OrderServiceFactory orderServiceFactory;
    @Autowired
    private IBizDistributorProductService distributorProductService;
    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private MailService mailService;

    @Autowired
    private UserRoleMapper  userRoleMapper;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ServiceCategoryService categoryService;

    @Autowired
    private BizBillingAccountMapper billingAccountMapper;

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;
    @DubboReference
    private SysHpcPassRemoteService hpcPassRemoteService;

    private final List<String> NOT_SUPPORT_UNSUBSCRIBE = Arrays.asList("frozen", "normal", "expired", "available",
                                                                       "normal", "modify_error", "mount_error");

    @Autowired
    private Tracer tracer;
    @Autowired
    private SpanNamer spanNamer;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private ResFederationInstService resFederationInstService;
    @DubboReference
    private CmpResRemoteService cmpResRemoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult unsubscribe(String id, Long accountId, Long behalfUserSid, BigDecimal amount, String type, String originStatus,
                                  String applyType, Long entityId, ActionParam actionParam) {
        boolean expireTag = false;
        if(accountId != null){
            String expire = JedisUtil.INSTANCE.get(accountId.toString());
            log.info("试用期账号的标识[{}]",expire);
            expireTag = ACCOUNT_EXPIRE.equals(expire);
        }
        User authUser = null;
        if(expireTag){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
            authUser = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        }else{
            AuthUser threadLocalAuthUser = AuthUserHolder.getAuthUser();
            authUser = BeanConvertUtil.convert(threadLocalAuthUser, User.class);
        }
        ProductOperation productOperation = new ProductOperation();
        if (Objects.nonNull(behalfUserSid)) {
            productOperation.setUserSid(authUser.getUserSid());
            productOperation.setBehalfUserSid(behalfUserSid);
        }
        if (ProductCodeEnum.HPC.getProductType().equals(type)
                || ProductCodeEnum.HPC_SAAS.getProductType().equals(type)
                || ProductCodeEnum.HPC_DRP.getProductType().equals(type)) {
            productOperation.setResourceOriginStatus(originStatus);
            productOperation.setApplyType(applyType);
        }
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(type)) {
            productOperation.setApplyType(applyType);
        }
        productOperation.setOrderType("release");
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user
                = sysUserService.selectByPrimaryKey(authUser.getUserSid());
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_923351580));
        }
        productOperation.setConsumer(user);
        List<ProductInfo> productInfos = Lists.newArrayList();
        //现在来说 这行代码已经成为了垃圾代码,每个资源需要自己写自己的特殊退订逻辑,而且id可能重复,可能导致退订意外的资源类型,比如你想退vm
        //结果退了vd
        BigDecimal unsubscribeAmount = BigDecimal.ZERO;
        //超期补扣金额
        BigDecimal makeUpAmount = BigDecimal.ZERO;
        BizBillingAccount account = bizBillingAccountMapper.getByEntityIdAndUserId(entityId, user.getUserSid());
        if (Objects.isNull(amount)) {
            UnsubInquiryPriceVO unsubInquiryPriceVO;
            if (ProductCodeEnum.SFS2.getProductType().equals(type)) {
                unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
            } else {
                DescribeProductResourceResponse resourceResponse = sfProductResourceService.getDetail(Long.valueOf(id));
                if (ProductCodeEnum.HPC.getProductType().equals(resourceResponse.getProductType())
                        || ProductCodeEnum.HPC_SAAS.getProductType().equals(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(resourceResponse.getProductType(),
                                                                                type);
                } else if (ProductCodeEnum.HPC_DRP.getProductType().equals(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
                } else if (ProductCodeEnum.MODEL_ARTS.getProductType().equals(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
                } else if (ProductCodeEnum.federationProducts().contains(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, resourceResponse.getProductType());
                    //退订询价退订金额
                    UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = inquiryPriceService.unsubInquiryAIPrice(id);
                    //退订金额
                    unsubscribeAmount = unsubInquiryPriceVO.getUnsubAmount();
                    //超期补扣金额使用询价超期金额
                    makeUpAmount =
                            unsubscribeInquiryPriceVO.getExpiredUsedAmount() != null ? unsubscribeInquiryPriceVO.getExpiredUsedAmount() : BigDecimal.ZERO;
                } else if (ProductCodeEnum.ECS.getProductType().equals(resourceResponse.getProductType())
                        || ProductCodeEnum.RS_BMS.getProductType().equals(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
                }
                else if (ProductCodeEnum.FLOATING_IP.getProductType().equals(resourceResponse.getProductType())
                  || ProductCodeEnum.DISK.getProductType().equals(resourceResponse.getProductType())
                        || ProductCodeEnum.RDS.getProductType().equals(resourceResponse.getProductType())
                        || ProductCodeEnum.DCS.getProductType().equals(resourceResponse.getProductType())
                        || ProductCodeEnum.SFS_TURBO.getProductType().equals(resourceResponse.getProductType())) {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
                }
                else {
                    unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
                    //退订询价退订金额
                    UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = inquiryPriceService.unsubInquiryAIPrice(id);
                    //退订金额
                    unsubscribeAmount = unsubInquiryPriceVO.getUnsubAmount();
                    //超期补扣金额使用询价超期金额
                    makeUpAmount =
                            unsubscribeInquiryPriceVO.getExpiredUsedAmount() != null ? unsubscribeInquiryPriceVO.getExpiredUsedAmount() : BigDecimal.ZERO;
                }
            }
            if (!Objects.isNull(unsubInquiryPriceVO) && CollectionUtil.isNotEmpty(unsubInquiryPriceVO.getDetail())) {
                unsubInquiryPriceVO.getDetail().forEach(d -> {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setId(d.getId());
                    productInfo.setProjectId(d.getProjectId());
                    BigDecimal unsubAmount = d.getTotalUsedAmount().subtract(d.getTotalPayment());
                    if (ProductCodeEnum.SFS2.getProductType().equals(type)) {
                        unsubAmount = d.getUnsubAmount().negate();
                        productInfo.setPeriod(new BigDecimal(d.getBuyMonth()));
                    }
                    productInfo.setPrice(unsubAmount);
                    productInfo.setFinalCost(unsubAmount);
                    productInfo.setServices(d.getServices());
                    productInfo.setQuantity(1);
                    ServicePrice serviceAmount = d.getServiceAmount();
                    productInfo.setServiceAmount(
                            serviceAmount.getTotalUsed().subtract(serviceAmount.getTotalPayment()));
                    ServicePrice extraConfigAmount = d.getExtraConfigAmount();
                    productInfo.setExtraConfigAmount(
                            extraConfigAmount.getTotalUsed().subtract(extraConfigAmount.getTotalPayment()));
                    //超期补扣金额
                    productInfo.setExpiredUsedAmount(unsubInquiryPriceVO.getExpiredUsedAmount());
                    if (ProductCodeEnum.MODEL_ARTS.getProductType().equalsIgnoreCase(type)) {
                        productInfo.setExpiredUsedAmount(unsubAmount.compareTo(BigDecimal.ZERO) > 0 ? unsubAmount : BigDecimal.ZERO);
                    }
                    if (ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(type)) {
                        productInfo.setExpiredUsedAmount(d.getExpiredUsedAmount()
                                                          .add(d.getServiceAmount().getExpiredUsedAmount()));
                        //只退订现金
                        productInfo.setFinalCost(NumberUtil.sub(BigDecimal.ZERO, d.getUnsubCashAmount()));
                    }
                    if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(type)
                            || ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(type)) {
                        productInfo.setExpiredUsedAmount(d.getExpiredUsedAmount().add(d.getServiceAmount().getExpiredUsedAmount()));
                        //只退订现金
                        productInfo.setPrice(NumberUtil.sub(BigDecimal.ZERO, d.getUnsubCashAmount()));
                        productInfo.setFinalCost(NumberUtil.sub(BigDecimal.ZERO, d.getUnsubCashAmount()));
                    }
                    productInfo.setServices(d.getServices());
                    productInfo.setProductCode(d.getProductCode());
                    productInfo.setOrderDetailId(d.getOrderDetailId());
                    resRenewRefService.completeProductInfo(productInfo);
                    if (Objects.nonNull(d.getComputeDate())) {
                        productInfo.setStartTime(d.getComputeDate());
                    }
                    if (Objects.nonNull(d.getNow())) {
                        productInfo.setEndTime(d.getNow());
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("resourcePriceBefore", d.getHourPrice());
                    jsonObject.put("orderType", "release");
                    jsonObject.put("resourceId", id);
                    HashMap<Object, Object> map = new HashMap<>();
                    map.put("resourceChangeRecord", jsonObject.toJSONString());
                    productInfo.setOptions(map);
                    if (id.equals(d.getId())) {
                        productInfo.setPrincipal(true);
                    }
                    if (ProductCodeEnum.HPC_DRP.getProductType().equals(d.getProductCode())) {
                        productInfo.setClusterId(
                                Optional.ofNullable(sfProductResourceService.getById(id))
                                        .map(SfProductResource::getClusterId).orElse(null));
                    }
                    productInfos.add(productInfo);
                });
                productOperation.setUnsubAmount(unsubInquiryPriceVO.getUnsubAmount());
            } else if (!Objects.isNull(unsubInquiryPriceVO)) {
                productOperation.setUnsubAmount(unsubInquiryPriceVO.getUnsubAmount());
            }
        } else {
            productOperation.setUnsubAmount(amount);
        }
        if (Objects.nonNull(productOperation.getUnsubAmount())) {
            BigDecimal invoiceableAmount = account.getTotalRechargeAmount().subtract(account.getInvoicedAmount()).subtract(account.getOfflineInvoicedAmount());
            BigDecimal balance = account.getBalance().compareTo(BigDecimal.ZERO) > 0 ?  account.getBalance() : BigDecimal.ZERO;
            balance = BigDecimalUtil.remainTwoPointAmount(balance);
            invoiceableAmount = invoiceableAmount.subtract(balance);
            if (invoiceableAmount.setScale(3,BigDecimal.ROUND_HALF_UP).compareTo(productOperation.getUnsubAmount().setScale(3,BigDecimal.ROUND_HALF_UP)) < 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.UNSUBSCRIBE_AMOUNT_EXCEEDS));
            }
        }

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo.getParentSid() != null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_899185304));
        }
        SfProductResource sfProductResource = sfProductResourceService.getById(id);
        if (Objects.nonNull(sfProductResource)) {
            //设置集群id  为了后续查询
            productOperation.setClusterId(sfProductResource.getClusterId());
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(type)) {
                if (Objects.nonNull(behalfUserSid) && !NOT_SUPPORT_UNSUBSCRIBE.contains(
                        sfProductResource.getStatus())) {
                    // 运营侧只支持退订正常、过期、冻结的资源
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00004));
                }
                sfProductResource.setPreStatus(sfProductResource.getStatus());
                sfProductResourceMapper.updateById(sfProductResource);
            }
        }
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(type)) {
            RestResult share = shareService.getShareById(Long.valueOf(id));
            ResShare resShare = BeanConvertUtil.convert(share.getData(), ResShare.class);
            if (resShare != null) {
                //设置集群id  为了后续查询
                productOperation.setClusterId(Long.valueOf(id));
                if (resShare.getClusterId() != null) {
                }
                String status = resShare.getStatus();
                if (ShareStatus.deleting.equals(status)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_DELETING));
                }
                if (ShareStatus.DELETED.equals(status)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_DELETED));
                }
                if (Objects.nonNull(behalfUserSid) && !NOT_SUPPORT_UNSUBSCRIBE.contains(status)) {
                    // 运营侧只支持退订正常、过期、冻结的资源
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00004));
                }
            }
        } else {
            if (sfProductResource != null) {
                //设置集群id  为了后续查询
                productOperation.setClusterId(sfProductResource.getClusterId());
                String status = sfProductResource.getStatus();
                if (SfProductEnum.DELETING.getStatus().equals(status) || SfProductEnum.UNSUBSCRIBING.getStatus()
                                                                                                    .equals(status)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_DELETING));
                }
                if (SfProductEnum.DELETED.getStatus().equals(status) || SfProductEnum.UNSUBSCRIBED.getStatus()
                                                                                                  .equals(status)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_DELETED));
                }
            }
        }

        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(type)) {
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + id + "\"");
            param.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired"));
            param.put("productCode", type);
            param.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                    .selectByCriteria(param);
            List<String> serviceOrderIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(orderPriceDetails)) {
                serviceOrderIds = orderPriceDetails.stream()
                                                   .map(ServiceOrderPriceDetail::getOrderId)
                                                   .map(Convert::toStr)
                                                   .distinct()
                                                   .collect(Collectors.toList());
            }

            org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                    .where("orderId").in(serviceOrderIds)
                    .and("invoiceStatus").in("pending", "done");
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> invoicedCostList = mongoTemplate
                    .find(Query.query(criteria),
                          cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class,
                          "biz_bill_usage_item");

            if (CollectionUtil.isNotEmpty(invoicedCostList)) {
                Date maxEndDate = invoicedCostList.stream()
                                                  .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getUsageEndDate)
                                                  .max((e1, e2) -> e1.compareTo(e2))
                                                  .get();
                if (maxEndDate.after(new Date())) {
                    throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1151614270));
                }
            }
        }

        if (ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(type)) {
            sfProductResource.setStatus(SfProductEnum.UNSUBSCRIBING.getStatus());
            sfProductResourceService.updateById(sfProductResource);
        }
        if (Objects.nonNull(sfProductResource) && !ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(type)
                && !ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(type)) {
            // 33844 退订资源
            if (!sfProductResource.getProductType().contains(ProductCodeEnum.HPC.getProductType())
                    && !sfProductResource.getProductType().contains(ProductCodeEnum.HPC_SAAS.getProductType())
                    && !sfProductResource.getProductType().contains(ProductCodeEnum.ECS.getProductType())
                    && !ProductCodeEnum.RS_BMS.getProductType().equals(sfProductResource.getProductType())) {
                sfProductResource.setStatus(SfProductEnum.UNSUBSCRIBING.getStatus());
                sfProductResourceService.updateById(sfProductResource);
                if (ProductCodeEnum.federationProducts().contains(sfProductResource.getProductType())) {
                    LambdaUpdateWrapper<ResFederationInst> updateWrapper = new LambdaUpdateWrapper<ResFederationInst>()
                            .eq(ResFederationInst::getResourceId, sfProductResource.getId())
                            .set(ResFederationInst::getStatus, FederationStatusConstants.UNSUBSCRIBING);
                    resFederationInstService.update(updateWrapper);
                }
            } else if (ProductCodeEnum.ECS.getProductType().equals(sfProductResource.getProductType())
                    || ProductCodeEnum.RS_BMS.getProductType().equals(sfProductResource.getProductType())) {
                productInfos.forEach(e -> {
                    LambdaUpdateWrapper<SfProductResource> update = new LambdaUpdateWrapper<>();
                    update.eq(SfProductResource::getId, Long.valueOf(e.getId()));
                    update.setSql("pre_status = status");
                    update.set(SfProductResource::getStatus, SfProductEnum.UNSUBSCRIBING.getStatus());
                    sfProductResourceService.update(null, update);
                });
            }
            ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService
                    .getOne(Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                                    .eq(ServiceOrderResourceRef::getResourceId, sfProductResource.getId())
                                    .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType()));
            if (ObjectUtils.isEmpty(serviceOrderResourceRef)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailService
                    .getById(serviceOrderResourceRef.getOrderDetailId());
            List<String> serviceOrderIds = Lists
                    .newArrayList(Convert.toStr(serviceOrderDetail.getOrderId()));
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + id + "\"");
            param.put("orderTypes", Lists.newArrayList("renew"));
            param.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> renewOrderPriceDetails = serviceOrderPriceDetailMapper
                    .selectByCriteria(param);
            List<String> renewServiceOrderIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(renewOrderPriceDetails)) {
                renewServiceOrderIds = renewOrderPriceDetails.stream()
                                                             .map(ServiceOrderPriceDetail::getOrderId).map(Convert::toStr).distinct()
                                                             .collect(Collectors.toList());
                serviceOrderIds.addAll(renewServiceOrderIds);
            }

            org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                    .where("orderId").in(serviceOrderIds)
                    .and("invoiceStatus").in("pending", "done");
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> invoicedCostList = mongoTemplate
                    .find(Query.query(criteria), cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class, "biz_bill_usage_item");

            if (CollectionUtil.isNotEmpty(invoicedCostList)) {
                Date maxEndDate = invoicedCostList.stream()
                                                  .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getUsageEndDate)
                                                  .max((e1, e2) -> e1.compareTo(e2))
                                                  .get();
                if (maxEndDate.after(new Date())) {
                    throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1151614270));
                }
            }

            ProductInfo productInfo = productInfos.stream().filter(
                    p -> Objects.equals(p.getProductCode(), ProductCodeEnum.HPC_DRP.getProductType()) ||
                            Objects.equals(p.getProductCode(), ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType())
                                                  || Objects.equals(p.getProductCode(), ProductCodeEnum.ECS.getProductType())
                                                  || Objects.equals(p.getProductCode(), ProductCodeEnum.RS_BMS.getProductType()))
                                                  .findFirst().orElse(new ProductInfo());
            productInfo.setProjectId(sfProductResource.getOrgSid());
            productInfo.setProductCode(sfProductResource.getProductType());
            productInfo.setPrincipal(true);
            String chargeType = serviceOrderDetail.getChargeType();
            productInfo.setChargeType(chargeType);
            productInfo.setServiceId(Convert.toStr(serviceOrderDetail.getServiceId()));
            String productConfigDesc = productInfo.getProductConfigDesc();
            productInfo.setProductConfigDesc(serviceOrderDetail.getProductConfigDesc());
            //超期退订的时候补扣金额
            if (!ProductCodeEnum.HPC_DRP.getProductType().equalsIgnoreCase(sfProductResource.getProductType())
                    && !ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(sfProductResource.getProductType())
                    && !ProductCodeEnum.RS_BMS.getProductType().equalsIgnoreCase(sfProductResource.getProductType())) {
                productInfo.setExpiredUsedAmount(makeUpAmount);
                //支付金额
                productInfo.setFinalCost(unsubscribeAmount.negate());
            }

            String serviceConfig = serviceOrderDetail.getServiceConfig();
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(serviceConfig);
            if (ProductCodeEnum.HPC_DRP.getProductType()
                                       .equalsIgnoreCase(sfProductResource.getProductType())) {
                jsonObject.getJSONObject("productConfigDesc")
                          .put("currentConfigDesc", JSONUtil.parseObj(productConfigDesc).get("currentDesc"));
            }
            jsonObject.put("productResourceId", id);
            ServiceOrder serviceOrder = serviceOrderService.getById(serviceOrderDetail.getOrderId());
            if (ObjectUtils.isEmpty(serviceOrder)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            productOperation.setResourceInfo(serviceOrder.getResourceInfo());
            jsonObject.put("payCost", serviceOrder.getFinalCost());
            jsonObject.put("applyServiceOrderId", serviceOrder.getId());
            jsonObject.put("renewServiceOrderIds", renewServiceOrderIds);
            jsonObject.put("originalEndTime", sfProductResource.getEndTime());

            //MA专属资源池新增扩容节点
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(type)
                    && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))) {
                List<ServiceOrderVo> serviceOrders = serviceOrderMapper.selectOrderByCluster(serviceOrder.getClusterId(), type);
                List<String> applyServiceOrderIds = Lists.newArrayList();
                List<String> upgradeServiceOrderIds = Lists.newArrayList();
                List<String> degradeServiceOrderIds = Lists.newArrayList();
                for (ServiceOrderVo applyServiceOrderId : serviceOrders) {
                    if ("apply".equals(applyServiceOrderId.getType())) {
                        applyServiceOrderIds.add(String.valueOf(applyServiceOrderId.getId()));
                    } else if ("upgrade".equals(applyServiceOrderId.getType())) {
                        upgradeServiceOrderIds.add(String.valueOf(applyServiceOrderId.getId()));
                    } else if ("degrade".equals(applyServiceOrderId.getType())) {
                        degradeServiceOrderIds.add(String.valueOf(applyServiceOrderId.getId()));
                    }
                }
                jsonObject.put("upgradeServiceOrderId", upgradeServiceOrderIds);
                jsonObject.put("applyServiceOrderId", applyServiceOrderIds);
                jsonObject.put("degradeServiceOrderId", degradeServiceOrderIds);
            } else {
                jsonObject.put("applyServiceOrderId", serviceOrder.getId());
            }

            Date startTime = sfProductResource.getStartTime();
            Date endTime = sfProductResource.getEndTime();
            if (startTime != null && endTime != null) {
                long month = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime, false);
                cn.hutool.json.JSONObject productConfigDescJOB = jsonObject.getJSONObject("productConfigDesc");
                if (Objects.nonNull(productConfigDescJOB) && productConfigDescJOB.getObj("currentConfigDesc") != null) {
                    cn.hutool.json.JSONArray descJOB = productConfigDescJOB.getJSONArray("currentDesc");
                    if (descJOB != null) {
                        coinfigMonth(descJOB, month);
                        productInfo.setProductConfigDesc(JsonUtil.toJson(descJOB));
                    } else {
                        String descArryStr = productConfigDescJOB.getObj("currentConfigDesc").toString();
                        try {
                            if (JSONUtil.isTypeJSONArray(descArryStr)) {
                                cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(descArryStr);
                                coinfigMonth(jsonArray, month);
                                productConfigDescJOB.put("currentConfigDesc", JSONArray.toJSONString(jsonArray));
                            } else if (JSONUtil.isTypeJSONObject(descArryStr)) {
                                descArryStr = resRenewRefService.newConfigurationReplacement(productOperation.getClusterId(), type,
                                                                               descArryStr);
                                productInfo.setProductConfigDesc(descArryStr);
                            }
                        } catch (JSONException e) {
                            productInfo.setProductConfigDesc(descArryStr);
                        }

                    }
                }
                if (ProductCodeEnum.HPC_DRP.getProductType().equals(sfProductResource.getProductType())) {
                    productInfos.stream().forEach(pInfo -> pInfo.setPeriod(new BigDecimal(month)));
                }
            }
            if (ProductCodeEnum.RS_BMS.getProductType().equals(productInfo.getProductCode())
                    || ProductCodeEnum.ECS.getProductType().equals(productInfo.getProductCode())) {
                cn.hutool.json.JSONObject configDesc = jsonObject.getJSONObject("productConfigDesc");
                cn.hutool.json.JSONObject currentConfigDesc = configDesc.getJSONObject("currentConfigDesc");
                cn.hutool.json.JSONArray jsonArray = currentConfigDesc.getJSONArray("productConfigDesc");
                jsonArray.removeIf(o -> {
                    cn.hutool.json.JSONObject object = (cn.hutool.json.JSONObject) o;
                    if ("申请数量".equals(object.getStr("label"))) {
                        return true;
                    }else {
                        return false;
                    }
                });
                configDesc.putOpt("currentConfigDesc", currentConfigDesc);

                cn.hutool.json.JSONObject serviceConfigObj = JSONUtil.parseObj(productInfo.getProductConfigDesc());
                cn.hutool.json.JSONArray configObjJSONArray = serviceConfigObj.getJSONArray("productConfigDesc");
                configObjJSONArray.removeIf(o -> {
                    cn.hutool.json.JSONObject object = (cn.hutool.json.JSONObject) o;
                    if ("申请数量".equals(object.getStr("label"))) {
                        return true;
                    }else {
                        return false;
                    }
                });
                productInfo.setProductConfigDesc(serviceConfigObj.toString());
            }else if (ProductCodeEnum.NAT.getProductType().equals(productInfo.getProductCode())) {
                cn.hutool.json.JSONObject configDesc = jsonObject.getJSONObject("productConfigDesc");
                cn.hutool.json.JSONObject currentConfigDesc = configDesc.getJSONObject("currentConfigDesc");
                cn.hutool.json.JSONArray jsonArray = currentConfigDesc.getJSONArray("productConfigDesc");
                NatGateway natGateway = cmpResRemoteService.selectNatGatewayById(sfProductResource.getClusterId());
                if (Objects.isNull(natGateway)) {
                    throw new BizException(MsgCd.ERR_MSG_BSS_607095064);
                }
                String specDesc = "";
                switch (natGateway.getNatTypeCode()) {
                    case "1":
                        specDesc = "小型|SNAT最大连接数10000";
                        break;
                    case "2":
                        specDesc = "中型|SNAT最大连接数50000";
                        break;
                    case "3":
                        specDesc = "大型|SNAT最大连接数200000";
                        break;
                    case "4":
                        specDesc = "超大型|SNAT最大连接数1000000";
                        break;
                    default:
                }
                for (Object o : jsonArray) {
                    cn.hutool.json.JSONObject object = (cn.hutool.json.JSONObject) o;
                    if ("规格".equals(object.getStr("label"))) {
                        object.set("value", specDesc);
                    }else if ("名称".equals(object.getStr("label"))) {
                        object.set("value", natGateway.getName());
                    }
                }
                configDesc.putOpt("currentConfigDesc", currentConfigDesc);

                cn.hutool.json.JSONObject serviceConfigObj = JSONUtil.parseObj(productInfo.getProductConfigDesc());
                cn.hutool.json.JSONArray configObjJSONArray = serviceConfigObj.getJSONArray("productConfigDesc");
                for (Object o : configObjJSONArray) {
                    cn.hutool.json.JSONObject object = (cn.hutool.json.JSONObject) o;
                    if ("规格".equals(object.getStr("label"))) {
                        object.set("value", specDesc);
                    }else if ("名称".equals(object.getStr("label"))) {
                        object.set("value", natGateway.getName());
                    }
                }
                serviceOrder.setName(natGateway.getName());

                productInfo.setProductConfigDesc(serviceConfigObj.toString());
            }
            productInfo.setConfig(JSONUtil.toJsonStr(jsonObject));
            //重新设置HPC HPC-Sass询价价格
            if (ProductCodeEnum.HPC.getProductType().equals(productInfo.getProductCode())
                || ProductCodeEnum.HPC_SAAS.getProductType().equals(productInfo.getProductCode())) {
                resetHpcUnsubPrice(productInfo,id);
            }

            if (serviceOrderDetail.getQuantity() != null && serviceOrderDetail.getQuantity() > 1) {
                serviceOrderDetail.setPrice(NumberUtil.div(productInfo.getFinalCost() != null ? productInfo.getFinalCost() : BigDecimal.ZERO,
                                                           serviceOrderDetail.getQuantity()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            if (!Objects.equals(productInfo.getProductCode(), ProductCodeEnum.ECS.getProductType())
                    && !Objects.equals(productInfo.getProductCode(), ProductCodeEnum.RS_BMS.getProductType())) {
                productInfo.setPrice(serviceOrderDetail.getPrice());
                productInfo.setQuantity(serviceOrderDetail.getQuantity());
            }
            productInfo.setName(serviceOrder.getName());
            productInfo.setSettlementType(Optional.ofNullable(serviceOrder.getSettlementType()).orElse("标准价"));
            productInfo.setContractId(serviceOrder.getContractId());
            productInfo.setClusterId(sfProductResource.getClusterId());

            if (ProductCodeEnum.CCE.getProductType().equals(type)) {
                com.alibaba.fastjson.JSONObject object = com.alibaba.fastjson.JSONObject.parseObject(productInfo.getProductConfigDesc());
                ResContainerCluster containerCluster = cceRemoteService.selectByPrimaryKey(sfProductResource.getClusterId());
                com.alibaba.fastjson.JSONArray array = object.getJSONArray("productConfigDesc");
                for (int i = 0; i < array.size(); i++) {
                    com.alibaba.fastjson.JSONObject item = array.getJSONObject(i);
                    String label = item.getString("label");
                    if ("集群规模".equals(label)) {
                        item.put("value", containerCluster.getSize() + "节点");
                    }
                }
                productInfo.setProductConfigDesc(JSONUtil.toJsonStr(object));

                JSONObject config = JSONObject.parseObject(productInfo.getConfig());
                JSONObject productConfigDesc2 = config.getJSONObject("productConfigDesc");
                JSONObject currentConfigDesc = productConfigDesc2.getJSONObject("currentConfigDesc");
                JSONArray productConfigDescArry = currentConfigDesc.getJSONArray("productConfigDesc");
                for (int i = 0; i < productConfigDescArry.size(); i++) {
                    com.alibaba.fastjson.JSONObject item = productConfigDescArry.getJSONObject(i);
                    String label = item.getString("label");
                    if ("集群规模".equals(label)) {
                        item.put("value", containerCluster.getSize() + "节点");
                    }
                }
                productConfigDesc2.put("currentConfigDesc", JSONObject.toJSONString(currentConfigDesc));

                productInfo.setConfig(JSONObject.toJSONString(config));
            }

            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(type)) {
                productInfo.setApplyType(applyType);
                if (ApplyTypeEnum.DEPTRAIN.getType().equals(serviceOrderDetail.getApplyType())
                        && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))) {

                    productInfo.setEndTime(sfProductResource.getEndTime());
                    Integer availableCount = Integer.valueOf(resMaPoolService.getResMaPoolAvailableCount(serviceOrder.getClusterId())
                                                                             .getData().toString());
                    boolean isAarray = serviceOrderDetail.getProductConfigDesc().startsWith("{");
                    cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray();
                    if (!isAarray) {
                        jsonArray = JSONUtil.parseArray(serviceOrderDetail.getProductConfigDesc());

                    } else {
                        jsonArray = JSONUtil.parseObj(serviceOrderDetail.getProductConfigDesc()).getJSONArray("currentDesc");
                    }
                    for (Object object : jsonArray) {
                        cn.hutool.json.JSONObject object1 = (cn.hutool.json.JSONObject) object;
                        Object value = object1.get("value");
                        if (Objects.isNull(value)) {
                            continue;
                        } else if (Objects.equals(object1.get("attrKey"), "count")) {
                            object1.put("value", availableCount + "/" + availableCount);
                        }
                    }
                    productInfo.setProductConfigDesc(jsonArray.toString());
                    productInfo.setQuantity(availableCount);
                }
            }
            if (!ProductCodeEnum.HPC_DRP.getProductType().equals(sfProductResource.getProductType())
                    && !ProductCodeEnum.ECS.getProductType().equals(sfProductResource.getProductType())
                    && !ProductCodeEnum.RS_BMS.getProductType().equals(sfProductResource.getProductType())) {
                productInfo.setStartTime(sfProductResource.getStartTime());
                productInfos.add(productInfo);
            }

            if (ProductCodeEnum.HPC.getProductType().equals(sfProductResource.getProductType())
                    || ProductCodeEnum.HPC_SAAS.getProductType().equals(sfProductResource.getProductType())
                    || ProductCodeEnum.HPC_DRP.getProductType().equals(sfProductResource.getProductType())) {
                if (ProductCodeEnum.HPC.getProductType().equals(sfProductResource.getProductType())) {
                    productInfo.setId("HPC");
                    productInfo.setProductCode("HPC");
                    productInfo.setPrincipal(true);
                    productInfo.setChargeType(ChargeType.POST_PAID);
                }
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(sfProductResource.getClusterId());
                // 冻结的资源 不做作业检查
                log.info("HPC退订作业检查：status[{}]", resHpcClusterRemoteModule.getStatus());
                if (!"frozen".equalsIgnoreCase(resHpcClusterRemoteModule.getStatus())) {
                    //作业检查
                    log.info("HPC退订作业检查进行中！");
                    ldapUserService.checkHPCJobOu(Arrays.asList(resHpcClusterRemoteModule),
                                                  sfProductResource.getOrgSid(), authUser.getUserSid());
            }
                String clusterUuid = serviceOrder.getClusterUuid();
                resHpcClusterRemoteModule.setClusterUuid(
                        org.apache.commons.lang.StringUtils.isBlank(clusterUuid) ? "-1" : clusterUuid);
                //调用CCP对账接口
                CompletableFuture.runAsync(new TraceRunnable(tracer, spanNamer, () -> {
                    callCCPReconciliation(resHpcClusterRemoteModule);
                }));
            }
            if (Objects.nonNull(applyType)) {
                productInfo.setApplyType(applyType);
            }

            if (ProductCodeEnum.MODEL_ARTS.getProductType()
                                          .equals(sfProductResource.getProductType())) {
                // 判断ModelArts下是否存在未关闭资源
                List<String> productTypes = new ArrayList<>();
                productTypes.add(ProductCodeEnum.IAAS.getProductType());
                productTypes.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
                if (CollectionUtil.isNotEmpty(sfProductResourceService.actionResources(productTypes, user.getOrgSid()))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1556683373));
                }

                LambdaQueryWrapper<ServiceOrderDetail> queryWrapper = Wrappers
                        .<ServiceOrderDetail>lambdaQuery()
                        .eq(ServiceOrderDetail::getOrderId, serviceOrder.getId())
                        .ne(ServiceOrderDetail::getId, serviceOrderDetail.getId());
                List<ServiceOrderDetail> applyObsDetails = serviceOrderDetailService
                        .list(queryWrapper);
                if (applyObsDetails.size()!=0) {
                    for (ServiceOrderDetail applyObsDetail : applyObsDetails) {
                    ProductInfo obsProductInfo = new ProductInfo();
                    obsProductInfo.setProjectId(sfProductResource.getOrgSid());
                    obsProductInfo.setProductCode(applyObsDetail.getServiceType());
                    obsProductInfo.setChargeType(applyObsDetail.getChargeType());
                    obsProductInfo.setServiceId(Convert.toStr(applyObsDetail.getServiceId()));
                    obsProductInfo.setProductConfigDesc(applyObsDetail.getProductConfigDesc());
                    jsonObject.put("payCost", serviceOrder.getFinalCost());
                    jsonObject.put("applyServiceOrderId", serviceOrder.getId());
                    jsonObject.put("renewServiceOrderIds", renewServiceOrderIds);
                    obsProductInfo.setConfig(applyObsDetail.getServiceConfig());
                    obsProductInfo.setPrice(applyObsDetail.getPrice());
                    obsProductInfo.setQuantity(applyObsDetail.getQuantity());
                    obsProductInfo.setStartTime(applyObsDetail.getStartTime());
                    productInfos.add(obsProductInfo);
                }
            }
        }
        }

        setChargingType(id, productOperation);
        // 添加审批单
        if (Objects.nonNull(actionParam)) {
            productOperation.setResourceInfo(JSONUtil.toJsonStr(actionParam));
        }
        if (ProductCodeEnum.DCS.getProductType()
                .equals(sfProductResource.getProductType()) && CollUtil.isNotEmpty(productInfos) && StrUtil.isBlank(productInfos.get(0).getResourceId())) {
            productInfos.get(0).setResourceId(id);
        }
        productOperation.setProductInfo(productInfos);
        return orderService.releaseOrder(productOperation);
    }

    /**
     * HPC HPC-Saas 退订询价，按当前配置价格设置
     *
     * @return
     */
    private void resetHpcUnsubPrice(ProductInfo productInfo,String id) {
        String configStr = productInfo.getConfig();
        if (StringUtils.isNotEmpty(configStr)) {
            ServiceConfig config = JSONObject.parseObject(configStr, ServiceConfig.class);
            DataDTO dataDTO = config.getData();
            Long cloudEnvId = Long.valueOf(config.getCloudEnvId());
            String productCode = config.getProductCode();
            //查询订单明细
            SfProductResource sfProductResource = sfProductResourceService.getById(Long.valueOf(id));
            Long serviceOrderId = sfProductResource.getServiceOrderId();
            ServiceOrder serviceOrder = serviceOrderService.lambdaQuery().eq(ServiceOrder::getId, serviceOrderId).one();
            List<ServiceOrderDetail> orderDetails = serviceOrderDetailService.lambdaQuery()
                .eq(ServiceOrderDetail::getOrderId, serviceOrderId)
                .eq(ServiceOrderDetail::getServiceType,productInfo.getProductCode())
                .orderByAsc(ServiceOrderDetail::getId).list();
            serviceOrder.setOrderDetails(orderDetails);
            //查询当前价格
            ApplyServiceRequest applyServiceRequst = inquiryPriceService.createPostPaidApplyServiceRequst(serviceOrder);
            ProductInfoVO productInfoVO = applyServiceRequst.getProductInfo().get(0);
            productInfoVO.setData(dataDTO);
            ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(applyServiceRequst, ApplyServiceVO.class);
            applyServiceVO.setAccountId(productInfoVO.getBillingAccountId());
            List<InquiryPriceResponse> priceResponses = inquiryPriceService.inquiryPrice(applyServiceVO);
            //重新设置价格
            InquiryPriceResponse priceResponse = CollectionUtil.getFirst(priceResponses);
            if (priceResponse != null && CollectionUtil.isNotEmpty(priceResponse.getBillingPrices())) {
                List<BillingPricesDTO> pricesDTOS = cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil.convert(priceResponse.getBillingPrices(), BillingPricesDTO.class);
                dataDTO.setBillingPrices(pricesDTOS);
                productInfo.setConfig(JSON.toJSONString(config));
            }
        }
    }

    /**
     * @Description:设置计费类型
     * @Param:
     */
    private void setChargingType(String id, ProductOperation productOperation) {
        Criteria param = new Criteria();
        param.put("refInstanceIdLike", "\"" + id + "\"");
        param.put("orderTypes", Lists.newArrayList("apply", "renew"));
        param.put("orderStatus", "completed");
        List<ServiceOrderPriceDetail> OrderPriceDetails = serviceOrderPriceDetailMapper
                .selectByCriteria(param);
        if (CollectionUtil.isNotEmpty(OrderPriceDetails)) {
            ServiceOrderPriceDetail serviceOrderPriceDetail = CollectionUtil.getFirst(OrderPriceDetails);
            productOperation.setChargingType(serviceOrderPriceDetail.getChargingType());
        }
    }

    /**
     * 调用ccp对账接口
     *
     * @param resHpcClusterRemoteModule
     */
    private void callCCPReconciliation(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {
        try {
            CloudEnvParams params = new CloudEnvParams();
            params.setCloudEnvType(
                    cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType.HCSO.getValue().get(0));
            List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
            if (CollectionUtils.isEmpty(cloudEnvs)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
            }
            HPCUnsubscribeReconciliation hpcUnsubscribeReconciliation =
                    CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(),
                                                   HPCUnsubscribeReconciliation.class);
            hpcUnsubscribeReconciliation.setOptions(
                    MapsKit.of("ROOT_URL",
                               ActiveDirectory.getCcpInternalAddr(resHpcClusterRemoteModule.getCcpInternelAddress())));

            // 获取集群管理员账号，要是没有就获取租户账号
            String userName = StringUtil.EMPTY;
            String userPass = StringUtil.EMPTY;

            String poolUUid = resHpcClusterRemoteModule.getPoolUuid();
            if (StringUtils.isNotBlank(poolUUid)) {
                log.info(
                        "调用ccp对账接口使用集群管理员账户密码 ServiceOrderServiceImpl.callCCPReconciliation OUTPUT poolUUid-clusterID: {}",
                        poolUUid);

                QueryResHpcClusterPoolRequest clusterPoolRequest = new QueryResHpcClusterPoolRequest();
                clusterPoolRequest.setClusterId(poolUUid);
                RestResult resHpcClusterResult = hpcClusterService.getResHpcPool(clusterPoolRequest);
                List<ResHpcClusterPool> hpcClusterPoolMap =
                        JSONObject.parseArray(JSON.toJSONString(resHpcClusterResult.getData()),
                                              ResHpcClusterPool.class);
                if (hpcClusterPoolMap != null && hpcClusterPoolMap.size() > 0) {
                    hpcClusterPoolMap = hpcClusterPoolMap.stream()
                                                         .filter(e -> org.apache.commons.lang.StringUtils.isNotBlank(
                                                                 e.getUsername()))
                                                         .collect(Collectors.toList());
                    if (hpcClusterPoolMap.size() > 0) {
                        ResHpcClusterPool pool = hpcClusterPoolMap.get(0);
                        userName = CrytoUtilSimple.decrypt(pool.getUsername());
                        userPass = CrytoUtilSimple.decrypt(pool.getPassword());

                        log.info(
                                "调用ccp对账接口使用集群管理员账户密码 ServiceOrderServiceImpl.callCCPReconciliation OUTPUT poolId: {}, userName: {}",
                                pool.getId(), userName);
                    }
                }
            }
            if (org.apache.commons.lang.StringUtils.isBlank(userName) || org.apache.commons.lang.StringUtils.isBlank(
                    userPass)) {
                cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserService.selectByPrimaryKey(
                        resHpcClusterRemoteModule.getOwnerId());
                userName = user.getAccount();
                userPass = hpcPassRemoteService.findHpcPass(user.getUserSid());

                log.info(
                        "调用ccp对账接口使用租户账户密码 ServiceOrderServiceImpl.callCCPReconciliation OUTPUT , userName： {}",
                        userName);
            }

            hpcUnsubscribeReconciliation.setTenantUserName(userName);
            hpcUnsubscribeReconciliation.setTenantUserPass(userPass);
            HPCUnsubscribeReconciliationResult result = (HPCUnsubscribeReconciliationResult) MQHelper.rpc(
                    hpcUnsubscribeReconciliation);
            log.info("调用ccp对账接口result:{}", result);
        } catch (Exception e) {
            log.error("调用ccp对账接口异常", e);
        }
    }

    /**
     * 设置时长
     *
     * @param jsonArray
     * @param month
     */
    private void coinfigMonth(cn.hutool.json.JSONArray jsonArray, long month) {
        for (Object json : jsonArray) {
            cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) json;
            Set<String> keySet = jsonObject.keySet();
            for (String key : keySet) {
                String label = jsonObject.getStr(key);
                if ("时长".equals(label)) {
                    jsonObject.put("value", month);
                }
                if ("购买时长".equals(label)) {
                    jsonObject.put("value", month + "个月");
                }
            }
        }
    }


    @Override
    public RestResult queryVmType(String id) {
        List<RenewalDTO> renewalDTOS = resRenewRefMapper.selectRenew(new Criteria().put("id", id));
        RestResult restResult = new RestResult();
        if (CollectionUtil.isEmpty(renewalDTOS)) {
            return restResult;
        }
        RenewalDTO renewalDTO = renewalDTOS.get(0);
        if (CloudEnvType.from(renewalDTO.getCloudEnvType()).isPublic() || "HCSO".equals(renewalDTO.getCloudEnvType())) {
            String data = ecsService.queryVmType(renewalDTO.getId());
            restResult.setData(data);

        } else {

            restResult.setData(this.queryVmType4Private(renewalDTO.getCloudEnvId(), renewalDTO.getId(), null));
        }
        return restResult;
    }

    private String queryVmType4Private(Long envId, String id, String zone) {
        CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(envId);
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1655439511));
        }

        QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
        queryResVmTypeByParamsRequest.setCloudEnvId(envId);
        if (!Strings.isNullOrEmpty(zone)) {
            queryResVmTypeByParamsRequest.setZone(zone);
        }

        List<ResVmType> resVmTypes = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest);

        ResVm resVm = resVmRemoteService.selectByPrimaryKey(id);
        queryResVmTypeByParamsRequest.setUuid(resVm.getInstanceType());
        ResVmType currentInstanceVmTypes = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
        resVmTypes = resVmTypes.stream()
                               .filter(vmType -> vmType.getDisk() >= currentInstanceVmTypes.getDisk() && !"BareMetal".equalsIgnoreCase(vmType.getVirtType()))
                               .filter(vmType -> !currentInstanceVmTypes.getUuid().equals(vmType.getUuid()))
                               .collect(Collectors.toList());

        return JSON.toJSONString(resVmTypes);
    }

    @Override
    public RestResult resizeResource(ResizeResourceRequest request) {
        // 代客下单 使用客户信息操作订单
        AuthUser userInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()),
                                                                                                            cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        ResizeResourceInfo product = request.getProductInfo();
        if (Objects.isNull(ProductCodeEnum.toEnum(product.getResourceType()))) {
            throw new BizException(WebUtil.getMessage(Sfs2Message.RESOURCE_NOT_FOUND));
        }
        User authUser = AuthUtil.getAuthUser();
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user
                = sysUserService.selectByPrimaryKey(authUser.getUserSid());

        // 判断用户状态
        if (!SysMNotifyConfigConstant.StatusEnum.ENABLE.getValue().equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2036929060));
        }
        //补扣区间的数据不能够扩缩容，不能变更配置
        Criteria cri= new Criteria();
        cri.put("refInstanceIdLike",request.getProductInfo().getId());
        cri.put("productCode",request.getProductInfo().getResourceType());
        cri.put("orderStatus",OrderStatus.COMPLETED);

        cri.setOrderByClause("A.start_time DESC");
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = this.serviceOrderPriceDetailMapper.selectByCriteria(cri);
        if(CollectionUtil.isNotEmpty(serviceOrderPriceDetails)){
            Date currentDate = new Date();
            List<ServiceOrderPriceDetail> expireList = serviceOrderPriceDetails.stream().filter(s->s.getType().equals("expired")).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(expireList)){
                List<ServiceOrderPriceDetail> nonExpireList = serviceOrderPriceDetails.stream().filter(s->!s.getType().equals("expired")).collect(Collectors.toList());
                boolean atExpiredTime = true;
                for(ServiceOrderPriceDetail serviceOrderPriceDetail: nonExpireList){
                    if(currentDate.after(serviceOrderPriceDetail.getStartTime())
                            &&  currentDate.before(serviceOrderPriceDetail.getEndTime())){
                        atExpiredTime = false;
                        break;
                    }
                }
                if(atExpiredTime){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_909416061));
                }
            }
        }
        if (Objects.nonNull(request.getProjectId())) {
            List<String> strings = orgMapper.selectAllSidsByOrgSidString(authUser.getOrgSid().toString());
            if (CollectionUtils.isEmpty(strings) || !strings.contains(request.getProjectId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1097139596));
            }
        }

        Org rootOrg = orgService.selectRootOrg(user.getOrgSid());



        if (!"authSucceed".equalsIgnoreCase(user.getCertificationStatus())
                && rootOrg != null
                && !CertificationStatus.AUTHSUCCEED.equals(rootOrg.getCertificationStatus())) {
            throw new BizException(WebUtil.getMessage(Sfs2Message.USER_AUTH));
        }
        if (Objects.isNull(user.getUserSid()) || Objects.isNull(user.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(Sfs2Message.USER_INVALID));
        }
        ProductOperation productOperation = new ProductOperation();
        productOperation.setConsumer(user);
        productOperation.setOrderType("modify");
        productOperation.setBehalfUserSid(request.getBehalfUserSid());
        productOperation.setUserSid(request.getUserSid());
        productOperation.setUserOrgSid(request.getUserOrgSid());
        productOperation.setResourceInfo(JSONUtil.toJsonStr(request.getResourceInfo()));
        ProductInfo productInfo = new ProductInfo();
        if (ProductCodeEnum.ECS.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.CCE.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.DISK.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.FLOATING_IP.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.SFS_TURBO.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.RDS.getProductType().equals(product.getResourceType())
                || ProductCodeEnum.DCS.getProductType().equals(product.getResourceType())
            ) {
            productInfo.setProductCode(product.getResourceType());
            productInfo.setQuantity(1);
            productInfo.setId(request.getProductInfo().getId());
            productInfo.setTargetType(product.getResourceType());
            productInfo.setTargetSize(product.getSize());
            productInfo.setTargetSpec(product.getSpec());
            productInfo.setPrincipal(true);
            SfProductResource sfProductResource = sfProductResourceService.getById(productInfo.getId());
            productOperation.setClusterId(sfProductResource.getClusterId());
        } else {
            //填充其他信息
            ResAllDTO resAllDTO = new ResAllDTO();
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(product.getResourceType())) {
                resAllDTO.setId(product.getId());
                resAllDTO.setProductType(product.getResourceType());
            } else {
                resAllDTO = resAllRemoteService.selectSimpleByPrimaryKey(product.getId());
            }
            RenewalDTO renewalDTO = resRenewRefMapper.selectSimpleByPrimaryKey(resAllDTO.getId(),
                                                                               resAllDTO.getProductType());
            BeanUtil.copyProperties(resAllDTO, renewalDTO,
                                    CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));

            if (Objects.isNull(renewalDTO)) {
                throw new BizException(WebUtil.getMessage(Sfs2Message.RESOURCE_NOT_FOUND));
            }
            if (ProductCodeEnum.DISK.getProductType().equals(renewalDTO.getProductType())) {
                throw new BizException(WebUtil.getMessage(Sfs2Message.NON_SUPPORT_FOR_DISK));
            }
            productInfo.setProductCode(renewalDTO.getProductType());
            productInfo.setQuantity(1);
            productInfo.setId(renewalDTO.getId());
            productInfo.setTargetType(product.getResourceType());
            productInfo.setTargetSize(product.getSize());
            productInfo.setPrincipal(true);
        }
        resRenewRefService.completeProductInfo(productInfo);
        //SFS2.0变配service_detail需要最新的容量
        log.info("getServiceType:{}",productInfo.getProductCode());
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                && Objects.nonNull(productInfo.getTargetSize())) {
            setLatestCapacity(productInfo);
        }
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(product.getResourceType())) {
            productOperation.setClusterId(Long.valueOf(product.getId()));
        }

        if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(productInfo.getProductCode())&&
                Objects.nonNull(productInfo.getTargetSize())) {
            setLatestEcsCapacity(productInfo);
        }
        else if (ProductCodeEnum.CCE.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            setLatestCceCapacity(productInfo);
        } else  if (ProductCodeEnum.FLOATING_IP.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                && Objects.nonNull(productInfo.getTargetSize())) {
            setLatestEipCapacity(productInfo);
        } else  if (ProductCodeEnum.DISK.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                && Objects.nonNull(productInfo.getTargetSize())) {
            SfProductResource sfProductResource = sfProductResourceService.getById(productInfo.getId());
            if(ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(sfProductResource.getProductType())){
                productOperation.setChargingType(BillingConstants.ChargingType.NORMAL_TYPE);
                setLatestRdsCapacity(productInfo);
            }else{
                setLatestEbsCapacity(productInfo);
            }
        }  else  if (ProductCodeEnum.DCS.getProductType().equalsIgnoreCase(productInfo.getProductCode())
                && Objects.nonNull(productInfo.getTargetSize())) {
            setLatestDcsCapacity(productInfo);
        } else if (ProductCodeEnum.SFS_TURBO.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            setLatestSfsTurboCapacity(productInfo);
        }else if (ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(productInfo.getProductCode())) {
            setLatestRdsCapacity(productInfo);
        }

        EntityDTO entity = categoryService.getEntityByCategoryId(Long.parseLong(productInfo.getServiceId()));
            if (Objects.isNull(entity)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1964963679));
            }
        BizBillingAccount bizBillingAccount = billingAccountMapper.selectByOrgId(user.getOrgSid(),
                                                                                 entity.getEntityId());
        if (SysMNotifyConfigConstant.ExpireStrategyEnum.FREEZE.getValue().equals(bizBillingAccount.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729724604));
        }
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entity.getEntityId(), AuthUtil.getAuthUser().getUserSid());
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productInfo.getProductCode())) {
            //查询现金余额
            if (Objects.isNull(account)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
            }
            if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1095922662));
            }
            if (Integer.valueOf(200).compareTo(productInfo.getTargetSize()) < 0){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_178778345));
            }
            SfProductResource sfProductResource = sfProductResourceService.getById(productInfo.getResourceId());
            RestResult result = resMaPoolService.getResMaPoolInfo(sfProductResource.getClusterId());
            ResMaPoolVO resMaPoolVO = BeanConvertUtil.convert(result.getData(), ResMaPoolVO.class);
            if (!user.getUserSid().equals(Long.parseLong(resMaPoolVO.getOwnerId())) || Objects.nonNull(user.getParentSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
            //判断账户是否冻结
            if(Objects.nonNull(sfProductResource)){
                ServiceOrder serviceOrder = serviceOrderMapper.selectById(sfProductResource.getServiceOrderId());
                if(Objects.nonNull(serviceOrder)){
                    List<BizBillingAccount> accounts = billingAccountMapper.findSelfAndSelfCustomer(
                            authUser.getUserSid(), serviceOrder.getEntityId());
                    if(accounts.size()>0){
                        if(SysMNotifyConfigConstant.ExpireStrategyEnum.FREEZE.getValue().equals(accounts.get(0).getStatus())){
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729724604));
                        }
                    }
                    //代客订购的订单，只能运营管理员可以操作，判断当前用户是不是运营管理员
                        if(serviceOrder.getBehalfUserSid() != null){
                            Criteria cw = new Criteria();
                            cw.put("userSid",userInfo.getUserSid());
                            cw.put("roleSid",301);
                            List<UserRole> roles = userRoleMapper.selectByParams(cw);
                            if(CollectionUtil.isEmpty(roles)){
                                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.NO_PERMISSION_OPERATION));
                            }
                        }
                }
            }
            if (!ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1055800560));
            }else {
                productOperation.setApplyType(ApplyTypeEnum.DEPTRAIN.getType());
            }
            if (!"normal".equals(productInfo.getStatus()) || sfProductResource.getEndTime().before(new Date())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1845439562));
            }
            Integer availableCount = resMaPoolVO.getAvailableCount();
            if (productInfo.getTargetSize().equals(availableCount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1708151541));
            }else if (productInfo.getTargetSize().compareTo(availableCount) > 0){
                productOperation.setOrderType(OrderType.UPGRADE);
            }else if (0 > productInfo.getTargetSize().compareTo(availableCount)){
                productOperation.setOrderType(OrderType.DEGRADE);
            }
            productOperation.setClusterId(sfProductResource.getClusterId());
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + product.getId() + "\"");
            param.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired"));
            param.put("productCode", product.getResourceType());
            param.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                    .selectByCriteria(param);
            if (CollectionUtil.isNotEmpty(orderPriceDetails)) {
                RestResult checkNonBillingProductResult = checkNonBillingProduct(orderPriceDetails, productOperation);
                if (!checkNonBillingProductResult.getStatus()){
                    return checkNonBillingProductResult;
                }
            }
        } else {
            String status = productInfo.getStatus();
            if (ShareStatus.UNINSTALL_STALLING.equalsIgnoreCase(status)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1812644843));
            }
            boolean notValidStatus = !"normal".equals(status) &&
                    !"available".equals(status) && !"modify_error".equals(status)
                            && !cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus.MOUNT_ERROR.equals(
                        status);
            if (notValidStatus || (Objects.nonNull(productInfo.getEndTime()) && new Date().after(productInfo.getEndTime()))) {
                throw new BizException(WebUtil.getMessage(Sfs2Message.EXPIRED_NOT_MODIFY));
            }
        }

        //弹性文件扩缩容进行校验判断
        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(product.getResourceType()) ||
                ProductCodeEnum.SFS.getProductType().equalsIgnoreCase(product.getResourceType())) {
                int increment = productInfo.getTargetSize() - productInfo.getCurrentSize();
            String orderType = "";
            if (increment > 0) {
                orderType = OrderType.UPGRADE;
            } else if (increment < 0) {
                orderType = OrderType.DEGRADE;
            } else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_580041844));
                        }


            if (Objects.nonNull(user.getParentSid())) {
                sysUserService.replacementUser(user.getParentSid(), null);
                productOperation.setConsumer(sysUserService.selectByPrimaryKey(user.getParentSid()));
                    }
                }

        if (ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(product.getResourceType())) {
            Criteria param = new Criteria();
            param.put("refInstanceIdLike", "\"" + product.getId() + "\"");
            param.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired"));
            param.put("productCode", product.getResourceType());
            param.put("orderStatus", "completed");
            List<ServiceOrderPriceDetail> orderPriceDetails = serviceOrderPriceDetailMapper
                    .selectByCriteria(param);
            List<String> serviceOrderIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(orderPriceDetails)) {
                //SFS2.0变配赋值serviceId
                ServiceOrderPriceDetail lastOrderDetail = CollectionUtil.getLast(orderPriceDetails);
                productInfo.setServiceId(lastOrderDetail.getServiceId());
                //判断是否关联不计费产品
                RestResult checkNonBillingProductResult = checkNonBillingProduct(orderPriceDetails,productOperation);
                if (!checkNonBillingProductResult.getStatus()){
                    return checkNonBillingProductResult;
                }

                serviceOrderIds = orderPriceDetails.stream()
                        .map(ServiceOrderPriceDetail::getOrderId)
                        .map(Convert::toStr)
                        .distinct()
                        .collect(Collectors.toList());
            }

            org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                    .where("orderId").in(serviceOrderIds)
                    .and("invoiceStatus").in("pending", "done");
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> invoicedCostList = mongoTemplate
                    .find(Query.query(criteria),
                            cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost.class,
                            "biz_bill_usage_item");

            if (CollectionUtil.isNotEmpty(invoicedCostList)) {
                Date maxEndDate = invoicedCostList.stream()
                        .map(cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost::getUsageEndDate)
                        .max((e1, e2) -> e1.compareTo(e2))
                        .get();
                if ((productInfo.getCurrentSize() > productInfo.getTargetSize()) && maxEndDate.after(new Date())) {
                    throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1151127480));
                }
            }
        }
        ModifyInquiryPriceRequest modifyInquiryPriceRequest = new ModifyInquiryPriceRequest();
        modifyInquiryPriceRequest.setId(product.getId());
        modifyInquiryPriceRequest.setTargetType(product.getResourceType());
        modifyInquiryPriceRequest.setTargetSize(product.getSize());
        modifyInquiryPriceRequest.setTargetSpec(product.getSpec());
        ModifyInquiryPriceVO modifyInquiryPriceVO = inquiryPriceService.modifyInquiryPrice(modifyInquiryPriceRequest);
        productInfo.setPlatformDiscount(modifyInquiryPriceVO.getPlatformDiscount());
        productInfo.setProjectId(modifyInquiryPriceVO.getProjectId());
        productInfo.setPrice(modifyInquiryPriceVO.getPrice());
        productInfo.setServices(
                BeanConvertUtil.convert(modifyInquiryPriceVO.getBillingPrices(), ProductService.class));
        if (BillingConstants.ChargeType.PRE_PAID.equals(productInfo.getChargeType())) {
            productInfo.setFinalCost(modifyInquiryPriceVO.getAmount());
            productInfo.setServiceAmount(modifyInquiryPriceVO.getServiceAmount());
            productInfo.setStartTime(modifyInquiryPriceVO.getComputeDate());
            productInfo.setComputeEndDate(modifyInquiryPriceVO.getComputeEndDate());
            productInfo.setGiveBack(modifyInquiryPriceVO.getGiveBack());
            productInfo.setServiceGiveBack(modifyInquiryPriceVO.getServiceGiveBack());
            //记录负数补差金额
            productInfo.setNegativeAmount(modifyInquiryPriceVO.getNegativeAmount());
            if (0 < BigDecimal.ZERO.compareTo(modifyInquiryPriceVO.getAmount())) {
                BigDecimal invoiceableAmount = account.getTotalRechargeAmount().subtract(account.getInvoicedAmount()).subtract(account.getOfflineInvoicedAmount());
                BigDecimal balance = account.getBalance().compareTo(BigDecimal.ZERO) > 0 ?  account.getBalance() : BigDecimal.ZERO;
                balance = BigDecimalUtil.remainTwoPointAmount(balance);
                invoiceableAmount = invoiceableAmount.subtract(balance);
                BigDecimal unsubAmount = modifyInquiryPriceVO.getAmount().abs();
                if (invoiceableAmount.setScale(3,BigDecimal.ROUND_HALF_UP).compareTo(unsubAmount.setScale(3,BigDecimal.ROUND_HALF_UP)) < 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.UNSUBSCRIBE_AMOUNT_EXCEEDS));
                }
            }
        } else {
            modifyInquiryPriceRequest.setTargetSize(productInfo.getCurrentSize());
            modifyInquiryPriceRequest.setTargetType(StringUtils.isEmpty(productInfo.getCurrentType())?productInfo.getProductCode():productInfo.getCurrentType());
            ModifyInquiryPriceVO modifyInquiryPriceVoOld =
                    inquiryPriceService.modifyInquiryPrice(modifyInquiryPriceRequest);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("resourcePriceBefore", modifyInquiryPriceVoOld.getPrice());
            jsonObject.put("resourcePriceAfter", modifyInquiryPriceVO.getPrice());
            jsonObject.put("orderType", "modify");
            jsonObject.put("resourceId", product.getId());
            HashMap<Object, Object> map = new HashMap<>();
            map.put("resourceChangeRecord", jsonObject.toJSONString());
            productInfo.setOptions(map);
            productInfo.setFinalCost(BigDecimal.ZERO);
        }
        productOperation.setProductInfo(Lists.newArrayList(productInfo));
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
        }
        return orderService.modifyResourceOrder(productOperation);
    }


    /**
     *  EIP变配把当前容量改为变更目标的容量
     * @param productInfo
     */
    private void setLatestEipCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }

        // 获取旧规格
        String resourceId = productInfo.getId();
        SfProductResource productResource = sfProductResourceMapper.selectById(resourceId);
        if (productResource == null || productResource.getClusterId() == null) {
            throw new BizException("资源不存在");
        }

        ResFloatingIp floatingIp = floatingIpRemoteService.selectByPrimaryKey(productResource.getClusterId());
        if (floatingIp == null) {
            throw new BizException("资源实例不存在");
        }


        JSONArray newDesc = new JSONArray();
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("带宽规格");
        configDesc.setValue(productInfo.getTargetSpec() + " | " + productInfo.getTargetSize() + "带宽(Mbps)");
        newDesc.add(configDesc);

        JSONArray oldDesc = new JSONArray();
        ConfigDesc oldConfigDesc = new ConfigDesc();
        oldConfigDesc.setLabel("带宽规格");
        oldConfigDesc.setValue(floatingIp.getInternetServiceProvider() + " | " + floatingIp.getBandWidth() + "带宽(Mbps)");
        oldDesc.add(oldConfigDesc);

        //变更当前的配置
        JSONObject modifyConfigDesc = new JSONObject();
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);

        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        JSONArray configDescJSONArray = jsonObject.getJSONArray("productConfigDesc");
        List<JSONObject> objectList = configDescJSONArray.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("带宽规格".equals(object.getString("label")) || "带宽".equals(object.getString("label"))) {
                object.put("value", productInfo.getTargetSize());
                break;
            }
        }
        jsonObject.put("productConfigDesc", objectList);
        jsonObject.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject.toJSONString());

        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.FLOATING_IP.getProductType());
        data.put("size", productInfo.getTargetSize());
        data.put("bandwidth", productInfo.getTargetSize());
        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
    }


    /**
     * @Description: 云硬盘变配把当前容量改为变更目标的容量
     * <AUTHOR> Created on 2022/7/19
     */
    private void setLatestEbsCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }

        // 获取旧规格
        String resourceId = productInfo.getId();
        SfProductResource productResource = sfProductResourceMapper.selectById(resourceId);
        if (productResource == null || productResource.getClusterId() == null) {
            throw new BizException("资源不存在");
        }

        ResVd resVd = vdRemoteService.selectByPrimaryKey(productResource.getClusterId().toString());
        if (resVd == null) {
            throw new BizException("资源实例不存在");
        }
        if (Objects.equals(Long.valueOf(productInfo.getTargetSize()), resVd.getSize())) {
            throw new BizException("容量未变化，无需扩容");
        }
        if (Long.valueOf(productInfo.getTargetSize()) < resVd.getSize()) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUPPORT_CAPACITY_REDUCTION));
        }

        JSONArray newDesc = new JSONArray();
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("容量(GB)");
        configDesc.setValue( productInfo.getTargetSize() + "");
        newDesc.add(configDesc);

        JSONArray oldDesc = new JSONArray();
        ConfigDesc oldConfigDesc = new ConfigDesc();
        oldConfigDesc.setLabel("容量(GB)");
        oldConfigDesc.setValue( resVd.getSize()  + "");
        oldDesc.add(oldConfigDesc);

        //变更当前的配置
        JSONObject modifyConfigDesc = new JSONObject();
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);

        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        JSONArray configDescJSONArray = jsonObject.getJSONArray("productConfigDesc");
        List<JSONObject> objectList = configDescJSONArray.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("存储大小(GB)".equals(object.getString("label"))) {
                object.put("value", productInfo.getTargetSize());
                break;
            }
        }
        jsonObject.put("productConfigDesc", objectList);
        jsonObject.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject.toJSONString());

        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.DISK.getProductType());
        data.put("size", productInfo.getTargetSize());
        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
    }

    private void setLatestSfsTurboCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }

        // 获取旧规格
        String resourceId = productInfo.getId();
        SfProductResource productResource = sfProductResourceMapper.selectById(resourceId);
        if (productResource == null || productResource.getClusterId() == null) {
            throw new BizException("资源不存在");
        }

        String size = resRenewRefMapper.selectSizeById(productResource.getClusterId());
        if (StrUtil.isBlank(size)) {
            throw new BizException("资源实例不存在");
        }


        JSONArray newDesc = new JSONArray();
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("容量(GB)");
        configDesc.setValue(String.valueOf(productInfo.getTargetSize()));
        newDesc.add(configDesc);

        JSONArray oldDesc = new JSONArray();
        ConfigDesc oldConfigDesc = new ConfigDesc();
        oldConfigDesc.setLabel("容量(GB)");
        oldConfigDesc.setValue(size);
        oldDesc.add(oldConfigDesc);

        //变更当前的配置
        JSONObject modifyConfigDesc = new JSONObject();
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);

        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        JSONArray configDescJSONArray = jsonObject.getJSONArray("productConfigDesc");
        List<JSONObject> objectList = configDescJSONArray.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("存储大小(GB)".equals(object.getString("label")) || "容量(GB)".equals(object.getString("label"))) {
                object.put("value", productInfo.getTargetSize());
                break;
            }

            if (object.containsKey("resInfo")) {
                JSONObject resInfo = object.getJSONObject("resInfo");
                if (resInfo.containsKey("size")) {
                    JSONObject resInfoSize = resInfo.getJSONObject("size");
                    resInfoSize.put("value", productInfo.getTargetSize());
                    resInfo.put("size", size);
                }
                object.put("resInfo", resInfo);
            }
        }
        jsonObject.put("productConfigDesc", objectList);
        jsonObject.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject.toJSONString());
        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.SFS_TURBO.getProductType());
        data.put("size", productInfo.getTargetSize());

        try {
            JSONObject productConfigDesc = serviceConfig.getJSONObject("productConfigDesc");
            String currentConfigDesc = productConfigDesc.getString("currentConfigDesc");
            JSONObject currentConfig = JSONObject.parseObject(currentConfigDesc);
            currentConfig.getJSONArray("productConfigDesc").forEach(item -> {
                JSONObject json = (JSONObject) item;
                if ("容量(GB)".equals(json.getString("label")) || "存储大小(GB)".equals(json.getString("label"))) {
                    json.put("value", productInfo.getTargetSize());
                }
            });
            productConfigDesc.put("currentConfigDesc", currentConfig.toJSONString());
        } catch (Exception e) {
            log.warn("获取productConfigDesc失败", e);
        }

        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
    }

    private void setLatestRdsCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }

        // 获取旧规格
        String resourceId = productInfo.getId();
        SfProductResource productResource = sfProductResourceMapper.selectById(resourceId);
        if (productResource == null || productResource.getClusterId() == null) {
            throw new BizException("资源不存在");
        }

        String size = resRenewRefMapper.selectRdsSizeById(productResource.getClusterId());
        if (StrUtil.isBlank(size)) {
            throw new BizException("资源实例不存在");
        }


        JSONArray newDesc = new JSONArray();
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("容量(GB)");
        configDesc.setValue(String.valueOf(productInfo.getTargetSize()));
        newDesc.add(configDesc);

        JSONArray oldDesc = new JSONArray();
        ConfigDesc oldConfigDesc = new ConfigDesc();
        oldConfigDesc.setLabel("容量(GB)");
        oldConfigDesc.setValue(size);
        oldDesc.add(oldConfigDesc);

        //变更当前的配置
        JSONObject modifyConfigDesc = new JSONObject();
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);

        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        JSONArray configDescJSONArray = jsonObject.getJSONArray("productConfigDesc");
        List<JSONObject> objectList = configDescJSONArray.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("存储大小(GB)".equals(object.getString("label"))
                    || "容量(GB)".equals(object.getString("label"))
                    || "存储空间(GB)".equals(object.getString("label"))) {
                object.put("value", productInfo.getTargetSize());
                break;
            }

            if (object.containsKey("resInfo")) {
                JSONObject resInfo = object.getJSONObject("resInfo");
                if (resInfo.containsKey("size")) {
                    JSONObject resInfoSize = resInfo.getJSONObject("size");
                    resInfoSize.put("value", productInfo.getTargetSize());
                    resInfo.put("size", size);
                }
                object.put("resInfo", resInfo);
            }
        }
        jsonObject.put("productConfigDesc", objectList);
        jsonObject.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject.toJSONString());
        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.DISK.getProductType());
        data.put("size", productInfo.getTargetSize());
        try {
            JSONObject productConfigDesc = serviceConfig.getJSONObject("productConfigDesc");
            String currentConfigDesc = productConfigDesc.getString("currentConfigDesc");
            JSONObject currentConfig = JSONObject.parseObject(currentConfigDesc);
            currentConfig.getJSONArray("productConfigDesc").forEach(item -> {
                JSONObject json = (JSONObject) item;
                if ("容量(GB)".equals(json.getString("label")) || "存储大小(GB)".equals(json.getString("label"))
                        || "存储空间(GB)".equals(json.getString("label"))) {
                    json.put("value", productInfo.getTargetSize());
                }
            });
            productConfigDesc.put("currentConfigDesc", currentConfig.toJSONString());
        } catch (Exception e) {
            log.warn("获取productConfigDesc失败", e);
        }
        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
    }
    /**
     * @Description: SFS2.0变配把当前容量改为变更目标的容量
     * <AUTHOR> Created on 2022/7/19
     */
    private void setLatestCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }
        com.alibaba.fastjson.JSONArray currentDesc;
        com.alibaba.fastjson.JSONArray changeDesc = null;
        if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.containsIgnoreCase(configStr, "currentDesc")) {
            com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(configStr);
            currentDesc = jsonObject1.getJSONArray("currentDesc");
            //如果有变更信息，记录下来
            if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.containsIgnoreCase(configStr, "changeDesc")) {
                changeDesc = jsonObject1.getJSONArray("changeDesc");
            }
        } else {
            currentDesc = com.alibaba.fastjson.JSONObject.parseArray(configStr);
        }
        for (int i = 0; i < currentDesc.size(); i++) {
            Object o = currentDesc.get(i);
            String str = o.toString();
            //替换变更目标的容量
            if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.containsIgnoreCase(str, "容量")) {
                currentDesc.remove(o);
                ConfigDesc configDesc = new ConfigDesc();
                configDesc.setLabel("容量(GB)");
                configDesc.setValue(new BigDecimal(productInfo.getTargetSize()).setScale(2).toString());
                currentDesc.add(configDesc);
            }
        }
        //更新当前的容量
        if (Objects.isNull(changeDesc)) {
            productInfo.setProductConfigDesc(currentDesc.toString());
        } else {
            cn.hutool.json.JSONObject newJsonObject = new cn.hutool.json.JSONObject();
            newJsonObject.put("currentDesc", currentDesc);
            newJsonObject.put("changeDesc", changeDesc);
            productInfo.setProductConfigDesc(newJsonObject.toString());
        }

    }

    /**
     * dcs变配把当前配置改为变更目标的配置
     */
    private void setLatestDcsCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }
        log.info("获取规格{}属性配置", productInfo.getTargetSpec());
        List<ResSpecTypeAttr> resSpecTypeAttrs = resVmTypeRemoteService.selectSpecTypeAttrByVmType(
                productInfo.getTargetSpec());
        if (CollUtil.isEmpty(resSpecTypeAttrs)) {
            log.error("未获取到实例规格配置:{}", productInfo.getTargetSize());
            return;
        }

        Map<String, String> map = new HashMap<>();
        map.put("spec",productInfo.getTargetSpec());
        for (ResSpecTypeAttr resSpecTypeAttr : resSpecTypeAttrs) {
            if (map.containsKey(resSpecTypeAttr.getAttrCode())) {
                log.warn("规格属性存在多个:{}", resSpecTypeAttr.getAttrCode());
            }
            map.put(resSpecTypeAttr.getAttrCode(), resSpecTypeAttr.getAttrValue());
        }

        String f = "{spec} | 规格:{capacity} GB | 分片数：{shardingNum} | 最大可用内存：{maxMemory} GB | 最大连接数(默认/可配)：{maxClients}/{maxConnections} | 基准/最大带宽：{maxBandwidth} Mbit/s | DB数：{dbNumber} | 占用IP个数：{tenantIpCount}";
        String format = StrUtil.format(f, map);

        JSONArray oldDesc = new JSONArray();
        JSONArray newDesc = new JSONArray();
        JSONObject modifyConfigDesc = new JSONObject();
        com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        com.alibaba.fastjson.JSONArray currentDesc = jsonObject1.getJSONArray("productConfigDesc");
        for (Object o : currentDesc) {
            String str = o.toString();
            // 获取新旧配置
            if (StringUtil.containsIgnoreCase(str, "实例规格")) {
                oldDesc.add(BeanConvertUtil.convert(str, ConfigDesc.class));
                ConfigDesc configDesc = new ConfigDesc();
                configDesc.setLabel("实例规格");
                configDesc.setValue(format);
                newDesc.add(configDesc);
            }
        }

        List<JSONObject> objectList = currentDesc.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("实例规格".equals(object.getString("label"))) {
                object.put("value", format);
                break;
            }
        }
        //变更当前的配置
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);
        jsonObject1.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject1.toJSONString());
        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.DCS.getProductType());
        data.put("category", productInfo.getTargetSize());
        data.put("spec", productInfo.getTargetSize());
        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
        log.info("dcs变更配置信息");
    }

    /**
     * ECS变配把当前配置改为变更目标的配置
     */
    private void setLatestEcsCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }
        List<ResSpecTypeAttr> resSpecTypeAttrs = resVmTypeRemoteService.selectSpecTypeAttrByVmType(
                productInfo.getTargetSpec());
        String cpu = null;
        String memory = null;
        for (ResSpecTypeAttr attr : resSpecTypeAttrs) {
            if ("cpu".equals(attr.getAttrCode())) {
                cpu = attr.getAttrValue();
                continue;
            }
            if ("memory".equals(attr.getAttrCode())) {
                memory = attr.getAttrValue();
            }
        }
        if (StringUtils.isEmpty(memory) || StringUtils.isEmpty(cpu)) {
            return;
        }
        JSONArray oldDesc = new JSONArray();
        JSONArray newDesc = new JSONArray();
        JSONObject modifyConfigDesc = new JSONObject();
        com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        com.alibaba.fastjson.JSONArray currentDesc = jsonObject1.getJSONArray("productConfigDesc");
        for (Object o : currentDesc) {
            String str = o.toString();
            // 获取新旧配置
            if (StringUtil.containsIgnoreCase(str, "主机规格")) {
                oldDesc.add(BeanConvertUtil.convert(str, ConfigDesc.class));
                ConfigDesc configDesc = new ConfigDesc();
                configDesc.setLabel("主机规格");
                configDesc.setValue(
                        productInfo.getTargetSpec() + " | " + cpu + "vCpu | " + memory + "GB");
                newDesc.add(configDesc);
            }
        }
        List<JSONObject> objectList = currentDesc.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("主机规格".equals(object.getString("label"))) {
                object.put("value", productInfo.getTargetSpec() + " | " + cpu + "vCpu | " + memory + "GB");
                break;
            }
        }
        //变更当前的配置
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);
        jsonObject1.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject1.toJSONString());
        JSONObject serviceConfig = JSON.parseObject(productInfo.getConfig());
        JSONObject data = serviceConfig.getJSONObject("data").getJSONObject(ProductCodeEnum.ECS.getProductType());
        data.put("category", productInfo.getTargetSize());
        data.put("spec", productInfo.getTargetSize());
        productInfo.setConfig(JSONUtil.toJsonStr(serviceConfig));
    }

    /**
     * ECS变配把当前配置改为变更目标的配置
     */
    private void setLatestCceCapacity(ProductInfo productInfo) {
        String configStr = productInfo.getProductConfigDesc();
        if (cn.com.cloudstar.rightcloud.bss.common.util.StringUtil.isEmpty(configStr)) {
            return;
        }

        // 获取旧规格
        String resourceId = productInfo.getId();
        SfProductResource productResource = sfProductResourceMapper.selectById(resourceId);
        if (productResource == null || productResource.getClusterId() == null) {
            throw new BizException("资源不存在");
        }

        ResContainerCluster containerCluster = cceRemoteService.selectByPrimaryKey(productResource.getClusterId());
        if (containerCluster == null) {
            throw new BizException("资源实例不存在");
        }


        JSONArray newDesc = new JSONArray();
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("节点数量");
        configDesc.setValue(productInfo.getTargetSize() + "");
        newDesc.add(configDesc);

        JSONArray oldDesc = new JSONArray();
        ConfigDesc oldConfigDesc = new ConfigDesc();
        oldConfigDesc.setLabel("节点数量");
        oldConfigDesc.setValue(containerCluster.getSize() + "");
        oldDesc.add(oldConfigDesc);

        //变更当前的配置
        JSONObject modifyConfigDesc = new JSONObject();
        modifyConfigDesc.put("oldDesc", oldDesc);
        modifyConfigDesc.put("newDesc", newDesc);

        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(configStr);
        JSONArray array = jsonObject.getJSONArray("productConfigDesc");
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            String label = object.getString("label");
            if ("集群规模".equals(label)) {
                object.put("value", productInfo.getTargetSize() + "节点");
            }
        }

        jsonObject.put("modifyConfigDesc", modifyConfigDesc);
        productInfo.setProductConfigDesc(jsonObject.toJSONString());
    }


    private RestResult checkNonBillingProduct(List<ServiceOrderPriceDetail> orderPriceDetails, ProductOperation productOperation) {
        ServiceOrderPriceDetail lastOrderDetail = CollectionUtil.getLast(orderPriceDetails);
        //查询是否关联不计费产品
        IsNonBillProductRequest request = new IsNonBillProductRequest();
        request.setSfServiceId(Long.valueOf(lastOrderDetail.getServiceId()));
        request.setChargeType(lastOrderDetail.getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        if (isNonBillProduct) {
            productOperation.setChargingType(BillingConstants.ChargingType.SALE_TYPE);
        } else {
            productOperation.setChargingType(BillingConstants.ChargingType.NORMAL_TYPE);
        }
        String chargingType = lastOrderDetail.getChargingType();
        //销售计费两种情况,1、不计费产品中包含该产品的场合,正常续订；2、不计费产品中不包含该产品的场合，不能续订产品
        //正常计费两种情况,1、不计费产品中包含该产品的场合,不能续订产品；2、不计费产品中不包含该产品的场合，正常续订
        if (Objects.nonNull(chargingType) && BillingConstants.ChargingType.SALE_TYPE.equals(chargingType)) {
            if (isNonBillProduct) {
                return new RestResult(true);
            } else {
                return new RestResult(RestResult.Status.FAILURE,
                                      WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            }
        } else {
            if (isNonBillProduct) {
                return new RestResult(RestResult.Status.FAILURE,
                                      WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.CHARGINGTYPE_CHANGE_RELEASE_PRODUCT));
            } else {
                return new RestResult(true);
            }
        }
    }


    private int getSFSUsed() {
        List<Long> allSFSIds = sfProductResourceMapper.getAllSFSIds();
        log.info("共享资源池SFS:[{}]", allSFSIds);
        // SFS2.0查询当前使用总量
        ResShareParams resShareParams = new ResShareParams();
        resShareParams.setIgnoreDataFilter(true);
        resShareParams.setStatusNotIn(Lists.newArrayList(ShareStatus.ERROR, ShareStatus.DELETED));
        resShareParams.setChargeType("PrePaid");
        List<ResShare> shareList = shareRemoteService.getShareList(resShareParams);

        allSFSIds.addAll(shareList.stream().map(ResShare::getId).collect(Collectors.toList()));
        BigDecimal all = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(allSFSIds)) {
            all = shareRemoteService.sumSizeByIds(allSFSIds);
        }
        return all.setScale(0, BigDecimal.ROUND_CEILING).intValue();
    }


    @Override
    public List<ServiceOrderVo> selectCloudProductInfo(List<Long> accountIds) {
        return serviceOrderMapper.selectCloudProductInfo(accountIds);
    }

    @Override
    public List<ServiceOrderVo> selectCloudProductTotal() {
        return serviceOrderMapper.selectCloudProductTotal();
    }

    @Override
    public List<MarketCatalogVO> selectProductCatalogWithService() {
        return serviceOrderMapper.selectProductCatalogWithService();
    }

    @Override
    public List<ServiceOrderVo> checkDuplicateNames(Criteria criteria) {
        return serviceOrderMapper.selectServiceOrderAndDetail(criteria);
    }

    @Override
    public List<ServiceOrderDetail> selectDetailByResId(String id) {

        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return serviceOrderMapper.selectDetailByResId(id);
    }

    /**
     * 修改节点状态
     *
     * @Param: request
     * @Author: wangcheng
     * @date: 2022/5/7
     */
    @Override
    public void updateNodeStatus(OperateNodeRequest request) {
        if (CollectionUtil.isEmpty(request.getComputeList()) && CollectionUtil.isEmpty(request.getManagerList())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1456492326));
        }
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(request.getClusterId());
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo.getParentSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_500058829));
        }
        if (!Objects.equals(resHpcCluster.getOrgSid(), authUserInfo.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        Long clusterId = request.getClusterId();
        this.checkRemovingNode(clusterId);

//        boolean b = hpcRemoteService.operateNode(clusterId, request.getComputeList(),
//                                                 request.getManagerList(), "OpenOrClosed", false);
    }

    @Override
    @Transactional
    @GlobalTransactional
    public void deleteNode(OperateNodeRequest request) {
        if (CollectionUtil.isEmpty(request.getComputeList()) && CollectionUtil.isEmpty(request.getManagerList())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1456492326));
        }

        Long clusterId = request.getClusterId();
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        if (resHpcClusterRemoteModule == null || !ResHpcClusterStatus.AVALIABLE.equals(
                resHpcClusterRemoteModule.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //检查节点删除状态
        this.checkRemovingNode(clusterId);
        // 删除节点数量校验
        //  this.checkDeleteNodeNum(getNodeInfos(clusterId), request.getComputeList(), request.getManagerList());

        User authUser = AuthUtil.getAuthUser();
        if (authUser != null) {

            Long userSid = authUser.getUserSid();

            List<Long> roleIdList = sysUserService.selectUserRole(userSid);
            //运营管理角色
            if (roleIdList.contains(BssRoleEnum.OPERATION_ADMIN.getRoleSid())) {
                //处理过期合同
                // this.handlingExpiredContracts(clusterId,request.getComputeList(),
                //         request.getManagerList(),request.getGraphicList());
                ServiceOrder serviceOrder = this.lambdaQuery()
                                                .eq(ServiceOrder::getClusterId, clusterId)
                                                .list()
                                                .stream()
                                                .findFirst()
                                                .get();
                if (serviceOrder != null) {
                    //查询集群扩容相关的合同
                    List<Long> contractIdList =
                            this.lambdaQuery()
                                .in(ServiceOrder::getType, Arrays.asList(OrderType.UPGRADE, OrderType.UPGRADE_RENEW))
                                .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                .eq(ServiceOrder::getClusterId, serviceOrder.getClusterId())
                                .list()
                                .stream()
                                .filter(order -> {
                                    return this.serviceOrderDetailService.lambdaQuery()
                                                                         .eq(ServiceOrderDetail::getOrderId,
                                                                             order.getId())
                                                                         .list().stream()
                                                                         .anyMatch(
                                                                                 orderDetail -> ProductCodeEnum.HPC_DRP.getProductType()
                                                                                                                       .equals(orderDetail.getServiceType()))
                                            && Objects.nonNull(order.getContractId());
                                })
                                .map(order -> Long.valueOf(order.getContractId()))
                                .collect(Collectors.toList());
                    List<HpcBizContractDTO> hpcBizContractList =
                            bizContractService.queryHpcContractTreeByIdList(contractIdList,
                                                                            Arrays.asList(ContractStatus.EXPIRED));
                    hpcBizContractList =
                            hpcBizContractList.stream()
                                              .filter(hpcBizContractDTO -> !hpcBizContractDTO.isZeroNode())
                                              .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(hpcBizContractList)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_561800932));
                    }
                    //调用
                    List<NodeInfo> computeNodeInfoList = convertToNodeList(clusterId, request.getComputeList());
                    List<NodeInfo> managerNodeInfoList = convertToNodeList(clusterId, request.getManagerList());
                    List<NodeInfo> graphicNodeInfoList = convertToNodeList(clusterId, request.getGraphicList());
                    //修改合同节点数
                    if (CollectionUtil.isNotEmpty(computeNodeInfoList)) {
                        changeContractNodeNum(computeNodeInfoList, hpcBizContractList, 0);
                    }
                    List<NodeInfo> cliNodeList = managerNodeInfoList.stream()
                                                                    .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                                            nodeInfo.getHpcPointType()))
                                                                    .collect(Collectors.toList());
                    //修改合同节点数
                    if (CollectionUtil.isNotEmpty(cliNodeList)) {
                        changeContractNodeNum(cliNodeList, hpcBizContractList, 1);
                    }
                    List<NodeInfo> vncNodeList = managerNodeInfoList.stream()
                                                                    .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(
                                                                            nodeInfo.getHpcPointType()))
                                                                    .collect(Collectors.toList());
                    graphicNodeInfoList.addAll(vncNodeList);

                    if (CollectionUtil.isNotEmpty(graphicNodeInfoList)) {
                        changeContractNodeNum(graphicNodeInfoList, hpcBizContractList, 2);
                    }

            //        boolean b = hpcRemoteService.operateNode(clusterId, request.getComputeList(),
             //                                                request.getManagerList(), "Remove", true);
              //      log.info("hpcRemoteService.operateNode result=[{}]", b);
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            } else {
                Long orgSid = authUser.getOrgSid();
                if (!resHpcClusterRemoteModule.getOrgSid().equals(orgSid)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                //判断用户缩容最大限制
                this.checkMaxLimit(orgSid, clusterId);
                request.setOrgSid(orgSid);
                //生成缩容订单
                ServiceOrder degradeServiceOrder = this.createDegradeOrder(request);
                //查询现金余额
                BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(
                        degradeServiceOrder.getEntityId(), authUser.getUserSid());
                if (Objects.nonNull(authUser.getParentSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                if (Objects.isNull(account)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
                }
                if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1095922662));
                }
                if (BillingAccountStatus.FREEZE.equals(account.getStatus())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_568557965));
                }
                try {
                    long traceId = TraceUtil.tracingInstance().currentTraceContext().get().traceId();
                    String cacheKey = TraceUtil.TRACE_ID_KEY + ProductCodeEnum.HPC_DRP.getProductType() + StrUtil.COLON
                            + resHpcClusterRemoteModule.getId();
                    JedisUtil.INSTANCE.set(cacheKey, String.valueOf(traceId));
                } catch (Exception e) {
                    log.info("设置当前链路traceId-ServiceOrderServiceImpl.deleteNode-失败:[{}]", e.getMessage());
                }

                //保存变更记录
                ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
                //设置时间
                getTime(degradeServiceOrder, resChangeRecordDTO);
                //获取配置
          //      String nodeInfo = hpcRemoteService.getNodeInfo(clusterId);
         //       resChangeRecordDTO.setConfigDesc(nodeInfo);
                saveResChangeRecord(request, resChangeRecordDTO);

                //调用删除
           //     boolean b = hpcRemoteService.operateNode(clusterId, request.getComputeList(),
           //                                              request.getManagerList(), "Remove", false);
           //     log.info("HPC删除节点-hpcRemoteService-operateNode-OUTPUT result=[{}]", b);
                resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
                //保存变更记录
                // saveResChangeRecord(request,resChangeRecordDTO);

                //发站消息
                // try {
                //     this.sendHpcDrpCompletedMessage(degradeServiceOrder, request);
                //
                // }catch (Exception e){
                //     log.error("发送信息异常：",e);
                // }
            }
        }

    }

    private void getTime(ServiceOrder degradeServiceOrder, ResChangeRecordDTO resChangeRecordDTO) {
        List<ServiceOrderDetail> applyServiceOrderDetailList =
                serviceOrderDetailService.lambdaQuery().eq(ServiceOrderDetail::getOrderId, degradeServiceOrder.getId())
                                         .notIn(ServiceOrderDetail::getServiceType, "HPC-DRP").list();
        Optional<ServiceOrderDetail> endOptional = applyServiceOrderDetailList.stream().max(Comparator.comparing(ServiceOrderDetail::getEndTime));
        Date endTime = null;
        if (endOptional.isPresent()) {
            endTime = endOptional.get().getEndTime();
        }

        Optional<ServiceOrderDetail> startOptional = applyServiceOrderDetailList.stream().min(Comparator.comparing(ServiceOrderDetail::getStartTime));
        Date startTime = null;
        if (startOptional.isPresent()) {
            startTime = startOptional.get().getStartTime();
        }

        resChangeRecordDTO.setChangeEndTime(endTime);
        resChangeRecordDTO.setChangeStartTime(startTime);
    }

    private void checkRemovingNode(Long clusterId) {
        List<NodeInfo> nodeInfos = getNodeInfos(clusterId);
        boolean removingNodeExist = nodeInfos.stream()
                                             .anyMatch(nodeInfo -> ResHpcClusterNodeStatus.REMOVING.equals(
                                                     nodeInfo.getStatus()));
        if (removingNodeExist) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1034666260));
        }
    }


    /**
     * hpc专属资源池扩缩容完成发送信息
     *
     * @param serviceOrder
     * @param request
     */
    private void sendHpcDrpCompletedMessage(ServiceOrder serviceOrder, OperateNodeRequest request) {
        String ownerId = serviceOrder.getOwnerId();

        Long clusterId = request.getClusterId();
        List<NodeInfo> managerNodeInfoList = convertToNodeList(clusterId, request.getManagerList());
        List<NodeInfo> cliNodeList = managerNodeInfoList.stream()
                                                        .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                                nodeInfo.getHpcPointType()))
                                                        .collect(Collectors.toList());
        List<NodeInfo> vncNodeList = managerNodeInfoList.stream()
                                                        .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(
                                                                nodeInfo.getHpcPointType()))
                                                        .collect(Collectors.toList());
        Map<String, String> nodeMap = new HashMap<>();
        List<String> managerList = Optional.ofNullable(request.getManagerList()).orElse(new ArrayList<>());
        List<String> computeList = Optional.ofNullable(request.getComputeList()).orElse(new ArrayList<>());
        nodeMap.put("managerList", String.valueOf(managerList.size()));
        nodeMap.put("cliNodeList", String.valueOf(cliNodeList.size()));
        nodeMap.put("vncNodeList", String.valueOf(vncNodeList.size()));
        nodeMap.put("computeList", String.valueOf(computeList.size()));

        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserService.lambdaQuery()
                                                                                     .eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getUserSid,
                                                                                         ownerId)
                                                                                     .one();
        //缩容完成向租户发送消息
        Map<String, String> sendAddress = new HashMap<>();
        sendAddress.put("email", user.getEmail());
        sendAddress.put("mobile", user.getMobile());
        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("userAccount", user.getAccount());
        messageContent.put("orderSn", serviceOrder.getOrderSn());
        messageContent.put("productName", serviceOrder.getProductName());
        messageContent.put("poolName", serviceOrder.getName());
        messageContent.put("entityId", serviceOrder.getEntityId().toString());
        messageContent.putAll(nodeMap);

        this.assembleBaseMessageContent(messageContent);
        SendNotifyRequest sendNotifyRequest = new SendNotifyRequest();
        sendNotifyRequest.setSendAddress(sendAddress);
        sendNotifyRequest.setMessageType(Arrays.asList(0, 1, 2));
        sendNotifyRequest.setIsCC(false);
        sendNotifyRequest.setMessageContent(messageContent);
        sendNotifyRequest.setMessageId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
        mailService.sendNotify(sendNotifyRequest);

        List<User> adminstrators = userMapper.findAdminstratorsByEntityId(
                serviceOrder.getEntityId());
        List<Long> roleSid = adminstrators.stream().map(User::getRoleSId).distinct().collect(Collectors.toList());

        //给运营管理员发信息
        SendNotifyRequest sendAdminNotifyRequest = new SendNotifyRequest();
        sendAdminNotifyRequest.setRoleIds(roleSid);
        sendAdminNotifyRequest.setMessageType(Arrays.asList(0, 1, 2));
        sendAdminNotifyRequest.setIsCC(false);
        Map<String, String> adminMessageContent = new HashMap<>();
        adminMessageContent.put("userAccount", user.getAccount());
        adminMessageContent.put("productName", serviceOrder.getProductName());
        adminMessageContent.put("orderSn", serviceOrder.getOrderSn());
        adminMessageContent.put("poolName", serviceOrder.getName());
        adminMessageContent.put("entityId", serviceOrder.getEntityId().toString());
        adminMessageContent.putAll(nodeMap);
        this.assembleBaseMessageContent(adminMessageContent);
        sendAdminNotifyRequest.setSendAddress(new HashMap<>());
        sendAdminNotifyRequest.setMessageContent(adminMessageContent);
        sendAdminNotifyRequest.setMessageId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_SCALE_DOWN);
        mailService.sendNotify(sendAdminNotifyRequest);
    }

    private void assembleBaseMessageContent(Map<String, String> messageContent) {
        String companyName = PropertiesUtil.getProperty("company.name");
        String consoleUrl = PropertiesUtil.getProperty("rightcloud.console.url");
        String portalUrl = PropertiesUtil.getProperty("rightcloud.portal.url");
        messageContent.put("consoleUrl", consoleUrl);
        messageContent.put("portalUrl", portalUrl);
        messageContent.put("companyName", companyName);
        messageContent.put("logoUrl", consoleUrl + PropertiesUtil.getProperty("platform.large.logo"));
        messageContent.put("systemName", PropertiesUtil.getProperty("system.name"));
        messageContent.put("companyPhone", PropertiesUtil.getProperty("system.contact.number"));
    }

    /**
     * 检查最大缩容次数
     *
     * @param orgSid
     */
    private void checkMaxLimit(Long orgSid, Long clusterId) {
        String maxCount = PropertiesUtil.getProperty(HPC_SHRINKAGE_MAX_COUNT);
        if (StringUtils.isNotEmpty(maxCount)) {

            Date oneMonthBefore = DateUtils.addMonths(new Date(), -1);
            long count = serviceOrderService.lambdaQuery()
                                            .eq(ServiceOrder::getOrgSid, orgSid)
                                            .eq(ServiceOrder::getType, OrderType.DEGRADE)
                                            .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                            .eq(ServiceOrder::getClusterId, clusterId)
                                            .ge(ServiceOrder::getCreatedDt, oneMonthBefore)
                                            .list().stream().filter(order -> {
                        return this.serviceOrderDetailService.lambdaQuery()
                                                             .eq(ServiceOrderDetail::getOrderId, order.getId())
                                                             .list()
                                                             .stream()
                                                             .anyMatch(
                                                                     orderDetail -> ProductCodeEnum.HPC_DRP.getProductType()
                                                                                                           .equals(orderDetail.getServiceType()));
                    }).count();

            if (count >= Long.valueOf(maxCount)) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_126506193), maxCount));
            }
        }
    }

    private boolean checkMax(Long orgSid) {
        return false;
    }

    private ResChangeRecordDTO saveResChangeRecord(OperateNodeRequest request,
                                                   ResChangeRecordDTO resChangeRecordDTO) {
        Long clusterId = request.getClusterId();
        List<NodeInfo> nodeInfoList = getNodeInfos(clusterId);
        nodeInfoList.removeIf(nodeInfo -> ResHpcClusterNodeStatus.REMOVING.equals(nodeInfo.getStatus()));
        Date curr = new Date();
        resChangeRecordDTO.setChangeType(ResChangeTypeEnum.DEGRADE.getCode());
        if (StringUtils.isEmpty(resChangeRecordDTO.getResourceId())) {
            ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
            //插入变更记录
            resChangeRecordDTO.setCloudEnvId(resHpcCluster.getCloudEnvId());
            resChangeRecordDTO.setResourceId(resHpcCluster.getId().toString());
            resChangeRecordDTO.setInstanceId(resHpcCluster.getResourceId());
            resChangeRecordDTO.setResType("HPC-DRP");
            long nodeNum = nodeInfoList.stream()
                                       .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(
                                               resource.getNodeType()))
                                       .count();
            resChangeRecordDTO.setOriginalType(String.valueOf(nodeNum));
            resChangeRecordDTO.setCreatedDt(curr);
            resChangeRecordDTO.setCreatedBy(resHpcCluster.getCreatedBy());
            resChangeRecordDTO.setUpdatedDt(curr);
            resChangeRecordDTO.setUpdatedBy(resHpcCluster.getCreatedBy());
            resChangeRecordDTO.setOrgSid(resHpcCluster.getOrgSid());
            resChangeRecordDTO.setOwnerId(resHpcCluster.getOwnerId());
            String oldExtrajson = getExtraJson(nodeInfoList);
            resChangeRecordDTO.setOriginalExtra(oldExtrajson);
        } else {
            long nodeNum = nodeInfoList.stream()
                                       .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(
                                               resource.getNodeType()))
                                       .count();
            resChangeRecordDTO.setNewType(String.valueOf(nodeNum));
            resChangeRecordDTO.setNewExtra(getExtraJson(nodeInfoList));
            resChangeRecordDTO.setUpdatedDt(curr);
        }
        return resChangeRecordDTO;
    }

    private List<NodeInfo> getNodeInfos(Long clusterId) {
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(bmsInfoList)) {
            nodeInfoList.addAll(bmsInfoList);
        }
        List<ResVmNodeInfo> resInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resInfoList)) {
            nodeInfoList.addAll(resInfoList);
        }
        return nodeInfoList;
    }

    /**
     * 变更资源json
     *
     * @param
     */
    private String getExtraJson(List<NodeInfo> nodeInfoList) {
        long vncNum = nodeInfoList.stream()
                                  .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(
                                          resource.getNodeType())
                                          && HpcPointType.VNC.equals(resource.getHpcPointType()))
                                  .count();
        long cliNum = nodeInfoList.stream()
                                  .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(
                                          resource.getNodeType())
                                          && HpcPointType.CCS_CLI.equals(resource.getHpcPointType()))
                                  .count();
        long computeNum = nodeInfoList.stream()
                                      .filter(resource -> "compute".equals(resource.getNodeType()))
                                      .count();
        Map<String, Long> nodeMap = new HashMap<>();
        nodeMap.put("计算节点数", computeNum);
        nodeMap.put("登录节点数", cliNum);
        nodeMap.put("VNC节点数", vncNum);
        return JSON.toJSONString(nodeMap);
    }


    private void handlingExpiredContracts(Long clusterId, List<String> computeList, List<String> managerList,
                                          List<String> graphicList) {
        ServiceOrder serviceOrder = this.lambdaQuery()
                                        .eq(ServiceOrder::getClusterId, clusterId)
                                        .list()
                                        .stream()
                                        .findFirst()
                                        .get();
        if (serviceOrder != null) {
            //查询集群扩容相关的合同
            List<Long> contractIdList = this.lambdaQuery()
                                            .in(ServiceOrder::getType,
                                                Arrays.asList(OrderType.UPGRADE, OrderType.UPGRADE_RENEW))
                                            .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                            .eq(ServiceOrder::getClusterId, serviceOrder.getClusterId())
                                            .list()
                                            .stream()
                                            .filter(order -> {
                                                return this.serviceOrderDetailService.lambdaQuery()
                                                                                     .eq(ServiceOrderDetail::getOrderId,
                                                                                         order.getId())
                                                                                     .list()
                                                                                     .stream()
                                                                                     .anyMatch(
                                                                                             orderDetail -> ProductCodeEnum.HPC_DRP.getProductType()
                                                                                                                                   .equals(orderDetail.getServiceType()))
                                                        && Objects.nonNull(order.getContractId());
                                            })
                                            .map(order -> Long.valueOf(order.getContractId()))
                                            .collect(Collectors.toList());

            //查询过期合同
            List<HpcBizContractDTO> hpcBizContractList = bizContractService.queryHpcContractTreeByIdList(contractIdList,
                                                                                                         Arrays.asList(
                                                                                                                 ContractStatus.EXPIRED));
            hpcBizContractList = hpcBizContractList.stream()
                                                   .filter(hpcBizContractDTO -> !hpcBizContractDTO.isZeroNode())
                                                   .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(hpcBizContractList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_561800932));
            }
            List<NodeInfo> computeNodeInfoList = convertToNodeList(clusterId, computeList);
            List<NodeInfo> managerNodeInfoList = convertToNodeList(clusterId, managerList);
            List<NodeInfo> graphicNodeInfoList = convertToNodeList(clusterId, graphicList);
            //修改合同节点数
            if (CollectionUtil.isNotEmpty(computeNodeInfoList)) {
                changeContractNodeNum(computeNodeInfoList, hpcBizContractList, 0);
            }
            List<NodeInfo> cliNodeList = managerNodeInfoList.stream()
                                                            .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                                    nodeInfo.getHpcPointType()))
                                                            .collect(Collectors.toList());
            //修改合同节点数
            if (CollectionUtil.isNotEmpty(cliNodeList)) {
                changeContractNodeNum(cliNodeList, hpcBizContractList, 1);
            }
            List<NodeInfo> vncNodeList = managerNodeInfoList.stream()
                                                            .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(
                                                                    nodeInfo.getHpcPointType()))
                                                            .collect(Collectors.toList());
            graphicNodeInfoList.addAll(vncNodeList);

            if (CollectionUtil.isNotEmpty(graphicNodeInfoList)) {
                changeContractNodeNum(graphicNodeInfoList, hpcBizContractList, 2);
            }
            //更新修改的合同
            List<BizContractDetail> bizContractDetailList = hpcBizContractList.stream()
                                                                              .filter(dto -> dto.isModifyNodeFlag())
                                                                              .map(HpcBizContractDTO::getContractDetail)
                                                                              .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(bizContractDetailList)) {
                bizContractDetailService.saveOrUpdateBatch(bizContractDetailList);
            }
        }
    }

    /**
     * 修改合同节点数
     *
     * @param nodeInfoList
     * @param hpcBizContractList
     * @param type
     */
    private void changeContractNodeNum(List<NodeInfo> nodeInfoList, List<HpcBizContractDTO> hpcBizContractList,
                                       int type) {
        Long openContractId = null;

        int allContractNode = this.getAllContractNode(hpcBizContractList, type);
        if (nodeInfoList.size() > allContractNode) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1375795666));
        }
        for (NodeInfo nodeInfo : nodeInfoList) {
            if (CollectionUtil.isNotEmpty(hpcBizContractList)) {
                for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractList) {
                    Integer remNodeNum = hpcBizContractDTO.getRemNodeNum(type);
                    if (remNodeNum <= 0) {
                        continue;
                    }
                    //
                    if (openContractId != null) {
                        if (!openContractId.equals(hpcBizContractDTO.getHpcOpenContractId())) {
                            continue;
                        }
                    }
                    openContractId = hpcBizContractDTO.getHpcOpenContractId();
                    //缩减节点数
                    hpcBizContractDTO.addNodeRenewNum(type);
                }
            }
        }
    }

    @Override
    public HpcDrpDeleteNodeTipVO deleteNodeTip(OperateNodeRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Date current = new Date();
        Long clusterId = request.getClusterId();
        SfProductResource sfProductResource = sfProductResourceService.lambdaQuery()
                                                                      .eq(SfProductResource::getClusterId, clusterId)
                                                                      .eq(SfProductResource::getProductType,
                                                                          ProductCodeEnum.HPC_DRP.getProductType())
                                                                      .one();
        if (!Objects.equals(authUserInfo.getOrgSid(), sfProductResource.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Long productResourceId = sfProductResource.getId();
        Date sfProductResourceEndTime = sfProductResource.getEndTime();
        Long serviceOrderId = sfProductResource.getServiceOrderId();

        //获取申请单
        ServiceOrder serviceOrder = serviceOrderService.lambdaQuery().eq(ServiceOrder::getId, serviceOrderId).one();

        //查询现金余额
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(serviceOrder.getEntityId(),
                                                                                    AuthUtil.getAuthUser()
                                                                                            .getUserSid());
        if (Objects.isNull(account)) {
            account = bizBillingAccountService.getByEntityIdAndUserId(serviceOrder.getEntityId(),AuthUtil.getAuthUser().getParentSid());
        }

        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
        }
        if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1095922662));
        }

        serviceOrder.setPayTime(current);

        List<String> computeList = request.getComputeList();
        List<String> managerList = request.getManagerList();

        List<NodeInfo> computeNodeInfoList = convertToNodeList(clusterId, computeList);
        List<NodeInfo> managerNodeInfoList = convertToNodeList(clusterId, managerList);

        List<ServiceOrderDetail> applyServiceOrderDetailList =
                serviceOrderDetailService.lambdaQuery().eq(ServiceOrderDetail::getOrderId, serviceOrderId).list();

        serviceOrder.setOrderDetails(applyServiceOrderDetailList);

        //获取所有申请，续订的基础节点的价格详情
        List<ServiceOrderPriceDetail> priceDetails = getApplyAndRenewOrderPriceDetails(current, productResourceId);

        //计算出补偿
        HpcDrpDegradeCompensation totalCompesation =
                this.totalCompensation(serviceOrder, computeNodeInfoList, managerNodeInfoList, priceDetails);

        HpcDrpDeleteNodeTipVO hpcDrpDeleteNodeTipVO = new HpcDrpDeleteNodeTipVO();

        long compensationDays = totalCompesation.getCompensationDays();
        hpcDrpDeleteNodeTipVO.setCompensationDays(compensationDays);
        //显示时间范围
        String range = getCompensationDaysRange(compensationDays);
        hpcDrpDeleteNodeTipVO.setCompensationDaysRange(range);

        if (sfProductResourceEndTime != null) {
            hpcDrpDeleteNodeTipVO.setProductResourceEndTime(
                    DateUtils.addDays(sfProductResourceEndTime, (int) compensationDays));
        }

        return hpcDrpDeleteNodeTipVO;

    }

    private String getCompensationDaysRange(long compensationDays) {
        StringBuilder sb = new StringBuilder();
        if (compensationDays >= 2) {
            sb.append(compensationDays - 2).append(StrUtil.DASHED).append(compensationDays);
        } else if (compensationDays == 1) {
            sb.append(0).append(StrUtil.DASHED).append(compensationDays);
        } else {
            sb.append(0);
        }
        return sb.toString();
    }

    private void checkMinComputeNodeNum(List<ServiceOrderDetail> applyServiceOrderDetailList, int size) {
        if (CollectionUtil.isNotEmpty(applyServiceOrderDetailList)) {

            String productConfigDesc = applyServiceOrderDetailList.stream().filter(orderDetail ->
                                                                                           org.apache.commons.lang3.StringUtils.equalsIgnoreCase(
                                                                                                   orderDetail.getServiceType(),
                                                                                                   cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum.HPC_DRP.getProductCode()))
                                                                  .map(ServiceOrderDetail::getProductConfigDesc)
                                                                  .findFirst().orElse("");

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(productConfigDesc)) {
                JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
                for (Object jsonO : jsonArray) {
                    com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                    if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                       ServiceConfigArrKey.MIN_COMPUTE_NODE_NUM)) {
                        Integer configMinComputeNodeNum = jsonObject.getInteger(ServiceConfigArrKey.JSON_VALUE);
                        if (configMinComputeNodeNum != null && configMinComputeNodeNum > size) {
                            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(
                                    "计算节点至少保留一个。");
                        }
                    }
                }
            }
        }
    }

    /**
     * 生成缩小订单
     *
     * @param request
     */
    private ServiceOrder createDegradeOrder(OperateNodeRequest request) {
        //获取申请单
        Long clusterId = request.getClusterId();
        Optional<ServiceOrder> applyOrderOpl = this.lambdaQuery()
                                                   .eq(ServiceOrder::getClusterId, clusterId)
                                                   .eq(ServiceOrder::getType, OrderType.APPLY)
                                                   .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                                   .list().stream().filter(order -> {
                    return this.serviceOrderDetailService.lambdaQuery()
                                                         .eq(ServiceOrderDetail::getOrderId, order.getId())
                                                         .list()
                                                         .stream()
                                                         .anyMatch(
                                                                 orderDetail -> ProductCodeEnum.HPC_DRP.getProductType()
                                                                                                       .equals(orderDetail.getServiceType()));
                }).findFirst();
        if (!applyOrderOpl.isPresent()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        ServiceOrder applyServiceOrder = applyOrderOpl.get();
        if (!applyServiceOrder.getOrgSid().equals(request.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        //创建serviceOrder
        ServiceOrder degradeServiceOrder = createDegradeServiceOrder(applyServiceOrder);
        //创建serviceOrderDetail
        List<ServiceOrderDetail> degradeServiceOrderDetailList = createDegradeServiceOrderDetail(request,
                                                                                                 degradeServiceOrder,
                                                                                                 applyServiceOrder);
        return degradeServiceOrder;
    }

    private List<ServiceOrderDetail> createDegradeServiceOrderDetail(OperateNodeRequest request,
                                                                     ServiceOrder degradeServiceOrder,
                                                                     ServiceOrder applyServiceOrder) {

        Date payTime = degradeServiceOrder.getPayTime();
        Long clusterId = request.getClusterId();

        List<String> computeList = request.getComputeList();
        List<String> managerList = request.getManagerList();

        List<NodeInfo> computeNodeInfoList = convertToNodeList(clusterId, computeList);
        List<NodeInfo> managerNodeInfoList = convertToNodeList(clusterId, managerList);

        List<ServiceOrderDetail> applyServiceOrderDetailList =
                serviceOrderDetailService.lambdaQuery()
                                         .eq(ServiceOrderDetail::getOrderId, applyServiceOrder.getId())
                                         .list();
        degradeServiceOrder.setOrderDetails(applyServiceOrderDetailList);
        //判断节点数
        // if(CollectionUtil.isNotEmpty(computeNodeInfoList)){
        //     int degradeNode = computeNodeInfoList.size();
        //     List<NodeInfo> nodeInfos = getNodeInfos(clusterId);
        //     int afterDegradeNode = (int) nodeInfos.stream().filter(node -> ClusterNodeType.COMPUTE.equals(node.getNodeType())).count()-degradeNode;
        //     this.checkMinComputeNodeNum(applyServiceOrderDetailList,afterDegradeNode);
        // }

        SfProductResource one = sfProductResourceService.lambdaQuery()
                                                        .eq(SfProductResource::getProductType,
                                                            ProductCodeEnum.HPC_DRP.getProductType())
                                                        .eq(SfProductResource::getClusterId, clusterId).one();
        Long productResourceId = one.getId();
        Date sfProductResourceEndTime = one.getEndTime();
        //获取所有申请，续订的基础节点的价格详情
        List<ServiceOrderPriceDetail> priceDetails = getApplyAndRenewOrderPriceDetails(payTime, productResourceId);

        //计算出补偿
        HpcDrpDegradeCompensation totalCompesation =
                this.totalCompensation(degradeServiceOrder, computeNodeInfoList, managerNodeInfoList, priceDetails);

        //更新集群结束时间
        long compensationDays = totalCompesation.getCompensationDays();
        // Date lastEndTime = DateUtils.addDays(sfProductResourceEndTime, (int) compensationDays);
        // one.setUpdatedDt(payTime);
        // one.setEndTime(lastEndTime);
        // sfProductResourceService.lambdaUpdate().eq(SfProductResource::getId,productResourceId).update(one);
        //记录补偿时间
        // if(compensationDays>0){
        //     SfProductResourceCompensation sfProductResourceCompensation = new SfProductResourceCompensation();
        //     sfProductResourceCompensation.setSfProductResourceId(productResourceId);
        //     sfProductResourceCompensation.setStartTime(sfProductResourceEndTime);
        //     sfProductResourceCompensation.setEndTime(lastEndTime);
        //     sfProductResourceCompensation.setCreatedDt(payTime);
        //     sfProductResourceCompensation.setUpdatedDt(payTime);
        //     sfProductResourceCompensation.setCompensationDays((int)compensationDays);
        //     sfProductResourceCompensationService.save(sfProductResourceCompensation);
        // }
        //修改集群时间
        // ResHpcClusterRemoteModule hpcClusterRemoteModule = new ResHpcClusterRemoteModule();
        // hpcClusterRemoteModule.setId(clusterId);
        // hpcClusterRemoteModule.setEndTime(lastEndTime);
        // hpcRemoteService.updateByPrimaryKeySelective(hpcClusterRemoteModule);
        // ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        // if(ClusterTypeEnum.PRE_SAAS_SHARE.code().equals(resHpcCluster.getClusterType())){
        //     //预部署资源池修改内置弹性文件的结束时间，补偿天数
        //     List<ResShare> shareHPCSFS = hpcRemoteService.getDefaultShareHPCSFS(clusterId);
        //     if(shareHPCSFS.size()>0){
        //         log.info("关联的内置弹性文件-ServiceOrderServiceImpl.createDegradeServiceOrderDetail-shareHPCSFS:[{}]", JSON.toJSONString(shareHPCSFS));
        //         for(ResShare shareSfs:shareHPCSFS){
        //             shareSfs.setEndTime(lastEndTime);
        //             shareRemoteService.updateByPrimaryKey(shareSfs);
        //         }
        //
        //     }
        // }

        //更新合同明细缩容数据
        // List<HpcBizContractDTO> contractList = totalCompesation.getBizContractList();
        // List<BizContractDetail> detailList = contractList.stream().map(HpcBizContractDTO::getContractDetail).collect(Collectors.toList());
        // if (CollectionUtil.isNotEmpty(detailList)) {
        //     bizContractDetailService.saveOrUpdateBatch(detailList);
        // }
        //更新未使用的合同明细节点数
        // bizContractDetailService.updateUnusedDetail(contractList);

        // List<Long> bizContractIdList = detailList.stream().map(BizContractDetail::getContractSid).collect(Collectors.toList());
        // String bizContractIdStr = bizContractIdList.toString();

        degradeServiceOrder.setCompensationDays(compensationDays);
        // degradeServiceOrder.setCompensationContractId(bizContractIdStr);
        // degradeServiceOrder.setStatus(OrderStatus.COMPLETED);
        serviceOrderService.lambdaUpdate()
                           .eq(ServiceOrder::getId, degradeServiceOrder.getId())
                           .update(degradeServiceOrder);

        //HPC申请单无VNC节点需要创建VNC节点

        boolean needCreateVNCProductInfo = false;
        CurrentConfigDesc vncNodeConfigDesc = null;

        //创建HPC-DRP的信息
        for (ServiceOrderDetail orderDetail : applyServiceOrderDetailList) {
            if (ProductCodeEnum.HPC_DRP.getProductType().equals(orderDetail.getServiceType())) {
                String serviceConfig = orderDetail.getServiceConfig();
                if (org.apache.commons.lang.StringUtils.isEmpty(serviceConfig)) {
                    continue;
                }
                ProductInfoVO productInfoVO = com.alibaba.fastjson.JSONObject.parseObject(serviceConfig,
                                                                                          ProductInfoVO.class);
                String productConfigDesc = productInfoVO.getProductConfigDesc().getCurrentConfigDesc();

                String productCode = productInfoVO.getProductCode();
                String productCategory = productInfoVO.getProductCategory();

                JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(productConfigDesc);
                JSONArray newJsonArray = new JSONArray();
                //计算节点
                int computeNum = computeNodeInfoList.size();
                List<NodeInfo> baseManagerNodeInfoList = managerNodeInfoList.stream()
                                                                            .filter(nodeInfo ->
                                                                                            !HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                                                                    nodeInfo.getHpcPointType())
                                                                                                    && !HpcPointType.VNC.equalsIgnoreCase(
                                                                                                    nodeInfo.getHpcPointType()))
                                                                            .collect(Collectors.toList());
                int managerNum = baseManagerNodeInfoList.size();
                long cliNum = managerNodeInfoList.stream()
                                                 .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                         nodeInfo.getHpcPointType()))
                                                 .count();

                long vncNum = managerNodeInfoList.stream()
                                                 .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(
                                                         nodeInfo.getHpcPointType()))
                                                 .count();

                boolean needCreateVNCNode = false;

                if (!org.apache.commons.lang.StringUtils.containsIgnoreCase(productConfigDesc,
                                                                            ServiceConfigArrKey.VNC_NODE_INFO)
                        && vncNum > 0) {
                    needCreateVNCNode = true;
                    needCreateVNCProductInfo = true;
                    //从产品模板中，获取VNC节点信息
                 //   vncNodeConfigDesc = hpcDrpOrderService.templateToVNCNodeConfigDesc(orderDetail.getServiceId());
                }
                if (CollectionUtil.isNotEmpty(jsonArray)) {
                    for (Object jsonO : jsonArray) {
                        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonO;
                        //修改计算节点信息
                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                           ServiceConfigArrKey.COMPUTE_NODE_INFO)) {

                            productInfoVO.setAmount(computeNum);
                            setAttr(computeNum, newJsonArray, jsonObject);
                            continue;
                        }
                        //修改登录节点信息
                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                           ServiceConfigArrKey.LOGIN_NODE_INFO)) {
                            productInfoVO.setAmount((int) cliNum);
                            setAttr((int) cliNum, newJsonArray, jsonObject);
                            continue;
                        }
                        //修改VNC节点信息
                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                           ServiceConfigArrKey.VNC_NODE_INFO)) {
                            productInfoVO.setAmount((int) vncNum);
                            setAttr((int) vncNum, newJsonArray, jsonObject);
                            continue;
                        } else if (needCreateVNCNode) {
                            setConfigAttr((int) vncNum, newJsonArray, vncNodeConfigDesc);
                            needCreateVNCNode = false;
                        }
                        //修改管理节点信息
                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                           ServiceConfigArrKey.MANAGEMENT_NODE_INFO)) {
                            productInfoVO.setAmount(managerNum);
                            setAttr(managerNum, newJsonArray, jsonObject);
                            continue;
                        }

                        //修改时间
                        if (Objects.equals(jsonObject.get(ServiceConfigArrKey.JSON_ATTR_KEY),
                                           ServiceConfigArrKey.TIME)) {
                            CurrentConfigDescStr currentConfigDescStr = null;
                            try {
                                currentConfigDescStr = JSON.parseObject(jsonObject.toJSONString(),
                                                                        CurrentConfigDescStr.class);
                                currentConfigDescStr.setValue("0");
                                DescDTO desc = currentConfigDescStr.getDesc();
                                desc.setValue(0 + "个月");
                                newJsonArray.add(JSON.toJSON(currentConfigDescStr));
                            } catch (Exception e) {
                                System.out.println(jsonObject.toJSONString());
                                log.info("json解析异常");
                                newJsonArray.add(jsonO);
                            }
                            continue;
                        }
                    }
                }

                long count = computeNodeInfoList.stream().filter(nodeInfo ->
                                                                         StringUtils.equalsIgnoreCase(
                                                                                 nodeInfo.getResourceType(),
                                                                                 productCode) &&
                                                                                 StringUtils.equals(
                                                                                         nodeInfo.getTypeName(),
                                                                                         productCategory))
                                                .count();
                long count1 = managerNodeInfoList.stream().filter(nodeInfo ->
                                                                          StringUtils.equalsIgnoreCase(
                                                                                  nodeInfo.getResourceType(),
                                                                                  productCode) &&
                                                                                  StringUtils.equals(
                                                                                          nodeInfo.getTypeName(),
                                                                                          productCategory))
                                                 .count();
                // long count2 = graphicNodeInfoList.stream().filter(nodeInfo ->
                //                 StringUtils.equalsIgnoreCase(nodeInfo.getResourceType(), productCode) &&
                //                         StringUtils.equals(nodeInfo.getTypeName(), productCategory))
                //         .count();
                long totalQuantity = count + count1;
                degradeServiceOrder.setOrgSid(orderDetail.getOrgSid());
                saveDegradeOrderDetail(degradeServiceOrder, Integer.valueOf((int) totalQuantity), productResourceId,
                                       productInfoVO, productCode, productCategory);
                break;
            }
        }

        //节点类型Map
        // Map<String,List<NodeInfo>> nodeInfoMap = new HashMap<>();
        List<NodeInfo> allNodeInfoList = new ArrayList<>();
        allNodeInfoList.addAll(computeNodeInfoList);
        allNodeInfoList.addAll(managerNodeInfoList);
        // allNodeInfoList.addAll(graphicNodeInfoList);
        // allNodeInfoList.stream().forEach(node -> {
        //     String key = node.getResourceType() + node.getTypeName();
        //     List<NodeInfo> nodeInfos = nodeInfoMap.get(key);
        //     if (CollectionUtil.isNotEmpty(nodeInfos)) {
        //         nodeInfos.add(node);
        //     } else {
        //         nodeInfos = new ArrayList<>();
        //         nodeInfos.add(node);
        //         nodeInfoMap.put(key, nodeInfos);
        //     }
        // });
        //根据节点返回
        allNodeInfoList.stream().forEach(nodeInfo -> {
            //资源类型规格
            String typeName = nodeInfo.getTypeName();
            String resourceType = nodeInfo.getResourceType();
            ServiceOrderDetail detail =
                    applyServiceOrderDetailList.stream()
                                               .filter(data -> resourceType.equals(data.getServiceType()))
                                               .findFirst()
                                               .orElse(CollectionUtil.getFirst(applyServiceOrderDetailList));
            ProductInfoVO productInfoVO = new ProductInfoVO();
            productInfoVO.setServiceId(detail.getServiceId());
            productInfoVO.setProductCategory(typeName);
            productInfoVO.setChargeType(detail.getChargeType());
            productInfoVO.setProductCode(nodeInfo.getResourceType());
            productInfoVO.setAmount(1);
            productInfoVO.setBillingAccountId(degradeServiceOrder.getBizBillingAccountId());
            productInfoVO.setProducts(Arrays.asList(resourceType));
            productInfoVO.setData(JSON.toJSONString(nodeInfo));
            ProductConfigDesc productConfigDesc = new ProductConfigDesc();
            productConfigDesc.setCurrentConfigDesc(
                    JSON.toJSONString(Arrays.asList(this.buildCurrentConfigDesc(nodeInfo))));
            productInfoVO.setProductConfigDesc(productConfigDesc);
            productInfoVO.setPeriod(BigDecimal.ZERO);
            // List<String> nodeIdList = nodeInfo.stream().map(NodeInfo::getId).collect(Collectors.toList());
            // List<String> nodeInstanceIdlist = nodeInfo.stream().map(NodeInfo::getInstanceId).collect(Collectors.toList());
            productInfoVO.setNodeIdList(nodeInfo.getId().toString());
            productInfoVO.setNodeInstanceIdList(nodeInfo.getInstanceId().toString());
            productInfoVO.setNodeStartTime(nodeInfo.getStarTime());
            productInfoVO.setNodeEndTime(nodeInfo.getEndTime());
            degradeServiceOrder.setOrgSid(detail.getOrgSid());
            this.saveDegradeOrderDetail(degradeServiceOrder, 1, productResourceId, productInfoVO,
                                        resourceType, typeName);


        });

        return applyServiceOrderDetailList;
    }

    /**
     * [ { "attrKey": "vncNodeInfo", "chidrenDesc": [ [ { "isShow": false, "label": "登录密码", "value": "1q2w3e4r" }, {
     * "isShow": true, "label": "资源规格", "value": "1vCPU、1GiB内存" }, { "isShow": true, "label": "节点数目", "value": "1" }, {
     * "isShow": false, "label": "资源类型", "value": "ECS" }, { "isShow": false, "label": "节点类型", "value": "CCS_VNC" }, {
     * "isShow": false, "label": "磁盘类型", "value": "SAS" }, { "isShow": false, "label": "磁盘大小", "value": "480" } ] ],
     * "isHidden": false, "label": "VNC节点", "value": [ { "flavorRef": "s3.small.1", "hpcNodeType": "CCS_VNC", "nodeNum":
     * 1, "password": "1q2w3e4r", "resourceType": "ECS", "size": 480, "volumeType": "SAS" } ] } ]
     */
    private CurrentConfigDesc buildCurrentConfigDesc(NodeInfo nodeInfo) {
        CurrentConfigDesc currentConfigDesc = new CurrentConfigDesc();
        String hpcPointType = nodeInfo.getHpcPointType();
        if (ClusterNodeType.COMPUTE.equals(nodeInfo.getNodeType())) {
            currentConfigDesc.setAttrKey(ServiceConfigArrKey.COMPUTE_NODE_INFO);
            currentConfigDesc.setLabel("计算节点");
        } else {
            if (HpcPointType.VNC.equals(hpcPointType)) {
                currentConfigDesc.setAttrKey(ServiceConfigArrKey.VNC_NODE_INFO);
                currentConfigDesc.setLabel("VNC节点");
            } else if (HpcPointType.CCS_CLI.equals(hpcPointType)) {
                currentConfigDesc.setAttrKey(ServiceConfigArrKey.LOGIN_NODE_INFO);
                currentConfigDesc.setLabel("登录节点");
            } else {
                currentConfigDesc.setAttrKey(ServiceConfigArrKey.MANAGEMENT_NODE_INFO);
                currentConfigDesc.setAttrKey("管理节点");
            }
        }
        List<NodeValueDTO> valueDTOList = new ArrayList<>();
        NodeValueDTO nodeValueDTO = new NodeValueDTO();
        nodeValueDTO.setNodeNum(1);
        nodeValueDTO.setHpcNodeType(nodeInfo.getHpcPointType());
        nodeValueDTO.setFlavorRef(nodeInfo.getTypeName());
        nodeValueDTO.setSize(
                StringUtils.isNotEmpty(nodeInfo.getAllocateDiskSize()) ? Integer.valueOf(nodeInfo.getAllocateDiskSize())
                        : 0);
        nodeValueDTO.setResourceType(nodeInfo.getResourceType());
        nodeValueDTO.setPassword(nodeInfo.getManagemenPassword());
        nodeValueDTO.setVolumeType(nodeInfo.getDiskMode());
        valueDTOList.add(nodeValueDTO);
        currentConfigDesc.setValue(valueDTOList);

        currentConfigDesc.setIsHidden(false);
        List<List<ChidrenDescDTO>> chidrenDescDtoList = new ArrayList<>();
        List<ChidrenDescDTO> dtoList = new ArrayList<>();
        this.buildChidrenDescDTO(dtoList, nodeInfo);
        chidrenDescDtoList.add(dtoList);
        currentConfigDesc.setChidrenDesc(chidrenDescDtoList);
        return currentConfigDesc;
    }

    private void buildChidrenDescDTO(List<ChidrenDescDTO> dtoList, NodeInfo nodeInfo) {
        ChidrenDescDTO descDTO = new ChidrenDescDTO();

        descDTO.setLabel("资源规格");
        descDTO.setIsShow(true);
        StringBuffer sb = new StringBuffer();
        if (nodeInfo.getCpu() != null) {
            sb.append(nodeInfo.getCpu()).append("vCPU");
        }
        if (nodeInfo.getRam() != null) {
            sb.append("、").append(Integer.valueOf(nodeInfo.getRam())).append("GiB内存");
        }
        descDTO.setValue(sb.toString());
        dtoList.add(descDTO);

        descDTO = new ChidrenDescDTO();
        descDTO.setLabel("节点数目");
        descDTO.setValue("1");
        descDTO.setIsShow(true);
        dtoList.add(descDTO);

        descDTO = new ChidrenDescDTO();
        descDTO.setLabel("资源类型");
        String resourceType = nodeInfo.getResourceType();
        if (ProductCodeEnum.BMS.getProductType().equals(resourceType)) {
            descDTO.setValue(ProductCodeEnum.BMS.getProductName());
        }
        if (ProductCodeEnum.ECS.getProductType().equals(resourceType)) {
            descDTO.setValue(ProductCodeEnum.ECS.getProductName());
        }
        descDTO.setIsShow(true);
        dtoList.add(descDTO);

        descDTO = new ChidrenDescDTO();
        descDTO.setLabel("登录密码");
        descDTO.setIsShow(false);
        //密码不在页面上显示
        descDTO.setValue("");
        dtoList.add(descDTO);

        descDTO = new ChidrenDescDTO();
        descDTO.setLabel("磁盘类型");
        descDTO.setIsShow(false);
        HuaweiCloudDiskModeEnum modeEnum = HuaweiCloudDiskModeEnum.getByCode(nodeInfo.getDiskMode());
        if (modeEnum != null) {
            descDTO.setValue(modeEnum.getModeDesc());
        }
        dtoList.add(descDTO);

        descDTO = new ChidrenDescDTO();
        descDTO.setLabel("磁盘大小");
        descDTO.setIsShow(false);
        descDTO.setValue(nodeInfo.getAllocateDiskSize());
        dtoList.add(descDTO);

    }

    private void saveDegradeOrderDetail(ServiceOrder degradeServiceOrder, Integer totalQuantity, Long productResourceId,
                                        ProductInfoVO productInfoVO, String productCode, String productCategory) {

        Long degradeServiceOrderId = degradeServiceOrder.getId();
        Date payTime = degradeServiceOrder.getPayTime();

        ServiceOrderDetail degreadeOrderDetail = new ServiceOrderDetail();
        degreadeOrderDetail.setOrderId(degradeServiceOrderId);
        degreadeOrderDetail.setServiceId(productInfoVO.getServiceId());
        degreadeOrderDetail.setChargeType(productInfoVO.getChargeType());
        degreadeOrderDetail.setServiceConfig(JSON.toJSONString(productInfoVO));

        degreadeOrderDetail.setVersion(1L);
        degreadeOrderDetail.setOrgSid(degradeServiceOrder.getOrgSid());
        degreadeOrderDetail.setServiceType(productCode);

        degreadeOrderDetail.setDuration(0);
        degreadeOrderDetail.setStartTime(productInfoVO.getNodeStartTime());
        degreadeOrderDetail.setEndTime(productInfoVO.getNodeEndTime());

        String currentConfigDesc = productInfoVO.getProductConfigDesc().getCurrentConfigDesc();
        degreadeOrderDetail.setProductConfigDesc(currentConfigDesc);
        degreadeOrderDetail.setPrice(BigDecimal.ZERO);

        degreadeOrderDetail.setAmount(BigDecimal.ZERO);
        degreadeOrderDetail.setOriginalCost(BigDecimal.ZERO);

        degreadeOrderDetail.setQuantity(totalQuantity);
        degreadeOrderDetail.setVersion(1L);

        degreadeOrderDetail.setDiscountRatio(BigDecimal.ONE);
        degreadeOrderDetail.setFloatingRatio(BigDecimal.ONE);
        //申请类型
        degreadeOrderDetail.setApplyType(productInfoVO.getApplyType());
        serviceOrderDetailService.save(degreadeOrderDetail);

        degradeServiceOrder.getOrderDetails().add(degreadeOrderDetail);

        //生成明细
        createOrderPriceDetail(degradeServiceOrder, productResourceId, degreadeOrderDetail);
    }

    private void setAttr(Integer vncNum, JSONArray newJsonArray, com.alibaba.fastjson.JSONObject jsonObject) {
        CurrentConfigDesc currentConfigDesc = null;
        try {
            currentConfigDesc = JSON.parseObject(jsonObject.toJSONString(), CurrentConfigDesc.class);
            setConfigAttr(vncNum, newJsonArray, currentConfigDesc);
        } catch (Exception e) {
            System.out.println(jsonObject.toJSONString());
            log.info("json解析异常");
            newJsonArray.add(jsonObject);
        }
    }

    private void setConfigAttr(Integer vncNum, JSONArray newJsonArray, CurrentConfigDesc currentConfigDesc) {
        changeNodeNum(vncNum, currentConfigDesc);
        newJsonArray.add(JSON.toJSON(currentConfigDesc));
    }

    /**
     * 修改节点数
     *
     * @param agentNum
     * @param currentConfigDesc
     */
    private void changeNodeNum(Integer agentNum, CurrentConfigDesc currentConfigDesc) {
        List<NodeValueDTO> nodeValueDTOList = currentConfigDesc.getValue();
        for (NodeValueDTO nodeValueDTO : nodeValueDTOList) {
            nodeValueDTO.setNodeNum(agentNum);
        }
        List<List<ChidrenDescDTO>> chidrenDesc = currentConfigDesc.getChidrenDesc();
        for (List<ChidrenDescDTO> chidrenDescDTOS : chidrenDesc) {
            for (ChidrenDescDTO chidrenDescDTO : chidrenDescDTOS) {
                if ("节点数目".equals(chidrenDescDTO.getLabel())) {
                    chidrenDescDTO.setValue(agentNum.toString());
                }
            }
        }
    }


    private void createOrderPriceDetail(ServiceOrder degradeServiceOrder, Long productResourceId,
                                        ServiceOrderDetail degreadeOrderDetail) {
        ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
        priceDetail.setType(degradeServiceOrder.getType());
        priceDetail.setChargeType(degreadeOrderDetail.getChargeType());
        priceDetail.setOrderSn(degradeServiceOrder.getOrderSn());
        priceDetail.setOrgSid(degradeServiceOrder.getOrgSid());
        priceDetail.setOrderDetailId(degreadeOrderDetail.getId());
        priceDetail.setVersion(1L);

        priceDetail.setCouponAmount(BigDecimal.ZERO);

        String serviceConfig = degreadeOrderDetail.getServiceConfig();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(serviceConfig)) {
            JSONObject jsonObject = JSONObject.parseObject(serviceConfig);
            if (jsonObject != null) {
                String productCategory = jsonObject.getString("productCategory");
                priceDetail.setServiceType(productCategory);
                priceDetail.setBillingSpec(productCategory);
            }
        }

        String serviceType = degreadeOrderDetail.getServiceType();
        priceDetail.setProductCode(serviceType);
        priceDetail.setQuantity(degreadeOrderDetail.getQuantity());
        priceDetail.setPriceType(PriceType.RESOURCE);
        priceDetail.setRefInstanceId(JSONArray.toJSONString(Lists.newArrayList(productResourceId.toString())));

        priceDetail.setPriceDesc(ProductCodeEnum.toDesc(serviceType));

        priceDetail.setPrice(degreadeOrderDetail.getPrice());
        priceDetail.setAmount(degreadeOrderDetail.getAmount());
        priceDetail.setOriginalCost(degreadeOrderDetail.getOriginalCost());
        priceDetail.setFixedHourPrice(BigDecimal.ZERO);
        priceDetail.setUnitHourPrice(BigDecimal.ZERO);
        priceDetail.setTradeFixedHourPrice(BigDecimal.ZERO);
        priceDetail.setTradeUnitHourPrice(BigDecimal.ZERO);
        priceDetail.setFixedMonth(BooleanEnum.NO.getCode());
        priceDetail.setPayBalance(BigDecimal.ZERO);
        priceDetail.setPayBalanceCash(BigDecimal.ZERO);
        priceDetail.setPayCreditLine(BigDecimal.ZERO);
        priceDetail.setStartTime(degreadeOrderDetail.getStartTime());
        priceDetail.setEndTime(degreadeOrderDetail.getEndTime());

        // 批量新增订单价格明细
        serviceOrderPriceDetailService.save(priceDetail);
    }

    /**
     * 计算总补偿天数
     *
     * @param degradeServiceOrder
     * @param computeNodeList
     * @param managerNodeInfoList
     * @param priceDetails
     */
    private HpcDrpDegradeCompensation totalCompensation(ServiceOrder degradeServiceOrder,
                                                        List<NodeInfo> computeNodeList,
                                                        List<NodeInfo> managerNodeInfoList,
                                                        List<ServiceOrderPriceDetail> priceDetails) {
        //
        Long clusterId = degradeServiceOrder.getClusterId();
        //查询集群扩容相关的合同
        List<HpcBizContractDTO> hpcBizContractDTOS = getHpcBizContractDTOList(clusterId);
        log.debug("扩容合同：{}", hpcBizContractDTOS);
        HpcDrpDegradeCompensation totalCompensation = new HpcDrpDegradeCompensation();
        //查询所有执行中的续订合同,计算出补偿金额
        HpcDrpDegradeCompensation computeCompensation = computeCompensation(degradeServiceOrder, priceDetails,
                                                                            computeNodeList, hpcBizContractDTOS, 0);

        List<NodeInfo> cliNodeList = managerNodeInfoList.stream()
                                                        .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(
                                                                nodeInfo.getHpcPointType()))
                                                        .collect(Collectors.toList());
        HpcDrpDegradeCompensation cliCompensation = computeCompensation(degradeServiceOrder, priceDetails, cliNodeList,
                                                                        hpcBizContractDTOS, 1);
        List<NodeInfo> vncNodeList = managerNodeInfoList.stream()
                                                        .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(
                                                                nodeInfo.getHpcPointType()))
                                                        .collect(Collectors.toList());

        HpcDrpDegradeCompensation vncCompensation = computeCompensation(degradeServiceOrder, priceDetails, vncNodeList,
                                                                        hpcBizContractDTOS, 2);
        //原始的管理节点不会被缩容
        // List<NodeInfo> baseManagerNodeInfoList = managerNodeInfoList.stream()
        //         .filter(nodeInfo -> !HpcPointType.CCS_CLI.equalsIgnoreCase(nodeInfo.getHpcPointType()) && !HpcPointType.CCS_VNC.equalsIgnoreCase(nodeInfo.getHpcPointType()))
        //         .collect(Collectors.toList());
        // HpcDrpDegradeCompensation baseManagerCompensation = computeCompensation(degradeServiceOrder, priceDetails, baseManagerNodeInfoList, 3);
        log.info("计算节点补偿金额{}", computeCompensation.getCompensationAmount());
        log.info("登录节点补偿金额{}", cliCompensation.getCompensationAmount());
        log.info("vnc节点补偿金额{}", vncCompensation.getCompensationAmount());
        //总的补偿金额
        BigDecimal totalCompesationAmount =
                computeTotalCompensationAmount(computeCompensation, cliCompensation, vncCompensation);
        //最新一个周期集群每日价格
        BigDecimal baseClusterPricePerDay = computeBaseClusterPricePerDay(clusterId, priceDetails, computeCompensation,
                                                                          cliCompensation, vncCompensation);
        log.info("集群每日价格{}", baseClusterPricePerDay);

        //总补偿天数
        long totalCompensationDays = 0L;
        if (totalCompesationAmount != null && NumberUtil.isGreater(baseClusterPricePerDay, BigDecimal.ZERO)) {
            totalCompensationDays = NumberUtil.div(totalCompesationAmount, baseClusterPricePerDay).longValue();
        }
        totalCompensation.setCompensationDays(totalCompensationDays);
        //涉及到的合同
        List<HpcBizContractDTO> totalModifyContractList =
                hpcBizContractDTOS.stream().filter(HpcBizContractDTO::isModifyNodeFlag).collect(Collectors.toList());
        totalCompensation.setBizContractList(totalModifyContractList);
        return totalCompensation;
    }

    /**
     * 获取集群扩容合同
     */
    @Override
    public List<HpcBizContractDTO> getHpcBizContractDTOList(Long clusterId) {
        List<Long> contractIdList = this.lambdaQuery()
                                        .in(ServiceOrder::getType,
                                            Arrays.asList(OrderType.UPGRADE, OrderType.UPGRADE_RENEW))
                                        .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                        .eq(ServiceOrder::getClusterId, clusterId)
                                        .list()
                                        .stream()
                                        .filter(order -> {
                                            return this.serviceOrderDetailService.lambdaQuery()
                                                                                 .eq(ServiceOrderDetail::getOrderId,
                                                                                     order.getId())
                                                                                 .list()
                                                                                 .stream()
                                                                                 .anyMatch(
                                                                                         orderDetail -> ProductCodeEnum.HPC_DRP.getProductType()
                                                                                                                               .equals(orderDetail.getServiceType()))
                                                    && Objects.nonNull(order.getContractId());
                                        })
                                        .map(order -> Long.valueOf(order.getContractId()))
                                        .collect(Collectors.toList());

        List<HpcBizContractDTO> hpcBizContractDTOS = bizContractService.queryHpcContractTreeByIdList(contractIdList,
                                                                                                     Arrays.asList(
                                                                                                             ContractStatus.AUDIT_EXECUTING,
                                                                                                             ContractStatus.EXPIRED));
        return hpcBizContractDTOS;
    }


    /**
     * 计算总的补偿金额
     *
     * @param compensations
     */
    private BigDecimal computeTotalCompensationAmount(HpcDrpDegradeCompensation... compensations) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (HpcDrpDegradeCompensation compensation : compensations) {
            totalAmount = NumberUtil.add(totalAmount, compensation.getCompensationAmount());
        }
        return totalAmount;
    }

    private List<ServiceOrderPriceDetail> getApplyAndRenewOrderPriceDetails(Date payTime, Long productResourceId) {
        Criteria criteria = new Criteria();
        criteria.put("refInstanceIdLike", "\"" + productResourceId + "\"");
        criteria.put("endTime", payTime);
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew"));
        criteria.put("orderStatus", "completed");
        // criteria.put("priceType", "resource");
        criteria.put("productCodeIn", Arrays.asList(ProductCodeEnum.HPC_DRP.getProductType(),
                                                    ProductCodeEnum.BMS.getProductType(),
                                                    ProductCodeEnum.ECS.getProductType(),
                                                    ProductCodeEnum.SFS2.getProductType()));
        List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailService.selectByCriteria(criteria);
        priceDetails = priceDetails.stream()
                                   .sorted(Comparator.comparing(ServiceOrderPriceDetail::getEndTime).reversed())
                                   .collect(Collectors.toList());
        return priceDetails;
    }

    private List<NodeInfo> convertToNodeList(Long clusterId, List<String> nodeInstanceIdList) {
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(nodeInstanceIdList)) {
            List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
            resVmNodeInfoList.stream()
                             .filter(node -> nodeInstanceIdList.contains(node.getInstanceId()))
                             .forEach(node -> nodeInfoList.add(BeanConvertUtil.convert(node, NodeInfo.class)));

            List<ResBmsNodeInfo> resBmsNodeInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
            resBmsNodeInfoList.stream()
                              .filter(node -> nodeInstanceIdList.contains(node.getInstanceId()))
                              .forEach(node -> nodeInfoList.add(BeanConvertUtil.convert(node, NodeInfo.class)));
        }
        return nodeInfoList;
    }

    /**
     * @param serviceOrder
     * @param priceDetails
     * @param nodeInfoList
     * @param type 0 计算节点，1登录节点，2，可视化节点
     */
    private HpcDrpDegradeCompensation computeCompensation(ServiceOrder serviceOrder,
                                                          List<ServiceOrderPriceDetail> priceDetails,
                                                          List<NodeInfo> nodeInfoList,
                                                          List<HpcBizContractDTO> hpcBizContractDTOS, int type) {
        Long clusterId = serviceOrder.getClusterId();
        Date current = serviceOrder.getPayTime();
        HpcDrpDegradeCompensation hpcDrpDegradeCompensation = new HpcDrpDegradeCompensation();
        int nodeNum = nodeInfoList.size();
        hpcDrpDegradeCompensation.setNodeType(type);
        //type 0 计算节点 1登录节点 2可视化节点 3其他管理节点
        BigDecimal compensationAmount = hpcDrpDegradeCompensation.getCompensationAmount();
        if (nodeNum == 0) {
            return hpcDrpDegradeCompensation;
        }
        //执行中的合同

        hpcBizContractDTOS = hpcBizContractDTOS.stream().filter(dto -> !dto.isZeroNode()).collect(Collectors.toList());
        if (type < 3) {
            //总的剩余合同节点数
            int allContractNodeNum = this.getAllContractNode(hpcBizContractDTOS, type);
            //缩容节点数小于等于合同节点数
            hpcDrpDegradeCompensation.addContractNodeList(allContractNodeNum, CollectionUtil.getFirst(nodeInfoList));
            if (allContractNodeNum >= nodeNum) {
                // long compensationDays = hpcDrpDegradeCompensation.getCompensationDays();
                for (NodeInfo nodeInfo : nodeInfoList) {
                    //合同补偿金额
                    BigDecimal contractAmount = computeBizContractAmount(nodeInfo, hpcBizContractDTOS, current, type);
                    //集群原始节点单价
                    // BigDecimal basePricePerDay = computeBaseNodePricePerDay(nodeInfo,priceDetails,current);
                    // if(contractAmount == null || NumberUtil.equals(basePricePerDay,BigDecimal.ZERO)){
                    //     continue;
                    // }
                    // long days = NumberUtil.div(contractAmount, basePricePerDay).longValue();
                    // compensationDays+=days;
                    if (contractAmount == null) {
                        continue;
                    }
                    compensationAmount = NumberUtil.add(compensationAmount, contractAmount);
                    nodeInfo.setEndTime(current);
                }
                // hpcDrpDegradeCompensation.setCompensationDays(compensationDays);
                hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
            } else {
                // 缩容 涉及原始节点计算原始节点补偿天数
                // long compensationDays = hpcDrpDegradeCompensation.getCompensationDays();
                for (NodeInfo nodeInfo : nodeInfoList) {
                    if (allContractNodeNum > 0) {
                        BigDecimal contractAmount = computeBizContractAmount(nodeInfo, hpcBizContractDTOS, current,
                                                                             type);
                        if (contractAmount == null) {
                            contractAmount = BigDecimal.ZERO;
                        }
                        allContractNodeNum--;
                        compensationAmount = NumberUtil.add(compensationAmount, contractAmount);
                    } else {
                        //基础节点补偿金额
                        BigDecimal baseCompensationAmount = computeBaseCompensation(nodeInfo, priceDetails, current,
                                                                                    clusterId);
                        if (baseCompensationAmount == null) {
                            baseCompensationAmount = BigDecimal.ZERO;
                        }
                        compensationAmount = NumberUtil.add(compensationAmount, baseCompensationAmount);
                        hpcDrpDegradeCompensation.getDegradeBaseNodeInfoList().add(nodeInfo);
                    }
                    //基础节点补偿金额
                    //
                    // BigDecimal baseAmountPerDay = computeBaseNodePricePerDay(nodeInfo,priceDetails,current);
                    // if (NumberUtil.equals(baseAmountPerDay, BigDecimal.ZERO)) {
                    //     continue;
                    // }
                    // long days = NumberUtil.div(contractAmount, baseAmountPerDay).longValue();
                    // compensationDays+=days;
                    nodeInfo.setEndTime(current);

                }
                // hpcDrpDegradeCompensation.setCompensationDays(compensationDays);
                hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
            }
        } else {
            //缩容 原始节点计算原始节点补偿金额
            for (NodeInfo nodeInfo : nodeInfoList) {
                BigDecimal baseCompensationAmount = computeBaseCompensation(nodeInfo, priceDetails, current, clusterId);
                compensationAmount = NumberUtil.add(compensationAmount, baseCompensationAmount);
            }
            hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
        }
        return hpcDrpDegradeCompensation;
    }

    /**
     * 原始节点补偿金额
     *
     * @param nodeInfo
     * @param priceDetails
     * @param current
     * @param clusterId
     */
    private BigDecimal computeBaseCompensation(NodeInfo nodeInfo, List<ServiceOrderPriceDetail> priceDetails,
                                               Date current, Long clusterId) {
        BigDecimal basePrice = BigDecimal.ZERO;
        SfProductResource sfProductResource = sfProductResourceService.lambdaQuery()
                                                                      .eq(SfProductResource::getProductType,
                                                                          ProductCodeEnum.HPC_DRP.getProductType())
                                                                      .eq(SfProductResource::getClusterId, clusterId)
                                                                      .one();
        if (CollectionUtil.isNotEmpty(priceDetails)) {
            //获取当前集群的剩余的补偿天数
            Integer remainCompensationDays = getRemainCompensationDays(sfProductResource, current);
            Date calResourceStarTime = current;
            if (remainCompensationDays > 0) {
                calResourceStarTime = DateUtils.addDays(current, -remainCompensationDays);
            }
            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                Date startTime = priceDetail.getStartTime();
                Date endTime = priceDetail.getEndTime();
                if (StringUtils.equals(priceDetail.getServiceType(), nodeInfo.getTypeName()) &&
                        StringUtils.equals(priceDetail.getProductCode(), nodeInfo.getResourceType())) {
                    //未超期返回节点总价格
                    BigDecimal oneNodeAmount = NumberUtil.div(priceDetail.getAmount(), priceDetail.getQuantity());
                    if (calResourceStarTime.before(startTime)) {
                        basePrice = NumberUtil.add(oneNodeAmount, basePrice);
                    } else if (calResourceStarTime.before(endTime) && !calResourceStarTime.before(startTime)) {
                        //节点总价格*剩余天数/总天数
                        BigDecimal amountPerDay = NumberUtil.div(NumberUtil.mul(oneNodeAmount,
                                                                                cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(
                                                                                        calResourceStarTime, endTime,
                                                                                        false)),
                                                                 cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(
                                                                         startTime, endTime, true));
                        basePrice = NumberUtil.add(amountPerDay, basePrice);
                    }
                }
                //如果是申请单设置节点开始时间
                if (OrderType.APPLY.equals(priceDetail.getType())) {
                    nodeInfo.setStarTime(startTime);
                }
            }
        }

        // #处理申请单过期，节点删除时没有开始时间导致报错空指针异常问题
        if (nodeInfo.getStarTime() == null) {
            nodeInfo.setStarTime(sfProductResource.getStartTime());
        }
        return basePrice;
    }

    /**
     * 获取当前集群实例的剩余补偿天数
     *
     * @param current
     * @param sfProductResource
     */
    private Integer getRemainCompensationDays(SfProductResource sfProductResource, Date current) {
        Integer remainCompensationDays = 0;
        List<SfProductResourceCompensation> resourceCompensations = sfProductResourceCompensationService.lambdaQuery()
                                                                                                        .eq(SfProductResourceCompensation::getSfProductResourceId,
                                                                                                            sfProductResource.getId())
                                                                                                        .gt(SfProductResourceCompensation::getEndTime,
                                                                                                            current)
                                                                                                        .list();
        if (CollectionUtil.isNotEmpty(resourceCompensations)) {
            for (SfProductResourceCompensation resourceCompensation : resourceCompensations) {
                remainCompensationDays += resourceCompensation.getCompensationDays();
            }
        }
        return remainCompensationDays;
    }

    /**
     * 基础节点每日价格
     *
     * @param nodeInfo
     * @param priceDetails
     * @param current
     */
    private BigDecimal computeBaseNodePricePerDay(NodeInfo nodeInfo,
                                                  List<ServiceOrderPriceDetail> priceDetails, Date current) {
        BigDecimal basePrice = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(priceDetails)) {
            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                Date startTime = priceDetail.getStartTime();
                Date endTime = priceDetail.getEndTime();
                if (StringUtils.equals(priceDetail.getServiceType(), nodeInfo.getTypeName())
                        && StringUtils.equals(priceDetail.getProductCode(), nodeInfo.getResourceType())
                        && current.before(endTime)
                        && !current.before(startTime)) {
                    //1个节点总价
                    BigDecimal oneNodeAmount = NumberUtil.mul(priceDetail.getPrice(),
                                                              DateUtil.betweenMonth(startTime, endTime, true));
                    //每日价格
                    BigDecimal amountPerDay = NumberUtil.div(oneNodeAmount,
                                                             DateUtil.betweenDay(startTime, endTime, true));
                    basePrice = NumberUtil.add(amountPerDay, basePrice);
                }
            }
        }
        return basePrice;
    }

    /**
     * 最新一个周期集群每日价格
     *
     * @param clusterId
     * @param priceDetails 集群申请单和续订订单
     * @param hpcdrpdegradecompensationList 补偿数据
     */
    private BigDecimal computeBaseClusterPricePerDay(Long clusterId, List<ServiceOrderPriceDetail> priceDetails,
                                                     HpcDrpDegradeCompensation... hpcdrpdegradecompensationList) {
        BigDecimal basePrice = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(priceDetails)) {
            //查询原始的节点剩余数
            List<NodeInfo> remainNodeList = new ArrayList<>();
            List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
            remainNodeList.addAll(resVmNodeInfoList);
            List<ResBmsNodeInfo> resBmsNodeInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
            remainNodeList.addAll(resBmsNodeInfoList);

            List<NodeInfo> baseNodeList = new ArrayList<>();
            //合同剩余的节点用于计算剩余原始的节点数
            List<NodeInfo> contractNodeList = new ArrayList<>();
            Arrays.stream(hpcdrpdegradecompensationList).forEach(hpcDrpDegradeCompensation -> {
                baseNodeList.addAll(hpcDrpDegradeCompensation.getDegradeBaseNodeInfoList());
                contractNodeList.addAll(hpcDrpDegradeCompensation.getAllContractNodeList());
            });

            long days = 0;
            String orderSn = null;
            log.info("原始节点{},缩容的原始节点{},订单详情：{}", remainNodeList, baseNodeList);

            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                if (orderSn != null && !StringUtils.equals(priceDetail.getOrderSn(), orderSn)) {
                    break;
                }
                //排除补扣的金额
                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                    continue;
                }
                log.info("订单详情：{}", priceDetail);
                //获取被缩容的原始节点数
                long degradeNodeNum = baseNodeList.stream()
                                                  .filter(node -> StringUtils.equalsIgnoreCase(node.getResourceType(),
                                                                                               priceDetail.getProductCode())
                                                          && StringUtils.equalsIgnoreCase(node.getTypeName(),
                                                                                          priceDetail.getServiceType()))
                                                  .count();
                //合同剩余的节点数
                long contractNodeNum = contractNodeList.stream()
                                                       .filter(node -> StringUtils.equalsIgnoreCase(
                                                               node.getResourceType(), priceDetail.getProductCode())
                                                               && StringUtils.equalsIgnoreCase(node.getTypeName(),
                                                                                               priceDetail.getServiceType()))
                                                       .count();
                //实际剩余的节点数
                long remainQuantity = remainNodeList.stream()
                                                    .filter(node -> StringUtils.equalsIgnoreCase(node.getResourceType(),
                                                                                                 priceDetail.getProductCode())
                                                            && StringUtils.equalsIgnoreCase(node.getTypeName(),
                                                                                            priceDetail.getServiceType()))
                                                    .count() - contractNodeNum;

                Integer quantity = priceDetail.getQuantity();
                if (ProductCodeEnum.HPC_DRP.getProductType().equals(priceDetail.getProductCode())
                        || ProductCodeEnum.SFS2.getProductType().equals(priceDetail.getProductCode())
                        || quantity <= remainQuantity) {
                    remainQuantity = quantity;
                }
                log.info("remainQuantity：{}", remainQuantity);
                log.info("degradeNodeNum：{}", degradeNodeNum);
                orderSn = priceDetail.getOrderSn();
                Date startTime = priceDetail.getStartTime();
                Date endTime = priceDetail.getEndTime();
                if (days == 0) {
                    days = DateUtil.betweenDay(startTime, endTime, true);
                }
                log.info("days：{}", days);
                //每日价格
                if (quantity > 0) {
                    basePrice = NumberUtil.add(basePrice, NumberUtil.div(
                        NumberUtil.mul(priceDetail.getOriginalCost(), remainQuantity - degradeNodeNum), quantity));
                }
                log.info("basePrice：{}", basePrice);
            }
            basePrice = NumberUtil.div(basePrice, days);
        }
        return basePrice;
    }

    /**
     * 获取合同剩余节点数
     * @param hpcBizContractDTOS
     * @param type 0 计算节点 1登录节点 2可视化节点 3其他管理节点
     * @return
     */
    @Override
    public int getAllContractNode(List<HpcBizContractDTO> hpcBizContractDTOS, int type) {
        int contractNode = 0;
        //统计扩容计算节点数
        List<Long> openBizContractIdList = new ArrayList<>();
        for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractDTOS) {
            Long contractSid = hpcBizContractDTO.getContractSid();
            if (contractSid == null) {
                contractSid = hpcBizContractDTO.getContractId();
            }
            if (openBizContractIdList.contains(contractSid)) {
                continue;
            }
            openBizContractIdList.add(contractSid);
            Integer remNodeNum = hpcBizContractDTO.getRemNodeNum(type);
            contractNode += remNodeNum;
        }
        return contractNode;
    }

    /**
     * 计算合同补偿
     *
     * @param nodeInfo
     * @param hpcBizContractDTOS
     * @param current
     * @param type
     */
    private BigDecimal computeBizContractAmount(NodeInfo nodeInfo, List<HpcBizContractDTO> hpcBizContractDTOS,
                                                Date current, int type) {
        BigDecimal totalCompensationAmount = null;
        //扩容合同ID
        Long openContractId = null;

        if (CollectionUtil.isNotEmpty(hpcBizContractDTOS)) {
            for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractDTOS) {
                //如果存在扩容合同,就不取其他合同了
                if (openContractId != null) {
                    if (!openContractId.equals(hpcBizContractDTO.getHpcOpenContractId())) {
                        continue;
                    }
                }
                //计算补偿金额
                BigDecimal compensationAmount = hpcBizContractDTO.computeCompensation(current, type);
                if (compensationAmount == null) {
                    continue;
                }
                openContractId = hpcBizContractDTO.getHpcOpenContractId();

                if (totalCompensationAmount == null) {
                    totalCompensationAmount = compensationAmount;
                } else {
                    totalCompensationAmount = NumberUtil.add(totalCompensationAmount, compensationAmount);
                }
                //缩减节点数
                hpcBizContractDTO.addNodeRenewNum(type);
            }
            if (openContractId != null) {
                Optional<BizContract> bizContractOptional =
                        bizContractService.lambdaQuery()
                                          .eq(BizContract::getContractId, openContractId)
                                          .list()
                                          .stream()
                                          .findFirst();
                if (bizContractOptional.isPresent()) {
                    nodeInfo.setStarTime(bizContractOptional.get().getStartTime());
                }
            }
        }
        return totalCompensationAmount;
    }


    private ServiceOrder createDegradeServiceOrder(ServiceOrder applyServiceOrder) {
        Date current = new Date();
        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
        serviceOrder.setName(applyServiceOrder.getName());
        serviceOrder.setType(OrderType.DEGRADE);
        serviceOrder.setOrgSid(applyServiceOrder.getOrgSid());
        serviceOrder.setOwnerId(applyServiceOrder.getOwnerId());
        serviceOrder.setCreatedDt(current);
        serviceOrder.setCreatedBy(applyServiceOrder.getCreatedBy());
        serviceOrder.setUpdatedDt(current);
        serviceOrder.setUpdatedBy(applyServiceOrder.getCreatedBy());
        serviceOrder.setOriginalCost(BigDecimal.ZERO);
        serviceOrder.setPayTime(current);
        serviceOrder.setProcessFlag(applyServiceOrder.getProcessFlag());
        serviceOrder.setStepName(applyServiceOrder.getStepName());
        serviceOrder.setBizBillingAccountId(applyServiceOrder.getBizBillingAccountId());
        serviceOrder.setOrgDiscount(BigDecimal.ZERO);
        serviceOrder.setCouponDiscount(BigDecimal.ZERO);
        serviceOrder.setFinalCost(BigDecimal.ZERO);
        serviceOrder.setCurrAmount(BigDecimal.ZERO);
        serviceOrder.setProductName(applyServiceOrder.getProductName());
        serviceOrder.setAccountName(applyServiceOrder.getAccountName());
        serviceOrder.setSettlementType(applyServiceOrder.getSettlementType());
        serviceOrder.setBehalfUserSid(applyServiceOrder.getBehalfUserSid());
        serviceOrder.setContractId(applyServiceOrder.getContractId());
        serviceOrder.setClusterId(applyServiceOrder.getClusterId());
        serviceOrder.setClusterUuid(applyServiceOrder.getClusterUuid());
        serviceOrder.setEntityId(applyServiceOrder.getEntityId());
        serviceOrder.setEntityName(applyServiceOrder.getEntityName());
        serviceOrder.setStatus(OrderStatus.PENDING);
        // setOrderSourceSn
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectList(
                new QueryWrapper<ServiceOrder>().lambda()
                                                .eq(ServiceOrder::getName, serviceOrder.getName())
                                                .eq(ServiceOrder::getType, OrderType.APPLY)
                                                .eq(ServiceOrder::getClusterId, serviceOrder.getClusterId())
                                                .eq(ServiceOrder::getOwnerId, serviceOrder.getOwnerId()));
        if (serviceOrders.size() > 0) {
            serviceOrder.setOrderSourceSn(serviceOrders.get(0).getOrderSn());
        }
        serviceOrderMapper.insert(serviceOrder);
        return serviceOrder;
    }



    @Override
    public RestResult executeUpgrade(UpgradeServiceRequest request) {
        ApplyServiceRequest applyServiceRequest = new ApplyServiceRequest();
        String productType = request.getProductType();
        Long productResourceId = request.getProductResourceId();
        if (StringUtils.isEmpty(productType)) {
            return new RestResult(RestResult.Status.FAILURE,
                                  cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                          cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
        }
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(productResourceId);
        if (sfProductResource != null) {
            this.checkRemovingNode(sfProductResource.getClusterId());
        } else {
            return new RestResult(RestResult.Status.FAILURE,
                                  cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                          cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!StringUtils.equalsIgnoreCase(sfProductResource.getProductType(), productType)) {
            return new RestResult(RestResult.Status.FAILURE,
                                  cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                          cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //获取productInfo参数
        UpgradeDetailVO upgradeDetailVO = orderServiceFactory.orderService(productType).upgradeDetail(request);
        List<ProductInfoVO> productInfoVOList = upgradeDetailVO.getProductInfoVOList();
        //设置请求参数
        Long contractId = request.getContractId();
        applyServiceRequest.setContractId(contractId);
        applyServiceRequest.setProudctType(productType);
        applyServiceRequest.setResourceId(productResourceId);
        applyServiceRequest.setProductInfo(productInfoVOList);
        applyServiceRequest.setOrderType(OrderType.UPGRADE);

        String errorMessage = orderServiceFactory.orderService(productType).upgrade(applyServiceRequest);
        if (StrUtil.isNotBlank(errorMessage)) {
            return new RestResult(RestResult.Status.FAILURE, errorMessage);
        }
        return null;
    }

    /**
     * HPC续订合同执行节点续订
     *
     * @param contract
     */
    @Override
    public RestResult executeUpgradeByHpcRenewContract(BizContract contract) {
        Long contractSid = contract.getContractSid();
        ServiceOrder upgradeOrder = this.lambdaQuery().eq(ServiceOrder::getContractId, contractSid)
                                        .eq(ServiceOrder::getType, OrderType.UPGRADE)
                                        .eq(ServiceOrder::getStatus, OrderStatus.COMPLETED)
                                        .one();
        Long clusterId = upgradeOrder.getClusterId();
        SfProductResource sfProductResource = sfProductResourceService.lambdaQuery()
                                                                      .eq(SfProductResource::getClusterId, clusterId)
                                                                      .eq(SfProductResource::getProductType,
                                                                          ProductCodeEnum.HPC_DRP.getProductType())
                                                                      .one();

        UpgradeServiceRequest upgradeServiceRequest = new UpgradeServiceRequest();

        upgradeServiceRequest.setContractId(contract.getContractId());
        upgradeServiceRequest.setProductType(ProductCodeEnum.HPC_DRP.getProductType());
        upgradeServiceRequest.setProductResourceId(sfProductResource.getId());
        return this.executeUpgrade(upgradeServiceRequest);
    }

    @Override
    public ServiceOrder selectOrderDetailByResourceId(String id, String mainProductCode) {
        return serviceOrderMapper.selectOrderDetailByResourceId(id,mainProductCode);

    }

    @Override
    public Map<String, Object> productDetail(String id) {
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(id);
        Map<String, Object> detail = Maps.newHashMap();
        if (sfProductResource != null) {
            //获取资源对应的最新的订单详情
            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + sfProductResource.getId() + "\"");
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew"));
            criteria.put("orderStatus", "completed");
            criteria.put("productCodeIn", Lists.newArrayList(sfProductResource.getProductType()));
            criteria.put("isValid", true);
            List<ServiceOrderDetail> serviceOrderDetails = serviceOrderPriceDetailService.selectOrderDetailByCriteria(criteria);
            if (ObjectUtils.isEmpty(serviceOrderDetails)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862986666));
            }
            ServiceOrderDetail applyDetail = CollectionUtil.getFirst(serviceOrderDetails);
            serviceOrderPriceDetailService.setProductConfigDescNodeInfo(applyDetail);
            detail.put("chargeType", applyDetail.getChargeType());
            detail.put("cloudEnvId", sfProductResource.getCloudEnvId());
            detail.put("resourceId", sfProductResource.getId());
            detail.put("serviceId", applyDetail.getServiceId().toString());
            detail.put("productCode", sfProductResource.getProductType());
            serviceOrderDetails.removeIf(orderDetail -> Objects.isNull(orderDetail.getApplyType()));
            if (serviceOrderDetails.size() > 0) {
                detail.put("applyType", serviceOrderDetails.get(0).getApplyType());
            }

            cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(applyDetail.getServiceConfig());
            long month = 0L;
            if (serviceConfig != null) {
                Date startTime = sfProductResource.getStartTime();
                Date endTime = sfProductResource.getEndTime();
                if (startTime != null && endTime != null) {
                    month = DateUtil.betweenMonth(startTime, endTime, true);
                    cn.hutool.json.JSONObject productConfigDescJOB = serviceConfig.getJSONObject("productConfigDesc");
                    Object currentConfigDescJOB = productConfigDescJOB.getObj("currentConfigDesc");
                    if (currentConfigDescJOB != null) {
                        cn.hutool.json.JSONArray descJOB = productConfigDescJOB.getJSONArray("currentDesc");
                        if (descJOB != null) {
                            coinfigMonth(descJOB, month);

                        } else {
                            String descArryStr = currentConfigDescJOB.toString();
                            cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(descArryStr);
                            coinfigMonth(jsonArray, month);
                            productConfigDescJOB.put("currentConfigDesc", JSONArray.toJSONString(jsonArray));
                        }
                    }

                    serviceConfig.put("period", month);
                }
                detail.put("inquiryJson", serviceConfig != null ? serviceConfig.toString() : "");
                detail.put("config", this.getConfig(sfProductResource.getClusterId(), serviceConfig.getByPath("productConfigDesc.currentConfigDesc")));
            }
            detail.put("endTime", DateUtil.format(sfProductResource.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
            detail.put("startTime", DateUtil.format(sfProductResource.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
            detail.put("status", sfProductResource.getStatus());
            Date now = new Date();
            detail.put("period", DateUtil.betweenMonth(now, sfProductResource.getEndTime(), false) + 1);

            if ("frozen".equalsIgnoreCase(sfProductResource.getStatus())) {
                detail.put("frozenTime", DateUtil.format(sfProductResource.getFrozenTime(), "yyyy年MM月dd日 HH:mm:ss"));
                Integer offDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.calculateOffDay(sfProductResource.getEndTime(),
                                                                                                      sfProductResource.getFrozenTime());
                detail.put("makeUpDays", offDay + "天");
            } else {
                if (sfProductResource.getEndTime().before(now)) {
                    Integer offDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.calculateOffDay(sfProductResource.getEndTime(), now);
                    detail.put("makeUpDays", offDay + "天");
                }
            }

            // 现在modelarts专属也有冻结状态
            if (sfProductResource.getEndTime().before(now)) {
                detail.put("now", DateUtil.format(now, "yyyy年MM月dd日 HH:mm:ss"));
            }

            detail.put("projectId", sfProductResource.getOrgSid());
            detail.put("quantity", applyDetail.getQuantity());

            ServiceOrder serviceOrder = serviceOrderService.getById(applyDetail.getOrderId());
            detail.put("name", serviceOrder.getName());
        }
        return detail;
    }

    private String getConfig(Long clusterId, Object currentConfigDesc) {
        if (currentConfigDesc == null) {
            return "";
        }

        String config = JSONUtil.toJsonStr(currentConfigDesc);
        List<BizContractDetail> contractDetails = contractDetailMapper.selectBizContractDetailByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(contractDetails)) {
            int cliNum = 0, agentNum = 0;
            for (BizContractDetail contractDetail : contractDetails) {
                cliNum += Optional.ofNullable(contractDetail.getCliNumberOpen()).orElse(0)  -  Optional.ofNullable(contractDetail.getCliNumberRenew()).orElse(0);
                agentNum += Optional.ofNullable(contractDetail.getAgentNumberOpen()).orElse(0)  -  Optional.ofNullable(contractDetail.getAgentNumberRenew()).orElse(0);
            }

            cn.hutool.json.JSONArray array = JSONUtil.parseArray(config);
            if (cliNum > 0) {
                for (int i = 0; i < array.size(); i++) {
                    cn.hutool.json.JSONObject object = array.getJSONObject(i);
                    if (object.getStr("attrKey").equals(LOGIN_NODE_INFO)) {
                        cn.hutool.json.JSONArray chidrenDesc = object.getJSONArray("chidrenDesc");
                        if (Objects.nonNull(chidrenDesc)) {
                            for (Object data : (cn.hutool.json.JSONArray) chidrenDesc.get(0)) {
                                cn.hutool.json.JSONObject label = (cn.hutool.json.JSONObject) data;
                                if ("节点数目".equals(label.get("label"))) {
                                    Integer value = label.getInt("value");
                                    label.put("value", value - cliNum);
                                    log.info("ResRenewRefServiceImpl.getConfig 登录节点去掉 【{}】 个", cliNum);
                                }
                            }
                        }
                        break;
                    }
                }
            }

            if (agentNum > 0) {
                for (int i = 0; i < array.size(); i++) {
                    cn.hutool.json.JSONObject object = array.getJSONObject(i);
                    if (object.getStr("attrKey").equals(COMPUTE_NODE_INFO)) {
                        cn.hutool.json.JSONArray chidrenDesc = object.getJSONArray("chidrenDesc");
                        if (Objects.nonNull(chidrenDesc)) {
                            for (Object data : (cn.hutool.json.JSONArray) chidrenDesc.get(0)) {
                                cn.hutool.json.JSONObject label = (cn.hutool.json.JSONObject) data;
                                if ("节点数目".equals(label.get("label"))) {
                                    Integer value = label.getInt("value");
                                    label.put("value", value - agentNum);
                                    log.info("ResRenewRefServiceImpl.getConfig 计算节点去掉 【{}】 个", agentNum);
                                }
                            }
                        }
                        break;
                    }
                }
            }

            config = JSONUtil.toJsonStr(array);
        }

        return config;
    }

    @Override
    public List<ServiceOrderVo> selectOrderByCluster(Long clusterId, String productCode) {
        return serviceOrderMapper.selectOrderByCluster(clusterId, productCode);
    }

    @Override
    public void checkPendingOrder(String id, String productCode) {
        Integer count = serviceOrderMapper.selectPendingOrder(id, productCode);
        if (count > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ORDER_PENDING_ERROR));
        }
    }


    @Data
    private static class ConfigDesc {

        private String label;

        private String value;
    }

}
