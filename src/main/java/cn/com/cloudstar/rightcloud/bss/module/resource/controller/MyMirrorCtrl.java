/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.controller;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeSysConfigByTypeListRequest;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.response.DescribeSysConfigResponse;
import cn.com.cloudstar.rightcloud.bss.module.order.service.SystemConfigService;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.MirrorCenterRequest;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.SwrNameSpaceCreateRequest;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.SwrNameSpaceDeleteRequest;
import cn.com.cloudstar.rightcloud.bss.module.resource.request.SwrNameSpaceRequest;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.MirrorCenterService;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.MyMirrorService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.BH.BH08;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.huaweicloud.sdk.swr.v2.model.AuthInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 我的镜像
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/myMirror")
@ApiGroup(ApiGroupEnum.RESOURCE_GROUP)
@Api(tags = "我的镜像")
public class MyMirrorCtrl {

    @Autowired
    MyMirrorService myMirrorService;

    @Autowired
    MirrorCenterService mirrorCenterService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private SysUserService sysUserService;

    /**
     * 我的镜像-镜像列表
     * 【Since v2.5.0】
     */
    @GetMapping("/listImages")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    @ApiOperation("运营控制平台-容器镜像服务")
    @Authorize(action = "myMirror:myMirror:ListMirror")
    public RestResult<List<Object>> listImages(MirrorCenterRequest request) {
        checkModelArts();
        return myMirrorService.listImages(request);
    }

    /**
     * 运营控制平台-镜像中心 【Since v2.5.0】
     *
     * @param request 请求
     *
     * @return {@link RestResult}<{@link List}<{@link Object}>>
     */
    @GetMapping("/mirrorCenter")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    @ApiOperation("运营控制平台-镜像中心")
    public RestResult<List<Object>> mirrorCenter(MirrorCenterRequest request) {
        checkModelArts();
        request.setIs_public(true);
        return mirrorCenterService.listImages(request);
    }

    /**
     * 【Since v2.5.0】 我的镜像-镜像同步
     *
     * @return {@link RestResult}<{@link Long}>
     */
    @PutMapping("/syncImages")
    @ApiOperation("运营控制平台-镜像中心")
    @AuthorizeBss(action = AuthModule.CB.CB2103)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'AICC镜像'",resource = OperationResourceEnum.SYNC_IMAGES, tagNameUs ="'mirror'")
    public RestResult<Long> syncImages() {
        checkModelArts();
        return myMirrorService.syncImages();
    }


    /**
     * 【Since v2.5.0】 我的镜像-版本列表
     *
     * @param request 请求
     *
     * @return {@link RestResult}<{@link List}<{@link Object}>>
     */
    @GetMapping("/listImageVersions")
    @ApiOperation("运营控制平台-版本列表")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    public RestResult<List<Object>> listImageVersions(MirrorCenterRequest request) {
        checkModelArts();
        return myMirrorService.listImageVersions(request);
    }


    /**
     * 【Since v2.5.0】 我的镜像-镜像版本删除
     *
     * @param request 请求
     *
     * @return {@link RestResult}<{@link String}>
     */
    @DeleteMapping("/imageTagDelete")
    @ApiOperation("运营控制平台-镜像版本删除")
    @AuthorizeBss(action = AuthModule.CB.CB2105)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'AICC镜像'",bizId = "#request.id",resource = OperationResourceEnum.IMAGE_TAG_DELETE, tagNameUs ="'mirror'")
    public RestResult<String> imageTagDelete(MirrorCenterRequest request) {
        checkModelArts();
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo.getParentSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_500058829));
        }
        RestResult restResult = myMirrorService.imageTagDelete(request);
        return restResult;
    }


    /**
     * 【Since v2.5.0】 运营控制平台-创建临时登录code
     *
     * @return {@link RestResult}<{@link Map}<{@link String}, {@link AuthInfo}>>
     */
    @GetMapping("/createTempLoginCode")
    @ApiOperation("运营控制平台-创建临时登录code")
    @AuthorizeBss(action = AuthModule.CB.CB2104)
    public RestResult<Map<String, AuthInfo>> createTempLoginCode() {
        checkModelArts();
        return mirrorCenterService.createTempLoginCode();
    }


    /**
     * 【Since v2.5.0】 运营控制平台-获取云环境
     *
     * @return {@link RestResult}<{@link CloudEnv}>
     */
    @GetMapping("/getCloudEnv")
    @ApiOperation("运营控制平台-获取云环境")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    public RestResult<CloudEnv> getCloudEnv() {
        checkModelArts();
        RestResult<CloudEnv> result = mirrorCenterService.getCloudEnv();
        if (result.getStatus()) {
            CloudEnv cloudEnv = JsonUtil.fromJson(JsonUtil.toJson(result.getData()), CloudEnv.class);
            String attrData = cloudEnv.getAttrData();
            Map<String, String> params;
            Map<String, Object> paramObjects = JsonUtil.fromJson(CrytoUtilSimple.decrypt(attrData, true),
                                                                 new TypeReference<Map<String, Object>>() {
                                                                 });
            params = paramObjects.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue() == null ? null : entry.getValue().toString()));

            //脱敏ak sk相关
            params.put("apiKey", "***");
            params.put("secureToken", "***");
            params.put("collectorKey", "***");
            params.put("collectorSecret", "***");
            cloudEnv.setAttrData(JsonUtil.toJson(params));
            result.setData(cloudEnv);
        }

        return result;
    }


    /**
     * 【Since v2.5.0】 运营控制平台-获取组织跳转
     *
     * @return {@link RestResult}<{@link String}>
     */
    @ApiOperation("运营控制平台-获取组织跳转")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    public RestResult<String> getOrgUrl() {
        checkModelArts();
        return mirrorCenterService.getOrgUrl();
    }

    /**
     * 【Since v2.5.0】 运营控制平台-获取组织名称
     *
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/getOrgName")
    @ApiOperation("运营控制平台-获取组织")
    @AuthorizeBss(action = AuthModule.CB.CB21)
    public List<DescribeSysConfigResponse> getOrgName() {
        checkModelArts();
        List<String> configTypes = new ArrayList<>();
        DescribeSysConfigByTypeListRequest request = new DescribeSysConfigByTypeListRequest();
        configTypes.add("mirror_center_config");
        request.setConfigKey("mirror.center.namespace");
        request.setConfigTypes(configTypes);
        cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult restResult =
                systemConfigService.getConfigsByTypeList(request);
        String jsonString = JSON.toJSONString(restResult.getData());
        List<DescribeSysConfigResponse> responses = JSON.parseArray(jsonString,
                DescribeSysConfigResponse.class);
        return responses;
    }

    /**
     * 【Since v2.5.0】
     * 检查ModelArts是否开通
     */
    private void checkModelArts() {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Constants.CONSOLE.equals(authUserInfo.getRemark())) {
            String account = authUserInfo.getAccount();
            if (Objects.nonNull(authUserInfo.getParentSid())) {
                account = sysUserService.selectByPrimaryKey(authUserInfo.getParentSid()).getAccount();
            }
            Criteria criteria = new Criteria();
            criteria.put("product_type", ProductCodeEnum.MODEL_ARTS.getProductType());
            criteria.put("inStatusList", Stream.of("normal", "frozen").collect(Collectors.toList()));
            criteria.put("created_by", account);
            List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
            if (sfProductResources.size() == 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_227346996));
            }
        }
    }


    /**
     * 运营控制平台-获取组织名称
     */
    @GetMapping("/nameSpace")
    @ApiOperation("用户控制平台-获取SWR组织")
    @AuthorizeBss(action = AuthModule.CB.CB21 + "," + BH08.BH08)
    public RestResult queryNameSpace(SwrNameSpaceRequest request) {
        return myMirrorService.getNameSpace(request);
    }

    /**
     * 运营控制平台-同步组织
     */
    @PutMapping("/nameSpace")
    @ApiOperation("用户控制平台-获取SWR组织")
    @AuthorizeBss(action = AuthModule.CB.CB21 + "," + BH08.BH08)
    public RestResult syncNameSpace() {
        myMirrorService.syncNameSpace();
        return new RestResult<>();
    }

    /**
     * 运营控制平台-创建组织
     */
    @PostMapping("/nameSpace")
    @ApiOperation("用户控制平台-创建SWR组织")
    @AuthorizeBss(action = AuthModule.CB.CB21 + "," + BH08.BH08)
    public RestResult createNameSpace(@RequestBody SwrNameSpaceCreateRequest request) {
        return myMirrorService.createNameSpace(request);
    }

    /**
     * 运营控制平台-删除组织
     */
    @DeleteMapping("/nameSpace")
    @ApiOperation("用户控制平台-删除SWR组织")
    @AuthorizeBss(action = AuthModule.CB.CB21 + "," + BH08.BH08)
    public RestResult deleteNameSpace(SwrNameSpaceDeleteRequest request) {
        return myMirrorService.deleteNameSpace(request);
    }

}
