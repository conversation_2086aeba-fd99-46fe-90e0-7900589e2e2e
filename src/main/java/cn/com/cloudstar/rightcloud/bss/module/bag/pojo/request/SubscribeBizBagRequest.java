/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2019/10/16 16:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("订购套餐包列表请求参数")
public class SubscribeBizBagRequest extends BaseRequest {

    /**
     * 名称
     */
    @ApiModelProperty("规格id")
    @NotNull(message = "规格id不能为空")
    private String id;

    /**
     * 优惠券id
     */
    @ApiModelProperty("优惠券id")
    private Long couponId;


    /**
     * 购买数量
     */
    @ApiModelProperty("购买数量")
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    /**
     * 订单类型
     */
    @NotBlank(message = "申请类型不能为空")
    @ApiModelProperty("订单类型,如购买（apply）")
    @EnumValue(strValues = {"apply"})
    private String orderType;

    /**
     * 内部参数传递
     */
    private transient Date startTime;

    /**
     * 账户id
     */
    private Long accountId;
}
