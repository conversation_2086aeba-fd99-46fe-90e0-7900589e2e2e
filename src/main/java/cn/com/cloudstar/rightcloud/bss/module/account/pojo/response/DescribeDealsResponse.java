/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/10/23.
 */
@Data
@ApiModel(description = "查询收支明细")
public class DescribeDealsResponse {

    /**
     * 交易sid
     */
    @ApiModelProperty("SID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dealSid;

    /**
     * 交易编号
     */
    @ApiModelProperty("交易编号")
    private String flowNo;

    /**
     * 云环境类型
     */
    @ApiModelProperty("云环境类型")
    private String envType;

    /**
     * 云环境名称
     */
    @ApiModelProperty("云环境名称")
    private String envName;

    /**
     * 收支类型
     */
    @ApiModelProperty("收支类型 in（收入） out （支出）")
    private String type;

    /**
     * 交易类型
     */
    @ApiModelProperty("交易类型 charge（充值）pay（消费）")
    private String tradeType;

    /**
     * 交易渠道
     */
    @ApiModelProperty("交易渠道 alipay（支付宝）platform（平台）acctCash（用户余额）accCredit（信用额度余额）coupon（优惠卷）")
    private String tradeChannel;

    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    private String tradeNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 账单号
     */
    @ApiModelProperty("账单号")
    private String billNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注 主要为明细针对的产品信息如：云主机，弹性公网IP等")
    private String remark;

    /**
     * 账期
     */
    @ApiModelProperty("账期 账期为年-月格式YYYY-MM")
    private String billingCycle;

    /**
     * 当前金额
     */
    @ApiModelProperty("当前金额")
    private BigDecimal amount;

    /**
     * 余额
     */
    @ApiModelProperty("余额")
    private BigDecimal balance;

    /**
     * 信用额度
     */
    @ApiModelProperty("信用额度")
    private BigDecimal balanceCredit;

    /**
     * 账户sid
     */
    @ApiModelProperty("账户")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accountSid;

    /**
     * 帐户名称
     */
    @ApiModelProperty("账户名称")
    private String accountName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdDt;

    /**
     * 所属分销商
     */
    @ApiModelProperty("所属分销商")
    private String distributorName;

    /**
     * 现金券余额
     */
    @ApiModelProperty("现金券余额")
    private BigDecimal balanceCash;

    /**
     * 当前现金券金额
     */
    @ApiModelProperty("当前现金券金额")
    private BigDecimal cashAmount;

    /**
     * 当前抵扣现金券余额
     */
    @ApiModelProperty("当前抵扣现金券余额")
    private BigDecimal deductBalanceCash;

    /**
     * 订单id
     */
    @ApiModelProperty("orderId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 实体名称
     */
    private String entityName;

    @ApiModelProperty("收费规则")
    private String chargingType;

    /**
     * 收费规则
     */
    @ApiModelProperty("收费规则")
    private String chargingTypeForExcel;

    /**
     * 信用额度消费金额
     */
    @ApiModelProperty("信用额度消费金额")
    private BigDecimal balanceCreditAmount;
    /**
     * 优惠券使用金额
     */
    @ApiModelProperty("优惠券使用金额")
    private BigDecimal couponAmount;

    private String account;

    @ApiModelProperty("自定义信息")
    private String customizationInfo;
}
