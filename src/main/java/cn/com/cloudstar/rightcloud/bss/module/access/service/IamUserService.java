package cn.com.cloudstar.rightcloud.bss.module.access.service;

import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamSubUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserResult;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-09-21 18:28
 * @Desc  iam 用户相关操作
 */
public interface IamUserService {

    /**
     * 是否创建IAM子用户
     * @return
     */
    boolean checkCreateIamSubUser();

    /**
     *
     * HCSO账户追加ModelArts子用户
     * @return 错误信息
     */
    boolean resUserCreate(Long userSid, String account, Long orgSid);

    /**
     *  获取ID用户
     * @param envId 云环境ID
     * @return iam用户列表
     */
    List<IamUserResult> getIamUserInfo(Long envId);

    /**
     * IAM管理员创建IAM子用户
     */
    IamSubUserResult createSubUser(Long envId, Long userSid, String name);

    /**
     * 删除子用户
     *
     * @param envId 云环境ID
     * @param account 子用户名
     * @param userSid
     *
     * @return 删除结果
     */
    boolean deleteSubUser(Long envId, String account, Long userSid);

    /**
     *  修改子用户状态
     * @param userId 用户id
     * @param enabled false-停用 true-启用
     * @return 成功与否
     */
    boolean updateSubUserEnabled(Long userId, Long orgSid, boolean enabled);

    /**
     *  查询华为IAM用户组详情
     */
    IamUserGroupResult getIamUserGroup(Long envId, String groupName, String domainId);

    /**
     * IAM管理员添加IAM用户到用户组
     */
    BaseResult addSubUserToGroup(Long envId, String userId, String groupId);

}
