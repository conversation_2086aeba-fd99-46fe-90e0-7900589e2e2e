/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponDelRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.ResultUtil;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BL.BL01;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 优惠劵 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@RestController
@RequestMapping("/coupons")
@Api("优惠劵管理")
public class BizCouponController {

    @Autowired
    private IBizCouponService bizCouponService;

    /**
     * 管理员创建优惠劵
     *
     * @param createCouponRequest 创建优惠券请求
     * @return {@link RestResult}
     * @since 2.4.1
     */
    @AuthorizeBss(action = BL01.BL0101)
    @ApiOperation("管理员创建优惠劵")
    @PostMapping
    @OperationLog(type = OperationTypeEnum.CREATE, tagName ="'优惠券'", resource = OperationResourceEnum.CREATECOUPON, tagNameUs ="'Coupon'")
    @Idempotent
    @ListenExpireBack
    public RestResult createCoupon(@RequestBody @Valid CreateCouponRequest createCouponRequest) {
        return ResultUtil.ofInsert(bizCouponService.createCoupon(createCouponRequest), createCouponRequest.getCouponSid());
    }

    /**
     * 管理员查询优惠劵
     *
     * @param couponRequest 优惠券请求
     * @return {@link IPage}<{@link DescribeCouponResponse}>
     * @since 2.4.1
     */
    @AuthorizeBss(action = BL01.BL01_COUPON_COMMON)
    @ApiOperation("管理员查询优惠劵")
    @GetMapping
    @ListenExpireBack
    public IPage<DescribeCouponResponse> listCoupons(@Valid DescribeCouponRequest couponRequest) {
        return bizCouponService.listCoupons(couponRequest);
    }

    /**
     * 管理员分发优惠劵
     *
     * @param sid        ID
     * @param accountIds 帐户ID
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = BL01.BL0102)
    @ApiOperation("管理员分发优惠劵")
    @PutMapping("/{sid}/distribute")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'优惠券'", resource = OperationResourceEnum.DISTRIBUTECOUPON, tagNameUs ="'Coupon'", 
            bizId = "#sid", param ="#accountIds" )
    @Idempotent
    @ListenExpireBack
    public RestResult distributeCoupon(@PathVariable("sid") Long sid,
                                       @RequestBody List<Long> accountIds) {
        return ResultUtil.ofOperate(bizCouponService.distributeCoupon(sid, accountIds));
    }

    /**
     * 管理员作废优惠劵
     *
     * @param sid    ID
     * @param remark 备注
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = BL01.BL0103)
    @ApiOperation("管理员作废优惠劵")
    @PutMapping("/{sid}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'优惠券'", resource = OperationResourceEnum.CANCELCOUPON, bizId = "#sid", tagNameUs ="'Coupon'")
    @ListenExpireBack
    public RestResult deleteCouponByLogic(@PathVariable("sid") Long sid,
                                          @RequestBody @Valid DescribeCouponDelRequest describeCouponDelRequest) {
        if(Objects.isNull(describeCouponDelRequest.getRemark()) ||
                "".equals(describeCouponDelRequest.getRemark())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_257552465));
        }
        return ResultUtil.ofOperate(bizCouponService.deleteCouponByLogic(sid, describeCouponDelRequest));
    }

    /**
     * 管理员查询某张优惠劵下的领取账户
     *
     * @param couponAccountRequest 优惠券帐户请求
     * @return {@link IPage}<{@link DescribeCouponAccountResponse}>
     * @since 2.4.1
     */
    @AuthorizeBss(action = BL01.BL01_COUPON_COMMON)
    @ApiOperation("管理员查询某张优惠劵下的领取账户")
    @GetMapping("/{sid}")
    public IPage<DescribeCouponAccountResponse> listCouponAccounts(@Valid DescribeCouponAccountRequest couponAccountRequest) {
        IPage<DescribeCouponAccountResponse> describeCouponAccountResponseIPage = bizCouponService.listCouponAccounts(couponAccountRequest);
        DesensitizationUtil.desensitization(describeCouponAccountResponseIPage.getRecords());
        return describeCouponAccountResponseIPage;
    }
}

