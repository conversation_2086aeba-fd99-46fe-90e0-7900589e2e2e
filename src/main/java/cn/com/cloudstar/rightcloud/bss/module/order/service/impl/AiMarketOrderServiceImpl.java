/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopLicenseCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopLicenseCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopSubscribesCreatResult;
import cn.com.cloudstar.rightcloud.basic.data.dao.user.BasicUserMapper;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.MarketSuperviseStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.MarketInquiryPriceBase;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductConfigDesc;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.SkuEnumDto;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MarketService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.market.mapper.MarketShopSuperviseRecordMapper;
import cn.com.cloudstar.rightcloud.bss.module.market.pojo.MarketShop;
import cn.com.cloudstar.rightcloud.bss.module.market.pojo.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.bss.module.market.pojo.MarketShopSuperviseRecord;
import cn.com.cloudstar.rightcloud.bss.module.market.service.MarketShopPriceJoinService;
import cn.com.cloudstar.rightcloud.bss.module.market.service.MarketShopService;
import cn.com.cloudstar.rightcloud.bss.module.market.service.MarketShopSubscribeService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.response.MarketInquiryPriceResp;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.util.BaseMockUtil;
import cn.com.cloudstar.rightcloud.bss.module.order.util.BaseUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.SellTypeEnum;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostSplitItem;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants.OrderMessage;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.BillType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.DealType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.PriceType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.TradeType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.IdWorker;
import cn.com.cloudstar.rightcloud.oss.common.util.NoUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.bson.types.ObjectId;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.PRODUCT;
import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.MONTH_PATTERN;

/**
 * impl 模型集市服务
 *
 * <AUTHOR>
 * @date 2023/08/07
 */
@Service
@Slf4j
public class AiMarketOrderServiceImpl extends AbstractOrderService {

    private static final String APPROVAL = "online";

    /**
     * 销售计费
     */
    private static final String SALE_TYPE = "02";

    /**
     * 正常计费
     */
    private static final String NORMAL_TYPE = "01";

    private final static String UNLIMITED = "unlimited";

    private final static String UNDELIVERED = "undelivered";

    /**
     * 已结算
     */
    private static final String SETTLED = "settled";

    /**
     * 未结算
     */
    private static final String UNSETTLED = "unsettled";

    /**
     * 供应商
     */
    private static final String SUPPLIER = "supplier";

    private static final IdWorker ID_WORKER = new IdWorker();
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private OrderService orderService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private BasicUserMapper basicUserMapper;

    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;

    @Autowired
    private MarketShopService marketShopService;

    @Autowired
    private MarketService marketService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private MarketShopSubscribeService shopSubscribeService;
    @Autowired
    private IBizAccountDealService bizAccountDealService;
    @Resource
    private MarketShopSuperviseRecordMapper shopSuperviseRecordMapper;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MarketShopPriceJoinService priceJoinService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String apply(ApplyServiceVO serviceVO) {

        ApplyEntity applyEntity = before();

        transformSpec(serviceVO, applyEntity);

        validateSpec(serviceVO);

        ((AiMarketOrderServiceImpl ) AopContext.currentProxy()).execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);

        after(applyEntity);

        bill(serviceVO, applyEntity);
        return result;
    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (CollectionUtil.isEmpty(serviceVO.getProductInfo())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_VALID_FAILURE));
        }

        cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult restResult = orderService.checkAuth(authUserInfo.getUserSid());
        if (!restResult.getStatus()) {
            throw new BizException(WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_VERIFIED_ERROR));
        }

        cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult freeze = orderService.isFreeze(authUserInfo.getUserSid());
        if (Convert.toBool(freeze.getData())) {
            throw new BizException(WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_STATUS_ERROR));
        }

        if (authUserInfo.getParentSid() != null) {
            throw new BizException(WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_ERROR));
        }

        ProductInfoVO marketInfo = CollectionUtil.getFirst(serviceVO.getProductInfo());
        String shopId = marketInfo.getShopId();
        MarketShop shop = marketShopService.getById(shopId);
        if (Objects.isNull(shop)){
            throw new BizException(WebUtil.getMessage(String.format(MsgCd.ERROR_RES_NOT_FOUND, "商品")));
        }
        if (authUserInfo.getUserSid().equals(shop.getOwnerId())){
            throw new BizException(WebUtil.getMessage(MsgCd.CANNOT_BUY_YOUR_OWN_PRODUCTS));
        }
        String status = shop.getStatus();
        if (!APPROVAL.equals(status)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_NOT_APPROVAL));
        }
    }

    @Override
    public void inquiryPrice(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
        productInfoVO.setOrderType("shop");
        productInfoVO.setSuperviseStatus(UNDELIVERED);

        MarketInquiryPriceBase baseReq = BeanUtil.copyProperties(productInfoVO, MarketInquiryPriceBase.class);
        RestResult restResult = marketService.price(baseReq);
        Object data = restResult.getData();
        MarketInquiryPriceResp priceResp = BeanUtil.copyProperties(data, MarketInquiryPriceResp.class);

        List<InquiryPriceResponse> prices = new ArrayList<>();
        InquiryPriceResponse inquiryPriceResponse = new InquiryPriceResponse();
        inquiryPriceResponse.setTradePrice(priceResp.getAmount());
        inquiryPriceResponse.setDiscountPrice(priceResp.getDiscountAmount());
        inquiryPriceResponse.setOriginalPrice(priceResp.getOriginalAmount());
        inquiryPriceResponse.setProductCode("AI-MARKET");
        inquiryPriceResponse.setSkuName(priceResp.getSkuName());
        BizBillingPriceVO bizBillingPriceVO = new BizBillingPriceVO();
        inquiryPriceResponse.getBillingPrices().add(bizBillingPriceVO);
        bizBillingPriceVO.setOriginalPrice(priceResp.getOriginalAmount());
        bizBillingPriceVO.setOncePrice(priceResp.getOriginalAmount());
        bizBillingPriceVO.setPlatformDiscount(priceResp.getPlatformDiscount());
        bizBillingPriceVO.setPriceType("service");


        prices.add(inquiryPriceResponse);
        applyEntity.setPrices(prices);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        Date finalEndTime;
        String payType;

        if (Objects.nonNull(serviceVO.getPriceJoinId())){
            ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
            MarketShop shop = marketShopService.getById(productInfoVO.getShopId());
            Long priceJoinId = serviceVO.getPriceJoinId();
            MarketShopPriceJoin shopPriceJoin = priceJoinService.getById(priceJoinId);
            Assert.notNull(shopPriceJoin,"参数错误");
            AiShopSubscriptionsCreate subscriptionsCreate = new AiShopSubscriptionsCreate();
            subscriptionsCreate.setContent_id(shopPriceJoin.getContentId());
            AiShopLicenseCreate aiShopLicenseCreate = new AiShopLicenseCreate();
            aiShopLicenseCreate.setContent_id(shopPriceJoin.getContentId());
            String flag = System.getenv("LARGE_MODEL_MOCK");
            if("true".equals(flag)){
                BaseMockUtil.extracted(subscriptionsCreate);
                BaseMockUtil.extracted(aiShopLicenseCreate);
            }else{
                BaseUtil.extracted(subscriptionsCreate);
                BaseUtil.fillProjectId(subscriptionsCreate, shop.getOwnerId());
                BaseUtil.extracted(aiShopLicenseCreate);
                BaseUtil.fillProjectId(aiShopLicenseCreate, shop.getOwnerId());
            }
            AiShopSubscribesCreatResult result = null;

            try {
                log.info("订阅资产参数信息：{}", subscriptionsCreate);
                result = BeanConvertUtil.convert(MQHelper.rpc(subscriptionsCreate), AiShopSubscribesCreatResult.class);
                log.info("订阅资产返回信息：{}", result);
                Assert.isTrue(result.isSuccess(),result.getErrMsg());
            } catch (MQException e) {
                log.error("订阅资产中-订阅资产失败：", e);
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
            }

            try {
                log.info("AI资产许可证创建参数信息：{}", aiShopLicenseCreate);
                AiShopLicenseCreateResult licenseResult = BeanConvertUtil.convert(MQHelper.rpc(aiShopLicenseCreate), AiShopLicenseCreateResult.class);
                log.info("AI资产许可证创建返回信息：{}", licenseResult);
                Assert.isTrue(licenseResult.isSuccess(),licenseResult.getErrMsg());
            } catch (MQException e) {
                log.error("AI资产许可证创建失败：", e);
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
            }

            // 保存
            MarketShopSubscribe shopSubscribe = BeanUtil.toBean(shopPriceJoin, MarketShopSubscribe.class);
            ServiceOrder order = applyEntity.getOrder();
            shopSubscribe.setOrderId(Long.valueOf(order.getId()));
            shopSubscribe.setShopId(productInfoVO.getShopId());
            shopSubscribe.setShopOwnerId(shop.getOwnerId());
            shopSubscribe.setOwnerId(RequestContextUtil.getAuthUserInfo().getUserSid());
            shopSubscribe.setSellType(productInfoVO.getSellType());
            shopSubscribe.setPriceJoinId(shopPriceJoin.getId());
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(shopPriceJoin.getUnitValue()).append(shopPriceJoin.getUnit());
            shopSubscribe.setUnit(stringBuilder.toString());
            shopSubscribe.setUnitValue(shopPriceJoin.getUnitValue());
            shopSubscribe.setAiHubSubscribeId(result.getSubscription_id());
            shopSubscribe.setAiHubContentId(shopPriceJoin.getContentId());
            BasicWebUtil.prepareInsertParams(shopSubscribe);
            shopSubscribe.setStartTime(new Date());
            //数量定死了是1
            Date date = new Date();
            Calendar endTime = Calendar.getInstance();
            endTime.setTime(date);
            switch (shopPriceJoin.getUnit()) {
                case "年":
                    endTime.add(Calendar.YEAR, shopPriceJoin.getUnitValue());
                    break;
                case "月":
                    endTime.add(Calendar.MONTH, shopPriceJoin.getUnitValue());
                    break;
                case "日":
                    endTime.add(Calendar.DATE, shopPriceJoin.getUnitValue());
                    break;
                default:
                    break;
            }
            finalEndTime = endTime.getTime();
            // 需要计算
            if (isUnlimited(shop)) {
                finalEndTime = null;
            }
            shopSubscribe.setEndTime(finalEndTime);

            payType = shop.getPayType();

            if(SUPPLIER.equals(shop.getShopSource())){
                shopSubscribe.setSettlementStatus(UNSETTLED);
            }else{
                shopSubscribe.setSettlementStatus(SETTLED);
            }

            shopSubscribe.setSuperviseStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
            shopSubscribe.setRemark(serviceVO.getRemark());
            //如果入库失败需要取消订阅抛异常然后移除白名单
            try{
                shopSubscribeService.save(shopSubscribe);

                MarketShopSuperviseRecord record = new MarketShopSuperviseRecord();
                record.setSubscribeId(shopSubscribe.getSubscribeId());
                record.setUpdateStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
                record.setCreatedUserId(RequestContextUtil.getAuthUserInfo().getUserSid());
                WebUserUtil.prepareInsertParams(record);
                shopSuperviseRecordMapper.insert(record);
            }catch (Exception e){
                log.error("aihub商品订阅入库失败：", e);
                AiShopSubscriptionsDelete subscriptionsDelete = new AiShopSubscriptionsDelete();
                subscriptionsDelete.setSubscription_id(shopSubscribe.getAiHubSubscribeId());
                String mockFlag = System.getenv("LARGE_MODEL_MOCK");
                if("true".equals(mockFlag)){
                    BaseMockUtil.extracted(subscriptionsDelete);
                }else{
                    BaseUtil.extracted(subscriptionsDelete);
                }

                try {
                    log.info("取消订阅资产参数信息：{}", subscriptionsDelete);
                    BaseResult baseResult = BeanConvertUtil.convert(MQHelper.rpc(subscriptionsDelete), BaseResult.class);
                    log.info("取消订阅资产返回信息：{}", baseResult);
                    Assert.isTrue(baseResult.isSuccess(),baseResult.getErrMsg());
                } catch (MQException mqException) {
                    log.error("取消订阅失败：", e);
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
                }
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));

            }

        }else{
            ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
            MarketShopSubscribe subscribe = new MarketShopSubscribe();
            ServiceOrder order = applyEntity.getOrder();
            subscribe.setOrderId(Long.valueOf(order.getId()));
            subscribe.setShopId(productInfoVO.getShopId());
            MarketShop shop = marketShopService.getById(productInfoVO.getShopId());
            subscribe.setShopOwnerId(shop.getOwnerId());
            subscribe.setOwnerId(RequestContextUtil.getAuthUserInfo().getUserSid());

            String periodType = productInfoVO.getPeriodType();
            subscribe.setSellType(SellTypeEnum.fromDesc(periodType));

            InquiryPriceResponse inquiryPriceResponse = applyEntity.getPrices().get(0);
            subscribe.setUnit(inquiryPriceResponse.getSkuName());
            subscribe.setPrice(inquiryPriceResponse.getTradePrice());
            subscribe.setCreatedDt(new Date());
            subscribe.setCreatedBy(RequestContextUtil.getAuthUserInfo().getAccount());
            subscribe.setSuperviseStatus(UNDELIVERED);
            subscribe.setRemark(serviceVO.getRemark());
            // 计算订阅结束时间
            Date endTime = null;
            if (periodType.equals(SellTypeEnum.YEAR.getDesc())) {
                endTime = DateUtil.offset(subscribe.getCreatedDt(), DateField.YEAR, productInfoVO.getAmount());
            } else if (periodType.equals(SellTypeEnum.MONTH.getDesc())) {
                endTime = DateUtil.offset(subscribe.getCreatedDt(), DateField.MONTH, productInfoVO.getAmount());
            }
            if (isUnlimited(shop)) {
                endTime = null;
            }
            subscribe.setEndTime(endTime);
            finalEndTime = endTime;
            shopSubscribeService.save(subscribe);
            payType = shop.getPayType();


            MarketShopSuperviseRecord record = new MarketShopSuperviseRecord();
            record.setSubscribeId(subscribe.getSubscribeId());
            record.setUpdateStatus(UNDELIVERED);
            record.setCreatedUserId(RequestContextUtil.getAuthUserInfo().getUserSid());
            WebUserUtil.prepareInsertParams(record);
            shopSuperviseRecordMapper.insert(record);
        }
        // 修改订单详情到期时间
        List<Long> orderDetails = Optional.ofNullable(applyEntity.getOrderDetails()).orElse(new ArrayList<>())
                .stream().map(ServiceOrderDetail::getId).collect(Collectors.toList());
        if (!orderDetails.isEmpty()) {
            serviceOrderDetailMapper.update(null,
                    new LambdaUpdateWrapper<ServiceOrderDetail>()
                            .set(ServiceOrderDetail::getEndTime, finalEndTime)
                            .set(ServiceOrderDetail::getChargeType, payType)
                            .in(ServiceOrderDetail::getId, orderDetails)
            );
        }


        return null;
    }

    /**
     * 是不限时
     *
     * @param shop 店
     * @return boolean
     */
    private static boolean isUnlimited(MarketShop shop) {
        return !(Objects.equals(shop.getPayType(), "month") || Objects.equals(shop.getPayType(), "year"));
    }

    @Override
    public void after(ApplyEntity applyEntity) {
        ServiceOrder order = applyEntity.getOrder();
        LambdaQueryWrapper<MarketShopSubscribe> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketShopSubscribe::getOrderId, order.getId());
        List<MarketShopSubscribe> subscribes = shopSubscribeService.getBaseMapper().selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(subscribes)) {
            MarketShopSubscribe subscribe = subscribes.get(0);
            //大模型 不需要通知
            if (Objects.nonNull(subscribe.getPriceJoinId())){
                notification(subscribe, NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_SUBSCRIBE_SUCCESS);
                return;
            }
            // 商品订阅待交付通知
            notification(subscribe, NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_PENDING_DELIVERY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.execute(serviceVO, applyEntity);
    }

    public void notification(MarketShopSubscribe subscribe, String msg) {
        String shopId = subscribe.getShopId();
        MarketShop shop = marketShopService.getById(shopId);
        if(Objects.isNull(shop)){
            throw new BizException(WebUtil.getMessage(String.format(MsgCd.ERROR_RES_NOT_FOUND, "商品")));
        }
        User supplierUser = userMapper.selectByPrimaryKey(shop.getOwnerId());
        User subscribeUser = userMapper.selectByPrimaryKey(subscribe.getOwnerId());
        if (Objects.isNull(subscribeUser) || Objects.isNull(supplierUser)){
            throw new BizException(WebUtil.getMessage(String.format(MsgCd.SHOP_SUPPLIER_DOES_NOT_EXIST)));
        }
        long entityId = 1;

        //封装模板参数
        HashMap<String, String> messageContent = new HashMap<>();
        messageContent.put("subscribeAccount", subscribeUser.getAccount());
        messageContent.put("supplierAccount", supplierUser.getAccount());
        messageContent.put("subId", subscribe.getSubscribeId());
        messageContent.put("shopTitle", shop.getTitle());
        messageContent.put("time", DateUtil.format(subscribe.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"));
        messageContent.put("shopId", subscribe.getShopId());

        //区分管理端与客户端
        Long supplierSid = supplierUser.getUserSid();
        Set<String> roleId = basicUserMapper.selectRoleByuserId(supplierSid);
        if (roleId.contains("301")) {
            messageContent.put("url", "#/appmodel/aimarket/subscribe/" + subscribe.getSubscribeId());
        } else {
            messageContent.put("url", "#/appmodel/subscribe/" + subscribe.getSubscribeId());
        }

        //通知供应商
        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.setMsgId(msg);
        baseNotificationMqBean.getToUserIds().add(supplierSid);
        baseNotificationMqBean.setMap(messageContent);
        baseNotificationMqBean.setEntityId(entityId);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, PRODUCT, baseNotificationMqBean);
    }

    private void bill(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ServiceOrderDetail detail = CollectionUtil.getFirst(applyEntity.getOrderDetails());
        log.info("NotifyListener-billing-detail:[]",JSONUtil.toJsonStr(detail));
        if (BillingConstants.ChargeType.POST_PAID.equals(detail.getChargeType())) {
            return;
        }
        ServiceOrder serviceOrder = applyEntity.getOrder();
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
                serviceOrder.getBizBillingAccountId());
        if (ObjectUtils.isEmpty(bizBillingAccount)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException("未找到资源");
        }
        Criteria criteria = new Criteria();
        criteria.put("orderDetailId", detail.getId());
        List<ServiceOrderPriceDetail>  serviceOrderPriceDetails = applyEntity.getPriceDetails();
        List<InstanceGaapCost> costs = Lists.newArrayList();

        // detail.getQuantity() = 1
        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            InstanceGaapCost cost = getBaseCost(serviceOrder, priceDetail);
            // originalCost 原价
            BigDecimal originalCost = priceDetail.getOriginalCost();
            // tradeFinalCost 最终价格
            BigDecimal finalCost = priceDetail.getAmount();
            cost.setPricingDiscount(priceDetail.getDiscount());
            BigDecimal couponAmount = priceDetail.getCouponAmount();
            cost.setCouponDiscount(couponAmount);

            //关联不计费产品设置实付金额为0
            if(Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equals(serviceOrder.getChargingType())){
                cost.setCashAmount(BigDecimal.ZERO);
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                finalCost = BigDecimal.ZERO;
                cost.setChargingType(SALE_TYPE);
            }else {
                cost.setChargingType(NORMAL_TYPE);
                //设置使用金额
                setUsedCost(bizBillingAccount, cost, finalCost);

                log.info("扣款费用：{}", NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setCreditLine(
                        NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
                bizBillingAccount.setBalanceCash(
                        NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            }
            cost.setPretaxGrossAmount(originalCost);
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount.class));
            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            log.info("所属运营实体id：{}，name：{}",serviceOrder.getEntityId(),serviceOrder.getEntityName());
            costs.add(cost);
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            serviceOrderPriceDetailMapper.updateById(priceDetail);
        }
        log.info("扣费账户：{},账户余额：{}",bizBillingAccount,bizBillingAccount.getBalance());
        bizBillingAccountMapper.updateById(bizBillingAccount);
        // 订单支付成功，修改订单状态
        serviceOrderMapper.update(null, new LambdaUpdateWrapper<ServiceOrder>()
                .set(ServiceOrder::getPayTime, new Date())
                .eq(ServiceOrder::getId, serviceOrder.getId()));
        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        insertCostAndCycleAndDeal(serviceOrder, costs, serviceVO);
    }

    private InstanceGaapCost getBaseCost(ServiceOrder serviceOrder, ServiceOrderPriceDetail priceDetail) {
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getOrderDetails());
        InstanceGaapCost cost = new InstanceGaapCost();

        cost.setOrderId(serviceOrder.getId().toString());
        cost.setOrderSn(serviceOrder.getOrderSn());
        cost.setPayTime(new Date());
        cost.setUsageStartDate(detail.getStartTime());
        cost.setUsageEndDate(detail.getEndTime());
        String billType = BillType.fromChargeTypeEnum(ChargeTypeEnum.OneTime.getType().equals(detail.getChargeType()) ? "PrePaid" : detail.getChargeType());
        cost.setBillType(billType);
        cost.setBillingCycle(DateUtil.format(DateUtil.date(), "yyyy-MM"));
        cost.setOrgSid(serviceOrder.getOrgSid());
        cost.setOwnerId(serviceOrder.getOwnerId().toString());
        cost.setBillNo(NoUtil.generateNo("ZD"));
        cost.setCurrency("CNY");
        cost.setPriceType(priceDetail.getPriceType());
        cost.setProductCode(detail.getServiceType());
        String productName = ProductCodeEnum.toDesc(detail.getServiceType());
        log.info("所属产品Code：[{}]", detail.getServiceType());
        if (ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())) {
            productName = serviceOrder.getProductName();
            log.info("HPC_DRP 所属产品名称：[{}]", productName);
        }
        cost.setProductName(productName);
        cost.setBillSource("platform");
        cost.setUserAccountId(serviceOrder.getBizBillingAccountId());
        cost.setUserAccountName(serviceOrder.getCreatedBy());
        cost.setPrice(priceDetail.getPrice());
        cost.setConfiguration(priceDetail.getBillingSpec());
        cost.setType(serviceOrder.getType());
        cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
        cost.setChargingType(serviceOrder.getChargingType());

        JSONObject data = JSON.parseObject(detail.getServiceConfig());
        if (data != null) {
            Long cloudEnvId = data.getLong("cloudEnvId");
            if (cloudEnvId != null) {
                cost.setCloudEnvId(cloudEnvId);
            }
            if (Objects.equals(OrderType.APPLY,serviceOrder.getType())){
                long period = LocalDate.now().plusMonths(detail.getDuration()).toEpochDay() - LocalDate.now().toEpochDay();
                cost.setUsageCount(period + "天");
            }else if( Objects.equals(OrderType.RENEW,serviceOrder.getType())){
                Integer offDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(cost.getUsageStartDate(),cost.getUsageEndDate(), false);
                cost.setUsageCount(offDay + "天");
            }else if (OrderType.MODIFY.equals(serviceOrder.getType())) {
                Integer offDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(new Date(), cost.getUsageEndDate(), true);
                cost.setUsageCount(offDay + "天");
            }
        }

        return cost;
    }

    /**
     * 设置使用成本
     *
     * @param bizBillingAccount 商务结算账户
     * @param cost              成本
     * @param finalCost         最终成本
     */
    public void setUsedCost(BizBillingAccount bizBillingAccount, InstanceGaapCost cost,
                            BigDecimal finalCost) {
        cost.setCashAmount(BigDecimal.ZERO);
        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setCouponAmount(BigDecimal.ZERO);
        cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            finalCost = BigDecimalUtil.remainTwoPointAmount(finalCost);
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()), BigDecimal.ZERO),
                    finalCost)) {
                cost.setCouponAmount(finalCost);
            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                cost.setCouponAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()), BigDecimal.ZERO),
                        overCost)) {
                    cost.setCashAmount(overCost);
                } else {
                    if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) >= 0) {
                        overCost = NumberUtil.sub(overCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                        cost.setCashAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                    }
                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()), BigDecimal.ZERO),
                            overCost)) {
                        if(bizBillingAccount.getCreditLineDt()!= null && !"".equals(bizBillingAccount.getCreditLineDt())){
                            //信用额度指定时间过期，未到期
                            if(!UNLIMITED.equals(bizBillingAccount.getCreditLineDt())
                                    && Long.parseLong(bizBillingAccount.getCreditLineDt()) > System.currentTimeMillis()){
                                cost.setCreditAmount(overCost);
                            }else if(UNLIMITED.equals(bizBillingAccount.getCreditLineDt())){
                                cost.setCreditAmount(overCost);
                            }else{
                                cost.setCreditAmount(new BigDecimal(0));
                            }
                        }else {
                            cost.setCreditAmount(new BigDecimal(0));
                        }
                    } else {
                        if(bizBillingAccount.getCreditLineDt()!= null && !"".equals(bizBillingAccount.getCreditLineDt())){
                            //信用额度指定时间过期，未到期
                            if(!UNLIMITED.equals(bizBillingAccount.getCreditLineDt())
                                    && Long.parseLong(bizBillingAccount.getCreditLineDt()) > System.currentTimeMillis()){
                                cost.setCreditAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()));
                            }else if(UNLIMITED.equals(bizBillingAccount.getCreditLineDt())){
                                cost.setCreditAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()));
                            }else{
                                cost.setCreditAmount(new BigDecimal(0));
                            }
                        }else {
                            cost.setCreditAmount(new BigDecimal(0));
                        }
                        overCost = NumberUtil.sub(overCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()));
                        cost.setCashAmount(cost.getCashAmount().add(overCost));
                        log.warn(StrUtil.format("[{}]余额不足，欠费扣款，金额[{}]", bizBillingAccount.getAccountName(), overCost));

                    }
                }
            }
        } else {
            cost.setCashAmount(finalCost);
        }
    }

    /**
     * 更新用户业务标识Tag
     *
     * @param bizBillingAccount 商务结算账户
     */
    private void updateUserBusinessTag(BizBillingAccount bizBillingAccount) {
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())){
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())){
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())){
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                }else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(StringUtils.join(tagList,";"));
            }else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userMapper.updateByPrimaryKeySelective(user);
        }else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())){
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())){
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())){
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (CollectionUtils.isEmpty(accountIdList)){
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(StringUtils.join(tagList,";"));
                    userMapper.updateByPrimaryKeySelective(user);
                }
            }
        }
    }


    /**
     * 改变规格
     *
     * @param serviceVO   服务签证官
     * @param applyEntity 应用实体
     */
    private void transformSpec(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
        ProductConfigDesc productConfigDesc = new ProductConfigDesc();
        productInfoVO.setProductConfigDesc(productConfigDesc);

        List<SkuEnumDto> skuInfo = productInfoVO.getSkuInfo();
        List<JSONObject> jsonObjects = new ArrayList<>();
        if (CollUtil.isNotEmpty(skuInfo)) {
            for (SkuEnumDto sku : skuInfo) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("label", sku.getAttrName());
                if ("quantity".equals(sku.getType())) {
                    Integer periodNum = sku.getPeriodNum();
                    String unit = sku.getUnit();
                    jsonObject.put("value", periodNum + unit);
                } else {
                    jsonObject.put("value", sku.getEnumValue());
                }
                jsonObjects.add(jsonObject);
            }
        }
        String toString = jsonObjects.toString();
        productConfigDesc.setCurrentConfigDesc(toString);

        //替换组织名称
        serviceVO.setProjectId(AuthUtil.getAuthUser().getOrgSid());
    }

    /**
     * 插入账单和交易成本和周期
     *
     * @param serviceOrder 服务订单
     * @param costs        成本
     * @param orderIds     订单id
     * @return boolean
     */
    public boolean insertCostAndCycleAndDeal(ServiceOrder serviceOrder,
                                             List<InstanceGaapCost> costs, ApplyServiceVO serviceVO, String... orderIds) {
        try {
            if (ArrayUtil.isNotEmpty(orderIds)) {
                Query query = new Query();
                query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.
                        where("orderId").in(orderIds));
                query.with(Sort.by(Sort.Direction.DESC, "usageStartDate"));
                List<InstanceGaapCost> excludeReleaseCosts = mongoTemplate.find(query, InstanceGaapCost.class);
                log.info("ServiceOrderServiceImpl-insertCostAndCycleAndDeal-excludeReleaseCosts-size:[{}]",excludeReleaseCosts.size());
                costs.forEach(releaseCost -> {
                    // 插入订单来源
                    releaseCost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
                    log.info("ServiceOrderServiceImpl-insertCostAndCycleAndDeal-releaseCost:[{}]",JSONUtil.toJsonStr(releaseCost));
                    BigDecimal releaseAmount = releaseCost.getPretaxAmount();
                    Range<Date> dateRange = Range.closed(releaseCost.getUsageStartDate(),
                            releaseCost.getUsageEndDate());
                    for (InstanceGaapCost cost : excludeReleaseCosts) {
                        if (Objects.equals(releaseCost.getPriceType(), cost.getPriceType())
                                && dateRange.contains(cost.getUsageEndDate())) {
                            Query queryCycle = new Query();
                            Update updateCycle = new Update();
                            queryCycle.addCriteria(org.springframework.data.mongodb.core.query.Criteria.
                                    where("_id")
                                    .is(new ObjectId(cost.getBillBillingCycleId())));
                            BigDecimal payAmount = cost.getPretaxAmount();
                            if (!dateRange.contains(cost.getUsageStartDate())) {
                                Query queryItem = new Query();
                                Update updateItem = new Update();
                                queryItem.addCriteria(org.springframework.data.mongodb.core.query.Criteria.
                                        where("_id")
                                        .is(new ObjectId(cost.getMongoId())));
                                updateItem.set("orderStatus", OrderStatus.RELEASE_SUCCESS);
                                mongoTemplate.findAndModify(queryItem, updateItem, InstanceGaapCost.class);
                                updateCycle.set("billEndTime", releaseCost.getUsageStartDate());
                                if (NumberUtil.isLess(releaseAmount, BigDecimal.ZERO) && NumberUtil.isGreater(payAmount,
                                        BigDecimal.ZERO)) {
                                    releaseAmount = releaseAmount.add(payAmount);
                                    updateCycle.set("releaseAmount",
                                            NumberUtil.isLess(releaseAmount, BigDecimal.ZERO) ? payAmount
                                                    : releaseAmount.subtract(payAmount).abs());
                                    UpdateResult billingCycle = mongoTemplate.updateMulti(queryCycle, updateCycle,
                                            "biz_bill_billing_cycle");
                                }
                            } else {
                                releaseAmount = releaseAmount.add(payAmount);
                                updateCycle.set("releaseAmount", payAmount);
                                UpdateResult billingCycle = mongoTemplate.updateMulti(queryCycle, updateCycle,
                                        "biz_bill_billing_cycle");
                            }
                        }
                    }
                });
            }
            Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costs);
            log.info("账单明细插入完成");
            if (CollectionUtil.isNotEmpty(instanceGaapCosts)) {
                List<InstanceGaapCost> costList = new ArrayList<>(instanceGaapCosts);
                insertBillCycleInfo(costList, serviceOrder.getId());
                insertAccountDeal(serviceOrder, costs);
            }
        } catch (Exception e) {
            log.error("入账失败", e);
        }
        return true;
    }

    /**
     * 插入账单周期信息
     *
     * @param costs 成本
     */
    public void insertBillCycleInfo(List<InstanceGaapCost> costs, Long orderId) {
        costs.forEach(cost -> {
            BillBillingCycleCost cycleCost = new BillBillingCycleCost();
            cycleCost.setBillingCycle(cost.getBillingCycle());
            cycleCost.setBillNo(NoUtil.generateNo("BP"));
            cycleCost.setCloudEnvType(cost.getCloudEnvType());
            cycleCost.setCloudEnvName(cost.getCloudEnvName());
            cycleCost.setProductCode(cost.getProductCode());
            cycleCost.setProductName(cost.getProductName());
            cycleCost.setBillStartTime(cost.getUsageStartDate());
            cycleCost.setBillEndTime(cost.getUsageEndDate());
            cycleCost.setPayTime(cost.getPayTime());
            cycleCost.setOfficialAmount(cost.getPretaxGrossAmount());
            cycleCost.setDiscountAmount(cost.getPretaxAmount());
            cycleCost.setCashAmount(cost.getCashAmount());
            cycleCost.setCreditAmount(cost.getCreditAmount());
            cycleCost.setVoucherAmount(cost.getCouponAmount());
            cycleCost.setInvoiceAmount(cost.getCashAmount());
            if (cost.getUserAccountId() != null) {
                cycleCost.setOwnerId(cost.getUserAccountId());
            }
            if (ProductCodeEnum.AI_MARKET.getProductCode().equals(cost.getProductCode())) {
                cycleCost.setMarketShopState(false);
            }
            cycleCost.setOrgId(cost.getOrgSid());
            cycleCost.setOrgName(cost.getOrgName());
            cycleCost.setPriceType(cost.getPriceType());
            cycleCost.setBillType(cost.getBillType());
            //运营实体Id
            cycleCost.setEntityId(cost.getEntityId());
            cycleCost.setEntityName(cost.getEntityName());
            //抹零金额
            cycleCost.setEraseZeroAmount(cost.getEraseZeroAmount());
            BigDecimal cashAmount =
                    cost.getCashAmount() != null ? cost.getCashAmount() : new BigDecimal(0);
            BigDecimal creditAmount =
                    cost.getCreditAmount() != null ? cost.getCreditAmount() : new BigDecimal(0);
            BigDecimal voucherAmount =
                    cost.getCouponAmount() != null ? cost.getCouponAmount() : new BigDecimal(0);
            cycleCost.setAmount((cashAmount.add(creditAmount).add(voucherAmount)).setScale(2,
                    RoundingMode.HALF_UP));
            cycleCost.setCouponDiscount(cost.getCouponDiscount());
            cycleCost.setCreateDt(new Date());
            BillBillingCycleCost insert = mongoTemplate.insert(cycleCost);

            //保存订单周期id
            LambdaQueryWrapper<MarketShopSubscribe> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MarketShopSubscribe::getOrderId, orderId);
            List<MarketShopSubscribe> subscribes = shopSubscribeService.getBaseMapper().selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(subscribes)) {
                MarketShopSubscribe subscribe = subscribes.get(0);
                subscribe.setBillingCycleId(String.valueOf(cycleCost.getBillNo()));
                LambdaQueryWrapper<MarketShopSubscribe> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MarketShopSubscribe::getSubscribeId, subscribe.getSubscribeId());
                shopSubscribeService.update(subscribe, wrapper);
            }

            //保存拆分账单周期数据
            splitAndSaveCycleItem(cycleCost);
            //更新账单明细
            Query query = new Query();
            Update update = new Update();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id")
                    .is(cost.getMongoId()));
            update.set("billBillingCycleId", insert.getId().toString());
            UpdateResult updateResult = mongoTemplate.updateMulti(query, update,
                    "biz_bill_usage_item");
            if (updateResult.getMatchedCount() > 0) {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------success");
            } else {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------Failed-----{}",
                        JSON.toJSONString(updateResult));
            }
        });
    }

    /**
     * 插入账户交易
     *
     * @param serviceOrder 服务订单
     * @param costs        成本
     */
    public void insertAccountDeal(ServiceOrder serviceOrder, List<InstanceGaapCost> costs) {
        List<BizAccountDeal> deals = Lists.newArrayList();
        costs.forEach(cost -> {
            BizAccountDeal accountDeal = new BizAccountDeal();
            accountDeal.setFlowNo(
                    NoUtil.generateNo(BillingConstants.DEAL_PREFIX) + RandomUtil.randomNumbers(3));
            if (serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) >= 0) {
                accountDeal.setType(DealType.OUT);
            } else {
                accountDeal.setType(DealType.IN);
            }
            BigDecimal tradePrice = cost.getPretaxAmount();
            boolean isRefund = tradePrice.compareTo(BigDecimal.ZERO) < 0;
            accountDeal.setChargingType(serviceOrder.getChargingType());
            accountDeal.setTradeType(isRefund ? TradeType.REFUND : TradeType.PAY);
            accountDeal.setTradeNo(cost.getBillNo());
            accountDeal.setEnvType(cost.getCloudEnvType());
            accountDeal.setEnvName(cost.getCloudEnvName());
            accountDeal.setOrderNo(serviceOrder.getOrderSn());
            accountDeal.setBillNo(cost.getBillNo());
            accountDeal.setRemark(cost.getDescription());
            accountDeal.setBillingCycle(
                    LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
            cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount account = cost.getBizBillingAccount();
            accountDeal.setAccountSid(account.getId());
            accountDeal.setAccountName(account.getAccountName());
            accountDeal.setOrgSid(account.getOrgSid());
            accountDeal.setUserSid(account.getId());
            accountDeal.setDealTime(System.currentTimeMillis());
            accountDeal.setBalance(account.getBalance());
            accountDeal.setBalanceCredit(account.getCreditLine());
            accountDeal.setBalanceCash(account.getBalanceCash());
            accountDeal.setEntityId(serviceOrder.getEntityId());
            accountDeal.setEntityName(serviceOrder.getEntityName());
            accountDeal.setSuperviseStatus(UNDELIVERED);
            String priceType = cost.getPriceType();
            String priceTypeDes = "";
            if (PriceType.RESOURCE.equals(priceType)) {
                priceTypeDes = "-资源费用";
            } else if (PriceType.SERVICE.equals(priceType)) {
                priceTypeDes = "-服务费用";
            } else if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                priceTypeDes = "-配置费用";
            }
            accountDeal.setRemark(StrUtil.concat(true,
                    EnumUtil.likeValueOf(ProductCodeEnum.class, cost.getProductCode())
                            .getProductName(),
                    priceTypeDes));
            WebUserUtil.prepareInsertParams(accountDeal, serviceOrder.getCreatedBy());
            //销售计费，记录收支明细
            if (SALE_TYPE.equals(serviceOrder.getChargingType())) {
                //销售计费情景下，记录收支明细，交易渠道默认现金余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalanceCredit(
                        NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                deals.add(bizAccountDeal);
                return;
            }
            //优惠券抵扣为0元
            if (cost.getCouponDiscount() != null && cost.getCouponDiscount().compareTo(BigDecimal.ZERO) > 0
                    && cost.getPretaxAmount().compareTo(BigDecimal.ZERO) >= 0) {
                // 采用余额

                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setRemark(accountDeal.getRemark() + "(优惠券抵扣" + cost.getCouponDiscount().setScale(2,
                        RoundingMode.HALF_UP)
                        + "元)");
                bizAccountDeal.setTradeChannel(RechargeTypeEnum.COUPON.getCode());
                bizAccountDeal.setCouponAmount(cost.getCouponDiscount());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setBalanceCash(NumberUtil.add(account.getBalanceCash(), cost.getCouponAmount()));
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                deals.add(bizAccountDeal);
            }
            //充值现金券
            BigDecimal couponAmount = cost.getCouponAmount();
            if (NumberUtil.isGreater(couponAmount, BigDecimal.ZERO) || NumberUtil.isLess(
                    couponAmount, BigDecimal.ZERO)) {
                // 现金券余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
                accountDeal.setAmount(isRefund ? couponAmount.abs() : couponAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalance(
                        NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(
                        NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                deals.add(bizAccountDeal);
            }
            BigDecimal cashAmount = cost.getCashAmount();
            if (NumberUtil.isGreater(cashAmount, BigDecimal.ZERO) || NumberUtil.isLess(cashAmount,
                    BigDecimal.ZERO)) {
                // 采用余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(isRefund ? cashAmount.abs() : cashAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalanceCredit(
                        NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                deals.add(bizAccountDeal);
            }
            BigDecimal creditAmount = cost.getCreditAmount();
            if (NumberUtil.isGreater(creditAmount, BigDecimal.ZERO) || NumberUtil.isLess(
                    creditAmount, BigDecimal.ZERO)) {
                // 信用额度
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
                accountDeal.setAmount(isRefund ? creditAmount.abs() : creditAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                deals.add(bizAccountDeal);
            }
        });
        log.info("记收支明细,账单数量【{}】,收支明细数量【{}】", costs.size(), deals.size());
        if (CollectionUtil.isNotEmpty(deals)) {
            boolean saveBatch = bizAccountDealService.saveBatch(deals);
            log.info("记收支明细,入库【{}】", saveBatch);
        }
    }

    /**
     * 分离和保存周期项
     *
     * @param cycleCost 周期成本
     */
    public void splitAndSaveCycleItem(BillBillingCycleCost cycleCost) {
        try {
            if (cycleCost != null) {
                BigDecimal oneHourDeciaml = new BigDecimal(1000 * 60 * 60);
                //复制账单周期数据
                BillBillingCycleCostSplitItem costSplitItem = BeanConvertUtil.convert(cycleCost, BillBillingCycleCostSplitItem.class);
                costSplitItem.setBillBillingCycleId(cycleCost.getId().toString());
                Date billStartTime = costSplitItem.getBillStartTime();
                Date billEndTime = costSplitItem.getBillEndTime();
                BigDecimal allHours = NumberUtil.div(billEndTime.getTime()-billStartTime.getTime(), oneHourDeciaml).setScale(0,BigDecimal.ROUND_HALF_UP);

                //判断账单周期类型，包年包月的才拆分
                if (BillingConstants.SUBSCRIPTION_ORDER.equalsIgnoreCase(costSplitItem.getBillType()) || !NumberUtil.equals(allHours,BigDecimal.ZERO)) {
                    Date tempTime = billStartTime;
                    String startMonth = DateFormatUtils.format(billStartTime, cn.com.cloudstar.rightcloud.common.util.DateUtil.MONTH_PATTERN);
                    Date startTimeOfMonth = DateUtils.parseDate(startMonth+"-01 00:00:00", cn.com.cloudstar.rightcloud.common.util.DateUtil.COMMON_DATE_PATTERN);
                    Date startTimeOfNextMonth = org.apache.commons.lang3.time.DateUtils.addMonths(startTimeOfMonth, 1);
                    //计算账单跨月的账期，按小时拆分金额
                    if (tempTime != null && billEndTime != null && billEndTime.after(startTimeOfNextMonth)) {
                        List<BillBillingCycleCostSplitItem> splitItemList = new ArrayList<>();
                        //账单总时间小于1小时，四舍五入保留两位小数
                        if(NumberUtil.isLess(allHours,BigDecimal.ONE)){
                            allHours =NumberUtil.div(billEndTime.getTime()-billStartTime.getTime(), oneHourDeciaml).setScale(2,BigDecimal.ROUND_HALF_UP);
                        }

                        BigDecimal oneHourCashAmount = NumberUtil.div(cycleCost.getCashAmount(), allHours);
                        BigDecimal oneHourCreditAmount = NumberUtil.div(cycleCost.getCreditAmount(), allHours);
                        BigDecimal oneHourOfficialAmount = NumberUtil.div(cycleCost.getOfficialAmount(), allHours);
                        //折后价
                        BigDecimal oneHourDiscountAmount = NumberUtil.div(cycleCost.getDiscountAmount(), allHours);
                        BigDecimal oneHourVoucherAmount = NumberUtil.div(cycleCost.getVoucherAmount(), allHours);
                        BigDecimal oneHourCouponDiscount = NumberUtil.div(cycleCost.getCouponDiscount(), allHours);
                        BigDecimal oneHourEraseZeroAmount = NumberUtil.div(cycleCost.getEraseZeroAmount(), allHours);
                        do{
                            BillBillingCycleCostSplitItem newItem = BeanConvertUtil.convert(costSplitItem, BillBillingCycleCostSplitItem.class);
                            String currentMonth = DateFormatUtils.format(tempTime, cn.com.cloudstar.rightcloud.common.util.DateUtil.MONTH_PATTERN);
                            Date startTimeOfCurrentMonth = DateUtils.parseDate(currentMonth+"-01 00:00:00", cn.com.cloudstar.rightcloud.common.util.DateUtil.COMMON_DATE_PATTERN);
                            startTimeOfNextMonth = org.apache.commons.lang3.time.DateUtils.addMonths(startTimeOfCurrentMonth, 1);
                            Date endTimeOfPeriod = startTimeOfNextMonth;
                            if (endTimeOfPeriod.after(billEndTime)) {
                                endTimeOfPeriod = billEndTime;
                            }
                            newItem.setBillingCycle(currentMonth);
                            newItem.setBillStartTime(tempTime);
                            newItem.setBillEndTime(endTimeOfPeriod);
                            //账单总时间小于1小时，四舍五入保留两位小数
                            BigDecimal currentHours = NumberUtil.div(endTimeOfPeriod.getTime()-tempTime.getTime(), oneHourDeciaml).setScale(0,BigDecimal.ROUND_HALF_UP);
                            if(NumberUtil.isLess(allHours,BigDecimal.ONE)){
                                currentHours =NumberUtil.div(endTimeOfPeriod.getTime()-tempTime.getTime(), oneHourDeciaml).setScale(2,BigDecimal.ROUND_HALF_UP);
                            }

                            newItem.setCashAmount(NumberUtil.mul(oneHourCashAmount, currentHours));
                            newItem.setCreditAmount(NumberUtil.mul(oneHourCreditAmount, currentHours));
                            newItem.setOfficialAmount(NumberUtil.mul(oneHourOfficialAmount, currentHours));
                            newItem.setDiscountAmount(NumberUtil.mul(oneHourDiscountAmount, currentHours));
                            newItem.setVoucherAmount(NumberUtil.mul(oneHourVoucherAmount, currentHours));
                            newItem.setCouponDiscount(NumberUtil.mul(oneHourCouponDiscount, currentHours));
                            newItem.setEraseZeroAmount(NumberUtil.mul(oneHourEraseZeroAmount, currentHours));
                            newItem.setAmount(newItem.getCashAmount().add(newItem.getCreditAmount()).add(newItem.getVoucherAmount()));
                            tempTime = startTimeOfNextMonth;
                            splitItemList.add(newItem);
                        }while (tempTime.before(billEndTime));
                        mongoTemplate.insertAll(splitItemList);
                    } else {
                        mongoTemplate.insert(costSplitItem);
                    }
                }else{
                    mongoTemplate.insert(costSplitItem);
                }
            } else {
                log.error("账单周期数据拆分-BizBillingCycleServiceImpl.splitAndSaveCycleItem-INPUT: null");
            }
        } catch (Exception e) {
            log.error("账单周期数据拆分-BizBillingCycleServiceImpl.splitAndSaveCycleItem-异常：[{}]",e.getMessage());
        }
    }

}
