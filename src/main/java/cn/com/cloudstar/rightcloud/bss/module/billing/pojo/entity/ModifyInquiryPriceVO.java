/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity;

import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/30 10:54
 */
@Data
public class ModifyInquiryPriceVO {

    /**
     * 变更金额
     */
    private BigDecimal amount;

    /**
     * 新规格参考价
     */
    private BigDecimal price;

    /**
     * 几折
     */
    private BigDecimal platformDiscount;

    /**
     * 项目ID
     */
    private Long projectId;

    private List<BizBillingPriceVO> billingPrices = Lists.newArrayList();

    /**
     * 服务变更金额
     */
    private BigDecimal serviceAmount;

    /**
     * 计算时间
     */
    private Date computeDate;

    private Date computeEndDate;

    private BigDecimal giveBack = BigDecimal.ZERO;

    private BigDecimal serviceGiveBack = BigDecimal.ZERO;

    /**
     * 变更容量时记录负数补差金额
     */
    private BigDecimal negativeAmount;

    /**
     *  单位 month hour
     */
    private String chargeUnit;

    /**
     *  是否显示实付金额
     */
    private boolean showAmount = true;
}
