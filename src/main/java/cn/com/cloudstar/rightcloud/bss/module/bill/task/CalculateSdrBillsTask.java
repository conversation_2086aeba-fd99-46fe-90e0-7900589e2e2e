/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.task;

import cn.com.cloudstar.rightcloud.bss.common.enums.SdrColectorTypeEnum;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.ISdrGaapCostService;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.ResourceTransactionManager;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/10/17 14:47
 */
@Component
@Slf4j
public class CalculateSdrBillsTask {

    private static final String SDR_LOCK_KEY = "bill:task:sdr:calculating";
    private static final String POST_PAID = "PostPaid";

    private static final String SDR_RECORD_KEY = "RECORD";

    @Autowired
    private ISdrGaapCostService sdrGaapCostService;

    @Autowired
    private ResourceTransactionManager dataSourceTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private MongoTemplate mongoTemplate;

    public void calculateSdrBills() {
        calculateByType();
    }

    public void calculateByType() {
        log.info("入账开始");
        boolean lock = JedisUtil.INSTANCE.setnx(CalculateSdrBillsTask.SDR_LOCK_KEY);
        if (lock) {
            JedisUtil.INSTANCE.expire(CalculateSdrBillsTask.SDR_LOCK_KEY, 1800);
            TransactionStatus transactionStatus = null;
            try {
                transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
                doCalculate();
                dataSourceTransactionManager.commit(transactionStatus);
            } catch (Exception e) {
                log.error("入账失败,回滚数据,原因{}", e.getMessage(), e);
                if (transactionStatus != null) {
                    dataSourceTransactionManager.rollback(transactionStatus);
                }
            }
            JedisUtil.INSTANCE.del(CalculateSdrBillsTask.SDR_LOCK_KEY);
            log.info("入账结束");
        } else {
            log.info("已有任务正在入账中,退出本次任务");
        }
    }

    private void doCalculate() {
        //查询所有话单
        List<Collector> collectorList = new ArrayList<>();
        ArrayList<Integer> typeList = CollectionUtil.toList(1, 2, 3, 4, 5);
        for (Integer type : typeList) {
            //获取所有话单记录
            collectorList.addAll(this.getCollectorOnMongo(type));
        }
        //账单集合
        List<InstanceGaapCost> costs = Lists.newArrayList();
        //收支明细集合
        List<BizAccountDeal> deals = Lists.newLinkedList();
        //归档集合
        List<Collector> archRecords = Lists.newArrayList();
        if (collectorList.size() > 0) {
            for (Collector detail : collectorList) {
                try {
                    sdrGaapCostService.handleSdrBill(archRecords, detail, CalculateSdrBillsTask.POST_PAID, costs, deals);
                } catch (Exception e) {
                    log.error("生成账单信息失败！", e);
                }
            }
        }
        // 新增收支明细
        if (deals.size() > 0) {
            bizAccountDealService.saveBatch(deals);
        }
        //将账单信息插入mongodb
        if (costs.size() > 0) {
            //保存账单到mongo
            mongoTemplate.insertAll(costs);
            //归档已出账话单
            handleArchRecords(archRecords);

        }
    }

    /**
     * 归档已出账话单记录
     *
     * @param archivalRecords
     */
    private void handleArchRecords(List<Collector> archivalRecords) {
        if (archivalRecords != null) {
            archivalRecords.forEach(c -> {
                if (c instanceof ObsCollector) {
                    //归档OBS类型话单
                    ObsCollectorArchived obsCollectorArchived = new ObsCollectorArchived();
                    BeanUtils.copyProperties(c, obsCollectorArchived);
                    mongoTemplate.insert(obsCollectorArchived);
                } else if (c instanceof SfsCollector) {
                    //归档SFS类型话单
                    SfsCollectorArchived sfsCollectorArchived = new SfsCollectorArchived();
                    BeanUtils.copyProperties(c, sfsCollectorArchived);
                    mongoTemplate.insert(sfsCollectorArchived);
                } else if (c instanceof HpcCollector) {
                    //归档HPC类型话单
                    HpcCollectorArchived hpcCollectorArchived = new HpcCollectorArchived();
                    BeanUtils.copyProperties(c, hpcCollectorArchived);
                    mongoTemplate.insert(hpcCollectorArchived);
                } else if (c instanceof ModelartsCollector) {
                    //归档Modelarts话单
                    ModelartsCollectorArchived modelartsCollectorArchived = new ModelartsCollectorArchived();
                    BeanUtils.copyProperties(c, modelartsCollectorArchived);
                    mongoTemplate.insert(modelartsCollectorArchived);
                } else if (c instanceof AiModelCollector) {
                    //归档AiModel话单
                    AiModelCollectorArchived aiModelCollectorArchived = new AiModelCollectorArchived();
                    BeanUtils.copyProperties(c, aiModelCollectorArchived);
                    mongoTemplate.insert(aiModelCollectorArchived);
                }
            });
        }
    }


    /**
     * 查询所有话单
     *
     * @return
     */
    private List<Collector> getCollectorOnMongo(Integer type) {
        List<Collector> collectorList = new ArrayList<>();
        //查询上次出账记录
        String lastRecord = JedisUtil.INSTANCE.get(SDR_RECORD_KEY);
        if (lastRecord == null) {
            //第一次出单出账，拉取全部数据
            this.queryData(null, collectorList, type);
        } else {
            SdrTimeRecord record = JSON.parseObject(lastRecord, SdrTimeRecord.class);
            this.queryData(record, collectorList, type);
        }
        return collectorList;
    }

    private void queryData(SdrTimeRecord rec, List<Collector> collectorList, Integer type) {
        SdrTimeRecord record = rec;
        HashMap<Integer, Class> map = new HashMap<>();
        map.put(1, ObsCollector.class);
        map.put(2, SfsCollector.class);
        map.put(3, HpcCollector.class);
        map.put(4, ModelartsCollector.class);
        map.put(5, AiModelCollector.class);
        Query query = new Query();
        ;
        List<Collector> collectors = new ArrayList<>();
        if (record != null) {
            Class recordClass = ClassUtils.getUserClass(record);
            Field[] fields = recordClass.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                ReflectionUtils.makeAccessible(field);
                String name = field.getName();
                String s = SdrColectorTypeEnum.get(type);
                if (s != null && name.contains(s)) {
                    try {
                        Long value = (Long) field.get(record);
                        collectors = mongoTemplate.find(query, map.get(type));
                        if (value != null) {
                            collectors = collectors.stream().filter(vo -> vo.getTime_stamp() > value).collect(Collectors.toList());
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            }

        } else {
            collectors = mongoTemplate.find(query, map.get(type));
        }
        if (collectors.size() > 0) {
            collectorList.addAll(collectors);
            this.updataArchivedRecord(collectors, type);
        }
    }

    /**
     * 按实际出账话单更新记录
     *
     * @param collectors
     * @param type
     */
    private void updataArchivedRecord(List<Collector> collectors, Integer type) {
        SdrTimeRecord record = new SdrTimeRecord();
        collectors.sort(Comparator.comparing(Collector::getTime_stamp));
        Collector collector = collectors.get(collectors.size() - 1);
        //1：OBS  2：SFS  3：HPC  4:Modelarts  5:AiModel
        switch (type) {
            case 1:
                record.setObsRecord(collector.getTime_stamp());
                break;
            case 2:
                record.setSfsRecord(collector.getTime_stamp());
                break;
            case 3:
                record.setHpcRecord(collector.getTime_stamp());
                break;
            case 4:
                record.setModelartsRecord(collector.getTime_stamp());
                break;
            case 5:
                // TODO: 需要在SdrTimeRecord中添加aiModelRecord字段
                // record.setAiModelRecord(collector.getTime_stamp());
                break;
            default:
                break;
        }
        JedisUtil.INSTANCE.set(SDR_RECORD_KEY, JSON.toJSONString(record));
    }

}
