/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/2/25 16:16
 */
@Data
@ApiModel("内置产品资源返回对象")
public class DescribeProductResourceSimpleResponse {

    /**
     * id
     */
    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderSn;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    private String productType;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;


    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 组织sid
     */
    @ApiModelProperty("组织id")
    private Long orgSid;



    /**
     * 计费模式
     */
    @ApiModelProperty("计费模式")
    private String chargeType;


    /**
     * 配置
     */
    @ApiModelProperty("配置")
    private String configDesc;

    /**
     * 所属项目
     */
    @ApiModelProperty("所属项目")
    private String orgName;




    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;



}
