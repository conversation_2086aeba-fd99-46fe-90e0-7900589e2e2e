/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.service;


import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductOperation;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.ResRenewRef;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.RenewalDTO;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.DescribeRenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.ListProductAndCloudEnvRequest;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.response.ListProductAndCloudEnvResponse;

import java.util.List;
import java.util.Map;

public interface ResRenewRefService {
    int countByParams(Criteria example);

    ResRenewRef selectByPrimaryKey(Long renewSid);

    List<ResRenewRef> selectByParams(Criteria example);

    int deleteByPrimaryKey(Long renewSid);

    int updateByPrimaryKeySelective(ResRenewRef record);

    int updateByPrimaryKey(ResRenewRef record);

    int deleteByParams(Criteria example);

    int updateByParamsSelective(ResRenewRef record, Criteria example);

    int updateByParams(ResRenewRef record, Criteria example);

    int insert(ResRenewRef record);

    int insertSelective(ResRenewRef record);

    ListProductAndCloudEnvResponse listProductAndCloud(ListProductAndCloudEnvRequest request);

    List<RenewalDTO> listRenewal(DescribeRenewRequest request);

    Map<String, Object> renewDetail(String id);

    Integer selectUnifyDate(Long orgSid);

    void modifyUnifyDate(Long orgSid, Integer unifyDate);

    RestResult renewResource(ProductOperation productOperation);

    void updateRenewRef(ResRenewRef resRenewRef);

    List<RenewalDTO> exportRenew(DescribeRenewRequest request);

    //根据订单详情够成ProductInfoVO
    ProductInfo buildProductInfo(ServiceOrderDetail serviceOrderDetail);

    void completeProductInfo(ProductInfo productInfo);

    RestResult aotuRenew();

    void mapToProductInfo(Map<String, Object> detail,ProductInfo productInfo);

    Map<String, Object> renewInnerProductDetail(String id, Boolean dealType);

    Map<String, Object> renewInnerProductDetail(String id, Boolean dealType,String productCode);

    String newConfigurationReplacement(Long clusterId, String productCode, String config);

    /**
     * 合同列表
     * @param sfProductResource
     * @param serviceOrderDetail
     * @param productInfo
     * @param compareFlage
     * @param hpcBizContractDTOS
     */
    void changeNodeNum(SfProductResource sfProductResource, ServiceOrderDetail serviceOrderDetail, ProductInfo productInfo, boolean compareFlage, List<HpcBizContractDTO> hpcBizContractDTOS) ;
}