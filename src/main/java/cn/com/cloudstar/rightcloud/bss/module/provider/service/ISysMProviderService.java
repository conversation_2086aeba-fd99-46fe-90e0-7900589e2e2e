package cn.com.cloudstar.rightcloud.bss.module.provider.service;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.com.cloudstar.rightcloud.bss.module.provider.pojo.entity.SysMProvider;

/**
 * <AUTHOR>
 * @date 2025/1/21 14:38
 */
public interface ISysMProviderService extends IService<SysMProvider> {
    /**
     * 申请供应商
     * @param sysMProvider
     */
    Long applyProvider(SysMProvider sysMProvider);

    /**
     * 注销供应商
     * @param userSid
     * @return
     */
    void cancelProvider(Long userSid,Long providerId);
}
