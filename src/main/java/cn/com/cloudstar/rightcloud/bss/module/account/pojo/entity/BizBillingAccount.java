/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;

/**
 * <p>
 * 账户
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-22
 */
@Data
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt = true, macEncryptDecrypt = false)
@EncryptDecryptClass
public class BizBillingAccount extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 管理员ID
     */
    private Long adminSid;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户余额
     */
    @CCSPIntegralityHashAndVerify(segment = 1,defineBigDecimalScale = true,bigDecimalScale = 2)
    private BigDecimal balance;

    /**
     * 现金券余额
     */
    @CCSPIntegralityHashAndVerify(segment = 2,defineBigDecimalScale = true,bigDecimalScale = 2)
    private BigDecimal balanceCash;

    /**
     * 折扣系数
     */
    private BigDecimal discount;

    /**
     * 所属行业
     */
    @TableField(exist = false)
    private String industry;
    @TableField(exist = false)
    private String industryName;
    @TableField(exist = false)
    private String industryNameUs;

    /**
     * 应用场景
     */
    @TableField(exist = false)
    private String applicationScenario;
    @TableField(exist = false)
    private String applicationScenarioName;
    /**
     * 人员规模
     */
    @TableField(exist = false)
    private String personnelSize;
    @TableField(exist = false)
    private String personnelSizeName;
    /**
     * 详细地址
     */
    @TableField(exist = false)
    private String address;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;
    @TableField(exist = false)
    private String orgName;

    /**
     * 信用额度
     */
    @CCSPIntegralityHashAndVerify(segment = 3,defineBigDecimalScale = true,bigDecimalScale = 2)
    private BigDecimal creditLine;

    private BigDecimal obsFreeCapacity;

    /**
     * 信用额度到期时间
     */
    private String creditLineDt;

    /**
     * 版本号
     */
    @Version
    private Long version;

    @TableField(exist = false)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String adminName;

    @TableField(exist = false)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String email;

    @TableField(exist = false)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String mobile;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private List<BizDiscount> discounts;

    @TableField(exist = false)
    @CCSPEncryptDecrypt
    private String salesmen;

    /**
     * 分销商ID
     */
    @TableField(value = "distributor_id", whereStrategy = FieldStrategy.IGNORED)
    private Long distributorId;

    /**
     * 销售ID
     */
    @TableField(value = "salesmen_id", whereStrategy = FieldStrategy.IGNORED)
    private Long salesmenId;

    /**
     * 运营实体id
     */
    private Long entityId;

    /**
     * 关联运营实体名称
     */
    private String entityName;

    /**
     * 账户状态 正常：normal冻结：freeze
     */
    private String status;

    /**
     * user状态
     */
    @TableField(exist = false)
    private String userStatus;

    /**
     * 冻结类型（0为自动1为手动）
     */
    private String unfreezeType;

    /**
     * 分销商名称
     */
    @TableField(exist = false)
    private String distributorName;

    /**
     * 资源冻结状态
     */
    @TableField(exist = false)
    private String freezeStatus;
    /**
     * 是否展示启用按钮：0不展示，1展示
     */
    @TableField(exist = false)
    private String showFlag;

    @TableField(exist = false)
    private String freezeStatusName;

    @TableField(exist = false)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String  realName;

    /**
     * UsrID
     */
    @TableField(exist = false)
    private Long userSid;

    @TableField(exist = false)
    private String account;

    /**
     * 用户身份认证状态
     */
    @TableField(exist = false)
    private String certificationStatus;

    /**
     * 联系人
     */
    @TableField(exist = false)
    @EncryptDecryptField
    private String contactName;
    /**
     * 业务标识tag，多个以;分隔，拓展中：expansion，已备案：recorded，试算中：trial，已签单：signed，商用中：commercial，欠费中：arrearage，已注销：cancelled
     */
    @TableField(exist = false)
    private String businessTag;

    @TableField(exist = false)
    private String solution;

    /**
     * 当前组织
     */
    @TableField(exist = false)
    private CurrentOrgVO currentOrg;

    @TableField(exist = false)
    private String accountId;

    /**
     * LDAP 对应的 OU
     */
    @TableField(exist = false)
    private String ldapOu;

    /**
     * 账号到期时间
     */
    private String endTime;

    /**
     * 处理策略
     */
    private String actionStrategy;


    /**
     * 充值总金额
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 手动录入的线下开票金额
     */
    private BigDecimal offlineInvoicedAmount;
    /**
     * 自定义信息
     */
    @TableField(exist = false)
    private String customizationInfo;

    @TableField(exist = false)
    /**
     * bsm是否开启
     */
    private Long bmsEnable;
    @TableField(exist = false)
    /**
     * 策略保留周期
     */
    private Long strategyBufferPeriod;
    @TableField(exist = false)
    /**
     * reservedResource 保留资源；releaseResource 释放资源；
     */
    private String freezingStrategy;

    /**
     *  是否隐藏弹性裸金属配置信息
     */
    @TableField(exist = false)
    private boolean bmsIsHide = true;

    /**
     *  开通产品信息
     */
    @TableField(exist = false)
    private String openProductInformation;

    /**
     * 联系电话
     */
    @TableField(exist = false)
    private String contactPhone;

    /**
     * 企业邮箱
     */
    @TableField(exist = false)
    private String contactEmail;

    @TableField(exist = false)
    private Long companyId;

    @TableField(exist = false)
    private String companyName;

    private String refOrgId;
    @TableField(exist = false)
    private Long orgId;

    @Override
    public String toString() {
        return "BizBillingAccount{" +
                "id=" + id +
                ", orgSid=" + orgSid +
                ", adminSid=" + adminSid +
                ", accountName='" + accountName + '\'' +
                ", balance=" + balance +
                ", balanceCash=" + balanceCash +
                ", discount=" + discount +
                ", industry='" + industry + '\'' +
                ", industryName='" + industryName + '\'' +
                ", applicationScenario='" + applicationScenario + '\'' +
                ", applicationScenarioName='" + applicationScenarioName + '\'' +
                ", personnelSize='" + personnelSize + '\'' +
                ", personnelSizeName='" + personnelSizeName + '\'' +
                ", address='" + address + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdDt=" + createdDt +
                ", updatedBy='" + updatedBy + '\'' +
                ", updatedDt=" + updatedDt +
                ", orgName='" + orgName + '\'' +
                ", creditLine=" + creditLine +
                ", creditLineDt='" + creditLineDt + '\'' +
                ", version=" + version +
                ", adminName='" + adminName + '\'' +
                ", email='" + email + '\'' +
                ", mobile='" + mobile + '\'' +
                ", remark='" + remark + '\'' +
                ", discounts=" + discounts +
                ", salesmen='" + salesmen + '\'' +
                ", distributorId=" + distributorId +
                ", salesmenId=" + salesmenId +
                ", entityId=" + entityId +
                ", entityName='" + entityName + '\'' +
                ", status='" + status + '\'' +
                ", userStatus='" + userStatus + '\'' +
                ", unfreezeType='" + unfreezeType + '\'' +
                ", distributorName='" + distributorName + '\'' +
                ", freezeStatus='" + freezeStatus + '\'' +
                ", showFlag='" + showFlag + '\'' +
                ", freezeStatusName='" + freezeStatusName + '\'' +
                ", realName='" + realName + '\'' +
                ", userSid=" + userSid +
                ", account='" + account + '\'' +
                ", certificationStatus='" + certificationStatus + '\'' +
                ", contactName='" + contactName + '\'' +
                ", businessTag='" + businessTag + '\'' +
                ", solution='" + solution + '\'' +
                ", currentOrg=" + currentOrg +
                ", accountId='" + accountId + '\'' +
                ", ldapOu='" + ldapOu + '\'' +
                ", endTime='" + endTime + '\'' +
                ", actionStrategy='" + actionStrategy + '\'' +
                ", totalRechargeAmount=" + totalRechargeAmount +
                ", invoicedAmount=" + invoicedAmount +
                ", offlineInvoicedAmount=" + offlineInvoicedAmount +
                ", bmsEnable=" + bmsEnable +
                ", strategyBufferPeriod=" + strategyBufferPeriod +
                ", freezingStrategy='" + freezingStrategy + '\'' +
                '}';
    }
}
