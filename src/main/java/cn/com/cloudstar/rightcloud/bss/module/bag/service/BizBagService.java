/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.SubscribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBagAndSpecResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagResponseVO;

import java.util.List;


/**
 * <p>
 * 运营-用户套餐包
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-07
 */

public interface BizBagService extends IService<BizBag> {


    /**
     * 创建套餐包
     *
     * @param request 创建参数
     * @return 创建是否成功
     */
    String createBag(CreateBizBagRequest request);


    /**
     * 分页查询套餐包
     *
     * @param request 创建参数
     * @return 创建是否成功
     */
    IPage<DescribeBizBagResponseVO> listBizBag(DescribeBizBagRequest request);

    /**
     * 分页查询套餐包规格分类
     * @param
     * @param request
     * @return
     */
    List<DescribeBagAndSpecResponse> listBizBagAndSpec(DescribeBizBagSpecRequest request);

    /**
     * 修改套餐包
     * @param bizBag
     * @return
     */
    Boolean updateBizBagById(BizBag bizBag);

    /**
     *  删除资源包
     * @param id
     */
    Boolean  deleteBizBag(String id);

    /**
     * 复制套餐包
     * @param id
     */
    Boolean  copyBag(String id);

    /**
     * 用名称查询资源包是否存在
     *
     * @param name
     */
    Boolean checkName(String name);

    void subscribe(SubscribeBizBagRequest request);

    void renew(SubscribeBizBagRequest request);
    /**
     * 上架套餐包
     * @param id 套餐包id
     * @param productType 产品类型
     * @return
     */
     Boolean  online(String id,String productType);

    /**
     * 下架套餐包
     * @param id
     * @return
     */
    Boolean  offline(String id);

    /**
     * 套餐包获取适用产品
     */
    List<DescribeBizBagProductResponse> listBizBagProduct(String type);
}
