/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.notice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.module.notice.mapper.SysMNoticeMapper;
import cn.com.cloudstar.rightcloud.bss.module.notice.mapper.SysMNoticeTypeMapper;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.entity.SysMNotice;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.entity.SysMNoticeType;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.request.NoticePageListRequest;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.response.NoticeDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.response.NoticeListResponse;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * <AUTHOR>
 * @Date 2021/5/18 14:07
 *  公告服务
 */
@Service
@Slf4j
public class SysMNoticeServiceImpl implements ISysMNoticeService {

    @Resource
    private SysMNoticeMapper sysMNoticeMapper;
    @Resource
    private SysMNoticeTypeMapper sysMNoticeTypeMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    private static final String MAIL_REGEXP = "^[a-zA-Z0-9_.-]{2,16}@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$";


    @Override
    public IPage<NoticeListResponse> pageQuery(NoticePageListRequest noticePageRequest) {
        // 分页参数为null时默认赋值
        if (ObjectUtils.isEmpty(noticePageRequest.getPagenum())) {
            noticePageRequest.setPagenum(0L);
        }
        if (ObjectUtils.isEmpty(noticePageRequest.getPagesize())) {
            noticePageRequest.setPagesize(10L);
        }

        Page<NoticeListResponse> pageData = PageUtil.preparePageParams(noticePageRequest);

        QueryWrapper<SysMNotice> queryWrapper = new QueryWrapper();
        String noticeTitle = noticePageRequest.getNoticeTitle();
        Long noticeTypeId = noticePageRequest.getNoticeTypeId();
        String publishEndDate = noticePageRequest.getPublishEndDate();
        String publishStartDate = noticePageRequest.getPublishStartDate();
        if (StringUtils.isNotEmpty(noticeTitle)) {
            queryWrapper.like("notice_title", noticeTitle);
        }
        if (noticeTypeId != null) {
            queryWrapper.like("notice_type_id", noticeTypeId);
        }
        if (StringUtils.isNotEmpty(publishStartDate)) {
            queryWrapper.ge("publish_dt", publishStartDate);
        }
        Date curr = new Date();
        if (StringUtils.isNotEmpty(publishEndDate)) {
            Date date = DateUtil.formatDate(publishEndDate);
            if(date.after(curr)){
                queryWrapper.le("publish_dt", curr);
            }else{
                queryWrapper.le("publish_dt",publishEndDate);
            }
        }else{
            queryWrapper.le("publish_dt",curr);
        }
        queryWrapper.eq("notice_status", "1");

        QueryWrapper<SysMNoticeType> typeQueryWrapper = new QueryWrapper();
        List<SysMNoticeType> sysMNoticeTypes = sysMNoticeTypeMapper.selectList(typeQueryWrapper);
        Map<Long, String> typeMap = sysMNoticeTypes.stream().collect(Collectors.toMap(SysMNoticeType::getId, noticeType -> noticeType.getNoticeTypeName()));
        Map<Long, String> type2Map = sysMNoticeTypes.stream().collect(Collectors.toMap(SysMNoticeType::getId, noticeType -> noticeType.getNoticeTypeNameUs()));
        Page<SysMNotice> page = PageUtil.preparePageParams(noticePageRequest, "publishDt", "desc");

        IPage<SysMNotice> sysMNoticeIPage = sysMNoticeMapper.selectPage(page, queryWrapper);
        List<NoticeListResponse> responseList = new ArrayList<>();

        Long pagenum = noticePageRequest.getPagenum();
        Long pagesize = noticePageRequest.getPagesize();
        long sn = (pagenum-1)*pagesize+1;
        List<SysMNotice> records = sysMNoticeIPage.getRecords();
        BeanUtils.copyProperties(sysMNoticeIPage,pageData);
        for (SysMNotice sysMNotice : records) {
            NoticeListResponse noticeListResponse = new NoticeListResponse();
            noticeListResponse.setSn(sn);
            noticeListResponse.setNoticeId(sysMNotice.getId());
            noticeListResponse.setNoticeTitle(sysMNotice.getNoticeTitle());
            noticeListResponse.setNoticeTypeNameUs(type2Map.get(sysMNotice.getNoticeTypeId()));
            noticeListResponse.setNoticeTypeName(typeMap.get(sysMNotice.getNoticeTypeId()));
            noticeListResponse.setPublishDt(sysMNotice.getPublishDt());
            responseList.add(noticeListResponse);
            sn++;
        }
        pageData.setRecords(responseList);
        return pageData;
    }

    @Override
    public NoticeDetailResponse selectDetail(Long noticeId) {

        if(noticeId ==null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1798638791));
        }
        QueryWrapper<SysMNotice> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("id",noticeId);

        SysMNotice sysMNotice = sysMNoticeMapper.selectOne(queryWrapper);
        if(sysMNotice !=null){
            QueryWrapper<SysMNoticeType> typeQueryWrapper = new QueryWrapper<>();
            typeQueryWrapper.eq("id",sysMNotice.getNoticeTypeId());
            SysMNoticeType sysMNoticeType = sysMNoticeTypeMapper.selectOne(typeQueryWrapper);
            NoticeDetailResponse detailResponse = new NoticeDetailResponse();
            detailResponse.setNoticeId(sysMNotice.getId());
            detailResponse.setNoticeTitle(sysMNotice.getNoticeTitle());
            detailResponse.setNoticeContent(sysMNotice.getNoticeContent());
            if(Objects.nonNull(sysMNoticeType)){
                detailResponse.setNoticeTypeName(sysMNoticeType.getNoticeTypeName());
            }
            detailResponse.setPublishDate(sysMNotice.getPublishDt());
            return detailResponse;
        }
        return null;

    }

    @Override
    public List<NoticeListResponse> selectList() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<NoticeListResponse> responseList = new ArrayList<>();
        //查询全部的公告信息
        Date date = new Date();
        List<SysMNotice> sysMNotices = sysMNoticeMapper.selectNoticeList(authUser.getUserSid(),date);
        //查询已查看或者下次不在提下的公告
        if(!CollectionUtils.isEmpty(sysMNotices)){
            sysMNotices.stream().forEach(record->{
                NoticeListResponse infosResponse = new NoticeListResponse();
                infosResponse.setNoticeId(record.getId());
                infosResponse.setNoticeTitle(record.getNoticeTitle());
                infosResponse.setNoticeTypeName(record.getNoticeTypeName());
                infosResponse.setPublishDt(record.getPublishDt());
                infosResponse.setNoticeContent(record.getNoticeContent());
                responseList.add(infosResponse);
            });
        }
        return responseList;
    }

    @Override
    public void sendZipCompressPassword(AuthUser user, String zipFileName, String password) {
        if (StringUtils.isEmpty(password)) {
            return;
        }
        if (user == null) {
            return;
        }

        HashMap<String, String> content = new HashMap<>();
        content.put("fileName", zipFileName.substring(0, zipFileName.lastIndexOf('.')));
        content.put("password", password);
        content.put("userAccount", user.getAccount());

        String email = user.getEmail();
        if (!email.matches(MAIL_REGEXP)) {
            return;
        }

        MailNotificationMq mailNotificationMq = new MailNotificationMq();
        mailNotificationMq.setMsgId(NotificationConsts.OtherMsg.COMMON_ZIP_COMPRESS_PASSWORD);
        mailNotificationMq.getMails().add(email);
        mailNotificationMq.setMap(content);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, mailNotificationMq);

    }
}
