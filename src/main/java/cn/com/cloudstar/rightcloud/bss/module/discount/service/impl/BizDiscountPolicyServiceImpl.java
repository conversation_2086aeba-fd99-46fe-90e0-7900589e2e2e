/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.discount.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import cn.com.cloudstar.rightcloud.bss.common.constants.status.PolicyStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.CloudEnvEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountPolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscountPolicy;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.DescribeDiscountPolicyPageRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.DescribeDiscountPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.OperateDiscountPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.response.DescribeDiscountPolicyDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountPolicyService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <p>
 * 折扣策略表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@Service
public class BizDiscountPolicyServiceImpl extends ServiceImpl<BizDiscountPolicyMapper, BizDiscountPolicy> implements IBizDiscountPolicyService {

    @Resource
    private BizDiscountPolicyMapper bizDiscountPolicyMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDiscountPolicy(BizDiscountPolicy bizDiscountPolicy) {
        // 折扣策略管理那里是先删再增加
        if (PolicyStatus.PLATFORM.equals(bizDiscountPolicy.getCategory())) {
            QueryWrapper<BizDiscountPolicy> queryWrapper= new QueryWrapper<>();
            //多运营实体过滤
            queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
            queryWrapper.eq("category", PolicyStatus.PLATFORM);
            this.remove(queryWrapper);
        }
        int count = this.count(createCheckQuery(bizDiscountPolicy)
                .eq(BizDiscountPolicy::getPolicyType, bizDiscountPolicy.getPolicyType()));
        if (count > 0) {
            BizException.e("该客户已存在该环境的产品折扣策略");
        }
        //如果存在启用的策略，则当前策略状态为禁用
        int enableCount = this.count(createCheckQuery(bizDiscountPolicy)
                .eq(BizDiscountPolicy::getStatus, 1));
        if (enableCount > 0) {
            bizDiscountPolicy.setStatus(0);
        }
        return this.save(bizDiscountPolicy);
    }

    /**
     * 构建检查查询条件
     */
    private LambdaQueryWrapper<BizDiscountPolicy> createCheckQuery(BizDiscountPolicy bizDiscountPolicy) {
        LambdaQueryWrapper<BizDiscountPolicy> query = Wrappers.<BizDiscountPolicy>lambdaQuery()
                .eq(BizDiscountPolicy::getEntityId, bizDiscountPolicy.getEntityId())
                .eq(BizDiscountPolicy::getUserAccountId, bizDiscountPolicy.getUserAccountId())
                .eq(BizDiscountPolicy::getCloudEnvScope, bizDiscountPolicy.getCloudEnvScope());
        if (ProductComponentEnum.COMPUTE.getKey().contains(bizDiscountPolicy.getProductScope())) {
            query.in(BizDiscountPolicy::getProductScope, ProductComponentEnum.COMPUTE.getKey());
        } else {
            query.like(BizDiscountPolicy::getProductScope, bizDiscountPolicy.getProductScope());
        }
        return query;
    }

    @Override
    public RestResult updateDiscountPolicy(OperateDiscountPolicyRequest request) {
        BizDiscountPolicy one = this.bizDiscountPolicyMapper.selectById(request.getPolicySid());
        if (Objects.isNull(one)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (StringUtils.equals(one.getPolicyType(), PolicyStatus.CUSTOMER)) {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(one.getUserAccountId());
            if (!Objects.equals(one.getUserAccountId(), request.getUserAccountId()) || !StringUtils.equals(bizBillingAccount.getOrgSid().toString(), request.getOrgSid())) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        // 删除
        if (PolicyStatus.DELETE.equals(request.getType())) {
            if (one.getStatus().equals(1)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_587662754));
            }
            this.removeById(request.getPolicySid());
        }

        // 禁用、修改
        if (PolicyStatus.DISABLE.equals(request.getType()) || PolicyStatus.UPDATE.equals(request.getType())) {
            BizDiscountPolicy bizDiscountPolicy = BeanConvertUtil.convert(request, BizDiscountPolicy.class);
            this.updateById(bizDiscountPolicy);
        }

        // 启用
        if (PolicyStatus.ENABLE.equals(request.getType())) {
            // 查询是否同一用户同一产品 启动折扣策略只能一个
            List<BizDiscountPolicy> onePolicy = this.bizDiscountPolicyMapper.
                    selectByParam(one.getUserAccountId(),StrUtil.splitToArray(one.getCloudEnvScope(), StrUtil.COMMA), StrUtil.splitToArray(one.getProductScope(), StrUtil.COMMA), one.getProductScope());
            if (onePolicy.size() > 0) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1139927544));
            }
            BizDiscountPolicy bizDiscountPolicy = BeanConvertUtil.convert(request, BizDiscountPolicy.class);
            this.updateById(bizDiscountPolicy);
        }

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    @Override
    public Page<DescribeDiscountPolicyDetailResponse> getCustomerPage(DescribeDiscountPolicyPageRequest request) {
        Page<DescribeDiscountPolicyDetailResponse> page = PageUtil.preparePageParams(request);
        //多运营实体过滤
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("entityId",RequestContextUtil.getEntityId());
        List<DescribeDiscountPolicyDetailResponse> accountList = this.bizDiscountPolicyMapper.getCustomerPage(page,criteria.getCondition());

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        for (DescribeDiscountPolicyDetailResponse response : accountList) {
            List<BizDiscountPolicy> policyList = response.getPolicyList();
            // 替换描述
            this.convertToDesc(policyList);
            // 策略名称组装 例子： HPC高性能计算/弹性文件服务：共享,
            if(CollectionUtil.isEmpty(policyList)){
                continue;
            }
            String productScope = this.getProductScope(policyList);
            if (isUs) {
                productScope = this.productScopeToUs(productScope);
            }
            response.setProductScope(productScope);
        }

        page.setRecords(accountList);
        return page;
    }

    private String productScopeToUs(String productScope) {
        productScope = productScope.replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                .replaceAll("云硬盘", "Cloud Hard Disk")
                .replaceAll("弹性裸金属", "Elastic bare metal")
                .replaceAll("对象存储", "Object storage")
                .replaceAll("AI开发平台专属资源池", "Ascend Modelarts exclusive resource pool")
                .replaceAll("客户折扣独享", "Exclusive customer discount")
                .replaceAll("平台折扣独享", "Platform Discount Exclusive")
                .replaceAll("共享", "Share")
                .replaceAll("不享受", "Don't enjoy");
        return productScope;
    }

    @Override
    public IPage<BizDiscountPolicy> findCustomerByUserAccountId(DescribeDiscountPolicyRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if(request.getUserAccountId() != null){
            BizBillingAccount account = bizBillingAccountMapper.selectById(request.getUserAccountId());
            Assert.notNull(account, WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            if(!authUserInfo.getEntityId().equals(account.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
            }
        }
        Page<BizDiscountPolicy> page = PageUtil.preparePageParams(request);
        QueryWrapper<BizDiscountPolicy> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_account_id", request.getUserAccountId());
        IPage<BizDiscountPolicy> result = this.page(page,queryWrapper);
        // 替换描述
        this.convertToDesc(result.getRecords());
        return result;
    }


    /**
     * 拼接策略描述,环境目前就一个
     *
     * @param policyList 所有策略
     * @return 描述 HSCO：HPC高性能计算 HPC、弹性文件服务 SFS：共享；
     */
    private String getProductScope(List<BizDiscountPolicy> policyList) {
        StringBuilder productScope = new StringBuilder();
        // 所有启用的折扣策略
        List<BizDiscountPolicy> enabledList = policyList.stream().filter(f -> f.getStatus().equals(1)).collect(Collectors.toList());
        for (BizDiscountPolicy bizDiscountPolicy : enabledList) {
            String productScopeStr = bizDiscountPolicy.getProductScopeList().stream().collect(Collectors.joining("/"));
            productScope.append(productScopeStr).append(StrUtil.COLON)
                    .append(bizDiscountPolicy.getPolicyTypeStr()).append(StrUtil.COMMA);
        }
        String result = productScope.toString();
        // 去掉最后一个逗号
        if (StrUtil.isNotBlank(result)) {
            productScope.deleteCharAt(productScope.length() - 1);
            result = productScope.toString();
        }
        return result;
    }

    /**
     * 转换描述
     *
     * @param result 返返回数据
     */
    private void convertToDesc(List<BizDiscountPolicy> result) {
        if (ObjectUtils.isEmpty(result)) {
            return;
        }
        result.forEach(bdp -> {
            bdp.setProductScopeList(ProductComponentEnum.transformDesc(StrUtil.splitToArray(bdp.getProductScope(), StrUtil.COMMA)));
            bdp.setCloudEnvScopeList(CloudEnvEnum.transformDesc(StrUtil.splitToArray(bdp.getCloudEnvScope(), StrUtil.COMMA)));
            bdp.setPolicyTypeStr(DiscountPolicyEnum.codeFromName(bdp.getPolicyType()));
        });
    }
}
