/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean.request;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/04/26 14:06
 */
@Data
@ApiModel("更新用户密码策略")
public class UpdatePasswordPolicyRequest {

    /**
     * 最小长度
     */
    @ApiModelProperty("最小长度")
    @NotNull
    @Max(value = 12, message = "密码长度最大不能超过12位")
    @Min(value = 6, message = "密码长度最小不能小于6位")
    private int minLength;

    /**
     * 密码包含规则
     */
    @ApiModelProperty("密码包含规则")
    @NotNull
    @EnumValue(strValues = {"uppercase", "number", "special_charactor", "lowercase"}, message = "密码包含规则参数异常")
    private List<String> charactorType;

    /**
     * 不同字符数限制
     */
    @ApiModelProperty("不同字符数限制")
    @NotNull
    @Max(value = 12, message = "最少相异字符串最大不能超过12位")
    private int charactorLimit;

    /**
     * 常用密码剔除
     */
    @ApiModelProperty("常用密码剔除")
    @NotNull
    private List<String> ruleOut;

    /**
     * 登陆失败锁定开启状态
     */
    @ApiModelProperty("登陆失败锁定开启状态")
    @NotNull
    private boolean loginfailureEnable;

    /**
     * 最大失败数
     */
    @ApiModelProperty("最大失败数")
    @NotNull
    @Min(value = 0, message = "登录最大失败数不能小于1")
    @Max(value = 10, message = "登录最大失败数不能大于10")
    private int loginfailureCount;

    /**
     * 账号有效期开启状态
     */
    @ApiModelProperty("账号有效期开启状态")
    @NotNull
    private boolean accountValidity;

    /**
     * 有效天数
     */
    @ApiModelProperty("有效天数")
    @NotNull
    @Min(value = 0, message = "有效天数最小为0")
    @Max(value = 360, message = "有效天数最大360天")
    private int expireTime;

    /**
     * 跳过双因子
     */
    @ApiModelProperty("跳过双因子")
    @NotNull
    private Boolean skip2FA;

    /**
     * 密码有效期
     */
    @ApiModelProperty("密码有效期")
    @Max(value = 360, message = "密码有效期最大360天")
    private Long pwdExpireTime;

    /**
     * 密码有效期开启状态
     */
    @ApiModelProperty("密码有效期开启状态")
    private Boolean pwdExpireTimeValidity;

    /**
     * 密码最少使用天数
     */
    @ApiModelProperty("密码最少使用天数")
    @Min(value = 0, message = "密码最少使用天数不能小于0")
    private Long pwdLeastUsedDay;

    /**
     * 密码不能与前N个历史密码重复
     */
    @ApiModelProperty("密码不能与前N个历史密码重复")
    @Min(value = 0, message = "密码不能与前N个历史密码重复参数错误")
    @Max(value = 10, message = "密码不能与前N个历史密码重复参数错误")
    private Long pwdRepeatNum;
    private String secAuth;

}
