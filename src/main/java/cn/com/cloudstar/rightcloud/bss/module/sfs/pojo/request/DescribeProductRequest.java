/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request;

import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;


/**
 * <AUTHOR>
 * @since 2019/11/1 16:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询产品请求参数")
public class DescribeProductRequest extends BaseRequest {

    /**
     * 产品类别id
     */
    @ApiModelProperty("产品类别id")
    private Long categoryId;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @SafeHtml
    private String productNameLike;

    /**
     * 产品组件
     */
    @ApiModelProperty("产品组件")
    @SafeHtml
    private String serviceComponent;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    @SafeHtml
    @Pattern(regexp = "^((?!%0d|%0a|%20|or).)*$",message = "输入可能存在CRLF攻击！")
    private String productCode;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @SafeHtml
    private String status;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    @SafeHtml
    private String tagNameLike;


}
