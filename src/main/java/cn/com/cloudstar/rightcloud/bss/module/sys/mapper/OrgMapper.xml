<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper" >
  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.common.pojo.Org" >
    <id column="org_sid" property="orgSid" jdbcType="BIGINT" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
    <result column="org_code" property="orgCode" jdbcType="VARCHAR" />
    <result column="org_type" property="orgType" jdbcType="VARCHAR" />
    <result column="owner" property="owner" jdbcType="BIGINT" />
    <result column="tree_path" property="treePath" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="org_icon" property="orgIcon" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="BIGINT" />
    <result column="distributorName" property="distributorName" jdbcType="VARCHAR" />
    <result column="certification_status" property="certificationStatus" jdbcType="VARCHAR"/>

  </resultMap>
  <update id="updateSolutionById">
    update sys_m_org set solution=#{solution} where org_sid = #{orgSid}
  </update>
  <select id="selectCustomerOrgSids" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select
    org_sid
    from sys_m_org
    where org_sid = #{orgSid} OR tree_path LIKE concat( '/%', #{orgSid}, '/%' )
  </select>

  <select id="selectSubOrgSids" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select
      org_sid
    from sys_m_org
    where tree_path LIKE concat( '/%', #{orgSid}, '/%' )
  </select>

  <select id="selectCustomerAccountIds" resultType="java.lang.Long" parameterType="java.lang.Long">
    select id
    from biz_billing_account
    where org_sid in (
      select  o.org_sid
      from sys_m_org o left join sys_bss_entity_user u on o.org_sid = u.org_sid
      where o.org_type = 'company'
        and (o.org_sid = #{orgSid} OR
             o.tree_path LIKE concat('/%', #{orgSid}, '/%')))
  </select>
  <select id="getOrgInfo" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
      smo.org_sid ,
      smo.org_name ,
      smo.org_code ,
      smo.org_type ,
      smo.owner ,
      smo.tree_path ,
      smo.parent_id ,
      smo.org_icon ,
      smo.status ,
      (
        select
          smo2.org_name
        from
          sys_m_org smo2
        where
          smo2.org_sid = smo.parent_id )distributorName
    from
      sys_m_org smo
    where
      1 = 1
    <if test="orgIds != null" >
      and smo.org_sid in
      <foreach collection="orgIds" open="(" separator="," close=")" item="val">
        #{val}
      </foreach>
    </if>
  </select>
    <select id="toCheckGroupCount" resultType="java.lang.Integer">
      select count(1)
      from sys_m_group where ( org_sid in (select org_sid
                                         from sys_m_org where org_sid = #{rootOrg} or tree_path like concat( '/%', #{rootOrg}, '/%' ) ) or sys_default=1)
        and group_sid in
      <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </select>

  <select id="selectOrgSidBySid" parameterType="map" resultMap="BaseResultMap">
    select * from sys_m_org  where parent_id=#{companyId} and org_code=#{orgCode};
  </select>

  <select id="selectOrgByUserSidAndType" parameterType="map" resultMap="BaseResultMap">
    select smo.org_type,smo.org_sid,smo.owner,smo.certification_status from sys_m_org smo where org_sid=#{orgSid} and org_type=#{type}
  </select>
  <select id="selectDistributorBy" resultMap="BaseResultMap">
    SELECT *
    from sys_m_org
    where org_sid = (
      SELECT parent_id
      from sys_m_org
      where org_name = #{orgName})
  </select>

  <select id="selectContactName" resultType="java.lang.String">
    select contact_name from sys_m_org where org_sid = #{orgSid}
  </select>
  <select id="selectAllSidsByOrgSid" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select org_sid
    from sys_m_org
    where org_sid = #{orgSid}
       or tree_path like CONCAT('%', #{orgSid}, '%')
  </select>
  <select id="selectAllSidsByOrgSidString" resultType="java.lang.String" parameterType="java.lang.String" >
    select org_sid
    from sys_m_org
    where org_sid = #{orgSid}
       or tree_path like CONCAT('%', #{orgSid}, '%')
  </select>
  <select id="getByParentId" resultType="java.lang.Long">
    select org_sid from sys_m_org where parent_id in
    <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
      #{parentId}
    </foreach>
  </select>
  <select id="selectOrg" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM `sys_m_org` s
    WHERE s.org_sid = (SELECT b.org_sid
                       FROM biz_billing_account b
                       WHERE b.id =
                             (SELECT c.account_id FROM biz_contract c WHERE c.contract_id = #{condition.contractId})
                         AND s.tree_path LIKE CONCAT('%', #{condition.orgSid}, '%'))
  </select>
  <select id="selectLdapOuByOrgSid" resultType="java.lang.String">
    select ldap_ou from sys_m_org where org_sid = #{orgSid}
  </select>

    <select id="selectOrgByIds"
            resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.CurrentOrgVO">

    </select>

  <select id="selectUserByOrgSid" parameterType="map" resultType="cn.com.cloudstar.rightcloud.bss.common.pojo.User">
    select * from sys_m_user where company_id=#{orgSid} and parent_sid is null
  </select>

  <select id="selectAllOrg" resultMap="BaseResultMap">
    select * from sys_m_org
  </select>
  <select id="selectOrgsByCustomize" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
      resultType="cn.com.cloudstar.rightcloud.bss.common.pojo.Org">
    select * from sys_m_org
    <where>
      <if test="condition.customizationInfoKey != null and condition.customizationInfoKey.size != 0
                and condition.customizationInfoValue != null and condition.customizationInfoValue.size != 0">
        <foreach item="item" collection="condition.customizationInfoKey" index="idx" open=" " separator=" " close=" ">
          and CASE WHEN JSON_VALID(customization_info) THEN JSON_EXTRACT(customization_info, CONCAT('',
          REPLACE(
          SUBSTRING_INDEX(
          JSON_SEARCH(customization_info, 'one', #{item}, NULL, '$[*].attrKey'),'.',1),'"',''), '.attrValue'))
          LIKE CONCAT('%', '${condition.customizationInfoValue[idx]}', '%')
          ELSE NULL END
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectAllOrgSimple" resultMap="BaseResultMap">
    select org_sid,org_name,org_type,parent_id from sys_m_org
  </select>

  <select id="selectAllSidsByOrgSidV" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select org_sid
    from sys_m_org
    where org_sid = #{orgSid}
       or tree_path like CONCAT('%/', #{orgSid}, '/%')
  </select>
</mapper>
