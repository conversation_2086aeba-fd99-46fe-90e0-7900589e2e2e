/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.ShareSupportClusterTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.OrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareProtocol;
import cn.com.cloudstar.rightcloud.common.enums.HPCPropertyKey;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants.OrderMessage;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResShareType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResShareTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResShareTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/16.
 */
@Service
@Slf4j
public class ShareOrderServiceImpl extends AbstractOrderService {

    private static final String SFS_MAX_STORAGE_KEY = "hpc.sfs.max.storage";
    private static final String SFS_SPECIAL_STORAGE_INTERVAL = "hpc.sfs.special.storage.interval";

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @DubboReference
    private ShareRemoteService shareService;

    @DubboReference
    private ResShareTypeRemoteService shareTypeRemoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String apply(ApplyServiceVO serviceVO) {

        ApplyEntity applyEntity = before();

        if (applyEntity.getAuthUser().getParentSid() != null) {
            return WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_ERROR);
        }

        cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult restResult = orderService.checkAuth(applyEntity.getAuthUser().getUserSid());
        if (!restResult.getStatus()) {
            return WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_VERIFIED_ERROR);
        }

        cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult freeze = orderService.isFreeze(applyEntity.getAuthUser().getUserSid());
        if (Convert.toBool(freeze.getData())) {
            return WebUtil.getMessage(OrderMessage.ORDER_APPLICANT_STATUS_ERROR);
        }

        validateSpec(serviceVO);

        execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);

        after(applyEntity);
        return result;
    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        if (CollectionUtil.isEmpty(serviceVO.getProductInfo())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_VALID_FAILURE));
        }
        ProductInfoVO productInfo = CollectionUtil.getFirst(serviceVO.getProductInfo());

        //SFS2.0 dpc&nfs 默认替换为 nfs&dpc
        Object data = productInfo.getData();
        if(data !=null && data instanceof String){
            String jsonString = (String) data;
            String shareProto = "dpc&nfs";
            if (StringUtil.isNotBlank(jsonString) && jsonString.contains(shareProto)) {
                jsonString = jsonString.replaceAll(shareProto,"nfs&dpc");
                productInfo.setData(jsonString);
            }
        }

        ResShare resShare = BeanConvertUtil.convert(
            JSONUtil.parseObj(productInfo.getData()).getStr(productInfo.getProductCode()),
            ResShare.class);

        // ShareType存储类型检查
        QueryResShareTypeByParamsRequest shareTypeByParamsRequest = new QueryResShareTypeByParamsRequest();
        shareTypeByParamsRequest.setCloudEnvId(productInfo.getCloudEnvId());
        List<ResShareType> shareTypes = shareTypeRemoteService.selectByParams(shareTypeByParamsRequest);
        AssertUtil.requireContains(shareTypes.stream().map(ResShareType::getName).collect(Collectors.toList()),
                resShare.getShareType(), "存储类型不符合当前ShareType规则！请检查后重试！");
        // 存储协议类型检查
        AssertUtil.requireContains(Arrays
                        .asList(ShareProtocol.NFS_DPC, ShareProtocol.DPC,
                                ShareProtocol.NFS),
                Objects.nonNull(resShare.getShareProto()) ? resShare.getShareProto().toUpperCase() : null,
                "存储协议类型只包含：NFS，DPC和NFS&DPC");

       /* String storageInterval = PropertiesUtil.getProperty(SFS_SPECIAL_STORAGE_INTERVAL);
        String[] split = StrUtil.split(storageInterval, StrUtil.DASHED);
        Integer lower = 50;
        Integer upper = 200;
        if (split.length > 1) {
            lower = Convert.toInt(split[0]);
            upper = Convert.toInt(split[1]);
        }
        Range<Integer> range = Range.closed(lower, upper);
        if (!range.contains(resShare.getSize())) {
            throw new BizException(WebUtil.getMessage(Sfs2Message.SIZE_ERROR, new Object[]{upper, lower}));
        }
        // 检验可申请的最大容量 查询配置
        String configValue = PropertiesUtil.getProperty(SFS_MAX_STORAGE_KEY);
        // 查询当前使用总量
        ResShareParams resShareParams = new ResShareParams();
        resShareParams.setIgnoreDataFilter(true);
        resShareParams.setStatusNotIn(Lists.newArrayList(ShareStatus.ERROR, ShareStatus.DELETED));
        resShareParams.setChargeType(productInfo.getChargeType());
        List<ResShare> shareList = shareService.getShareList(resShareParams);
        // 共享SFS使用量
        List<Long> allSFSIds = sfProductResourceMapper.getAllSFSIds();
        allSFSIds.addAll(shareList.stream().map(ResShare::getId).collect(Collectors.toList()));
        log.info("弹性文件服务已使用ID：[{}]", JSON.toJSONString(shareList));
        BigDecimal all = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(allSFSIds)) {
            all = shareService.sumSizeByIds(allSFSIds);
        }
        all = all.add(new BigDecimal(resShare.getSize()));
        int max = Integer.parseInt(configValue);
        log.info("弹性文件服务申请量：[{}]，总量：[{}], 创建SFS量：[{}]", all, max, resShare.getSize());
        if (all.compareTo(new BigDecimal(max)) > 0) {
            throw new BizException(WebUtil.getMessage(Sfs2Message.OVER_MAX_SIZE_LIMIT));
        }*/
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ProductInfoVO productInfoVO = CollectionUtil.getFirst(serviceVO.getProductInfo());
        ResShare resShare = BeanConvertUtil.convert(
            JSONUtil.parseObj(productInfoVO.getData()).getStr(productInfoVO.getProductCode()),
            ResShare.class);
        if ("test".equalsIgnoreCase(resShare.getShareType())) {
            resShare.setShareType(null);
        }
        resShare.setHpcVersion(3);
        resShare.setCloudEnvId(productInfoVO.getCloudEnvId());
        ServiceOrder serviceOrder = applyEntity.getOrder();
        resShare.setServiceOrderId(serviceOrder.getId());
        resShare.setCreatedOrgSid(serviceOrder.getOrgSid());
        resShare.setOrgSid(serviceOrder.getOrgSid());
        resShare.setOwnerId(serviceOrder.getOwnerId());
        ServiceOrderDetail detail = CollectionUtil.getFirst(applyEntity.getOrderDetails());
        resShare.setStartTime(detail.getStartTime());
        resShare.setEndTime(detail.getEndTime());
        resShare.setChargeType(detail.getChargeType());
        WebUserUtil.prepareInsertParams(resShare);
        // 增加产品关联
        resShare.setCategoryId(productInfoVO.getServiceId());
        resShare.setIsClusterDefault(false);
        if (HPCPropertyKey.SWITCH_ON.getCode().equals(PropertiesUtil.getProperty(HPCPropertyKey.HPC_PREDEPLOY_SWITCH_KEY.getCode()))) {
            resShare.setSupportClusterType(ShareSupportClusterTypeEnum.PREDEPLOY.getCode());
        }else {
            resShare.setSupportClusterType(ShareSupportClusterTypeEnum.ONDEMAND.getCode());
        }
        RestResult restResult = shareService.createShare(
            resShare);
        if (restResult.getStatus()) {
            Object data = restResult.getData();
            if (Objects.nonNull(data)) {
                applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(data)));
                //更新集群id 到订单表
                orderMapper.updateOrderByClusterId(serviceOrder.getOrderSn(), Convert.toStr(data));
            }
        } else {
            throw new BizException(Convert.toStr(restResult.getMessage()));
        }
        return null;
    }

    @Override
    public void after(ApplyEntity applyEntity) {
        if (CollectionUtil.isEmpty(applyEntity.getOrderDetails())) {
            return;
        }
        if (CollectionUtil.isEmpty(applyEntity.getResourceId())) {
            return;
        }
        List<ServiceOrderResourceRef> orderResourceRefs = Lists.newArrayList();

        ServiceOrderDetail orderDetail = applyEntity.getOrderDetails().get(0);
        JSONObject configDesc = new JSONObject();
        configDesc.put("label", "ID");
        configDesc.put("attrKey", "id");
        configDesc.put("value", String.join(StrUtil.COMMA, applyEntity.getResourceId()));

        JSONArray parent = JSON.parseArray(orderDetail.getProductConfigDesc());
        if (parent != null) {
            parent.add(0, configDesc);
            orderDetail.setProductConfigDesc(JSON.toJSONString(parent));
            serviceOrderDetailMapper.updateById(orderDetail);
        }

        applyEntity.getResourceId().forEach(resourceId -> {
            ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
            resourceRef.setOrderDetailId(orderDetail.getId());
            resourceRef.setType(orderDetail.getServiceType());
            resourceRef.setResourceId(resourceId);
            orderResourceRefs.add(resourceRef);
            applyEntity.getPriceDetails().forEach(priceDetail -> {
                priceDetail.setRefInstanceId(JSONArray.toJSONString(applyEntity.getResourceId()));
                priceDetail.setRefKey(StrUtil.concat(true, priceDetail.getRefKey(),
                    resourceId, "-"));
            });
        });
        if (CollectionUtil.isNotEmpty(applyEntity.getPriceDetails())) {
            serviceOrderPriceDetailService.updateBatchById(applyEntity.getPriceDetails());
        }

        serviceOrderResourceRefService.saveBatch(orderResourceRefs);
    }
}
