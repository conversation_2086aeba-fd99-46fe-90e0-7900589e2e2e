/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户当前组织
 *
 * <AUTHOR>
 * @date 2019/7/15.
 */
@ApiModel(description = "用户当前组织")
@Data
public class CurrentOrgVO {

    /**
     * 组织sid
     */
    @ApiModelProperty("组织ID")
    private Long orgSid;
    /**
     * 组织名字
     */
    @ApiModelProperty("组织名称")
    private String orgName;
    /**
     * 组织类型
     */
    @ApiModelProperty("组织类型")
    private String orgType;
    /**
     * 上级组织ID
     */
    @ApiModelProperty("上级组织ID")
    private Long parentOrgSid;
    /**
     * 项目余额
     */
    @ApiModelProperty("项目余额")
    private BigDecimal balance;
    /**
     * 项目账户sid
     */
    @ApiModelProperty("项目账户 ID")
    private Long accountSid;
    /**
     * 配额控制方式
     */
    @ApiModelProperty("配额控制方式")
    private String quotaMode;
    /**
     * 是否跳过双因子认证
     */
    @ApiModelProperty("是否跳过双因子认证")
    private Boolean skip2FA;
    /**
     * 企业认证状态
     */
    @ApiModelProperty("企业认证状态")
    private String certificationStatus;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("自定义信息")
    private String customizationInfo;
}
