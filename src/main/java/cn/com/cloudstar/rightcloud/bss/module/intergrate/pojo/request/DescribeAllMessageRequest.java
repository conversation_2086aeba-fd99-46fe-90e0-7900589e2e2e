/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

import cn.com.cloudstar.rightcloud.oss.common.safe.EnumValue;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseParam;
import org.hibernate.validator.constraints.Length;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "显示所有信息请求参数")
public class DescribeAllMessageRequest extends BaseParam implements Serializable {

    /**
     * 消息状态
     */
    @NotBlank
    @ApiModelProperty(value = "消息状态")
    @EnumValue(strValues = {"01","02"})
    @SafeHtml
    private String readFlag;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    @NotIllegalString
    @SafeHtml
    @Length(max = 256, message = "最大长度不超过256")
    private String msgTitle;
}
