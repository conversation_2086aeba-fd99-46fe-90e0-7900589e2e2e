/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.MessageUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResMaFlavorResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaFlavorService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.CouponService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.CpuArchEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.MaPoolTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/3/1 15:38
 * ModelArts开通类
 */
@Service
@Slf4j
public class InnerProductOrderServiceImpl extends AbstractOrderService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private ISfProductResourceService sfProductResourceService;
    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private ResMaFlavorService resMaFlavorService;

    @Autowired
    private CouponService couponService;

    @Override
    public String apply(ApplyServiceVO serviceVO) {
        ApplyEntity applyEntity = before();

        if (applyEntity.getAuthUser().getParentSid() != null) {
            return "只有租户管理员可以开通服务";
        }
        ProductInfoVO productInfoVO = CollectionUtil.getFirst(serviceVO.getProductInfo());
        String productCode = productInfoVO.getProductCode();
        Long userSid = applyEntity.getAuthUser().getUserSid();
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productCode)) {
            String poolType = productInfoVO.getPoolType();
            if (StringUtils.isBlank(poolType) ||
                    (!MaPoolTypeEnum.DEDICATE.getType().equals(poolType) && !MaPoolTypeEnum.LOGICAL.getType().equals(poolType))) {
                return "资源池类型参数异常";
            }

            String cpuArch = productInfoVO.getCpuArch();
            if (StringUtils.isBlank(cpuArch) ||
                    (!CpuArchEnum.X86.getType().equals(cpuArch) && !CpuArchEnum.ARM64.getType().equals(cpuArch))) {
                return "资源池架构参数异常";
            }

            ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(ProductCodeEnum.MODEL_ARTS.getProductType(), applyEntity.getAuthUser().getUserSid());
            if (OrderType.APPLY.equals(productOrderInfo.getType())) {
                if(!OrderStatus.COMPLETED.equals(productOrderInfo.getStatus())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_831990527));
                }
            } else {
                if(OrderStatus.COMPLETED.equals(productOrderInfo.getStatus())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_831990527));
                }
            }

            // 验证重名
            List<String>  names = serviceOrderMapper.selectResourceName(userSid, productCode);
            if (names.contains(serviceVO.getProductName())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_96609621)+ serviceVO.getProductName() +WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049732961));
            }

            // 设置转化系数
            this.setConvertRatio(serviceVO.getProductInfo());

        }
        else if (ProductCodeEnum.MODEL_ARTS.getProductType().equals(productCode)) {
            ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(ProductCodeEnum.MODEL_ARTS.getProductType(), applyEntity.getAuthUser().getUserSid());

            // 已申请ModelArts
            boolean flg = true;
            if (Objects.isNull(productOrderInfo) || Strings.isBlank(productOrderInfo.getType())) {
                flg = false;
            } else if (OrderType.APPLY.equals(productOrderInfo.getType()) && OrderStatus.LEV2_REFUSED.equals(productOrderInfo.getStatus())) {
                flg = false;
            } else if (OrderType.RELEASE.equals(productOrderInfo.getType()) && OrderStatus.COMPLETED.equals(productOrderInfo.getStatus())) {
                flg = false;
            }
            if (flg) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_774444276));
            }
        }

        serviceVO.setProjectId(serviceVO.getProjectId());
        //验证用户是否实名
        RestResult restResult = orderService.checkAuth(userSid);
        if (!restResult.getStatus()) {
            return "请先实名认证";
        }

        validateSpec(serviceVO);
        // execute重要代码
        ((InnerProductOrderServiceImpl ) AopContext.currentProxy()).execute(serviceVO, applyEntity);
        // 保存res_ma_pool表
        String result = remoteInvoke(serviceVO, applyEntity);
        //保存资源ID.用作计费
        ServiceOrderDetail orderDetails = applyEntity.getOrderDetails().get(0);
        if (Objects.nonNull(result)) {
            applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(result)));
        } else {
            ServiceOrderResourceRef resourceRef = serviceOrderResourceRefService.getOrderResourceRefByDetailId(
                    orderDetails.getId());
            if (Objects.nonNull(resourceRef)) {
                applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(resourceRef.getResourceId())));
            }

        }

        after(applyEntity);
        return null;
    }


    /**
     *  设置转化系数
     * @param productInfos
     */
    private void setConvertRatio(List<ProductInfoVO> productInfos) {
        try {
            for (ProductInfoVO productInfoVO : productInfos) {
                //设置单位算力
                BigDecimal unitComputingPower = new BigDecimal(productInfoVO.getComputingPower()).divide(new BigDecimal(productInfoVO.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
                productInfoVO.setUnitComputingPower(unitComputingPower.toString());

                // 设置转化系数
                JSONObject object = JSON.parseObject(productInfoVO.getData().toString()).getJSONObject("DRP");
                Map<String, Object> query = new HashMap<>();
                query.put("phase", "normal");
                query.put("specName", object.getString("spec"));
                RestResult restResult = resMaFlavorService.getMaFlavoerList(query);
                if (restResult.getStatus()) {
                    Object data = restResult.getData();
                    List<ResMaFlavorResponse> responses =  BeanConvertUtil.convert((List<?>) data, ResMaFlavorResponse.class);
                    if (responses.size() > 0) {
                        ResMaFlavorResponse response = responses.get(0);
                        productInfoVO.setConvertRatio(response.getConvertRatio());
                    }
                }
            }
        } catch (Exception e) {
            log.error("MA专属开通设置转化系数异常： error: {}", e.getMessage());
        }

    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        ProductInfoVO productInfoVO = CollectionUtil.getFirst(serviceVO.getProductInfo());
        String productCode = productInfoVO.getProductCode();
        if (ProductCodeEnum.MODEL_ARTS.getProductType().equals(productCode)) {
            LambdaQueryWrapper<SfProductResource> wrapper = Wrappers.<SfProductResource>lambdaQuery()
                .eq(SfProductResource::getProductType, productCode)
                .in(SfProductResource::getStatus, SfProductEnum.PENDING, SfProductEnum.NORMAL,
                    SfProductEnum.DELETING,SfProductEnum.UNSUBSCRIBING)
                .eq(SfProductResource::getOrgSid, serviceVO.getProjectId());
            List<SfProductResource> sfProductResources = sfProductResourceMapper
                .selectList(wrapper);
            if (CollectionUtil.isNotEmpty(sfProductResources)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_831990527));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.execute(serviceVO, applyEntity);
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ServiceOrder order = applyEntity.getOrder();
        RestResult restResult = orderService.startProcessByOrder(order.getId());
        //bss需要对调用oss的结果做处理，否则会出现订单有，审批单没有等情况。
        if (Objects.nonNull(restResult)){
            if (!restResult.getStatus()){
                // 将优惠券状态重置为未使用
                BizCouponResource bizCouponResource = couponService.getCouponResourceByOrderId(order.getId());
                if (Objects.nonNull(bizCouponResource)) {
                    couponService.updateCouponStatus(bizCouponResource.getCouponSid(), CouponStatusEnum.UNUSED.getCode());
                    couponService.updateCouponUsed(bizCouponResource.getCouponSid());
                }
                serviceOrderMapper.deleteById(order.getId());
                QueryWrapper<SfProductResource> resourceQueryWrapper = new QueryWrapper<>();
                resourceQueryWrapper.eq("service_order_id",order.getId());
                sfProductResourceMapper.delete(resourceQueryWrapper);
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
            }
        }

        return JSONUtil.toJsonStr(restResult.getData());
    }

    @Override
    public void inquiryPrice(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        super.inquiryPrice(serviceVO, applyEntity);
        List<InquiryPriceResponse> prices = applyEntity.getPrices();

        if (CollectionUtil.isEmpty(prices)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1793371337));
        }

        prices = applyEntity.getPrices().stream().filter(e -> e.getProductCode().equals(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(prices) || CollectionUtil.isEmpty(prices.get(0).getBillingPrices())) {
            for (InquiryPriceResponse response : prices) {
                String productCategory = response.getProductCategory();
                if (CollectionUtil.isEmpty(response.getBillingPrices())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1535266246, new String[]{productCategory}));
                }
            }
        }

        prices.forEach(inquiryPriceResponse -> {
            if (!ChargeTypeEnum.PREPAID.getValue().equals(applyEntity.getChargeType())) {
                inquiryPriceResponse.setOriginalPrice(BigDecimal.ZERO);
                inquiryPriceResponse.setDiscountPrice(BigDecimal.ZERO);
                inquiryPriceResponse.setTradeOncePrice(BigDecimal.ZERO);
            }
        });
    }
}
