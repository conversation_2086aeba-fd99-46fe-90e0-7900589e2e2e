<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.access.mapper.PasswordPolicyMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="rule" property="rule" jdbcType="VARCHAR"/>
        <result column="min_length" property="minLength" jdbcType="INTEGER"/>
        <result column="charactor_limit" property="charactorLimit" jdbcType="INTEGER"/>
        <result column="ruled_out" property="ruledOut" jdbcType="VARCHAR"/>
        <result column="loginfailure_enable" property="loginfailureEnable" jdbcType="BIT"/>
        <result column="loginfailure_count" property="loginfailureCount" jdbcType="INTEGER"/>
        <result column="account_validity" property="accountValidity" jdbcType="BIT"/>
        <result column="expire_time" property="expireTime" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="pwd_expire_time" property="pwdExpireTime" jdbcType="BIGINT"/>
        <result column="pwd_expire_time_validity" property="pwdExpireTimeValidity" jdbcType="BIT"/>
        <result column="pwd_least_used_day" property="pwdLeastUsedDay" jdbcType="BIGINT"/>
        <result column="pwd_repeat_num" property="pwdRepeatNum" jdbcType="BIGINT"/>
        <result column="sec_auth" property="secAuth" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.userSid != null">
                and user_sid = #{condition.userSid}
            </if>
            <if test="condition.rule != null">
                and rule = #{condition.rule}
            </if>
            <if test="condition.minLength != null">
                and min_length = #{condition.minLength}
            </if>
            <if test="condition.charactorLimit != null">
                and charactor_limit = #{condition.charactorLimit}
            </if>
            <if test="condition.ruledOut != null">
                and ruled_out = #{condition.ruledOut}
            </if>
            <if test="condition.loginfailureEnable != null">
                and loginfailure_enable = #{condition.loginfailureEnable}
            </if>
            <if test="condition.loginfailureCount != null">
                and loginfailure_count = #{condition.loginfailureCount}
            </if>
            <if test="condition.accountValidity != null">
                and account_validity = #{condition.accountValidity}
            </if>
            <if test="condition.expireTime != null">
                and expire_time = #{condition.expireTime}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
    id, user_sid, rule, min_length, charactor_limit, ruled_out, loginfailure_enable,
    loginfailure_count, account_validity, expire_time, created_by, created_dt, updated_by, 
    updated_dt, version, org_sid, pwd_expire_time, pwd_expire_time_validity, pwd_least_used_day, pwd_repeat_num,sec_auth
  </sql>
    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_password_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_m_password_policy
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_m_password_policy
    where id = #{id}
  </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        delete from sys_m_password_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy">
        insert into sys_m_password_policy (id, user_sid, rule, min_length, charactor_limit, ruled_out,
        loginfailure_enable, loginfailure_count, account_validity, expire_time,
        created_by, created_dt, updated_by, updated_dt, version, org_sid)
        values (#{id}, #{userSid}, #{rule}, #{minLength}, #{charactorLimit}, #{ruledOut},
        #{loginfailureEnable}, #{loginfailureCount}, #{accountValidity}, #{expireTime},
        #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{version}, #{orgSid})
    </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy">
        insert into sys_m_password_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userSid != null">
                user_sid,
            </if>
            <if test="rule != null">
                rule,
            </if>
            <if test="minLength != null">
                min_length,
            </if>
            <if test="charactorLimit != null">
                charactor_limit,
            </if>
            <if test="ruledOut != null">
                ruled_out,
            </if>
            <if test="loginfailureEnable != null">
                loginfailure_enable,
            </if>
            <if test="loginfailureCount != null">
                loginfailure_count,
            </if>
            <if test="accountValidity != null">
                account_validity,
            </if>
            <if test="expireTime != null">
                expire_time,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="pwdExpireTime != null">
                pwd_expire_time,
            </if>
            <if test="pwdExpireTimeValidity != null">
                pwd_expire_time_validity,
            </if>
            <if test="pwdLeastUsedDay != null">
                pwd_least_used_day,
            </if>
            <if test="pwdRepeatNum != null">
                pwd_repeat_num,
            </if>
            <if test="secAuth != null">
                sec_auth,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userSid != null">
                #{userSid},
            </if>
            <if test="rule != null">
                #{rule},
            </if>
            <if test="minLength != null">
                #{minLength},
            </if>
            <if test="charactorLimit != null">
                #{charactorLimit},
            </if>
            <if test="ruledOut != null">
                #{ruledOut},
            </if>
            <if test="loginfailureEnable != null">
                #{loginfailureEnable},
            </if>
            <if test="loginfailureCount != null">
                #{loginfailureCount},
            </if>
            <if test="accountValidity != null">
                #{accountValidity},
            </if>
            <if test="expireTime != null">
                #{expireTime},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="pwdExpireTime != null">
                #{pwdExpireTime},
            </if>
            <if test="pwdExpireTimeValidity != null">
                #{pwdExpireTimeValidity},
            </if>
            <if test="pwdLeastUsedDay != null">
                #{pwdLeastUsedDay},
            </if>
            <if test="pwdRepeatNum != null">
                #{pwdRepeatNum},
            </if>
            <if test="secAuth != null">
                #{secAuth},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="java.lang.Integer">
        select count(*) from sys_m_password_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update sys_m_password_policy
        <set>
            <if test="record.id != null">
                id = #{record.id},
            </if>
            <if test="record.userSid != null">
                user_sid = #{record.userSid},
            </if>
            <if test="record.rule != null">
                rule = #{record.rule},
            </if>
            <if test="record.minLength != null">
                min_length = #{record.minLength},
            </if>
            <if test="record.charactorLimit != null">
                charactor_limit = #{record.charactorLimit},
            </if>
            <if test="record.ruledOut != null">
                ruled_out = #{record.ruledOut},
            </if>
            <if test="record.loginfailureEnable != null">
                loginfailure_enable = #{record.loginfailureEnable},
            </if>
            <if test="record.loginfailureCount != null">
                loginfailure_count = #{record.loginfailureCount},
            </if>
            <if test="record.accountValidity != null">
                account_validity = #{record.accountValidity},
            </if>
            <if test="record.expireTime != null">
                expire_time = #{record.expireTime},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.pwdExpireTimeValidity != null ">
                pwd_expire_time_validity = #{record.pwdExpireTimeValidity},
            </if>
            <if test="record.pwdExpireTime != null ">
                pwd_expire_time = #{record.pwdExpireTime},
            </if>
            <if test="record.pwdLeastUsedDay != null ">
                pwd_least_used_day = #{record.pwdLeastUsedDay},
            </if>
            <if test="record.pwdRepeatNum != null ">
                pwd_repeat_num = #{record.pwdRepeatNum},
            </if>
            <if test="record.secAuth != null ">
                sec_auth = #{record.secAuth},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update sys_m_password_policy
        set id = #{record.id},
        user_sid = #{record.userSid},
        rule = #{record.rule},
        min_length = #{record.minLength},
        charactor_limit = #{record.charactorLimit},
        ruled_out = #{record.ruledOut},
        loginfailure_enable = #{record.loginfailureEnable},
        loginfailure_count = #{record.loginfailureCount},
        account_validity = #{record.accountValidity},
        expire_time = #{record.expireTime},
        created_by = #{record.createdBy},
        created_dt = #{record.createdDt},
        updated_by = #{record.updatedBy},
        updated_dt = #{record.updatedDt},
        version = #{record.version}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy">
        update sys_m_password_policy
        <set>
            <if test="userSid != null">
                user_sid = #{userSid},
            </if>
            <if test="rule != null">
                rule = #{rule},
            </if>
            <if test="minLength != null">
                min_length = #{minLength},
            </if>
            <if test="charactorLimit != null">
                charactor_limit = #{charactorLimit},
            </if>
            <if test="ruledOut != null">
                ruled_out = #{ruledOut},
            </if>
            <if test="loginfailureEnable != null">
                loginfailure_enable = #{loginfailureEnable},
            </if>
            <if test="loginfailureCount != null">
                loginfailure_count = #{loginfailureCount},
            </if>
            <if test="accountValidity != null">
                account_validity = #{accountValidity},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="pwdExpireTimeValidity != null ">
                pwd_expire_time_validity = #{pwdExpireTimeValidity},
            </if>
            <if test="pwdExpireTime != null ">
                pwd_expire_time = #{pwdExpireTime},
            </if>
            <if test="pwdLeastUsedDay != null ">
                pwd_least_used_day = #{pwdLeastUsedDay},
            </if>
            <if test="pwdRepeatNum != null ">
                pwd_repeat_num = #{pwdRepeatNum},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy">
    update sys_m_password_policy
    set user_sid = #{userSid},
      rule = #{rule},
      min_length = #{minLength},
      charactor_limit = #{charactorLimit},
      ruled_out = #{ruledOut},
      loginfailure_enable = #{loginfailureEnable},
      loginfailure_count = #{loginfailureCount},
      account_validity = #{accountValidity},
      expire_time = #{expireTime},
      created_by = #{createdBy},
      created_dt = #{createdDt},
      updated_by = #{updatedBy},
      updated_dt = #{updatedDt},
      version = #{version}
    where id = #{id}
  </update>

    <resultMap type="cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory" id="PasswordHistoryBaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Password_History_Base_Column_List">
        id,
        user_id,
        password,
        created_by,
        created_dt,
        updated_by,
        updated_dt,
        version
    </sql>

    <sql id="Password_HistoryExample_Where_Clause">
        <where>
            <if test="condition.id != null">
                and id = #{condition.id}
            </if>
            <if test="condition.userId != null">
                and user_id = #{condition.userId}
            </if>
            <if test="condition.password != null">
                and password = #{condition.password}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.startTime != null and condition.endTime != null">
                and created_dt between #{condition.startTime} and #{condition.endTime}
            </if>
        </where>
    </sql>

    <select id="selectPasswordHistoryByParams" resultMap="PasswordHistoryBaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Password_History_Base_Column_List"/>
        from sys_m_user_password_history
        <if test="_parameter != null">
            <include refid="Password_HistoryExample_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>