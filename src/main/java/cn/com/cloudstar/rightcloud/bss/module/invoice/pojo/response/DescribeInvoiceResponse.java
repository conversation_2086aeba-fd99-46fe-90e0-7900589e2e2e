/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response;

import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2019/10/17 17:44
 */
@ToString
@Getter
@Setter
public class DescribeInvoiceResponse implements Serializable {
    /**
     * 发票ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(notes = "ID")
    private Long invoiceSid;

    /**
     * 账户id
     */
    @ApiModelProperty(notes = "账户id")
    private String accountId;

    /**
     * 发票流水号
     */
    @ApiModelProperty(notes = "发票流水号")
    private String invoiceNo;

    /**
     * 发票类型 0普通发票1增值税专用发票
     */
    @ApiModelProperty(notes = "发票类型")
    private String invoiceType;

    /**
     * 发票抬头
     */
    @ApiModelProperty(notes = "发票抬头")
    private String invoiceHead;

    /**
     * 纳税识别号
     */
    @ApiModelProperty(notes = "纳税识别号")
    private String taxId;

    /**
     * 开户行
     */
    @ApiModelProperty(notes = "开户行")
    private String depositBank;

    /**
     * 银行账号
     */
    @ApiModelProperty(notes = "银行账号")
    private String bankAccount;

    /**
     * 注册场所地址
     */
    @ApiModelProperty(notes = "注册场所地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String registerAddress;

    /**
     * 公司注册电话
     */
    @ApiModelProperty(notes = "公司注册电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String registerPhone;

    /**
     * 寄送地址
     */
    @ApiModelProperty(notes = "寄送地址")
    @DesensitizationField(type = DesensitizedType.ADDRESS)
    private String address;

    /**
     * 邮政编码
     */
    @ApiModelProperty(notes = "邮政编码")
    private String postCode;

    /**
     * 寄送状态
     */
    @ApiModelProperty(notes = "寄送状态")
    private String sendStatus;

    /**
     * 发票状态
     */
    @ApiModelProperty(notes = "发票状态")
    private String invoiceStatus;

    /**
     * 发票状态名称
     */
    @ApiModelProperty(notes = "发票状态名称")
    private String invoiceStatusName;

    /**
     * 接受人
     */
    @ApiModelProperty(notes = "收件人")
    @DesensitizationField(type = DesensitizedType.NAME)
    private String receiver;

    /**
     * 联系电话
     */
    @ApiModelProperty(notes = "联系电话")
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String phone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(notes = "联系人邮箱")
    @DesensitizationField(type = DesensitizedType.EMAIL)
    private String email;

    /**
     * 充值金额
     */
    @ApiModelProperty(notes = "充值金额")
    private BigDecimal depositeAmount;

    /**
     * 开票时间
     */
    @ApiModelProperty(notes = "开票时间")
    private Date invoiceDt;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    private Date createdDt;

    /**
     * 组织名字
     */
    @ApiModelProperty(notes = "组织名称")
    private String orgName;

    /**
     * 分销商id
     */
    @ApiModelProperty(notes = "分销商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private String distributorId;

    /**
     * 分销商名称
     */
    @ApiModelProperty(notes = "分销商Name")
    private String distributorName;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 实体名称
     */
    private String entityName;

    /**
     *
     * 开票方式:billing账单、recharge_amount充值金额
     */
    private String invoiceMethod;
}
