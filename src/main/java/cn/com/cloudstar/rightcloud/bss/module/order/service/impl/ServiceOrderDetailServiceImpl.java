/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;

import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductService;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ServiceOrderDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServicePrice;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;

/**
 * <p>
 * 申请单明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
@Service
public class ServiceOrderDetailServiceImpl extends ServiceImpl<ServiceOrderDetailMapper, ServiceOrderDetail> implements
        IServiceOrderDetailService {
    public static final List<String> createdFailure = Lists.newArrayList("create_failure", "failure");

    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final List<String> DRP_MODIFY_ORDER_TYPE = Arrays.asList(OrderType.DEGRADE, OrderType.UPGRADE, OrderType.MODIFY);

    @Override
    public void completeResourceDetail(ResourceDetailVO resourceDetailVO) {
        List<ServiceOrderDetailVO> detailVOS = new ArrayList<>();
        resourceDetailVO.setOrderDetails(Lists.newArrayList());
        Criteria criteria = new Criteria();
        boolean isSfs = ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode());
        boolean isDrp = ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode());
        boolean ebs = ProductCodeEnum.DISK.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode());
        criteria.put("refInstanceIdLike", "\"" + resourceDetailVO.getId() + "\"");
        if (!isSfs && !isDrp && !ebs) {
            criteria.put("endTime", new Date());
            criteria.put("priceType", "resource");
        }
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired", OrderType.UPGRADE, OrderType.DEGRADE));
        criteria.put("orderStatus", "completed");
        criteria.put("productCode", resourceDetailVO.getProductCode());
        //如果是缩容只考虑结束时间在当前时间之后的数据,与退订区分开
        if(OrderType.DEGRADE.equalsIgnoreCase(resourceDetailVO.getOrderType())){
            criteria.put("endTime",new Date());
        }
        List<ServiceOrderPriceDetail> list = serviceOrderPriceDetailService.selectByCriteria(criteria);
        if (isSfs) {
            if (OrderType.MODIFY.equals(resourceDetailVO.getOrderType())) {
                computeModifySfsUseAndPayment(resourceDetailVO, list);
            } else {
            computeSfsUseAndPayment(resourceDetailVO, list);
            }
            return;
        }
        if (ProductCodeEnum.DISK.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
                || ProductCodeEnum.FLOATING_IP.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
                || ProductCodeEnum.SFS_TURBO.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
                || ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
                || ProductCodeEnum.DCS.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
        ) {
            computeModifySfsUseAndPayment(resourceDetailVO, list);
            return;
        }
        if (OrderType.MODIFY.equals(resourceDetailVO.getOrderType()) && (
                ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())
                || ProductCodeEnum.CCE.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode()))) {
            //计算规格已使用和总支付
            modifyComputeSpecUseAndPayment(resourceDetailVO, list);
            return;
        }
        List<ServiceOrderPriceDetail> change = Lists.newArrayList();
        List<ServiceOrderPriceDetail> buyOrRenew = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(list)) {

            // 按量
            if (ChargeTypeEnum.PostPaid.getType().equals(resourceDetailVO.getChargeType())) {
                Optional<ServiceOrderPriceDetail> detail = list.stream().max(Comparator.comparing(ServiceOrderPriceDetail::getStartTime));
                detail.ifPresent(serviceOrderPriceDetail -> resourceDetailVO.setHourPrice(serviceOrderPriceDetail.getTradePrice()));
                return;
            }            // 创建失败
            if (createdFailure.contains(resourceDetailVO.getStatus())) {
                resourceDetailVO.setServiceAmount(this.calculateServicePrice(resourceDetailVO, "service", true));
                resourceDetailVO.setExtraConfigAmount(this.calculateServicePrice(resourceDetailVO, "extraConfig", true));
                list.stream().forEach(serviceOrderPriceDetail -> {
                    ServiceOrderDetail orderDetail = this.getById(serviceOrderPriceDetail.getOrderDetailId());
                    ServiceOrderDetailVO convert =
                            BeanConvertUtil.convert(orderDetail, ServiceOrderDetailVO.class);
                    resourceDetailVO.setTotalUsedAmount(BigDecimal.ZERO);
                    convert.setUsedAmount(BigDecimal.ZERO);
                    Integer duration = Objects.isNull(orderDetail.getDuration()) ? 0 : orderDetail.getDuration();
                    BigDecimal singlePay = serviceOrderPriceDetail.getPrice().multiply(BigDecimal.valueOf(duration))
                                                                  .divide(BigDecimal.valueOf(serviceOrderPriceDetail.getQuantity()), 3, BigDecimal.ROUND_HALF_UP);
                    resourceDetailVO.setTotalPayment(resourceDetailVO.getTotalPayment().add(singlePay));
                    convert.setPayment(singlePay);
                    convert.setPrice(serviceOrderPriceDetail.getPrice());
                    detailVOS.add(convert);
                });
                resourceDetailVO.setOrderDetails(detailVOS);
                return;
            }

            list.forEach(priceDetail -> {
                if (Arrays.asList("modify", OrderType.UPGRADE, OrderType.DEGRADE).contains(priceDetail.getOrderType())) {
                    change.add(priceDetail);
                } else {
                    buyOrRenew.add(priceDetail);
                }
            });
        }
        resourceDetailVO.setServiceAmount(calculateServicePrice(resourceDetailVO, "service", false));
        resourceDetailVO.setExtraConfigAmount(calculateServicePrice(resourceDetailVO, "extraConfig", false));

        if (isDrp) {
            // 走这里算法为已付款未使用部分金额平均数计算节点单价
            computeDrpUseAndPayment(resourceDetailVO, list);
            return;
        }
        Date now = new Date();
        // 缩容需要考虑现金券、信用额度支付方式的场景退款计算
        BigDecimal payBalanceProportion = BigDecimal.ONE;
        if (OrderType.DEGRADE.equals(resourceDetailVO.getOrderType())) {
            BigDecimal payBalance = BigDecimal.ZERO;
            BigDecimal payBalanceCash = BigDecimal.ZERO;
            BigDecimal payCreditLine = BigDecimal.ZERO;
            for (ServiceOrderPriceDetail detail:list) {
                Date startTime = detail.getStartTime();
                Date endTime = detail.getEndTime();
                long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime);
                long lestMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(now, endTime,true);
                // 未使用的完整月份占总月份比例
                BigDecimal monthProportion = BigDecimal.ZERO;
                if(offsetMonths>=0){
                    monthProportion= BigDecimal.valueOf(NumberUtil.div(lestMonths, offsetMonths));
                }
                payBalance = payBalance.add(detail.getPayBalance().multiply(monthProportion));
                payBalanceCash = payBalanceCash.add(detail.getPayBalanceCash().multiply(monthProportion));
                payCreditLine = payCreditLine.add(detail.getPayCreditLine().multiply(monthProportion));
            }
            BigDecimal totalPayment = payBalance.add(payBalanceCash).add(payCreditLine);
            // 现金支付占比
            payBalanceProportion = NumberUtil.div(payBalance, totalPayment);
        }
        resourceDetailVO.setUnsubAmount(payBalanceProportion);
        // 如果找不到变更记录，直接取续费和购买的金额
        // 否则直接拿最新的变更记录进行修改
        // 最后补充上变更之后的续费
        if (CollectionUtil.isNotEmpty(change)) {
            Optional<ServiceOrderPriceDetail> first = change.stream().max(Comparator.comparing(ServiceOrderPriceDetail::getStartTime));
            ServiceOrderDetailVO convert = new ServiceOrderDetailVO();
            ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
            if (first.isPresent()) {
                priceDetail = first.get();
                convert = BeanConvertUtil.convert(priceDetail, ServiceOrderDetailVO.class);
            }

            resourceDetailVO.setOrderDetails(Lists.newArrayList(convert));
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())){
                // modelarts按月计费，不满一月按一月计算
                Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(convert.getStartTime(), convert.getEndTime());
                resourceDetailVO.setTotalPayment(convert.getPrice()
                                                        .multiply(BigDecimal.valueOf(offsetMonths)));
            }else {
                long offDay = (convert.getEndTime().getTime() - convert.getStartTime().getTime()) / (1000 * 60 * 60 * 24);
                resourceDetailVO.setTotalPayment(convert.getPrice()
                                                        .multiply(BigDecimal.valueOf(offDay))
                                                        .divide(BigDecimal.valueOf(30), 10, BigDecimal.ROUND_HALF_UP));
            }
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())){
                // ******目前的理解是不计算已使用的完整的月数******
                Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(convert.getStartTime(), now);
                resourceDetailVO.setTotalUsedAmount(convert.getPrice()
                                                           .multiply(BigDecimal.valueOf(offsetMonths)));
            }else {
                Integer integer = DateUtil.calculateOffDay(convert.getStartTime(), now);
                resourceDetailVO.setTotalUsedAmount(convert.getPrice()
                                                           .multiply(BigDecimal.valueOf(integer))
                                                           .divide(BigDecimal.valueOf(30), 10, BigDecimal.ROUND_HALF_UP));
            }
            resourceDetailVO.setProjectId(priceDetail.getOrgSid());
            if (CollectionUtil.isNotEmpty(buyOrRenew)) {
                ServiceOrderDetailVO finalConvert = convert;
                buyOrRenew.stream().filter(o -> o.getStartTime().after(finalConvert.getStartTime()))
                          .forEach(o -> resourceDetailVO.setTotalPayment(
                                  resourceDetailVO.getTotalPayment().add(o.getAmount())));
            }

            return;
        }

        if (CollectionUtil.isNotEmpty(buyOrRenew)) {
            buyOrRenew.stream().forEach(serviceOrderPriceDetail -> {
                ServiceOrderDetail orderDetail = this.getById(serviceOrderPriceDetail.getOrderDetailId());
                ServiceOrderDetailVO convert =
                        BeanConvertUtil.convert(orderDetail, ServiceOrderDetailVO.class);

                BigDecimal singlePay;
                if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())){
                    // modelarts按月计费，不满一月按一月计算
                    Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(convert.getStartTime(), convert.getEndTime());
                    if (OrderType.APPLY.equals(serviceOrderPriceDetail.getOrderType()) && offsetMonths>0) {
                        convert.setPrice(NumberUtil.div(orderDetail.getAmount(), offsetMonths));
                    }
                    singlePay = convert.getPrice().multiply(BigDecimal.valueOf(offsetMonths));
                }else {
                Integer duration = Objects.isNull(orderDetail.getDuration()) ? 0 : orderDetail.getDuration();
                    singlePay = serviceOrderPriceDetail.getPrice().multiply(BigDecimal.valueOf(duration))
                        .divide(BigDecimal.valueOf(serviceOrderPriceDetail.getQuantity()), 3, BigDecimal.ROUND_HALF_UP);
                }

                BigDecimal singleUsed;
                if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equalsIgnoreCase(resourceDetailVO.getProductCode())){
                    // ******目前的理解是不计算已使用的完整的月数******
                    Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(convert.getStartTime(), now);
                    // offsetMonths = OrderType.DEGRADE.equals(resourceDetailVO.getOrderType()) ? offsetMonths + 1 : offsetMonths;
                    singleUsed = convert.getPrice().multiply(BigDecimal.valueOf(offsetMonths));
                }else {
                    Integer offsetDays = DateUtil.calculateOffDay(convert.getStartTime(), now);
                    singleUsed = serviceOrderPriceDetail.getPrice().multiply(BigDecimal.valueOf(offsetDays)).divide(BigDecimal.valueOf(30), 3, BigDecimal.ROUND_HALF_UP);
                }

                resourceDetailVO.setTotalUsedAmount(resourceDetailVO.getTotalUsedAmount().add(singleUsed));
                convert.setUsedAmount(singleUsed);
                resourceDetailVO.setTotalPayment(resourceDetailVO.getTotalPayment().add(singlePay));
                convert.setPayment(singlePay);
                convert.setPrice(serviceOrderPriceDetail.getPrice());
                resourceDetailVO.setProjectId(serviceOrderPriceDetail.getOrgSid());
                detailVOS.add(convert);
            });
        }
        resourceDetailVO.setOrderDetails(detailVOS);
    }

    private void computeDrpUseAndPayment(ResourceDetailVO resourceDetailVO, List<ServiceOrderPriceDetail> list) {
        Date now = new Date();
        // 缩容需要考虑现金券、信用额度支付方式的场景退款计算
        BigDecimal payBalanceProportion = BigDecimal.ONE;
        if (OrderType.DEGRADE.equals(resourceDetailVO.getOrderType())) {
            BigDecimal payBalance = BigDecimal.ZERO;
            BigDecimal payBalanceCash = BigDecimal.ZERO;
            BigDecimal payCreditLine = BigDecimal.ZERO;
            for (ServiceOrderPriceDetail detail:list) {
                if (OrderType.DEGRADE.equals(detail.getOrderType()) || OrderType.EXPIRED.equals(detail.getOrderType())) {
                    continue;
                }
                Date startTime = detail.getStartTime();
                Date endTime = detail.getEndTime();
                long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(startTime, endTime);
                // offsetMonths = OrderType.UPGRADE.equals(detail.getOrderType()) ? offsetMonths + 1 : offsetMonths;
                long lestMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(now, endTime, true);
                // 未使用的完整月份占总月份比例
                BigDecimal monthProportion = BigDecimal.valueOf(NumberUtil.div(lestMonths, offsetMonths == 0 ? 1 :offsetMonths));
                monthProportion = monthProportion.compareTo(BigDecimal.ONE) > 0 ? BigDecimal.ONE : monthProportion;
                payBalance = payBalance.add(detail.getPayBalance().multiply(monthProportion));
                payBalanceCash = payBalanceCash.add(detail.getPayBalanceCash().multiply(monthProportion));
                payCreditLine = payCreditLine.add(detail.getPayCreditLine().multiply(monthProportion));
            }
            BigDecimal totalPayment = payBalance.add(payBalanceCash).add(payCreditLine);
            // 现金支付占比
            payBalanceProportion = NumberUtil.equals(BigDecimal.ZERO, totalPayment) ? BigDecimal.ZERO : NumberUtil.div(payBalance, totalPayment);
        }
        resourceDetailVO.setUnsubAmount(payBalanceProportion);

        BigDecimal payment = BigDecimal.ZERO;
        BigDecimal used = BigDecimal.ZERO;

        // 根据每个订单支付金额，与已使用月数、总月数比值，计算已使用金额
        for (ServiceOrderPriceDetail detail:list) {

            BigDecimal singlePay = detail.getPayBalance().add(detail.getPayBalanceCash().add(detail.getPayCreditLine()));

            BigDecimal singleUsed;
            if (OrderType.DEGRADE.equals(detail.getOrderType())) {
                singleUsed = BigDecimal.ZERO;
            }else {

                Long offsetMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(detail.getStartTime(),detail.getEndTime());

                Long usedMonths = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(detail.getStartTime(), now);

                if (now.before(detail.getStartTime())) {
                    usedMonths = 0L;
                }

                BigDecimal usedProportion= BigDecimal.ONE;
                if (offsetMonths > 0) {
                    usedProportion = BigDecimal.ONE.compareTo(NumberUtil.div(usedMonths, offsetMonths)) < 0
                            ? BigDecimal.ONE : NumberUtil.div(usedMonths, offsetMonths);
                }
                singleUsed = singlePay.multiply(usedProportion);
            }
            payment = payment.add(singlePay);
            used = used.add(singleUsed);
        }
        resourceDetailVO.setTotalPayment(payment.setScale(2,RoundingMode.HALF_DOWN));
        resourceDetailVO.setTotalUsedAmount(used.setScale(2,RoundingMode.HALF_DOWN));
    }

    private void computeSfsUseAndPayment(ResourceDetailVO resourceDetailVO,
        List<ServiceOrderPriceDetail> list) {
        list.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
        Date now = new Date();
        //资源开始时间
        Date applyTime = list.stream().map(ServiceOrderPriceDetail::getStartTime)
                             .min(Date::compareTo).orElse(now);
        //资源结束时间
        Date maxEndTime = list.stream().map(ServiceOrderPriceDetail::getEndTime)
                              .max(Date::compareTo).orElse(now);
        Date applyTimeTemp = applyTime;
        int buyMonth = 0;
        while (applyTimeTemp.before(maxEndTime)) {
            applyTimeTemp = cn.hutool.core.date.DateUtil.offsetMonth(applyTimeTemp, 1);
            buyMonth++;
        }
        resourceDetailVO.setBuyMonth(buyMonth);
        boolean isRelease = OrderType.RELEASE.equals(resourceDetailVO.getOrderType());
        //退订按月计算,不足一个月补足一个月  ex: 3.15 -4.15 如果3.17号退订(只要在4.15之前退订都算4.15) 时间结束时间是4.15
        // 如果4.16退订 超过部分按天数计算
        if (isRelease) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(maxEndTime);
            int day = instance.get(Calendar.DAY_OF_MONTH);
            instance.setTime(applyTime);
            boolean notSameDay = day != instance.get(Calendar.DAY_OF_MONTH);
            if (notSameDay) {
                instance.set(Calendar.DAY_OF_MONTH, day);
            }
            //实际已使用时间
            Date usedTime = instance.getTime();
            // BUG48510
            if (usedTime.compareTo(applyTime) < 0) {
                usedTime = applyTime;
            }
            Date temp = usedTime;
            int month=0;
            while (temp.before(now)) {
                month++;
                temp = cn.hutool.core.date.DateUtil.offsetMonth(usedTime, month);
            }
            usedTime= DateUtils.addMonths(usedTime,month);
            //算出来的实际结束使用的时间
            now = usedTime.after(maxEndTime) ? maxEndTime : usedTime;
        }
        // 变更
        if (OrderType.MODIFY.equals(resourceDetailVO.getOrderType())) {
            Date usedTime = applyTime;
            while (usedTime.before(now)) {
                usedTime = cn.hutool.core.date.DateUtil.offsetDay(usedTime, 1);
            }
            now = cn.hutool.core.date.DateUtil.offsetDay(usedTime, -1);
        }
        resourceDetailVO.setComputeDate(now);
        // 资源取值
        BigDecimal resourceTotalPayment = resourceDetailVO.getTotalPayment();
        BigDecimal resourceOriginalCost = Convert.toBigDecimal(resourceDetailVO.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal resourceOrgDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal resourceTotalUsed = resourceDetailVO.getTotalUsedAmount();
        BigDecimal resourceUsedCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 服务
        ServicePrice service = new ServicePrice();
        BigDecimal serviceTotalPayment = service.getTotalPayment();
        BigDecimal serviceOriginalCost = Convert.toBigDecimal(service.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal serviceOrgDiscount = Convert.toBigDecimal(service.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponDiscount = Convert.toBigDecimal(service.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponAmount = Convert.toBigDecimal(service.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceCashAmount = Convert.toBigDecimal(service.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceCreditAmount = Convert.toBigDecimal(service.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal serviceTotalUsed = service.getTotalUsed();
        BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(service.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(service.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(service.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 额外计费（未使用）
        ServicePrice extraConfig = new ServicePrice();
        BigDecimal extraConfigTotalPayment = extraConfig.getTotalPayment();
        BigDecimal extraConfigOriginalCost = Convert.toBigDecimal(extraConfig.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal extraConfigOrgDiscount = Convert.toBigDecimal(extraConfig.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigCouponDiscount = Convert.toBigDecimal(extraConfig.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigTotalUsed = extraConfig.getTotalUsed();
        Date lastStartTime = null;
        List<String> priceTypes = Lists.newArrayList();
        //时间跨了几个月 多一天也算一个月
        Integer actualMonth = 0;

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal globalPayBalance,globalPayBalanceCash,globalPayCreditLine,serviceTradePriceTag;
        globalPayBalance = globalPayBalanceCash = globalPayCreditLine = serviceTradePriceTag =BigDecimal.ZERO;
        boolean tag = false;
        for (ServiceOrderPriceDetail d : list) {
            boolean innerTag = true;
            BigDecimal amount = d.getAmount();
            Date startTime = d.getStartTime();
            Date endTime = d.getEndTime();
            BigDecimal used = BigDecimal.ZERO;
            BigDecimal payBalance = d.getPayBalance();
            if (OrderType.MODIFY.equalsIgnoreCase(d.getType())) {
                if (BigDecimal.ZERO.compareTo(payBalance) > 0 && payBalance.compareTo(d.getAmount()) > 0) {
                    // Bug57023 弹性文件扩缩容问题
                    payBalance = d.getAmount();
                }
            }
            BigDecimal payBalanceCash = d.getPayBalanceCash();
            BigDecimal payCreditLine = d.getPayCreditLine();

            if (endTime.after(startTime)) {
                Date cursorDate = startTime;
                String type = d.getType();
                Integer offsetMonths = 0;
                do {
                    Integer currentOffsetMonths = ++offsetMonths;
                    if ("modify".equalsIgnoreCase(type)) {
                        Calendar instance = Calendar.getInstance();
                        instance.setTime(endTime);
                        int day = instance.get(Calendar.DAY_OF_MONTH);
                        instance.setTime(startTime);
                        boolean notSameDay = day != instance.get(Calendar.DAY_OF_MONTH);
                        if (notSameDay) {
                            instance.set(Calendar.DAY_OF_MONTH, day);
                        }
                        Integer offsetDays = DateUtil.calculateOffDay(cursorDate, instance.getTime());
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(instance.getTime());
                        instance.setTime(endTime);
                        int month = instance.get(Calendar.MONTH);
                        instance.setTime(startTime);
                        boolean notSameMonth = month != instance.get(Calendar.MONTH);
                        if (notSameMonth) {
                            calendar.setTime(cn.hutool.core.date.DateUtil.offsetMonth(instance.getTime(), -1));
                        }
                        BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(),
                                                                 BigDecimal.valueOf(calendar.getActualMaximum(
                                                                         Calendar.DAY_OF_MONTH)))
                                                            .multiply(BigDecimal.valueOf(offsetDays))
                                                            .setScale(5, RoundingMode.HALF_UP);
                        used = used.add(durationUsed);
                        cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
                    }
                    // 尝试按一个月计算
                    // 当前周期结束时间
                    cursorDate = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths);
                    // 当前周期开始时间
                    DateTime st = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths - 1);
                    // 当前周期总天数
                    Integer currentCycleDays = DateUtil.calculateOffDay(st, cursorDate);
                    if (OrderType.EXPIRED.equals(d.getType())) {
                        // 过期时间段用续订的时间周期计算
                        DateTime expEt = cn.hutool.core.date.DateUtil.offsetMonth(startTime, 0);
                        DateTime expSt = cn.hutool.core.date.DateUtil.offsetMonth(startTime, -1);
                        currentCycleDays = DateUtil.calculateOffDay(expSt, expEt);
                        if (isRelease) {
                            now = endTime;
                        }
                    }
                    // 判断时间
                    actualMonth++;
                    //不足一月 按天算 多使用了
                    if (cursorDate.after(endTime)) {
                        // 按天算
                        Integer offsetDays =0;
                        if (now.after(endTime)){
                            offsetDays = DateUtil.calculateOffDay(st, endTime);
                        }else {
                            offsetDays = DateUtil.calculateOffDay(st, now);
                        }

                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(st);
                        if ("renew".equalsIgnoreCase(type) && Objects.nonNull(lastStartTime)) {
                            calendar.setTime(lastStartTime);
                            priceTypes.add(d.getPriceType());
                        }
                        // 月价/月天数*使用天数
                        BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(), currentCycleDays)
                                                            .multiply(BigDecimal.valueOf(offsetDays))
                                                            .setScale(5, RoundingMode.HALF_UP);
                        used = used.add(durationUsed);
                        cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
                    } else {
                        //不足一月 按天算 少使用了
                        if (now.before(cursorDate)) {
                            // 按天算
                            Integer offsetDays = DateUtil.calculateOffDay(st, now);
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(st);
                            /// 月价/月天数*使用天数
                            BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(), currentCycleDays)
                                                                .multiply(BigDecimal.valueOf(offsetDays))
                                                                .setScale(5, RoundingMode.HALF_UP);
                            used = used.add(durationUsed);
                            cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
                        } else {
                            // 一个月
                            //正好一个月  按一个月计算
                            BigDecimal durationUsed = d.getTradePrice().multiply(BigDecimal.ONE);
                            used = used.add(durationUsed);
                        }
                    }
                } while (cursorDate.before(now));
                if (priceTypes.isEmpty()) {
                    lastStartTime = startTime;
                } else if (priceTypes.contains(d.getPriceType())) {
                    priceTypes.clear();
                    lastStartTime = startTime;
                }
            }else {
                //目前针对变更配置后，原订购订单数据不参与计算，导致遗漏
                totalAmount = NumberUtil.add(totalAmount,amount);

                BigDecimal balance = d.getPayBalance();
                if (BigDecimal.ZERO.compareTo(balance) > 0 && balance.compareTo(d.getAmount()) > 0) {
                    // Bug57023 弹性文件扩缩容问题
                    balance = d.getAmount();
                }
                globalPayBalance = globalPayBalance.add(balance);
                globalPayCreditLine = globalPayCreditLine.add(d.getPayCreditLine());
                globalPayBalanceCash = globalPayBalanceCash.add(d.getPayBalanceCash());
                payBalance = payBalanceCash = payCreditLine = BigDecimal.ZERO;
                if ("service".equalsIgnoreCase(d.getPriceType())){
                    serviceTradePriceTag = d.getTradePrice();
                }

                // 退订时，把变更过的订单优惠券加到总价
                d.setOriginalCost(Objects.nonNull(d.getCouponAmount()) ? d.getCouponAmount() : BigDecimal.ZERO);
                d.setDiscount(BigDecimal.ZERO);
                tag = true;
                innerTag = false;
            }

            boolean resourceUsedTag = false;
            if (tag && innerTag){
                //变更场景
                if (NumberUtil.isGreater(used, amount.subtract(d.getGiveBack()).add(totalAmount))) {
                    used = amount.subtract(d.getGiveBack()).add(totalAmount);
                }
                if ("release".equalsIgnoreCase(resourceDetailVO.getOrderType())){
                    used = used.add(serviceTradePriceTag);
                    //这里把服务费用加到了资源计费里面做计算，下面赋值资源使用费用的时候应该减去。
                    resourceUsedTag = true;
                }
                payBalance = payBalance.add(globalPayBalance);
                payBalanceCash = payBalanceCash.add(globalPayBalanceCash);
                payCreditLine = payCreditLine.add(globalPayCreditLine);
                globalPayBalance = globalPayBalanceCash = globalPayCreditLine = BigDecimal.ZERO;
                tag = false;
            }else {
                //原逻辑
                if (NumberUtil.isGreater(used, amount.subtract(d.getGiveBack()))) {
                    used = amount.subtract(d.getGiveBack());
                }
            }

            if (Objects.isNull(payBalance)){
                payBalance = BigDecimal.ZERO;
            }
            if (Objects.isNull(payBalanceCash)){
                payBalanceCash = BigDecimal.ZERO;
            }
            if (Objects.isNull(payCreditLine)){
                payCreditLine = BigDecimal.ZERO;
            }

            BigDecimal total = payBalance.add(payBalanceCash).add(payCreditLine);
            if ("resource".equalsIgnoreCase(d.getPriceType())) {
                resourceTotalPayment = resourceTotalPayment.add(amount);
                resourceOriginalCost = resourceOriginalCost.add(d.getOriginalCost());
                resourceOrgDiscount = resourceOrgDiscount.add(d.getDiscount());
                resourceCouponDiscount = resourceCouponDiscount.add(d.getCouponAmount());
                resourceCouponAmount = resourceCouponAmount.add(payBalanceCash);
                resourceCashAmount = resourceCashAmount.add(payBalance);
                resourceCreditAmount = resourceCreditAmount.add(payCreditLine);
                // 按比例
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    resourceUsedCouponAmount = resourceUsedCouponAmount.add(
                            NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    resourceUsedCashAmount = resourceUsedCashAmount.add(
                            NumberUtil.div(used.multiply(payBalance), total, 5));
                    resourceUsedCreditAmount = resourceUsedCreditAmount.add(
                            NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
                if (resourceUsedTag){
                    used = used.subtract(serviceTradePriceTag);
                    serviceTradePriceTag = BigDecimal.ZERO;
                }
                resourceTotalUsed = resourceTotalUsed.add(used);
            } else if ("service".equalsIgnoreCase(d.getPriceType())) {
                serviceTotalPayment = serviceTotalPayment.add(amount);
                serviceOriginalCost = serviceOriginalCost.add(d.getOriginalCost());
                serviceOrgDiscount = serviceOrgDiscount.add(d.getDiscount());
                serviceCouponDiscount = serviceCouponDiscount.add(d.getCouponAmount());
                serviceCouponAmount = serviceCouponAmount.add(payBalanceCash);
                serviceCashAmount = serviceCashAmount.add(payBalance);
                serviceCreditAmount = serviceCreditAmount.add(payCreditLine);
                serviceTotalUsed = serviceTotalUsed.add(used);
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    serviceUsedCouponAmount = serviceUsedCouponAmount.add(
                            NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    serviceUsedCashAmount = serviceUsedCashAmount.add(
                            NumberUtil.div(used.multiply(payBalance), total, 5));
                    serviceUsedCreditAmount = serviceUsedCreditAmount.add(
                            NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
            } else {
                extraConfigTotalPayment = extraConfigTotalPayment.add(amount);
                extraConfigOriginalCost = extraConfigOriginalCost.add(d.getOriginalCost());
                extraConfigOrgDiscount = extraConfigOrgDiscount.add(d.getDiscount());
                extraConfigCouponDiscount = extraConfigCouponDiscount.add(d.getCouponAmount());
                extraConfigTotalUsed = extraConfigTotalUsed.add(used);
            }
        }
        if (isRelease && now.equals(maxEndTime)) {
            serviceTotalUsed = serviceTotalPayment;
            resourceTotalUsed = resourceTotalPayment;
            extraConfigTotalUsed = extraConfigTotalPayment;
        }
        resourceDetailVO.setActualMonth(actualMonth);
        // 设值
        resourceDetailVO.setTotalPayment(resourceTotalPayment);
        resourceDetailVO.setTotalOriginalCost(resourceOriginalCost);
        resourceDetailVO.setTotalOrgDiscount(resourceOrgDiscount);
        resourceDetailVO.setTotalCouponDiscount(resourceCouponDiscount);
        resourceDetailVO.setTotalCouponAmount(resourceCouponAmount);
        resourceDetailVO.setTotalCashAmount(resourceCashAmount);
        resourceDetailVO.setTotalCreditAmount(resourceCreditAmount);
        resourceDetailVO.setTotalUsedAmount(resourceTotalUsed);
        resourceDetailVO.setTotalUsedCouponAmount(resourceUsedCouponAmount);
        resourceDetailVO.setTotalUsedCashAmount(resourceUsedCashAmount);
        resourceDetailVO.setTotalUsedCreditAmount(resourceUsedCreditAmount);
        resourceDetailVO.setUnsubAmount(resourceTotalPayment.subtract(resourceTotalUsed));
        resourceDetailVO.setUnsubCouponAmount(resourceCouponAmount.subtract(resourceUsedCouponAmount));
        resourceDetailVO.setUnsubCashAmount(resourceCashAmount.subtract(resourceUsedCashAmount));
        resourceDetailVO.setUnsubCreditAmount(resourceCreditAmount.subtract(resourceUsedCreditAmount));
        service.setTotalPayment(serviceTotalPayment);
        service.setTotalOriginalCost(serviceOriginalCost);
        service.setTotalOrgDiscount(serviceOrgDiscount);
        service.setTotalCouponDiscount(serviceCouponDiscount);
        service.setTotalCouponAmount(serviceCouponAmount);
        service.setTotalCashAmount(serviceCashAmount);
        service.setTotalCreditAmount(serviceCreditAmount);
        service.setTotalUsed(serviceTotalUsed);
        service.setTotalUsedCouponAmount(serviceUsedCouponAmount);
        service.setTotalUsedCashAmount(serviceUsedCashAmount);
        service.setTotalUsedCreditAmount(serviceUsedCreditAmount);
        service.setUnsubAmount(serviceTotalPayment.subtract(serviceTotalUsed));
        service.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
        service.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
        service.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
        resourceDetailVO.setServiceAmount(service);
        extraConfig.setTotalPayment(extraConfigTotalPayment);
        extraConfig.setTotalUsed(extraConfigTotalUsed);
        resourceDetailVO.setExtraConfigAmount(extraConfig);
    }


    /**
     * 规格已使用和总支付，不包含折扣，计算了优惠券、现金、信用额度、抵扣现金券；补扣需要计算出未使用（包含优惠券、现金、信用额度、抵扣现金券），退款时，未使用中只退现金。
     *
     * @param resourceDetailVO
     * @param list
     */
    private void modifyComputeSpecUseAndPayment(ResourceDetailVO resourceDetailVO, List<ServiceOrderPriceDetail> list) {
        list.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
        Date now = new Date();
        Date current = now;
        //资源开始时间
        Date applyTime = list.stream().map(ServiceOrderPriceDetail::getStartTime).min(Date::compareTo).orElse(now);
        //资源结束时间
        Date maxEndTime = list.stream().map(ServiceOrderPriceDetail::getEndTime).max(Date::compareTo).orElse(now);
        Date applyTimeTemp = applyTime;
        int buyMonth = 0;
        while (applyTimeTemp.before(maxEndTime)) {
            applyTimeTemp = cn.hutool.core.date.DateUtil.offsetMonth(applyTimeTemp, 1);
            buyMonth++;
        }
        resourceDetailVO.setBuyMonth(buyMonth);

        Date usedTime = applyTime;
        while (usedTime.before(now)) {
            usedTime = cn.hutool.core.date.DateUtil.offsetDay(usedTime, 1);
        }
        now = cn.hutool.core.date.DateUtil.offsetDay(usedTime, -1);
        resourceDetailVO.setComputeDate(now);
        resourceDetailVO.setCurrentDate(current);
        // 资源取值
        BigDecimal resourceTotalPayment = resourceDetailVO.getTotalPayment();
        BigDecimal resourceOriginalCost = Convert.toBigDecimal(resourceDetailVO.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal resourceOrgDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal resourceTotalUsed = resourceDetailVO.getTotalUsedAmount();
        BigDecimal resourceUsedCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 服务
        ServicePrice service = new ServicePrice();
        BigDecimal serviceTotalPayment = service.getTotalPayment();
        BigDecimal serviceOriginalCost = Convert.toBigDecimal(service.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal serviceOrgDiscount = Convert.toBigDecimal(service.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponDiscount = Convert.toBigDecimal(service.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponAmount = Convert.toBigDecimal(service.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceCashAmount = Convert.toBigDecimal(service.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceCreditAmount = Convert.toBigDecimal(service.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal serviceTotalUsed = service.getTotalUsed();
        BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(service.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(service.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(service.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 额外计费（未使用）
        ServicePrice extraConfig = new ServicePrice();
        BigDecimal extraConfigTotalPayment = extraConfig.getTotalPayment();
        BigDecimal extraConfigOriginalCost = Convert.toBigDecimal(extraConfig.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal extraConfigOrgDiscount = Convert.toBigDecimal(extraConfig.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigCouponDiscount = Convert.toBigDecimal(extraConfig.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigTotalUsed = extraConfig.getTotalUsed();
        List<String> priceTypes = Lists.newArrayList();
        //时间跨了几个月 多一天也算一个月
        Integer actualMonth = 0;

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal globalPayBalance, globalPayBalanceCash, globalPayCreditLine;
        globalPayBalance = globalPayBalanceCash = globalPayCreditLine = BigDecimal.ZERO;
        for (ServiceOrderPriceDetail d : list) {
            //总支付价,外层会累加总优惠券金额，这里不累加优惠券
            BigDecimal amount = d.getPayBalance().add(d.getPayBalanceCash()).add(d.getPayCreditLine());
            Date startTime = d.getStartTime();
            Date endTime = d.getEndTime();

            BigDecimal used = BigDecimal.ZERO;
            BigDecimal unUsed = BigDecimal.ZERO;
            BigDecimal payBalance = d.getPayBalance();
            BigDecimal payBalanceCash = d.getPayBalanceCash();
            BigDecimal payCreditLine = d.getPayCreditLine();
            BigDecimal couponAmount = d.getCouponAmount();

            //由于批量创建引出的问题，这里需要重新计算金额，因为批量创建时，pricedetail不能详细计算单个资源的实际金额，需要根据资源对应的账单计算
            if (OrderType.APPLY.equals(d.getType()) && ProductCodeEnum.ECS.getProductType().equals(d.getProductCode())) {
                SfProductResource resource = sfProductResourceService.getById(resourceDetailVO.getId());
                Query query = new Query();
                org.springframework.data.mongodb.core.query.Criteria c = new org.springframework.data.mongodb.core.query.Criteria();
                c.and("orderSn").is(d.getOrderSn());
                c.and("instanceId").is(resource.getId().toString());
                query.addCriteria(c);
                List<InstanceGaapCost> costs = mongoTemplate.find(query, InstanceGaapCost.class, "biz_bill_usage_item");
                InstanceGaapCost applyCost = costs.get(0);
                amount = applyCost.getCashAmount().add(applyCost.getCreditAmount()).add(applyCost.getCouponAmount());
                payBalance = applyCost.getCashAmount();
                payBalanceCash = applyCost.getCouponAmount();
                payCreditLine = applyCost.getCreditAmount();
                couponAmount = scaleThree(applyCost.getCouponDiscount());

            }

            //订单已经过周期
            if (endTime.after(startTime)) {
                if (!current.before(endTime)) {
                    used = used.add(BigDecimalUtil.remainTwoPointAmount(amount));
                } else if (!current.before(startTime)) {
                    //当前周期的月份
                    Integer offsetMonths = 0;
                    int tempMonth = 0;
                    Date tempdate = endTime;
                    while (tempdate.after(current)) {
                        tempMonth++;
                        tempdate = DateUtils.addMonths(endTime, -tempMonth);
                        if (!tempdate.before(current)) {
                            offsetMonths++;
                        }
                    }
                    Date cursorDate = DateUtils.addMonths(endTime, -offsetMonths);
                    //未使用天数
                    Integer noUseOffsetDays;
                    //如果扩容未使用使用天数就向上取整，如果缩容未使用天数向下取整
                    if (offsetMonths > 0) {
                        noUseOffsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(current, cursorDate,
                                                                                                               resourceDetailVO.getScaleUp());
                    } else {
                        noUseOffsetDays =
                                cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(current, endTime, resourceDetailVO.getScaleUp());
                    }
                    // 变配单月差价
                    BigDecimal div = NumberUtil.div(amount, 1 + offsetMonths);
                    // 当前周期开始时间
                    DateTime ed = cn.hutool.core.date.DateUtil.offsetMonth(startTime, 1);
                    // 当前周期总天数
                    Integer currentCycleDays = DateUtil.calculateOffDay(startTime, ed);
                    BigDecimal durationUnUsed = NumberUtil.div(div, currentCycleDays)
                                                          .multiply(BigDecimal.valueOf(noUseOffsetDays))
                                                          .setScale(5, RoundingMode.HALF_UP);
                    if (offsetMonths > 0) {
                        durationUnUsed = durationUnUsed.add(NumberUtil.mul(div, offsetMonths));
                    }
                    unUsed = unUsed.add(durationUnUsed);
                    used = used.add(NumberUtil.sub(BigDecimalUtil.remainTwoPointAmount(amount), unUsed));
                }
                if (priceTypes.isEmpty()) {
                } else if (priceTypes.contains(d.getPriceType())) {
                    priceTypes.clear();
                }
            } else {
                used = used.add(BigDecimalUtil.remainTwoPointAmount(amount));
                //目前针对变更配置后，原订购订单数据不参与计算，导致遗漏
                totalAmount = NumberUtil.add(totalAmount, amount);
                globalPayBalance = globalPayBalance.add(payBalance);
                globalPayCreditLine = globalPayCreditLine.add(payCreditLine);
                globalPayBalanceCash = globalPayBalanceCash.add(payBalanceCash);
                payBalance = payBalanceCash = payCreditLine = BigDecimal.ZERO;
                // 退订时，把变更过的订单优惠券加到总价
                d.setDiscount(BigDecimal.ZERO);
            }
            //原逻辑
            if (NumberUtil.isGreater(used, amount.subtract(d.getGiveBack()))) {
                used = amount.subtract(d.getGiveBack());
            }

            if (Objects.isNull(payBalance)) {
                payBalance = BigDecimal.ZERO;
            }
            if (Objects.isNull(payBalanceCash)) {
                payBalanceCash = BigDecimal.ZERO;
            }
            if (Objects.isNull(payCreditLine)) {
                payCreditLine = BigDecimal.ZERO;
            }

            BigDecimal total = payBalance.add(payBalanceCash).add(payCreditLine);
            if ("resource".equalsIgnoreCase(d.getPriceType())) {
                resourceTotalPayment = resourceTotalPayment.add(BigDecimalUtil.getTwoPointAmount(amount));
                resourceOriginalCost = resourceOriginalCost.add(d.getOriginalCost());
                resourceOrgDiscount = resourceOrgDiscount.add(Optional.ofNullable(d.getDiscount()).orElse(BigDecimal.ZERO));
                resourceCouponDiscount = resourceCouponDiscount.add(couponAmount);
                resourceCouponAmount = resourceCouponAmount.add(payBalanceCash);
                resourceCashAmount = resourceCashAmount.add(payBalance);
                resourceCreditAmount = resourceCreditAmount.add(payCreditLine);
                // 按比例
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    resourceUsedCouponAmount = resourceUsedCouponAmount.add(
                            NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    resourceUsedCashAmount = resourceUsedCashAmount.add(
                            NumberUtil.div(used.multiply(payBalance), total, 5));
                    resourceUsedCreditAmount = resourceUsedCreditAmount.add(
                            NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
                resourceTotalUsed = resourceTotalUsed.add(used);
            } else if ("service".equalsIgnoreCase(d.getPriceType())) {
                serviceTotalPayment = serviceTotalPayment.add(amount);
                serviceOriginalCost = serviceOriginalCost.add(d.getOriginalCost());
                serviceOrgDiscount = serviceOrgDiscount.add(d.getDiscount());
                serviceCouponDiscount = serviceCouponDiscount.add(d.getCouponAmount());
                serviceCouponAmount = serviceCouponAmount.add(payBalanceCash);
                serviceCashAmount = serviceCashAmount.add(payBalance);
                serviceCreditAmount = serviceCreditAmount.add(payCreditLine);
                serviceTotalUsed = serviceTotalUsed.add(used);
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    serviceUsedCouponAmount = serviceUsedCouponAmount.add(
                            NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    serviceUsedCashAmount = serviceUsedCashAmount.add(
                            NumberUtil.div(used.multiply(payBalance), total, 5));
                    serviceUsedCreditAmount = serviceUsedCreditAmount.add(
                            NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
            } else {
                extraConfigTotalPayment = extraConfigTotalPayment.add(amount);
                extraConfigOriginalCost = extraConfigOriginalCost.add(d.getOriginalCost());
                extraConfigOrgDiscount = extraConfigOrgDiscount.add(d.getDiscount());
                extraConfigCouponDiscount = extraConfigCouponDiscount.add(couponAmount);
                extraConfigTotalUsed = extraConfigTotalUsed.add(used);
            }
        }

        resourceDetailVO.setActualMonth(actualMonth);
        // 设值
        resourceDetailVO.setTotalPayment(resourceTotalPayment);
        resourceDetailVO.setTotalOriginalCost(resourceOriginalCost);
        resourceDetailVO.setTotalOrgDiscount(resourceOrgDiscount);
        resourceDetailVO.setTotalCouponDiscount(resourceCouponDiscount);
        resourceDetailVO.setTotalCouponAmount(resourceCouponAmount);
        resourceDetailVO.setTotalCashAmount(resourceCashAmount);
        resourceDetailVO.setTotalCreditAmount(resourceCreditAmount);
        resourceDetailVO.setTotalUsedAmount(resourceTotalUsed);
        resourceDetailVO.setTotalUsedCouponAmount(resourceUsedCouponAmount);
        resourceDetailVO.setTotalUsedCashAmount(resourceUsedCashAmount);
        resourceDetailVO.setTotalUsedCreditAmount(resourceUsedCreditAmount);
        resourceDetailVO.setUnsubAmount(resourceTotalPayment.subtract(resourceTotalUsed));
        resourceDetailVO.setUnsubCouponAmount(resourceCouponAmount.subtract(resourceUsedCouponAmount));
        resourceDetailVO.setUnsubCashAmount(resourceCashAmount.subtract(resourceUsedCashAmount));
        resourceDetailVO.setUnsubCreditAmount(resourceCreditAmount.subtract(resourceUsedCreditAmount));
        service.setTotalPayment(serviceTotalPayment);
        service.setTotalOriginalCost(serviceOriginalCost);
        service.setTotalOrgDiscount(serviceOrgDiscount);
        service.setTotalCouponDiscount(serviceCouponDiscount);
        service.setTotalCouponAmount(serviceCouponAmount);
        service.setTotalCashAmount(serviceCashAmount);
        service.setTotalCreditAmount(serviceCreditAmount);
        service.setTotalUsed(serviceTotalUsed);
        service.setTotalUsedCouponAmount(serviceUsedCouponAmount);
        service.setTotalUsedCashAmount(serviceUsedCashAmount);
        service.setTotalUsedCreditAmount(serviceUsedCreditAmount);
        service.setUnsubAmount(serviceTotalPayment.subtract(serviceTotalUsed));
        service.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
        service.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
        service.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
        resourceDetailVO.setServiceAmount(service);
        extraConfig.setTotalPayment(extraConfigTotalPayment);
        extraConfig.setTotalUsed(extraConfigTotalUsed);
        resourceDetailVO.setExtraConfigAmount(extraConfig);
    }

    /**
     * 弹性文件扩缩容价格计算
     * @param resourceDetailVO
     * @param list
     */
    private void  computeModifySfsUseAndPayment(ResourceDetailVO resourceDetailVO,
                                         List<ServiceOrderPriceDetail> list) {
        //判断是不是rds 扩容
        SfProductResource sfProductResource = sfProductResourceService.getById(resourceDetailVO.getId());
        Boolean isRds=ProductCodeEnum.RDS.getProductType().equals(sfProductResource.getProductType());
        list.sort(Comparator.comparing(ServiceOrderPriceDetail::getOrderDetailId));
        Date now = new Date();
        Date current = now;
        //资源开始时间
        Date applyTime = list.stream().map(ServiceOrderPriceDetail::getStartTime)
            .min(Date::compareTo).orElse(now);
        //资源结束时间
        Date maxEndTime = list.stream().map(ServiceOrderPriceDetail::getEndTime)
            .max(Date::compareTo).orElse(now);
        Date applyTimeTemp = applyTime;
        int buyMonth = 0;
        while (applyTimeTemp.before(maxEndTime)) {
            applyTimeTemp = cn.hutool.core.date.DateUtil.offsetMonth(applyTimeTemp, 1);
            buyMonth++;
        }
        resourceDetailVO.setBuyMonth(buyMonth);

        // 变更
        if (OrderType.MODIFY.equals(resourceDetailVO.getOrderType())) {
            Date usedTime = applyTime;
            while (usedTime.before(now)) {
                usedTime = cn.hutool.core.date.DateUtil.offsetDay(usedTime, 1);
            }
            now = cn.hutool.core.date.DateUtil.offsetDay(usedTime, -1);
        }
        resourceDetailVO.setComputeDate(now);
        resourceDetailVO.setCurrentDate(current);
        // 资源取值
        BigDecimal resourceTotalPayment = resourceDetailVO.getTotalPayment();
        BigDecimal resourceOriginalCost = Convert.toBigDecimal(resourceDetailVO.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal resourceOrgDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponDiscount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal resourceCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal resourceTotalUsed = resourceDetailVO.getTotalUsedAmount();
        BigDecimal resourceUsedCouponAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCashAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal resourceUsedCreditAmount = Convert.toBigDecimal(resourceDetailVO.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 服务
        ServicePrice service = new ServicePrice();
        BigDecimal serviceTotalPayment = service.getTotalPayment();
        BigDecimal serviceOriginalCost = Convert.toBigDecimal(service.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal serviceOrgDiscount = Convert.toBigDecimal(service.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponDiscount = Convert.toBigDecimal(service.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal serviceCouponAmount = Convert.toBigDecimal(service.getTotalCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceCashAmount = Convert.toBigDecimal(service.getTotalCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceCreditAmount = Convert.toBigDecimal(service.getTotalCreditAmount(), BigDecimal.ZERO);
        BigDecimal serviceTotalUsed = service.getTotalUsed();
        BigDecimal serviceUsedCouponAmount = Convert.toBigDecimal(service.getTotalUsedCouponAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCashAmount = Convert.toBigDecimal(service.getTotalUsedCashAmount(), BigDecimal.ZERO);
        BigDecimal serviceUsedCreditAmount = Convert.toBigDecimal(service.getTotalUsedCreditAmount(), BigDecimal.ZERO);
        // 额外计费（未使用）
        ServicePrice extraConfig = new ServicePrice();
        BigDecimal extraConfigTotalPayment = extraConfig.getTotalPayment();
        BigDecimal extraConfigOriginalCost = Convert.toBigDecimal(extraConfig.getTotalOriginalCost(), BigDecimal.ZERO);
        BigDecimal extraConfigOrgDiscount = Convert.toBigDecimal(extraConfig.getTotalOrgDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigCouponDiscount = Convert.toBigDecimal(extraConfig.getTotalCouponDiscount(), BigDecimal.ZERO);
        BigDecimal extraConfigTotalUsed = extraConfig.getTotalUsed();
        Date lastStartTime = null;
        List<String> priceTypes = Lists.newArrayList();
        //时间跨了几个月 多一天也算一个月
        Integer actualMonth = 0;

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal globalPayBalance,globalPayBalanceCash,globalPayCreditLine,serviceTradePriceTag;
        globalPayBalance = globalPayBalanceCash = globalPayCreditLine = serviceTradePriceTag =BigDecimal.ZERO;
        boolean tag = false;
        for (ServiceOrderPriceDetail d : list) {
            boolean innerTag = true;
            BigDecimal amount = d.getAmount();
            Date startTime = d.getStartTime();
            Date endTime = d.getEndTime();
            if(isRds){
                //相差几个月
                d.setTradePrice( NumberUtil.div(d.getTradePrice(), resourceDetailVO.getBuyMonth()));
            }
            BigDecimal used = BigDecimal.ZERO;
            BigDecimal unUsed = BigDecimal.ZERO;
            BigDecimal payBalance = d.getPayBalance();
            if (OrderType.MODIFY.equalsIgnoreCase(d.getType())) {
                if (BigDecimal.ZERO.compareTo(payBalance) > 0 && payBalance.compareTo(d.getAmount()) > 0) {
                    // Bug57023 弹性文件扩缩容问题
                    payBalance = BigDecimalUtil.remainTwoPointAmount(d.getAmount());
                }
            }
            BigDecimal payBalanceCash = d.getPayBalanceCash();
            BigDecimal payCreditLine = d.getPayCreditLine();
            //订单已经过周期
            if (endTime.after(startTime) ) {
                Date cursorDate = endTime;
                String type = d.getType();
                if (!current.before(endTime)) {
                    used = used.add(BigDecimalUtil.remainTwoPointAmount(d.getAmount()));
                }else if(!current.before(startTime)){
                    //当前周期的月份
                    Integer offsetMonths = 0;
                    int tempMonth = 0;
                    Date tempdate = endTime;
                    while (tempdate.after(current)) {
                        tempMonth++;
                        tempdate = DateUtils.addMonths(endTime, -tempMonth);
                        if (!tempdate.before(current)) {
                            offsetMonths++;
                        }
                    }
                    cursorDate  = DateUtils.addMonths(endTime,-offsetMonths);
                    //未使用天数
                    Integer noUseOffsetDays = 0;
                    //如果扩容未使用使用天数就向上取整，如果缩容未使用天数向下取整
                    if (offsetMonths > 0) {
                        noUseOffsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(current, cursorDate, resourceDetailVO.getScaleUp());
                    } else {
                        noUseOffsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(current, endTime, resourceDetailVO.getScaleUp());
                    }
                    if (OrderType.MODIFY.equalsIgnoreCase(d.getType())) {
                        // 变配单月差价
                        BigDecimal div = NumberUtil.div(d.getAmount(), 1 + offsetMonths);
                        // 当前周期开始时间
                        DateTime ed = cn.hutool.core.date.DateUtil.offsetMonth(startTime, 1);
                        // 当前周期总天数
                        Integer currentCycleDays = DateUtil.calculateOffDay(startTime, ed);
                        BigDecimal durationUnUsed = NumberUtil.div(div, currentCycleDays)
                                                              .multiply(BigDecimal.valueOf(noUseOffsetDays))
                                                              .setScale(5, RoundingMode.HALF_UP);
                        if (offsetMonths > 0) {
                            durationUnUsed = durationUnUsed.add(NumberUtil.mul(div, offsetMonths));
                        }
                        unUsed = unUsed.add(durationUnUsed);
                        used = used.add(NumberUtil.sub(BigDecimalUtil.remainTwoPointAmount(d.getAmount().add(Optional.ofNullable(d.getCouponAmount()).orElse(BigDecimal.ZERO))), unUsed));
                    } else {
                        // 当前周期开始时间
                        DateTime ed = cn.hutool.core.date.DateUtil.offsetMonth(startTime, 1);
                        // 当前周期总天数
                        Integer currentCycleDays = DateUtil.calculateOffDay(startTime, ed);
                        BigDecimal durationUnUsed = NumberUtil.div(d.getTradePrice(), currentCycleDays)
                            .multiply(BigDecimal.valueOf(noUseOffsetDays))
                            .setScale(5, RoundingMode.HALF_UP);
                        if (offsetMonths > 0) {
                            durationUnUsed = durationUnUsed.add(NumberUtil.mul(d.getTradePrice(), offsetMonths));
                        }
                        unUsed = unUsed.add(durationUnUsed);
                        used = used.add(NumberUtil.sub(BigDecimalUtil.remainTwoPointAmount(d.getAmount().add(Optional.ofNullable(d.getCouponAmount()).orElse(BigDecimal.ZERO))), unUsed));
                    }
                }

//                do {
//                    Integer currentOffsetMonths = ++offsetMonths;
//                    if ("modify".equalsIgnoreCase(type)) {
//                        Calendar instance = Calendar.getInstance();
//                        instance.setTime(endTime);
//                        int day = instance.get(Calendar.DAY_OF_MONTH);
//                        instance.setTime(startTime);
//                        boolean notSameDay = day != instance.get(Calendar.DAY_OF_MONTH);
//                        if (notSameDay) {
//                            instance.set(Calendar.DAY_OF_MONTH, day);
//                        }
//                        Date calcEndTime = endTime.before(current)?endTime:current;
//                        Date tempDate = DateUtils.addMonths(startTime,currentOffsetMonths);
//                        //不足1个月按天计算，满足月份
//                        if (tempDate.after(calcEndTime)) {
//                            //如果扩容已使用天数就向下取整，如果缩容已使用天数向上取整
//                             offsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(cursorDate, calcEndTime,!resourceDetailVO.getScaleUp());
//                            // 尝试按一个月计算
//                            // 当前周期结束时间
//                            cursorDate = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths);
//                            // 当前周期开始时间
//                            DateTime st = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths - 1);
//                            // 当前周期总天数
//                            Integer currentCycleDays = DateUtil.calculateOffDay(st, cursorDate);
//                            BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(),currentCycleDays)
//                                .multiply(BigDecimal.valueOf(offsetDays))
//                                .setScale(5, RoundingMode.HALF_UP);
//                            used = used.add(durationUsed);
//                            cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
//                        }else{
//                            used = used.add(d.getTradePrice());
//                            cursorDate = DateUtils.addMonths(startTime, currentOffsetMonths);
//                        }
//                    }else {
//                        // 尝试按一个月计算
//                        // 当前周期结束时间
//                        cursorDate = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths);
//                        // 当前周期开始时间
//                        DateTime st = cn.hutool.core.date.DateUtil.offsetMonth(startTime, currentOffsetMonths - 1);
//                        // 当前周期总天数
//                        Integer currentCycleDays = DateUtil.calculateOffDay(st, cursorDate);
//                        if (OrderType.EXPIRED.equals(d.getType())) {
//                            // 过期时间段用续订的时间周期计算
//                            DateTime expEt = cn.hutool.core.date.DateUtil.offsetMonth(startTime, 0);
//                            DateTime expSt = cn.hutool.core.date.DateUtil.offsetMonth(startTime, -1);
//                            currentCycleDays = DateUtil.calculateOffDay(expSt, expEt);
//                        }
//                        // 判断时间
//                        actualMonth++;
//                        //不足一月 按天算 多使用了
//                        if (cursorDate.after(endTime)) {
//                            // 按天算
//                            Integer offsetDays = 0;
//                            if (current.after(endTime)) {
//                                offsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(st, endTime,!resourceDetailVO.getScaleUp());
//                            } else {
//                                offsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(st, current,!resourceDetailVO.getScaleUp());
//                            }
//
//                            Calendar calendar = Calendar.getInstance();
//                            calendar.setTime(st);
//                            if ("renew".equalsIgnoreCase(type) && Objects.nonNull(lastStartTime)) {
//                                calendar.setTime(lastStartTime);
//                                priceTypes.add(d.getPriceType());
//                            }
//                            // 月价/月天数*使用天数
//                            BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(), currentCycleDays)
//                                .multiply(BigDecimal.valueOf(offsetDays))
//                                .setScale(5, RoundingMode.HALF_UP);
//                            used = used.add(durationUsed);
//                            cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
//                        } else {
//                            //不足一月 按天算 少使用了
//                            if (current.before(cursorDate)) {
//                                // 按天算
//                                Integer offsetDays = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(st, current,!resourceDetailVO.getScaleUp());
//
//                                Calendar calendar = Calendar.getInstance();
//                                calendar.setTime(st);
//                                /// 月价/月天数*使用天数
//                                BigDecimal durationUsed = NumberUtil.div(d.getTradePrice(), currentCycleDays)
//                                    .multiply(BigDecimal.valueOf(offsetDays))
//                                    .setScale(5, RoundingMode.HALF_UP);
//                                used = used.add(durationUsed);
//                                cursorDate = cn.hutool.core.date.DateUtil.offsetDay(cursorDate, offsetDays);
//                            } else {
//                                // 一个月
//                                //正好一个月  按一个月计算
//                                BigDecimal durationUsed = d.getTradePrice().multiply(BigDecimal.ONE);
//                                used = used.add(durationUsed);
//                            }
//                        }
//                    }
//                } while (cursorDate.before(now) && cursorDate.before(endTime));
                if (priceTypes.isEmpty()) {
                    lastStartTime = startTime;
                } else if (priceTypes.contains(d.getPriceType())) {
                    priceTypes.clear();
                    lastStartTime = startTime;
                }
            }else {
                used = used.add(BigDecimalUtil.remainTwoPointAmount(d.getAmount())) ;
                //目前针对变更配置后，原订购订单数据不参与计算，导致遗漏
                totalAmount = NumberUtil.add(totalAmount,amount);

                BigDecimal balance = d.getPayBalance();
                if (BigDecimal.ZERO.compareTo(balance) > 0 && balance.compareTo(d.getAmount()) > 0) {
                    // Bug57023 弹性文件扩缩容问题
                    balance = d.getAmount();
                }
                globalPayBalance = globalPayBalance.add(balance);
                globalPayCreditLine = globalPayCreditLine.add(d.getPayCreditLine());
                globalPayBalanceCash = globalPayBalanceCash.add(d.getPayBalanceCash());
                payBalance = payBalanceCash = payCreditLine = BigDecimal.ZERO;
                if ("service".equalsIgnoreCase(d.getPriceType())){
                    serviceTradePriceTag = d.getTradePrice();
                }

                // 退订时，把变更过的订单优惠券加到总价
                d.setOriginalCost(Objects.nonNull(d.getCouponAmount()) ? d.getCouponAmount() : BigDecimal.ZERO);
                d.setDiscount(BigDecimal.ZERO);
                tag = true;
                innerTag = false;
            }

            boolean resourceUsedTag = false;
            if (tag && innerTag){
                //变更场景
                if (NumberUtil.isGreater(used, amount.subtract(d.getGiveBack()).add(totalAmount))) {
                    used = amount.subtract(d.getGiveBack()).add(totalAmount);
                }
                payBalance = payBalance.add(globalPayBalance);
                payBalanceCash = payBalanceCash.add(globalPayBalanceCash);
                payCreditLine = payCreditLine.add(globalPayCreditLine);
                globalPayBalance = globalPayBalanceCash = globalPayCreditLine = BigDecimal.ZERO;
                tag = false;
            }else {
                //原逻辑
                if (NumberUtil.isGreater(used, amount.subtract(d.getGiveBack()))) {
                    used = amount.subtract(d.getGiveBack());
                }
            }

            if (Objects.isNull(payBalance)){
                payBalance = BigDecimal.ZERO;
            }
            if (Objects.isNull(payBalanceCash)){
                payBalanceCash = BigDecimal.ZERO;
            }
            if (Objects.isNull(payCreditLine)){
                payCreditLine = BigDecimal.ZERO;
            }

            BigDecimal total = payBalance.add(payBalanceCash).add(payCreditLine);
            if ("resource".equalsIgnoreCase(d.getPriceType())) {
                resourceTotalPayment = resourceTotalPayment.add(BigDecimalUtil.getTwoPointAmount(amount));
                resourceOriginalCost = resourceOriginalCost.add(d.getOriginalCost());
                resourceOrgDiscount = resourceOrgDiscount.add(Optional.ofNullable(d.getDiscount()).orElse(BigDecimal.ZERO));
                resourceCouponDiscount = resourceCouponDiscount.add(d.getCouponAmount());
                resourceCouponAmount = resourceCouponAmount.add(payBalanceCash);
                resourceCashAmount = resourceCashAmount.add(payBalance);
                resourceCreditAmount = resourceCreditAmount.add(payCreditLine);
                // 按比例
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    resourceUsedCouponAmount = resourceUsedCouponAmount.add(
                        NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    resourceUsedCashAmount = resourceUsedCashAmount.add(
                        NumberUtil.div(used.multiply(payBalance), total, 5));
                    resourceUsedCreditAmount = resourceUsedCreditAmount.add(
                        NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
                if (resourceUsedTag){
                    used = used.subtract(serviceTradePriceTag);
                    serviceTradePriceTag = BigDecimal.ZERO;
                }
                resourceTotalUsed = resourceTotalUsed.add(used);
            } else if ("service".equalsIgnoreCase(d.getPriceType())) {
                serviceTotalPayment = serviceTotalPayment.add(amount);
                serviceOriginalCost = serviceOriginalCost.add(d.getOriginalCost());
                serviceOrgDiscount = serviceOrgDiscount.add(d.getDiscount());
                serviceCouponDiscount = serviceCouponDiscount.add(d.getCouponAmount());
                serviceCouponAmount = serviceCouponAmount.add(payBalanceCash);
                serviceCashAmount = serviceCashAmount.add(payBalance);
                serviceCreditAmount = serviceCreditAmount.add(payCreditLine);
                serviceTotalUsed = serviceTotalUsed.add(used);
                if (!NumberUtil.equals(total, BigDecimal.ZERO)) {
                    serviceUsedCouponAmount = serviceUsedCouponAmount.add(
                        NumberUtil.div(used.multiply(payBalanceCash), total, 5));
                    serviceUsedCashAmount = serviceUsedCashAmount.add(
                        NumberUtil.div(used.multiply(payBalance), total, 5));
                    serviceUsedCreditAmount = serviceUsedCreditAmount.add(
                        NumberUtil.div(used.multiply(payCreditLine), total, 5));
                }
            } else {
                extraConfigTotalPayment = extraConfigTotalPayment.add(amount);
                extraConfigOriginalCost = extraConfigOriginalCost.add(d.getOriginalCost());
                extraConfigOrgDiscount = extraConfigOrgDiscount.add(d.getDiscount());
                extraConfigCouponDiscount = extraConfigCouponDiscount.add(d.getCouponAmount());
                extraConfigTotalUsed = extraConfigTotalUsed.add(used);
            }
        }

        resourceDetailVO.setActualMonth(actualMonth);
        // 设值
        resourceDetailVO.setTotalPayment(resourceTotalPayment);
        resourceDetailVO.setTotalOriginalCost(resourceOriginalCost);
        resourceDetailVO.setTotalOrgDiscount(resourceOrgDiscount);
        resourceDetailVO.setTotalCouponDiscount(resourceCouponDiscount);
        resourceDetailVO.setTotalCouponAmount(resourceCouponAmount);
        resourceDetailVO.setTotalCashAmount(resourceCashAmount);
        resourceDetailVO.setTotalCreditAmount(resourceCreditAmount);
        resourceDetailVO.setTotalUsedAmount(resourceTotalUsed);
        resourceDetailVO.setTotalUsedCouponAmount(resourceUsedCouponAmount);
        resourceDetailVO.setTotalUsedCashAmount(resourceUsedCashAmount);
        resourceDetailVO.setTotalUsedCreditAmount(resourceUsedCreditAmount);
        resourceDetailVO.setUnsubAmount(resourceTotalPayment.subtract(resourceTotalUsed));
        resourceDetailVO.setUnsubCouponAmount(resourceCouponAmount.subtract(resourceUsedCouponAmount));
        resourceDetailVO.setUnsubCashAmount(resourceCashAmount.subtract(resourceUsedCashAmount));
        resourceDetailVO.setUnsubCreditAmount(resourceCreditAmount.subtract(resourceUsedCreditAmount));
        service.setTotalPayment(serviceTotalPayment);
        service.setTotalOriginalCost(serviceOriginalCost);
        service.setTotalOrgDiscount(serviceOrgDiscount);
        service.setTotalCouponDiscount(serviceCouponDiscount);
        service.setTotalCouponAmount(serviceCouponAmount);
        service.setTotalCashAmount(serviceCashAmount);
        service.setTotalCreditAmount(serviceCreditAmount);
        service.setTotalUsed(serviceTotalUsed);
        service.setTotalUsedCouponAmount(serviceUsedCouponAmount);
        service.setTotalUsedCashAmount(serviceUsedCashAmount);
        service.setTotalUsedCreditAmount(serviceUsedCreditAmount);
        service.setUnsubAmount(serviceTotalPayment.subtract(serviceTotalUsed));
        service.setUnsubCouponAmount(serviceCouponAmount.subtract(serviceUsedCouponAmount));
        service.setUnsubCashAmount(serviceCashAmount.subtract(serviceUsedCashAmount));
        service.setUnsubCreditAmount(serviceCreditAmount.subtract(serviceUsedCreditAmount));
        resourceDetailVO.setServiceAmount(service);
        extraConfig.setTotalPayment(extraConfigTotalPayment);
        extraConfig.setTotalUsed(extraConfigTotalUsed);
        resourceDetailVO.setExtraConfigAmount(extraConfig);
    }

    private ServicePrice calculateServicePrice(ResourceDetailVO resourceDetailVO, String priceType, boolean isFailure) {
        Criteria criteria = new Criteria();
        ProductService productService = new ProductService();
        productService.setPriceType(priceType);
        criteria.put("refInstanceIdLike", "\"" + resourceDetailVO.getId() + "\"");
        criteria.put("endTime", new Date());
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired"));
        criteria.put("orderStatus", "completed");
        criteria.put("priceType", priceType);
        List<ServiceOrderPriceDetail> list = serviceOrderPriceDetailService.selectByCriteria(criteria);
        list = list.stream().filter(o -> Objects.isNull(o.getOncePrice())).collect(Collectors.toList());
        BigDecimal totalUsed = BigDecimal.ZERO;
        BigDecimal totalPayment = BigDecimal.ZERO;
        if (Objects.nonNull(list) && CollectionUtil.isNotEmpty(list)) {
            for (ServiceOrderPriceDetail d : list) {
                if (isFailure) {
                    totalPayment = totalPayment.add(d.getTradePrice());
                    continue;
                }
                ServiceOrderDetail orderDetail = this.getById(d.getOrderDetailId());
                Integer offsetDays = DateUtil.calculateOffDay(d.getStartTime(), new Date());
                BigDecimal singleUsed = d.getPrice().multiply(BigDecimal.valueOf(offsetDays)).divide(BigDecimal.valueOf(30), 3, BigDecimal.ROUND_HALF_UP);
                BigDecimal singlePay = d.getTradePrice().multiply(BigDecimal.valueOf(orderDetail.getDuration()));
                totalPayment = totalPayment.add(singlePay);
                singleUsed = singleUsed.compareTo(singlePay) <= 0 ? singleUsed : singlePay;
                totalUsed = totalUsed.add(singleUsed);
                productService.setPriceDesc(d.getPriceDesc());
            }
            productService.setOriginalPrice(totalUsed.subtract(totalPayment));
            resourceDetailVO.getServices().add(productService);
        }
        ServicePrice servicePrice = new ServicePrice();
        servicePrice.setTotalPayment(totalPayment);
        servicePrice.setTotalUsed(totalUsed);
        return servicePrice;
    }

    private BigDecimal scaleThree(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(5, BigDecimal.ROUND_HALF_UP);
    }

}
