/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.bean.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/04/28 10:15
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询用户")
@Data
public class DescribeSubuserRequest extends BaseRequest {


    @ApiModelProperty("账号id")
    private Long accountId;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    @SafeHtml
    private String accountLike;

    /**
     * 真实姓名
     */
    @ApiModelProperty("姓名")
    @SafeHtml
    private String realNameLike;

    /**
     * 电话
     */
    @ApiModelProperty("电话")
    @SafeHtml
    private String mobileLike;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @SafeHtml
    private String emailLike;

    /**
     * 是否已关联项目
     */
    @ApiModelProperty("是否已关联项目")
    private Boolean inProject;

    /**
     * 策略id
     */
    private Long policySid;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @SafeHtml
    private String status;

    /**
     * 组织sid
     */
    @ApiModelProperty("组织ID(用户运营控制台客户管理功能查询子用户)")
    @SafeHtml
    private String orgSid;

    /**
     * 是否包含租户管理员
     */
    @ApiModelProperty("是否包含租户管理员")
    private Boolean includeParent = false;

    /**
     * 认证类型
     */
    @ApiModelProperty("认证类型 user-个人,company--企业")
    @SafeHtml
    private String type;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @SafeHtml
    @Deprecated
    private String accountName;

    /**
     * 管理员姓名
     */
    @ApiModelProperty("管理员姓名")
    @SafeHtml
    @Deprecated
    private String adminName;

    /**
     * 主用户名
     */
    @ApiModelProperty("主用户名")
    @SafeHtml
    @Deprecated
    private String mainAccount;

    private Long userSid;
}
