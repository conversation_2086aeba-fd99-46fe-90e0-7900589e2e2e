/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 月费用分摊服务
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Document(collection = "biz_bill_usage_item")
@CompoundIndexes({@CompoundIndex(name = "payTime_-1_usageStartDate_-1", def = "{'payTime':-1, 'usageStartDate':-1}")})
public class InstanceGaapCost extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @MongoId
    private String id;

    private Long orgSid;

    private Long cloudEnvId;

    private String cloudEnvType;

    /**
     * 账期YYYY－MM
     */
    private String billingCycle;

    private String instanceId;

    /**
     * 产品代码 disk,ecs
     */
    private String productCode;

    /**
     * 订阅类型 Subscription／PayAsYouGo
     */
    private String subscriptionType;

    /**
     * 账单类型：SubscriptionOrder (预付订单)， PayAsYouGoBill (后付账单)， Refund (退款)， Adjustment (调账)
     */
    private String billType;

    /**
     * 订单类型：New(新购)，Renewal(续费)，Upgrade(升级)，Degrade(降级)，BillType非SubscriptionOrder时为空
     */
    private String orderType;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 如果是订单填写子订单号
     */
    private String subOrderId;

    /**
     * Caller UID
     */
    private String payerAccount;

    /**
     * OwnerID
     */
    private String ownerId;

    /**
     * Region
     */
    private String region;

    /**
     * 原始金额
     */
    private BigDecimal pretaxGrossAmount;

    /**
     * 询价优惠
     */
    private BigDecimal pricingDiscount;

    /**
     * 优惠后金额
     */
    @CCSPIntegralityHashAndVerify
    private BigDecimal pretaxAmount;

    /**
     * 币种CNY/USD/JPY
     */
    private String currency;

    /**
     * 支付币种(国际)
     */
    private String paymentCurrency;

    /**
     * 费用产生时间:支付时间
     */

    @Field("payTime")
    private Date payTime;

    /**
     * 服务起始时间
     */

    @Field("usageStartDate")
    private Date usageStartDate;

    /**
     * 服务结束时间
     */
    private Date usageEndDate;

    /**
     * 创建时间
     */
    private Date createdDt;

    private String resourceId;

    /**
     * 云环境账号
     */
    private Long cloudEnvAccountId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 扩展属性
     */
    private String extra;

    /**
     * 云平台服务类型编码（华为云）
     */
    private String cloudServiceCode;
    private String cloudServiceCodeUs;

    /**
     * 本月分摊优惠后金额
     */
    private BigDecimal monthGaapPretaxAmount;

    /**
     * 已分摊优惠后金额
     */
    private BigDecimal gaapPretaxAmount;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * 分销商名称
     */
    @TableField(exist = false)
    private String distributorName;

    /**
     * 规格项
     */
    @TableField(exist = false)
    private String configuration;

    @TableField(exist = false)
    private String cloudEnvName;

    @TableField(exist = false)
    private String cloudEnvAccountName;

    @TableField(exist = false)
    private String orgName;

    @TableField(exist = false)
    private BigDecimal couponDiscount;

    @TableField(exist = false)
    private BigDecimal orgDiscount;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;
    /**
     * 官网价
     */
    private BigDecimal officialAmount;
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    /**
     * 现金支付金额
     */
    private BigDecimal cashAmount;
    /**
     * 信用额度支付金额
     */
    private BigDecimal creditAmount;
    /**
     * 代金券支付金额
     */
    private BigDecimal couponAmount;

    private String resourceConfig;

    private String billSource;

    private String priceType;

    private String status;

    private String description;

    /**
     * 单个话单内使用量
     */
    @CCSPIntegralityHashAndVerify
    private String usageCount;
    /**
     * 需要计费的用量和免费的用量
     */
    private String billedAndFree;
    /**
     * 计费周期归档表关联id
     */
    private String billBillingCycleId;

    /**
     * 开票状态
     * done（已开票）
     */
    private String invoiceStatus;

    /**
     * 审核开票时间
     */
    private Date invoiceDt;

    @TableField(exist = false)
    private String priceTypeName;
    @TableField(exist = false)
    private String payTypeName;


    /**
     * 订单状态
     * release_success（退订成功）
     */
    private String orderStatus;

    /**
     * 订单类型
     * release-退订，apply--申请
     */
    private String type;

    /**
     * accountID
     */

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userAccountId;
    /**
     * 用户名
     */
    private String userAccountName;
    /**
     * 订单编号
     */
    private String orderSn;
    /**
     * 有效算力（CUE）
     */
    private String cueValue;

    /**
     * 是否可开票
     */
    private String invoicable;
    /**
     * 抵扣信用余额
     */
    private BigDecimal rechargeCreditAmount;

    /**
     * 抹零金额
     */
    private BigDecimal eraseZeroAmount;

    /**
     * 抵扣现金券优惠金额
     */
    private BigDecimal deductCouponDiscount;


    /**
     * 套餐包产品ID
     */
    private Long bagId;
    /**
     * 用户套餐折扣包订购实例ID
     */
    private String bagDiscountInstId;
    /**
     * 套餐包折扣 如0.88==88折
     */
    private Double bagDiscount;
    /**
     * 套餐优惠价格
     */
    private BigDecimal bagDiscountAmount;
    /**
     * 套餐包抵扣之前价格
     */
    private BigDecimal beforeBagDiscountAmount;
    /**
     * 作业ID
     */
    private String jobId;
    /**
     * 作业名称
     */
    private String jobName;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 集群ID
     */
    private Long clusterId;
    /**
     *  租户
     */
    private String userGroup1;
    /**
     * Account
     */
    private String account;
    /**
     * 作业提交时间
     */
    private Date submitTime;
    /**
     * 作业开始时间
     */
    private Date startTime;
    /**
     * 作业完成时间
     */
    private Date endTime;
    /**
     * 作业使用CPU总核数/GPU总卡数
     */
    private String count;
    /**
     * 收费规则：02:销售计费、01:正常计费
     */
    private String chargingType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 实体名称
     */
    private String entityName;

    /**
     *  算力
     */
    private String computingPower;

    /**
     * 订单来源
     */
    private String orderSourceSn;

    /**
     * 使用的卡时
     */
    private BigDecimal usedCardHourAmount;


    /**
     * 统计小时数
     */
    private BigDecimal statisticHours;

    /**
     * 统计天数
     */
    private BigDecimal statisticDays;
}
