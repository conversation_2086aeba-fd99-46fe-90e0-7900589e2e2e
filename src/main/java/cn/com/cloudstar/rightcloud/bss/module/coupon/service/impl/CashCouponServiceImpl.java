/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.*;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.DistributeStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.CashCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.CashCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CashCouponSendRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DeductCashCouponRecordRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponAccountSumResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponGroupNameResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DeductCashCouponRecordResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.CashCouponFeignService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.CashCouponService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *@description
 *<AUTHOR>
 *@date
 */
@Slf4j
@Service
public class CashCouponServiceImpl extends ServiceImpl<CashCouponMapper, CashCoupon> implements CashCouponService {

    /**
     * 默认充值现金券CZXJJ
     */
    private static final String DEFAULT_PREFIX_CZXJQ = "CZXJQ";

    /**
     * 默认抵扣充值现金券DKXJJ
     */
    private static final String DEFAULT_PREFIX_DYXJQ = "DKXJQ";

    /**
     * 类型-充值现金券
     */
    private static final String TYPE_DEPOSIT = "deposit";

    /**
     * 类型-抵扣现金券
     */
    private static final String TYPE_DEDUCT = "deduct";

    private static final String CONSOLE = "console";
    private static final String COUPON_NAME = "couponName";
    private static final String DESC = "desc";
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private CashCouponMapper cashCouponMapper;

    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Resource
    private CashCouponFeignService cashCouponFeignService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    @Autowired
    private OrgService orgService;
    @Autowired
    private IBizBillingAccountService billingAccountService;

    @Override
    public void createCashCoupon(CreateCashCouponRequest createCashCouponRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        log.info("开始创建现金卷 数量:{}", createCashCouponRequest.getSum());
        // 33960 开始时间与结束时间相同
        Date requestStartTime = createCashCouponRequest.getStartTime();
        Date currentDate = new Date();
        if (requestStartTime.getTime() - createCashCouponRequest.getEndTime().getTime() == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        if (createCashCouponRequest.getStartTime().before(DateUtil.beginOfDay(new Date()))) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_5));
        }
        // 开始时间大于结束时间
        if (requestStartTime.after(createCashCouponRequest.getEndTime())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        Date preDate = DateUtils.addDays(currentDate, -1);
        if (requestStartTime.before(preDate)) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_264378936), DateFormatUtils.format(preDate, cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.COMMON_DATE_PATTERN)));
        }
        if (createCashCouponRequest.getEndTime().before(currentDate)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_940920911));
        }
        List<CashCoupon> cashCouponList = new ArrayList<>();
        for (int i = 0; i < createCashCouponRequest.getSum(); i++) {
            CashCoupon cashCoupon = BeanConvertUtil.convert(createCashCouponRequest, CashCoupon.class);
            if (cashCoupon==null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1639013906));
            }
            if (TYPE_DEDUCT.equals(createCashCouponRequest.getType())) {
                cashCoupon.setCouponNo(NoUtil.generateNo(DEFAULT_PREFIX_DYXJQ) + RandomUtil.randomNumbers(4));
            } else {
                cashCoupon.setCouponNo(NoUtil.generateNo(DEFAULT_PREFIX_CZXJQ) + RandomUtil.randomNumbers(4));
                cashCoupon.setType(TYPE_DEPOSIT);
            }
            //初始状态为未分发
            cashCoupon.setStatus(DistributeStatusEnum.UNDISTRIBUTED.getCode());
            cashCoupon.setBalance(String.valueOf(cashCoupon.getAmount()));
            // 运营实体Id
            cashCoupon.setEntityId(authUser.getEntityId());
            cashCoupon.setEntityName(entityUserMapper.getByEntityId(authUser.getEntityId()).getEntityName());
            WebUserUtil.prepareInsertParams(cashCoupon);
            //增加运营实体
            Long entityId = RequestContextUtil.getEntityId();
            cashCoupon.setEntityId(entityId);
            cashCoupon.setEntityName(entityUserMapper.getByEntityId(entityId).getEntityName());
            cashCouponList.add(cashCoupon);
        }
        this.saveBatch(cashCouponList);
    }

    @Override
    public RestResult check(String cashCouponNo,String accountId) {
        CashCoupon cashCoupon = null;
        if (Objects.isNull(cashCouponNo)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_208845664));
        }
        //查询现金券相关信息
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("coupon_no", cashCouponNo);
        queryWrapper.eq("type", "deposit");
        cashCoupon = cashCouponMapper.selectOne(queryWrapper);
        if (Objects.isNull(cashCoupon)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1278593277));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        LambdaQueryWrapper<BizBillingAccount> qw = new LambdaQueryWrapper<>();
        qw.eq(BizBillingAccount::getOrgSid, authUserInfo.getOrgSid());
        qw.eq(BizBillingAccount::getEntityId, cashCoupon.getEntityId());
        qw.eq(BizBillingAccount::getId, accountId);
        if (bizBillingAccountMapper.selectCount(qw) == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        if (!Long.valueOf(accountId).equals(cashCoupon.getAccountId())
                || !cashCoupon.getStatus().equals(CouponStatusEnum.UNUSED.getCode())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1580643361));
        }
        if (cashCoupon.getStartTime().after(new DateTime())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1969113451));
        }

        return new RestResult(cashCoupon.getAmount());
    }


    @Override
    public IPage<DeductCashCouponRecordResponse> deductRecordList(DeductCashCouponRecordRequest request) {
        Criteria criteria = new Criteria();
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (CONSOLE.equals(authUserInfo.getRemark())) {
            Long userSid = authUserInfo.getUserSid();
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserMapper.selectByPrimaryKey(userSid);
            if (Objects.isNull(user)) {
                throw  new BizException("登录信息不能为空!");
            }
            if (Objects.nonNull(user)) {
                QueryWrapper<BizBillingAccount> query = new QueryWrapper<BizBillingAccount>().eq("org_sid", user.getOrgSid());
                List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(query);
                if (Objects.nonNull(bizBillingAccounts) && bizBillingAccounts.size() > 0) {
                    criteria.put("accountIds", bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList()));
                }
            }
            QueryWrapper<BizBillingAccount> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("admin_sid", user.getUserSid());

            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(queryWrapper);
            List<Long> accountIds = bizBillingAccounts.stream()
                    .map(BizBillingAccount::getId)
                    .collect(Collectors.toList());
            QueryWrapper<CashCoupon> queryWrapper1 = new QueryWrapper<>();
            if(!CollectionUtils.isEmpty(accountIds)){
                queryWrapper1.in("account_id", accountIds);
            }
            if(StringUtil.isNotBlank(request.getDeductId())){
                queryWrapper1.eq("coupon_no", request.getDeductId());
            }
            Integer integer = cashCouponMapper.selectCount(queryWrapper1);
            if (integer == 0) {
                throw new BizException(RestConst.HttpConst.Unauthorized.getType());
            }
        } else {
            CashCoupon cashCoupon = cashCouponMapper.selectOne(Wrappers.<CashCoupon>lambdaQuery()
                                                                       .eq(CashCoupon::getCouponNo, request.getDeductId()));
            if (!cashCoupon.getEntityId().equals(authUserInfo.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_885284322));
            }
        }

        criteria.put("deductCashNo", request.getDeductId());
        IPage<BizAccountDeal> bizAccountDealIPage = bizAccountDealMapper.selectByParams(
                PageUtil.preparePageParams(request), criteria.getCondition());
        IPage<DeductCashCouponRecordResponse> iPage = BeanConvertUtil.convertPage(bizAccountDealIPage,
                                                                                  DeductCashCouponRecordResponse.class);
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("coupon_no", request.getDeductId());
        for (DeductCashCouponRecordResponse date : iPage.getRecords()) {
            Query query = new Query();
            org.springframework.data.mongodb.core.query.Criteria cs = org.springframework.data.mongodb.core.query.Criteria
                    .where("billNo")
                    .is(date.getBillNo());
            query.addCriteria(cs);
            List<InstanceGaapCost> costs = mongoTemplate.find(query, InstanceGaapCost.class, "biz_bill_usage_item");
            if (!CollectionUtils.isEmpty(costs)) {
                String productCode = costs.get(0).getProductCode();
                if (StringUtil.isNotEmpty(productCode)) {
                    List<String> productScopeList = ProductComponentEnum.transformDesc(
                            StrUtil.splitToArray(productCode, StrUtil.COMMA));
                    if (!CollectionUtils.isEmpty(productScopeList)) {
                        date.setProduct(String.join("，", productScopeList));
                    }
                }
            }
        }
        return iPage;
    }

    @Override
    public void distributeCashCoupon(DistributeCashCouponRequest distributeCashCouponRequest) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_927777777));
        }
        List<Long> accountIds = distributeCashCouponRequest.getAccountId();
        if(CollectionUtil.isEmpty(accountIds)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_881048870));
        }
        //账户状态是否冻结
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(
                Wrappers.<BizBillingAccount>lambdaQuery()
                        .in(BizBillingAccount::getId, accountIds));
        bizBillingAccounts.stream().forEach(e -> {
            if (e.getStatus().equals("freeze")){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2070382864));
            }
        });
        for (int i = 0; i < accountIds.size(); i++) {
            String couponNo = distributeCashCouponRequest.getCouponNo().get(i);
            Long accountId = accountIds.get(i);
            QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("coupon_no", couponNo);
            CashCoupon cashCoupon = this.getOne(queryWrapper);
            if (cashCoupon == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if (Objects.nonNull(cashCoupon) && !cashCoupon.getEntityId().equals(RequestContextUtil.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
            // 验证现金券是否是未分发状态
            if (!cashCoupon.getStatus().equals(DistributeStatusEnum.UNDISTRIBUTED.getCode())
                    && !cashCoupon.getStatus().equals(DistributeStatusEnum.APPROVALING.getCode())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1026419822));
            }
            cashCoupon.setAccountId(accountId);
            cashCoupon.setUpdatedBy(authUser.getAccount());
            cashCoupon.setUpdatedDt(new Date());
            cashCoupon.setDistributeDt(new Date());
            if (TYPE_DEPOSIT.equals(cashCoupon.getType())) {
                //将状态置为 未使用 状态
                cashCoupon.setStatus(CouponStatusEnum.UNUSED.getCode());
            } else {
                //将状态置为 可用 状态
                cashCoupon.setStatus(CouponStatusEnum.AVAILABLE.getCode());
            }
            Boolean save = this.saveOrUpdate(cashCoupon);
            if (!save) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_566566488));
            }
            // 分发后推送相关消息
            if (distributeCashCouponRequest.getIsNotice() || distributeCashCouponRequest.getIsSMS()
                    || distributeCashCouponRequest.getIsEmail()) {
                BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
                User user = cashCouponMapper.getUserById(bizBillingAccount.getAdminSid());
                if (user != null) {
                    CashCouponSendRequest cashCouponSendRequest = new CashCouponSendRequest();
                    int[] ary = new int[]{};
                    if (distributeCashCouponRequest.getIsNotice()) {
                        ary = Arrays.copyOf(ary, ary.length + 1);
                        ary[ary.length - 1] = 0;
                    }
                    if (distributeCashCouponRequest.getIsEmail()) {
                        ary = Arrays.copyOf(ary, ary.length + 1);
                        ary[ary.length - 1] = 1;
                        cashCouponSendRequest.setEmail(user.getEmail());
                    }
                    if (distributeCashCouponRequest.getIsSMS()) {
                        ary = Arrays.copyOf(ary, ary.length + 1);
                        ary[ary.length - 1] = 2;
                        cashCouponSendRequest.setMobile(user.getMobile());
                    }
                    cashCouponSendRequest.setAccountId(cashCoupon.getAccountId());
                    cashCouponSendRequest.setAmount(cashCoupon.getAmount());
                    cashCouponSendRequest.setCouponNo(cashCoupon.getCouponNo());
                    cashCouponSendRequest.setStartDate(cashCoupon.getStartTime());
                    cashCouponSendRequest.setEndDate(cashCoupon.getEndTime());
                    cashCouponSendRequest.setType(cashCoupon.getType());
                    cashCouponSendRequest.setProductScope(cashCoupon.getProductScope());
                    cashCouponSendRequest.setAccountName(bizBillingAccount.getAdminName());
                    cashCouponSendRequest.setEntityId(bizBillingAccount.getEntityId());
                    cashCouponSendRequest.setEntityName(bizBillingAccount.getEntityName());
                    cashCouponSendRequest.setMessageType(ary);
                    try {
                        Boolean r = cashCouponFeignService.sendNotification(cashCouponSendRequest);
                    } catch (Exception e) {
                        log.info("分发现金券推送消息失败  msg:{}", e.getMessage());
                    }
                }
            }

        }

    }

    @Override
    public void cancelCashCoupon(List<String> couponNos, String remark) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_927777777));
        }
        for (String couponNo : couponNos) {
            QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("coupon_no", couponNo);
            CashCoupon cashCoupon = this.getOne(queryWrapper);
            if (cashCoupon == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            //if (!cashCoupon.getStatus().equals(DistributeStatusEnum.UNDISTRIBUTED.getCode())) {
            //    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_806482332));
            //}
            if (!cashCoupon.getEntityId().equals(RequestContextUtil.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
            cashCoupon.setUpdatedBy(authUser.getAccount());
            cashCoupon.setUpdatedDt(new Date());
            //将状态置为作废
            cashCoupon.setStatus(CouponStatusEnum.DELETED.getCode());
            cashCoupon.setRemark(remark);
            Boolean save = this.saveOrUpdate(cashCoupon);
            if (!save) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_19127072));
            }
        }

    }

    @Override
    public IPage<CashCouponGroupNameResponse> pageCashCoupon(Integer pagenum, Integer pagesize, String type,
                                                             String couponNo, String status, String sortdatafield,
                                                             String sortorder, String couponName, String accountNameLike) {
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        if (StringUtil.isNotEmpty(couponName)) {
            queryWrapper.like("coupon_name", couponName);
        }
        if (StringUtil.isNotEmpty(couponNo)) {
            queryWrapper.like("coupon_no", couponNo);
        }
        queryWrapper.groupBy("coupon_name");
        if (StringUtil.isNotEmpty(sortdatafield) && sortdatafield.equalsIgnoreCase(COUPON_NAME)) {
            sortdatafield = "coupon_name";
        }
        if (sortorder != null && !sortorder.equals(DESC)) {
            queryWrapper.orderByAsc(sortdatafield);
        } else {
            if (sortorder == null) {
                sortdatafield = "created_dt";
            }
            queryWrapper.orderByDesc(sortdatafield);
        }
        //增加多运营实体查询条件
        Long entityId = RequestContextUtil.getEntityId();
        queryWrapper.eq("entity_id", entityId);

        //账户
        List<BizBillingAccount> billingAccountList = new ArrayList<>();
        Map<Long, BizBillingAccount> bizAccountIdMap = new HashMap<>();
        if (StringUtils.isNotEmpty(accountNameLike)) {
            LambdaQueryWrapper<BizBillingAccount> bizBillingAccountLambdaQueryWrapper = Wrappers.<BizBillingAccount>lambdaQuery()
                .eq(BizBillingAccount::getEntityId, entityId)
                .like(BizBillingAccount::getAccountName, accountNameLike);
            billingAccountList = bizBillingAccountMapper.selectList(bizBillingAccountLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(billingAccountList)) {
                return new Page<>(1L,0L);
            }
            bizAccountIdMap = billingAccountList.stream().collect(Collectors.toMap(BizBillingAccount::getId, b -> b, (v1, v2) -> v1));
            queryWrapper.in("account_id",new ArrayList<>(bizAccountIdMap.keySet()));
        }

        IPage<CashCoupon> page = new Page<>(pagenum + 1, pagesize);
        IPage<CashCoupon> cashCoupons = this.page(page, queryWrapper);
        IPage<CashCouponGroupNameResponse> cashCouponGroupNameResponsePage = BeanConvertUtil.convertPage(cashCoupons,
                                                                                                         CashCouponGroupNameResponse.class);
        boolean bizAccountIdMapNotEmpty = !bizAccountIdMap.isEmpty();
        for (CashCouponGroupNameResponse cashCouponGroupNameResponse : cashCouponGroupNameResponsePage.getRecords()) {
            // 获取此分组下全部张数
            QueryWrapper<CashCoupon> queryWrapperSum = new QueryWrapper<>();
            queryWrapperSum.eq("type", type);
            queryWrapperSum.eq("coupon_name", cashCouponGroupNameResponse.getCouponName());
            queryWrapperSum.eq("entity_id", entityId);
            if (bizAccountIdMapNotEmpty) {
                queryWrapperSum.in("account_id",new ArrayList<>(bizAccountIdMap.keySet()));
            }
            cashCouponGroupNameResponse.setSumCount(this.count(queryWrapperSum));
            // 获取此分组下未分发张数
            queryWrapperSum.eq("status", DistributeStatusEnum.UNDISTRIBUTED);
            cashCouponGroupNameResponse.setUndistributedCount(this.count(queryWrapperSum));
            // 获取现金券详情分页列表
            QueryWrapper<CashCoupon> queryWrapperCash = new QueryWrapper<>();
            queryWrapperCash.eq("type", type);
            queryWrapperCash.eq("coupon_name", cashCouponGroupNameResponse.getCouponName());
            queryWrapperCash.orderByDesc("status");
            queryWrapperCash.eq("entity_id", entityId);
            if (bizAccountIdMapNotEmpty) {
                queryWrapperCash.in("account_id", new ArrayList<>(bizAccountIdMap.keySet()));
            }
            queryWrapperCash.eq("entity_id", entityId);
            List<CashCoupon> cashCouponsInfo = this.list(queryWrapperCash);
            for (CashCoupon cashCoupon : cashCouponsInfo) {
                if (Objects.nonNull(cashCoupon.getStatus())) {
                    cashCoupon.setStatus(
                            EnumUtil.likeValueOf(CouponStatusEnum.class, cashCoupon.getStatus()).getAlia());
                }
                if (StringUtil.isNotEmpty(cashCoupon.getProductScope())) {
                    List<String> productScopeList = ProductComponentEnum.transformDesc(
                            StrUtil.splitToArray(cashCoupon.getProductScope(), StrUtil.COMMA));
                    if (!CollectionUtils.isEmpty(productScopeList)) {
                        cashCoupon.setProductScope(String.join("，", productScopeList));
                    }
                }

                if (cashCoupon.getAccountId() == null) {
                    continue;
                }
                BizBillingAccount bizBillingAccount = null;
                if (bizAccountIdMapNotEmpty) {
                    bizBillingAccount = bizAccountIdMap.get(cashCoupon.getAccountId());
                } else {
                    bizBillingAccount = bizBillingAccountMapper.selectById(cashCoupon.getAccountId());
                }
                if (bizBillingAccount != null) {
                    cashCoupon.setAccountName(bizBillingAccount.getAccountName());
                }
            }
            cashCouponGroupNameResponse.setCouponList(cashCouponsInfo);
        }
        return cashCouponGroupNameResponsePage;
    }

    @Override
    public IPage<CashCoupon> pageCashListInfo(Integer pagenum, Integer pagesize, String type, String couponNo,
                                              String couponName, String status, String sortdatafield,
                                              String sortorder, String accountNameLike) {
        QueryWrapper<CashCoupon> queryWrapperCash = new QueryWrapper<>();
        if(Objects.nonNull(type) && !"".equals(type)){
            queryWrapperCash.eq("type", type);
        }
        queryWrapperCash.eq("coupon_name", couponName);
        queryWrapperCash.orderByAsc(
                "FIELD(status,\"approvaling\",\"undistributed\",\"available\",\"exhausted\",\"unavailable\",\"deleted\")");
        if (StringUtil.isEmpty(sortdatafield)) {
            sortdatafield = "id";
            sortorder = "desc";
        }
        if (sortorder != null && !sortorder.equals(DESC)) {
            queryWrapperCash.orderByAsc(sortdatafield);
        } else {
            queryWrapperCash.orderByDesc(sortdatafield);
        }
        if (StringUtil.isNotEmpty(couponNo)) {
            queryWrapperCash.like("coupon_no", couponNo);
        }
        if (StringUtil.isNotEmpty(status)) {
            queryWrapperCash.eq("status", EnumUtil.likeValueOf(CouponStatusEnum.class, status).getCode());
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        queryWrapperCash.eq("entity_id",authUserInfo.getEntityId());

        //账户
        List<BizBillingAccount> billingAccountList = new ArrayList<>();
        Map<Long, BizBillingAccount> bizAccountIdMap = new HashMap<>();
        if (StringUtils.isNotEmpty(accountNameLike)) {
            LambdaQueryWrapper<BizBillingAccount> bizBillingAccountLambdaQueryWrapper = Wrappers.<BizBillingAccount>lambdaQuery()
                .eq(BizBillingAccount::getEntityId, authUserInfo.getEntityId())
                .like(BizBillingAccount::getAccountName, accountNameLike);
            billingAccountList = bizBillingAccountMapper.selectList(bizBillingAccountLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(billingAccountList)) {
                return new Page<>(1L,0L);
            }
            bizAccountIdMap = billingAccountList.stream().collect(Collectors.toMap(BizBillingAccount::getId, b -> b, (v1, v2) -> v1));
            queryWrapperCash.in("account_id",new ArrayList<>(bizAccountIdMap.keySet()));
        }

        IPage<CashCoupon> pageCash = new Page<>(pagenum + 1, pagesize);
        IPage<CashCoupon> cashCouponsPage = this.page(pageCash, queryWrapperCash);
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        for (CashCoupon cashCoupon : cashCouponsPage.getRecords()) {
            if (StringUtil.isNotEmpty(cashCoupon.getProductScope())) {
                List<String> productScopeList = ProductComponentEnum.transformDesc(
                        StrUtil.splitToArray(cashCoupon.getProductScope(), StrUtil.COMMA));
                if (!CollectionUtils.isEmpty(productScopeList)) {
                    cashCoupon.setProductScope(String.join("，", productScopeList));
                }
            }

            if (isUs) {
                cashCoupon.setProductScope(this.productScopeToUs(cashCoupon.getProductScope()));
            }

            cashCoupon.setStatus(EnumUtil.likeValueOf(CouponStatusEnum.class, cashCoupon.getStatus()).getAlia());
            if (cashCoupon.getAccountId() == null) {
                continue;
            }
            BizBillingAccount bizBillingAccount = null;
            if (!bizAccountIdMap.isEmpty()) {
                bizBillingAccount = bizAccountIdMap.get(cashCoupon.getAccountId());
            } else {
                bizBillingAccount = bizBillingAccountMapper.selectById(cashCoupon.getAccountId());
            }
            if (bizBillingAccount != null) {
                cashCoupon.setAccountName(bizBillingAccount.getAccountName());
            }

        }
        List<CashCoupon> records = cashCouponsPage.getRecords();
        records.stream().filter(record -> Objects.nonNull(record.getProductScope()) && record.getProductScope().contains("OBS"))
                .forEach(record -> record.setProductScope(record.getProductScope().replaceAll("OBS", "对象存储")));
        cashCouponsPage.setRecords(records);
        return cashCouponsPage;
    }

    private String productScopeToUs(String productScope) {
        productScope = productScope
                .replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                .replaceAll("云硬盘", "Cloud Hard Disk")
                .replaceAll("弹性裸金属", "Elastic bare metal")
                .replaceAll("对象存储", "Object storage")
                .replaceAll("AI开发平台专属资源池", "Ascend Modelarts exclusive resource pool");
        return productScope;
    }

    @Override
    public void overdueTask() {
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.le("end_time", DateUtil.date());
        queryWrapper.eq("status", CouponStatusEnum.UNUSED).or().eq("status", DistributeStatusEnum.UNDISTRIBUTED);
        List<CashCoupon> cashCoupons = this.list(queryWrapper);
        if (cashCoupons.size() > 0) {
            log.info("找到{}条过期现金券", cashCoupons.size());
            for (CashCoupon cashCoupon : cashCoupons) {
                // 将状态置为过期
                cashCoupon.setStatus(CouponStatusEnum.UNAVAILABLE.getCode());
            }
            updateBatchById(cashCoupons);
        }
    }

    @Override
    public IPage<BizBillingAccount> findBillingAccount(Integer pagenum, Integer pagesize, String accountName,
                                                       String orgName, String mobile, String account) {

        Page<BizBillingAccount> page = new Page<>(pagenum + 1, pagesize);
        Map<String, Object> condition = new HashMap<>(0);
        condition.put("status", 1);
        condition.put("certificationStatus", "authSucceed");
        condition.put("freezeStatus", 1);
        condition.put("accountName", accountName);
        condition.put("orgName", orgName);
        condition.put("mobile", mobile);
        condition.put("account", account);
        condition.put("entityId", RequestContextUtil.getEntityId());
        IPage<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(page, condition);
        bizBillingAccounts.setRecords(DesensitizationUtil.desensitization(bizBillingAccounts.getRecords()));
        return bizBillingAccounts;
    }

    @Override
    public IPage<CashCouponAccountResponse> accountListCashCoupon(Integer pagenum, Integer pagesize, String type,
                                                                  String couponNo, String couponStatus, Long accountId,
                                                                  String sortdatafield, String sortorder,boolean selfFlag) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_803011385));
            }
        List<Long> accountIds;
        if (Objects.isNull(authUserInfo.getOrgSid()) || UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
            if (!Objects.equals(authUserInfo.getEntityId(), bizBillingAccount.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
            // 运营管理员
            accountIds = Collections.singletonList(accountId);
        } else {
            Long userSid = authUserInfo.getUserSid();
            if (Objects.nonNull(authUserInfo.getParentSid())) {
                // 子用户查看租户账户
                userSid = authUserInfo.getParentSid();
        }
            //根据当前登录用户查找出对应account
            List<BizBillingAccount> billingAccounts = billingAccountService.getByAdminSid(userSid);
            accountIds = billingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(accountIds)) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_803011385));
        }
        }
        Criteria criteria = new Criteria();
        criteria.put("accountIds",accountIds);
        criteria.put("type",type);
        if (StringUtil.isNotEmpty(couponNo)) {
            criteria.put("couponNo",couponNo);
        }
        criteria.put("sortdatafield",sortdatafield);
        if (StringUtil.isNotEmpty(couponStatus)) {
            criteria.put("status", EnumUtil.likeValueOf(CouponStatusEnum.class, couponStatus).getCode());
        }
        BaseRequest request = new BaseRequest();
        request.setPagenum(pagenum.longValue());
        request.setPagesize(pagesize.longValue());
        IPage<CashCoupon>  iPage = PageUtil.preparePageParams(request);
        IPage page = cashCouponMapper.pageCashCouponCustomer(iPage, criteria.getCondition());
        List<CashCouponAccountResponse> cashCouponAccountResponseList = BeanConvertUtil.convert(
                page.getRecords(), CashCouponAccountResponse.class);
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        cashCouponAccountResponseList.forEach(data -> {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(data.getAccountId());
            data.setAccountName(bizBillingAccount == null ? null: bizBillingAccount.getAccountName());
            data.setStatus(EnumUtil.likeValueOf(CouponStatusEnum.class, data.getStatus()).getAlia());
            if (StringUtil.isNotEmpty(data.getProductScope())) {
                List<String> productScopeList = ProductComponentEnum.transformDesc(
                        StringUtil.split(data.getProductScope(), StrUtil.COMMA));
                if (!CollectionUtils.isEmpty(productScopeList)) {
                    if (isUs) {
                        productScopeList = ProductComponentEnum.ProductDescUsEnum.transformDescUs(productScopeList);
                    }
                    data.setProductScope(String.join("，", productScopeList));
                }
            }
        });
        page.setRecords(cashCouponAccountResponseList);
        return page;
    }

    @Override
    public CashCouponAccountSumResponse accountCashSum() {
        CashCouponAccountSumResponse cashCouponAccountSumResponse = new CashCouponAccountSumResponse();
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_927777777));
        }
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.getByAdminSid(authUser.getUserSid());

        if (CollectionUtil.isEmpty(bizBillingAccounts)){
            bizBillingAccounts=bizBillingAccountMapper.getByOrgSid(authUser.getOrgSid());
        }
        List<Long> accountIds = bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            log.info("bizBillingAccount is null");
            return cashCouponAccountSumResponse;
        }
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id",accountIds);
        queryWrapper.eq("status", CouponStatusEnum.UNUSED.getCode());
        queryWrapper.eq("type", TYPE_DEPOSIT);
        cashCouponAccountSumResponse.setSum(cashCouponMapper.selectCount(queryWrapper));

        QueryWrapper<CashCoupon> couponQueryWrapper = new QueryWrapper<>();
        couponQueryWrapper.in("account_id",
                              bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList()));
        couponQueryWrapper.eq("status", CouponStatusEnum.USED.getCode());
        couponQueryWrapper.eq("type", TYPE_DEPOSIT);
        cashCouponAccountSumResponse.setUsed(cashCouponMapper.selectCount(couponQueryWrapper));
        return cashCouponAccountSumResponse;
    }


    @Override
    public CashCouponAccountSumResponse accountDeductCashSum() {
        CashCouponAccountSumResponse cashCouponAccountSumResponse = new CashCouponAccountSumResponse();
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_927777777));
        }

        List<BizBillingAccount> accounts = bizBillingAccountMapper.getByAdminSid(authUser.getUserSid());

        if (CollectionUtil.isEmpty(accounts)){
            accounts=bizBillingAccountMapper.getByOrgSid(authUser.getOrgSid());
        }

        List<Long> accountIds = accounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(accountIds)) {
            log.info("bizBillingAccount is null");
            return cashCouponAccountSumResponse;
        }
        QueryWrapper<CashCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountIds);
        queryWrapper.eq("status", CouponStatusEnum.AVAILABLE.getCode());
        queryWrapper.eq("type", TYPE_DEDUCT);
        cashCouponAccountSumResponse.setSum(cashCouponMapper.selectCount(queryWrapper));
        return cashCouponAccountSumResponse;
    }
}
