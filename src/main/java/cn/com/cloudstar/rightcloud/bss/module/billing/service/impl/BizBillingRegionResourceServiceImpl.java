/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingCustomRegionResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingRegionResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ConfigRegionChargeRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.RegionConfigVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 区域资源类型配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
@Slf4j
public class BizBillingRegionResourceServiceImpl extends
    ServiceImpl<BizBillingRegionResourceMapper, BizBillingRegionResource> implements IBizBillingRegionResourceService {

    @Autowired
    private IBizBillingRegionChargeService bizBillingRegionChargeService;

    @Autowired
    private BizBillingStrategyAccountService bizBillingStrategyAccountService;

    @Autowired
    private BizBillingStrategyServingService strategyServingService;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private BizBillingServingConfigService bizBillingServingConfigService;

    @Autowired
    private IBizBillingCustomRegionChargeService bizBillingCustomRegionChargeService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private BizBillingCustomRegionResourceMapper bizBillingCustomRegionResourceMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configRegionCharge(ConfigRegionChargeRequest request) {
        BizBillingRegionResource regionResource = baseMapper.selectById(request.getId());
        if (Objects.isNull(regionResource)) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1348369781));
        }
        if (!regionResource.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        List<String> usingProductNames = this.checkUsingProductNames(regionResource.getResourceType());
        if (CollectionUtil.isNotEmpty(usingProductNames)) {
            log.info("该资源计费所属产品:[{}],正在被使用，请先下架产品！", usingProductNames.toString());
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2000872691));
        }
        // 保存对应最新的计费配置
        QueryWrapper<BizBillingRegionCharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingRegionCharge::getRegionResourceId, regionResource.getId());
        bizBillingRegionChargeService.remove(queryWrapper);

        List<RegionConfigVO> regionConfigs = request.getRegionConfigs();
        for (RegionConfigVO regionConfig : regionConfigs) {
            BizBillingRegionCharge regionCharge = new BizBillingRegionCharge();
            regionCharge.setRegionResourceId(regionResource.getId());
            regionCharge.setStrategyId(regionConfig.getStrategyId());
            regionCharge.setCategory(regionConfig.getCategory());
            regionCharge.setSpecType(regionConfig.getSpecType());
            regionCharge.setSpecConfig(JSON.toJSONString(regionConfig.getSpecs()));
            WebUserUtil.prepareInsertParams(regionCharge);
            bizBillingRegionChargeService.save(regionCharge);

            // 云账号与策略关联
            BizBillingStrategyAccount strategyAccount = new BizBillingStrategyAccount();
            strategyAccount.setAccountId(regionResource.getEnvAccountId());
            strategyAccount.setBillingStrategyId(regionConfig.getStrategyId());
            strategyAccount.setCategory(regionConfig.getCategory());
            WebUserUtil.prepareInsertParams(strategyAccount);
            bizBillingStrategyAccountService.save(strategyAccount);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configCustomRegionCharge(ConfigRegionChargeRequest request) {
        BizBillingCustomRegionResource regionResource = bizBillingCustomRegionResourceMapper.selectById(request.getId());
        if (Objects.isNull(regionResource)) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1348369781));
        }
        if (!regionResource.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        String userSId = request.getUserSid();
        User user = sysUserMapper.selectById(userSId);
        if (user == null) {
            throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1391388302));
        }

        // 保存对应最新的计费配置
        QueryWrapper<BizBillingCustomRegionCharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingCustomRegionCharge::getRegionResourceId, regionResource.getId());
        bizBillingCustomRegionChargeService.remove(queryWrapper);

        List<RegionConfigVO> regionConfigs = request.getRegionConfigs();
        Long orgSid = user.getOrgSid();
        for (RegionConfigVO regionConfig : regionConfigs) {
            BizBillingCustomRegionCharge regionCharge = new BizBillingCustomRegionCharge();
            regionCharge.setRegionResourceId(regionResource.getId());
            regionCharge.setStrategyId(regionConfig.getStrategyId());
            regionCharge.setCategory(regionConfig.getCategory());
            regionCharge.setSpecType(regionConfig.getSpecType());
            regionCharge.setOwnerId(userSId);
            regionCharge.setOrgSid(orgSid);
            regionCharge.setSpecConfig(JSON.toJSONString(regionConfig.getSpecs()));
            WebUserUtil.prepareInsertParams(regionCharge);
            bizBillingCustomRegionChargeService.save(regionCharge);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRegionResource(Long regionResourceId) {
        BizBillingRegionResource regionResource = this.getById(regionResourceId);
        if (regionResource == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1149954380));
        }
        if (!regionResource.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        QueryWrapper<ServiceCategory> serviceCategoryQueryWrapper = new QueryWrapper<>();
        serviceCategoryQueryWrapper.lambda()
                                   .eq(ServiceCategory::getServiceType, regionResource.getResourceType());
        ServiceCategory serviceCategory = serviceCategoryService.list(serviceCategoryQueryWrapper).get(0);
        if (!ObjectUtils.isEmpty(serviceCategory) && "using".equals(serviceCategory.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1543987437));
        }
        //删除区域资源类型
        this.removeById(regionResourceId);

        //删除区域资源类型对应的计费配置
        QueryWrapper<BizBillingRegionCharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingRegionCharge::getRegionResourceId, regionResourceId);
        List<BizBillingRegionCharge> regionCharges = bizBillingRegionChargeService.list(queryWrapper);
        List<Long> strategyIds = regionCharges.stream().map(BizBillingRegionCharge::getStrategyId)
            .collect(Collectors.toList());
        bizBillingRegionChargeService.remove(queryWrapper);

        // 清楚策略与云账号关联
        if (CollectionUtil.isNotEmpty(strategyIds)) {
            QueryWrapper<BizBillingStrategyAccount> strategyAccountQuery = new QueryWrapper<>();
            strategyAccountQuery.lambda().eq(BizBillingStrategyAccount::getAccountId, regionResource.getEnvAccountId())
                .in(BizBillingStrategyAccount::getBillingStrategyId, strategyIds);
            bizBillingStrategyAccountService.remove(strategyAccountQuery);
        }
    }

    @Override
    public List<BizBillingRegionResource> selectByParams(Wrapper<BizBillingRegionResource> queryWrapper) {
        return this.baseMapper.selectByParams(queryWrapper);
    }

    @Override
    public List<String> checkUsingProductNames(String resourceType) {
        // 检查资源定价是否被上架产品使用
        List<BizBillingStrategyServing> strategyServings = strategyServingService.list(
                new QueryWrapper<BizBillingStrategyServing>().lambda().eq(
                        BizBillingStrategyServing::getResourceType, resourceType));
        // 取得使用中的产品id
        List<Long> servingIds = CollectionUtil.isNotEmpty(strategyServings)
                ? strategyServings.stream().map(BizBillingStrategyServing::getServiceId).collect(Collectors.toList())
                : Lists.newArrayList();
        // 检查产品上架
        return getUsingProductNames(servingIds);
    }

    @Override
    public List<String> getUsingProductNames(List<Long> servingIds) {
        List<String> usingServingNames = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(servingIds)) {
            servingIds.forEach(servingId -> {
                ServiceCategory serviceCategory = serviceCategoryService.getById(servingId);
                if (Objects.nonNull(serviceCategory)) {
                    String productCode = bizBillingServingConfigService.checkServingUsingStatus(serviceCategory);
                    if (Strings.isNotBlank(productCode)) {
                        usingServingNames.add(
                                Strings.isNotBlank(ProductCodeEnum.keyFromDesc(productCode)) ? ProductCodeEnum.keyFromDesc(productCode) : serviceCategory.getServiceName());
                    }
                }
            });
        }
        return usingServingNames;
    }

}
