/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.CashCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DeductCashCouponRecordRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponAccountSumResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponGroupNameResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DeductCashCouponRecordResponse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CashCouponService extends IService<CashCoupon> {

    //创建现金券
    void createCashCoupon(CreateCashCouponRequest createCashCouponRequest);

    //分发现金券
    void distributeCashCoupon(DistributeCashCouponRequest distributeCashCouponRequest);

    //作废现金券
    void cancelCashCoupon(List<String> couponNos, String remark);

    //分页查询现金券
    IPage<CashCouponGroupNameResponse> pageCashCoupon(Integer pagenum, Integer pagesize, String type, String couponNo,
                                                      String status, String sortdatafield, String sortorder,
                                                      String couponName, String accountNameLike);

    //分页查询现金券详情分页列表
    IPage<CashCoupon> pageCashListInfo(Integer pagenum, Integer pagesize, String type, String couponNo,
                                       String couponName, String status, String sortdatafield, String sortorder, String accountNameLike);

    //处理过期现金券
    void overdueTask();

    // 查询账户
    IPage<BizBillingAccount> findBillingAccount(Integer pagenum, Integer pagesize, String accountName, String orgName,
                                                String mobile, String account);

    // 账户分页查询现金券
    IPage<CashCouponAccountResponse> accountListCashCoupon(Integer pagenum, Integer pagesize, String type,
                                                           String couponNo, String couponStatus, Long accountId,
                                                           String sortdatafield, String sortorder, boolean selfFlag);

    //统计账户共有多少现金券,已使用多少
    CashCouponAccountSumResponse accountCashSum();

    //统计账户共有多少抵扣现金券,已使用多少
    CashCouponAccountSumResponse accountDeductCashSum();

    /**
     * 校验现金券是否有效 并返回金额
     */
    RestResult check(String cardNO, String accountId);

    /**
     * 分页查询抵扣现金券抵扣记录
     */
    IPage<DeductCashCouponRecordResponse> deductRecordList(DeductCashCouponRecordRequest request);
}
