/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @ClassName: AiModelCollectorArchived.java
 * @Description： AiModel话单归档数据实体类
 * @Author: system
 * @Date: 2025/01/21
 * @Version: 1.0.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "hws_sdr_aimodel_data_archived")
public class AiModelCollectorArchived extends Collector {
}