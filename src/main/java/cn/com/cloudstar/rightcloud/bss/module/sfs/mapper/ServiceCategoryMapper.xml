<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper">




    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.service_id != null">
                and id = #{condition.service_id}
            </if>
            <if test="condition.serviceTypeList != null">
                and service_type in
                <foreach item="serviceType" index="index" collection="condition.serviceTypeList"
                    open="(" separator="," close=")">
                    #{serviceType}
                </foreach>
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.publish_status != null">
                and publish_status = #{condition.publish_status}
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_two">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.entity_id != null">
                and entity_id = #{condition.entity_id}
            </if>
            <if test="condition.serviceTypeList != null">
                and service_type in
                <foreach item="serviceType" index="index" collection="condition.serviceTypeList"
                         open="(" separator="," close=")">
                    #{serviceType}
                </foreach>
            </if>
        </trim>
    </sql>
    <select id="selectDistinctByParams" resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory">
        select service_type,product_name,status,ccsp_mac
        from sf_service_category
        where status != 'delete' and service_type is not null
        group by service_type
    </select>


    <select id="getServiceCategoryByServiceType" parameterType="map" resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory">
        select * from sf_service_category where service_type=#{serviceType};
    </select>

    <select id="selectServiceCategoryHPCByClusterId" parameterType="java.lang.String"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool">
        select service_id, service_type, cluster_id, cluster_name, entity_id, entity_name, hpc_version
        from sf_service_category_hpc_cluster_pool
        where cluster_id = #{clusterId}
    </select>

    <select id="getEntityByCategoryId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO">
        select ent.id as entity_id,ent.name as entity_name
        from sf_service_category cate
                 left join sys_bss_entity ent on cate.entity_id = ent.id
        where cate.id = #{categoryId}
    </select>

    <select id="getClusterPoolByServiceId"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool">
        select *
        from sf_service_category_hpc_cluster_pool
        where service_id = #{serviceId}
    </select>
    <select id="getServiceCategoryByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory">
        select *
        from sf_service_category
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <select id="selectServiceCategoryHPCByEntityId" parameterType="long"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool">
        select service_id, service_type, cluster_id, cluster_name, entity_id, entity_name, hpc_version
        from sf_service_category_hpc_cluster_pool
        where entity_id = #{entityId} order By hpc_version
    </select>

    <select id="selectByServiceType"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory">
        select   sc.id,
                 sc.service_form,
                 sc.service_type,
                 sc.service_class,
                 sc.service_name,
                 sc.service_desc,
                 sc.service_icon_path,
                 sc.service_config,
                 sc.service_workflow_id,
                 sc.status,
                 sc.created_by,
                 sc.created_dt,
                 sc.updated_by,
                 sc.updated_dt,
                 sc.entity_id,
                 sc.publish_status,
                 sc.entity_name
        from sf_service_category sc where sc.service_type=#{serviceType} order by sc.id limit 1
    </select>

    <select id="getByClusterIdAndServiceId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO">
        select *
        from sf_service_category_hpc_cluster_pool
        WHERE cluster_id = #{clusterId}
        <if test="serviceId != null">
            and service_id = #{serviceId}
        </if>
    </select>

    <select id="selectByPrimaryKey" resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory" parameterType="java.lang.Long">
        select *
        from sf_service_category
        where id = #{id}
    </select>

    <select id="getProductVersionByEntityId" resultType="java.lang.String">
        select service_type from sf_service_category
        where entity_id = #{entityId}
    </select>
    <select id="pageServiceCategory"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory">
        select s.* from sf_service_category s left join biz_tag_relation r on
        s.id = r.service_id left join biz_tag t on r.tag_id = t.id
        <where>
            <if test="condition.productIds != null and condition.productIds.size >0">
                and s.id in
                <foreach collection="condition.productIds" item="id" close=")" open="(" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.productNameLike != null and condition.productNameLike != ''">
                and s.product_name like concat('%',#{condition.productNameLike},'%')
            </if>
            <if test="condition.serviceComponent != null and condition.serviceComponent != ''">
                and s.service_component = #{condition.serviceComponent}
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and s.product_code = #{condition.productCode}
            </if>
            <if test="condition.status != null and condition.status != ''">
                and s.status = #{condition.status}
            </if>
            <if test="condition.neqStatus != null and condition.neqStatus != ''">
                and s.status != #{condition.neqStatus}
            </if>
            <if test="condition.publishStatus != null and condition.publishStatus != ''">
                and s.publish_status = #{condition.publishStatus}
            </if>
            <if test="condition.tagNameLike != null and condition.tagNameLike != ''">
                and t.tag_name like concat('%',#{condition.tagNameLike},'%')
            </if>
        </where>
        group by s.id
        order by IF(sort_number IS NULL, 1, 0),sort_number
    </select>
    <select id="getClusterPoolsByHpcVersion"
            resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool">
        select *
        from sf_service_category_hpc_cluster_pool
        where hpc_version = #{hpcVersion}
    </select>
    <delete id="deleteClusterPoolsByHpcVersion" parameterType="int">
        delete from sf_service_category_hpc_cluster_pool
        where hpc_version = #{hpcVersion}
    </delete>
    <insert id="insertBatchClusterPools" parameterType="list">
        insert into sf_service_category_hpc_cluster_pool (service_id, service_type,
        cluster_id, cluster_name, entity_id, entity_name, hpc_version)
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.serviceId}, #{item.serviceType}, #{item.clusterId}, #{item.clusterName},
            #{item.entityId}, #{item.entityName}, #{item.hpcVersion})
        </foreach>
    </insert>
    <select id="findServiceTypeByEntityId" resultType="java.lang.String">
        select distinct(service_type)
        from sf_service_category a
        where a.publish_status = 'succeed'
#         and a.editable = '1'
        and a.publish_dt is not NULL
        and a.entity_id = #{entityId}
    </select>
    <select id="findClusterId" resultType="java.lang.String" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select cluster_id
        from sf_service_category_hpc_cluster_pool
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_two"/>
        </if>
    </select>
    <select id="selectServiceCategoryByHPC"
        resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool">
        select service_id, service_type, cluster_id, cluster_name, entity_id, entity_name, hpc_version
        from sf_service_category_hpc_cluster_pool
    </select>

    <select id="getByServiceType" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO">
        select ent.id as entity_id,ent.name as entity_name, cate.id service_id
        from sf_service_category cate
        left join sys_bss_entity ent on cate.entity_id = ent.id
        where cate.service_type =#{serviceType}
    </select>

</mapper>
