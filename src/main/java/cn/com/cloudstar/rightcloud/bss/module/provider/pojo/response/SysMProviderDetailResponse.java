package cn.com.cloudstar.rightcloud.bss.module.provider.pojo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;

import javax.validation.constraints.NotNull;

import lombok.Data;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.DesensitizationField;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.annotation.EncryptDecryptClass;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;

/**
 * 供应商详情
 * <AUTHOR>
 * @date 2025/1/22
 */
@Data
public class SysMProviderDetailResponse {
    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 企业id(sys_m_org表主键id)
     */
    private Long companyId;

    /**
     * 所属用户id(sys_m_user表主键id)
     */
    private Long ownerId;

    /**
     * 状态（申请中/已入驻/申请失败/注销中/已注销）
     */
    private String status;

    /**
     * 审批信息
     */
    private String statusInfo;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 预留字段，目前仅支持中国大陆
     */
    private String region;

    /**
     * 法定代表人
     */
    @DesensitizationField(type = DesensitizedType.NAME)
    private String legalPerson;

    /**
     * 法定代表人身份证号码
     */
    @DesensitizationField(type = DesensitizedType.ID_CARD)
    private String legalPersonCard;

    /**
     * 电话
     */
    @DesensitizationField(type = DesensitizedType.PHONE)
    private String contactPhone;

    /**
     * 统一社会信用代码
     */
    private String socialCode;

    /**
     * 注册资金（万）
     */
    private String registeredCapital;

    /**
     * 注册地址
     */
    private String address;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 银行账号
     */
    private Long bankAccount;

    /**
     * 银行账户
     */
    private String bankAccountName;

    /**
     * 注册时间
     */
    private Date setUpDt;

    /**
     * 营业执照图片路径
     */
    private String businessLicenseUrl;


}
