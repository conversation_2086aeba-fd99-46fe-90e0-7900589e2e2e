/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants.BillingChargeType;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingTariffSpecChargeMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.CreateBizBillingTariffSpecChargeRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.SpecMonthVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.*;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created on 2019/10/23
 */
@Slf4j
@Service
public class BizBillingTariffSpecChargeServiceImpl
        extends ServiceImpl<BizBillingTariffSpecChargeMapper, BizBillingTariffSpecCharge>
        implements BizBillingTariffSpecChargeService {

    @Autowired
    @Lazy
    private BizBillingTariffSpecService tariffSpecService;

    @Autowired
    private BizBillingSpecService specService;

    private static final Pattern PATTERN = Pattern.compile("^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$");

    private static final Pattern PATTERNNAME = Pattern.compile("[a-zA-Z0-9\\u4e00-\\u9fa5.,，。！!()（）-]*$");

    @Autowired
    private IBizBillingSpecGroupService bizBillingSpecGroupService;

    @Autowired
    private IBizBillingChargeMonthService bizBillingChargeMonthService;

    @Autowired
    private IBizBillingRegionChargeService bizBillingRegionChargeService;

    @Autowired
    private IBizBillingRegionResourceService bizBillingRegionResourceService;

    @Autowired
    private BizBillingStrategyServingService strategyServingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBillingTariffSpecCharge(BizBillingTariffSpecCharge specCharge) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        BizBillingSpecGroup specGroup = bizBillingSpecGroupService.getById(specCharge.getSpecGroupId());
        if (Objects.isNull(specGroup)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1600874684));
        }
        if (BillingChargeType.STATIC_TYPE.equals(specGroup.getBillingMode())) {
            validateSpecChargeRepeat(Lists.newArrayList(specCharge), specGroup.getId(), specGroup.getSpec());
        }
        if (org.apache.commons.lang3.StringUtils.equals(specCharge.getSpecType(), "resourceUsage") || org.apache.commons.lang3.StringUtils.equals(specCharge.getSpecType(), "pflopsResourceUsage")) {
            LambdaQueryWrapper<BizBillingTariffSpecCharge> qw = new LambdaQueryWrapper<>();
            qw.eq(BizBillingTariffSpecCharge::getSpecGroupId, specCharge.getSpecGroupId());
            qw.eq(BizBillingTariffSpecCharge::getSpecType, specCharge.getSpecType());
            if (0 < this.count(qw)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2023301275));
            }
        }
        WebUserUtil.prepareInsertParams(specCharge);
        this.save(specCharge);

        //保存包年包月指定月数计费
        if (CollectionUtil.isNotEmpty(specCharge.getMonths())) {
            saveBatchChargeMonth(specCharge);
        }
        return specCharge.getId();
    }

    /**
     * 检查规格定价是否重复
     * @param target
     * @param specCharges
     * @param tariffSpec
     * @return
     */
    private boolean checkSpecChargeRepeated(
            BizBillingTariffSpecCharge target,
            List<BizBillingTariffSpecCharge> specCharges, BizBillingTariffSpec tariffSpec) {

        JSONObject billingConfig = JSON.parseObject(target.getBillingConfig());
        if (BillingConstants.BillingChargeType.INC_TYPE.equals(tariffSpec.getChargeStrategy())) {
            // 匹配计费区间
            List<String> specIdList = Arrays.asList(tariffSpec.getSpecIds().split(","));
            QueryWrapper<BizBillingSpec> specQueryWrapper = new QueryWrapper<>();
            specQueryWrapper.in("id", specIdList);
            List<BizBillingSpec> specs = specService.list(specQueryWrapper);
            List<BizBillingSpec> chargeSpecs = specs.stream()
                    .filter(BizBillingSpec::getIsCharge).collect(Collectors.toList());
            BizBillingSpec spec = chargeSpecs.get(0);
            String specValue = billingConfig.getString(spec.getSpecName());
            String group = billingConfig.getString("group");
            List<BizBillingTariffSpecCharge> groupedCharges = Lists.newArrayList();
            for (BizBillingTariffSpecCharge charge : specCharges) {
                JSONObject config = JSON.parseObject(charge.getBillingConfig());
                String groupValue = config.getString("group");
                if (!StringUtils.isEmpty(group)) {
                    if (group.equals(groupValue)) {
                        groupedCharges.add(charge);
                    }
                } else {
                    if (StringUtils.isEmpty(groupValue)) {
                        groupedCharges.add(charge);
                    }
                }
            }
            boolean ok = true;
            List<Double> valueRange = Arrays.stream(specValue.split("-"))
                    .map(Double::parseDouble).collect(Collectors.toList());
            for (BizBillingTariffSpecCharge charge : groupedCharges) {
                JSONObject config = JSON.parseObject(charge.getBillingConfig());
                String string = config.getString(spec.getSpecName());
                List<Double> oldValueRange = Arrays.stream(string.split("-"))
                        .map(Double::parseDouble).collect(Collectors.toList());
                if (valueRange.get(1) < oldValueRange.get(0) && valueRange.get(0) > oldValueRange.get(1)) {
                    ok = true;
                } else {
                    ok = false;
                }
            }
            if (!ok) {
                return true;
            }
        } else {
            // 匹配计费项
            for (BizBillingTariffSpecCharge charge : specCharges) {
                JSONObject config = JSON.parseObject(charge.getBillingConfig());
                Set<Map.Entry<String, Object>> entries = config.entrySet();
                boolean equal = true;
                for (Map.Entry<String, Object> entry : entries) {
                    if (!entry.getValue().equals(billingConfig.get(entry.getKey()))) {
                        equal = false;
                    }
                }
                if (equal) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBillingTariffSpecCharge(BizBillingTariffSpecCharge specCharge) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        BizBillingTariffSpecCharge dbSpecCharge = this.getById(specCharge.getId());
        if (Objects.isNull(dbSpecCharge)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_798962822));

        }
        BizBillingSpecGroup specGroup = bizBillingSpecGroupService.getById(specCharge.getSpecGroupId());
        if (Objects.isNull(specGroup)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1600874684));
        }
        if (BillingChargeType.STATIC_TYPE.equals(specGroup.getBillingMode())) {
            validateSpecChargeRepeat(Lists.newArrayList(specCharge), specGroup.getId(), specGroup.getSpec());
        }

        WebUserUtil.prepareUpdateParams(specCharge);
        this.updateById(specCharge);

        //保存包年包月指定月数计费
        if (CollectionUtil.isNotEmpty(specCharge.getMonths())) {
            QueryWrapper<BizBillingChargeMonth> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizBillingChargeMonth::getSpecChargeId, dbSpecCharge.getId());
            bizBillingChargeMonthService.remove(queryWrapper);
            saveBatchChargeMonth(specCharge);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchBillingTariffSpecCharge(Long specGroupId, List<CreateBizBillingTariffSpecChargeRequest> specCharges) {

        if (CollectionUtils.isEmpty(specCharges)) {
            return "";
        }

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        BizBillingSpecGroup specGroup = bizBillingSpecGroupService.getById(specGroupId);
        if (Objects.isNull(specGroup)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1600874684));
        }
        if (specGroup.getEntityId() != null && !specGroup.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1163490172));
        }

        // 计算资源策略验证重复规格
        if (BillingChargeType.STATIC_TYPE.equals(specGroup.getBillingMode())) {
            List<BizBillingTariffSpecCharge> newSpecCharges = BeanConvertUtil.convert(specCharges, BizBillingTariffSpecCharge.class);
            validateSpecChargeRepeat(newSpecCharges, specGroup.getId(), specGroup.getSpec());
        }
        List<Long> ids = new ArrayList<>();
        for (CreateBizBillingTariffSpecChargeRequest specChargeRequest : specCharges) {
            BizBillingSpecGroup specGroupRequest = bizBillingSpecGroupService.getById(specChargeRequest.getSpecGroupId());
            if (Objects.isNull(specGroupRequest)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1600874684));
            }
            BizBillingTariffSpecCharge specCharge = BeanConvertUtil.convert(specChargeRequest, BizBillingTariffSpecCharge.class);
            WebUserUtil.prepareInsertParams(specCharge);
            specCharge.setSpecGroupId(specChargeRequest.getSpecGroupId());
            specCharge.setSpecGroupName(specChargeRequest.getSpecGroupName());
            this.save(specCharge);
            //保存包年包月指定月数计费
            if (CollectionUtil.isNotEmpty(specCharge.getMonths())) {
                saveBatchChargeMonth(specCharge);
            }
            ids.add(specCharge.getId());
        }
        return org.apache.commons.lang3.StringUtils.join(ids,",");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBillingTariffSpecCharge(Long id) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1928367842));
        }

        QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
        chargeQueryWrapper.eq("id", id);
        List<BizBillingTariffSpecCharge> tariffSpecCharges = this.list(chargeQueryWrapper);
        if (CollectionUtils.isEmpty(tariffSpecCharges)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_60741981));
        }
        Set<Long> groupIds = tariffSpecCharges.stream().map(BizBillingTariffSpecCharge::getSpecGroupId).collect(Collectors.toSet());
        List<BizBillingSpecGroup> groups = bizBillingSpecGroupService.listByIds(groupIds);
        if (CollectionUtils.isEmpty(groups)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        if (groups.stream().noneMatch(e -> Objects.equals(e.getEntityId(), authUserInfo.getEntityId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2036580347));
        }

        this.removeById(id);

        //删除除指定月份计费
        QueryWrapper<BizBillingChargeMonth> monthQueryWrapper = new QueryWrapper<>();
        monthQueryWrapper.lambda().eq(BizBillingChargeMonth::getSpecChargeId, id);
        bizBillingChargeMonthService.remove(monthQueryWrapper);
    }


    @Override
    public void validateSpecChargeRepeat(List<BizBillingTariffSpecCharge> newSpecCharges, Long specGroupId, String specType) {
        // 先判断本身是否存在重复
        Map<String, Long> countMap = newSpecCharges.stream().collect(
            Collectors.groupingBy(BizBillingTariffSpecCharge::getBillingConfig, Collectors.counting()));

        newSpecCharges.forEach(charge -> {
            if (countMap.getOrDefault(charge.getBillingConfig(), 1L) > 1) {

                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_723028075));
            }
        });
        //判断金额，只能保留2位小数
        newSpecCharges.forEach(charge -> {
            charge.setMonthPrice(charge.getMonthPrice() == null ? BigDecimal.ZERO: charge.getMonthPrice());
            if (!PATTERN.matcher(charge.getMonthPrice().toString()).matches()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_239235683));
            }
            if (!PATTERNNAME.matcher(charge.getSpecGroupName()).matches()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_162700400));
            }
        });
        QueryWrapper<BizBillingTariffSpecCharge> chargeQueryWrapper = new QueryWrapper<>();
        chargeQueryWrapper.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
        List<BizBillingTariffSpecCharge> existSpecCharges = this.list(chargeQueryWrapper);
        List<BizBillingTariffSpecCharge> updateRecords = newSpecCharges.stream().filter(charge -> charge.getId() != null).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateRecords)) {
            existSpecCharges = existSpecCharges.stream().filter(charge -> !Objects.equals(charge.getId(), updateRecords.get(0).getId())).collect(
                Collectors.toList());
        }
        Set<String> existSet = existSpecCharges.stream().map(BizBillingTariffSpecCharge::getBillingConfig)
            .collect(Collectors.toSet());

        Set<String> newSet = newSpecCharges.stream().map(BizBillingTariffSpecCharge::getBillingConfig)
            .collect(Collectors.toSet());

        for (String str : newSet) {
            if (existSet.contains(str)) {

                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_723028075));
            }
        }
    }

    /**
     * 保存指定月数的费用配置
     * @param specCharge
     */
    private void saveBatchChargeMonth(BizBillingTariffSpecCharge specCharge) {
        List<SpecMonthVO> specMonths = specCharge.getMonths();
        List<BizBillingChargeMonth> chargeMonths = Lists.newArrayListWithExpectedSize(specMonths.size());
        specMonths.forEach(specMonthVO -> {
            BizBillingChargeMonth chargeMonth = BeanConvertUtil.convert(specMonthVO, BizBillingChargeMonth.class);
            chargeMonth.setSpecChargeId(specCharge.getId());
            WebUserUtil.prepareInsertParams(chargeMonth);
            chargeMonths.add(chargeMonth);
        });
        bizBillingChargeMonthService.saveBatch(chargeMonths);
    }

    @Override
    public Set<String> checkUsingProduceNames(Set<Long> specGroupIds) {
        Set<String> usingProductNames = Sets.newHashSet();
        specGroupIds.forEach(specGroupId -> {
            // 检查使用该规格簇的资源计费
            List<BizBillingRegionCharge> billingRegionCharges = bizBillingRegionChargeService.list(
                    new QueryWrapper<BizBillingRegionCharge>().lambda().like(
                            BizBillingRegionCharge::getSpecConfig, "\"specGroupId\":" + specGroupId + ","));
            Set<Long> resourceIds = billingRegionCharges.stream().map(BizBillingRegionCharge::getRegionResourceId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(resourceIds)) {
                // 获取资源计费resource type
                List<BizBillingRegionResource> bizBillingRegionResources = bizBillingRegionResourceService.list(
                        new QueryWrapper<BizBillingRegionResource>().lambda().in(BizBillingRegionResource::getId, resourceIds));
                Set<String> resourceTypes = CollectionUtil.isNotEmpty(bizBillingRegionResources)
                        ? bizBillingRegionResources.stream().map(BizBillingRegionResource::getResourceType).collect(Collectors.toSet())
                        : Sets.newHashSet();
                resourceTypes.forEach(resourceType -> {
                    usingProductNames.addAll(bizBillingRegionResourceService.checkUsingProductNames(resourceType));
                });
            }
        });
        return usingProductNames;
    }

    @Override
    public Set<String> checkServerUsingProduceNames(Set<Long> specIds) {
        Set<String> usingProductNames = Sets.newHashSet();
        List<Long> servingIds = Lists.newArrayList();
        specIds.stream().forEach(specId -> {
            List<BizBillingStrategyServing> strategyServings = strategyServingService.list(
                    new QueryWrapper<BizBillingStrategyServing>().lambda().likeLeft(
                            BizBillingStrategyServing::getSpecChargeIds, specId).or()
                            .likeRight(BizBillingStrategyServing::getSpecChargeIds, specId).or()
                            .like(BizBillingStrategyServing::getSpecChargeIds, "%," + specId + ",%"));
            servingIds.addAll(strategyServings.stream().map(BizBillingStrategyServing::getServiceId).collect(Collectors.toList()));
        });
        usingProductNames.addAll(bizBillingRegionResourceService.getUsingProductNames(servingIds));
        return usingProductNames;
    }
}
