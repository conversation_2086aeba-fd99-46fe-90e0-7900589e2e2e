<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.module.access.bean.User">
        <id column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="user_code" property="userCode" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="sex" property="sex" jdbcType="INTEGER"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="project_id" property="projectId" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="error_count" property="errorCount" jdbcType="INTEGER"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="service_limit_quantity" property="serviceLimitQuantity" jdbcType="INTEGER"/>
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR"/>
        <result column="sms_max" property="smsMax" jdbcType="INTEGER"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="skin_theme" property="skinTheme" jdbcType="VARCHAR"/>
        <result column="auth_id" property="authId" jdbcType="VARCHAR"/>
        <result column="auth_type" property="authType" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="wechat_name" property="wechatName" jdbcType="VARCHAR"/>
        <result column="domain_id" property="domainId" jdbcType="VARCHAR"/>
        <result column="iam_id" property="iamId" jdbcType="VARCHAR"/>
        <result column="password_expires_at" property="passwordExpiresAt" jdbcType="VARCHAR"/>
        <result column="forceResetPwd" property="forceresetpwd" jdbcType="BIT"/>
        <result column="default_project_id" property="defaultProjectId" jdbcType="VARCHAR"/>
        <result column="last_project_id" property="lastProjectId" jdbcType="VARCHAR"/>
        <result column="pwd_strength" property="pwdStrength" jdbcType="VARCHAR"/>
        <result column="parent_sid" property="parentSid" jdbcType="BIGINT"/>
        <result column="certification_status" property="certificationStatus" jdbcType="VARCHAR"/>
        <result column="freeze_status" property="freezeStatus" jdbcType="VARCHAR"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="policy_agree_sign" property="policyAgreeSign" jdbcType="INTEGER"/>
        <result column="business_tag" property="businessTag" jdbcType="VARCHAR"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="policy_agree_time" property="policyAgreeTime" jdbcType="TIMESTAMP"/>
        <result column="id_card_front" property="idCardFront" jdbcType="VARCHAR"/>
        <result column="id_card_reverse" property="idCardReverse" jdbcType="VARCHAR"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.userType != null">
                and user_type = #{condition.userType}
            </if>
            <if test="condition.userCode != null">
                and user_code = #{condition.userCode}
            </if>
            <if test="condition.account != null">
                and account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and real_name = #{condition.realName}
            </if>
            <if test="condition.sex != null">
                and sex = #{condition.sex}
            </if>
            <if test="condition.email != null">
                and email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and mobile = #{condition.mobile}
            </if>
            <if test="condition.title != null">
                and title = #{condition.title}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId}
            </if>
            <if test="condition.projectId != null">
                and project_id = #{condition.projectId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.remark != null">
                and remark = #{condition.remark}
            </if>
            <if test="condition.errorCount != null">
                and error_count = #{condition.errorCount}
            </if>
            <if test="condition.lastLoginTime != null">
                and last_login_time = #{condition.lastLoginTime}
            </if>
            <if test="condition.lastLoginIp != null">
                and last_login_ip = #{condition.lastLoginIp}
            </if>
            <if test="condition.startTime != null">
                and start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and end_time = #{condition.endTime}
            </if>
            <if test="condition.serviceLimitQuantity != null">
                and service_limit_quantity = #{condition.serviceLimitQuantity}
            </if>
            <if test="condition.applyReason != null">
                and apply_reason = #{condition.applyReason}
            </if>
            <if test="condition.smsMax != null">
                and sms_max = #{condition.smsMax}
            </if>
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.skinTheme != null">
                and skin_theme = #{condition.skinTheme}
            </if>
            <if test="condition.authId != null">
                and auth_id = #{condition.authId}
            </if>
            <if test="condition.authType != null">
                and auth_type = #{condition.authType}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.openId != null">
                and open_id = #{condition.openId}
            </if>
            <if test="condition.avatarUrl != null">
                and avatar_url = #{condition.avatarUrl}
            </if>
            <if test="condition.province != null">
                and province = #{condition.province}
            </if>
            <if test="condition.city != null">
                and city = #{condition.city}
            </if>
            <if test="condition.country != null">
                and country = #{condition.country}
            </if>
            <if test="condition.wechatName != null">
                and wechat_name = #{condition.wechatName}
            </if>
            <if test="condition.domainId != null">
                and domain_id = #{condition.domainId}
            </if>
            <if test="condition.iamId != null">
                and iam_id = #{condition.iamId}
            </if>
            <if test="condition.passwordExpiresAt != null">
                and password_expires_at = #{condition.passwordExpiresAt}
            </if>
            <if test="condition.forceresetpwd != null">
                and forceResetPwd = #{condition.forceresetpwd}
            </if>
            <if test="condition.defaultProjectId != null">
                and default_project_id = #{condition.defaultProjectId}
            </if>
            <if test="condition.lastProjectId != null">
                and last_project_id = #{condition.lastProjectId}
            </if>
            <if test="condition.pwdStrength != null">
                and pwd_strength = #{condition.pwdStrength}
            </if>
            <if test="condition.parentSid != null">
                and parent_sid = #{condition.parentSid}
            </if>
            <if test="condition.accountLike != null">
                and account like concat('%', #{condition.accountLike},'%')
            </if>
            <if test="condition.realNameLike != null">
                and realName like concat('%', #{condition.realNameLike},'%')
            </if>
            <if test="condition.mobileLike != null">
                and mobile like concat('%', #{condition.mobileLike},'%')
            </if>
            <if test="condition.emailLike != null">
                and email like concat('%', #{condition.emailLike},'%')
            </if>
            <if test="condition.certificationStatus != null">
                and certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.accountNot != null">
                and account != #{condition.accountNot}
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_1">
        <trim prefix="where" prefixOverrides="and|or">
            and A.status != 8
            <if test="condition.userType != null">
                and A.user_type = #{condition.userType}
            </if>
            <if test="condition.userCode != null">
                and A.user_code = #{condition.userCode}
            </if>
            <if test="condition.account != null">
                and A.account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and A.password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and A.real_name = #{condition.realName}
            </if>
            <if test="condition.sex != null">
                and A.sex = #{condition.sex}
            </if>
            <if test="condition.email != null">
                and A.email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and A.mobile = #{condition.mobile}
            </if>
            <if test="condition.title != null">
                and A.title = #{condition.title}
            </if>
            <if test="condition.companyId != null">
                and A.company_id = #{condition.companyId}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.remark != null">
                and A.remark = #{condition.remark}
            </if>
            <if test="condition.errorCount != null">
                and A.error_count = #{condition.errorCount}
            </if>
            <if test="condition.lastLoginTime != null">
                and A.last_login_time = #{condition.lastLoginTime}
            </if>
            <if test="condition.lastLoginIp != null">
                and A.last_login_ip = #{condition.lastLoginIp}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
            <if test="condition.serviceLimitQuantity != null">
                and A.service_limit_quantity = #{condition.serviceLimitQuantity}
            </if>
            <if test="condition.applyReason != null">
                and A.apply_reason = #{condition.applyReason}
            </if>
            <if test="condition.smsMax != null">
                and A.sms_max = #{condition.smsMax}
            </if>
            <if test="condition.uuid != null">
                and A.uuid = #{condition.uuid}
            </if>
            <if test="condition.skinTheme != null">
                and A.skin_theme = #{condition.skinTheme}
            </if>
            <if test="condition.authId != null">
                and A.auth_id = #{condition.authId}
            </if>
            <if test="condition.authType != null">
                and A.auth_type = #{condition.authType}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.openId != null">
                and A.open_id = #{condition.openId}
            </if>
            <if test="condition.avatarUrl != null">
                and A.avatar_url = #{condition.avatarUrl}
            </if>
            <if test="condition.province != null">
                and A.province = #{condition.province}
            </if>
            <if test="condition.city != null">
                and A.city = #{condition.city}
            </if>
            <if test="condition.country != null">
                and A.country = #{condition.country}
            </if>
            <if test="condition.wechatName != null">
                and A.wechat_name = #{condition.wechatName}
            </if>
            <if test="condition.domainId != null">
                and A.domain_id = #{condition.domainId}
            </if>
            <if test="condition.iamId != null">
                and A.iam_id = #{condition.iamId}
            </if>
            <if test="condition.passwordExpiresAt != null">
                and A.password_expires_at = #{condition.passwordExpiresAt}
            </if>
            <if test="condition.forceresetpwd != null">
                and A.forceResetPwd = #{condition.forceresetpwd}
            </if>
            <if test="condition.defaultProjectId != null">
                and A.default_project_id = #{condition.defaultProjectId}
            </if>
            <if test="condition.lastProjectId != null">
                and A.last_project_id = #{condition.lastProjectId}
            </if>
            <if test="condition.pwdStrength != null">
                and A.pwd_strength = #{condition.pwdStrength}
            </if>
            <if test="condition.parentSid != null">
                and A.parent_sid = #{condition.parentSid}
            </if>
            <if test="condition.accountLike != null">
                and A.account like concat('%', #{condition.accountLike},'%')
            </if>
            <if test="condition.realNameLike != null">
                and A.real_name like concat('%', #{condition.realNameLike},'%')
            </if>
            <if test="condition.realNameHash != null">
                and A.real_name_hash = #{condition.realNameHash}
            </if>
            <if test="condition.mobileLike != null">
                and A.mobile like concat('%', #{condition.mobileLike},'%')
            </if>
            <if test="condition.mobileHash != null">
                and A.mobile_hash = #{condition.mobileHash}
            </if>
            <if test="condition.emailLike != null">
                and A.email like concat('%', #{condition.emailLike},'%')
            </if>
            <if test="condition.emailHash != null">
                and A.email_hash = #{condition.emailHash}
            </if>
            <if test="condition.groupSid != null">
                and B.group_sid = #{condition.groupSid}
            </if>
            <if test="condition.parentSidNotNull != null">
                and A.parent_sid is not null
            </if>
            <if test="condition.userSidIn != null">
                AND A.user_sid in
                <foreach item="item" index="index" collection="condition.userSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.parentSidSidIn != null">
                AND A.parent_sid in
                <foreach item="item" index="index" collection="condition.parentSidSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.projectId != null">
                and B.org_sid = #{condition.projectId}
            </if>

            <if test="condition.policySid != null">
                and P.policy_sid = #{condition.policySid}
            </if>
            <if test="condition.orgSids != null">
                and A.org_sid in
                <foreach item="item" index="index" collection="condition.orgSids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


        </trim>
    </sql>
    <sql id="Example_Where_Clause_2">
        <trim prefix="where" prefixOverrides="and|or">
            and A.status != 8
            <if test="condition.userType != null">
                and A.user_type = #{condition.userType}
            </if>
            <if test="condition.userCode != null">
                and A.user_code = #{condition.userCode}
            </if>
            <if test="condition.userSid != null">
                and A.user_sid = #{condition.userSid}
            </if>
            <if test="condition.account != null">
                and A.account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and A.password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and A.real_name = #{condition.realName}
            </if>
            <if test="condition.sex != null">
                and A.sex = #{condition.sex}
            </if>
            <if test="condition.email != null">
                and A.email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and A.mobile = #{condition.mobile}
            </if>
            <if test="condition.title != null">
                and A.title = #{condition.title}
            </if>
            <if test="condition.companyId != null">
                and A.company_id = #{condition.companyId}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.remark != null">
                and A.remark = #{condition.remark}
            </if>
            <if test="condition.errorCount != null">
                and A.error_count = #{condition.errorCount}
            </if>
            <if test="condition.lastLoginTime != null">
                and A.last_login_time = #{condition.lastLoginTime}
            </if>
            <if test="condition.lastLoginIp != null">
                and A.last_login_ip = #{condition.lastLoginIp}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
            <if test="condition.serviceLimitQuantity != null">
                and A.service_limit_quantity = #{condition.serviceLimitQuantity}
            </if>
            <if test="condition.applyReason != null">
                and A.apply_reason = #{condition.applyReason}
            </if>
            <if test="condition.smsMax != null">
                and A.sms_max = #{condition.smsMax}
            </if>
            <if test="condition.uuid != null">
                and A.uuid = #{condition.uuid}
            </if>
            <if test="condition.skinTheme != null">
                and A.skin_theme = #{condition.skinTheme}
            </if>
            <if test="condition.authId != null">
                and A.auth_id = #{condition.authId}
            </if>
            <if test="condition.authType != null">
                and A.auth_type = #{condition.authType}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.openId != null">
                and A.open_id = #{condition.openId}
            </if>
            <if test="condition.avatarUrl != null">
                and A.avatar_url = #{condition.avatarUrl}
            </if>
            <if test="condition.province != null">
                and A.province = #{condition.province}
            </if>
            <if test="condition.city != null">
                and A.city = #{condition.city}
            </if>
            <if test="condition.country != null">
                and A.country = #{condition.country}
            </if>
            <if test="condition.wechatName != null">
                and A.wechat_name = #{condition.wechatName}
            </if>
            <if test="condition.domainId != null">
                and A.domain_id = #{condition.domainId}
            </if>
            <if test="condition.iamId != null">
                and A.iam_id = #{condition.iamId}
            </if>
            <if test="condition.passwordExpiresAt != null">
                and A.password_expires_at = #{condition.passwordExpiresAt}
            </if>
            <if test="condition.forceresetpwd != null">
                and A.forceResetPwd = #{condition.forceresetpwd}
            </if>
            <if test="condition.defaultProjectId != null">
                and A.default_project_id = #{condition.defaultProjectId}
            </if>
            <if test="condition.lastProjectId != null">
                and A.last_project_id = #{condition.lastProjectId}
            </if>
            <if test="condition.pwdStrength != null">
                and A.pwd_strength = #{condition.pwdStrength}
            </if>
            <if test="condition.parentSid != null">
                and A.parent_sid = #{condition.parentSid}
            </if>
            <if test="condition.accountLike != null">
                and A.account like concat('%', #{condition.accountLike},'%')
            </if>
            <if test="condition.realNameLike != null">
                and A.real_name like concat('%', #{condition.realNameLike},'%')
            </if>
            <if test="condition.realNameHash != null">
                and A.real_name_hash = #{condition.realNameHash}
            </if>
            <if test="condition.mobileLike != null">
                and A.mobile like concat('%', #{condition.mobileLike},'%')
            </if>
            <if test="condition.mobileHash != null">
                and A.mobile_hash = #{condition.mobileHash}
            </if>
            <if test="condition.emailLike != null">
                and A.email like concat('%', #{condition.emailLike},'%')
            </if>
            <if test="condition.emailHash != null">
                and A.email_hash =  #{condition.emailHash}
            </if>
            <if test="condition.groupSid != null">
                and B.group_sid = #{condition.groupSid}
            </if>
            <if test="condition.parentSidNotNull != null">
                and A.parent_sid is not null
            </if>
            <if test="condition.userSidIn != null">
                AND A.user_sid in
                <foreach item="item" index="index" collection="condition.userSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.parentSidSidIn != null">
                AND A.parent_sid in
                <foreach item="item" index="index" collection="condition.parentSidSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.projectId != null">
                and B.org_sid = #{condition.projectId}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
    user_sid, user_type, user_code, account, real_name, sex, email,
    mobile, title, company_id, project_id, org_sid, status, remark, error_count, last_login_time,
    last_login_ip, start_time, end_time, service_limit_quantity, apply_reason, sms_max,
    uuid, skin_theme, auth_id, auth_type, created_by, created_dt, updated_by, updated_dt,
    version, open_id, avatar_url, province, city, country, wechat_name, domain_id, iam_id,policy_agree_sign,policy_agree_time,
    password_expires_at, forceResetPwd, default_project_id, last_project_id, pwd_strength, parent_sid,certification_status,freeze_status,unfreeze_type,business_tag,
    password,real_name,email,mobile,auth_id,province,city,country,id_card_front,id_card_reverse,ccsp_mac
  </sql>

    <sql id="Base_Column_List_1">
    A.user_sid, A.user_type, A.user_code, A.account, A.password,  A.real_name, A.sex, A.email,
    A.mobile, A.title, A.company_id, A.project_id, A.org_sid, A.status, A.remark, A.error_count, A.last_login_time,
    A.last_login_ip, A.start_time, A.end_time, A.service_limit_quantity, A.apply_reason, A.sms_max,
    A.uuid, A.skin_theme, A.auth_id, A.auth_type, A.created_by, A.created_dt, A.updated_by, A.updated_dt,
    A.version, A.open_id, A.avatar_url, A.province, A.city, A.country, A.wechat_name, A.domain_id, iam_id,
    A.password_expires_at, A.forceResetPwd, A.default_project_id, A.last_project_id, A.pwd_strength, A.parent_sid, A.certification_status,
    A.id_card_front,A.id_card_reverse,A.ccsp_mac
  </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        distinct
        <include refid="Base_Column_List_1"/>
        ,C.role_sid
        ,D.role_name
        from sys_m_user A
        left join sys_m_user_group B on A.user_sid = B.user_sid
        left join sys_m_policy_user P on A.user_sid = P.user_sid
        left join sys_m_user_role C on A.user_sid = C.user_sid
        left join sys_m_role D on C.role_sid = D.role_sid
        left join sys_m_org smo on smo.org_sid = A.org_sid
        <if test="condition.inProjectId != null and !condition.inProjectId">
            inner join sys_m_user_org C on A.user_sid = c.user_sid
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_1"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByParam" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        distinct
        <include refid="Base_Column_List_1"/>
        ,C.role_sid
        ,D.role_name
        from sys_m_user A
        left join sys_m_user_group B on A.user_sid = B.user_sid
        left join sys_m_policy_user P on A.user_sid = P.user_sid
        left join sys_m_user_role C on A.user_sid = C.user_sid
        left join sys_m_role D on C.role_sid = D.role_sid
        left join sys_m_org smo on smo.org_sid = A.org_sid
        <if test="condition.inProjectId != null and !condition.inProjectId">
            inner join sys_m_user_org C on A.user_sid = c.user_sid
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_2"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="pageSize != null">
            limit
            <if test="condition.startNum != null">
                #{condition.startNum},
            </if>
            #{pageSize}
        </if>
    </select>

    <select id="countUser" resultType="int" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
            count(distinct a.user_sid)
        from sys_m_user A
        left join sys_m_user_group B on A.user_sid = B.user_sid
        left join sys_m_policy_user P on A.user_sid = P.user_sid
        left join sys_m_user_role C on A.user_sid = C.user_sid
        left join sys_m_role D on C.role_sid = D.role_sid
        left join sys_m_org smo on smo.org_sid = A.org_sid
        <if test="condition.inProjectId != null and !condition.inProjectId">
            inner join sys_m_user_org C on A.user_sid = c.user_sid
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_1"/>
        </if>
    </select>






    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where user_sid = #{userSid} and status!=8
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_m_user
    where user_sid = #{userSid}
  </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        delete from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.User">
    insert into sys_m_user (user_sid, user_type, user_code, account, password,
      real_name, sex, email, mobile, title, company_id, project_id,
      org_sid, status, remark, error_count, last_login_time, last_login_ip,
      start_time, end_time, service_limit_quantity, apply_reason, sms_max, uuid,
      skin_theme, auth_id, auth_type, created_by, created_dt, updated_by,
      updated_dt, version, open_id, avatar_url, province, city, country,
      wechat_name, domain_id, iam_id, password_expires_at, forceResetPwd, default_project_id,
      last_project_id, pwd_strength,ccsp_mac,real_name_hash,mobile_hash,email_hash)
    values (#{userSid}, #{userType}, #{userCode}, #{account}, #{password},
      #{realName}, #{sex}, #{email}, #{mobile}, #{title}, #{companyId}, #{projectId},
      #{orgSid}, #{status}, #{remark}, #{errorCount}, #{lastLoginTime}, #{lastLoginIp},
      #{startTime}, #{endTime}, #{serviceLimitQuantity}, #{applyReason}, #{smsMax}, #{uuid},
      #{skinTheme}, #{authId}, #{authType}, #{createdBy}, #{createdDt}, #{updatedBy},
      #{updatedDt}, #{version}, #{openId}, #{avatarUrl}, #{province}, #{city}, #{country},
      #{wechatName}, #{domainId}, #{iamId}, #{passwordExpiresAt}, #{forceresetpwd}, #{defaultProjectId},
      #{lastProjectId}, #{pwdStrength},#{ccspMac},#{realNameHash},#{mobileHash},#{emailHash})
  </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.User"
            useGeneratedKeys="true" keyProperty="userSid">
        insert into sys_m_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userSid != null">
                user_sid,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="userCode != null">
                user_code,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="realName != null">
                real_name,
            </if>
            <if test="realNameHash != null">
                real_name_hash,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="emailHash != null">
                email_hash,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="mobileHash != null">
                mobile_hash,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="errorCount != null">
                error_count,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="lastLoginIp != null">
                last_login_ip,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="serviceLimitQuantity != null">
                service_limit_quantity,
            </if>
            <if test="applyReason != null">
                apply_reason,
            </if>
            <if test="smsMax != null">
                sms_max,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="skinTheme != null">
                skin_theme,
            </if>
            <if test="authId != null">
                auth_id,
            </if>
            <if test="authType != null">
                auth_type,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="avatarUrl != null">
                avatar_url,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="wechatName != null">
                wechat_name,
            </if>
            <if test="domainId != null">
                domain_id,
            </if>
            <if test="iamId != null">
                iam_id,
            </if>
            <if test="passwordExpiresAt != null">
                password_expires_at,
            </if>
            <if test="forceresetpwd != null">
                forceResetPwd,
            </if>
            <if test="defaultProjectId != null">
                default_project_id,
            </if>
            <if test="lastProjectId != null">
                last_project_id,
            </if>
            <if test="pwdStrength != null">
                pwd_strength,
            </if>
            <if test="parentSid != null">
                parent_sid,
            </if>
            <if test="freezeStatus != null">
                freeze_status,
            </if>
            <if test="certificationStatus != null">
                certification_status,
            </if>
            <if test="unfreezeType != null">
                unfreeze_type,
            </if>
            <if test="pwdEndTime !=null">
                pwd_end_time,
            </if>
            <if test="businessTag != null">
                business_tag,
            </if>
            <if test="ccspMac != null">
                ccsp_mac,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userSid != null">
                #{userSid},
            </if>
            <if test="userType != null">
                #{userType},
            </if>
            <if test="userCode != null">
                #{userCode},
            </if>
            <if test="account != null">
                #{account},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="realName != null">
                #{realName},
            </if>
            <if test="realNameHash != null">
                #{realNameHash},
            </if>
            <if test="sex != null">
                #{sex},
            </if>
            <if test="email != null">
                #{email},
            </if>
            <if test="emailHash != null">
                #{emailHash},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="mobileHash != null">
                #{mobileHash},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="companyId != null">
                #{companyId},
            </if>
            <if test="projectId != null">
                #{projectId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="errorCount != null">
                #{errorCount},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime},
            </if>
            <if test="lastLoginIp != null">
                #{lastLoginIp},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="serviceLimitQuantity != null">
                #{serviceLimitQuantity},
            </if>
            <if test="applyReason != null">
                #{applyReason},
            </if>
            <if test="smsMax != null">
                #{smsMax},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="skinTheme != null">
                #{skinTheme},
            </if>
            <if test="authId != null">
                #{authId},
            </if>
            <if test="authType != null">
                #{authType},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="openId != null">
                #{openId},
            </if>
            <if test="avatarUrl != null">
                #{avatarUrl},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="country != null">
                #{country},
            </if>
            <if test="wechatName != null">
                #{wechatName},
            </if>
            <if test="domainId != null">
                #{domainId},
            </if>
            <if test="iamId != null">
                #{iamId},
            </if>
            <if test="passwordExpiresAt != null">
                #{passwordExpiresAt},
            </if>
            <if test="forceresetpwd != null">
                #{forceresetpwd},
            </if>
            <if test="defaultProjectId != null">
                #{defaultProjectId},
            </if>
            <if test="lastProjectId != null">
                #{lastProjectId},
            </if>
            <if test="pwdStrength != null">
                #{pwdStrength},
            </if>
            <if test="parentSid != null">
                #{parentSid},
            </if>
            <if test="freezeStatus != null">
                #{freezeStatus},
            </if>
            <if test="certificationStatus != null">
                #{certificationStatus},
            </if>
            <if test="unfreezeType != null">
                #{unfreezeType},
            </if>
            <if test="pwdEndTime != null">
                #{pwdEndTime},
            </if>
            <if test="businessTag != null">
                #{businessTag},
            </if>
            <if test="ccspMac != null">
                #{ccspMac},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="java.lang.Integer">
        select count(*) from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.User">
        update sys_m_user
        <set>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="userCode != null">
                user_code = #{userCode},
            </if>
            <if test="account != null">
                account = #{account},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>

            <if test="realName != null">
                real_name = #{realName},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="mobile != null">
                mobile = #{mobile},
            </if>

            <if test="realNameHash != null">
                real_name_hash = #{realNameHash},
            </if>
            <if test="emailHash != null">
                email_hash = #{emailHash},
            </if>
            <if test="mobileHash != null">
                mobile_hash = #{mobileHash},
            </if>

            <if test="title != null">
                title = #{title},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="errorCount != null">
                error_count = #{errorCount},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime},
            </if>
            <if test="lastLoginIp != null">
                last_login_ip = #{lastLoginIp},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="serviceLimitQuantity != null">
                service_limit_quantity = #{serviceLimitQuantity},
            </if>
            <if test="applyReason != null">
                apply_reason = #{applyReason},
            </if>
            <if test="smsMax != null">
                sms_max = #{smsMax},
            </if>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="skinTheme != null">
                skin_theme = #{skinTheme},
            </if>
            <if test="authId != null">
                auth_id = #{authId},
            </if>
            <if test="authType != null">
                auth_type = #{authType},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="openId != null">
                open_id = #{openId},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="country != null">
                country = #{country},
            </if>
            <if test="wechatName != null">
                wechat_name = #{wechatName},
            </if>
            <if test="domainId != null">
                domain_id = #{domainId},
            </if>
            <if test="iamId != null">
                iam_id = #{iamId},
            </if>
            <if test="passwordExpiresAt != null">
                password_expires_at = #{passwordExpiresAt},
            </if>
            <if test="forceresetpwd != null">
                forceResetPwd = #{forceresetpwd},
            </if>
            <if test="defaultProjectId != null">
                default_project_id = #{defaultProjectId},
            </if>
            <if test="lastProjectId != null">
                last_project_id = #{lastProjectId},
            </if>
            <if test="pwdStrength != null">
                pwd_strength = #{pwdStrength},
            </if>
            <if test="policyAgreeSign != null">
                policy_agree_sign = #{policyAgreeSign},
            </if>
            <if test="policyAgreeTime != null">
                policy_agree_time = #{policyAgreeTime},
            </if>
            <if test="ccspMac != null">
                ccsp_mac = #{ccspMac},
            </if>
        </set>
        where user_sid = #{userSid}
    </update>

    <select id="userDataExist" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.User" resultType="java.lang.Integer">
        select count(*) from sys_m_user
        <trim prefix="where" prefixOverrides="and|or">
            <if test="account != null">
                and account = #{account}
                and status!=8
            </if>
            <if test="mobile != null">
                or (status not in (4, 8) and mobile = #{mobile})
            </if>
            <if test="email != null">
                or (status not in (4, 8) and email = #{email})
            </if>
        </trim>
    </select>

    <select id="selectByProjectUsers" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        distinct
        <include refid="Base_Column_List_1"/>
        from sys_m_user A
        left join sys_m_user_org B on A.user_sid = B.user_sid
        <include refid="Example_Where_Clause_1"/>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <insert id="insertUserRole" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.UserRole">
        insert into sys_m_user_role (user_sid, role_sid, org_sid) values (#{userSid}, #{roleSid}, #{orgSid});
    </insert>
    <insert id="insertUserParent">
        insert into sys_m_user_parent(user_sid, parent_user_sid)
        values (#{userSid}, #{userParentSid})
    </insert>

    <select id="findByAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where
        account = #{account}
    </select>
    <select id="assertIamUser" resultType="boolean" parameterType="long">
    select distinct ifnull((select parent_sid is not null from sys_m_user where user_sid = #{userSid}), 0) from sys_m_user;
  </select>

    <update id="updateDistributorUserRole" parameterType="long">
        update sys_m_user_role set role_sid = #{roleSid} where user_sid = #{userSid}
    </update>

    <delete id="deleteUserRole" parameterType="list">
        DELETE FROM sys_m_user_role WHERE user_sid in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectOperationUser" resultMap="BaseResultMap">
        select distinct
        <include refid="Base_Column_List_1"/>
        from sys_m_user A
            INNER JOIN sys_m_user_role B
                on B.role_sid IN(301,303,304,305)
                AND A.user_sid = B.user_sid
        where
        A.status = 1
        group by A.user_sid
    </select>
    <select id="selectUserRole" resultType="long" parameterType="long">
        select role_sid from sys_m_user_role where user_sid = #{userSid}
    </select>
    <select id="findByAccountLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_m_user
        where
        account like concat ('%', #{accountLike}, '%') and status != 8;
    </select>
    <select id="toCheckUserCount" resultType="java.lang.Integer">
        select count(1)
        from sys_m_user where org_sid = ( select org_sid from sys_m_user where user_sid=#{rootUser}) and user_sid in
        <foreach item="item" index="index" collection="userSids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectAllByOrgName" resultType="cn.com.cloudstar.rightcloud.bss.module.access.bean.User">
        select u.user_sid,u.account from sys_m_user u left join sys_m_org o on o.owner=u.user_sid
        where o.org_name=#{orgName}
    </select>

    <insert id="insertSelectivePasswordHistory" keyProperty="id" useGeneratedKeys="true"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory">
        insert into sys_m_user_password_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version}
            </if>
        </trim>
    </insert>

    <select id="getAllDistributotUser"
            resultType="cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DistributorUserDTO">
        select us.user_sid, us.account, us.real_name, org.org_sid, org.org_name,org.ldap_ou as uid
        from sys_m_org org
        left join sys_m_user us
        on org.org_sid = us.org_sid
        left join sys_m_user_role usr on usr.user_sid=us.user_sid
        WHERE us.user_type = #{userType,jdbcType=VARCHAR}
        and us.`status` = #{status,jdbcType=VARCHAR}
        and usr.role_sid=302
    </select>

    <select id="selectUserRoleByUserSid" resultType="cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role">
        SELECT
            smr.*
        FROM
            sys_m_role smr
                LEFT JOIN sys_m_user_role sur ON smr.role_sid  = sur.role_sid
        WHERE
            sur.user_sid = #{userSid}
    </select>

    <select id="getUserByDistributotId"
            resultType="cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DistributorUserDTO">
        select us.user_sid, us.account, us.real_name,org.org_sid,org.org_name,org.ldap_ou as uid
        from biz_distributor dis
        left join biz_billing_account acc on dis.id = acc.distributor_id
        left JOIN sys_m_user us on us.user_sid = acc.admin_sid
        left join sys_m_org org on org.org_sid=us.org_sid
        WHERE dis.id = (SELECT dis.id
        from sys_m_user us
        left join biz_distributor dis on us.company_id = dis.id
        WHERE us.user_sid = #{userSid})
        and us.status=1
    </select>
    <select id="selectOrgSidByAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where account = #{tenant}
    </select>

    <select id="countSameMobileByOrgIds" resultType="int">
        select
        count(distinct user_sid)
        from sys_m_user where mobile = #{mobile} and org_sid in
        <foreach collection="orgIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
