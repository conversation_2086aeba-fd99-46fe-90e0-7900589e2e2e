/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;

/**
 * <AUTHOR>
 * @since 2019/10/16 16:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("查询优惠劵下的账户请求参数")
public class DescribeCouponAccountRequest extends BaseRequest {

    /**
     * 优惠劵sid
     */
    @NotNull(message = "优惠劵sid不能为空")
    @ApiModelProperty("优惠劵sid")
    private Long sid;

    /**
     * 账户查询类型
     */
    @ApiModelProperty("账户查询类型")
    private String accountType;

    /**
     * 账户名称模糊查询
     */
    @ApiModelProperty("账户名称模糊查询")
    private String accountNameLike;

}
