/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.request.ResVmInfoListRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.result.ResVmInfoListResult;

/**
 * <AUTHOR>
 * @date 2020/5/6.
 */
@FeignClient(value = "${feign.url.resource-dc:https://cmp-resource-dc:6003}", configuration = FeignConfig.class, path = "/api/resource/v1")
public interface EcsDcService {

    @GetMapping("/vms/info/list")
    RestResult<List<ResVmInfoListResult>> getVmInfoByIds(@SpringQueryMap ResVmInfoListRequest request);

}
