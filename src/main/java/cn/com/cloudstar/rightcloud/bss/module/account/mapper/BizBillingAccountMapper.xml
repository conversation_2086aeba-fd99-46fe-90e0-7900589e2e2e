<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper">
    <resultMap id="BaseResultMap"
        type="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="admin_sid" property="adminSid" jdbcType="BIGINT"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="credit_line" property="creditLine" jdbcType="DECIMAL"/>
        <result column="credit_line_dt" property="creditLineDt" jdbcType="VARCHAR"/>
        <result column="salesmen" property="salesmen" jdbcType="VARCHAR"/>
        <result column="salesmen_id" property="salesmenId" jdbcType="BIGINT"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
        <result column="balance_cash" property="balanceCash" jdbcType="DECIMAL"/>
        <result column="userStatus" property="userStatus" jdbcType="VARCHAR"/>
        <result column="freeze_status" property="freezeStatus" jdbcType="VARCHAR"/>
        <result column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="certification_status" property="certificationStatus" jdbcType="VARCHAR"/>
        <result column="contact_name" property="contactName" jdbcType="VARCHAR"/>
        <result column="entity_id" property="entityId" jdbcType="BIGINT"/>
        <result column="entity_name" property="entityName" jdbcType="VARCHAR"/>
        <result column="userStatus" property="userStatus" jdbcType="VARCHAR"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="industry" property="industry" jdbcType="VARCHAR"/>
        <result column="industryName" property="industryName" jdbcType="VARCHAR"/>
        <result column="industryNameUs" property="industryNameUs" jdbcType="VARCHAR"/>
        <result column="applicationScenario" property="applicationScenario" jdbcType="VARCHAR"/>
        <result column="applicationScenarioName" property="applicationScenarioName" jdbcType="VARCHAR"/>
        <result column="personnelSize" property="personnelSize" jdbcType="VARCHAR"/>
        <result column="personnelSizeName" property="personnelSizeName" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="ldap_ou" property="ldapOu" jdbcType="VARCHAR"/>
        <result column="business_tag" property="businessTag" jdbcType="VARCHAR"/>
        <result column="entity_id" property="entityId" jdbcType="BIGINT"/>
        <result column="entity_name" property="entityName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="userStatus" property="userStatus" jdbcType="VARCHAR"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="entity_id" property="entityId" jdbcType="BIGINT"/>
        <result column="entity_name" property="entityName" jdbcType="VARCHAR"/>
        <result column="userStatus" property="userStatus" jdbcType="VARCHAR"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="solution" property="solution" jdbcType="VARCHAR"/>
        <result column="customization_info" property="customizationInfo" jdbcType="VARCHAR"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="VARCHAR"/>
        <result column="action_strategy" property="actionStrategy" jdbcType="VARCHAR"/>
        <result column="offline_invoiced_amount" property="offlineInvoicedAmount" jdbcType="VARCHAR"/>
        <result column="invoiced_amount" property="invoicedAmount" jdbcType="VARCHAR"/>
        <result column="total_recharge_amount" property="totalRechargeAmount" jdbcType="VARCHAR"/>
        <result column="bms_enable" property="bmsEnable" jdbcType="BIGINT"/>
        <result column="obs_free_capacity" property="obsFreeCapacity" jdbcType="DECIMAL"/>
        <collection property="discounts"
            ofType="cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount">
            <result column="discount_sid" property="discountSid" jdbcType="BIGINT"/>
            <result column="discount_name" property="discountName" jdbcType="VARCHAR"/>
            <result column="user_sid" property="userSid" jdbcType="BIGINT"/>
            <result column="origin_type" property="originType" jdbcType="VARCHAR"/>
            <result column="cloud_env_scope" property="cloudEnvScope" jdbcType="VARCHAR"/>
            <result column="product_scope" property="productScope" jdbcType="VARCHAR"/>
            <result column="scope_type" property="scopeType" jdbcType="VARCHAR"/>
            <result column="scope_value" property="scopeValue" jdbcType="VARCHAR"/>
            <result column="discount_ratio" property="discountRatio" jdbcType="DECIMAL"/>
            <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
            <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
            <result column="description" property="description" jdbcType="VARCHAR"/>
            <result column="discountStatus" property="status" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <resultMap id="BaseResult"
               type="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="admin_sid" property="adminSid" jdbcType="BIGINT"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="credit_line" property="creditLine" jdbcType="DECIMAL"/>
        <result column="salesmen" property="salesmen" jdbcType="VARCHAR"/>
        <result column="salesmen_Id" property="salesmenId" jdbcType="BIGINT"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
        <result column="balance_cash" property="balanceCash" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="freeze_status" property="freezeStatus" jdbcType="VARCHAR"/>
        <result column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="certification_status" property="certificationStatus" jdbcType="VARCHAR"/>
        <result column="contact_name" property="contactName" jdbcType="VARCHAR"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="industry" property="industry" jdbcType="VARCHAR"/>
        <result column="industryName" property="industryName" jdbcType="VARCHAR"/>
        <result column="industryNameUs" property="industryNameUs" jdbcType="VARCHAR"/>
        <result column="applicationScenario" property="applicationScenario" jdbcType="VARCHAR"/>
        <result column="applicationScenarioName" property="applicationScenarioName" jdbcType="VARCHAR"/>
        <result column="personnelSize" property="personnelSize" jdbcType="VARCHAR"/>
        <result column="personnelSizeName" property="personnelSizeName" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="end_time" property="endTime" jdbcType="VARCHAR"/>
        <result column="action_strategy" property="actionStrategy" jdbcType="VARCHAR"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR" />
        <result column="offline_invoiced_amount" property="offlineInvoicedAmount" jdbcType="VARCHAR"/>
        <result column="invoiced_amount" property="invoicedAmount" jdbcType="VARCHAR"/>
        <result column="total_recharge_amount" property="totalRechargeAmount" jdbcType="VARCHAR"/>
        <collection property="discounts" ofType="cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount"
                    select="selectDiscountForCount"  javaType="ArrayList" column="id">
        </collection>
    </resultMap>
    <resultMap id="myMap" type="cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount">
        <result column="discount_sid" property="discountSid" jdbcType="BIGINT"/>
        <result column="discount_name" property="discountName" jdbcType="VARCHAR"/>
        <result column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="origin_type" property="originType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="cloud_env_scope" property="cloudEnvScope" jdbcType="VARCHAR"/>
        <result column="product_scope" property="productScope" jdbcType="VARCHAR"/>
        <result column="scope_type" property="scopeType" jdbcType="VARCHAR"/>
        <result column="scope_value" property="scopeValue" jdbcType="VARCHAR"/>
        <result column="discount_ratio" property="discountRatio" jdbcType="DECIMAL"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.productAccountList != null and condition.productAccountList.size() > 0 ">
                and A.created_by in
                <foreach collection="condition.productAccountList" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.bmsEnable == 1">
                and o.bms_enable = 1
            </if>
            <if test="condition.bmsEnable == 0">
                and (o.bms_enable = 0 or o.bms_enable is null)
            </if>
            <if test="condition.keyword != null">
                and(
                B.account like concat('%', #{condition.keyword}, '%')
                or o.ldap_ou like concat('%', #{condition.keyword}, '%')
                or A.id like concat('%', #{condition.keyword}, '%')
                or A.account_name like concat('%', #{condition.keyword}, '%')
                <choose>
                    <when test="condition.keywordHash != null">
                        or B.email_hash = #{condition.keywordHash}
                        or B.mobile_hash = #{condition.keywordHash}
                        or t2.real_name_hash = #{condition.keywordHash}
                    </when>
                    <otherwise>
                        or B.email like concat('%', #{condition.keyword}, '%')
                        or B.mobile like concat('%', #{condition.keyword}, '%')
                        or t2.real_name like concat('%', #{condition.keyword}, '%')
                    </otherwise>
                </choose>
                <choose>
                    <when test="condition.distributorTag != null">
                        or A.distributor_id is null
                    </when>
                    <otherwise>
                        or t3.name like concat('%', #{condition.keyword}, '%')
                    </otherwise>
                </choose>
                <if test="condition.businessList != null and condition.businessList.size() > 0">
                    <foreach collection="condition.businessList" item="id">
                        or B.business_tag like concat('%', #{id}, '%')
                    </foreach>
                </if>
                <if test="condition.industryList != null and condition.industryList.size() > 0">
                    or o.industry_type in
                    <foreach collection="condition.industryList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <if test="condition.applicationList != null and condition.applicationList.size() > 0">
                    or o.application_scenario in
                    <foreach collection="condition.applicationList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="condition.id != null">
                and A.id = #{condition.id}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.orgName != null">
                and o.org_name like concat('%', #{condition.orgName}, '%')
            </if>
            <if test="condition.accountName != null">
                and A.account_name = #{condition.accountName}
            </if>
            <if test="condition.accountNameLike != null">
                and A.account_name like concat('%', #{condition.accountNameLike}, '%')
            </if>
            <if test="condition.direct != null">
                and A.distributor_id is null
            </if>
            <if test="condition.balance != null">
                and A.balance = #{condition.balance}
            </if>
            <if test="condition.createdBy != null">
                and A.CREATED_BY = #{condition.createdBy}
            </if>
            <if test="condition.entityId != null">
                and A.entity_id = #{condition.entityId}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.UPDATED_BY = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.VERSION = #{condition.version}
            </if>
            <if test="condition.distributorId != null">
                and A.distributor_id = #{condition.distributorId}
            </if>
            <if test="condition.mobile != null">
                and B.mobile like concat('%', #{condition.mobile}, '%')
            </if>
            <if test="condition.email != null">
                and B.email like concat('%', #{condition.email}, '%')
            </if>
            <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
                and A.id in
                <foreach collection="condition.accountIds" item="item" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.accountIds != null and condition.accountIds.size() == 0 ">
                and A.id in ('')
            </if>
            <if test="condition.discountNameLike != null">
                and B.discount_name like concat('%', #{condition.discountNameLike}, '%')
            </if>
            <if test="condition.status != null">
                and B.status = #{condition.status}
            </if>
            <if test="condition.notStatus != null">
                and B.status != #{condition.notStatus}
            </if>
            <if test="condition.statusList != null and condition.statusList.size() > 0 ">
                and B.status in
                <foreach collection="condition.statusList" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.certificationStatus != null">
                and B.certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.freezeStatus != null">
                <choose>
                    <when test="condition.freezeStatus == 0">
                        and B.freeze_status = 0
                    </when>
                    <otherwise>
                        and (B.freeze_status = 1 or B.freeze_status is null)
                    </otherwise>
                </choose>

            </if>
            <if test="condition.salesmenId != null">
                and A.salesmen_id = #{condition.salesmenId}
            </if>
            <if test="condition.distributorName != null">
                and t3.name like concat('%', #{condition.distributorName}, '%')
            </if>
            <if test="condition.salesmen != null">
                and t2.real_name like concat('%', #{condition.salesmen}, '%')
            </if>
            <if test="condition.account != null">
                and B.account like concat('%', #{condition.account}, '%')
            </if>
            <if test="condition.creditLine != null">
                and A.credit_line = #{condition.creditLine}
            </if>
            <if test="condition.industry != null">
                o.industry_type = #{condition.industry}
            </if>
            <if test="condition.applicationScenario != null">
                o.application_scenario = #{condition.applicationScenario}
            </if>
            <if test="condition.businessTag != null">
                and B.business_tag like concat('%', #{condition.businessTag}, '%')
            </if>
            <if test="condition.entityId != null">
                and A.entity_id = #{condition.entityId}
            </if>
        </trim>
    </sql>

    <sql id="Example_Where_Industry">
        <if test="condition.notAccountIds != null and condition.notAccountIds.size() > 0 ">
            and A.id not in
            <foreach collection="condition.notAccountIds" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.id != null">
            and A.id = #{condition.id}
        </if>
        <if test="condition.entityId != null">
            and A.entity_id = #{condition.entityId}
        </if>
        <if test="condition.orgSid != null">
            and A.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.orgName != null">
            and o.org_name like concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.accountName != null">
            and A.account_name = #{condition.accountName}
        </if>
        <if test="condition.accountNameLike != null">
            and A.account_name like concat('%', #{condition.accountNameLike}, '%')
        </if>
        <if test="condition.direct != null">
            and A.distributor_id is null
        </if>
        <if test="condition.balance != null">
            and A.balance = #{condition.balance}
        </if>
        <if test="condition.createdBy != null">
            and A.CREATED_BY = #{condition.createdBy}
        </if>
        <if test="condition.createdDt != null">
            and A.created_dt = #{condition.createdDt}
        </if>
        <if test="condition.updatedBy != null">
            and A.UPDATED_BY = #{condition.updatedBy}
        </if>
        <if test="condition.updatedDt != null">
            and A.updated_dt = #{condition.updatedDt}
        </if>
        <if test="condition.version != null">
            and A.VERSION = #{condition.version}
        </if>
        <if test="condition.distributorId != null">
            and A.distributor_id = #{condition.distributorId}
        </if>
        <if test="condition.accountStatus != null">
            and A.status = #{condition.accountStatus}
        </if>
        <if test="condition.mobile != null">
            and B.mobile like concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.email != null">
            and B.email like concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
            and A.id in
            <foreach collection="condition.accountIds" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.discountNameLike != null">
            and B.discount_name like concat('%', #{condition.discountNameLike}, '%')
        </if>
        <if test="condition.status != null">
            and U.status = #{condition.status}
        </if>
        <if test="condition.excludeUnapproved != null">
            and U.status != '2'
        </if>
        <if test="condition.notStatus != null and condition.notStatus.size() > 0" >
            and U.status not in
            <foreach collection="condition.notStatus" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.statusList != null and condition.statusList.size() > 0 ">
            and B.status in
            <foreach collection="condition.statusList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null">
            and (U.certification_status = #{condition.certificationStatus}
            or B.certification_status=#{condition.certificationStatus})
        </if>
        <if test="condition.freezeStatus != null">
            <choose>
                <when test="condition.freezeStatus == 0">
                    and U.freeze_status = 0
                </when>
                <otherwise>
                    and (U.freeze_status = 1 or U.freeze_status is null)
                </otherwise>
            </choose>

        </if>
        <if test="condition.salesmenId != null">
            and A.salesmen_id = #{condition.salesmenId}
        </if>
        <if test="condition.distributorName != null">
            and t3.name like concat('%', #{condition.distributorName}, '%')
        </if>
        <if test="condition.salesmen != null">
            and t2.real_name like concat('%', #{condition.salesmen}, '%')
        </if>
        <if test="condition.account != null">
            and B.account like concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.ldapOu != null">
            and B.ldap_ou like concat('%', #{condition.ldapOu}, '%')
        </if>
        <if test="condition.creditLine != null">
            and A.credit_line = #{condition.creditLine}
        </if>
        <if test="condition.creditLineDt != null">
            and A.credit_line_dt = #{credit_line_dt.creditLineDt}
        </if>
        <if test="condition.entityId != null">
            and A.entity_id = #{condition.entityId}
        </if>
        <if test="condition.entityName != null and condition.entityName != ''">
            and A.entity_name like concat('%', #{condition.entityName}, '%')
        </if>
        <if test="condition.customizationInfoKey != null and condition.customizationInfoKey.size != 0
                and condition.customizationInfoValue != null and condition.customizationInfoValue.size != 0">
            <foreach item="item" collection="condition.customizationInfoKey" index="idx" open=" " separator=" " close=" ">
                and CASE WHEN JSON_VALID(o.customization_info) THEN JSON_EXTRACT(o.customization_info, CONCAT('',
                REPLACE(
                SUBSTRING_INDEX(
                JSON_SEARCH(o.customization_info, 'one', #{item}, NULL, '$[*].attrKey'),'.',1),'"',''), '.attrValue'))
                LIKE CONCAT('%', '${condition.customizationInfoValue[idx]}', '%')
                ELSE NULL END
            </foreach>
        </if>
    </sql>

    <sql id="Example_Where_CertificationStatus">
        <if test="condition.orgSid != null" >
            and A.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.ldapOu != null">
            and o.ldap_ou like concat('%', #{condition.ldapOu}, '%')
        </if>
        <if test="condition.id != null">
            and A.id = #{condition.id}
        </if>
        <if test="condition.orgName != null">
            and o.org_name like concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.accountName != null" >
            and A.account_name = #{condition.accountName}
        </if>
        <if test="condition.accountNameLike != null" >
            and A.account_name like concat('%', #{condition.accountNameLike}, '%')
        </if>
        <if test="condition.direct != null" >
            and A.distributor_id is null
        </if>
        <if test="condition.balance != null" >
            and A.balance = #{condition.balance}
        </if>
        <if test="condition.createdBy != null" >
            and A.CREATED_BY = #{condition.createdBy}
        </if>
        <if test="condition.createdDt != null" >
            and A.created_dt = #{condition.createdDt}
        </if>
        <if test="condition.updatedBy != null" >
            and A.UPDATED_BY = #{condition.updatedBy}
        </if>
        <if test="condition.updatedDt != null" >
            and A.updated_dt = #{condition.updatedDt}
        </if>
        <if test="condition.version != null" >
            and A.VERSION = #{condition.version}
        </if>
        <if test="condition.distributorId != null" >
            and A.distributor_id = #{condition.distributorId}
        </if>
        <if test="condition.mobile != null" >
            and B.mobile like concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.email != null" >
            and B.email like concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.businessTag != null">
            and B.business_tag like concat('%', #{condition.businessTag}, '%')
        </if>
        <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
            and A.id in
            <foreach collection="condition.accountIds" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.discountNameLike != null" >
            and B.discount_name like concat('%', #{condition.discountNameLike}, '%')
        </if>
        <if test="condition.status != null" >
            and U.status = #{condition.status}
        </if>
        <if test="condition.notStatus != null" >
            and U.status != #{condition.notStatus}
        </if>
        <if test="condition.statusList != null and condition.statusList.size() > 0 ">
            and B.status in
            <foreach collection="condition.statusList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null" >
            and (U.certification_status = #{condition.certificationStatus} or
            O.certification_status=#{condition.certificationStatus} )
        </if>
        <if test="condition.freezeStatus != null">
            <choose>
                <when test="condition.freezeStatus == 1">
                    and A.status = 'normal'
                </when>
                <when test="condition.freezeStatus == 0">
                    and U.freeze_status = 0
                </when>
                <otherwise>
                    and (U.freeze_status = 1 or U.freeze_status is null)
                </otherwise>
            </choose>

        </if>
        <if test="condition.salesmenId != null" >
            and A.salesmen_id = #{condition.salesmenId}
        </if>
        <if test="condition.distributorName != null" >
            and t3.name like concat('%', #{condition.distributorName}, '%')
        </if>
        <if test="condition.salesmen != null" >
            and t2.real_name = #{condition.salesmen}
        </if>
        <if test="condition.account != null">
            and B.account like concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.creditLine != null">
            and A.credit_line = #{condition.creditLine}
        </if>
        <if test="condition.creditLineDt != null">
            and A.credit_line_dt = #{credit_line_dt.creditLineDt}
        </if>
        <if test="condition.entityId != null">
            and A.entity_id = #{condition.entityId}
        </if>
        <if test="condition.entityName != null and condition.entityName != ''">
            and A.entity_name like concat('%', #{condition.entityName}, '%')
        </if>
    </sql>

    <sql id="Example_Where_Status">
        <if test="condition.productAccountList != null and condition.productAccountList.size() > 0 ">
            and A.created_by in
            <foreach collection="condition.productAccountList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.keyword != null">
            and(
            B.account like concat('%', #{condition.keyword}, '%')
            or o.ldap_ou like concat('%', #{condition.keyword}, '%')
            or A.id like concat('%', #{condition.keyword}, '%')
            or A.account_name like concat('%', #{condition.keyword}, '%')
            <choose>
                <when test="condition.keywordHash != null">
                    or B.email_hash = #{condition.keywordHash}
                    or B.mobile_hash = #{condition.keywordHash}
                    or t2.real_name_hash = #{condition.keywordHash}
                </when>
                <otherwise>
                    or B.email like concat('%', #{condition.keyword}, '%')
                    or B.mobile like concat('%', #{condition.keyword}, '%')
                    or t2.real_name like concat('%', #{condition.keyword}, '%')
                </otherwise>
            </choose>
            <choose>
                <when test="condition.distributorTag != null">
                    or A.distributor_id is null
                </when>
                <otherwise>
                    or t3.name like concat('%', #{condition.keyword}, '%')
                </otherwise>
            </choose>
            <if test="condition.businessList != null and condition.businessList.size() > 0">
                <foreach collection="condition.businessList" item="id" index="index" >
                    or B.business_tag like concat('%', #{id}, '%')
                </foreach>
            </if>
            <if test="condition.industryList != null and condition.industryList.size() > 0">
                or o.industry_type in
                <foreach collection="condition.industryList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.applicationList != null and condition.applicationList.size() > 0">
                or o.application_scenario in
                <foreach collection="condition.applicationList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="condition.id != null">
            and A.id = #{condition.id}
        </if>
        <if test="condition.orgSid != null" >
            and A.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.entityId != null" >
            and A.entity_id = #{condition.entityId}
        </if>
        <if test="condition.orgName != null">
            and o.org_name like concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.accountName != null" >
            and A.account_name like concat('%', #{condition.accountName}, '%')
        </if>
        <if test="condition.accountNameLike != null" >
            and A.account_name like concat('%', #{condition.accountNameLike}, '%')
        </if>
        <if test="condition.direct != null" >
            and A.distributor_id is null
        </if>
        <if test="condition.balance != null" >
            and A.balance = #{condition.balance}
        </if>
        <if test="condition.createdBy != null" >
            and A.CREATED_BY = #{condition.createdBy}
        </if>
        <if test="condition.createdDt != null" >
            and A.created_dt = #{condition.createdDt}
        </if>
        <if test="condition.updatedBy != null" >
            and A.UPDATED_BY = #{condition.updatedBy}
        </if>
        <if test="condition.updatedDt != null" >
            and A.updated_dt = #{condition.updatedDt}
        </if>
        <if test="condition.version != null" >
            and A.VERSION = #{condition.version}
        </if>
        <if test="condition.distributorId != null" >
            and A.distributor_id = #{condition.distributorId}
        </if>
        <if test="condition.mobile != null" >
            and B.mobile like concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.mobileHash != null" >
            and B.mobile_hash = #{condition.mobileHash}
        </if>
        <if test="condition.email != null" >
            and B.email like concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.emailHash != null" >
            and B.email_hash = #{condition.emailHash}
        </if>
        <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
            and A.id in
            <foreach collection="condition.accountIds" item="item" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.discountNameLike != null" >
            and B.discount_name like concat('%', #{condition.discountNameLike}, '%')
        </if>
        <if test="condition.status != null" >
            and B.status = #{condition.status}
        </if>
        <if test="condition.notStatus != null" >
            and B.status != #{condition.notStatus}
        </if>
        <if test="condition.notStatusList != null and condition.notStatusList.size() > 0 ">
            and B.status not in
            <foreach collection="condition.notStatusList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.statusList != null and condition.statusList.size() > 0 ">
            and B.status in
            <foreach collection="condition.statusList" item="item" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null" >
            and (B.certification_status = #{condition.certificationStatus} or
            O.certification_status=#{condition.certificationStatus} )
        </if>
        <if test="condition.freezeStatus != null">
            <choose>
                <when test="condition.freezeStatus == 0">
                    and (A.status != 'normal' or A.status is null)
                </when>
                <otherwise>
                    and A.status = 'normal'
                </otherwise>
            </choose>

        </if>
        <if test="condition.salesmenId != null" >
            and A.salesmen_id = #{condition.salesmenId}
        </if>
        <if test="condition.distributorName != null" >
            and t3.name like concat('%', #{condition.distributorName}, '%')
        </if>
        <if test="condition.salesmen != null" >
            and t2.real_name like concat('%', #{condition.salesmen}, '%')
        </if>
        <if test="condition.account != null">
            and B.account like concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.creditLine != null">
            and A.credit_line = #{condition.creditLine}
        </if>
<!--        <if test="condition.businessTag != null">-->
<!--            and B.business_tag like concat('%', #{condition.businessTag}, '%')-->
<!--        </if>-->
        <if test="condition.ldapOu != null">
            and o.ldap_ou like concat('%', #{condition.ldapOu}, '%')
        </if>
        <if test="condition.customizationInfoKey != null and condition.customizationInfoKey.size != 0
                and condition.customizationInfoValue != null and condition.customizationInfoValue.size != 0">
            <foreach item="item" collection="condition.customizationInfoKey" index="idx" open=" " separator=" " close=" ">
                and CASE WHEN JSON_VALID(o.customization_info) THEN JSON_EXTRACT(o.customization_info, CONCAT('',
                REPLACE(
                SUBSTRING_INDEX(
                JSON_SEARCH(o.customization_info, 'one', #{item}, NULL, '$[*].attrKey'),'.',1),'"',''), '.attrValue'))
                LIKE CONCAT('%', '${condition.customizationInfoValue[idx]}', '%')
                ELSE NULL END
            </foreach>
        </if>
        <if test="condition.adminSid != null">
            and A.admin_sid = #{condition.adminSid}
        </if>
        <if test="condition.businessTagLike != null and condition.businessTagLike != ''">
            AND (
            B.business_tag LIKE CONCAT('%', #{condition.businessTagLike}, '%')
            <if test="condition.businessTagList != null and condition.businessTagList.size != 0">
                <foreach item="item" collection="condition.businessTagList" index="idx" open=" " separator=" " close=" ">
                    OR B.business_tag LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            )
        </if>
    </sql>

    <sql id="Example_Where_CertificationStatusByBug">
        <if test="condition.orgSid != null" >
            and A.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.entityId != null" >
            and A.entity_id = #{condition.entityId}
        </if>
        <if test="condition.orgName != null">
            and o.org_name like concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.accountName != null" >
            and A.account_name like concat('%', #{condition.accountName}, '%')
        </if>
        <if test="condition.accountName != null" >
            and B.account like concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.accountNameLike != null" >
            and A.account_name like concat('%', #{condition.accountNameLike}, '%')
        </if>
        <if test="condition.balance != null" >
            and A.balance = #{condition.balance}
        </if>
        <if test="condition.createdBy != null" >
            and A.CREATED_BY = #{condition.createdBy}
        </if>
        <if test="condition.createdDt != null" >
            and A.created_dt = #{condition.createdDt}
        </if>
        <if test="condition.updatedBy != null" >
            and A.UPDATED_BY = #{condition.updatedBy}
        </if>
        <if test="condition.updatedDt != null" >
            and A.updated_dt = #{condition.updatedDt}
        </if>
        <if test="condition.version != null" >
            and A.VERSION = #{condition.version}
        </if>
        <if test="condition.distributorId != null" >
            and A.distributor_id = #{condition.distributorId}
        </if>
        <if test="condition.mobile != null" >
            and B.mobile like concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.email != null" >
            and B.email like concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.adminSid != null">
            and A.admin_sid = #{condition.adminSid}
        </if>
        <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
            and A.id in
            <foreach collection="condition.accountIds" item="item" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.discountNameLike != null" >
            and B.discount_name like concat('%', #{condition.discountNameLike}, '%')
        </if>
        <if test="condition.status != null" >
            and U.status = #{condition.status}
        </if>
        <if test="condition.notStatus != null" >
            and U.status != #{condition.notStatus}
        </if>
        <if test="condition.notStatusList != null and condition.notStatusList.size() > 0 ">
            and U.status not in
            <foreach collection="condition.notStatusList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.statusList != null and condition.statusList.size() > 0 ">
            and B.status in
            <foreach collection="condition.statusList" item="item" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null" >
            and (U.certification_status = #{condition.certificationStatus} or
            O.certification_status=#{condition.certificationStatus} )
        </if>
        <if test="condition.freezeStatus != null">
            <choose>
                <when test="condition.freezeStatus == 0">
                    and U.freeze_status = 0
                </when>
                <otherwise>
                    and (U.freeze_status = 1 or U.freeze_status is null)
                </otherwise>
            </choose>

        </if>
        <if test="condition.salesmenId != null" >
            and A.salesmen_id = #{condition.salesmenId}
        </if>
        <if test="condition.distributorName != null" >
            and (t3.name like concat('%', #{condition.distributorName}, '%') or t3.name is null)
        </if>
        <if test="condition.salesmen != null" >
            and t2.real_name like concat('%', #{condition.salesmen}, '%')
        </if>
        <if test="condition.account != null">
            and B.account like concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.creditLine != null">
            and A.credit_line = #{condition.creditLine}
        </if>
        <if test="condition.ldapOu != null">
            and o.ldap_ou like concat('%', #{condition.ldapOu}, '%')
        </if>
    </sql>
    <sql id="Base_Column_List2" >
        id, org_sid, admin_sid, account_name, balance, discount, CREATED_BY, created_dt, UPDATED_BY, updated_dt,entity_id,entity_name,status,unfreeze_type,
    VERSION, credit_line, balance_cash,credit_line_dt,ccsp_mac,offline_invoiced_amount,total_recharge_amount,invoiced_amount
    </sql>
    <sql id="Base_Column_List">
        A
        .
        id
        , A.org_sid, A.admin_sid, A.account_name, A.balance, A.discount, A.CREATED_BY, A.created_dt, A.UPDATED_BY, A.updated_dt,
    A.VERSION,A.remark,A.credit_line,A.credit_line_dt,A.distributor_id,A.balance_cash,A.salesmen_Id,A.entity_id,A.entity_name,A.status,A.unfreeze_type,
    A.ccsp_mac,A.end_time,A.action_strategy,A.offline_invoiced_amount,A.total_recharge_amount,A.invoiced_amount

    </sql>
    <sql id="Alias_Column_List">
    A. id, A.org_sid, A.admin_sid, A.account_name, A.balance, A.discount, A.CREATED_BY, A.created_dt, A.UPDATED_BY, A.updated_dt,
    A.VERSION,A.remark,A.credit_line,A.distributor_id,A.balance_cash,A.salesmen_Id,A.entity_id,A.entity_name,A.status,A.unfreeze_type,
    A.credit_line_dt,A.end_time,A.action_strategy,A.ccsp_mac,A.offline_invoiced_amount,A.total_recharge_amount,A.invoiced_amount,
    A.obs_free_capacity
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        distinct
        <include refid="Alias_Column_List"/>
        ,B.real_name admin_name
        ,B.mobile
        ,B.email
        ,o.org_name
        ,t2.real_name as salesmen
        ,t3.name as distributor_name
        ,B.status as userStatus
        ,B.user_sid
        ,B.freeze_status
        ,B.account
        ,B.certification_status
        ,B.business_tag
        ,o.contact_name
        ,o.address as address
        ,o.ldap_ou
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        from biz_billing_account A
        left join (select user_sid,account,real_name,mobile,email,status,freeze_status,certification_status,business_tag
        from
        sys_m_user) B
        on A.admin_sid = B.user_sid
        left join (select user_sid, real_name, email from sys_m_user) t2 on A.salesmen_id = t2.user_sid
        left join (select user_sid,certification_status,freeze_status, status from sys_m_user) U on A.admin_sid =
        U.user_sid
        left join sys_m_org o on o.org_sid = A.org_sid
        left join biz_distributor t3 on t3.id = A.distributor_id
        left join sys_m_code itCode on o.industry_type = itCode.CODE_VALUE and itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        left join sys_m_code asCode on o.application_scenario = asCode.CODE_VALUE and asCode.CODE_CATEGORY =
        'APPLICATION_SCENARIO'
        left join sys_m_code psCode on o.personnel_size = psCode.CODE_VALUE and psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.industry != null">
                and o.industry_type = #{condition.industry}
            </if>
            <if test="condition.applicationScenario != null">
                and o.application_scenario = #{condition.applicationScenario}
            </if>
            <include refid="Example_Where_CertificationStatus"/>
        </trim>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        distinct
        <include refid="Alias_Column_List"/>
        ,B.real_name admin_name
        ,B.mobile
        ,B.email
        ,o.org_name
        ,t2.real_name as salesmen
        ,t3.name as distributor_name
        ,B.status as userStatus
        ,B.user_sid
        ,B.freeze_status
        ,B.account
        ,B.certification_status
        ,B.business_tag
        ,o.contact_name
        ,o.ldap_ou
        ,o.address as address
        ,o.bms_enable
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        from sys_m_org o
        LEFT JOIN biz_billing_account A ON o.org_sid = A.org_sid
        LEFT JOIN sys_m_user B ON A.admin_sid = B.user_sid
        LEFT JOIN sys_m_user t2 ON A.salesmen_id = t2.user_sid
        LEFT JOIN biz_distributor t3 ON t3.id = A.distributor_id
        LEFT JOIN sys_m_code itCode ON o.industry_type = itCode.CODE_VALUE
        AND itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        LEFT JOIN sys_m_code asCode ON o.application_scenario = asCode.CODE_VALUE
        AND asCode.CODE_CATEGORY = 'APPLICATION_SCENARIO'
        LEFT JOIN sys_m_code psCode ON o.personnel_size = psCode.CODE_VALUE
        AND psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.industry != null">
                and o.industry_type = #{condition.industry}
            </if>
            <if test="condition.applicationScenario != null">
                and o.application_scenario = #{condition.applicationScenario}
            </if>
            <if test="condition.bmsEnable == 1">
                and o.bms_enable = 1
            </if>
            <if test="condition.bmsEnable == 0">
                and (o.bms_enable = 0 or o.bms_enable is null)
            </if>
            <include refid="Example_Where_Status"/>
        </trim>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>


    <select id="selectByLikeDistributorName" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        distinct
        <include refid="Alias_Column_List"/>
        ,B.real_name admin_name
        ,B.mobile
        ,B.email
        ,o.org_name
        ,t2.real_name as salesmen
        ,t3.name as distributor_name
        ,B.status as userStatus
        ,B.user_sid
        ,B.freeze_status
        ,B.account
        ,B.certification_status
        ,B.business_tag
        ,o.contact_name
        ,o.address as address
        ,o.ldap_ou
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        from biz_billing_account A
        left join (select user_sid,account,real_name,mobile,email,status,freeze_status,certification_status,business_tag
        from
        sys_m_user) B
        on A.admin_sid = B.user_sid
        left join (select user_sid, real_name, email from sys_m_user) t2 on A.salesmen_id = t2.user_sid
        left join (select user_sid,certification_status,freeze_status, status from sys_m_user) U on A.admin_sid =
        U.user_sid
        left join sys_m_org o on o.org_sid = A.org_sid
        left join biz_distributor t3 on t3.id = A.distributor_id
        left join sys_m_code itCode on o.industry_type = itCode.CODE_VALUE and itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        left join sys_m_code asCode on o.application_scenario = asCode.CODE_VALUE and asCode.CODE_CATEGORY =
        'APPLICATION_SCENARIO'
        left join sys_m_code psCode on o.personnel_size = psCode.CODE_VALUE and psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.industry != null">
                and o.industry_type = #{condition.industry}
            </if>
            <if test="condition.applicationScenario != null">
                and o.application_scenario = #{condition.applicationScenario}
            </if>
            <include refid="Example_Where_CertificationStatusByBug"/>
        </trim>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <select id="selectDiscountByParams" resultMap="BaseResult" parameterType="java.util.Map">
        select
        distinct
        <include refid="Alias_Column_List"/>
        from biz_billing_account A
        INNER JOIN biz_discount B ON A.id = B.user_sid
        <include refid="Example_Where_Clause"/>
        group by A.id
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>
    <select id="selectDiscountForCount" resultMap="myMap">
      select  discount_sid,discount_name,user_sid,status,cloud_env_scope,product_scope,scope_type,scope_value
        ,discount_ratio,start_time,end_time,description,origin_type   from  biz_discount  where  user_sid=#{id}
    </select>

    <select id="selectByList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        ,B.org_name
        ,A.id as accountId
        ,A.entity_name
        ,t2.real_name as salesmen
        ,t2.real_name as admin_name
        ,t3.name as distributor_name
        ,U.status as userStatus
        ,U.user_sid
        ,U.freeze_status
        ,U.account
        ,U.certification_status
        ,U.mobile
        ,U.email
        ,U.business_tag
        ,B.address as address
        ,B.ldap_ou
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        from biz_billing_account A
        left join (select user_sid, account, real_name, mobile,email, status, freeze_status, certification_status, business_tag from
        sys_m_user) U
        on A.admin_sid = U.user_sid
        left join (select user_sid, real_name, email from sys_m_user) t2 on A.salesmen_id = t2.user_sid
        left join sys_m_org B on A.org_sid = B.org_sid
        left join biz_distributor t3 on t3.id = A.distributor_id
        left join sys_m_code itCode on B.industry_type = itCode.CODE_VALUE and itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        left join sys_m_code asCode on B.application_scenario = asCode.CODE_VALUE and asCode.CODE_CATEGORY =
        'APPLICATION_SCENARIO'
        left join sys_m_code psCode on B.personnel_size = psCode.CODE_VALUE and psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.industry != null">
                B.industry_type = #{condition.industry}
            </if>
            <if test="condition.applicationScenario != null">
                and B.application_scenario = #{condition.applicationScenario}
            </if>
            <if test="condition.userSidList != null and condition.userSidList.size()>0">
                and A.admin_sid NOT IN
                <foreach collection="condition.userSidList" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            <if test="condition.accountNameLike != null">
                and A.account_name like concat('%',#{condition.accountNameLike},'%')
            </if>
            <if test="condition.minFrozenAmount != null">
                and IFNULL(A.balance,0)+IFNULL(A.credit_line,0)+IFNULL(A.balance_cash,0) &lt; #{condition.minFrozenAmount}
            </if>
            <if test="condition.userSidList != null and condition.userSidList.size()>0">
                and A.admin_sid NOT IN
                <foreach collection="condition.userSidList" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            <include refid="Example_Where_Industry"/>
        </trim>
        order by CREATED_DT desc
    </select>

    <select id="selectByExport" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        distinct
        <include refid="Alias_Column_List"/>
        ,B.real_name admin_name
        ,B.mobile
        ,B.email
        ,o.org_name
        ,t2.real_name as salesmen
        ,t3.name as distributor_name
        ,B.status as userStatus
        ,B.user_sid
        ,B.freeze_status
        ,B.account
        ,B.certification_status
        ,o.ldap_ou
        ,B.business_tag
        ,o.contact_name
        ,o.address as address
        ,o.bms_enable
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        ,B.status as userStatus
        ,o.customization_info as customization_info
        from sys_m_org o
        LEFT JOIN biz_billing_account A ON o.org_sid = A.org_sid
        LEFT JOIN sys_m_user B ON A.admin_sid = B.user_sid
        LEFT JOIN sys_m_user t2 ON A.salesmen_id = t2.user_sid
        LEFT JOIN biz_distributor t3 ON t3.id = A.distributor_id
        LEFT JOIN sys_m_code itCode ON o.industry_type = itCode.CODE_VALUE
        AND itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        LEFT JOIN sys_m_code asCode ON o.application_scenario = asCode.CODE_VALUE
        AND asCode.CODE_CATEGORY = 'APPLICATION_SCENARIO'
        LEFT JOIN sys_m_code psCode ON o.personnel_size = psCode.CODE_VALUE
        AND psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        <include refid="Example_Where_Clause"/>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>
        ,B.real_name admin_name
        ,B.account
        ,B.real_name
        ,B.mobile
        ,B.email
        ,t2.real_name as salesmen
        ,t3.name as distributor_name
        ,B.user_sid
        ,B.freeze_status
        ,B.business_tag
        ,o.address as address
        ,o.solution as solution
        ,itCode.CODE_VALUE as industry
        ,itCode.CODE_DISPLAY as industryName
        ,itCode.code_display_us as industryNameUs
        ,asCode.CODE_VALUE as applicationScenario
        ,asCode.CODE_DISPLAY as applicationScenarioName
        ,psCode.CODE_VALUE as personnelSize
        ,psCode.CODE_DISPLAY as personnelSizeName
        from biz_billing_account A
        left join (select user_sid,account,mobile,email,freeze_status,business_tag,real_name from sys_m_user) B
        on A.admin_sid = B.user_sid
        left join (select user_sid, real_name, email from sys_m_user) t2 on A.salesmen_id = t2.user_sid
        left join biz_distributor t3 on t3.id = A.distributor_id
        left join sys_m_org o on o.org_sid = A.org_sid
        left join sys_m_code itCode on o.industry_type = itCode.CODE_VALUE and itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        left join sys_m_code asCode on o.application_scenario = asCode.CODE_VALUE and asCode.CODE_CATEGORY =
        'APPLICATION_SCENARIO'
        left join sys_m_code psCode on o.personnel_size = psCode.CODE_VALUE and psCode.CODE_CATEGORY = 'PERSONNEL_SIZE'
        where A.id = #{id}
    </select>

    <select id="selectByIdAndVersion" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from biz_billing_account A
        where A.id = #{id} and A.version = #{version}
    </select>
    <select id="selectByPrimaryKeys" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from biz_billing_account A
        where A.id = #{id}
    </select>
    <select id="selectByOrgId" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>,
        t3.name as distributor_name
        from biz_billing_account A
        left join biz_distributor t3 on t3.id = A.distributor_id
        where A.org_sid = #{orgId}
        <if test="entityId != null">
            and A.entity_id = #{entityId}
        </if>
    </select>


    <select id="selectAccountIdByOrgId"  parameterType="java.lang.Long" resultType="java.lang.Long">
        select id from biz_billing_account where org_sid in(
        select org_sid from sys_m_org where org_type = 'company'
        and (org_sid = #{orgId} OR
        tree_path LIKE concat( '/%', #{orgId}, '/%' )))
    </select>
    <select id="selectByOrgIdAndEntityId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>,
        t3.name as distributor_name
        from biz_billing_account A
        left join biz_distributor t3 on t3.id = A.distributor_id
        where A.org_sid = #{orgId}
        <if test="entityId != null">
                and A.entity_id = #{entityId}
        </if>

    </select>

    <select id="selectProductCatalogWithService" resultMap="ResultMapCloudMarketEnvCatalog">
        SELECT A.id, A.category_name, A.icon, A.sort_number
        FROM sf_product_category_catalog A
        ORDER BY A.sort_number ASC, A.id ASC
    </select>

    <resultMap id="ResultMapCloudMarketEnvCatalog"
        type="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketCatalogVO">
        <result column="id" property="catalogId"/>
        <result column="category_name" property="catalogName"/>
        <result column="icon" property="icon"/>
        <result column="sort_number" property="sortNumber"/>
        <collection property="services" column="id" select="selectAvailableServiceByCatalogId"/>
    </resultMap>

    <select id="selectAvailableServiceByCatalogId" parameterType="java.lang.Long"
        resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.MarketServiceVO">
        SELECT A.id, A.service_name, A.product_name, A.show_type, A.service_form serviceForm
        FROM sf_service_category A
                 LEFT JOIN sf_service_catalog_relation B ON B.service_id = A.id
        WHERE B.catalog_id = #{catalogId}
          AND A.publish_status = 'succeed'
          AND A.status = 'using'
        ORDER BY A.sort_number ASC, A.id ASC
    </select>

    <update id="reduceBalance">
        update biz_billing_account
        set balance=balance - #{subtractAmount}
        where id = #{bizBillingAccountId}
    </update>

    <update id="increaseBalance">
        update biz_billing_account
        set balance=balance + #{addAmount}
        where id = #{bizBillingAccountId}
    </update>

    <select id="countCustomers" parameterType="java.lang.Long" resultType="java.lang.Long">
        select COUNT(DISTINCT admin_sid) from biz_billing_account where id in (select
                        id as billing_account_id
                 from biz_billing_account
                 where salesmen_id = #{userSid}
                                                                               union
                                                                               select
                        billing_account_id
                 from biz_salesmen_account
                                                                               where salesmen_id = #{userSid})
    </select>
    <select id="getSimpleAccountAndDistributorInfo" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        bba.id ,
        bba.account_name ,
        bd.name distributor_name
        from
        biz_billing_account bba
        left join biz_distributor bd on
        bd.id = bba.distributor_id
        <where>
            1 = 1
            <if test="ids != null and ids.size() != 0">
                and bba.id in
                <foreach collection="ids" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getByDistributorNameAndIds" resultMap="BaseResultMap">
        select bba.id ,
        bba.account_name ,
        bba.admin_sid ,
        bba.balance ,
        bba.balance_cash ,
        bba.created_by ,
        bba.credit_line ,
        bba.discount ,
        bba.distributor_id ,
        bba.org_sid ,
        bba.remark ,
        bba.salesmen_id ,
        bba.ccsp_mac ,
        smo.org_name,
        smo1.org_name as distributor_name
        from
        biz_billing_account bba
        left join
        sys_m_org smo on
        smo.org_sid = bba.org_sid
        left join sys_m_org smo1 on smo1.org_sid = smo.parent_id
        where 1=1
        <if test="distributorName != null">
            <choose>
                <when test="distributorName == '直营'">
                    and smo1.org_name is null
                </when>
                <otherwise>
                    and smo1.org_name like concat('%',#{distributorName,jdbcType=VARCHAR},'%')
                </otherwise>
            </choose>
        </if>
        <if test="ids != null and ids.size() != 0">
            and bba.id in
            <foreach collection="ids" open="(" separator="," close=")" item="val">
                #{val,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectAccountInfoByCustomerName" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest">
        select
        <include refid="Base_Column_List"/>
        from
        biz_billing_account A
        join sys_m_user u on
        A.org_sid = u.org_sid
        where u.status ='1'
        <if test="customerName != null and ''!= customerName">
            and A.account_name LIKE concat('%', #{customerName}, '%')
        </if>
        group by A.id
    </select>

    <select id="selectAllDirectlyAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        biz_billing_account A
        where ISNULL(A.distributor_id)
    </select>

    <update id="updateSalesmenAccount">
        update biz_salesmen_account
        set salesmen_id = #{salesmenId}
        where billing_account_id = #{billingAccountId}
    </update>

    <select id="getSalesmenAccountBySalesmenId" resultType="java.lang.Integer">
        select count(salesmen_id) from biz_salesmen_account
        <if test="salesmenIds != null and salesmenIds.size() != 0">
            WHERE salesmen_id in
            <foreach collection="salesmenIds" item="salesmenId" separator="," open="(" close=")">
                #{salesmenId}
            </foreach>
        </if>
    </select>
    <select id="getBillingAccountByIdAndSalesmenId" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Alias_Column_List"/>   from biz_billing_account A
        where id=#{accountId}  and distributor_id=#{distributorId}
        <choose>
            <when test="salesmenId !=null">
                and salesmen_id=#{salesmenId}
            </when>
            <otherwise>
                and salesmen_id is null
            </otherwise>
        </choose>

    </select>

    <select id="getByEntityIdAndUserId" resultMap="BaseResultMap">
        select *
        from biz_billing_account
        where entity_id = #{entitySid}
        and admin_sid = #{userSid}
    </select>

    <select id="getFirst" resultMap="BaseResultMap">
        select * from biz_billing_account ORDER BY created_by DESC LIMIT 1
    </select>

    <select id="getBillingAccountByEntityId" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select * from biz_billing_account
        <where>
            <if test="entityId != null and entityId != ''">
                entity_id = #{entityId}
            </if>
        </where>
    </select>

    <select id="getAccountIdByEntityId" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select id from biz_billing_account
        <where>
            <if test="entityId != null and entityId != ''">
                entity_id = #{entityId}
            </if>
        </where>
    </select>

    <select id="getBillingAccountByEntityIdAndOrgSid" resultType="java.lang.Long">
        SELECT
            id
        FROM
            biz_billing_account
        <where>
            <if test="entityId != null and entityId != ''">
                entity_id = #{entityId}
            </if>
            <if test="orgSid != null and orgSid != ''">
               and  distributor_id = #{orgSid}
            </if>
        </where>
    </select>

    <select id="getAccountByEntityId" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        SELECT
        distinct
        <include refid="Alias_Column_List"/>
        FROM
            biz_billing_account A
        WHERE
            A.org_sid = #{orgSid}
            and A.entity_id = #{entityId}
    </select>

    <select id="findSelfAndSelfCustomer" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select * from biz_billing_account where (admin_sid = #{userSid} or salesmen_id = #{userSid}) and entity_id = #{entityId};
    </select>

    <select id="findSelfAndSelfCustomerAccountId" resultType="java.lang.Long">
        select id from biz_billing_account where (admin_sid = #{userSid} or salesmen_id = #{userSid}) and entity_id = #{entityId};
    </select>




    <update id="updateFreezeAccount">
        update biz_billing_account
        set status = 'normal',
            unfreeze_type = '0'
        where id = #{accountId}
    </update>

    <update id="updateFrozenAccount">
        update biz_billing_account
        set status = 'freeze',
            unfreeze_type = IFNULL(#{unfreezeType},'0')
        where id = #{accountId}
    </update>

    <update id="updateEndTime">
        update biz_billing_account
        set end_time = #{bizBillingAccount.endTime},
        action_strategy = #{bizBillingAccount.actionStrategy}
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="getByCategoryId" resultMap="BaseResultMap">
        select acc.*
        from sf_service_category cate
                 left join sys_bss_entity ent on ent.id = cate.entity_id
                 left join biz_billing_account acc on acc.entity_id = ent.id
        where cate.id = #{categoryId}
          and acc.admin_sid = #{userSid}
    </select>




    <select id="getByAdminSid" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select b.id,
               b.org_sid,
               b.admin_sid,
               b.account_name,
               b.balance,
               b.discount,
               b.created_by,
               b.created_dt,
               b.updated_by,
               b.updated_dt,
               b.version,
               b.remark,
               b.balance_cash,
               b.credit_line,
               b.ccsp_mac,
               b.status,
               b.end_time,
               b.action_strategy,
               s.freeze_status,
               s.unfreeze_type,
               s.account,
               t3.name as distributor_name,
               ent.id as entity_id,
               ent.name as entity_name
        from biz_billing_account b
                     left join sys_m_user s on s.user_sid = b.admin_sid
                     left join biz_distributor t3 on t3.id = b.distributor_id
                     left join sys_bss_entity ent on ent.id=b.entity_id
        where b.admin_sid = #{userSid}
    </select>


    <update id="updateFreezeAccount">
        update biz_billing_account
        set status = 'normal',
            unfreeze_type = '0'
        where id = #{accountId}
    </update>

    <update id="updateFrozenAccount">
        update biz_billing_account
        set status = 'freeze',
            unfreeze_type = IFNULL(#{unfreezeType},'0')
        where id = #{accountId}
    </update>

    <select id="getByCategoryId" resultMap="BaseResultMap">
        select acc.*
        from sf_service_category cate
                 left join sys_bss_entity ent on ent.id = cate.entity_id
                 left join biz_billing_account acc on acc.entity_id = ent.id
        where cate.id = #{categoryId}
          and acc.admin_sid = #{userSid}
    </select>



    <select id="getFirst" resultMap="BaseResultMap">
        select * from biz_billing_account ORDER BY created_by DESC LIMIT 1
    </select>

    <select id="getByOrgSids"
        resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select b.id,
               b.org_sid,
               b.admin_sid,
               b.account_name,
               b.balance,
               b.discount,
               b.created_by,
               b.created_dt,
               b.updated_by,
               b.updated_dt,
               b.version,
               b.remark,
               b.balance_cash,
               b.status,
        b.credit_line,
        b.ccsp_mac,
               s.freeze_status,
               s.unfreeze_type,
               s.account,
               t3.name as distributor_name,
               ent.id as entity_id,
               ent.name as entity_name
        from biz_billing_account b
                 left join sys_m_user s on s.user_sid = b.admin_sid
                 left join biz_distributor t3 on t3.id = b.distributor_id
                 left join sys_bss_entity ent on ent.id=b.entity_id
        where b.org_sid in
        <foreach collection="orgSids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>,
        t3.name as distributor_name
        from biz_billing_account A
        left join biz_distributor t3 on t3.id = A.distributor_id
        where A.org_sid = #{orgId} and A.entity_id = #{entityId}
    </select>

    <select id="selectAccountsByOrgId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>,
        t3.name as distributor_name
        from biz_billing_account A
        left join biz_distributor t3 on t3.id = A.distributor_id
        where A.org_sid = #{orgId}
    </select>

    <select id="getByOrgSid" resultMap="BaseResultMap">
        select b.id,
        b.org_sid,
        b.admin_sid,
        b.account_name,
        b.balance,
        b.discount,
        b.created_by,
        b.created_dt,
        b.updated_by,
        b.updated_dt,
        b.version,
        b.remark,
        b.balance_cash,
        b.status,
        b.end_time,
        b.action_strategy,
        b.credit_line,
        b.ccsp_mac,
        b.distributor_id,
        b.salesmen_id,
        s.freeze_status,
        s.unfreeze_type,
        s.account,
        t3.name as distributor_name,
        ent.id as entity_id,
        ent.name as entity_name
        from biz_billing_account b
        left join sys_m_user s on s.user_sid = b.admin_sid
        left join biz_distributor t3 on t3.id = b.distributor_id
        left join sys_bss_entity ent on ent.id=b.entity_id
        where b.org_sid = #{orgSid}
    </select>
    <select id="selectAccountIsExist" resultType="java.lang.Integer">
        SELECT count(1)
        from biz_billing_account bba
                 LEFT JOIN sys_m_user smu on bba.admin_sid = smu.user_sid
        where bba.distributor_id = #{orgSid}
          and smu.account = #{tenant}
    </select>
    <select id="getBillingAccountByUserIdAndEntitytId"
        resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select b.id,
               b.org_sid,
               b.admin_sid,
               b.account_name,
               b.balance,
               b.discount,
               b.credit_line,
               b.created_by,
               b.created_dt,
               b.updated_by,
               b.updated_dt,
               b.version,
               b.remark,
               b.balance_cash,
               b.ccsp_mac,
               s.freeze_status,
               s.unfreeze_type,
               s.account,
               t3.name as distributor_name
        from biz_billing_account b
                 left join sys_m_user s on s.user_sid = b.admin_sid
                 left join biz_distributor t3 on t3.id = b.distributor_id
        where b.admin_sid = #{userSid} and b.entity_id =#{entityId}
    </select>
    <select id="selectByParamsForCollector"
        resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List2"/>
        from biz_billing_account
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="getByEntityIdAndOrgSid" resultMap="BaseResultMap">
        select *
        from biz_billing_account
        where entity_id = #{entitySid}
          and org_sid = #{orgSid}
    </select>

    <select id="selectAccountByIds"
        resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount">
        select bba.id, bd.name distributor_name, bba.entity_name, bba.entity_id  from biz_billing_account bba
        left join biz_distributor bd on bd.id = bba.distributor_id
        where bd.name is not null and bba.id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectDiscountByParamsExport" resultMap="BaseResult">
        select
        distinct
        <include refid="Alias_Column_List"/>
        from biz_billing_account A
        INNER JOIN biz_discount B ON A.id = B.user_sid
        <include refid="Example_Where_Clause"/>
        group by A.id
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <select id="selectByOrgIdAndEntity" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>,
        t3.name as distributor_name
        from biz_billing_account A
        left join biz_distributor t3 on t3.id = A.distributor_id
        where A.org_sid = #{orgId}
        limit 1
    </select>

</mapper>
