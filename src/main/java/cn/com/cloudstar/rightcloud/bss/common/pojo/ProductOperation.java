/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/10 22:26
 */
@Data
public class ProductOperation implements Serializable {
    private String applyType;

    private String orderType;

    private String projectId;

    private Long couponSid;

    private Long clusterId;

    private List<ProductInfo> productInfo;

    private BigDecimal serviceAmount;

    private BigDecimal platformDiscount;

    private Long accountId;

    private User consumer;

    private BigDecimal unsubAmount;

    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    @ApiModelProperty("下单用户ID")
    private Long userSid;

    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    @ApiModelProperty("HPC资源原始状态")
    private String resourceOriginStatus;

    @ApiModelProperty("收费规则")
    private String chargingType;

    /**
     * 资源配置
     */
    @ApiModelProperty(value = "申请单配置", name = "申请单配置")
    private String resourceInfo;

    public BigDecimal getServiceAmount() {
        return Objects.isNull(this.serviceAmount) ? BigDecimal.ZERO : this.serviceAmount;
    }

    public BigDecimal getPlatformDiscount() {
        return Objects.isNull(this.platformDiscount) ? BigDecimal.ONE : this.platformDiscount;
    }

}
