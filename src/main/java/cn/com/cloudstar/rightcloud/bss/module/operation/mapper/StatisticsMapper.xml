<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.operation.mapper.StatisticsMapper">

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            1 = 1 and price_type = 'resource'
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.startTime != null">
                 AND pay_time &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND pay_time &lt;= #{condition.endTime}
            </if>
        </trim>
    </sql>
    <resultMap id="OrderStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.OrderVO">
        <result column="total_count" property="totalCount" jdbcType="BIGINT" />
        <result column="total_payed_amount" property="totalPayedAmount" jdbcType="DECIMAL" />
        <result column="total_coupon_amount" property="totalCouponAmount" javaType="DECIMAL"/>
        <result column="current_month_count" property="currentMonthCount" jdbcType="BIGINT" />
        <result column="current_month_amount" property="currentMonthAmount" jdbcType="DECIMAL" />
        <result column="current_coupon_amount" property="currentCouponAmount" javaType="DECIMAL"/>
    </resultMap>

    <select id="selectOrderStatistics" resultMap="OrderStatisticsMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        SELECT
            ROUND(  SUM(total_count), 5 ) AS total_count,
            ROUND(  SUM(total_payed_amount), 5 ) AS total_payed_amount,
            ROUND(  SUM(total_coupon_amount), 5 ) AS total_coupon_amount,
            ROUND(  SUM(current_month_count), 5 ) AS current_month_count,
            ROUND(  SUM(current_month_amount), 5 ) AS current_month_amount,
            ROUND(  SUM(current_coupon_amount), 5 ) AS current_coupon_amount
        FROM
            (
                SELECT
                    COUNT(*) AS total_count,
                    SUM(TRUNCATE(final_cost,2)) AS total_payed_amount,
                    SUM(coupon_discount) AS total_coupon_amount,
                    0 AS current_month_count,
                    0 AS current_month_amount,
                    0 AS current_coupon_amount
                FROM
                    service_order
                WHERE
                status = 'completed'
               <if test="condition.accountIds != null and condition.accountIds.size() > 0">
                 and biz_billing_account_id in
               <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                #{accountId}
               </foreach>
               </if>
                UNION ALL
                SELECT
                    0 AS total_count,
                    0 AS total_payed_amount,
                    0 AS total_coupon_amount,
                    COUNT(*) AS current_month_count,
                    SUM(TRUNCATE(a.final_cost,2)) AS current_month_amount,
                    SUM(a.coupon_discount) AS current_coupon_amount
                FROM
                    service_order  a
        WHERE
                 a.type != 'service-publish'
                 and a.status IN ('completed' ,'renew_success')
                         AND (a.created_dt BETWEEN date_format(now(),'%Y-%m-01 00:00:00')
                         AND date_format(LAST_DAY(NOW()),'%Y-%m-%d 23:59:59'))
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and biz_billing_account_id in
            <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
        </if>
            ) t
    </select>

    <resultMap id="RechargeStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.RechargeVO">
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="current_month_amount" property="currentMonthAmount" jdbcType="DECIMAL" />
        <result column="onLineAmount" property="onLineAmount" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectRechargeStatistics" resultMap="RechargeStatisticsMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        SELECT
        	ROUND( SUM(total_amount), 5 ) AS total_amount,
        	ROUND( SUM(current_month_amount), 5 ) AS current_month_amount,
        	ROUND( SUM(onLineAmount), 5 ) as onLineAmount
        FROM
            (
                SELECT
                    SUM(amount_deposit) AS total_amount,
                    0 AS current_month_amount,
                    0 AS onLineAmount
                FROM
                    biz_deposit
                    WHERE pay_status = 1
                   <if test="condition.accountIds != null and condition.accountIds.size() > 0">
                        and account_sid in
                        <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                            #{accountId}
                        </foreach>
                   </if>
                UNION ALL
                SELECT
                    0 AS total_amount,
                    SUM(amount_deposit) AS current_month_amount,
                    0 AS onLineAmount
                FROM
                    biz_deposit
                WHERE (pay_time between date_format(now(),'%Y-%m-01 00:00:00')
                        and date_format(LAST_DAY(NOW()),'%Y-%m-%d 23:59:59'))
                    and pay_status = 1
                   <if test="condition.accountIds != null and condition.accountIds.size() > 0">
                       and account_sid in
                   <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                       #{accountId}
                   </foreach>
                   </if>
                UNION ALL
                    SELECT
                    0 AS total_amount,
                    0 AS current_month_amount,
                    SUM(amount_deposit) AS onLineAmount
                    FROM
                    biz_deposit left join biz_billing_account b on b.id = account_sid
                    WHERE channel in('alipay','wechatPay','unionpay')
                        and pay_status = 1
                   <if test="condition.accountIds != null and condition.accountIds.size() > 0">
                         and account_sid in
                    <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                         #{accountId}
                    </foreach>
                   </if>
            ) t
    </select>

    <resultMap id="BillStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.BillVO">
        <result column="total_bill_amount" property="totalBillAmount" jdbcType="DECIMAL" />
        <result column="current_bill_amount" property="currentBillAmount" jdbcType="DECIMAL" />
        <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectBillStatistics" resultMap="BillStatisticsMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        SELECT
            (
                SELECT
                    sum(pretax_amount) as total_bill_amount
                FROM
                    instance_gaap_cost
                <where>
                    <if test="condition.orgSid != null">
                        org_sid in(
                        select org_sid from sys_m_org where org_type = 'company'
                        and (org_sid = #{condition.orgSid} OR
                        tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
                    </if>
                </where>
            ) total_bill_amount,
            (
                SELECT
                    sum(pretax_amount) as current_bill_amount
                FROM
                    instance_gaap_cost
                WHERE
                    (pay_time between date_format(now(),'%Y-%m-01 00:00:00')
                        and date_format(LAST_DAY(NOW()),'%Y-%m-%d 23:59:59'))
                <if test="condition.orgSid != null">
                    and org_sid in(
                    select org_sid from sys_m_org where org_type = 'company'
                    and (org_sid = #{condition.orgSid} OR
                    tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
                </if>
            ) current_bill_amount,
            (
                SELECT
                    sum(deposite_amount) as invoice_amount
                FROM
                    biz_invoice m
                INNER JOIN
                    (select id,org_sid from  biz_billing_account) n on m.account_id = n.id
                WHERE invoice_status = 'done'
                <if test="condition.orgSid != null">
                    and m.account_id in (
                    select id from biz_billing_account where org_sid in(
                    select org_sid from sys_m_org where org_type = 'company'
                    and (org_sid = #{condition.orgSid} OR
                    tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' ))))
                </if>
            ) invoice_amount
    </select>

    <resultMap id="ConsumerStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ConsumeVO">
        <result column="org_sid" property="orgSid" jdbcType="BIGINT" />
        <result column="org_name" property="orgName" jdbcType="VARCHAR" />
        <result column="parent_id" property="parentId" jdbcType="BIGINT" />
        <result column="recharge_amount" property="rechargeAmount" jdbcType="DECIMAL" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="bill_amount" property="billAmount" jdbcType="DECIMAL" />
        <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectConsumeStatistics" resultMap="ConsumerStatisticsMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        SELECT c.org_sid,
               c.org_name,
               c.parent_id,
               IFNULL(SUM(a.rechargeAmount), 0) recharge_amount,
               IFNULL(SUM(a.orderAmount), 0) order_amount,
               IFNULL(SUM(a.billAmount), 0) bill_amount,
               IFNULL(SUM(a.invoiceAmount), 0) invoice_amount
        FROM (
            SELECT org_sid,
                   amount_deposit rechargeAmount,
                   0 orderAmount,
                   0 billAmount,
                   0 invoiceAmount
            FROM biz_deposit
            WHERE pay_status = 1
            and channel in <foreach collection="condition.channels" item="channel" open="(" close=")" separator=",">
         #{channel}
                </foreach>
           <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and account_sid in
            <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
           </if>
            UNION ALL
            SELECT org_sid,
                   0 rechargeAmount,
                   final_cost orderAmount,
                   0 billAmount,
                   0 invoiceAmount
            FROM service_order
            WHERE type != 'service-publish'
            and status = 'completed'
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and biz_billing_account_id in
            <foreach collection="condition.accountIds"  open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
        </if>
            UNION ALL
            SELECT org_sid,
                   0 rechargeAmount,
                   0 orderAmount,
                   0 billAmount,
                   0 invoiceAmount
            FROM instance_gaap_cost
            UNION ALL
            SELECT org_sid,
                   0 rechargeAmount,
                   0 orderAmount,
                   0 billAmount,
                   0 invoiceAmount
            FROM (select account_id, deposite_amount, updated_dt from biz_invoice where INVOICE_STATUS = 'done' ) m
                  INNER JOIN (select id,org_sid from  biz_billing_account) n on m.account_id = n.id
            ) a
        LEFT JOIN
            (SELECT org_sid,
                    org_name,
                    parent_id
               FROM sys_m_org) c
                 ON a.org_sid = c.org_sid
        where c.org_sid is not null
            <if test="condition.orgSid != null">
                and c.org_sid in(select org_sid from sys_m_org where  (org_sid = #{condition.orgSid} OR
                tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
            </if>
        GROUP BY c.org_sid, c.org_name, c.parent_id
    </select>

    <resultMap id="ResourceExpireMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ResourceVO">
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="selectResourceExpireTop" resultMap="ResourceExpireMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        a.name,
        a.type,
        a.end_time
        from
        (SELECT ta.name,
               ta.type,
               ta.end_time
        FROM (
            SELECT a.org_sid,
                   a.instance_name as name,
                   'instance' as type,
                   a.end_time
            FROM res_vm a
            where a.status not in ('deleted', 'creating', 'create_failure', 'pending', 'deleting')
        ) ta
        where ta.end_time is not null and ta.end_time > now()
        <if test="condition.orgSid != null">
            and ta.org_sid in(select org_sid from sys_m_org where org_type = 'company'
            and (org_sid = #{condition.orgSid} OR
            tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
        </if>
        union all
        select
        w.product_name name, w.product_type type, w.end_time
        from
        sf_product_resource w
        join service_order_resource_ref sorr on
        w.id = sorr.resource_id
        and sorr.`type` = w.product_type
        join service_order_detail sod on
        sod.id = sorr.order_detail_id
        where
        w.status = 'normal' AND sod.charge_type = 'PrePaid'
        and w.end_time &lt; now()
        <if test="condition.orgSid != null">
            and w.org_sid in(select org_sid from sys_m_org where org_type = 'company'
            and (org_sid = #{condition.orgSid} OR
            tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
        </if>)a
        order by a.end_time asc limit #{condition.pageSize}
    </select>

    <resultMap id="ConsumerTrendMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ConsumeTrendVO">
        <result column="period" property="period" jdbcType="VARCHAR" />
        <result column="recharge_amount" property="rechargeAmount" jdbcType="DECIMAL" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="bill_amount" property="billAmount" jdbcType="DECIMAL" />
        <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
        <result column="data_time" property="dataTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="selectConsumeTrend" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria"
            resultMap="ConsumerTrendMap">
        SELECT org_sid,
        data_time,
        IFNULL(SUM(a.rechargeAmount),0) recharge_amount,
        IFNULL(SUM(a.orderAmount),0) order_amount,
        IFNULL(SUM(a.billAmount),0) bill_amount,
        IFNULL(SUM(a.invoiceAmount),0) invoice_amount
        FROM (
        SELECT org_sid,
        amount_deposit rechargeAmount,
        0 orderAmount,
        0 billAmount,
        0 invoiceAmount,
        date_format(pay_time,'%Y-%m-%d') AS data_time
        FROM biz_deposit
        WHERE pay_status = 1
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and account_sid in
            <foreach collection="condition.accountIds" open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
        </if>
        UNION ALL
        SELECT org_sid,
        0 rechargeAmount,
        final_cost orderAmount,
        0 billAmount,
        0 invoiceAmount,
        date_format(created_dt,'%Y-%m-%d') AS data_time
        FROM service_order
        WHERE type != 'service-publish' and status in ('completed','renew_success')
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and biz_billing_account_id in
            <foreach collection="condition.accountIds" open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
        </if>
        UNION ALL
        SELECT org_sid,
        0 rechargeAmount,
        0 orderAmount,
        0 billAmount,
        0 invoiceAmount,
        date_format(pay_time,'%Y-%m-%d') AS data_time
        FROM instance_gaap_cost
        UNION ALL
        SELECT org_sid,
        0 rechargeAmount,
        0 orderAmount,
        0 billAmount,
        m.invoiceAmount,
        date_format(updated_dt,'%Y-%m-%d') AS data_time
        FROM (select account_id, deposite_amount AS invoiceAmount, updated_dt from biz_invoice where INVOICE_STATUS =
        'done' and entity_id = #{condition.entityId}) m
        INNER JOIN (select id,org_sid from biz_billing_account where entity_id = #{condition.entityId}) n on m.account_id = n.id
        where 1=1
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            and m.account_id in
            <foreach collection="condition.accountIds" open="(" separator="," close=")" item="accountId">
                #{accountId}
            </foreach>
        </if>
        ) a
        <where>
            data_time is not null
            <if test="condition.orgSid != null">
                and a.org_sid in(select org_sid from sys_m_org where org_type in ('company','project')
                and (org_sid = #{condition.orgSid} OR
                tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
            </if>
        </where>
        GROUP BY org_sid,data_time
    </select>

    <select id="countExpireResource" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="int">
        select count(*) from (
        select
        TIMESTAMPDIFF(DAY, now(), A.end_time) as countdown,A.org_sid, A.charge_type, A.status
        from res_all A
        ) H
        where H.countdown &lt;= 30 and 0 &lt;= H.countdown
        <if test="condition.orgSid != null">
            AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
            CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = H.org_sid))
        </if>
        <if test="condition.projectIds != null and condition.projectIds.size  > 0">
            and H.org_sid in
            <foreach collection="condition.projectIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="condition.chargeType != null">
            and H.charge_type = #{condition.chargeType}
        </if>
        <if test="condition.statusIn != null">
            and H.status in
            <foreach item="item" index="index" collection="condition.statusIn"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.orgSid != null">
            and H.org_sid in(select org_sid from sys_m_org where org_type = 'company'
            and (org_sid = #{condition.orgSid} OR
            tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
        </if>
    </select>

    <select id="countServiceOrder" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="int">
        SELECT
        count(1)
        FROM service_order so
        WHERE status = 'pending'
        AND type = 'apply'
        AND `org_sid`
        <if test="condition.orgSid != null">
            AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
            CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = so.org_sid))
        </if>
        <if test="condition.projectIds != null and condition.projectIds.size  > 0">
            and so.org_sid in
            <foreach collection="condition.projectIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="countWorkOrder" resultType="java.lang.Integer" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select count(*) FROM sys_m_ticket smt
        left join sys_m_user smu
        on smt.ticket_user_id = smu.user_sid
        where smt.deal_status in ('01','02')
        <if test="condition.orgSid != null" >
            and smu.org_sid = #{condition.orgSid}
        </if>
    </select>

    <resultMap id="CouponStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.CouponStatisticsVO">
        <result column="coupon_sid" property="couponSid" jdbcType="BIGINT" />
        <result column="discount_used" property="discountUsed" jdbcType="DECIMAL" />
        <result column="updated_dt" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="org_sid" property="orgSid" jdbcType="BIGINT" />
    </resultMap>

    <select id="selectCouponStatistic" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria"  resultMap="CouponStatisticsMap">
        SELECT  m.coupon_sid, m.discount_used, m .updated_dt,m.org_sid,m.`status`
        FROM
        (   SELECT B.coupon_sid, IFNULL(B.discount_used,0) discount_used,B.account_id, B.coupon_status as status, B.updated_dt ,n.org_sid
        FROM   biz_coupon_account  B
        INNER JOIN biz_billing_account n  ON  B.account_id = n.id   ) m
        WHERE
        status = 'used'
        <if test="condition.orgSid != null">
            and org_sid in(select org_sid from sys_m_org where org_type = 'company'
            and (org_sid = #{condition.orgSid} OR
            tree_path LIKE concat( '/%', #{condition.orgSid}, '/%' )))
        </if>
    </select>

    <select id="countCoupon" parameterType="java.lang.Long" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.WaitingCenterVO">
        select max(couponCount) couponCount, max(expireCouponCount) expireCouponCount
        from (
            select count(*) couponCount,
                   0 expireCouponCount
                FROM biz_coupon a left join biz_coupon_account b on a.coupon_sid = b.coupon_sid
            where b.account_id = #{accountId} and coupon_status = 'unused'
            union all
            select 0 couponCount,
                   count(*) expireCouponCount
                FROM biz_coupon a left join biz_coupon_account b on a.coupon_sid = b.coupon_sid
            where b.account_id = #{accountId} and coupon_status = 'unused' and a.end_time between now() and date_add(NOW(),INTERVAL 7 DAY)

            ) t
    </select>

    <select id="selectUserCurrentConsume" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(pretax_amount), 0) as currentMonthAmount
        FROM
            instance_gaap_cost A
        WHERE
            (pay_time between date_format(now(),'%Y-%m-01 00:00:00')
            AND date_format(LAST_DAY(NOW()),'%Y-%m-%d 23:59:59'))
            <if test="condition.orgSid != null">
              AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
              CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = A.org_sid))
            </if>
            <if test="condition.projectIds != null and condition.projectIds.size  > 0">
              and A.org_sid in
              <foreach collection="condition.projectIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
              </foreach>
            </if>
            <if test="condition.orgSid != null">
                and EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid}
                OR tree_path LIKE concat('/%', #{condition.orgSid}, '/%')))
            </if>
            <if test="condition.subscriptionType != null and condition.subscriptionType == 'PayAsYouGo'">
                and (A.bill_type = 'PayAsYouGoBill' or A.is_public = 0)
            </if>
    </select>

    <select id="selectProjectVmCount" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectOverviewVO">
        SELECT
            count(*) hostCount,
            sum(ifnull(cpu, 0)) cpuCount,
            sum(ifnull(memory, 0))/1024 memory
        FROM
            res_vm
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.statusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.serverTypeIn != null and condition.serverTypeIn.size() > 0">
                AND server_type in
                <foreach item="item" index="index" collection="condition.serverTypeIn"
                  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectProjectVdCount" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectOverviewVO">
        select
            sum(a.mount) mount,
            sum(b.unmount) unmount
        from (select
        sum(allocate_disk_size) mount
        from
        res_vd
        where (res_vm_id is not null and res_vm_id != '')
        <if test="condition.orgSid != null">
            AND org_sid = #{condition.orgSid}
        </if>
        <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
            and status not in
            <foreach collection="condition.statusNotIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) a,
        (select
        sum(allocate_disk_size) unmount
        from
        res_vd
        where (res_vm_id is null or res_vm_id = '')
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.statusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>) b

    </select>

    <select id="selectProjectFloatingIpCount" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectOverviewVO">
        SELECT
            count(*) as totalEip
        FROM
            res_floating_ip
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.statusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectProjectLbCount" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectOverviewVO">
        SELECT
            count(*) as totalLb
        FROM
            res_loadbalance
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
        </where>
    </select>

    <resultMap id="ProjectStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectStatisticsVO">
        <result column="data_time" property="dataTime" jdbcType="VARCHAR" />
        <result column="meterage" property="meterage" jdbcType="DECIMAL" />
        <result column="meter_type" property="meterType" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectProjectStatistics" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultMap="ProjectStatisticsMap">
        select data_time,
               meterage,
               meter_type
        from (SELECT
            date_format(created_dt,'%Y-%m-%d') AS data_time,
            count(*) meterage,
            'compute' as meter_type
        FROM
            res_vm
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.vmStatusNotIn != null and condition.vmStatusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.vmStatusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vmServerTypeIn != null and condition.vmServerTypeIn.size() > 0">
                AND server_type in
                <foreach item="item" index="index" collection="condition.vmServerTypeIn"
                  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
          <if test="condition.startTime != null">
            AND created_dt &gt;= #{condition.startTime}
          </if>
          <if test="condition.endTime != null">
            AND created_dt &lt;= #{condition.endTime}
          </if>
        </where>
        group by data_time,meter_type
        union all
        select
            date_format(created_dt,'%Y-%m-%d') AS data_time,
            sum(allocate_disk_size) meterage,
            'disk' as meter_type
        from
            res_vd
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.vdStatusNotIn != null and condition.vdStatusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.vdStatusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
          <if test="condition.startTime != null">
            AND created_dt &gt;= #{condition.startTime}
          </if>
          <if test="condition.endTime != null">
            AND created_dt &lt;= #{condition.endTime}
          </if>
        </where>
        group by data_time,meter_type
        union all
        SELECT
            date_format(created_dt,'%Y-%m-%d') AS data_time,
            count(*) meterage,
            'floatingIp' as meter_type
        FROM
            res_floating_ip
        <where>
            <if test="condition.orgSid != null">
                AND org_sid = #{condition.orgSid}
            </if>
            <if test="condition.ipStatusNotIn != null and condition.ipStatusNotIn.size() > 0">
                and status not in
                <foreach collection="condition.ipStatusNotIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
          <if test="condition.startTime != null">
            AND created_dt &gt;= #{condition.startTime}
          </if>
          <if test="condition.endTime != null">
            AND created_dt &lt;= #{condition.endTime}
          </if>
        </where>
        group by data_time,meter_type
        ) c

    </select>

    <resultMap id="ResBillStatisticsMap"
               type="cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.ProjectStatisticsVO">
        <result column="data_time" property="dataTime" jdbcType="VARCHAR" />
        <result column="meterage" property="meterage" jdbcType="DECIMAL" />
        <result column="meter_type" property="meterType" jdbcType="VARCHAR" />
        <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectResBillStatistics" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultMap="ResBillStatisticsMap">
       SELECT
          data_time,
          amount,
          meter_type,
          product_code
        FROM
          (
          SELECT
            date_format(pay_time, '%Y-%m-%d' ) AS data_time,
            sum( pretax_amount ) amount,
            bill_type AS meter_type,
            'compute' product_code
          FROM
                instance_gaap_cost
            where product_code = 'ecs'
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.startTime != null">
                AND pay_time &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND pay_time &lt;= #{condition.endTime}
            </if>

          GROUP BY
            data_time,
            meter_type,
            product_code
        UNION ALL
          SELECT
            date_format( pay_time, '%Y-%m-%d' ) AS data_time,
            sum( pretax_amount ) amount,
            ifnull(type_name,'默认类型') AS meter_type,
            'disk' product_code
          FROM
            ( SELECT pay_time, pretax_amount, resource_id, product_code FROM instance_gaap_cost
            <include refid="Example_Where_Clause"/>
            ) m
            INNER JOIN (
            SELECT
              rv.res_vd_sid AS resource_id,
              rvy.type_name,
              'disk' AS product_code
            FROM
              ( SELECT cloud_env_id, res_volume_type_id, res_vd_sid FROM res_vd
                where status not in ('creating', 'create_failure')
                ) rv
              LEFT JOIN res_volume_type rvy ON rv.cloud_env_id = rvy.cloud_env_id
              AND rv.res_volume_type_id = rvy.id
            ) n ON m.resource_id = n.resource_id

          GROUP BY
            data_time,
            meter_type,
            product_code
        UNION ALL
          SELECT
            date_format( pay_time, '%Y-%m-%d' ) AS data_time,
            sum( pretax_amount ) amount,
            ifnull(configuration,'1M') AS meter_type,
            'floatingIp' product_code
          FROM
            ( SELECT pay_time, pretax_amount, resource_id, product_code FROM instance_gaap_cost
            <include refid="Example_Where_Clause"/>
            ) igt
            INNER JOIN ( SELECT id AS resource_id, ifnull( concat( bandwidth, 'M' ), ip ) AS configuration, 'eip' AS product_code FROM res_floating_ip
                where status not in ('creating', 'create_failure')
            ) ip ON igt.resource_id = ip.resource_id
          GROUP BY
            data_time,
            meter_type,
          product_code
          ) t
    </select>
</mapper>
