/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.DescribeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.CompanyOpenApiResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.CompanyOrgOpenApiResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;

/**
 * 企业管理
 *
 * <AUTHOR>
 */
@Api(tags = "账户管理")
@RestController
@RequestMapping("/company")
@Slf4j
public class BillingAccountOpenApiController {

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    BizBillingAccountMapper bizBillingAccountMapper;

    /**
     * 账户列表查询请求体
     */
    @ApiOperation(httpMethod = "GET", value = "获取企业列表")
    @GetMapping()
    public IPage<CompanyOpenApiResponse> findBillingAccounts(DescribeBillingAccountRequest request) {
        request.setEntityId(1L);
        IPage<BizBillingAccount> billingAccountList = bizBillingAccountService.getBillingAccountOpenApiList(request);
        IPage<CompanyOpenApiResponse> responseIPage = BeanConvertUtil.convertPage(
                billingAccountList,
                CompanyOpenApiResponse.class);
        return responseIPage;
    }

    /**
     * 根组织列表查询请求体
     */
    @ApiOperation(httpMethod = "GET", value = "获取企业列表")
    @GetMapping("/org")
    public IPage<CompanyOrgOpenApiResponse> findBillingAccountOrg(DescribeBillingAccountRequest request) {
        request.setEntityId(1L);
        request.setNeqOrgId(0L);
        IPage<BizBillingAccount> billingAccountList = bizBillingAccountService.getBillingAccountOpenApiList(request);
        IPage<CompanyOrgOpenApiResponse> responseIPage = BeanConvertUtil.convertPage(
                billingAccountList,
                CompanyOrgOpenApiResponse.class);
        return responseIPage;
    }

    /**
     * 获取企业详情
     */
    @ApiOperation(httpMethod = "GET", value = "获取企业详情")
    @GetMapping("/{id}")
    public CompanyOpenApiResponse getBillingAccountDetail(@PathVariable("id")
                                                          @ApiParam(value = "账户ID", type = "Long", required = true) Long id) {
        BizBillingAccount billingAccountDetail = bizBillingAccountService.getBillingAccountDetail(id);
        billingAccountDetail.setCompanyId(billingAccountDetail.getId());
        billingAccountDetail.setOrgId(billingAccountDetail.getOrgSid());
        billingAccountDetail.setCompanyName(billingAccountDetail.getAccountName());
        return BeanConvertUtil.convert(billingAccountDetail, CompanyOpenApiResponse.class);
    }

}
