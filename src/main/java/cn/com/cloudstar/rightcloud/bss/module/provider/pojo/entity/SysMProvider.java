package cn.com.cloudstar.rightcloud.bss.module.provider.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.*;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;


@Data
@TableName("sys_m_provider")
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt =true)
@EncryptDecryptClass
public class SysMProvider implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 企业id(sys_m_org表主键id)
     */
    private Long companyId;

    /**
     * 所属用户id(sys_m_user表主键id)
     */
    private Long ownerId;

    /**
     * 状态（申请中/已入驻/申请失败/注销中/已注销）
     */
    private String status;

    /**
     * 审批信息
     */
    private String statusInfo;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 预留字段，目前仅支持中国大陆
     */
    private String region;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 法定代表人身份证号码
     */
    private String legalPersonCard;

    /**
     * 电话
     */
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String contactPhone;

    /**
     * 统一社会信用代码
     */
    private String socialCode;

    /**
     * 注册资金（万）
     */
    private String registeredCapital;

    /**
     * 注册地址
     */
    private String address;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 银行账号
     */
    private Long bankAccount;

    /**
     * 银行账户
     */
    private String bankAccountName;

    /**
     * 注册时间
     */
    private Date setUpDt;

    /**
     * 营业执照图片路径
     */
    private String businessLicenseUrl;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新者
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;
}
