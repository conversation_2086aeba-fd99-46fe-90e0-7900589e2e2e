package cn.com.cloudstar.rightcloud.bss.module.provider.pojo.request;

import java.util.Date;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;

@Data
public class SysMProviderCreateRequest {
    /**
     * 供应商ID
     */
    private Long id;

    /**
     * 企业名称
     */
    @NotEmpty(message = "企业名称不能为空")
    private String companyName;

    /**
     * 预留字段，目前仅支持中国大陆
     */
    @NotEmpty(message = "认证地区不能为空")
    private String region;

    /**
     * 法定代表人
     */
    @NotEmpty(message = "法定代表人不能为空")
    private String legalPerson;

    /**
     * 法定代表人身份证号码
     */
    @NotEmpty(message = "法定代表人身份证号码不能为空")
    private String legalPersonCard;

    /**
     * 电话
     */
    @NotEmpty(message = "电话不能为空")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$"
        , message = "请输入正确的手机号码")
    private String contactPhone;

    /**
     * 统一社会信用代码
     */
    @NotEmpty(message = "统一社会信用代码不能为空")
    private String socialCode;

    /**
     * 注册资金（万）
     */
    @NotEmpty(message = "注册资金不能为空")
    private String registeredCapital;

    /**
     * 注册地址
     */
    @NotEmpty(message = "注册地址不能为空")
    private String address;

    /**
     * 开户行
     */
    @NotEmpty(message = "开户行不能为空")
    private String bank;

    /**
     * 银行账号
     */
    @NotNull(message = "银行账号不能为空")
    private Long bankAccount;

    /**
     * 银行账户
     */
    @NotEmpty(message = "银行账户不能为空")
    private String bankAccountName;

    /**
     * 注册时间
     */
    @NotNull(message = "注册时间不能为空")
    private Date setUpDt;

    /**
     * 营业执照图片
     */
    @NotEmpty(message = "营业执照图片路径不能为空")
    private String businessLicenseUrl;

}
