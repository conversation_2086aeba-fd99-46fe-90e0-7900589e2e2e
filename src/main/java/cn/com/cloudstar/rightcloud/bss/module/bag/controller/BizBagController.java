package cn.com.cloudstar.rightcloud.bss.module.bag.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.OrgType;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.UserGroupMapper;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.OfflineBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.OnlineBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.SubscribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBagAndSpecResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagResponseVO;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagSpecService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bag.util.ValidUtil;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;

/**
 * 套餐包管理
 *
 * <AUTHOR>
 * @since 2023-2-15
 */
@RestController
@RequestMapping("/bag")
@Api("套餐包管理")
public class BizBagController {

    public static final String ONE = "1";

    @Autowired
    private BizBagService bizBagService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private UserGroupMapper userGroupMapper;
    @Autowired
    private BizBagUserService bizBagUserService;
    @Autowired
    private BizBagSpecService bizBagSpecService;
    @Autowired
    private ServiceCategoryService serviceCategoryService;


    /**
     * 【Since v2.5.0】查询套餐包
     * [INNER API] 查询套餐包
     *
     * @param request 请求
     *
     * @return {@link IPage}<{@link DescribeBizBagResponseVO}>
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0305)
    @ApiOperation("查询套餐包")
    @GetMapping
    @ListenExpireBack
    public IPage<DescribeBizBagResponseVO> listBag(@Valid DescribeBizBagRequest request) {
        return bizBagService.listBizBag(request);
    }

    /**
     * 【Since v2.5.0】查询可购买套餐包 归类 排序
     *
     * @param request 请求
     *
     * @return {@link Map}<{@link ?} {@link extends} {@link Object}, {@link ?} {@link extends} {@link Object}>
     */
    @AuthorizeBss(action = AuthModule.CC.CC020501 + "," + CC.CC02)
    @ApiOperation("查询可购买套餐包 归类 排序")
    @GetMapping("buy")
    @ListenExpireBack
    public Map<? extends Object, ? extends Object> listBuyBag(@Valid DescribeBizBagRequest request) {
        // 不是管理组，子用户返回null
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUser.getParentSid())) {
            List<Long> managementGroups = userGroupMapper.getDistinctUserSidByGroupId(1L);
            if (!managementGroups.contains(authUser.getUserSid())) {
                return new HashMap<>();
            }
        }
        IPage<DescribeBizBagResponseVO> describeBizBagResponseVOIPage = bizBagService.listBizBag(request);
        if (CollectionUtil.isNotEmpty(describeBizBagResponseVOIPage.getRecords())) {
            Map<String, Map<String, List<BizBagSpec>>> result = Maps.newHashMap();
            for (DescribeBizBagResponseVO describeBizBagResponseVO : describeBizBagResponseVOIPage.getRecords()) {
                List<BizBagSpec> bizBagSpecs = describeBizBagResponseVO.getBizBagSpecs();
                if (CollectionUtil.isNotEmpty(bizBagSpecs)) {
                    //查询sf_service_category表已经发布的产品信息
                    LambdaQueryWrapper<ServiceCategory> qw = new LambdaQueryWrapper<>();
                    qw.eq(ServiceCategory::getPublishStatus, "succeed");
                    qw.eq(ServiceCategory::getStatus, ServiceCategoryStatus.USING);
                    qw.eq(ServiceCategory::getServiceComponent, "innerService");
                    qw.eq(ServiceCategory::getEditable, ONE);
                    qw.eq(ServiceCategory::getServiceType, describeBizBagResponseVO.getProductType());
                    qw.isNotNull(ServiceCategory::getPublishDt);
                    long count = serviceCategoryService.count(qw);
                    if (count == 0) {
                        continue;
                    }

                    Map<String, List<BizBagSpec>> collect = bizBagSpecs.stream()
                                                                       .sorted(Comparator.comparing(
                                                                               BizBagSpec::getSpecValue))
                                                                       .collect(Collectors.groupingBy(
                                                                               BizBagSpec::getSpecName,
                                                                               LinkedHashMap::new,
                                                                               Collectors.toList()));
                    collect.forEach((k, v) -> {
                        List<BizBagSpec> bizBagSpecs1 = collect.get(k);
                        collect.put(k, bizBagSpecs1.stream()
                                                   .map(e -> {
                                                       e.setClusterId(describeBizBagResponseVO.getClusterId());
                                                       return e;
                                                   })
                                                   .sorted(Comparator.comparingInt(BizBagSpec::getPeriod))
                                                   .collect(Collectors.toList()));

                    });
                    result.put(describeBizBagResponseVO.getProductName(), collect);
                }
            }
            return result;
        }
        return new HashMap<>();
    }

    /**
     * 【Since v2.5.0】续订可购买套餐包 归类 排序
     *
     * @param bagUserId 套餐包用户ID
     *
     * @return {@link Map}<{@link ?} {@link extends} {@link Object}, {@link ?} {@link extends} {@link Object}>
     */
//    @AuthorizeBss(action = AuthModule.CC.CC020503)
//    @ApiOperation("续订可购买套餐包 归类 排序")
//    @GetMapping("renew/{bagUserId}")
//    @ListenExpireBack
//    public Map<? extends Object, ? extends Object> listRenewBuyBag(@PathVariable Long bagUserId) {
//
//        BizBagUser bizBagUser = bizBagUserService.getById(bagUserId);
//        if (null == bizBagUser) {
//            throw new BizException("续订资源不存在");
//        }
//        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
//        assert authUserInfo != null;
//        if (!Objects.equals(bizBagUser.getOrgSid(), authUserInfo.getOrgSid())) {
//            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
//        }
//        if (!"available".equals(bizBagUser.getStatus())) {
//            throw new BizException("不可续订已过期资源");
//        }
//        BizBag byId = bizBagService.lambdaQuery().eq(BizBag::getBagId, bizBagUser.getBagId()).one();
//        if (null == byId || !"online".equals(byId.getStatus())) {
//            throw new BizException("资源已下架");
//        }
//        List<BizBagSpec> list = bizBagSpecService.lambdaQuery().eq(BizBagSpec::getBagId, bizBagUser.getBagId()).list();
//        if (CollectionUtil.isNotEmpty(list)) {
//            Map<String, List<BizBagSpec>> collect = list.stream()
//                                                        .filter(a -> bizBagUser.getBagValue()
//                                                                               .compareTo(a.getSpecValue()) == 0)
//                                                        .peek(e -> e.setClusterId(bizBagUser.getClusterId()))
//                                                        .sorted(Comparator.comparing(
//                                                                BizBagSpec::getPeriod))
//                                                        .collect(Collectors.groupingBy(
//                                                                BizBagSpec::getSpecName,
//                                                                LinkedHashMap::new,
//                                                                Collectors.toList()));
//            return MapsKit.of("lastEndTime", DateUtil.format(bizBagUser.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"), "spec",
//                              collect);
//        }
//        return new HashMap<>();
//    }

    /**
     * 【Since v2.5.0】创建套餐包
     * [INNER API] 创建套餐包
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0301)
    @ApiOperation("创建套餐包")
    @PostMapping
    @ListenExpireBack
    public RestResult createBag(@Valid @RequestBody CreateBizBagRequest request) {
        ValidUtil.validRequest(request);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo != null) {
            request.setEntityId(authUserInfo.getEntityId());
        }
        String bagId = bizBagService.createBag(request);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1758021060),bagId);
    }

    /**
     * 【Since v2.5.0】修改套餐包
     * [INNER API] 修改套餐包
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0302)
    @ApiOperation("修改套餐包")
    @PutMapping
    @ListenExpireBack
    public RestResult updateById(@Valid @RequestBody BizBag request) {
        boolean flag = bizBagService.updateBizBagById(request);
        if (flag) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_652007152));
        } else {
            return new RestResult(RestResult.Status.FAILURE);
        }
    }

    /**
     * 【Since v2.5.0】删除套餐包
     * [INNER API] 删除套餐包
     *
     * @param id ID
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0303)
    @ApiOperation("删除套餐包")
    @DeleteMapping
    @ListenExpireBack
    public RestResult deleteBizSpecById(String id) {
        return new RestResult().setData(bizBagService.deleteBizBag(id));
    }

    /**
     * 【Since v2.5.0】复制套餐包
     * [INNER API] 复制套餐包
     *
     * @param id ID
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0304)
    @ApiOperation("复制套餐包")
    @PostMapping("/copy/{id}")
    @ListenExpireBack
    public RestResult copyBag(@PathVariable String id) {
        return new RestResult().setData(bizBagService.copyBag(id));
    }

    /**
     * 【Since v2.5.0】检查是否存在资源包
     * [INNER API] 检查是否存在资源包
     *
     * @param name 名字
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0301)
    @ApiOperation("检查是否存在资源包")
    @GetMapping("/check")
    @ListenExpireBack
    public RestResult checkName(String name) {
        return new RestResult(bizBagService.checkName(name));
    }

    /**
     * 【Since v2.5.0】订购套餐包
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = CC.CC020502)
    @ApiOperation("订购套餐包")
    @PostMapping("subscribe")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'订购套餐包'", resource = OperationResourceEnum.SUBSCRIBE_PACKAGES,param = "#request",bizId = "#request.id", tagNameUs ="'Order a package'")
    @ListenExpireBack
    public RestResult subscribe(@RequestBody @Valid SubscribeBizBagRequest request) {
        //校验企业是否认证中
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (user.getParentSid() != null) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil
                    .getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        List<Org> orgList = orgService.selectOrgByUserSidAndType(user.getOrgSid(), OrgType.COMPANY);
        if (0 < orgList.size()) {
            List<String> certificationStatusList = orgList.stream()
                                                          .map(Org::getCertificationStatus)
                                                          .collect(Collectors.toList());
            if (0 < certificationStatusList.size()) {
                if (CertificationStatus.AUTHING.equals(certificationStatusList.get(0))) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1805126085));
                }
            }
        }
        bizBagService.subscribe(request);
        return new RestResult(Status.SUCCESS);
    }

    /**
     * 【Since v2.5.0】上架套餐包
     * [INNER API] 上架套餐包
     *
     * @param bizBagRequest 生意场套餐包请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0307)
    @ApiOperation("上架套餐包")
    @PostMapping("/online")
    @ListenExpireBack
    public RestResult online(@RequestBody OnlineBizBagRequest bizBagRequest) {
        Boolean online = bizBagService.online(bizBagRequest.getId(), bizBagRequest.getProductType());
        if (online) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_326330361));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_326385780));
        }
    }

    /**
     * 【Since v2.5.0】下架套餐包
     * [INNER API] 下架套餐包
     *
     * @param offlineBizBagRequest 下架套餐包包请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0307)
    @ApiOperation("下架套餐包")
    @PostMapping("/offline")
    @ListenExpireBack
    public RestResult offline(@RequestBody OfflineBizBagRequest offlineBizBagRequest) {
        Boolean offline = bizBagService.offline(offlineBizBagRequest.getId());
        if (offline) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_326300570));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_326355989));
        }
    }

    /**
     * 【Since v2.5.0】查询同一个套餐包下的相同规格
     * [INNER API] 查询同一个套餐包下的相同规格
     *
     * @param request 请求
     *
     * @return {@link List<DescribeBagAndSpecResponse>}
     */
    @RejectCall
    @ApiOperation("查询同一个套餐包下的相同规格")
    @AuthorizeBss(action = AuthModule.BL.BL03.BL0302)
    @GetMapping("/list")
    @ListenExpireBack
    public List<DescribeBagAndSpecResponse> listBizBagAndSpec(@Valid DescribeBizBagSpecRequest request) {
        return bizBagService.listBizBagAndSpec(request);
    }

    /**
     * 【Since v2.5.0】套餐包获取适用产品
     * [INNER API] 套餐包获取适用产品
     *
     * @param type 类型
     *
     * @return {@code List<DescribeBizBagProductResponse>}
     */
    @RejectCall
    @ApiOperation("套餐包获取适用产品")
    @GetMapping("/product")
    @AuthorizeBss(action = AuthModule.BL.BL03.BL03)
    @ListenExpireBack
    public List<DescribeBizBagProductResponse> listBizBagProduct(String type) {
        return bizBagService.listBizBagProduct(type);
    }
}
