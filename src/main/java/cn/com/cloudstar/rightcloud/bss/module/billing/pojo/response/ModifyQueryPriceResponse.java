/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/30 10:52
 */
@ApiModel("变更询价")
@Data
public class ModifyQueryPriceResponse {

    /**
     * 变更金额
     */
    @ApiModelProperty("变更金额")
    private BigDecimal amount;

    /**
     * 新规格参考价
     */
    @ApiModelProperty("新规格参考价")
    private BigDecimal price;

    /**
     *  单位 month hour
     */
    private String chargeUnit;

    /**
     *  是否显示实付金额
     */
    private boolean showAmount = true;
}
