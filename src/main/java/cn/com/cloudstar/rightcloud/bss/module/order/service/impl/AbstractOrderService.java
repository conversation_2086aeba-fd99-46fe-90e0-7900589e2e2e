package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import brave.Tracing;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.bss.common.constants.DealType;
import cn.com.cloudstar.rightcloud.bss.common.constants.InquiryPriceConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.PriceType;
import cn.com.cloudstar.rightcloud.bss.common.constants.TradeType;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingStrategyAccountConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccountConfig;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpgradeServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DiscountDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.UpgradeDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountPolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscountOrder;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscountPolicy;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.BaseOrderService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SidService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <AUTHOR>
 * @date 2020/6/16.
 */
@Slf4j
public abstract class AbstractOrderService implements BaseOrderService {

    public static final String POST_PAID_CONFIG_KEY = "postpaid.min.limit";

    private static final String MONTH_PATTERN = "yyyy-MM";

    private static final String PUBLIC_ENV = "Public";

    private static final String SALE_TYPE = "02";

    private static final String NORMAL_TYPE = "01";

    protected static final String YEAR = "year";

    protected static final String MONTH = "month";

    private static final String MONTH_ONE = "1";

    private static final String YEAR_TWO = "2";

    /**
     * 分布式链路跟踪，缓存traceId
     */
    private static final String TRACE_ID_KEY = "traceIdKey:";

    @Autowired
    public ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private BizCouponMapper bizCouponMapper;

    @Autowired
    private BizCouponAccountMapper bizCouponAccountMapper;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private BizCouponResourceMapper bizCouponResourceMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;


    @Autowired
    private SidService sidService;

    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;

    @Autowired
    private BizInquiryPriceService bizInquiryPriceService;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private BizDiscountPolicyMapper bizDiscountPolicyMapper;

    @Autowired
    private BizDiscountMapper bizDiscountMapper;

    @Autowired
    private BizBillingStrategyAccountConfigMapper strategyAccountConfigMapper;

    @Autowired
    private BizDiscountOrderMapper bizDiscountOrderMapper;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private IBizDistributorProductService distributorProductService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private Tracing tracing;

    public void execute(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        //参数有效性校验
        validateParams(serviceVO, applyEntity);
        // 询价
        inquiryPrice(serviceVO, applyEntity);

        couponUsed(serviceVO, applyEntity);

        validateBalance(applyEntity);

        buildOrder(serviceVO, applyEntity);

        saveDiscountDetailOfOrder(applyEntity);

        addCouponRelation(applyEntity);
    }

    public void couponUsed(ApplyServiceVO serviceVO, ApplyEntity applyEntity){
        if (Objects.nonNull(serviceVO.getCouponId())){
            Long couponSid = serviceVO.getCouponId();
            List<InquiryPriceResponse> prices = applyEntity.getPrices();
            BigDecimal bigDecimal = prices.stream().map(InquiryPriceResponse::getCouponAmount)
                                          .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //根据运营实体与用户id查询对应账户
            BizBillingAccount bizBillingAccount = bizBillingAccountService.getByEntityIdAndUserId(serviceVO.getEntityId(), applyEntity.getAuthUser().getUserSid());
            if (Objects.isNull(bizBillingAccount)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
            }
            Long accountId = bizBillingAccount.getId();
            QueryWrapper<BizCouponAccount> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizCouponAccount::getAccountId, accountId).eq(BizCouponAccount::getCouponSid, couponSid);
            List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectList(queryWrapper);
            BizCouponAccount bizCouponAccount = bizCouponAccounts.get(0);
            bizCouponAccount.setDiscountUsed(bigDecimal);
            WebUserUtil.prepareUpdateParams(bizCouponAccount);
            bizCouponAccountMapper.updateById(bizCouponAccount);
        }
    }

    public void after(ApplyEntity applyEntity) {
        if (CollectionUtil.isEmpty(applyEntity.getOrderDetails())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2071376150));
        }

        // 分布式链路跟踪，缓存traceId
        if (Objects.nonNull(applyEntity.getOrder())) {
            Long serviceOrderId = applyEntity.getOrder().getId();
            QueryWrapper<SfProductResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("service_order_id", serviceOrderId);
            SfProductResource sfProductResource = sfProductResourceMapper.selectOne(queryWrapper);
            if (Objects.nonNull(sfProductResource)) {
                try {
                    long traceId = tracing.currentTraceContext().get().traceId();
                    String cacheKey = TRACE_ID_KEY + sfProductResource.getProductType() + StrUtil.COLON + sfProductResource.getClusterId();
                    JedisUtil.INSTANCE.set(cacheKey,String.valueOf(traceId));
                } catch (Exception e) {
                    log.info("获取当前链路traceId-AbstractOrderService.after-失败:[{}]", e.getMessage());
                }
            }
        }

        if (CollectionUtil.isEmpty(applyEntity.getResourceId())) {
            return;
        }
        List<ServiceOrderResourceRef> orderResourceRefs = Lists.newArrayList();

        ServiceOrderDetail orderDetail = applyEntity.getOrderDetails().get(0);
        ConfigDesc configDesc = new ConfigDesc();
        configDesc.setLabel("ID");
        configDesc.setValue(String.join(StrUtil.COMMA, applyEntity.getResourceId()));
        String productConfigDesc = orderDetail.getProductConfigDesc();

        if (Objects.nonNull(productConfigDesc)) {
            //判断是json对象还是json数组
            boolean isAarray = productConfigDesc.startsWith("{");
            if (isAarray) {
                JSONObject jsonNode = JSON.parseObject(orderDetail.getProductConfigDesc());
                JSONArray parent = jsonNode.getJSONArray("currentDesc");
                if (parent != null) {
                    parent.add(0, configDesc);
                    orderDetail.setProductConfigDesc(JSON.toJSONString(jsonNode));
                    serviceOrderDetailMapper.updateById(orderDetail);
                }
            }
        }
        boolean isCreateRef = !ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(orderDetail.getServiceType())
                && !ProductCodeEnum.MODEL_ARTS.getProductType().equals(orderDetail.getServiceType())
                && !ProductCodeEnum.isCmpApiProduct().contains(orderDetail.getServiceType())
                && !ProductCodeEnum.federationProducts().contains(orderDetail.getServiceType())
                && !ProductCodeEnum.AS_GROUP.getProductType().equals(orderDetail.getServiceType())
                && !ProductCodeEnum.NAT.getProductType().equals(orderDetail.getServiceType());

        applyEntity.getResourceId().forEach(resourceId -> {
            if(isCreateRef){
                ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
                resourceRef.setOrderDetailId(orderDetail.getId());
                resourceRef.setType(orderDetail.getServiceType());
                resourceRef.setResourceId(resourceId);
                log.info("资源resourceId..{},orderType..{}",resourceId,orderDetail.getServiceType());
                orderResourceRefs.add(resourceRef);
            }
            applyEntity.getPriceDetails().forEach(priceDetail -> {
                priceDetail.setRefInstanceId(JSONArray.toJSONString(applyEntity.getResourceId()));
                priceDetail.setRefKey(StrUtil.concat(true, priceDetail.getRefKey(),
                                                     resourceId, "-"));
            });
        });
        if (CollectionUtil.isNotEmpty(applyEntity.getPriceDetails())) {
            serviceOrderPriceDetailService.updateBatchById(applyEntity.getPriceDetails());
        }

        serviceOrderResourceRefService.saveBatch(orderResourceRefs);

    }
    /**
     * 参数有效性校验
     * @param serviceVO
     * @return
     */
    public void validateParams(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        AssertUtil.requireNonBlank(serviceVO.getProductInfo(), "未找到订单项目.");
        String periodType = serviceVO.getProductInfo().get(0).getPeriodType();
        if(ChargeTypeEnum.OneTime.getType().equals(periodType)){
            serviceVO.getProductInfo().get(0).setChargeType(periodType);
        }

        String chargeType = serviceVO.getProductInfo().get(0).getChargeType();
        applyEntity.setChargeType(chargeType);
        AssertUtil.requireContains(Arrays
                .asList(ChargeTypeEnum.PrePaid.getType(), ChargeTypeEnum.PostPaid.getType(),
                    ChargeTypeEnum.None.getType(), ChargeTypeEnum.OneTime.getType()),
            chargeType, "计费类型只包含：不计费，包年包月和按量计费模式");

        //根据运营实体查询对应账户
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getByEntityIdAndUserId(serviceVO.getEntityId(), applyEntity.getAuthUser().getUserSid());
        AssertUtil.requireNonBlank(bizBillingAccount, "登录用户权限错误.未关联对应的账户.");

        applyEntity.setAccount(bizBillingAccount);

        ServiceCategory serviceCategory = serviceCategoryMapper.selectById(serviceVO.getProductInfo().get(0).getServiceId());
        applyEntity.setServiceCategory(serviceCategory);
        BizCoupon bizCoupon = null;
        if (Objects.nonNull(serviceVO.getCouponId())) {
            // 使用优惠券
            bizCoupon = bizCouponMapper.selectById(serviceVO.getCouponId());
            AssertUtil.requireNonBlank(bizCoupon, "优惠券未找到.");
            // 预占优惠券
            preStationBizCoupon(serviceVO.getCouponId(), bizBillingAccount.getId());
        }
        applyEntity.setBizCoupon(bizCoupon);
    }

    /**
     * 创建订单、订单详情、订单价格详情
     * @param serviceVO
     * @param applyEntity
     */
    public void buildOrder(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        List<InquiryPriceResponse> priceResult = applyEntity.getPrices();
        BigDecimal couponDiscount = applyEntity.getBizCoupon() == null ? BigDecimal.ZERO : applyEntity.getBizCoupon().getDiscountAmount();
        boolean hpcDrpFlg = unUniqueOrder(serviceVO);
        boolean unUnique = unUniqueOrderCommon(serviceVO);
        boolean isRds = isRdsOrder(serviceVO);
        Map<String, List<InquiryPriceResponse>> priceMap = priceResult.stream().collect(Collectors.groupingBy(x ->
                hpcDrpFlg ? StrUtil.concat(true, x.getProductCode(), StrUtil.toString(x.getProductCategory())) : x.getProductCode()));
        // 询出的总价
        BigDecimal resultOriginalPrice = priceResult.stream().map(InquiryPriceResponse::getOriginalPrice).reduce(BigDecimal::add)
                                                    .orElse(BigDecimal.ZERO);
        // 询出的折扣
        BigDecimal orgDiscount = priceResult.stream().map(InquiryPriceResponse::getDiscountPrice).reduce(BigDecimal::add)
                                            .orElse(BigDecimal.ZERO);
        // 询出的固定收费
        BigDecimal resultFixOncePrice = priceResult.stream().map(InquiryPriceResponse::getTradeOncePrice).reduce(BigDecimal::add)
                                                   .orElse(BigDecimal.ZERO);

        if (hpcDrpFlg || unUnique||isRds) {
            BigDecimal sumCouponDiscount =  priceResult.stream().map(InquiryPriceResponse::getCouponAmount).reduce(BigDecimal::add)
                                                       .orElse(BigDecimal.ZERO);
            // HPC专属资源池询价时已经减去了优惠券价格
            orgDiscount = NumberUtil.sub(orgDiscount, sumCouponDiscount);
            couponDiscount = sumCouponDiscount;
        }
        BigDecimal priceWithoutCoupon = resultOriginalPrice.subtract(orgDiscount)
                                                           .add(resultFixOncePrice);
        if (NumberUtil.isGreater(couponDiscount, priceWithoutCoupon)) {
            couponDiscount = priceWithoutCoupon;
            applyEntity.getBizCoupon().setDiscountAmount(couponDiscount);
        }
        BigDecimal finalCost = priceWithoutCoupon.subtract(couponDiscount);

        applyEntity.setTradePrice(finalCost);
        applyEntity.setOriginalPrice(resultOriginalPrice);
        applyEntity.setOncePrice(resultFixOncePrice);
        applyEntity.setDiscountPrice(orgDiscount);
        ServiceOrder serviceOrder = new ServiceOrder();
        BeanUtils.copyProperties(serviceVO, serviceOrder);
        if (Objects.nonNull(serviceVO.getResourceInfo())) {
            serviceOrder.setResourceInfo(JSON.toJSONString(serviceVO.getResourceInfo()));
        }
        // 订单、服务实例名称追加时间戳，区分重名
        String productName = ProductCodeEnum.toDesc(serviceVO.getProductInfo().get(0).getProductCode());
        serviceOrder.setName(Strings.isNotBlank(serviceVO.getProductName()) ? serviceVO.getProductName() : productName);
        serviceOrder.setType(serviceVO.getOrderType());
        serviceOrder.setOwnerId(Convert.toStr(applyEntity.getAuthUser().getUserSid()));
        serviceOrder.setProcessFlag("01");
        serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
        serviceOrder.setStatus(OrderStatus.PENDING);
        // 如果是AIhub对接的商品 直接就是开通成功
        if (Objects.nonNull(serviceVO.getPriceJoinId())){
            serviceOrder.setStatus(OrderStatus.COMPLETED);
        }
        serviceOrder.setEntityId(serviceVO.getEntityId());
        serviceOrder.setEntityName(serviceVO.getEntityName());
        serviceOrder.setClusterUuid(serviceVO.getClusterUuid());
        if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())){
            serviceOrder.setFinalCost(BigDecimal.ZERO);
            serviceOrder.setCurrAmount(BigDecimal.ZERO);
            serviceOrder.setOriginalCost(BigDecimal.ZERO);
            serviceOrder.setOrgDiscount(BigDecimal.ZERO);
        } else {
            serviceOrder.setFinalCost(finalCost);
            log.info("生成审批订单的价格,...{}",finalCost);
            serviceOrder.setCurrAmount(finalCost);
            serviceOrder.setOriginalCost(NumberUtil.add(resultOriginalPrice, resultFixOncePrice));
            serviceOrder.setOrgDiscount(orgDiscount);
        }

        //查询是否关联不计费产品
        IsNonBillProductRequest request = new IsNonBillProductRequest();
        Long serviceId = serviceVO.getProductInfo().get(0).getServiceId();
        request.setSfServiceId(serviceId);
        request.setChargeType(applyEntity.getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        if (isNonBillProduct){
            log.info("不计费产品，实际金额赋值为0");
            serviceOrder.setFinalCost(BigDecimal.ZERO);
            serviceOrder.setCurrAmount(BigDecimal.ZERO);
            serviceOrder.setChargingType(SALE_TYPE);
            serviceOrder.setOrgDiscount(BigDecimal.ZERO);
        }else {
            serviceOrder.setChargingType(NORMAL_TYPE);
        }

        if ("None".equals(serviceVO.getProductInfo().get(0).getChargeType())) {
            // 设置不计费
            serviceOrder.setChargingType("03");
        }

        //设置服务id到订单中
        serviceOrder.setServiceId(serviceId+"");
        //临时方案，针对分销商开通弹性文件服务后，无法查询到弹性文件服务问题，修改为资源关联的orgSid为companyId而不是projectId
        if (ProductCodeEnum.SFS2.getProductType().equals(serviceVO.getProductInfo().get(0).getProductCode())){
            serviceOrder.setOrgSid(AuthUtil.getAuthUser().getOrgSid());
        }else {
            serviceOrder.setOrgSid(Objects.isNull(serviceVO.getProjectId()) ?
                                           AuthUtil.getAuthUser().getOrgSid() : serviceVO.getProjectId());
        }
        serviceOrder.setCouponDiscount(couponDiscount);
        serviceOrder.setBizBillingAccountId(applyEntity.getAccount().getId());
        List<ProductInfoVO> productInfoVOList = serviceVO.getProductInfo();
        if (productInfoVOList.size() > 0) {
            productInfoVOList.get(0).setProjectId(serviceVO.getProjectId());
        }
        serviceOrder.setExtraAttr(JSON.toJSONString(productInfoVOList));

        WebUserUtil.prepareInsertParams(serviceOrder, applyEntity.getAuthUser().getAccount());
        serviceOrder.setProductCode(serviceVO.getProductInfo().get(0).getProductCode());
        log.info("所属产品Code：[{}]", serviceOrder.getProductCode());
        if (ProductCodeEnum.HPC_DRP.getProductType().equals(serviceOrder.getProductCode())) {
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByPrimaryKey(serviceId);
            productName = Objects.nonNull(serviceCategory) && StrUtil.isNotBlank(serviceCategory.getProductName()) ? String.format("%s %s",serviceCategory.getProductName(), serviceCategory.getServiceType()) : productName;
            log.info("HPC_DRP 所属产品名称：[{}]", productName);
        }
        serviceOrder.setProductName(productName);
        serviceOrder.setAccountName(applyEntity.getAccount().getAccountName());
        // 结算类型
        if (Objects.nonNull(serviceVO.getProductInfo().get(0).getContractId())) {
            serviceOrder.setSettlementType("合同价");
            // 合同ID
            serviceOrder.setContractId(serviceVO.getProductInfo().get(0).getContractId());
        } else {
            serviceOrder.setSettlementType("标准价");
        }
        // 代客下单管理员ID
        if (Objects.nonNull(serviceVO.getBehalfUserSid())) {
            serviceOrder.setBehalfUserSid(serviceVO.getBehalfUserSid());
        }
        // 订单来源
        serviceOrder.setOrderSourceSn(serviceOrder.getOrderSn());
        log.info("生成审批订单的价格wxh,...{}",serviceOrder.getFinalCost());
        this.serviceOrderMapper.insert(serviceOrder);
        applyEntity.setOrder(serviceOrder);
        // 新增订单明细
        int n = 0,sfsIndex=0;
        long bmsCount = serviceVO.getProductInfo().stream().filter(o -> ProductCodeEnum.RS_BMS.getProductType().equals(o.getProductCode())).count();
        long bmsIndex = 0;
        for (ProductInfoVO productInfo : serviceVO.getProductInfo()) {
            ServiceOrderDetail orderDetail = new ServiceOrderDetail();
            orderDetail.setOrderId(serviceOrder.getId());
            orderDetail.setServiceId(productInfo.getServiceId());
            orderDetail.setChargeType(productInfo.getChargeType());
            orderDetail.setServiceConfig(JSON.toJSONString(productInfo));

            orderDetail.setVersion(1L);
            orderDetail.setOrgSid(serviceOrder.getOrgSid());
            String productCode = productInfo.getProductCode();
            orderDetail.setServiceType(productCode);
            orderDetail.setQuantity(productInfo.getAmount());
            BigDecimal period = productInfo.getPeriod();
            orderDetail.setDuration(Optional.ofNullable(period).orElse(BigDecimal.ONE).intValue());
            orderDetail.setStartTime(Calendar.getInstance().getTime());
            orderDetail.setDiscountRatio(priceResult.get(n).getPlatformDiscount());
            orderDetail.setFloatingRatio(priceResult.get(n).getFloatingRatio());
            orderDetail.setCustomFlag(productInfo.getCustomFlag());
            if (Objects.nonNull(productInfo.getProductConfigDesc())) {
                orderDetail.setProductConfigDesc(productInfo.getProductConfigDesc().getCurrentConfigDesc());
            }

            // 不同规格的同类产品需要通过规格区分（当前只用到了HPC专属资源池）
            String productCategory = getProductCategoryFromServiceConfig(productInfo.getData(), hpcDrpFlg);

            //设置额外配置费用,服务费用，资源费用
            orderDetail.setOncePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
                                                                                                   PriceType.EXTRA_CONFIG.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getOncePrice)
                                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())) {
                orderDetail.setServicePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
                                                                                                          PriceType.SERVICE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getHourPrice)
                                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                orderDetail.setResourcePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
                                                                                                           PriceType.RESOURCE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getHourPrice)
                                                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            } else {
                orderDetail.setServicePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
                                                                                                          PriceType.SERVICE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getMonthPrice)
                                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                orderDetail.setResourcePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
                                                                                                           PriceType.RESOURCE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getMonthPrice)
                                                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            }
            // 获取当前产品价格信息
            List<InquiryPriceResponse> priceResponses = priceMap.get(
                    hpcDrpFlg ? StrUtil.concat(true, productCode, productCategory) : productCode);
            // 单价
            BigDecimal price =
                    finalCost != null ? NumberUtil.div(finalCost, orderDetail.getQuantity())
                            : BigDecimal.ZERO;
            if (hpcDrpFlg) {
                price = NumberUtil.div(priceResponses.get(0).getOriginalPrice(), orderDetail.getQuantity());
                //处理内置弹性文件sfs2.0(包含多条数据)
                if(ProductCodeEnum.SFS2.getProductType().equals(priceResponses.get(sfsIndex).getProductCode())){
                    price = NumberUtil.div(priceResponses.get(sfsIndex).getOriginalPrice(), orderDetail.getQuantity());
                }
            }
            if (unUnique||isRds) {
                price = NumberUtil.div(priceResult.get(n).getOriginalPrice(), orderDetail.getQuantity());
            }
//            if (bmsCount > 0 && ProductCodeEnum.RS_BMS.getProductType().equals(productInfo.getProductCode())) {
//                price = NumberUtil.div(price, bmsCount);
//            }

            orderDetail.setPrice(price);
            //关联不计费产品金额置为0
            if (isNonBillProduct) {
                orderDetail.setAmount(BigDecimal.ZERO);
            }else{
                orderDetail.setAmount(price.multiply(new BigDecimal(orderDetail.getQuantity())));
                if (unUnique||isRds) {
                    orderDetail.setAmount(priceResult.get(n).getTradePrice());
                }
            }
            orderDetail.setOriginalCost(NumberUtil.div(priceResult.get(n).getOriginalPrice(), orderDetail.getQuantity()));
            Integer duration = Convert.toInt(orderDetail.getDuration(), 0);
            if (orderDetail.getDuration() != null && duration > 0) {
                orderDetail.setEndTime(DateUtil.offsetMonth(orderDetail.getStartTime(), duration));
            }
            if (ProductCodeEnum.isInnerProduct(productInfo.getProductCode())
                    || ProductCodeEnum.federationProducts().contains(productInfo.getProductCode())
                    || ProductCodeEnum.isCmpApiProduct().contains(productInfo.getProductCode())) {
                orderDetail.setStartTime(new Date());
                orderDetail.setEndTime(null);
            }
            if (ProductCodeEnum.AI_MARKET.getProductType().equals(productInfo.getProductCode())) {
                orderDetail.setDuration(productInfo.getAmount());
                orderDetail.setQuantity(1);
                if (YEAR.equals(productInfo.getPeriodType())) {
                    orderDetail.setEndTime(DateUtil.offset(orderDetail.getStartTime(), DateField.YEAR, productInfo.getAmount()));
                } else if (MONTH.equals(productInfo.getPeriodType())) {
                    orderDetail.setEndTime(DateUtil.offset(orderDetail.getStartTime(), DateField.MONTH, productInfo.getAmount()));
                }
            }
            //新增一个申请类型
            orderDetail.setApplyType(productInfo.getApplyType());
            serviceOrderDetailMapper.insert(orderDetail);

            if (bmsCount > 0) {
                this.completeBmsProductInfo(orderDetail, serviceVO.getProductInfo(), bmsIndex, productInfo);
                if (ProductCodeEnum.RS_BMS.getProductType().equals(productInfo.getProductCode())) {
                    bmsIndex ++;
                }
            }

            serviceOrder.getOrderDetails().add(orderDetail);
            if (CollectionUtil.isNotEmpty(priceResponses)) {
                //处理内置弹性文件sfs2.0(包含多条数据)
                if(ProductCodeEnum.SFS2.getProductType().equals(priceResponses.get(sfsIndex).getProductCode())){
                    makeOrderPriceDetail(priceResponses.get(sfsIndex), serviceOrder, orderDetail, applyEntity, hpcDrpFlg, unUnique);
                    sfsIndex++;
                }else if (ProductCodeEnum.ECS.getProductType().equals(serviceOrder.getProductCode())
                        || ProductCodeEnum.RS_BMS.getProductType().equals(serviceOrder.getProductCode())){
                    makeOrderPriceDetail(priceResult.get(n), serviceOrder, orderDetail, applyEntity, hpcDrpFlg, unUnique);
                } else {
                    makeOrderPriceDetail(priceResponses.get(0), serviceOrder, orderDetail, applyEntity, hpcDrpFlg, unUnique);
                }
            }
            n++;
        }

        applyEntity.setOrderDetails(serviceOrder.getOrderDetails());

    }

    private void completeBmsProductInfo(ServiceOrderDetail orderDetail, List<ProductInfoVO> productInfoVOList
            , long bmsIndex, ProductInfoVO productInfo) {
        if (ProductCodeEnum.RS_BMS.getProductType().equals(productInfo.getProductCode())) {
            for (ProductInfoVO productInfoVO : productInfoVOList) {
                if (bmsIndex == productInfoVO.getBmsOrderDetailId()) {
                    productInfoVO.setBmsOrderDetailId(orderDetail.getId());
                }
            }
        }else if (productInfo.getBmsOrderDetailId() > 1000){
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectById(productInfo.getBmsOrderDetailId());
            JSONObject serviceConfig = JSON.parseObject(serviceOrderDetail.getServiceConfig());
            JSONArray volumeOrderDetailIds = serviceConfig.getJSONArray("volumeOrderDetailIds");
            if (CollectionUtils.isEmpty(volumeOrderDetailIds)) {
                serviceConfig.put("volumeOrderDetailIds", Collections.singletonList(orderDetail.getId()));
            }else {
                volumeOrderDetailIds.add(orderDetail.getId());
                List<Object> collect = volumeOrderDetailIds.stream().distinct().collect(Collectors.toList());
                serviceConfig.put("volumeOrderDetailIds", collect);
            }
            serviceOrderDetail.setServiceConfig(serviceConfig.toJSONString());
            serviceOrderDetailMapper.updateById(serviceOrderDetail);
        }
    }

    private void _orderActionLog(ServiceOrder serviceOrder){
        StringBuffer actionName = new StringBuffer();
        actionName.append("订购");
        actionName.append("["+serviceOrder.getProductName()+"]");
        actionName.append("支付金额￥");
        actionName.append(serviceOrder.getFinalCost().setScale(2, BigDecimal.ROUND_HALF_UP));
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
        String userClient = request.getHeader("User-Agent");
        String remoteIp = cn.com.cloudstar.rightcloud.oss.common.util.IPAddressUtil.getRemoteHostIp(request);
        mongoTemplate.insert(ActionLog.builder()
                                      .actionName(actionName.toString())
                                      .account(authUserInfo.getAccount())
                                      .actionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request))
                                      .actionTime(new Date())
                                      .httpMethod(request.getMethod())
                                      .lbIp(IPAddressUtil.getRemoteAddr(request))
                                      .remoteIp(remoteIp)
                                      .roleName("客户管理员(内置)")
                                      .client(userClient).build()
                , "action_log");
    }


    /**
     * 优惠券与资源关联
     * @param applyEntity 申请实例
     */
    public void addCouponRelation(ApplyEntity applyEntity) {
        // 优惠券-资源
        if (Objects.nonNull(applyEntity.getBizCoupon())) {
            BizCouponResource bizCouponResource = new BizCouponResource();
            bizCouponResource.setCouponSid(applyEntity.getBizCoupon().getCouponSid());
            bizCouponResource.setCouponNo(applyEntity.getBizCoupon().getCouponNo());
            bizCouponResource.setResourceType(applyEntity.getOrder().getProductCode());
            bizCouponResource.setOrderId(applyEntity.getOrder().getId());
            WebUserUtil.prepareInsertParams(bizCouponResource);
            bizCouponResourceMapper.insert(bizCouponResource);
        }
    }

    /**
     * 按量计费扣减账户余额，包年包月在资源创建成功时，按账单实际金额扣费
     * @param applyEntity 申请实例
     */
    public void reduceBalance(ApplyEntity applyEntity) {
        BigDecimal tradePrice;

        if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())) {
            // 按量计费有可能有单次收费 需要入账
            tradePrice = applyEntity.getOncePrice();

            if (Objects.nonNull(applyEntity.getAccount()) && Objects.nonNull(tradePrice)
                    && tradePrice.compareTo(BigDecimal.ZERO) > 0) {
                bizBillingAccountService.reduceBalance(applyEntity.getAccount().getId(), tradePrice);
            }

            saveAccountDeal(applyEntity);
        }

    }


    public void saveAccountDeal(ApplyEntity applyEntity) {
        //只新增按量单次收费收支明细
        if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())) {
            BizAccountDeal accountDeal = new BizAccountDeal();
            accountDeal.setFlowNo(NoUtil.generateNo("SZ"));
            accountDeal.setType(DealType.OUT);
            accountDeal.setTradeType(TradeType.PAY);
            accountDeal.setTradeChannel(RechargeTypeEnum.PLATFORM.getCode());
            accountDeal.setTradeNo(applyEntity.getOrder().getOrderSn());

            accountDeal.setRemark("配置费用：单次收费");
            accountDeal.setBillingCycle(DateUtil.format(new Date(), MONTH_PATTERN));
            accountDeal.setAmount(applyEntity.getTradePrice());
            accountDeal.setBalance(NumberUtil.sub(applyEntity.getAccount().getBalance(), applyEntity.getTradePrice()));
            accountDeal.setBalanceCredit(applyEntity.getAccount().getCreditLine());
            accountDeal.setBalanceCash(applyEntity.getAccount().getBalanceCash());
            accountDeal.setAccountSid(applyEntity.getAccount().getId());
            accountDeal.setAccountName(applyEntity.getAccount().getAccountName());
            accountDeal.setOrgSid(applyEntity.getAccount().getOrgSid());
            accountDeal.setUserSid(applyEntity.getAuthUser().getUserSid());
            WebUserUtil.prepareInsertParams(accountDeal);
            bizAccountDealService.save(accountDeal);
        }
    }

    /**
     * 询价
     * @param serviceVO 请求参数
     * @return 询价结果
     */
    public void inquiryPrice(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        applyEntity.setPrices(bizInquiryPriceService.inquiryPrice(serviceVO));
        //查询是否关联不计费产品
        IsNonBillProductRequest request = new IsNonBillProductRequest();
        request.setSfServiceId(serviceVO.getProductInfo().get(0).getServiceId());
        request.setChargeType(applyEntity.getChargeType());
        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
        applyEntity.getPrices().get(0).setChargingType(isNonBillProduct ? SALE_TYPE : NORMAL_TYPE);
    }

    /**
     * 预占用优惠券
     * @param couponSid 优惠券ID
     * @param accountId 账户ID
     */
    private void preStationBizCoupon(Long couponSid, Long accountId) {
        Criteria criteria = new Criteria();
        criteria.put("accountId", accountId);
        criteria.put("couponSid", couponSid);
        QueryWrapper<BizCouponAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizCouponAccount::getAccountId, accountId).eq(BizCouponAccount::getCouponSid, couponSid);
        List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectList(queryWrapper);
        AssertUtil.requireNonBlank(bizCouponAccounts, "你选择的优惠券未找到,请选择其他优惠券.");
        BizCouponAccount getCouponAccount = bizCouponAccounts.get(0);
        if (!CouponStatusEnum.UNUSED.getCode().equalsIgnoreCase(getCouponAccount.getCouponStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1219130789));
        }
        BizCouponAccount bizCouponAccount = bizCouponAccounts.get(0);
        bizCouponAccount.setCouponStatus(CouponStatusEnum.USED.getCode());
        WebUserUtil.prepareUpdateParams(bizCouponAccount);
        bizCouponAccountMapper.updateById(bizCouponAccount);

    }

    /**
     * 产品购买
     * @param serviceVO
     * @return
     */
    @Override
    public abstract String apply(ApplyServiceVO serviceVO);

    @Override
    public UpgradeDetailVO upgradeDetail(UpgradeServiceRequest upgradeServiceRequest) {
        return new UpgradeDetailVO();
    }

    /**
     * 购买力验证
     * @param applyEntity 申请实例
     */
    public void validateBalance(ApplyEntity applyEntity) {
        BigDecimal finalCost = applyEntity.getPrices().stream().map(InquiryPriceResponse::getTradePrice).reduce(BigDecimal::add)
                                          .orElse(BigDecimal.ZERO);
        //优惠券金额
        BigDecimal finalCoupon = BigDecimal.ZERO;
        BizCoupon bizCoupon = applyEntity.getBizCoupon();
        if(bizCoupon !=null) {
            finalCoupon = bizCoupon.getDiscountAmount() == null ? BigDecimal.ZERO : bizCoupon.getDiscountAmount();
        }
        String chargingType = applyEntity.getPrices().get(0).getChargingType();
        if (ChargeTypeEnum.PrePaid.getType().equalsIgnoreCase(applyEntity.getChargeType()) && !SALE_TYPE.equals(chargingType)) {
            // 若余额+信用额度+现金余额+优惠金额 < 当前订单消费
            BigDecimal totalValue = NumberUtil.add(applyEntity.getAccount().getBalance(),
                                            applyEntity.getAccount().getCreditLine())
                                       .add(applyEntity.getAccount().getBalanceCash())
                                        .add(finalCoupon);

            if (ProductCodeEnum.HPC_DRP.getProductType().equals(applyEntity.getServiceCategory().getServiceType())) {
                // HPC专属询价已经减去了优惠券金额, 所以此处总金额要减掉优惠券金额
                totalValue = NumberUtil.sub(totalValue, finalCoupon);
            }

            if (totalValue.compareTo(finalCost) < 0) {
                log.info("AbstractOrderService.validateBalance totalValue: {}, finalCost: {}", totalValue, finalCost);
                // 归还优惠券
                if (bizCoupon != null) {
                    QueryWrapper<BizCouponAccount> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda()
                                .eq(BizCouponAccount::getAccountId, applyEntity.getAccount().getId())
                                .eq(BizCouponAccount::getCouponSid, bizCoupon.getCouponSid());
                    List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectList(queryWrapper);
                    BizCouponAccount bizCouponAccount = bizCouponAccounts.get(0);
                    bizCouponAccount.setCouponStatus(CouponStatusEnum.UNUSED.getCode());
                    bizCouponAccount.setDiscountUsed(null);
                    bizCouponAccountMapper.updateById(bizCouponAccount);
                }
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_840524885));
            }

        } else if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType()) && !SALE_TYPE.equals(chargingType)) {
            String configValue = PropertiesUtil.getProperty(POST_PAID_CONFIG_KEY);
            BigDecimal amount = StrUtil.isBlank(configValue) ? BigDecimal.ZERO : new BigDecimal(configValue);
            if (NumberUtil.isLess(NumberUtil.add(applyEntity.getAccount().getBalance(), applyEntity.getAccount().getCreditLine()).add(applyEntity.getAccount().getBalanceCash()), amount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1769382503) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1466642481));
            }
        }
    }

    /**
     * 生成订单详情
     * @param result
     * @param serviceOrder
     */
    public void makeOrderPriceDetail(InquiryPriceResponse result, ServiceOrder serviceOrder,
                                     ServiceOrderDetail detail, ApplyEntity applyEntity, boolean hpcDrpFlg, boolean ecsFlg) {
        try {
            List<BizBillingPriceVO> priceVOs = result.getBillingPrices();
            // 有优惠卷信息的时候，需要做均分操作
            BigDecimal couponAmountOfDetail = result.getCouponAmount();
            BigDecimal usedCouponAmount = BigDecimal.ZERO;

            String serviceType = detail.getServiceType();
            boolean isSfs = ProductCodeEnum.SFS2.getProductType().equalsIgnoreCase(serviceType);
            boolean isMarket = ProductCodeEnum.AI_MARKET.getProductType().equalsIgnoreCase(serviceType);
            boolean isEbs = ProductCodeEnum.DISK.getProductType().equalsIgnoreCase(serviceType);
            //判断是否rds
            boolean isRds = ProductCodeEnum.RDS.getProductType().equalsIgnoreCase(serviceOrder.getProductCode());
            if (isSfs && ChargeTypeEnum.PrePaid.getType()
                                               .equalsIgnoreCase(applyEntity.getChargeType())) {
                // remove usage
                priceVOs.removeIf(bizBillingPriceVO ->
                                          "resource".equalsIgnoreCase(bizBillingPriceVO.getPriceType())
                                                  && "storageUsage".equalsIgnoreCase(bizBillingPriceVO.getSpecType()));
            }
            List<ServiceOrderPriceDetail> orderPriceDetails = Lists.newArrayList();
            //订单详情根据实际购买时包含的资源计费项来新增
            ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
            priceDetail.setType(serviceOrder.getType());
            priceDetail.setChargeType(detail.getChargeType());
            priceDetail.setOrderSn(serviceOrder.getOrderSn());
            priceDetail.setOrgSid(serviceOrder.getOrgSid());
            priceDetail.setOrderDetailId(detail.getId());
            priceDetail.setVersion(1L);
            if (Objects.nonNull(detail.getEndTime())) {
                priceDetail.setEndTime(detail.getEndTime());
            } else {
                priceDetail.setEndTime(DateUtil.offsetMonth(detail.getStartTime(),
                                                            detail.getDuration() == null ? 0 : detail.getDuration()));
            }
            if (Objects.nonNull(detail.getStartTime())) {
                priceDetail.setStartTime(detail.getStartTime());
            } else {
                priceDetail.setStartTime(serviceOrder.getCreatedDt());
            }
            priceDetail.setCouponAmount(applyEntity.getBizCoupon() == null ?
                                                BigDecimal.ZERO : applyEntity.getBizCoupon().getDiscountAmount());
            BigDecimal totalMonth = priceVOs.stream().map(BizBillingPriceVO::getMonthPrice)
                                            .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ONE);
            // 每种计费项生成对应的详情
            for (BizBillingPriceVO priceVO : priceVOs) {
                ServiceOrderPriceDetail clone = priceDetail.clone();
                clone.setProductCode(Convert.toStr(priceVO.getProductCode(), detail.getServiceType()));
                clone.setServiceType(priceVO.getResourceType());
                clone.setQuantity(ecsFlg || isEbs ? detail.getQuantity() : 1);
                clone.setPriceType(priceVO.getPriceType());
                clone.setPriceDesc(priceVO.getPriceDesc());
                clone.setResourceConfig(priceVO.getResourceConfig());
                clone.setBillingSpec(priceVO.getBillingSpec());
                clone.setSpecType(priceVO.getSpecType());
                clone.setFixedHourPrice(priceVO.getFixedHourPrice());
                clone.setUnitHourPrice(priceVO.getUnitHourPrice());
                clone.setTradeFixedHourPrice(priceVO.getTradeFixedHourPrice());
                clone.setTradeUnitHourPrice(priceVO.getTradeUnitHourPrice());
                clone.setProductType(priceVO.getProductType());
                clone.setFixedMonth(priceVO.getAppoint()? BooleanEnum.YES.getCode():BooleanEnum.NO.getCode());
                //用于存资源标识
                clone.setResourceConfigDesc(priceVO.getIdentification());
                if (NumberUtil.isGreater(priceVO.getOncePrice(), BigDecimal.ZERO)) {
                    clone.setOncePrice(priceVO.getOncePrice());
                    clone.setOriginalCost(clone.getOncePrice());
                    clone.setPrice(clone.getOncePrice());
                    clone.setAmount(clone.getOncePrice());
                    if(isMarket){
                        clone.setAmount(
                                NumberUtil.mul(clone.getOriginalCost(), priceVO.getPlatformDiscount()));
                    }
                } else {
                    if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())) {
                        clone.setPrice(priceVO.getHourPrice());
                        clone.setAmount(priceVO.getHourPrice());
                    } else {
                        clone.setPrice(priceVO.getMonthPrice());
                        clone.setAmount(priceVO.getMonthPrice());
                        if(isRds){
                            clone.setAmount(detail.getAmount());
                        }
                    }
                    clone.setOriginalCost(clone.getAmount());
                    clone.setDiscount(NumberUtil.sub(clone.getOriginalCost(),
                            NumberUtil.mul(clone.getOriginalCost(), priceVO.getPlatformDiscount())));
                }
                clone.setDiscount(NumberUtil.sub(clone.getOriginalCost(),
                        NumberUtil.mul(clone.getOriginalCost(), priceVO.getPlatformDiscount())));
                clone.setTradePrice(NumberUtil.sub(clone.getOriginalCost(), clone.getDiscount()));

                if (isSfs && ChargeTypeEnum.PrePaid.getType()
                                                   .equalsIgnoreCase(applyEntity.getChargeType()) && Objects.isNull(
                        clone.getOncePrice())) {
                    BigDecimal amount = clone.getTradePrice();
                    BigDecimal originalCost = clone.getPrice();
                    if (!priceVO.getAppoint()) {
                        amount = NumberUtil.mul(amount, detail.getDuration());
                        originalCost = NumberUtil.mul(originalCost, detail.getDuration());
                    }
                    clone.setOriginalCost(originalCost);
                    clone.setDiscount(NumberUtil.sub(originalCost, amount));
                    BigDecimal couponAmount = NumberUtil.mul(
                            NumberUtil.div(priceDetail.getCouponAmount(), detail.getQuantity()),
                            BigDecimal.ZERO.compareTo(totalMonth) < 0 ? NumberUtil.div(priceVO.getMonthPrice(),
                                                                                       totalMonth) : BigDecimal.ZERO);
                    clone.setAmount(NumberUtil.sub(amount, couponAmount));
                    clone.setCouponAmount(couponAmount);
                }

                clone.setRefKey(StrUtil.concat(true, priceVO.getResourceType(), "-"));
                if (PUBLIC_ENV.equals(priceVO.getCategory())) {
                    clone.setOriginalCost(priceVO.getOriginalPrice());
                }

                // HPC 专属资源池 产品集合特殊处理
                if (hpcDrpFlg) {
                    clone.setQuantity(detail.getQuantity());
                    BigDecimal amount = clone.getTradePrice();
                    BigDecimal originalCost = clone.getPrice();
                    if (!priceVO.getAppoint()) {
                        amount = NumberUtil.mul(NumberUtil.mul(amount, detail.getDuration(), detail.getQuantity()));
                        originalCost = NumberUtil.mul(NumberUtil.mul(originalCost, detail.getDuration(), detail.getQuantity()));
                    }
                    clone.setOriginalCost(originalCost);
                    clone.setDiscount(NumberUtil.sub(originalCost, amount));
                    BigDecimal couponAmount = BigDecimal.ZERO;
                    if (couponAmountOfDetail.compareTo(BigDecimal.ZERO) > 0) {
                        couponAmount = NumberUtil.mul(
                                NumberUtil.div(couponAmountOfDetail, NumberUtil.mul(detail.getAmount(), detail.getDiscountRatio())), amount)
                                                 .setScale(2, BigDecimal.ROUND_DOWN);
                        usedCouponAmount = NumberUtil.add(usedCouponAmount, couponAmount);
                    }

                    clone.setAmount(NumberUtil.sub(amount, couponAmount));
                    clone.setCouponAmount(couponAmount);
                }
                if (ecsFlg) {
                    clone.setQuantity(detail.getQuantity());
                    BigDecimal amount = clone.getTradePrice();
                    BigDecimal originalCost = clone.getPrice();
                    if (!priceVO.getAppoint()) {
                        amount = NumberUtil.mul(NumberUtil.mul(amount, detail.getDuration(), detail.getQuantity()));
                        originalCost = NumberUtil.mul(NumberUtil.mul(originalCost, detail.getDuration(), detail.getQuantity()));
                    }
                    clone.setOriginalCost(originalCost);
                    clone.setDiscount(NumberUtil.sub(originalCost, amount));
                    clone.setAmount(NumberUtil.sub(amount, result.getCouponAmount()));
                    clone.setCouponAmount(result.getCouponAmount());
                }

                if (ProductCodeEnum.isCmpApiProduct().contains(serviceType) && !ecsFlg && !isRds) {
                    // service_order_price_detail中amount=原价-折扣-优惠券金额
                    clone.setAmount(Optional.ofNullable(clone.getOriginalCost()).orElse(BigDecimal.ZERO)
                                            .subtract(Optional.ofNullable(clone.getDiscount()).orElse(BigDecimal.ZERO))
                                            .subtract(Optional.ofNullable(clone.getCouponAmount())
                                                              .orElse(BigDecimal.ZERO)));
                }

                /*if (OrderType.APPLY.equals(serviceOrder.getType()) && ProductCodeEnum.DCS.getProductType().equals(detail.getServiceType())) {
                    clone.setQuantity(detail.getQuantity());
                    clone.setAmount(detail.getAmount());
                    clone.setOriginalCost(detail.getPrice());
                    clone.setTradePrice(detail.getPrice());
                }*/

                orderPriceDetails.add(clone);
            }
            // HPC 专属资源池 产品集合特殊处理
            if (hpcDrpFlg && couponAmountOfDetail.compareTo(usedCouponAmount) > 0) {
                // 补足没有扣除的优惠卷(指定一个补足)
                BigDecimal disCouponAmount = NumberUtil.sub(couponAmountOfDetail, usedCouponAmount);
                for (ServiceOrderPriceDetail orderPriceDetail : orderPriceDetails) {
                    if (orderPriceDetail.getAmount().compareTo(disCouponAmount) < 0) {
                        continue;
                    }
                    orderPriceDetail.setCouponAmount(NumberUtil.add(orderPriceDetail.getCouponAmount(), disCouponAmount));
                    orderPriceDetail.setAmount(NumberUtil.sub(orderPriceDetail.getAmount(), disCouponAmount));
                    break;
                }
            }
            // 批量新增订单价格明细
            serviceOrderPriceDetailService.saveBatch(orderPriceDetails);
            List<ServiceOrderPriceDetail> priceDetails = applyEntity.getPriceDetails();
            if(ObjectUtil.isEmpty(priceDetails)){
                priceDetails=new ArrayList<>();
            }
            priceDetails.addAll(orderPriceDetails);
            applyEntity.setPriceDetails(priceDetails);
        } catch (Exception e) {
            log.error("新增订单价格明细失败!", e.getMessage());
        }

    }

    /**
     * 保存订单折扣详情
     * @param entity
     */
    public void saveDiscountDetailOfOrder(ApplyEntity entity) {
        try {
            if (CollectionUtil.isEmpty(entity.getPrices())) {
                return;
            }

            Map<String, List<ServiceOrderDetail>> orderDetailMap = entity.getOrderDetails().stream().collect(
                    Collectors.groupingBy(ServiceOrderDetail::getServiceType));
            entity.getPrices().forEach(price -> {
                // 当前折扣策略,平台会初始化-共享折扣策略
                List<BizDiscountPolicy> policies = bizDiscountPolicyMapper.selectList(new QueryWrapper<>());
                BizDiscountPolicy policy = CollectionUtil.isEmpty(policies) ? new BizDiscountPolicy() : policies.get(0);
                if (CollectionUtil.isNotEmpty(price.getDiscountDetails())) {
                    for (DiscountDetailVO detailVO : price.getDiscountDetails()) {
                        Map<String, String> detailMap = detailVO.getDiscountMap();
                        List<ServiceOrderDetail> orderDetails = orderDetailMap.get(detailVO.getProductCode());
                        Long refId = CollectionUtil.isEmpty(orderDetails) ? entity.getOrder().getId() : orderDetails.get(0).getId();
                        detailMap.forEach((id, type) -> {
                            BizDiscountOrder discountOrder = new BizDiscountOrder();
                            discountOrder.setRecordSid(System.currentTimeMillis());
                            discountOrder.setOrderSid(refId);
                            discountOrder.setPolicyType(policy.getPolicyType());
                            discountOrder.setPolicyLevel(policy.getPolicyLevel());
                            discountOrder.setOriginType(type);
                            WebUserUtil.prepareInsertParams(discountOrder);
                            if (DiscountPolicyEnum.CUSTOMER.getCode().equals(type)
                                    || DiscountPolicyEnum.PLATFORM.getCode().equals(type)) {
                                BizDiscount discount = bizDiscountMapper.selectByPrimaryKey(Convert.toLong(id));
                                discountOrder.setDiscountRatio(Convert.toStr(discount.getDiscountRatio()));
                                discountOrder.setScopeType(discount.getScopeType());
                                discountOrder.setScopeValue(discount.getScopeValue());
                            } else if (DiscountPolicyEnum.ENV.getCode().equals(type)){
                                BizBillingStrategyAccountConfig strategyAccountConfig = strategyAccountConfigMapper.selectById(Convert.toInt(id));
                                discountOrder.setDiscountRatio(Convert.toStr(strategyAccountConfig.getDiscount()));
                            }
                            bizDiscountOrderMapper.insert(discountOrder);
                        });
                    }
                }
            });

        } catch (Exception e) {
            log.error("记录订单折扣详情失败！", e.getMessage());
        }
    }

    @Data
    private static class ConfigDesc {
        private String label;

        private String value;
    }


    /**
     * 判断是否为HPC专属资源池
     * @return
     */
    private boolean unUniqueOrderCommon(ApplyServiceVO serviceVO) {
        if (Objects.nonNull(serviceVO)) {
            List<String> productInfos = serviceVO.getProductInfo().stream().map(ProductInfoVO::getProductCode).collect(Collectors.toList());
            if (productInfos.contains(ProductCodeEnum.ECS.getProductType())
                    || productInfos.contains(ProductCodeEnum.RS_BMS.getProductType())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为HPC专属资源池
     * @return
     */
    private boolean isRdsOrder(ApplyServiceVO serviceVO) {
        if (Objects.nonNull(serviceVO)) {
            List<String> productInfos = serviceVO.getProductInfo().stream().map(ProductInfoVO::getProductCode).collect(Collectors.toList());
            if (productInfos.contains(ProductCodeEnum.RDS.getProductType())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为HPC专属资源池
     * @return
     */
    private boolean unUniqueOrder(ApplyServiceVO serviceVO) {
        if (Objects.nonNull(serviceVO)) {
            List<String> productInfos = serviceVO.getProductInfo().stream().map(ProductInfoVO::getProductCode).collect(Collectors.toList());
            if (productInfos.contains(ProductCodeEnum.HPC_DRP.getProductType())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从配置中取出产品规格
     * @param serviceConfig
     * @param flg
     * @return
     */
    protected String getProductCategoryFromServiceConfig(Object serviceConfig, boolean flg) {
        if (flg) {
            JsonNode jsonNode = JsonUtil.fromJson(serviceConfig.toString());

            if(jsonNode == null){
                jsonNode = JsonUtil.fromJson(JsonUtil.toJson(serviceConfig));
            }

            Iterator<Map.Entry<String, JsonNode>> iterator = jsonNode.fields();

            while (iterator.hasNext()) {
                Map.Entry<String, JsonNode> currNode = iterator.next();
                JsonNode chargeNode = currNode.getValue();
                List<JsonNode> billingNodes = chargeNode.findParents(InquiryPriceConstants.CHARGE_ITEM_CATEGORY);
                int n = 0;
                for (JsonNode billingNode : billingNodes) {
                    return billingNode.findValue("category").textValue();
                }
            }

            return null;
        } else {
            return null;
        }
    }
}

