/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.common.util;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;

import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.common.enums.CharatorTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;

import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/04/26 16:07
 */
@Slf4j
public class PasswordUtil {

    public static final Long DEFAULT_CREDENTIAL_EXPIRE_TIME = 30L;
    public static final Boolean DEFAULT_CREDENTIAL_EXPIRE_TIME_VALIDITY = Boolean.FALSE;
    public static final Long DEFAULT_CREDENTIAL_LEAST_USED_DAY = 0L;
    public static final Long DEFAULT_CREDENTIAL_REPEAT_NUM = 3L;
    private static final String LETTER_STORE_HOUSE_KEY = "cloudstar:randomPassword:storehouse:letter";
    private static final String NUMBER_STORE_HOUSE_KEY = "cloudstar:randomPassword:storehouse:number";
    private static final String SPECIAL_CHARACTORS_STORE_HOUSE_KEY = "cloudstar:randomPassword:storehouse:specialCharactors";
    private static final String LENGTH_STORE_HOUSE_KEY = "cloudstar:randomPassword:storehouse:length";
    private static final String OFFSET_STORE_HOUSE_KEY = "cloudstar:randomPassword:storehouse:offset";
    private static final String[] LETTER_STORE_HOUSE = {"0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25"};
    private static final String[] NUMBER_STORE_HOUSE = {"0","1","2","3","4","5","6","7","8","9"};

    private static final List<String> allowedSpecialCharactorList = new ArrayList<>();

    public static final char[] ALLOWED_SPECIAL_CHARACTORS = {
            '`', '~', '@', '#', '$', '%', '^', '&',
            '*', '(', ')', '-', '_', '=', '+', '[',
            '{', '}', ']', '\\', '|', ';', ':', '"',
            '\'', ',', '<', '.', '>', '/', '?'};

    static {

        JedisUtil.INSTANCE.del(LETTER_STORE_HOUSE_KEY);
        JedisUtil.INSTANCE.addSet(LETTER_STORE_HOUSE_KEY,LETTER_STORE_HOUSE);

        JedisUtil.INSTANCE.del(NUMBER_STORE_HOUSE_KEY);
        JedisUtil.INSTANCE.addSet(NUMBER_STORE_HOUSE_KEY,NUMBER_STORE_HOUSE);

        for (int i = 0; i < ALLOWED_SPECIAL_CHARACTORS.length; i++) {
            allowedSpecialCharactorList.add(String.valueOf(i));
        }
        JedisUtil.INSTANCE.del(SPECIAL_CHARACTORS_STORE_HOUSE_KEY);
        JedisUtil.INSTANCE.addSet(SPECIAL_CHARACTORS_STORE_HOUSE_KEY,allowedSpecialCharactorList.toArray(new String[ALLOWED_SPECIAL_CHARACTORS.length]));
    }

    public static PasswordPolicyDTO defaultPolicy() {
        PasswordPolicyDTO passwordPolicyDTO = new PasswordPolicyDTO();
        passwordPolicyDTO.setMinLength(8);
        passwordPolicyDTO.setCharactorType(Lists.newArrayList(
                CharatorTypeEnum.NUMBER.getType()
                , CharatorTypeEnum.LOWERCASE.getType(), CharatorTypeEnum.UPPERCASE.getType()));
        passwordPolicyDTO.setCharactorLimit(6);
        passwordPolicyDTO.setRuleOut(new ArrayList<>());
        passwordPolicyDTO.setLoginfailureEnable(true);
        passwordPolicyDTO.setLoginfailureCount(3);
        passwordPolicyDTO.setAccountValidity(false);
        passwordPolicyDTO.setExpireTime(30);
        passwordPolicyDTO.setPwdExpireTimeValidity(DEFAULT_CREDENTIAL_EXPIRE_TIME_VALIDITY);
        passwordPolicyDTO.setPwdExpireTime(DEFAULT_CREDENTIAL_EXPIRE_TIME);
        passwordPolicyDTO.setPwdLeastUsedDay(DEFAULT_CREDENTIAL_LEAST_USED_DAY);
        passwordPolicyDTO.setPwdRepeatNum(DEFAULT_CREDENTIAL_REPEAT_NUM);
        passwordPolicyDTO.setSecAuth("enable");
        return passwordPolicyDTO;
    }


    public static String generatRandomPassword(PasswordPolicyDTO policy) {
        AuthUser userInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(policy)) {
            policy = defaultPolicy();
        }
        if (ObjectUtils.isEmpty(userInfo) || ObjectUtils.isEmpty(userInfo.getUserSid())){
            userInfo = new AuthUser();
            userInfo.setUserSid(new SecureRandom().nextLong());
        }
        // 获取随机位数
        int length = getRandomLength(policy.getMinLength());
        return getRandomStr(userInfo.getUserSid(), length, policy.getCharactorType());
    }

    /**
     * 获取随机值
     *
     * @param userSid 用户id
     * @param length 随机值长度
     * @param charactorType 策略
     */
    public static String getRandomStr(Long userSid, int length, List<String> charactorType){
        StringBuilder str = new StringBuilder();
        String key = OFFSET_STORE_HOUSE_KEY + userSid;
        if (CollectionUtil.isEmpty(charactorType)){
            List<CharatorTypeEnum> collect = Arrays.stream(CharatorTypeEnum.values()).collect(Collectors.toList());
            List<Integer> randoms = getRandoms(key, length, collect);
            randoms.forEach(v -> str.append(generatRandomChar(collect.get(v))));
        } else {
            List<Integer> randoms = getRandoms(key, length, charactorType);
            randoms.forEach(v -> str.append(generatRandomChar(CharatorTypeEnum.tranFromType(charactorType.get(v)))));
        }
        JedisUtil.INSTANCE.del(key);
        String result = str.toString();
        //是否包含指定值，全都包含则返回，没有则继续生成
        return checkRandom(charactorType, result) ? result : getRandomStr(userSid, length, charactorType);
    }

    /**
     * 检查随机值是否包含指定值
     */
    public static boolean checkRandom(List<String> charactorType, String str){
        if (CollectionUtil.isEmpty(charactorType)){
            return Arrays.stream(CharatorTypeEnum.values()).anyMatch(v -> isCheck(v, str));
        }
        return charactorType.stream().map(CharatorTypeEnum::tranFromType).anyMatch(v -> isCheck(v, str));
    }


    private static final List<String> charList = Arrays.asList("`~!@#$%^&*()+=|{}':;',[].<>/?~！@#￥%……&*（）——+|{}【】‘；：'\"’。，、？".split(""));
    public static boolean isCheck(CharatorTypeEnum charatorType, String str){
        switch (charatorType){
            case LOWERCASE:
                return str.matches(".*[a-z]+.*");
            case UPPERCASE:
                return str.matches(".*[A-Z]+.*");
            case NUMBER:
                return str.matches(".*\\d+.*");
            case SPECIAL_CHARACTOR:
                return charList.parallelStream().anyMatch(str::contains);
        }
        return true;
    }


    /**
     * 获取随机索引集合
     */
    private static List<Integer> getRandoms(String key, int length, Collection<?> collection){
        List<String> list = new ArrayList<>();
        for (int i = 0; i < collection.size(); i++) {
            list.add(String.valueOf(i));
        }
        JedisUtil.INSTANCE.addSet(key, list.toArray(new String[0]));
        List<Integer> integers = new ArrayList<>();
        for (int i = 0; i < length; i++) {
            integers.add(Integer.parseInt(getSecureRandomValue(key)));
        }
        return integers;
    }

    /**
     * 获取随机数长度
     */
    private static int getRandomLength(int minLength){
        if (minLength == 32) {
            return minLength;
        }
        try{
            minLength = RandomUtil.getSecureRandom().nextInt(12 - minLength) + minLength;
        }catch (Exception e){
            log.error("get secureRandom value error",e);
        }
        return minLength;
    }

    /**
     * 获取随机值
     */
    private static String getSecureRandomValue(String key){
        Set<String> set = JedisUtil.INSTANCE.getSet(key);
        String value = "0";
        try{
            if(!CollectionUtils.isEmpty(set)){
                int randomIndex = RandomUtil.getSecureRandom().nextInt(set.size());
                value = Arrays.asList(set.toArray(new String[0])).get(randomIndex);
            }
        }catch (Exception e){
            log.error("get secureRandom value error",e);
        }
        return value;
    }


    /**
     * 生成随机字符
     */
    public static String generatRandomChar(CharatorTypeEnum charatorTypeEnum) {
        Character c = null;
        int rand;
        switch (charatorTypeEnum) {
            case LOWERCASE://随机小写字母
                rand = getNextInt("1");
                rand += 97;
                c = (char) rand;
                break;
            case UPPERCASE://随机大写字母
                rand = getNextInt("1");
                rand += 65;
                c = (char) rand;
                break;
            case NUMBER://随机数字
                rand = getNextInt("2");
                rand += 48;
                c = (char) rand;
                break;
            case SPECIAL_CHARACTOR://随机特殊字符
                rand = getNextInt("3");
                c = ALLOWED_SPECIAL_CHARACTORS[rand];
                break;
            default:
                return "";
        }
        return String.valueOf(c);

    }

    public static boolean validPasswordPolicy(PasswordPolicyDTO passwordPolicyDTO, String str4Authentication) {
        // 长度校核
        if (str4Authentication.length() > 32 || str4Authentication.length() < passwordPolicyDTO.getMinLength()) {
            return false;
        }

        // 不同字符数校核
        Set<Character> strings = new HashSet<>();
        for (char ch : str4Authentication.toCharArray()) {
            strings.add(ch);
        }
        if (strings.size() < passwordPolicyDTO.getCharactorLimit()) {
            return false;
        }

        // 剔除常用密码
        if (CollectionUtil.isNotEmpty(passwordPolicyDTO.getRuleOut())) {
            HashSet<String> ruleOut = new HashSet<>(passwordPolicyDTO.getRuleOut());
            if (ruleOut.contains(str4Authentication)) {
                return false;
            }
        }

        // 校验包含规则
        boolean find = true;
        if (CollectionUtil.isNotEmpty(passwordPolicyDTO.getCharactorType())) {
            for (String s : passwordPolicyDTO.getCharactorType()) {
                Pattern pattern = Pattern.compile(CharatorTypeEnum.getExampleByType(s));
                Matcher matcher = pattern.matcher(str4Authentication);
                if (!matcher.find()) {
                    return false;
                }
            }
        }
        return find;
    }

    /**
     * 获取redis set的随机数
     * @param flag 1为获取0-25，2为获取0-9，3为获取特殊字符索引
     */
    private static Integer getNextInt(String flag){
        switch (flag){
            case "1":
                return Integer.parseInt(getSecureRandomValue(LETTER_STORE_HOUSE_KEY));
            case "2":
                return Integer.parseInt(getSecureRandomValue(NUMBER_STORE_HOUSE_KEY));
            case "3":
                return Integer.parseInt(getSecureRandomValue(SPECIAL_CHARACTORS_STORE_HOUSE_KEY));
            default:
                return 0;
        }
    }

}
