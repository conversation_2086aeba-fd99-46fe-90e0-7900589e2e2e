package cn.com.cloudstar.rightcloud.bss.module.sfs.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.BizProductQuotaMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateRelationMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.BizProductQuota;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplateRelation;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.BizProductQuotaService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfProductTemplateService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 产品限额配置 实现类
 *
 * @Auther: 张淇囿
 * @Date: 2022/04/25
 */
@Service
@Slf4j
public class BizProductQuotaServiceImpl extends ServiceImpl<BizProductQuotaMapper, BizProductQuota> implements
        BizProductQuotaService {

    @Autowired
    private SfProductTemplateRelationMapper productTemplateRelationMapper;

    @Autowired
    private ISfProductTemplateService productTemplateService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Override
    public IPage<BizProductQuota> selectByParams(IPage<BizProductQuota> page, Criteria criteria) {
        return this.baseMapper.selectByParams(page, criteria.getCondition());
    }

    @Override
    public Boolean isMinFrozenAmount(Long serviceId) {
        // serviceId为0代表全部
        if (0L == serviceId) {
            return true;
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByPrimaryKey(serviceId);
        if(serviceCategory.getEntityId() != null && !serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_25920845));
        }
        if (ProductCodeEnum.innerServiceProducts().contains(serviceCategory.getServiceType())) {
            QueryWrapper<SfProductTemplateRelation> queryWrapper = new QueryWrapper<>();
            SfProductTemplateRelation sfProductTemplateRelation = productTemplateRelationMapper.selectOne(
                    queryWrapper.eq("product_id", serviceId));
            if (Objects.isNull(sfProductTemplateRelation)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00016));
            }
            SfProductTemplate productTemplate = productTemplateService.getById(
                    sfProductTemplateRelation.getTemplateId());
            String templateContent = productTemplate.getTemplateContent();
            // ModelArts AI开发平台共享资源池 特殊处理
            if (templateContent.contains("公共资源池")) {
                return true;
            }
            return templateContent.contains(ChargeTypeEnum.PostPaid.getType());
        } else if (ProductCodeEnum.noPostPaidProduct().contains(serviceCategory.getServiceType())){
            return false;
        }
        return true;
    }

    @Override
    public Boolean isHaveNonBillAccount(BizBillingAccount account) {
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectOrderByAdminSid(account.getAdminSid(),account.getEntityId());
        Map<String, ServiceOrder> orderMap = new HashMap<>();
        //获取最新的不计费产品订单,判断是否是不计费产品
        if (CollectionUtil.isNotEmpty(serviceOrders)) {
            for (ServiceOrder serviceOrder : serviceOrders) {
                if (Objects.isNull(orderMap.get(serviceOrder.getServiceId()))){
                    if ("02".equals(serviceOrder.getChargingType())){
                        return true;
                    }
                    //一种不计费产品判断完之后，放入map，避免旧数据影响结果
                    orderMap.put(serviceOrder.getServiceId(),serviceOrder);
                }
            }
        }
        return false;
    }

}
