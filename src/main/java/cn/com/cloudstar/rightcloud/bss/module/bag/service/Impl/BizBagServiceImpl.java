/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.service.impl;


import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst.HttpConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagMapper;
import cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagSpecMapper;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagUser;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.SubscribeBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBagAndSpecResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagResponseVO;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagSpecService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bag.util.ValidUtil;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IBizBillingCycleService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MailService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceCategoryHpcClusterPool;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SidService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.BizBagUserStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.BillType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.DealType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.TradeType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BizBagTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.IdWorker;
import cn.com.cloudstar.rightcloud.oss.common.util.NoUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendNotifyRequest;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.UserSyncRemoteService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.MONTH_PATTERN;

/**
 * 套餐包
 *
 * @author: pujian
 * @date: 2023/03/20 14:55
 */
@Service
@Slf4j
public class BizBagServiceImpl extends ServiceImpl<BizBagMapper, BizBag> implements BizBagService {

    /**
     * 折扣包单位
     */
    public static final String DISCOUNT_UNIT = "折";

    /**
     * 卡时单位
     */
    public static final String CARD_HOUR_UNIT = "卡时";

    @Autowired
    private BizBagMapper bizBagMapper;

    @Autowired
    private BizBagSpecMapper bizBagSpecMapper;

    @Autowired
    private BizBagSpecService bizBagSpecService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SidService sidService;
    @Autowired
    private BizCouponMapper bizCouponMapper;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private BizCouponAccountMapper bizCouponAccountMapper;

    @Autowired
    BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    IBizAccountDealService bizAccountDealService;
    @Autowired
    ServiceOrderDetailMapper serviceOrderDetailMapper;
    @Autowired
    BizBagUserService bizBagUserService;
    @Autowired
    MailService mailService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    private ServiceCategoryService serviceCategoryService;
    @Resource
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private BizCouponResourceMapper bizCouponResourceMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private IBizBillingCycleService bizBillingCycleService;

    @DubboReference
    private UserSyncRemoteService userSyncRemoteService;

    @Autowired
    private OrgService orgService;
    private static final String ORDER = "订购";
    private static final String FREEZE = "freeze";
    private static final Integer MAX_NAME = 28;

    /**
     * 状态：启用
     */
    private static final String ONE = "1";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBag(CreateBizBagRequest request) {
        BizBag bizBag = new BizBag();
        String format = ValidUtil.getUUID();
        String productType = request.getProductType();
        List<ServiceCategory> serviceCategories = serviceCategoryMapper.getServiceCategoryByServiceType(productType);
        Set<Long> entityIds = serviceCategories.stream().map(ServiceCategory::getEntityId).collect(Collectors.toSet());
        if (!entityIds.contains(request.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_728123500));
        }

        long time = System.currentTimeMillis();
        String substring = (time + format).substring(0, 19);
        bizBag.setId(format.substring(4, 23));
        bizBag.setBagId(substring);
        bizBag.setName(request.getName());
        bizBag.setProductType(productType);
        bizBag.setBillingType(request.getBillingType());
        bizBag.setStatus(StatusType.OFFLINE);
        bizBag.setType(request.getType());
        bizBag.setDescription(request.getDescription());
        bizBag.setClusterId(request.getClusterId());
        bizBag.setEntityId(request.getEntityId());
        WebUserUtil.prepareInsertParams(bizBag);
        this.save(bizBag);
        List<BizBagSpec> bagSpecs = request.getBizBagRequests();
        bagSpecs.forEach(spec -> {
            BizBagSpec bizBagSpec = new BizBagSpec();
            BigDecimal specValue = spec.getSpecValue();
            String format1 = ValidUtil.getUUID();
            bizBagSpec.setId(format1.substring(9, 28));
            bizBagSpec.setBagId(bizBag.getBagId());
            bizBagSpec.setSpecValue(specValue);
            if (Objects.nonNull(specValue)) {
                if(StatusType.CARD_HOUR.equals(bizBag.getType())){
                    bizBagSpec.setSpecName( specValue+ CARD_HOUR_UNIT);
                }else{
                    bizBagSpec.setSpecName(specValue.multiply(new BigDecimal("10")).doubleValue() + DISCOUNT_UNIT);
            }
            }
            bizBagSpec.setPeriod(spec.getPeriod());
            bizBagSpec.setPrice(spec.getPrice());
            WebUserUtil.prepareInsertParams(bizBagSpec);
            bizBagSpecMapper.createBizBagSpec(bizBagSpec);
        });
        return bizBag.getBagId();
    }

    @Override
    public IPage<DescribeBizBagResponseVO> listBizBag(DescribeBizBagRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        request.setEntityId(authUserInfo.getEntityId());
        Page<DescribeBizBagResponseVO> pageParams = PageUtil.preparePageParams(request, "status", "desc");
        pageParams.addOrder(new OrderItem("created_dt",false));
        IPage<DescribeBizBagResponseVO> describeBizBagResponseVOIPage = bizBagMapper.listBizBag(pageParams, request);
        describeBizBagResponseVOIPage.getRecords().stream().forEach(resp ->{
            if (resp.getProductType() != null) {
                ProductCodeEnum anEnum = ProductCodeEnum.toEnum(resp.getProductType());
                if (anEnum != null) {
                    resp.setProductName(anEnum.getProductName());
                }
    }
        });

        return describeBizBagResponseVOIPage;
    }
    @Override
    public List<DescribeBagAndSpecResponse> listBizBagAndSpec(DescribeBizBagSpecRequest request) {
        return bizBagMapper.listBizBagAndSpec(request);
    }

    @Override
    public Boolean updateBizBagById(BizBag bizBag) {
        BizBag bag = bizBagMapper.selectById(bizBag.getId());
        if (Objects.isNull(bag)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if ("online".equals(bag.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_906077120));
        }
        bag.setId(bizBag.getId());
        bag.setName(bizBag.getName());
        bag.setDescription(bizBag.getDescription());
        return bizBagMapper.updateById(bag) == 1;
        }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBizBag(String id) {
        BizBag bizBag = bizBagMapper.selectById(id);
        if (Objects.isNull(bizBag)) {
            throw new BizException("套餐包不存在！");
        }
        if ("online".equals(bizBag.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_612346356));
        }

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bizBag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        int flag = bizBagMapper.deleteById(id);
        LambdaQueryWrapper<BizBagSpec> qw = new LambdaQueryWrapper<>();
        qw.eq(BizBagSpec::getBagId, bizBag.getBagId());
        bizBagSpecMapper.delete(qw);
        return flag == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyBag(String id) {
        BizBag bizBag = bizBagMapper.selectById(id);
        if (bizBag == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bizBag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String targetBagId = bizBag.getBagId();
        String name = bizBag.getName();
        if (MAX_NAME <= name.length()) {
            throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_33892673));
        }
        LambdaQueryWrapper<BizBag> qw = new LambdaQueryWrapper<>();
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        String copy = isUs ? "copy" : "复制";
        qw.eq(BizBag::getName, name + "("+ copy +")");
        Integer repeatNum = this.bizBagMapper.selectCount(qw);
        if (Objects.nonNull(repeatNum) && 0 < repeatNum) {
            throw  new BizException("当前套餐包已复制!");
        }

        String format = ValidUtil.getUUID();
        long time = System.currentTimeMillis();
        String substring = (time + format).substring(0, 19);
        bizBag.setBagId(substring);
        bizBag.setId(format.substring(4, 23));
        bizBag.setStatus(StatusType.OFFLINE);
        bizBag.setName(name + "("+ copy +")");

        LambdaQueryWrapper<BizBagSpec> qwSpec = new LambdaQueryWrapper<>();
        qwSpec.eq(BizBagSpec::getBagId, targetBagId);
        List<BizBagSpec> bizBagSpecs = bizBagSpecMapper.selectList(qwSpec);
        if (Objects.nonNull(bizBagSpecs)) {
            bizBagSpecs.forEach(bizBagSpec -> {
                String format1 = ValidUtil.getUUID();
                bizBagSpec.setBagId(bizBag.getBagId());
                bizBagSpec.setId(format1.substring(9, 28));
                bizBagSpecMapper.insert(bizBagSpec);
            });
        }
        return bizBagMapper.insert(bizBag) == 1;
    }

    @Override
    public Boolean checkName(String name) {
        BizBag bizBag = this.baseMapper.selectOne(new LambdaQueryWrapper<BizBag>().eq(BizBag::getName, name));
        return Objects.nonNull(bizBag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subscribe(SubscribeBizBagRequest request) {
        BizBagSpec bizBagSpec = bizBagSpecService.getById(request.getId());
        if (bizBagSpec == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizBag bizBag = lambdaQuery().eq(BizBag::getBagId, bizBagSpec.getBagId()).eq(BizBag::getStatus,StatusType.ONLINE).one();
        if (bizBag == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_133998730));
        }
        AuthUser authUser = AuthUserHolder.getAuthUser();
        Integer available = bizBagUserService.lambdaQuery().eq(BizBagUser::getProductType, bizBag.getProductType())
                                             .eq(BizBagUser::getClusterId, bizBag.getClusterId())
                                             .eq(BizBagUser::getOwnerId, authUser.getUserSid())
                                             .eq(BizBagUser::getStatus, "available")
                                             .count();
        if (available > 0 && BizBagTypeEnum.DISCOUNT.getCode().equalsIgnoreCase(bizBag.getType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00026));
        }
        // 套餐包资源类型仅支持用户当前共享资源池
        Long orgSid = authUser.getOrgSid();
        Criteria criteriaResources = new Criteria();
        List<Long> orgIdList = orgService.selectChildrenOrgIds(orgSid);
        orgIdList.add(orgSid);
        criteriaResources.put("orgSidIn",orgIdList);
        criteriaResources.put("product_type", bizBag.getProductType());
        criteriaResources.put("noInStatusList", Arrays.asList(SfProductEnum.UNSUBSCRIBED.getStatus(),SfProductEnum.PENDING.getStatus()));
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteriaResources);
        if (CollectionUtil.isNotEmpty(sfProductResources)) {
            SfProductResource productRes = CollectionUtil.getFirst(sfProductResources);
            if (!bizBag.getProductType().equals(productRes.getProductType())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_OOO13));
            }
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_OOO14));
        }
        // 根据当前选择账户进行查询
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getByEntityIdAndUserId(bizBag.getEntityId(),
                                                                                              authUser.getUserSid());
        if (FREEZE.equals(bizBillingAccount.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_OOO12));
        }
        AssertUtil.requireNonBlank(bizBillingAccount, WebUtil.getMessage(MsgCd.ERROR_MSG_00027));
        BigDecimal bagPrice = NumberUtil.mul(bizBagSpec.getPrice(), request.getQuantity())
                                        .setScale(2, RoundingMode.DOWN);
        ;
        //插入订单
        ServiceOrder serviceOrder = insertOrder(authUser, bizBagSpec, request, bizBillingAccount, bagPrice);
        //优惠券与资源关联
        addCouponRelation(serviceOrder, request);
        //插入订单明细
        insertOrderDetail(request, bizBagSpec, bagPrice, serviceOrder, bizBag);
        //插入userbag
        BizBagUser bizBagUser = insertBizBagUser(bizBagSpec, bizBag, authUser, serviceOrder, bizBillingAccount,
                                                 bizBag.getClusterId());
        // 收支明细 扣钱
        cost(serviceOrder, bizBillingAccount, bizBagUser);

        sendNotify(bizBagUser, authUser, "订购", BizBagTypeEnum.getByCode(bizBag.getType()).getTypeName());

    }

    private void addCouponRelation(ServiceOrder serviceOrder, SubscribeBizBagRequest request) {
        BigDecimal couponDiscount = serviceOrder.getCouponDiscount();
        if (Objects.nonNull(couponDiscount) && couponDiscount.compareTo(BigDecimal.ZERO) > 0) {
            // 优惠券-资源
            BizCoupon bizCoupon = bizCouponMapper.selectById(request.getCouponId());
            BizCouponResource bizCouponResource = new BizCouponResource();
            bizCouponResource.setCouponSid(bizCoupon.getCouponSid());
            bizCouponResource.setCouponNo(bizCoupon.getCouponNo());
            bizCouponResource.setResourceType("套餐包");
            bizCouponResource.setOrderId(serviceOrder.getId());
            WebUserUtil.prepareInsertParams(bizCouponResource);
            bizCouponResourceMapper.insert(bizCouponResource);
        }
    }

    private void sendNotify(BizBagUser bizBagUser, AuthUser authUser, String type, String bagTypeName) {
        String msgid = null;
        String msgid1 = null;
        if (ORDER.equals(type)){
            msgid = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_BAG_ORDER;
            msgid1 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_BAG_ORDER;
        }else{
            msgid = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_BAG_RENEW;
            msgid1 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_BAG_RENEW;
        }
        //发送通知
        //用户
        SendNotifyRequest sendNotifyRequest = new SendNotifyRequest();
        sendNotifyRequest.setIsCC(false);
        sendNotifyRequest.setMessageId(msgid);
        sendNotifyRequest.setMessageType(Arrays.asList(0, 1, 2));
        sendNotifyRequest.getToUserIds().add(authUser.getUserSid());
        HashMap<String, String> content = new HashMap<>(16);
        content.put("userAccount", authUser.getAccount());
        content.put("type", type);
        content.put("bagType", bagTypeName);
        content.put("bagID", bizBagUser.getBagId() + "");
        content.put("discount",bizBagUser.getBagSpecName());
        content.put("startDate", DateUtil.format(bizBagUser.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
        content.put("endDate", DateUtil.format(bizBagUser.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        content.put("owner", authUser.getAccount());
        content.put("recipient", authUser.getAccount());
        sendNotifyRequest.setMessageContent(content);
        mailService.sendNotify(sendNotifyRequest);
        //给运营管理员发消息
        List<User> users = userMapper.selectUserByDataScope();
        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.setMsgId(msgid1);
        baseNotificationMqBean.setMap(content);
        //管理员
        if (bizBagUser.getEntityId() != null) {
            content.put("entityId", bizBagUser.getEntityId().toString());
            baseNotificationMqBean.setEntityId(bizBagUser.getEntityId());
        }
        baseNotificationMqBean.getImsgUserIds()
                              .addAll(users.stream().map(User::getUserSid).collect(Collectors.toSet()));
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                      baseNotificationMqBean);
    }

    private BizBagUser insertBizBagUser(BizBagSpec bizBagSpec, BizBag bizBag, AuthUser authUser,
                                        ServiceOrder serviceOrder,BizBillingAccount account, String clusterId) {
        //插入userbag
        BizBagUser bizBagUser = new BizBagUser();
        bizBagUser.setId(ID_WORKER.nextId() + "");
        bizBagUser.setBagId(bizBagSpec.getBagId());
        bizBagUser.setBagType(bizBag.getType());
        bizBagUser.setBagValue(bizBagSpec.getSpecValue());
        bizBagUser.setEndTime(serviceOrder.getOrderDetails().get(0).getEndTime());
        bizBagUser.setStartTime(serviceOrder.getOrderDetails().get(0).getStartTime());
        bizBagUser.setBagInstUuid(ID_WORKER.nextId() + "");
        bizBagUser.setOrderSn(serviceOrder.getOrderSn());
        bizBagUser.setProductType(bizBag.getProductType());
        bizBagUser.setClearPolicy(0L);
        bizBagUser.setBagSpecName(bizBagSpec.getSpecName());
        bizBagUser.setBillingType(ChargeTypeEnum.PrePaid.getType());
        bizBagUser.setOrgSid(authUser.getOrgSid());
        bizBagUser.setOwnerId(authUser.getUserSid() + "");
        if (BizBagTypeEnum.CARD_HOUR.getCode().equals(bizBagUser.getBagType())) {
            bizBagUser.setStatus(BizBagUserStatus.NOT_USED);
            bizBagUser.setClearPolicy(1L);
        }else {
            bizBagUser.setStatus(BizBagUserStatus.AVAILABLE);
        }
        bizBagUser.setOrgName(serviceOrder.getAccountName());
        bizBagUser.setEntityId(account.getEntityId());
        bizBagUser.setEntityName(account.getEntityName());
        bizBagUser.setClusterId(clusterId);
        WebUserUtil.prepareInsertParams(bizBagUser, authUser.getAccount());
        bizBagUserService.save(bizBagUser);
        return bizBagUser;
    }

    private void insertOrderDetail(SubscribeBizBagRequest request, BizBagSpec bizBagSpec, BigDecimal bagPrice,
                                   ServiceOrder serviceOrder,
                                   BizBag bizBag) {
        //订单详情
        ServiceOrderDetail orderDetail = new ServiceOrderDetail();
        serviceOrder.getOrderDetails().add(orderDetail);
        orderDetail.setOrderId(serviceOrder.getId());
        orderDetail.setServiceId(-1L);
        orderDetail.setChargeType(ChargeTypeEnum.PrePaid.getType());
        orderDetail.setServiceConfig(JSON.toJSONString(bizBagSpec));

        orderDetail.setVersion(1L);
        orderDetail.setOrgSid(serviceOrder.getOrgSid());
        String productCode = ProductCodeEnum.BIZ_BAG.getProductType();
        orderDetail.setServiceType(productCode);
        orderDetail.setQuantity(request.getQuantity());
        Integer period = bizBagSpec.getPeriod() * request.getQuantity();
        orderDetail.setDuration(period);
        if (request.getStartTime() == null) {
            orderDetail.setStartTime(Calendar.getInstance().getTime());
        } else {
            orderDetail.setStartTime(request.getStartTime());
        }
        ArrayList<Object> labels = Lists.newArrayList();
        labels.add(MapsKit.of("label", "套餐ID", "value", bizBagSpec.getBagId()));
        labels.add(MapsKit.of("label", "套餐类型", "value", BizBagTypeEnum.getByCode(bizBag.getType()).getTypeName()));
        labels.add(MapsKit.of("label", "产品", "value", bizBag.getProductType()));
        labels.add(MapsKit.of("label", "规格", "value", bizBagSpec.getSpecName()));
        orderDetail.setProductConfigDesc(JSON.toJSONString(labels));

        //设置额外配置费用,服务费用，资源费用
        orderDetail.setOncePrice(BigDecimal.ZERO);
        orderDetail.setServicePrice(BigDecimal.ZERO);
        orderDetail.setResourcePrice(bagPrice);
        // 单价
        orderDetail.setPrice(new BigDecimal(bizBagSpec.getPrice()));
        orderDetail.setAmount(bagPrice);
        Integer duration = Convert.toInt(orderDetail.getDuration(), 0);
        if (orderDetail.getDuration() != null && duration > 0) {
            orderDetail.setEndTime(DateUtil.offsetMonth(orderDetail.getStartTime(), duration));
        }
        serviceOrderDetailMapper.insert(orderDetail);
    }


    private ServiceOrder insertOrder(AuthUser authUser, BizBagSpec bizBagSpec, SubscribeBizBagRequest request,
                                     BizBillingAccount bizBillingAccount, BigDecimal bagPrice) {
        ServiceOrder serviceOrder = new ServiceOrder();
        // 订单、服务实例名称追加时间戳，区分重名
        String productName = ProductCodeEnum.BIZ_BAG.getProductName();
        serviceOrder.setName(productName);
        serviceOrder.setType(request.getOrderType());
        serviceOrder.setOwnerId(authUser.getUserSid() + "");
        serviceOrder.setProcessFlag("01");
        serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
        serviceOrder.setStatus(OrderStatus.COMPLETED);
        serviceOrder.setEntityName(bizBillingAccount.getEntityName());
        serviceOrder.setEntityId(bizBillingAccount.getEntityId());
        serviceOrder.setFinalCost(bagPrice);
        serviceOrder.setOriginalCost(bagPrice);
        //设置优惠券金额
        if (request.getCouponId() != null) {
            BizCoupon bizCoupon = bizCouponMapper.selectById(request.getCouponId());
            if (bizCoupon != null && bizCoupon.getReductionCondition().compareTo(bagPrice) <= 0) {
                preStationBizCoupon(bizCoupon.getCouponSid(), bizBillingAccount.getId());
                BigDecimal couponDiscount = bizCoupon.getDiscountAmount();
                if (NumberUtil.isGreater(bizCoupon.getDiscountAmount(),serviceOrder.getFinalCost())) {
                    couponDiscount = serviceOrder.getFinalCost();
                }
                serviceOrder.setCouponDiscount(couponDiscount);
                BigDecimal finalCost = serviceOrder.getFinalCost().subtract(serviceOrder.getCouponDiscount());
                finalCost = finalCost.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : finalCost;
                serviceOrder.setFinalCost(finalCost);
            }
        }
        if (OrderType.RENEW.equals(request.getOrderType())) {
            //设置续订前时间 和续订后时间

        }
        serviceOrder.setCurrAmount(serviceOrder.getFinalCost());

        serviceOrder.setOrgDiscount(BigDecimal.ZERO);

        serviceOrder.setOrgSid(authUser.getOrgSid());

        serviceOrder.setPayTime(new Date());
        serviceOrder.setBizBillingAccountId(bizBillingAccount.getId());

        WebUserUtil.prepareInsertParams(serviceOrder, authUser.getAccount());
        serviceOrder.setProductName(productName);
        serviceOrder.setAccountName(bizBillingAccount.getAccountName());
        // 结算类型

        serviceOrder.setSettlementType("标准价");
        // 代客下单管理员ID
        // 订单来源
        if (OrderType.APPLY.equals(serviceOrder.getType())) {
            serviceOrder.setOrderSourceSn(serviceOrder.getOrderSn());
        } else if (OrderType.RENEW.equals(serviceOrder.getType())) {
            BizBag bizBag = lambdaQuery().eq(BizBag::getBagId, bizBagSpec.getBagId()).one();
            BizBagUser bizBagUser = bizBagUserService.lambdaQuery().eq(BizBagUser::getProductType, bizBag.getProductType())
                                                     .eq(BizBagUser::getOwnerId, authUser.getUserSid())
                                                     .eq(BizBagUser::getStatus, "available")
                                                     .eq(BizBagUser::getBagId, bizBagSpec.getBagId())
                                                     .one();
            if (Objects.nonNull(bizBagUser)) {
                serviceOrder.setOrderSourceSn(bizBagUser.getOrderSn());
            }
        }
        this.serviceOrderMapper.insert(serviceOrder);
        return serviceOrder;
    }

    private void preStationBizCoupon(Long couponSid, Long accountId) {
        Criteria criteria = new Criteria();
        criteria.put("accountId", accountId);
        criteria.put("couponSid", couponSid);
        QueryWrapper<BizCouponAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                    .eq(BizCouponAccount::getAccountId, accountId)
                    .eq(BizCouponAccount::getCouponSid, couponSid);
        List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectList(queryWrapper);
        AssertUtil.requireNonBlank(bizCouponAccounts, "你选择的优惠券未找到,请选择其他优惠券.");
        BizCouponAccount getCouponAccount = bizCouponAccounts.get(0);
        if (!CouponStatusEnum.UNUSED.getCode().equalsIgnoreCase(getCouponAccount.getCouponStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1219130789));
        }
        BizCouponAccount bizCouponAccount = bizCouponAccounts.get(0);
        bizCouponAccount.setCouponStatus(CouponStatusEnum.USED.getCode());
        WebUserUtil.prepareUpdateParams(bizCouponAccount);
        bizCouponAccountMapper.updateById(bizCouponAccount);
    }


    public void cost(ServiceOrder serviceOrder, BizBillingAccount bizBillingAccount,
                     BizBagUser bizBagUser) {
        ServiceOrderDetail detail = serviceOrder.getOrderDetails().get(0);
        InstanceGaapCost cost = new InstanceGaapCost();
        cost.setQuantity(detail.getQuantity());
        cost.setOrderId(serviceOrder.getId().toString());
        cost.setOrderSn(serviceOrder.getOrderSn());
        cost.setPayTime(new Date());
        cost.setUsageStartDate(detail.getStartTime());
        cost.setUsageEndDate(detail.getEndTime());
        cost.setUsageCount(
                cn.hutool.core.date.DateUtil.betweenDay(detail.getStartTime(), detail.getEndTime(), false) + "天");
        cost.setBillType(BillType.fromChargeTypeEnum(detail.getChargeType()));
        cost.setBillingCycle(cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
        cost.setOrgSid(serviceOrder.getOrgSid());
        cost.setOwnerId(serviceOrder.getOwnerId().toString());
        cost.setBillNo(NoUtil.generateNo("ZD"));
        cost.setCurrency("CNY");
        cost.setPriceType("resource");
        cost.setProductCode(ProductCodeEnum.BIZ_BAG.getProductType());
        cost.setProductName(ProductCodeEnum.toEnum(bizBagUser.getProductType()).getProductName());
        cost.setBillSource("platform");
        cost.setUserAccountId(bizBillingAccount.getId());
        cost.setPrice(serviceOrder.getFinalCost());
        cost.setResourceId(bizBagUser.getBagInstUuid());
        cost.setInstanceName("套餐包-"+ BizBagTypeEnum.getByCode(bizBagUser.getBagType()).getTypeName());
        cost.setInstanceId(bizBagUser.getBagInstUuid());
        cost.setEntityId(bizBillingAccount.getEntityId());
        cost.setEntityName(bizBillingAccount.getEntityName());
        cost.setConfiguration(bizBagUser.getBagSpecName());
        // originalCost 原价
        BigDecimal originalCost = serviceOrder.getOriginalCost();
        // tradeFinalCost 最终价格
        BigDecimal finalCost = serviceOrder.getFinalCost();

        cost.setCouponDiscount(serviceOrder.getCouponDiscount());

        setUsedCost(bizBillingAccount, cost, finalCost);
        bizBillingAccount.setBalance(
                NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
        bizBillingAccount.setCreditLine(NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
        bizBillingAccount.setBalanceCash(NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
        cost.setPretaxGrossAmount(originalCost);
        cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
        cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
        cost.setBizBillingAccount(
                BeanUtil.toBean(bizBillingAccount,
                                cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount.class));
        cost.setType(serviceOrder.getType());
        cost.setCloudEnvId(null);
        cost.setCloudEnvName(null);
        cost.setCloudEnvType(null);
        cost.setRegion(null);
        cost.setDistributorName(bizBillingAccount.getDistributorName());
        cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
        bizBillingAccountMapper.updateById(bizBillingAccount);
        //修改业务标识
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        updateBusinessArrearageTag(bizBillingAccount,user);
        try {
            InstanceGaapCost instanceGaapCost = mongoTemplate.insert(cost);
            List<InstanceGaapCost> costList = Arrays.asList(instanceGaapCost);
            this.insertBillCycleInfo(costList);
            this.insertAccountDeal(costList, serviceOrder);
        } catch (Exception e) {
            log.error("入账失败", e);
        }
    }

    private void updateBusinessArrearageTag(BizBillingAccount bizBillingAccount, User user) {
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
            } else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userMapper.updateByPrimaryKeySelective(user);
            userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));

        } else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList =  tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (org.springframework.util.CollectionUtils.isEmpty(accountIdList)) {
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "["
                                        + org.apache.commons.lang3.StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(org.apache.commons.lang3.StringUtils.join(tagList, ";"));
                    userMapper.updateByPrimaryKeySelective(user);
                    userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));
                }
            }
        }
    }

    private static final IdWorker ID_WORKER = new IdWorker();

    public void insertAccountDeal(List<InstanceGaapCost> costs, ServiceOrder serviceOrder) {
        List<BizAccountDeal> deals = Lists.newArrayList();
        boolean releaseBeforeEndTime = false;
        costs.forEach(cost -> {
            BizAccountDeal accountDeal = new BizAccountDeal();
            accountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
            if (serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) >= 0) {
                accountDeal.setType(DealType.OUT);
            } else {
                accountDeal.setType(DealType.IN);
            }
            BigDecimal tradePrice = cost.getPretaxAmount();
            accountDeal.setTradeType(
                    tradePrice.compareTo(BigDecimal.ZERO) < 0 ? TradeType.REFUND : TradeType.PAY);
            accountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
            accountDeal.setEnvType(cost.getCloudEnvType());
            accountDeal.setEnvName(cost.getCloudEnvName());
            accountDeal.setOrderNo(serviceOrder.getOrderSn());
            accountDeal.setBillNo(cost.getBillNo());
            accountDeal.setRemark(cost.getDescription());
            accountDeal.setBillingCycle(
                    LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
            cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount account = cost.getBizBillingAccount();
            accountDeal.setAccountSid(account.getId());
            accountDeal.setAccountName(account.getAccountName());
            accountDeal.setOrgSid(account.getOrgSid());
            accountDeal.setUserSid(account.getId());
            accountDeal.setDealTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            accountDeal.setBalance(account.getBalance());
            accountDeal.setBalanceCredit(account.getCreditLine());
            accountDeal.setBalanceCash(account.getBalanceCash());
            accountDeal.setEntityId(serviceOrder.getEntityId());
            accountDeal.setEntityName(serviceOrder.getEntityName());
            String priceType = cost.getPriceType();
            String priceTypeDes = "费用";
            accountDeal.setRemark(StrUtil.concat(true, ProductCodeEnum.BIZ_BAG.getProductName(), priceTypeDes));
            cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil.prepareInsertParams(accountDeal,
                                                                                    serviceOrder.getCreatedBy());
            BigDecimal couponAmount = cost.getCouponAmount();
            //优惠券抵扣为0元
            if (cost.getCouponDiscount() != null && cost.getCouponDiscount().compareTo(BigDecimal.ZERO) > 0
                    && cost.getPretaxAmount().compareTo(BigDecimal.ZERO) >= 0) {
                // 采用余额
                accountDeal.setRemark(accountDeal.getRemark() + "(优惠券抵扣" + cost.getCouponDiscount().setScale(2,
                                                                                                             RoundingMode.HALF_UP)
                                              + "元)");

                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setTradeChannel(RechargeTypeEnum.COUPON.getCode());
                bizAccountDeal.setCouponAmount(cost.getCouponDiscount());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setBalanceCash(NumberUtil.add(account.getBalanceCash(), cost.getCouponAmount()));
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }

            if (NumberUtil.isGreater(couponAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(couponAmount, BigDecimal.ZERO)) {
                // 现金券余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? couponAmount.abs() : couponAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal cashAmount = cost.getCashAmount();
            if (NumberUtil.isGreater(cashAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(cashAmount, BigDecimal.ZERO)) {
                // 采用余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? cashAmount.abs() : cashAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal creditAmount = cost.getCreditAmount();
            if (NumberUtil.isGreater(creditAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(creditAmount, BigDecimal.ZERO)) {
                // 信用额度
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? creditAmount.abs() : creditAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo(BillingConstants.BILL_NO_PREFIX));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
        });
        if (CollectionUtil.isNotEmpty(deals)) {
            bizAccountDealService.saveBatch(deals);
        }
    }


    private void setUsedCost(BizBillingAccount bizBillingAccount, InstanceGaapCost cost,
                             BigDecimal finalCost) {
        cost.setCashAmount(BigDecimal.ZERO);
        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setCouponAmount(BigDecimal.ZERO);
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(bizBillingAccount.getBalanceCash(), BigDecimal.ZERO),
                    finalCost)) {
                cost.setCouponAmount(finalCost);
            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, bizBillingAccount.getBalanceCash());
                cost.setCouponAmount(bizBillingAccount.getBalanceCash());
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(bizBillingAccount.getBalance(), BigDecimal.ZERO),
                        overCost)) {
                    cost.setCashAmount(overCost);
                } else {
                    overCost = NumberUtil.sub(overCost, bizBillingAccount.getBalance());
                    cost.setCashAmount(bizBillingAccount.getBalance());
                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(bizBillingAccount.getCreditLine(), BigDecimal.ZERO),
                            overCost)) {
                        cost.setCreditAmount(overCost);
                    } else {
                        throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_643345578));
                    }
                }
            }
        } else {
            cost.setCashAmount(finalCost);
        }
    }


    /**
     * 生成账单周期表数据
     *
     * @param costs
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertBillCycleInfo(List<InstanceGaapCost> costs) {
        costs.forEach(cost -> {
            BillBillingCycleCost cycleCost = new BillBillingCycleCost();
            cycleCost.setBillingCycle(cost.getBillingCycle());
            cycleCost.setBillNo(NoUtil.generateNo("BP"));
            cycleCost.setCloudEnvType(cost.getCloudEnvType());
            cycleCost.setCloudEnvName(cost.getCloudEnvName());
            cycleCost.setProductCode(cost.getProductCode());
            cycleCost.setProductName(cost.getProductName());
            cycleCost.setBillStartTime(cost.getUsageStartDate());
            cycleCost.setBillEndTime(cost.getUsageEndDate());
            cycleCost.setPayTime(cost.getPayTime());
            cycleCost.setOfficialAmount(cost.getPretaxGrossAmount());
            cycleCost.setDiscountAmount(cost.getPretaxAmount());
            cycleCost.setCashAmount(cost.getCashAmount());
            cycleCost.setCreditAmount(cost.getCreditAmount());
            cycleCost.setVoucherAmount(cost.getCouponAmount());
            cycleCost.setInvoiceAmount(cost.getCashAmount());
            cycleCost.setEntityId(cost.getEntityId());
            cycleCost.setEntityName(cost.getEntityName());
            //抹零金额
            cycleCost.setEraseZeroAmount(cost.getEraseZeroAmount());
            if (cost.getUserAccountId() != null) {
                cycleCost.setOwnerId(cost.getUserAccountId());
            }
            cycleCost.setOrgId(cost.getOrgSid());
            cycleCost.setOrgName(cost.getOrgName());
            cycleCost.setPriceType(cost.getPriceType());
            cycleCost.setBillType(cost.getBillType());
            BigDecimal cashAmount = cost.getCashAmount() != null ? cost.getCashAmount() : new BigDecimal(0);
            BigDecimal creditAmount = cost.getCreditAmount() != null ? cost.getCreditAmount() : new BigDecimal(0);
            BigDecimal voucherAmount = cost.getCouponAmount() != null ? cost.getCouponAmount() : new BigDecimal(0);
            cycleCost.setAmount(
                    (cashAmount.add(creditAmount).add(voucherAmount)).setScale(3, BigDecimal.ROUND_HALF_UP));
            cycleCost.setCouponDiscount(cost.getCouponDiscount());
            cycleCost.setCreateDt(new Date());
            BillBillingCycleCost insert = mongoTemplate.insert(cycleCost);
            //保存拆分账单周期数据
            bizBillingCycleService.splitAndSaveCycleItem(cycleCost);
            //更新账单明细
            Query query = new Query();
            Update update = new Update();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(cost.getMongoId()));
            update.set("billBillingCycleId", insert.getId().toString());
            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");
            if (updateResult.getMatchedCount() > 0) {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------success");
            } else {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------Failed-----{}",
                          JSON.toJSONString(updateResult));
            }
        });

    }


    @Override
    public void renew(SubscribeBizBagRequest request) {
        BizBagSpec bizBagSpec = bizBagSpecService.getById(request.getId());
        if (Objects.isNull(bizBagSpec)){
            throw new BizException(HttpConst.Unauthorized.getType());
        }
        BizBag bizBag = lambdaQuery().eq(BizBag::getBagId, bizBagSpec.getBagId()).one();
        AuthUser authUser = AuthUserHolder.getAuthUser();
        BizBagUser one = bizBagUserService.lambdaQuery().eq(BizBagUser::getProductType, bizBag.getProductType())
                                          .eq(BizBagUser::getOwnerId, authUser.getUserSid())
                                          .eq(BizBagUser::getStatus, "available")
                                          .eq(BizBagUser::getBagId, bizBagSpec.getBagId())
                                          .one();
        if (one == null) {
            throw new BizException(HttpConst.Unauthorized.getType());
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(one.getOrgSid(), authUserInfo.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //比较续订资源和当前资源  8折hpc 只能续订8折hpc 可以选择不同时长
        if (!one.getBagId().equals(bizBagSpec.getBagId()) || one.getBagValue().compareTo(bizBagSpec.getSpecValue())
                != 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00029));
        }
        BizBillingAccount bizBillingAccount = bizBillingAccountService.getByEntityIdAndUserId(one.getEntityId(),
                                                                                              authUser.getUserSid());
        AssertUtil.requireNonBlank(bizBillingAccount, WebUtil.getMessage(MsgCd.ERROR_MSG_00027));
        BigDecimal bagPrice = NumberUtil.mul(bizBagSpec.getPrice(), request.getQuantity())
                                        .setScale(2, RoundingMode.DOWN);
        //插入订单
        ServiceOrder serviceOrder = insertOrder(authUser, bizBagSpec, request, bizBillingAccount, bagPrice);
        //优惠券与资源关联
        addCouponRelation(serviceOrder, request);
        //上次结束的时间为下个周期的开始时间
        request.setStartTime(one.getEndTime());
        //插入订单明细
        insertOrderDetail(request, bizBagSpec, bagPrice, serviceOrder, bizBag);
        // 收支明细 扣钱
        cost(serviceOrder, bizBillingAccount, one);
        //更新userbag
        one.setEndTime(serviceOrder.getOrderDetails().get(0).getEndTime());
        bizBagUserService.updateById(one);
        sendNotify(one, authUser, "续订",BizBagTypeEnum.getByCode(bizBag.getType()).getTypeName());
    }

    @Override
    public Boolean online(String id, String productType) {
        //查询套餐包里面是否有规格
        BizBag bag = this.baseMapper.selectById(id);
        if(bag == null){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String bagId = bag.getBagId();
        List<BizBagSpec> specList = bizBagSpecMapper.selectList(new QueryWrapper<BizBagSpec>().eq("bag_id", bagId));
        if (CollectionUtils.isEmpty(specList)) {
            throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_588254963));
        }
        //查询产品类型和状态来判断是否能上架
        QueryWrapper<BizBag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_type", productType);
        queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
        queryWrapper.eq("status", StatusType.ONLINE);
        List<Map<String, Object>> list = this.baseMapper.selectMaps(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_361071887));
        }
            BizBag bizBag = this.baseMapper.selectById(id);
            bizBag.setStatus(StatusType.ONLINE);
        return bizBagMapper.updateById(bizBag) == 1;
    }

    @Override
    public Boolean offline(String id) {
        BizBag bizBag = this.baseMapper.selectById(id);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (bizBag == null || !Objects.equals(bizBag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if(StatusType.OFFLINE.equals(bizBag.getStatus())){
            throw new BizException("套擦包已经下架，请勿重复下架!");
        }
        bizBag.setStatus(StatusType.OFFLINE);
        return bizBagMapper.updateById(bizBag) == 1;
    }

    @Override
    public List<DescribeBizBagProductResponse> listBizBagProduct(String type) {
        ArrayList<DescribeBizBagProductResponse> responses = new ArrayList<>();
        if (StatusType.CARD_HOUR.equals(type)) {
            LambdaQueryWrapper<ServiceCategory> tWrapper = new LambdaQueryWrapper<>();
            tWrapper.eq(ServiceCategory::getServiceType,ProductCodeEnum.MODELARTS.getProductType()).eq(ServiceCategory::getEntityId,RequestContextUtil.getEntityId());
            Integer count = serviceCategoryMapper.selectCount(tWrapper);
            if(count > 0){
                responses.add(new DescribeBizBagProductResponse(ProductCodeEnum.MODELARTS.getProductName(),ProductCodeEnum.MODELARTS.getProductType(),null));
            }
        } else {
            List<SfServiceCategoryHpcClusterPool> hpcClusterPools = serviceCategoryMapper.selectServiceCategoryHPCByEntityId(
                    RequestContextUtil.getEntityId());
            for (SfServiceCategoryHpcClusterPool data : hpcClusterPools) {
                responses.add(new DescribeBizBagProductResponse(data.getClusterName(), data.getServiceType(),
                                                                data.getClusterId()));
        }
    }
        return responses;
    }
}
