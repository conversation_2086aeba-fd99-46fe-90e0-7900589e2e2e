<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductCategoryCatalogMapper">

    <select id="pageSfProductCategoryCatalog" resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductCategoryCatalog">
        select * from sf_product_category_catalog
        <where>
            <if test="condition.categoryNameLike != null and condition.categoryNameLike != ''">
                and category_name like concat('%',#{condition.categoryNameLike},'%')
            </if>
        </where>
        order by category_type desc,sort_number asc
    </select>

    <select id="sfProductCategoryCatalogList" resultType="cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductCategoryCatalog">
        select * from sf_product_category_catalog
        <where>
            <if test="condition.categoryNameLike != null and condition.categoryNameLike != ''">
                and category_name like concat('%',#{condition.categoryNameLike},'%')
            </if>
        </where>
        order by category_type desc,sort_number asc
    </select>

    <select id="findServiceIdByCategory" parameterType="map" resultType="long">
        select sca.id from sf_service_catalog_relation scr,sf_service_category sca

        where scr.service_id=sca.id and  scr.catalog_id=#{categoryId} and
            entity_id=#{entitySid}
    </select>

</mapper>
