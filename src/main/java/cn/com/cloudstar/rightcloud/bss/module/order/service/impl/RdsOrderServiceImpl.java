/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import com.google.common.collect.Lists;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import cn.com.cloudstar.rightcloud.bss.common.constants.BusinessError;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.RdsDataVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.CreateRdsRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResRdsService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;

@Service
public class RdsOrderServiceImpl extends AbstractOrderService {
    @Autowired
    private ResRdsService resRdsService;

    @Override
    public String apply(ApplyServiceVO serviceVO) {
        ApplyEntity applyEntity = before();

        validateSpec(serviceVO);

        execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);

        after(applyEntity);
        return result;
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
        CreateRdsRequest request = BeanConvertUtil.convert(productInfoVO, CreateRdsRequest.class);
        JSONObject jsonObject = JSONUtil.parseObj(serviceVO.getProductInfo().get(0).getData());
        RdsDataVO dataVO = BeanConvertUtil.convert(jsonObject.get("rds"), RdsDataVO.class);
        request.setData(dataVO);
        RestResult result = resRdsService.createRdsDBInstance(request);
        if (result.getStatus() && result.getMessage() != null) {
            applyEntity.setResourceId(Lists.newArrayList(result.getData().toString()));
        } else {
            if (Objects.equals(BusinessError.QUOTA_ERROR, result.get("code"))
                    || Objects.equals(BusinessError.BAD_PARAM, result.get("code"))) {
                throw new BizException(Convert.toStr(result.getMessage()));
            }
        }
        return null;
    }


}
