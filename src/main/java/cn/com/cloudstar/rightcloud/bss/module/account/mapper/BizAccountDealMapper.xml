<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizAccountDealMapper">


    <sql id="Example_Where_Clause">
        <where>

            <if test="condition.flowNo != null">
                and d.flow_no = #{condition.flowNo}
            </if>
            <if test="condition.flowNoLike != null">
                and d.flow_no like concat('%', #{condition.flowNoLike}, '%')
            </if>
            <if test="condition.type != null">
                and d.type = #{condition.type}
            </if>
            <if test="condition.tradeType != null">
                and d.trade_type = #{condition.tradeType}
            </if>
            <if test="condition.tradeChannel != null">
                and d.trade_channel = #{condition.tradeChannel}
            </if>
            <if test="condition.tradeNo != null">
                and d.trade_no = #{condition.tradeNo}
            </if>
            <if test="condition.tradeNoLike != null">
                and d.trade_no like concat('%', #{condition.tradeNoLike}, '%')
            </if>
            <if test="condition.orderNo != null">
                and d.order_no = #{condition.orderNo}
            </if>
            <if test="condition.orderNoLike != null">
                and d.order_no like concat('%', #{condition.orderNoLike}, '%')
            </if>
            <if test="condition.billNo != null">
                and d.bill_no = #{condition.billNo}
            </if>
            <if test="condition.billNoLike != null">
                and d.bill_no like concat('%', #{condition.billNoLike}, '%')
            </if>
            <if test="condition.remark != null">
                and d.remark = #{condition.remark}
            </if>
            <if test="condition.billingCycle != null">
                and d.billing_cycle = #{condition.billingCycle}
            </if>
            <if test="condition.amount != null">
                and d.amount = #{condition.amount}
            </if>
            <if test="condition.balance != null">
                and d.balance = #{condition.balance}
            </if>
            <if test="condition.balanceCredit != null">
                and d.balance_credit = #{condition.balanceCredit}
            </if>
            <if test="condition.accountSid != null and condition.accountSid !=''">
                and d.account_sid = #{condition.accountSid}
            </if>
            <if test="condition.accountId != null and condition.accountId !=''">
                and d.account_sid = #{condition.accountId}
            </if>
            <if test="condition.accountName != null and condition.accountName !='' ">
                and d.account_name = #{condition.accountName}
            </if>
            <if test="condition.accountNames !=null and condition.accountNames.size > 0">
                and d.account_name in
                <foreach item="accountName" index="index" collection="condition.accountNames"
                         open="(" separator="," close=")">
                    #{accountName}
                </foreach>
            </if>
            <if test="condition.accountNames !=null and condition.accountNames.size == 0">
                and d.account_name in ('')
            </if>
            <if test="condition.accountNameLike != null">
                and d.account_name like concat('%', #{condition.accountNameLike}, '%')
            </if>

            <if test="condition.account != null">
                and d.created_by like concat('%', #{condition.account}, '%')
            </if>

            <if test="condition.orgSid != null">
                and d.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.consoleOrgSid != null">
                and d.org_sid in (select  org_sid from sys_m_org  where org_sid =#{condition.consoleOrgSid} OR tree_path LIKE concat( '/%', #{condition.consoleOrgSid}, '/%' ))
            </if>
            <if test="condition.orgSids != null and condition.orgSids.size > 0">
                and d.org_sid in
                <foreach item="orgSid" index="index" collection="condition.orgSids"
                         open="(" separator="," close=")">
                    #{orgSid}
                </foreach>
            </if>
            <if test="condition.accountIds != null and condition.accountIds.size() > 0 ">
                and d.account_sid in
                <foreach collection="condition.accountIds" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.userSid != null">
                and d.user_sid = #{condition.userSid}
            </if>
            <if test="condition.userList != null">
                and d.user_sid in
                <foreach collection="condition.userList" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="condition.orgSidIn != null and condition.orgSidIn.size == 0">
                and d.org_sid in ('')
            </if>
            <if test="condition.createdBy != null">
                and d.created_by = #{condition.createdBy}
            </if>
            <if test="condition.startTime != null">
                and d.created_dt &gt;= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and d.created_dt &lt;= #{condition.endTime}
            </if>
            <if test="condition.updatedBy != null">
                and d.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and d.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and d.version = #{condition.version}
            </if>
            <if test="condition.deductCashNo != null">
                and d.deduct_cash_no = #{condition.deductCashNo}
            </if>
            <if test="condition.userSid != null and condition.userSid != ''">
                and d.user_sid = #{condition.userSid}
            </if>
            <if test="condition.entityName != null and condition.entityName != ''">
                and d.entity_name like concat('%', #{condition.entityName}, '%')
            </if>
            <if test="condition.customizationInfoKey != null and condition.customizationInfoKey.size != 0
                and condition.customizationInfoValue != null and condition.customizationInfoValue.size != 0">
                <foreach item="item" collection="condition.customizationInfoKey" index="idx" open=" " separator=" " close=" ">
                    and CASE WHEN JSON_VALID(o.customization_info) THEN JSON_EXTRACT(o.customization_info, CONCAT('',
                    REPLACE(
                    SUBSTRING_INDEX(
                    JSON_SEARCH(o.customization_info, 'one', #{item}, NULL, '$[*].attrKey'),'.',1),'"',''), '.attrValue'))
                    LIKE CONCAT('%', '${condition.customizationInfoValue[idx]}', '%')
                    ELSE NULL END
                </foreach>
            </if>
        </where>
    </sql>
    <sql id="Base_Column_List">
    d.deal_sid, d.flow_no, d.type, d.trade_type, d.trade_channel, d.trade_no, d.order_no, d.bill_no,
    d.remark, d.billing_cycle, d.amount, d.balance, d.balance_credit, d.account_sid, d.account_name,
    d.org_sid, d.user_sid, d.created_by, d.created_dt, d.updated_by, d.updated_dt, d.version,d.balance_cash,
    d.cash_amount,d.deduct_balance_cash, d.deduct_cash_no,d.entity_name,d.entity_id,d.charging_type,d.coupon_amount
    </sql>


    <select id="selectByParams" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal">
        select
        <include refid="Base_Column_List"/>
        from biz_account_deal d
        <include refid="Example_Where_Clause"/>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
    </select>

    <select id="getList" resultType="cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDealDTO">
        select
        d.deal_sid, d.flow_no, d.type, d.trade_type, d.trade_channel, d.trade_no, d.order_no, d.bill_no,
        d.remark, d.billing_cycle, d.amount, d.balance, d.balance_credit, d.account_sid, d.account_name,
        d.org_sid, d.user_sid, u.account, d.created_dt, d.updated_by, d.updated_dt, d.version,d.balance_cash,
        d.cash_amount,d.deduct_balance_cash, d.deduct_cash_no,d.entity_id,d.entity_name,d.charging_type,
        d.coupon_amount,o.customization_info
        from biz_account_deal d
        left join biz_billing_account acc on acc.id = d.account_sid
        left join sys_m_user u on acc.admin_sid = u.user_sid
        LEFT JOIN sys_m_org o ON d.org_sid = o.org_sid
            <include refid="Example_Where_Clause"/>
        <if test="condition.orderByClause != null">
            order by ${condition.orderByClause}
        </if>
        ,d.deal_sid DESC
    </select>
</mapper>
