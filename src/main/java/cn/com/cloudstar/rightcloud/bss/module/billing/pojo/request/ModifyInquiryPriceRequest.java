/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import java.math.BigDecimal;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.common.constraint.EnumValue;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/30 13:38
 */
@Data
@ApiModel("变更询价")
public class ModifyInquiryPriceRequest {

    @ApiModelProperty("资源Id")
    @NotBlank
    private String id;

    @EnumValue(strValues = {"ECS", "DRP", "ModelArts", "CCE", "EIP", "EBS", "SFS_TURBO","RDS","DCS"}, message = "该产品不存在")
    @ApiModelProperty("目标类型")
    private String targetType;

    @ApiModelProperty("目标大小")
    @NotNull
    @Min(0)
    //@Max(1000)
    private Integer targetSize;

    @ApiModelProperty("目标规格")
    private String targetSpec;

    @ApiModelProperty("有系统盘")
    private boolean hasSystemDisk;

    @ApiModelProperty("有数据磁盘")
    private boolean hasDataDisk;

    @ApiModelProperty("有eip")
    private boolean hasEip;

    /**
     * 代客下单管理员sid
     */
    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    /**
     * 用户sid
     */
    @ApiModelProperty("下单用户ID")
    private Long userSid;

    /**
     * 用户组织sid
     */
    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;
}
