/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @ClassName: AiModelCollector.java
 * @Description： AiModel话单数据实体类
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "hws_sdr_aimodel_data")
public class AiModelCollector extends Collector {
}
