/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.RenewRequest;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2021/07
 */
public interface RenewService {
    /**
     * 续费
     * @param request
     * @return
     */
    RestResult renew(RenewRequest request);
}
