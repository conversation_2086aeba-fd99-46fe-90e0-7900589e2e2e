/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 运营-套餐包规格表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-3-7
 */
@Data
public class BizBagSpec implements Serializable {

  private static final long serialVersionUID = 1L;
  /**
   * id
   */
  @TableId(value = "id")
  @NotNull(message = "规格id不能为空")
  private String id;
  /**
   * 套餐包ID
   */
  private String bagId;
  /**
   * 规格名称 ,中文，数值+固定值，如0.8折
   */
  private String specName;
  /**
   * 规格值,当前的规格值，如折扣包0.8，代表0.8折扣
   */
  @NotNull(message = "规格值不能为空!")
  @Max(value = 10000,message = "规格不能大于10000!")
  @Min(value = 0,message = "规格不能小于0!")
  @Digits(fraction = 2,integer = 5,message = "规格值最多两位小数!")
  private BigDecimal specValue;
  /**
   * 周期
   */
  @NotNull
  @Max(value = 60,message = "周期不能大于60!")
  @Min(value = 1,message = "周期小于1!")
  private Integer period;
  /**
   *价格
   */
  @NotNull(message = "价格不能为空")
  @Max(value = 1000000000L,message = "价格不能超过10亿!")
  @Min(value = 0,message = "价格不能小于0!")
  private Double price;
  /**
   *所有者ID
   */
  private String ownerId;
  /**
   *组织ID
   */
  private Long orgSid;
  /**
   *版本号   默认为 1
   */
  private Long version;
  /**
   *创建人
   */
  private String createdBy;
  /**
   *创建时间
   */
  private Date createdDt;
  /**
   *更新人
   */
  private String updatedBy;
  /**
   *更新时间
   */
  private Date updatedDt;

  /**
   * 资源池id
   */
  @TableField(exist = false)
  private String clusterId;


}
