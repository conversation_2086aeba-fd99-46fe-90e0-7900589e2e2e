package cn.com.cloudstar.rightcloud.bss.module.provider.pojo.request;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 申请注销供应商请求
 * <AUTHOR>
 * @date 2025/1/22
 */
@Data
public class SysMProviderApplyCancelRequest {

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空")
    private Long id;

    /**
     * 短信验证码
     */
    @Length(max = 6, min = 6, message = "请输入六位验证码")
    private String smscode;
}
