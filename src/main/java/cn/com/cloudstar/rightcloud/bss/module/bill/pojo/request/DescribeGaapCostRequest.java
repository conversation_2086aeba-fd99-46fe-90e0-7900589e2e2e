/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.common.constraint.EnumValue;
import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @since 2019-10-24
 */
@Data
@ApiModel(description = "成本明细 请求参数")
public class DescribeGaapCostRequest extends BaseRequest {

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @Pattern(regexp = "^\\d{4}-\\d{1,2}-\\d{1,2} \\d{2}:\\d{2}:\\d{2}", message = "时间格式错误!")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    @Pattern(regexp = "^\\d{4}-\\d{1,2}-\\d{1,2} \\d{2}:\\d{2}:\\d{2}", message = "时间格式错误!")
    private String endTime;

    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境id")
    private Long envId;

    @ApiModelProperty(value = "云环境名称模糊查询")
    private String cloudEnvName;

    @ApiModelProperty(value = "云账号id")
    private Long accountId;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private Long orgSid;

    /**
     * 云环境类型
     */
    @ApiModelProperty(value = "云环境类型")
    private String cloudEnvType;

    /**
     * 实例名
     */
    @ApiModelProperty(name = "instanceName", value = "实例名")
    private String instanceName;

    /**
     * 实例id
     */
    @ApiModelProperty(name = "instanceId", value = "实例id")
    private String instanceId;

    /**
     * 产品代码
     */
    @ApiModelProperty(name = "productCode", value = "产品代码")
    private String productCode;

    /**
     * 付费类型
     */
    @ApiModelProperty(name = "subscriptionType", value = "付费类型")
    private String subscriptionType;

    /**
     * 项目名
     */
    @ApiModelProperty(name = "projectNameLike", value = "项目名")
    private String projectNameLike;

    /**
     * 查询类型
     */
    @ApiModelProperty(name = "queryType", value = "查询类型")
    private String queryType;

    /**
     * 订单号模糊匹配
     */
    @ApiModelProperty(name = "orderNoLike", value = "订单号模糊匹配")
    private String orderNoLike;

    /**
     * 订单id
     */
    @ApiModelProperty("订单编号查询")
    private String orderId;

    /**
     * 账单号模糊匹配
     */
    @ApiModelProperty(name = "billNoLike", value = "账单号模糊匹配")
    private String billNoLike;

    /**
     * 查询类型
     */
    @ApiModelProperty(name = "priceType", value = "查询类型")
    private String priceType;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态 0 未分摊， 1已分摊")
    private String status;

    /**
     * 发票状态
     */
    @ApiModelProperty(name = "invoiceStatus", value = "开票")
    private String invoiceStatus;

    /**
     * 是否校验开票金额
     */
    @ApiModelProperty(name = "invoiceAmountFlag", value = "是否校验开票金额")
    private String invoiceAmountFlag;

    /**
     * 账单周期ID
     */
    @ApiModelProperty(name = "billBillingCycleId", value = "账单周期ID")
    private String billBillingCycleId;

    /**
     * 账期
     */
    @ApiModelProperty(name = "billingCycle", value = "账期")
    private String billingCycle;

    /**
     * 客户名称
     */
    @ApiModelProperty(name = "customerName", value = "客户名称")
    private String customerName;

    /**
     * 客户编码
     */
    @ApiModelProperty(name = "customerCode", value = "客户编码")
    private List<String> customerCode;

    /**
     * 分销商名称
     */
    @ApiModelProperty(name = "distributorName", value = "分销商名称")
    private String distributorName;

    /**
     * 分组标识
     */
    @ApiModelProperty(name = "groupByFlag", value = "分组标识：cycle按周期分组/customer按客户分组")
    @EnumValue(strValues = {"cycle","customer"}, message = "分组标识不合法！")
    private String groupByFlag;

    /**
     * 导出方式
     */
    @ApiModelProperty(name = "exportType", value = "导出方式：current 当前所选/all 所有内容（指定月份内）")
    @EnumValue(strValues = {"current","month","currentsearch"}, message = "导出类型不合法！")
    private String exportType;

    /**
     * 分页标识
     */
    @ApiModelProperty(name = "pageFlag", value = "Y：分页")
    private String pageFlag;

    /**
     * 账单来源
     */
    @ApiModelProperty(name = "billSource", value = "账单来源")
    private String billSource;

    /**
     * 指定月份
     */
    @ApiModelProperty(value = "指定月份", required = true)
    private String specifiMonth;

    /**
     * 统计开始时间
     */
    @ApiModelProperty(value = "统计开始时间", required = true)
    private String usageStartDate;

    /**
     * 统计结束时间
     */
    @ApiModelProperty(value = "统计结束时间", required = true)
    private String usageEndDate;

    /**
     * 包本月uuid
     */
    private String bagInstUuid;


    private List<String> productCodes;

    /**
     * 帐户id列表
     */
    private Set<Long> accountIds = new HashSet<>();
    /**
     * 是否屏蔽HPC专属资源池租户和子用户提交作业的账单
     */
    private String hpcDrpCostFlag;
    /**
     * 用户名
     */
    private String userAccountName;

    private String moduleType;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 订单来源
     */
    private String orderSourceSn;
    private boolean orderSourceSnIsNull;

    /**
     * 统计账期，格式yyyy-MM
     */
    private String groupByPeriod;

    /**
     * 统计维度，
     */
    @EnumValue(strValues = {"order","productCode","resName"})
    private String groupByDimension;

    /**
     * 账单周期是否拆分
     */
    private Boolean splitByPeriod  = Boolean.FALSE;


    /**
     * 统计维度枚举类
     */
    public enum DimesionEnum{
        /**
         * 订单维度
         */
        ORDER("order","订单"),
        /**
         * 产品维度
         */
        PRODUCT_CODE("productCode","产品"),
        /**
         * 产品名称/ID维度
         */
        RESNAME("resName","名称/ID");

        /**
         * 统计维度代码
         */
        private String code;

        /**
         * 统计维度名称
         */
        private String name;

        private DimesionEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * @return Returns the name.
         */
        public String getName() {
            return name;
        }

        /**
         * @return Returns the code.
         */
        public String getCode() {
            return code;
        }
        /**
         * 通过枚举<code>code</code>获得枚举
         *
         * @param code
         * @return BooleanEnum
         */
        public static DimesionEnum getByCode(String code) {
            for (DimesionEnum anEnum : values()) {
                if (anEnum.getCode().equals(code)) {
                    return anEnum;
                }
            }
            return null;
        }

        /**
         * 获取全部枚举
         *
         * @return List<BooleanEnum>
         */
        public static List<DimesionEnum> getAllEnum() {
            List<DimesionEnum> list = new ArrayList<DimesionEnum>();
            for (DimesionEnum anEnum : values()) {
                list.add(anEnum);
            }
            return list;
        }

        /**
         * 获取全部枚举值
         *
         * @return List<String>
         */
        public static List<String> getAllEnumCode() {
            List<String> list = new ArrayList<String>();
            for (DimesionEnum anEnum : values()) {
                list.add(anEnum.getCode());
            }
            return list;
        }

    }


}
