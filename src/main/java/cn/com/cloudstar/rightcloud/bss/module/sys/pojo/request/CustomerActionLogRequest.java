/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "运营控制台操作用户的日志查询")
@Data
public class CustomerActionLogRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作者名
     */
    @ApiModelProperty("操作者名")
    private String opUserName;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 组织名字
     */
    @ApiModelProperty("组织名")
    private String orgName;

    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    private String opType;

    /**
     * 用户sid
     */
    @ApiModelProperty("用户id")
    private Long userSid;

    /**
     * 组织sid
     */
    @NotNull
    @ApiModelProperty("组织id")
    private Long orgSid;

}
