/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.CreateRdsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = "https://cmp-resource:38180", configuration = FeignConfig.class, path = "/api/v1/resource")
public interface ResRdsService {

    @PostMapping("/rds")
    RestResult createRdsDBInstance(CreateRdsRequest request);
}
