/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sys.mapper;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;

/**
 * <p>
 * 组织架构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@Repository
public interface OrgMapper extends BaseMapper<Org> {

    /**
     * 查询当前组织以及下级所有用户组织(包括客户的项目)
     * @param orgSid
     * @return
     */
    List<Long> selectCustomerOrgSids(Long orgSid);

    /**
     * 查询当前组织的所有下级(包括客户和分销商)
     * @param orgSid
     * @return
     */
    List<Long> selectSubOrgSids(Long orgSid);

    /**
     * 查询当前组织的下级所有用户Account
     * @param orgSid
     * @return
     */
    List<Long> selectCustomerAccountIds(@Param("orgSid") Long orgSid,@Param("entityId") Long entityId);

    List<Org> getOrgInfo(Map param);

    int toCheckGroupCount(@Param("groupIds") List<Long> groupIds, @Param("rootOrg") Long rootOrg);

    List<Org> selectOrgSidBySid(Long companyId, String orgCode);

    List<Org> selectOrgByUserSidAndType(Long orgSid, String type);

    Org selectDistributorBy(String orgName);

    String selectContactName(Long orgSid);

    boolean updateSolutionById(Org org);


    List<Long> getByParentId(@Param("parentIds") List<Long> parentIds);
    List<Long> selectAllSidsByOrgSid(Long orgSid);
    List<String> selectAllSidsByOrgSidString(String orgSid);

    int selectOrg(Criteria criteria);

    /**
     * 根据orgSid查询ldapOu
     * @param orgSid orgSid
     * @return ldapOu
     */
    String selectLdapOuByOrgSid(Long orgSid);

    User selectUserByOrgSid(Long orgSid);

    /**
     * 查询所有组织
     *
     * @return
     */
    List<Org> selectAllOrg();

    /**
     * 查询所有组织简单信息 org_sid,org_name,org_type,parent_id
     * @return
     */
    List<Org> selectAllOrgSimple();

    List<Long> selectAllSidsByOrgSidV(Long orgSid);

    List<Org> selectOrgsByCustomize(Criteria customizationInfo);
}
