/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request;

import java.math.BigDecimal;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class CreateBizBagSpecRequest  {
    /**
     *套餐包ID
     */
    @NotNull(message = "bagId不能为空")
    private String bagId;
    /**
     *规格值,当前的规格值，如折扣包0.8，代表0.8折扣
     */
    @NotNull(message = "规格值不能为空!")
    @Max(value = 10000,message = "规格不能大于10000!")
    @Min(value = 0,message = "规格不能小于0!")
    @Digits(fraction = 2,integer = 5,message = "规格值最多两位小数!")
    private BigDecimal specValue;
    /**
     *周期
     */
    @NotNull(message = "周期不能为空!")
    @Pattern(regexp = "^[1-9]\\d*$",message = "请填写正确周期数!")
    @Max(value = 60,message = "周期不能大于60!")
    @Min(value = 1,message = "周期小于1!")
    private String period;
    /**
     *价格
     */
    @NotNull(message = "价格不能为空")
    @Max(value = 1000000000L,message = "价格不能超过10亿!")
    @Min(value = 0,message = "价格不能小于0!")
    private Double price;
}
