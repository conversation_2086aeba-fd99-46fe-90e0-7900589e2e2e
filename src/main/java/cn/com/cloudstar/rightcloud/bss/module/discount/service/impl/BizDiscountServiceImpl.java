/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.discount.service.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingStrategyType;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.PolicyStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.CloudEnvEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ScopeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ComputeUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.enums.BizBillingAccountStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccountConfig;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DiscountDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyAccountConfigService;
import cn.com.cloudstar.rightcloud.bss.module.discount.mapper.BizDiscountMapper;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscountPolicy;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 折扣表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@Service
@Slf4j
public class BizDiscountServiceImpl extends ServiceImpl<BizDiscountMapper, BizDiscount> implements IBizDiscountService {

    @Autowired
    private IBizDiscountPolicyService bizDiscountPolicyService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @DubboReference
    private CloudEnvAccountRemoteService accountService;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private BizBillingStrategyAccountConfigService strategyAccountConfigService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    private static final String MARKET = "market";

    @Override
    public IPage<BizDiscount> selectByParams(IPage<BizBillingAccount> page, Criteria criteria) {
        return this.baseMapper.selectByParams(page, criteria);
    }

    /**
     * @param bizDiscount
     * @return
     * @description: 校验用户
     * @date 2023/2/9 15:34
     */
    @Override
    public void validateUser(BizDiscount bizDiscount) {
        BizBillingAccount account = bizBillingAccountService.getById(bizDiscount.getUserSid());
        User user = sysUserService.selectByPrimaryKey(account.getAdminSid());
        // 判断用户是否被拒绝
        if (BizBillingAccountStatusEnum.rejected.getStatus().equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1232434569));
        }
    }
    @Override
    public void validateParams(BizDiscount bizDiscount) {

        String discountName = bizDiscount.getDiscountName();
        if(StringUtils.isNotEmpty(discountName) && discountName.length()>128){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_672172893));
        }


        // 获取平台折扣或客户折扣列表
        QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizDiscount::getDiscountType, bizDiscount.getDiscountType())
                .eq(BizDiscount::getEntityId,bizDiscount.getEntityId()).eq(BizDiscount::getStatus,1);
        if (DiscountPolicyEnum.CUSTOMER.getCode().equals(bizDiscount.getDiscountType())) {
            AssertUtil.requireNonBlank(bizDiscount.getUserSid(), "客户不能为空！");
            queryWrapper.lambda().eq(BizDiscount::getUserSid, bizDiscount.getUserSid());
        }else if (MARKET.equals(bizDiscount.getDiscountType())) {
            queryWrapper.lambda().eq(BizDiscount::getCreatedBy, AuthUtil.getAuthUser().getAccount());
        }
        List<BizDiscount> discounts = this.list(queryWrapper);

        if (bizDiscount.getDiscountSid() != null) {
            discounts = discounts.stream().filter(discount -> !Objects.equals(bizDiscount.getDiscountSid(), discount.getDiscountSid()))
                .collect(Collectors.toList());
        }
        discounts = filterByTime(discounts, bizDiscount.getStartTime(), bizDiscount.getEndTime());
        // 是否已存在适用环境和产品

        Set<String> compares = Sets.newHashSet();
        String compareEnvScope = bizDiscount.getCloudEnvScope();
        String compareProductScope = bizDiscount.getProductScope();
        String[] compareEnvScopes = StrUtil.splitToArray(compareEnvScope, StrUtil.COMMA);
        String[] compareProductScopes = StrUtil.splitToArray(compareProductScope, StrUtil.COMMA);
        List<String> comEnvScopsList = Arrays.asList(compareEnvScopes);
        List<String> comProductScopeList = Arrays.asList(compareProductScopes);

        Set<String> exists = Sets.newHashSet();
        discounts.forEach(discount -> {
            String envScope = discount.getCloudEnvScope();
            String productScope = discount.getProductScope();
            String[] envScopes = StrUtil.splitToArray(envScope, StrUtil.COMMA);
            String[] productScopes = StrUtil.splitToArray(productScope, StrUtil.COMMA);

            exists.addAll(ComputeUtil.assembleList(envScopes, productScopes));

            List<String> envScopList = Arrays.asList(envScopes);
            List<String> productScopeList = Arrays.asList(productScopes);
            if(comProductScopeList.contains("all") || productScopeList.contains("all")){
                if (CollectionUtil.containsAny(envScopList,comEnvScopsList)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1381461091));
                }
            }


        });

        compares.addAll(ComputeUtil.assembleList(compareEnvScopes, compareProductScopes));
        if (CollectionUtil.containsAny(exists,compares)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1381461091));
        }
    }

    @Override
    public List<DiscountDetailVO> getPortalDiscount(List<String> products, Long envId, BigDecimal period, Integer amount,
                                                    Long accountId, BigDecimal originPrice, Date pointInTime) {
        return getPortalDiscount(products, envId, period, amount, accountId, originPrice, null, pointInTime);
    }

    /**
     * 计算折扣价
     *
     * @param products    产品类型，目前都是单个产品类型，所以不兼容多个产品类型一起询价
     * @param envId
     * @param period
     * @param amount
     * @param accountId
     * @param originPrice
     * @param policyType  使用折扣类型列表，申请时policyType没有值
     * @return
     */
    @Override
    public List<DiscountDetailVO> getPortalDiscount(List<String> products, Long envId, BigDecimal period, Integer amount,
                                                    Long accountId, BigDecimal originPrice, String policyType, Date pointInTime) {

        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(envId);
        if (cloudEnv == null) {
            ResCloudEnv resCloudEnv = cloudEnvService.selectResCloudEnvById(envId);
            if (resCloudEnv != null && resCloudEnv.getCloudEnvId() != null) {
                cloudEnv = cloudEnvService.selectByPrimaryKey(resCloudEnv.getCloudEnvId());
            }
        }

        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1810809622));
        }

        CloudEnvAccount envAccount = accountService.selectByPrimaryKey(cloudEnv.getCloudEnvAccountId());
        if (Objects.isNull(envAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_275964975));
        }
        if (CollectionUtil.isEmpty(products)) {
            // 无适用产品折扣
            return Collections.emptyList();
        }
        List<DiscountDetailVO> result = Lists.newArrayList();

        for (String product : products) {
        BizBillingAccount billingAccount = bizBillingAccountService.getById(accountId);
            /*
             * req312任务，把折扣策略分为客户策略和默认策略，默认执行客户策略
             * 1.判断客户策略是否启动,默认使用客户策略（客户策略和默认策略是同一条数据，客户策略和默认策略是互斥的）
             * 2.先查询该客户询价的产品是否有策略启动的,如果没有使用默认策略，如果有使用该策略
             * 如果折扣策略有数据，而具体的折扣没有数据则不管；如果折扣策略么有数据，而具体的折扣有数据则具体折扣会有效
             */
            // 1. 判断客户策略是否启动,默认使用客户策略（客户策略和默认策略是同一条数据）
            QueryWrapper<BizDiscountPolicy> policyQueryWrapper = new QueryWrapper<>();
            policyQueryWrapper.eq("category", PolicyStatus.PLATFORM);
            policyQueryWrapper.eq("entity_id", billingAccount.getEntityId());
            BizDiscountPolicy defaultPolicy = bizDiscountPolicyService.getOne(policyQueryWrapper);
            if (Objects.isNull(defaultPolicy)) {
                // 增加默认折扣策略
                defaultPolicy = new BizDiscountPolicy();
                defaultPolicy.setCategory(PolicyStatus.PLATFORM);
                defaultPolicy.setPolicyType(PolicyStatus.SHARE);
                defaultPolicy.setPolicyLevel("customer,platform");
                defaultPolicy.setStatus(1);
                defaultPolicy.setVersion(1L);
                defaultPolicy.setEntityId(billingAccount.getEntityId());
                WebUserUtil.prepareInsertParams(defaultPolicy);
                bizDiscountPolicyService.save(defaultPolicy);
            }


            // 最终使用的折扣策略
            BizDiscountPolicy policy = defaultPolicy;
            if (defaultPolicy.getStatus().equals(1)) {
                // 2.先查询该客户询价的产品是否有策略启动的,如果没有直接返回，如果有使用该策略
                policyQueryWrapper.clear();
                policyQueryWrapper.eq("entity_id", billingAccount.getEntityId());
                policyQueryWrapper.eq("status", 1);
                policyQueryWrapper.eq("user_account_id", accountId);
                policyQueryWrapper.like("cloud_env_scope", convertCloudEnvType(cloudEnv.getCloudEnvType()));
                policyQueryWrapper.like("product_scope", product);
                if (ProductComponentEnum.COMPUTE.getKey().contains(product.toLowerCase())) {
                    policyQueryWrapper.like("product_scope", product)
                            .or().like("product_scope", "ecs")
                            .or().like("product_scope", "compute");
                }
                BizDiscountPolicy bizDiscountPolicy = bizDiscountPolicyService.getOne(policyQueryWrapper);
                if(bizDiscountPolicy != null){
                    policy = bizDiscountPolicy;
                }

                // 没有客户策略还是会走平台策略  这里还需要判断like查询，如果product为DRP，会将HPC-DRP的折扣查询出来。
                if (Objects.isNull(bizDiscountPolicy) || !(Arrays.asList(bizDiscountPolicy.getProductScope().split(",")).stream().anyMatch(t -> t.equals(product)))) {
                    policy = defaultPolicy;
                }
                if (PolicyStatus.NO.equals(policy.getPolicyType()) ){
                    break;
        }
            }


            // 客户策略未启动/折扣类型不等于NO 走之前的逻辑
            if (Strings.isNotBlank(policyType)) {
                policy.setPolicyType(policyType);
            }
                BigDecimal ratio = BigDecimal.ONE;
                // 设置折扣详情
                DiscountDetailVO detailVO = new DiscountDetailVO();
                detailVO.setProductCode(product);
                detailVO.setProductName(ProductComponentEnum.keyFromDesc(product));
                detailVO.setOriginPrice(originPrice);
                detailVO.setPolicyType(policy.getPolicyType());
                detailVO.setDiscount(BigDecimal.ONE);
                //查询当前适用环境和适用产品且在有效范围内的折扣
                QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();

            CloudEnv finalCloudEnv = cloudEnv;
            queryWrapper.lambda().eq(BizDiscount::getStatus, 1).and(wrapper->
                    wrapper.like(BizDiscount::getCloudEnvScope, convertCloudEnvType(finalCloudEnv.getCloudEnvType()))
                    .or().eq(BizDiscount::getCloudEnvScope, CloudEnvEnum.ALL.getKey()))
                    .eq(BizDiscount::getEntityId,billingAccount.getEntityId())
                    .ge(BizDiscount::getEndTime, Objects.nonNull(pointInTime) ? pointInTime : LocalDate.now())
                    .le(BizDiscount::getStartTime, Objects.nonNull(pointInTime) ? pointInTime : LocalDate.now());

                if (ProductComponentEnum.COMPUTE.getKey().contains(product.toLowerCase())) {
                    queryWrapper.lambda().and(wrapper ->
                            wrapper.like(BizDiscount::getProductScope, product)
                                    .or().like(BizDiscount::getProductScope, "ecs")
                                    .or().like(BizDiscount::getProductScope, "compute")
                                    .or()
                                    .eq(BizDiscount::getProductScope,
                                            ProductComponentEnum.ALL.getKey().get(0)));
                } else {
                    queryWrapper.lambda().and(wrapper ->
                                                      wrapper.like(BizDiscount::getProductScope, product).or()
                                                             .eq(BizDiscount::getProductScope,
                                                                 ProductComponentEnum.ALL.getKey().get(0)));
                }
                List<BizDiscount> discounts = this.list(queryWrapper);
            // like查询，如果product为DRP，会将HPC-DRP的折扣查询出来。
            discounts = discounts.stream().filter(bizDiscount -> {
                String productScope = bizDiscount.getProductScope();
                return Arrays.asList(productScope.split(",")).stream().anyMatch(t -> t.equals(product));
            }).collect(Collectors.toList());
                if (DiscountPolicyEnum.SHARE.getCode().equals(policy.getPolicyType())) {
                    computeDiscountOrderByLevel(period, amount, accountId, envAccount, policy,
                        ratio, detailVO, discounts);

                } else if (DiscountPolicyEnum.CUSTOMER.getCode().equals(policy.getPolicyType())) {
                    //客户折扣独享
                    computeCustomerDiscount(ratio, discounts, detailVO, period, amount, accountId);
                } else if (DiscountPolicyEnum.PLATFORM.getCode().equals(policy.getPolicyType())) {
                    // 平台折扣独享
                    computePlatformDiscount(ratio, discounts, detailVO, period, amount);
                } else if (DiscountPolicyEnum.ENV.getCode().equals(policy.getPolicyType())) {
                    // 环境折扣独享
                    computeEnvDiscount(ratio, envAccount.getId(), detailVO);
                }
                result.add(detailVO);
            }
        return result;
    }

    /**
     * 共享折扣类型 计算折扣价
     * @param period
     * @param amount
     * @param accountId 账户id
     * @param envAccount 环境信息
     * @param policy 折扣策略
     * @param ratio 折扣率
     * @param detailVO 详细信息
     * @param discounts 具体折扣信息
     */
    private void computeDiscountOrderByLevel(BigDecimal period, Integer amount, Long accountId,
                                             CloudEnvAccount envAccount, BizDiscountPolicy policy, BigDecimal ratio,
                                             DiscountDetailVO detailVO, List<BizDiscount> discounts) {
        String policyLevel = policy.getPolicyLevel();
        if (Objects.nonNull(policyLevel)) {
            String[] levels = policyLevel.split(StrUtil.COMMA);
            List<Function<BigDecimal, BigDecimal>> toComputeDiscounts = Lists.newArrayList();
            for (String level : levels) {
                if (DiscountPolicyEnum.CUSTOMER.getCode().equals(level)) {
                    Function<BigDecimal, BigDecimal> customer = (r) -> computeCustomerDiscount(r,
                                                                                               discounts, detailVO,
                                                                                               period, amount,
                                                                                               accountId);
                    toComputeDiscounts.add(customer);
                }
                if (DiscountPolicyEnum.PLATFORM.getCode().equals(level)) {
                    Function<BigDecimal, BigDecimal> platform = (r) -> computePlatformDiscount(r,
                                                                                               discounts, detailVO,
                                                                                               period, amount);
                    toComputeDiscounts.add(platform);
                }
                if (DiscountPolicyEnum.ENV.getCode().equals(level)) {
                    Function<BigDecimal, BigDecimal> env = (r) -> computeEnvDiscount(r,
                                                                                     envAccount.getId(), detailVO);
                    toComputeDiscounts.add(env);
                }
            }
            Iterator<Function<BigDecimal, BigDecimal>> iterator = toComputeDiscounts.iterator();
            while (iterator.hasNext()) {
                ratio = iterator.next().apply(ratio);
                iterator.remove();
            }
        } else {

            List<Function<BigDecimal, BigDecimal>> toComputeDiscounts = Lists.newArrayList();
            //计算客户折扣
            Function<BigDecimal, BigDecimal> customer = (r) -> computeCustomerDiscount(r,
                                                                                       discounts, detailVO,
                                                                                       period, amount,
                                                                                       accountId);
            toComputeDiscounts.add(customer);
            //计算平台折扣
            Function<BigDecimal, BigDecimal> platform = (r) -> computePlatformDiscount(r,
                                                                                       discounts, detailVO,
                                                                                       period, amount);
            toComputeDiscounts.add(platform);

            Iterator<Function<BigDecimal, BigDecimal>> iterator = toComputeDiscounts.iterator();
            while (iterator.hasNext()) {
                ratio = iterator.next().apply(ratio);
                iterator.remove();
            }
        }
    }

    /**
     * 优惠计算客户折扣
     * @param ratio
     * @param discounts
     * @param detailVO
     * @return
     */
    private BigDecimal computeCustomerDiscount(BigDecimal ratio, List<BizDiscount> discounts, DiscountDetailVO detailVO,
        BigDecimal period, Integer amount, Long billingAccountId) {
        if (CollectionUtil.isEmpty(discounts)) {
            return ratio;
        }
        Long accountId = billingAccountId;
        if (Objects.nonNull(accountId)) {
            List<BizDiscount> userDiscounts = discounts.stream()
                                                       .filter(discount -> Objects.equals(discount.getUserSid(), accountId)
                                                               && DiscountPolicyEnum.CUSTOMER.getCode().equals(discount.getDiscountType()))
                                                       .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(userDiscounts)) {
                return ratio;
            }

            for (BizDiscount discount : userDiscounts) {
                if (checkDiscountScope(period, amount, discount, detailVO)) {
                    ratio = NumberUtil.mul(ratio, discount.getDiscountRatio());
                    detailVO.getDiscountMap().put(String.valueOf(discount.getDiscountSid()), DiscountPolicyEnum.CUSTOMER.getCode());
                    detailVO.setDiscount(ratio);
                }
            }
        }
        return ratio;
    }

    /**
     * 优惠计算平台折扣
     * @param ratio
     * @param discounts
     * @param detailVO
     * @return
     */
    private BigDecimal computePlatformDiscount(BigDecimal ratio, List<BizDiscount> discounts, DiscountDetailVO detailVO,
        BigDecimal period, Integer amount) {
        if (CollectionUtil.isEmpty(discounts)) {
            return ratio;
        }
        List<BizDiscount> platformDiscounts = discounts.stream()
                                                       .filter(discount -> DiscountPolicyEnum.PLATFORM.getCode().equals(discount.getDiscountType()))
                                                       .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(platformDiscounts)) {
            return ratio;
        }
        for (BizDiscount discount : platformDiscounts) {
            if (checkDiscountScope(period, amount, discount, detailVO)) {
                //在应用范围
                ratio = NumberUtil.mul(ratio, discount.getDiscountRatio());
                detailVO.getDiscountMap().put(String.valueOf(discount.getDiscountSid()), DiscountPolicyEnum.PLATFORM.getCode());
                detailVO.setDiscount(ratio);
            }
        }

        return ratio;
    }

    /**
     * 优惠计算环境折扣
     * @param ratio
     * @param envAccountId
     * @param detailVO
     * @return
     */
    private BigDecimal computeEnvDiscount(BigDecimal ratio, Long envAccountId, DiscountDetailVO detailVO) {
        QueryWrapper<BizBillingStrategyAccountConfig> accountConfigQuery = new QueryWrapper<>();
        accountConfigQuery.lambda().eq(BizBillingStrategyAccountConfig::getAccountId, envAccountId);
        List<BizBillingStrategyAccountConfig> accountConfigs = strategyAccountConfigService.list(accountConfigQuery);
        if (CollectionUtil.isNotEmpty(accountConfigs)) {
            for (BizBillingStrategyAccountConfig accountConfig : accountConfigs) {
                // 除不计费以外
                if (!BillingStrategyType.CHARGED_BY_NONE.equals(accountConfig.getBillingStrategy()) && accountConfig.getDiscount() != null) {
                    ratio = NumberUtil.mul(ratio, accountConfig.getDiscount());
                    detailVO.getDiscountMap().put(String.valueOf(accountConfig.getId()), DiscountPolicyEnum.ENV.getCode());
                    detailVO.setDiscount(ratio);
                    detailVO.setFloatingRatio(accountConfig.getFloatingRatio());
                }
            }
        }
        return ratio;
    }

    /**
     * 检查购买产品参数是否在折扣应用范围内
     * @param period
     * @param amount
     * @param discount
     * @param detailVO
     * @return
     */
    private boolean checkDiscountScope(BigDecimal period, Integer amount, BizDiscount discount, DiscountDetailVO detailVO) {
        // 范围格式 50-100
        String[] scopeArr = StrUtil.splitToArray(discount.getScopeValue(), StrUtil.DASHED);
        if (scopeArr.length == 0) {

            return false;
        }
        ScopeTypeEnum scopeTypeEnum = ScopeTypeEnum.getEnum(discount.getScopeType());
        if (scopeTypeEnum == null) {

            return true;
        }
        if(amount == null){
            amount = 1;
        }
        switch (scopeTypeEnum) {
            case MONEY:
                return checkMoneyScope(detailVO, scopeArr);
            case TIME:
                return checkTimeScope(period, scopeArr);
            case QUANTITY:
                return checkQuantityScope(amount, scopeArr);
            case UNLIMITED:
                return true;
            default:
                return false;
        }
    }

    /**
     * 检查金额范围
     * @param detailVO
     * @param scope
     * @return
     */
    private boolean checkMoneyScope(DiscountDetailVO detailVO, String... scope) {
        // 金额范围
        BigDecimal originPrice = detailVO.getOriginPrice();
        BigDecimal discount = detailVO.getDiscount();
        BigDecimal currentPrice = NumberUtil.mul(originPrice, discount);
        if (StrUtil.isNotBlank(scope[1])) {
            return currentPrice.compareTo(new BigDecimal(scope[0])) >= 0
                && currentPrice.compareTo(new BigDecimal(scope[1])) <= 0;
        } else {
            return currentPrice.compareTo(new BigDecimal(scope[0])) >= 0;
        }
    }

    /**
     * 检查数量范围
     * @param amount
     * @param scope
     * @return
     */
    private boolean checkQuantityScope(Integer amount, String... scope) {
        // 数量范围,代客订购的时候询价，amount为null，导致报错，如果为null默认为1，不为null用amount本身值
        amount = amount == null ? 1:amount;
        if (StrUtil.isNotBlank(scope[1])) {
            return amount >= Convert.toInt(scope[0], 1)
                && amount <= Convert.toInt(scope[1]);
        } else {
            return amount >= Convert.toInt(scope[0], 1);
        }
    }

    /**
     * 检查时间范围
     * @param period
     * @param scope
     * @return
     */
    private boolean checkTimeScope(BigDecimal period, String... scope) {
        if (StrUtil.isNotBlank(scope[1])) {
            return period.compareTo(Convert.toBigDecimal(scope[0], BigDecimal.ONE)) > -1
                && period.compareTo(Convert.toBigDecimal(scope[1])) < 1;
        } else {
            return period.compareTo(Convert.toBigDecimal(scope[0], BigDecimal.ONE)) > -1;
        }
    }

    private String convertCloudEnvType(String type) {
        String admin = "Admin";
        if (StrUtil.indexOfIgnoreCase(type, admin) != -1) {
            return type.split(StrUtil.DASHED)[0];
        }
        return type;
    }

    /**
     * 过滤相同时间范围折扣
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    private List<BizDiscount> filterByTime(List<BizDiscount> list, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return list;
        }
        list = list.stream().filter(con -> {
            if (Objects.isNull(con.getStartTime()) || Objects.isNull(con.getEndTime())) {
                return false;
            }
            if (startTime.after(con.getEndTime()) || endTime.before(con.getStartTime())) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        return list;
    }
}
