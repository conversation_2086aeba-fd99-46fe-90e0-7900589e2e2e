package cn.com.cloudstar.rightcloud.bss.module.bag.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.PageDTO;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagSpecService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.BL.BL03;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 套餐包规格管理
 *
 * <AUTHOR>
 * @date 2023/11/09
 */
@RestController
@RequestMapping("/bag_spec")
@Api("套餐包规格管理")
public class BizBagSpecController {

    public static final int MONTH_OF_YEAR = 12;

    @Autowired
    private BizBagSpecService bizBagSpecService;
    @Autowired
    private BizBagService bizBagService;
    @Autowired
    private BizBagMapper bizBagMapper;

    /**
     * 【Since v2.5.0】新增套餐包规格表
     * [INNER API] 新增套餐包规格表
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL030601)
    @ApiOperation("新增套餐包规格表")
    @PostMapping
    public RestResult insertBizSpec(@Valid @RequestBody CreateBizBagSpecRequest request) {
        BizBag bag = bizBagService.getOne(new LambdaQueryWrapper<BizBag>().in(BizBag::getBagId, request.getBagId()));
        if (Objects.isNull(bag)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if ("online".equals(bag.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1398519262));
        }

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //周期限制1-11月，并只能是12的倍数
        String period = request.getPeriod();
        Integer periodInt = Integer.valueOf(period);
        if (periodInt > MONTH_OF_YEAR && periodInt % MONTH_OF_YEAR > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BigDecimal specValue = request.getSpecValue();
        if (StatusType.DISCOUNT.equals(bag.getType())) {
            if (checkDiscountSpecValue(specValue)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        } else {
            if (specValue.compareTo(BigDecimal.valueOf(1)) < 0  || BigDecimal.valueOf(1000).compareTo(specValue) < 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }

        int flag = bizBagSpecService.createBizBagSpec(request);
        if (flag == 1) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2062749797));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2062694378));
        }

    }

    private boolean checkDiscountSpecValue(BigDecimal specValue) {
        return specValue.compareTo(BigDecimal.valueOf(0.01)) < 0 || BigDecimal.valueOf(1).compareTo(specValue) < 0 || !checkTwoDecimalPlaces(specValue);
    }

    /**
     * 判断给定的BigDecimal是否为两位小数。
     *
     * @param number 要检查的BigDecimal对象
     * @return 如果是两位小数则返回true，否则返回false
     */
    private boolean checkTwoDecimalPlaces(BigDecimal number) {
        // 使用setScale方法设置小数点后两位，RoundingMode.UNNECESSARY表示如果操作会导致舍入，则抛出异常
        try {
            number = number.setScale(2, RoundingMode.UNNECESSARY);
            // 如果设置小数点后两位没有导致舍入（即原数就是两位小数或整数），则返回true
            return true;
        } catch (ArithmeticException e) {
            // 如果发生异常，说明原数的小数位超过两位，需要舍入，因此返回false
            return false;
        }
    }

    /**
     * 【Since v2.5.0】修改套餐包规格
     * [INNER API] 修改套餐包规格
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation("")
    @PutMapping
    @AuthorizeBss(action = AuthModule.BL.BL03.BL030601)
    public RestResult updateBizSpecById(@Valid @RequestBody BizBagSpec request) {
        BizBagSpec bagSpec = bizBagSpecService.getById(request.getId());
        if (Objects.isNull(bagSpec)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizBag bag = bizBagService.getOne(new LambdaQueryWrapper<BizBag>().in(BizBag::getBagId, bagSpec.getBagId()));
        if (Objects.isNull(bag)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (StatusType.DISCOUNT.equals(bag.getType())) {
            if (checkDiscountSpecValue(request.getSpecValue())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        } else {
            if (request.getSpecValue().compareTo(BigDecimal.valueOf(1)) < 0  || BigDecimal.valueOf(1000).compareTo(request.getSpecValue()) < 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        request.setBagId(bag.getBagId());
        boolean flag = bizBagSpecService.updateBizSpecById(request);
        if (flag) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_177439832));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_177495251));
        }
    }

    /**
     * 【Since v2.5.0】删除套餐包规格
     * [INNER API] 删除套餐包规格
     *
     * @param ids 套餐包ID
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL030602)
    @ApiOperation("删除套餐包规格")
    @DeleteMapping
    public RestResult deleteBizSpecById(@RequestBody List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            List<String> status = bizBagMapper.selectBizBagStatus(ids);
            if (!CollectionUtils.isEmpty(status) && status.contains("online")) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_683292687));
            }
            List<BizBagSpec> bizBagSpecs = bizBagSpecService.listByIds(ids);
            if (CollectionUtils.isEmpty(bizBagSpecs)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            Set<String> bagIds = bizBagSpecs.stream().map(e -> e.getBagId()).collect(Collectors.toSet());
            List<BizBag> bizBags = bizBagService.list(new LambdaQueryWrapper<BizBag>().in(BizBag::getBagId, bagIds));
            if (CollectionUtils.isEmpty(bizBags)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            Set<Long> entityIds = bizBags.stream().map(e -> e.getEntityId()).collect(Collectors.toSet());
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (CollectionUtils.isEmpty(entityIds) || entityIds.size() != 1 || !entityIds.contains(
                    authUserInfo.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            bizBagSpecService.deleteBizSpecById(ids);
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1139791));
    }


    /**
     * 【Since v2.5.0】查询套餐包规格列表
     * [INNER API] 查询套餐包规格列表
     *
     * @param request 请求
     *
     * @return {@link PageDTO}
     */
    @RejectCall
    @AuthorizeBss(action = BL03.Bl030603)
    @ApiOperation("查询套餐包规格列表")
    @GetMapping("/page")
    public PageDTO listBag(@Valid DescribeBizBagSpecRequest request) {
        BizBag bag = bizBagService.getOne(new LambdaQueryWrapper<BizBag>().eq(BizBag::getBagId, request.getBagId()));
        if (Objects.isNull(bag)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bag.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return bizBagSpecService.listBizSpecBags(request);
    }
}
