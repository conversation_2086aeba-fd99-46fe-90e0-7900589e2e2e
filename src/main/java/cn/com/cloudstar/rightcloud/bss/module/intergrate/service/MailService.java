/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.core.annotation.log.ActionLogTypeEnum;
import cn.com.cloudstar.rightcloud.core.annotation.log.CustomerActionLog;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendNotifyRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/05/08 11:31void
 */
@FeignClient(value = "https://cmp-oss:8080", configuration = FeignConfig.class, path = "/api/v1/oss")
public interface MailService {

    @PostMapping("/users/creat_sub_user/send_approve_mail")
    void sendMail(User user);


    @PostMapping("/common/sendNotify")
    RestResult sendNotify(@RequestBody SendNotifyRequest request);

    @PostMapping("/common/sendUpdateCreditLine")
    RestResult sendUpdateCreditLine(@RequestBody SendNotifyRequest request);

    @GetMapping("/common/sendUpdateCreditLineSchedule/{id}")
    RestResult sendUpdateCreditLineSchedule(@PathVariable Long id);
}
