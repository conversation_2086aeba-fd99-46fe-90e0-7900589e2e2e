/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.service;

import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecGroup;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecRefer;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceSpecDetailVO;

/**
 * <AUTHOR>
 * Created on 2019/10/24
 */
public interface BizBillingSpecService extends IService<BizBillingSpec> {

    /**
     * 获取计费关联项
     * @param billingSpec
     * @param cloudEnvId
     * @return
     */
    List<BizBillingSpecRefer> getCloudBillingSpecRefers(BizBillingSpec billingSpec, Long cloudEnvId);

    /**
     * 动态-组装计费规格项显示
     * @param billingConfig
     * @param specMap
     * @param specKey
     * @return
     */
    String packageSpecDesc(String billingConfig, Map<String, BizBillingSpec> specMap, String specKey);

    /**
     * 组装规格族价格及规格名称
     * @param specGroup
     * @param specCharge
     * @param specDetailVO
     * @param specMap
     * @return
     */
    String packageSpecName(BizBillingSpecGroup specGroup, BizBillingTariffSpecCharge specCharge, AccountPriceSpecDetailVO specDetailVO,
        Map<String, BizBillingSpec> specMap);

    /**
     * 组装规格族的规格项名称
     * @param specNames
     * @param specMap
     * @return
     */
    Map<String, Object> packageSpecNameOfGroup(List<String> specNames, Map<Long, BizBillingSpec> specMap);


}
