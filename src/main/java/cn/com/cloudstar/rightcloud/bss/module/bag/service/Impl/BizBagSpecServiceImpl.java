/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagSpecMapper;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagSpecResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagSpecResponseVO;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.PageDTO;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagSpecService;
import cn.com.cloudstar.rightcloud.bss.module.bag.util.ValidUtil;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 套餐包规格
 *
 * @author: pujian
 * @date: 2022/03/09 14:55
 */
@Service
public class BizBagSpecServiceImpl extends ServiceImpl<BizBagSpecMapper, BizBagSpec> implements BizBagSpecService {

    /**
     * 折扣单位
     */
    public static final String DISCOUNT_UNIT = "折";

    /**
     * 卡时单位
     */
    public static final String CARD_HOUR_UNIT = "卡时";

    @Autowired
    private BizBagSpecMapper bizBagSpecMapper;

    @Autowired
    private BizBagService bizBagService;

    @Override
    public Integer createBizBagSpec(CreateBizBagSpecRequest bagSpec) {

        String bagId = bagSpec.getBagId();
        BizBag one = bizBagService.query().eq("bag_id", bagId).one();
        if (one == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        BizBagSpec bizBagSpec=new BizBagSpec();
        Integer period = Integer.valueOf(bagSpec.getPeriod());
        QueryWrapper<BizBagSpec> queryWrapper= new QueryWrapper<>();
        queryWrapper.eq("period",period);
        queryWrapper.eq("bag_id", bagId);
        queryWrapper.eq("spec_value",bagSpec.getSpecValue());
        List<Map<String, Object>> list = this.baseMapper.selectMaps(queryWrapper);
        if (list.size()!=0){
            throw  new BizException(WebUtil.getMessage(MsgCd.cycle_exists));
        }
        BigDecimal specValue = bagSpec.getSpecValue();
        String format = ValidUtil.getUUID();
        bizBagSpec.setId(format.substring(9,28));
        bizBagSpec.setBagId(bagId);
        bizBagSpec.setPeriod(period);
        bizBagSpec.setPrice(bagSpec.getPrice());
        bizBagSpec.setSpecValue(specValue);
        if (Objects.nonNull(specValue)){
            if (StatusType.CARD_HOUR.equals(one.getType())) {
                bizBagSpec.setSpecName(specValue.intValue() + CARD_HOUR_UNIT);
            }else{
                bizBagSpec.setSpecName(specValue.multiply(new BigDecimal("10")).doubleValue() + DISCOUNT_UNIT);
            }
        }
        WebUserUtil.prepareInsertParams(bizBagSpec);
        return bizBagSpecMapper.insert(bizBagSpec);
    }

    @Override
    public Boolean updateBizSpecById(BizBagSpec bizSpec) {
        BigDecimal specValue = bizSpec.getSpecValue();
        if (Objects.nonNull(specValue)){
            String bagId = bizSpec.getBagId();
            BizBag one = bizBagService.query().eq("bag_id", bagId).one();
            String type = one.getType();
            if (StatusType.CARD_HOUR.equals(type)) {
                bizSpec.setSpecName(specValue.intValue() + CARD_HOUR_UNIT);
            }else{
                bizSpec.setSpecName(specValue.multiply(new BigDecimal("10")) + DISCOUNT_UNIT);
            }
        }
        int flag = bizBagSpecMapper.updateById(bizSpec);
        if (flag==1){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public void deleteBizSpecById(List<String> ids) {
        bizBagSpecMapper.deleteBatchIds(ids);
    }

    @Override
    public IPage<BizBagSpec> listBizSpecBag(IPage<BizBagSpec> page, DescribeBizBagSpecRequest request) {
        QueryWrapper<BizBagSpec> queryWrapper = new QueryWrapper<>();
        if (Objects.nonNull(request.getBagId())){
            queryWrapper.eq("bag_id",request.getBagId());
        }
        if (Objects.nonNull(request.getPeriod())){
            queryWrapper.eq("period",request.getPeriod());
        }
        if (Objects.nonNull(request.getSpecValue())){
            queryWrapper.eq("Spec_value",request.getSpecValue());
        }
        return this.page(page,queryWrapper);
    }

    @Override
    public PageDTO listBizSpecBags(DescribeBizBagSpecRequest request) {
        Integer totalRows = bizBagSpecMapper.selectTotalRowsByGrep(request);
        List<DescribeBizBagSpecResponse> list = bizBagSpecMapper.listBizBagSpec(request);
        if (CollectionUtils.isEmpty(list)){
            return PageDTO.setPageDataList(totalRows,list);
        }
        Map<BigDecimal, List<DescribeBizBagSpecResponse>> collect =
                list.stream().collect(Collectors.groupingBy(DescribeBizBagSpecResponse::getSpecValue));
        if (CollectionUtils.isEmpty(collect)){
            return PageDTO.setPageDataList(totalRows,collect);
        }
        List<DescribeBizBagSpecResponseVO> res = new ArrayList<>();
        Set<Map.Entry<BigDecimal, List<DescribeBizBagSpecResponse>>> entries = collect.entrySet();
        for (Map.Entry<BigDecimal, List<DescribeBizBagSpecResponse>> entry : entries) {
            DescribeBizBagSpecResponseVO describeBizBagSpecResponseVO = new DescribeBizBagSpecResponseVO();
            describeBizBagSpecResponseVO.setSpecValue(entry.getKey());
            describeBizBagSpecResponseVO.setSpecList(entry.getValue());
            res.add(describeBizBagSpecResponseVO);
        }
        return PageDTO.setPageDataList(totalRows,res);
    }
}
