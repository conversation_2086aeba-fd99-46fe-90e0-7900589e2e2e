<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.notice.mapper.SysMNoticeMapper">
  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.module.notice.pojo.entity.SysMNotice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result property="noticeTitle" column="notice_title" />
    <result property="noticeContent" column="notice_content" />
    <result property="noticeStatus" column="notice_status" />
    <result property="publishDt" column="publish_dt" />
    <result property="createDt" column="create_dt" />
    <result property="createBy" column="create_by" />
    <result property="updateDt" column="update_dt" />
    <result property="updateBy" column="upadte_by" />
    <result property="noticeTypeId" column="notice_type_id" />
    <result property="noticeTypeName" column="notice_type_name" />
  </resultMap>
  <select id="selectList" resultMap="BaseResultMap">
      SELECT smn.*,smnt.notice_type_name
      FROM `sys_m_notice` smn
      left join sys_m_notice_type smnt on smn.notice_type_id = smnt.id
      <include refid="whereSql"></include>
  </select>

    <select id="selectNoticeList" resultMap="BaseResultMap" >
    SELECT
	smn.id,
	smn.notice_title,
	smn.notice_type_id,
	smn.notice_content,
	smn.notice_status,
	smn.publish_dt,
	smn.create_dt,
	smn.create_by,
	smn.update_dt,
	smn.update_by,
	smn.entity_id,
	smn.entity_name,
	smnt.notice_type_name
   FROM
	`sys_m_notice` smn
	LEFT JOIN sys_m_notice_type smnt ON smn.notice_type_id = smnt.id
   WHERE
	smn.notice_status = 1
	AND smn.id NOT IN (
	SELECT notice_id FROM sys_m_user_notice_info WHERE `status` = 'closed'  AND  user_sid = #{usrSid}
	)
   ORDER BY
	smn.publish_dt ASC;
  </select>


  <sql id="whereSql">
    <where>
      <if test="criteria.noticeStatus !=null">
          and smn.notice_status =#{criteria.noticeStatus}
      </if>
      <if test="criteria.noticeTitle !=null">
        and smn.notice_title like concat("%",#{criteria.noticeStatus},"%")
      </if>
      <if test="criteria.noticeTypeId !=null">
        and smn.notice_type_id = #{criteria.noticeTypeId}
      </if>
      <if test="criteria.publishStartDate !=null">
        and   #{criteria.publishStartDate} >= smn.publish_dt
      </if>
      <if test="criteria.publishEndDate !=null">
        and  smn.publish_dt >= #{criteria.publishEndDate}
      </if>
    </where>


  </sql>

</mapper>