/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BillingConfigCategory;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpec;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecGroup;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategy;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingStrategyAccount;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.CreateBizBillingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.DescribeBizBillingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpdateBizBillingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceGroupDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.AccountPriceSpecDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBizBillingStrategyDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBizBillingStrategyResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingSpecService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingTariffSpecChargeService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingSpecGroupService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;

import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD03.BD03;
import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD03.BD0304;
import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD03.BD0305;
import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD03.BD0306;

/**
 * 计费策略管理
 *
 * <AUTHOR>
 * Created on 2019/10/22
 */
@Api(tags = "计费策略管理")
@Slf4j
@RestController
@RequestMapping("/billing/strategy")
public class BizBillingStrategyController {

    @Autowired
    @Lazy
    private BizBillingStrategyService strategyService;

    @Autowired
    private IBizBillingSpecGroupService bizBillingSpecGroupService;

    @Autowired
    private BizBillingSpecService specService;

    @Autowired
    private BizBillingTariffSpecChargeService chargeService;

    @Autowired
    private BizBillingStrategyAccountService bizBillingStrategyAccountService;

    @DubboReference
    private CloudEnvAccountRemoteService cloudEnvAccountRemoteService;

    @Autowired
    private EntityUserMapper entityUserMapper;

    /**
     * [INNER API] 计费策略列表
     *
     * @param request 计费策略查询请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("计费策略列表")
    @GetMapping("")
    public RestResult<BaseGridReturn<List<DescribeBizBillingStrategyResponse>>> queryList(@Validated DescribeBizBillingStrategyRequest request) {
        /* 获取并验证当前登录用户*/
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1255531007));
        }
        QueryWrapper<BizBillingStrategy> strategyQueryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(request.getNameLike())) {
            strategyQueryWrapper.like("name", request.getNameLike());
        }
        if (!StringUtils.isEmpty(request.getCategory())) {
            strategyQueryWrapper.eq("category", request.getCategory());
        }
        if (Objects.nonNull(request.getStatus())) {
            strategyQueryWrapper.eq("status", request.getStatus());
        }

        /* 填充分页信息*/
        Page<BizBillingStrategy> strategyPage;
        if (PageUtil.isPageQuery(request)) {
            strategyPage = new Page<>(request.getPagenum(), request.getPagesize());
        } else {
            strategyPage = new Page<>(1, DescribeBizBillingStrategyRequest.DEFAULT_PAGE_SIZE);
        }

        IPage<BizBillingStrategy> page = strategyService.selectByParams(strategyPage, strategyQueryWrapper);
        for (BizBillingStrategy strategy : page.getRecords()) {
            if (BillingConfigCategory.CLOUD_SERVICE.equals(strategy.getCategory())) {
                String serviceNames = strategyService.getServiceNames(strategy.getId());
                strategy.setLinkedNames(serviceNames);
            } else {
                // 取得当前策略对应的环境ID
                List<BizBillingStrategyAccount> bizBillingStrategyAccounts =
                        bizBillingStrategyAccountService.list(
                                new QueryWrapper<BizBillingStrategyAccount>().lambda()
                                        .eq(BizBillingStrategyAccount::getBillingStrategyId, strategy.getId()));
                Set<Long> cloudIds = CollectionUtil.isNotEmpty(bizBillingStrategyAccounts)
                        ? bizBillingStrategyAccounts.stream().map(
                        BizBillingStrategyAccount::getAccountId).collect(Collectors.toSet())
                        : Sets.newHashSet();
                Set<String> accountNames = Sets.newHashSet();
                cloudIds.forEach(cloudId -> {
                    // 取得CMP库中的环境信息
                    CloudEnvAccount cloudEnvAccount = cloudEnvAccountRemoteService.selectByPrimaryKey(cloudId);
                    if (Objects.nonNull(cloudEnvAccount)) {
                        accountNames.add(cloudEnvAccount.getEnvAccountName());
                    }
                });
                strategy.setLinkedNames(accountNames.stream().collect(Collectors.joining(",")));
            }
        }

        return new RestResult(new BaseGridReturn(BeanConvertUtil.convertPage(page, DescribeBizBillingStrategyResponse.class)));
    }

    /**
     * 创建计费策略
     *
     * @param request 创建计费策略请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD.BD03.BD0302)
    @ApiOperation("创建计费策略")
    @PostMapping("")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.name", resource = OperationResourceEnum.CREATE_A_BILLING_POLICY,param = "#request")
    @Idempotent
    public RestResult createBillingStrategy(@Validated @RequestBody CreateBizBillingStrategyRequest request) {
        BizBillingStrategy strategy = BeanConvertUtil.convert(request, BizBillingStrategy.class);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS),strategyService.createBillingStrategy(strategy));
    }

    /**
     * 更新计费策略
     *
     * @param request 创建计费策略请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD0305)
    @ApiOperation("更新计费策略")
    @PutMapping("")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.name", resource = OperationResourceEnum.EDIT_BILLING_POLICY,bizId = "#request.id",param = "#request")
    public RestResult updateBillingStrategy(@RequestBody @Valid UpdateBizBillingStrategyRequest request) {
        BizBillingStrategy strategy = BeanConvertUtil.convert(request, BizBillingStrategy.class);
        strategyService.updateBillingStrategy(strategy);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 启用/禁用计费策略
     *
     * @param id     计费策略id
     * @param status 计费策略状态
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD0304)
    @ApiOperation("启用/禁用计费策略")
    @PutMapping("/{id}/{status}")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'计费策略名称'", resource = OperationResourceEnum.ENABLE_AND_DISABLE_BILLING_POLICY,bizId = "#id",param = "#status", tagNameUs ="'Billing Policy Name'")
    public RestResult updateBillingStrategyStatus(@PathVariable("id") Long id, @PathVariable("status") String status) {
        strategyService.updateBillingStrategyStatus(id, status);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 删除计费策略
     *
     * @param id 计费策略id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD0306)
    @ApiOperation("删除计费策略")
    @DeleteMapping("/{id}")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'计费策略名称'", tagNameUs ="'Billing Policy Name'", 
            resource = OperationResourceEnum.DELETE_BILLING_POLICY,bizId = "#id")
    public RestResult deleteBillingStrategy(@PathVariable("id") Long id) {
        strategyService.deleteBillingStrategy(id);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 获取策略详情
     *
     * @param id 策略id
     * @return {@code DescribeBizBillingStrategyDetailResponse}
     */
    @AuthorizeBss(action = BD03)
    @ApiOperation(httpMethod = "GET", value = "获取策略详情")
    @GetMapping("/{id}")
    public DescribeBizBillingStrategyDetailResponse findStrategyDetail(@PathVariable("id")
                                                                 @ApiParam(value = "策略ID", type = "Long", required = true) Long id) {
        BizBillingStrategy strategy = strategyService.getById(id);
        if (strategy == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2016808703));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(strategy.getEntityId());
        if (Objects.isNull(authUserInfo.getOrgSid()) && !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        QueryWrapper<BizBillingSpecGroup> specGroupQuery = new QueryWrapper<>();
        specGroupQuery.lambda().eq(BizBillingSpecGroup::getStrategyId, id);
        List<BizBillingSpecGroup> specGroups = bizBillingSpecGroupService.list(specGroupQuery);
        if (CollectionUtil.isEmpty(specGroups)) {
            return BeanConvertUtil.convert(strategy, DescribeBizBillingStrategyDetailResponse.class);
        }
        Map<Long, BizBillingSpecGroup> specGroupMap = specGroups.stream()
                .collect(Collectors.toMap(BizBillingSpecGroup::getId, o -> o, (key1, key2) -> key1));
        Set<String> specIdSet = specGroups.stream().map(BizBillingSpecGroup::getSpecId).collect(Collectors.toSet());
        List<String> specIds = Lists.newArrayList();
        specIdSet.forEach(str -> {
            specIds.addAll(Arrays.asList(StrUtil.splitToArray(str, StrUtil.COMMA)));
        });
        Collection<BizBillingSpec> specs = specService.listByIds(specIds);

        Map<String, BizBillingSpec> specMap = specs.stream()
                .collect(Collectors.toMap(BizBillingSpec::getSpecName, o -> o, (key1, key2) -> key1));
        for (BizBillingSpecGroup specGroup : specGroups) {
            AccountPriceGroupDetailVO groupDetailVO = new AccountPriceGroupDetailVO();
            //类型族名称
            groupDetailVO.setGroupName(specGroup.getName());
            Long specGroupId = specGroup.getId();
            strategy.getGroups().add(groupDetailVO);
            QueryWrapper<BizBillingTariffSpecCharge> specChargeQuery = new QueryWrapper<>();
            specChargeQuery.lambda().eq(BizBillingTariffSpecCharge::getSpecGroupId, specGroupId);
            List<BizBillingTariffSpecCharge> specCharges = chargeService.list(specChargeQuery);
            if (CollectionUtil.isEmpty(specCharges)) {
                continue;
            }

            for (BizBillingTariffSpecCharge specCharge : specCharges) {
                AccountPriceSpecDetailVO specDetailVO = new AccountPriceSpecDetailVO();
                groupDetailVO.getSpecs().add(specDetailVO);
                specDetailVO.setSpecName(
                        specService.packageSpecName(specGroupMap.get(specGroupId), specCharge, specDetailVO, specMap));
            }
        }
        if (CollUtil.isNotEmpty(strategy.getGroups())) {
            strategy.getGroups().removeIf(item -> CollUtil.isEmpty(item.getSpecs()));
        }
        return BeanConvertUtil.convert(strategy, DescribeBizBillingStrategyDetailResponse.class);
    }

}
