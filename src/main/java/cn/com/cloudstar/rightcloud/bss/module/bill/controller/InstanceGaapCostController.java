/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.BizConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserDto;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagUser;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.IBizBagCardHourUsageDetailService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeBizBagOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest.DimesionEnum;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.GaapCostDetailRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeGaapCostResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeGaapCostSimpleResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IInstanceGaapCostService;
import cn.com.cloudstar.rightcloud.bss.module.bill.task.CalculateSdrBillsTask;
import cn.com.cloudstar.rightcloud.bss.module.bill.task.RegularCleaningOfBillingTask;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.DynamicMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.enums.DesensitizedType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 月费用分摊服务
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@RestController
@RequestMapping("/bills")
@Slf4j
public class InstanceGaapCostController {

    private static final String HUAWEI_CLOUD = "HuaweiCloud";
    private static final String SUBSCRIPTION_ORDER = "SubscriptionOrder";
    private static final String PAY_AS_YOU_GO_BILL = "PayAsYouGoBill";

    /**
     *
     */
    private static final String NOT_APPLICABLE = "N/A";

    private static final String CUE_VALUE = "cueValue";

    private static final String ASC = "asc";

    @Autowired
    private IInstanceGaapCostService instanceGaapCostService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private DynamicMapper dynamicMapper;

    @Autowired
    private CalculateSdrBillsTask calculateSdrBillsTask;
    @Autowired
    private OrgService orgService;
    @Autowired
    private IServiceOrderService serviceOrderService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RegularCleaningOfBillingTask regularCleaningOfBillingTask;
    @Autowired
    private BizBagUserService bizBagUserService;
    @Autowired
    private IBizBagCardHourUsageDetailService bizBagCardHourUsageDetailService;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    /**
     * 账单列表(费用中心-我的账单)
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC03)
    @GetMapping
    public IPage<DescribeGaapCostSimpleResponse> listBills(DescribeGaapCostRequest request) {
        return getListBills(request);
    }

    /**
     * 账单列表 总记录数
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC03)
    @GetMapping("/totalRows")
    public IPage billTotalRow(DescribeGaapCostRequest request) {
        return getListBillsRows(request);
    }
    /**
     * [INNER API] 账单列表
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @RejectCall
    @GetMapping("/totalRows/feign")
    public IPage billTotalRowFeign(DescribeGaapCostRequest request) {
        return billTotalRow(request);
    }

    private IPage getListBillsRows(DescribeGaapCostRequest request) {
        if (StringUtils.isNotEmpty(request.getGroupByPeriod()) && StringUtils.isEmpty(request.getGroupByDimension())) {
            request.setGroupByDimension(DimesionEnum.ORDER.getCode());
        }
        request.setPageFlag("Y");
        if (ProductCodeEnum.HPC.getProductCode().equals(request.getProductCode())) {
            request.setProductCodes(
                Arrays.asList(ProductCodeEnum.HPC.getProductCode(), ProductCodeEnum.BIZ_BAG.getProductCode()));
        } else if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(request.getProductCode())) {
            request.setProductCodes(
                Arrays.asList(ProductCodeEnum.MODEL_ARTS.getProductCode(), ProductCodeEnum.BIZ_BAG.getProductCode()));
        }
        IPage responseIPage =  new Page<>();
        //如果是卡时包查询卡时包明细
        String bagInstUuid = request.getBagInstUuid();
        boolean isCardHourBag = false;
        if (StringUtils.isNotEmpty(bagInstUuid)) {
            BizBagUser oneBagUser = bizBagUserService.lambdaQuery()
                .eq(BizBagUser::getBagInstUuid, bagInstUuid)
                .eq(BizBagUser::getBagType, StatusType.CARD_HOUR).one();
            if (oneBagUser != null) {
                responseIPage = bizBagCardHourUsageDetailService.getResponseFromUsageDetail(request,oneBagUser);
                isCardHourBag = true;
            }
        }
        if (!isCardHourBag) {
            responseIPage = getResponseIPageCount(request);
        }
        return responseIPage;
    }




    /**
     * 用户套餐包使用明细
     *【Since v2.5.0】
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @AuthorizeBss(action = AuthModule.BC.BC03.BC03_COMMON)
    @GetMapping("/user/bag")
    @RejectCall
    public IPage<DescribeGaapCostSimpleResponse> userBagListBills(DescribeGaapCostRequest request) {
        return getListBills(request);
    }


    /**
     * [INNER API] 账单列表
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @RejectCall
    @GetMapping("/feign")
    public IPage<DescribeGaapCostSimpleResponse> listBillsByFeign(DescribeGaapCostRequest request) {
        return getListBills(request);
    }

    /**
     * 【Since v2.5.0】
     * req260 账单明细优化
     * 提供给upgrade模块处理历史数据
     */
    @GetMapping("/upgrade")
    @RejectCall
    public void upgradeOfBills() {
        instanceGaapCostService.upgradeOfBills();
    }

    private BigDecimal scaleTwo(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取账单周期列表数据
     *
     * @param request 成本明细请求体
     * @return {@code IPage<BillBillingCycleCostVo>}
     */
    @AuthorizeBss(action = CC.CC030301)
    @GetMapping(value = "/listCycleBills")
    @ApiOperation(httpMethod = "GET", value = "获取账单周期列表数据")
    @Authorize(action = "billcenter:billings:ListCycleBills")
    public IPage<BillBillingCycleCostVo> listCycleBills(DescribeGaapCostRequest request) {
        /* 通过请求获取账单数据*/
        IPage<BillBillingCycleCostVo> response = instanceGaapCostService.listCycleBills(request);
        return response;
    }

    /**
     * [INNER API] 获取账单周期列表数据
     *
     * @param request 成本明细请求体
     * @return {@code IPage<BillBillingCycleCostVo>}
     */
    @RejectCall
    @GetMapping(value = "/listCycleBills/feign")
    @ApiOperation(httpMethod = "GET", value = "获取账单周期列表数据")
    public IPage<BillBillingCycleCostVo> listCycleBillsByFeign(DescribeGaapCostRequest request) {
        /* 通过请求获取账单数据*/
        IPage<BillBillingCycleCostVo> response = instanceGaapCostService.listCycleBills(request);
        return response;
    }

    /**
     * 账单明细异步导出
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = CC.CC0302  + "," + AuthModule.CC.CC030402)
    @PostMapping(value = "/asynExportBillDetails")
    @ApiOperation(httpMethod = "POST", value = "账单明细异步导出")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单导出'",integrity = true, resource = OperationResourceEnum.BILL_ASYNC_EXPORT, param = "#request", tagNameUs ="'Bill export'")
    @Idempotent
    @ListenExpireBack
    public RestResult asynExportBillDetails(@RequestBody @Valid DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        return instanceGaapCostService.asynExportBillDetails(request, moduleType);
    }

    /**
     * [INNER API] 账单明细异步导出
     *【Since v2.5.0】
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @RejectCall
    @PostMapping(value = "/asynExportBillDetails/feign")
    @ApiOperation(httpMethod = "POST", value = "账单明细异步导出")
    @Idempotent
    public RestResult asynExportBillDetailsByFeign(@RequestBody @Valid DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        return instanceGaapCostService.asynExportFeginBillDetails(request, moduleType);
    }


    /**
     * [INNER API] 套餐包使用明细导出
     * 套餐包使用明细导出
     *【Since v2.5.0】
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.BL.BL03.BL03)
    @GetMapping(value = "/mgtAsynExportBizBagBillDetails")
    @ApiOperation(httpMethod = "GET", value = "折扣包明细异步导出")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "#request.customerName", bizId = "#request.billBillingCycleId", resource = OperationResourceEnum.ASYN_BIZ_BAG_BILL_DETAILS_EXPORT)
    @RejectCall
    public RestResult mgtAsynExportBizBagBillDetails(DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        if (StringUtils.isBlank(request.getBagInstUuid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizBagUser bagUser = bizBagUserService.getOne(
                new LambdaQueryWrapper<BizBagUser>().eq(BizBagUser::getBagInstUuid, request.getBagInstUuid()));
        if (Objects.isNull(bagUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(bagUser.getEntityId(), authUserInfo.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return instanceGaapCostService.asynExportBizBagBillDetails(request, moduleType);
    }

    /**
     * 套餐包使用明细导出
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = AuthModule.CC.CC03)
    @GetMapping(value = "/asynExportBizBagBillDetails")
    @ApiOperation(httpMethod = "GET", value = "折扣包明细异步导出")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'折扣包明细异步导出'",param = "#request" ,bizId = "#request.bagInstUuid", resource = OperationResourceEnum.ASYN_BIZ_BAG_BILL_DETAILS_EXPORT, tagNameUs ="'Asynchronous export of discount package details'")
    public RestResult asynExportBizBagBillDetails(DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        if (StringUtils.isBlank(request.getBagInstUuid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizBagUser bagUser = bizBagUserService.getOne(
                new LambdaQueryWrapper<BizBagUser>().eq(BizBagUser::getBagInstUuid, request.getBagInstUuid()));
        if (Objects.isNull(bagUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if ((!Objects.equals(bagUser.getOrgSid(), authUserInfo.getOrgSid()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return instanceGaapCostService.asynExportBizBagBillDetails(request, moduleType);
    }


    /**
     * 折扣包明细异步导出
     * [INNER API] 折扣包订购明细异步导出
     *
     * @param request    套餐包请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @RejectCall
    @AuthorizeBss(action = AuthModule.BL.BL03.BL030801)
    @GetMapping(value = "/asynExportBizBagOrder")
    @ApiOperation(httpMethod = "GET", value = "折扣包明细异步导出")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "#request.orgName", bizId = "#request.bagId", resource = OperationResourceEnum.ASYN_BIZ_BAG_ORDER_EXPORT)
    public RestResult asynExportBizBagOrder(DescribeBizBagOrderRequest request, @RequestHeader String moduleType) {
        return instanceGaapCostService.asynExportBizBagOrder(request, moduleType);
    }


    /**
     * 账单周期异步导出
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = CC.CC030302)
    @GetMapping(value = "/asynExportBills")
    @ApiOperation(httpMethod = "GET", value = "账单周期异步导出")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单周期列表'",integrity = true,bizId = "#request.billBillingCycleId", resource = OperationResourceEnum.BILLING_CYCLE_EXPORT, tagNameUs ="'Billing Cycle List'")
    @Idempotent
    public RestResult asynExportBills(DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        if (StringUtils.isBlank(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_18819451));
        }
        if (!Arrays.asList(Constants.BSS, Constants.CONSOLE).contains(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1319710067));
        }

        String specifiMonth = request.getSpecifiMonth();
        if (StringUtils.isNotBlank(specifiMonth)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setLenient(false);
            try {
                sdf.parse(specifiMonth);
            } catch (Exception e) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_426564724));
            }
        }

        return instanceGaapCostService.asynExportBillCycles(request, moduleType);
    }

    /**
     * [INNER API] 账单周期异步导出
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     */
    @RejectCall
    @GetMapping(value = "/asynExportBills/feign")
    @ApiOperation(httpMethod = "GET", value = "账单周期异步导出")
    @Idempotent
    public RestResult asynExportBillsByFeign(DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        String specifiMonth = request.getSpecifiMonth();
        if (StringUtils.isNotBlank(specifiMonth)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setLenient(false);
            try {
                sdf.parse(specifiMonth);
            } catch (ParseException e) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_426564724));
            }
        }

        return instanceGaapCostService.asynExportBillCycles(request, moduleType);
    }

    /**
     * 周期明细数据导出(我的账单)
     *
     * @param request 账单周期查询明细请求体
     * @return {@code IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost>}
     */
    @AuthorizeBss(action = AuthModule.CC.CC03)
    @ApiOperation("账单周期账期列表")
    @PostMapping("/listBillDetails")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单周期账期列表'", param = "#request", tagNameUs ="'Billing Period Billing Period List'",
            resource = OperationResourceEnum.QUERY_BILLING_CRYLE_LIST,bizId = "#request.ids")
    @Idempotent
    public IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> listBillDetails(
            @RequestBody GaapCostDetailRequest request) {
        return instanceGaapCostService.listBillDetails(request);
    }

    /**
     * [INNER API] 账单周期账期列表
     *
     * @param request 账单周期查询明细请求体
     * @return {@code IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost>}
     */
    @RejectCall
    @ApiOperation("账单周期账期列表")
    @PostMapping("/listBillDetails/feign")
    @Idempotent
    public IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> listBillDetailsByFeign(@RequestBody GaapCostDetailRequest request) {
        authorityJudgment(request);
        return instanceGaapCostService.listBillDetails(request);
    }

    private void authorityJudgment(GaapCostDetailRequest request) {
        List<String> ids = new ArrayList<>();
        DescribeGaapCostRequest describeGaapCostRequest = new DescribeGaapCostRequest();
        describeGaapCostRequest.setGroupByFlag("cycle");
        describeGaapCostRequest.setPagesize(10L);
        describeGaapCostRequest.setPagenum(0L);
        List<BillBillingCycleCostVo> records = listCycleBillsByFeign(describeGaapCostRequest).getRecords();
        records.stream().map(BillBillingCycleCostVo::getIds).collect(Collectors.toList()).forEach(ids::addAll);
        ids = new ArrayList<>(new LinkedHashSet<>(ids));
        List<String> finalIds = ids;
        request.getIds().forEach(id1->{
            if (finalIds.stream().noneMatch(id2->id2.equals(id1))){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        });
    }

    /**
     * [INNER API] 客户导入信息异步导出
     *
     * @param request 客户导入信息请求体
     * @return {@code RestResult}
     */
    @PostMapping(value = "/asyn-export-customer")
    @ApiOperation(httpMethod = "GET", value = "客户导入信息异步导出")
    @Idempotent
    @RejectCall
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'客户信息'", resource = OperationResourceEnum.ASYN_CUSTOMER_EXPORT, tagNameUs ="'Customer Information'")
    public RestResult<Long> asynExportCustomer(@RequestBody ImportCustomerRequest request) {
        return instanceGaapCostService.asynExportCustomer(request);
    }


    private BigDecimal scaleThree(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(3, BigDecimal.ROUND_HALF_UP);
    }

    private BigDecimal preventNPE(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        } else {
            return bigDecimal.setScale(5, BigDecimal.ROUND_DOWN);
        }
    }

    private Map<String, String> billProductInfoMap(){
        List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectDistinctByParams();
        return serviceCategories.stream().collect(
                Collectors.toMap(ServiceCategory::getServiceType, ServiceCategory::getProductName));
    }

    private IPage<DescribeGaapCostSimpleResponse> getListBills(DescribeGaapCostRequest request) {
        if (StringUtils.isNotEmpty(request.getGroupByPeriod()) && StringUtils.isEmpty(request.getGroupByDimension())) {
            request.setGroupByDimension(DimesionEnum.ORDER.getCode());
        }
        request.setPageFlag("Y");
        if (ProductCodeEnum.HPC.getProductCode().equals(request.getProductCode())) {
            request.setProductCodes(
                    Arrays.asList(ProductCodeEnum.HPC.getProductCode(), ProductCodeEnum.BIZ_BAG.getProductCode()));
        } else if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(request.getProductCode())) {
            request.setProductCodes(
                    Arrays.asList(ProductCodeEnum.MODEL_ARTS.getProductCode(), ProductCodeEnum.BIZ_BAG.getProductCode()));
        }
        IPage<DescribeGaapCostResponse> responseIPage =  new Page<>();
        //如果是卡时包查询卡时包明细
        String bagInstUuid = request.getBagInstUuid();
        boolean isCardHourBag = false;
        if (StringUtils.isNotEmpty(bagInstUuid)) {
            BizBagUser oneBagUser = bizBagUserService.lambdaQuery()
                                              .eq(BizBagUser::getBagInstUuid, bagInstUuid)
                                              .eq(BizBagUser::getBagType, StatusType.CARD_HOUR).one();
            if (oneBagUser != null) {
                responseIPage = bizBagCardHourUsageDetailService.getResponseFromUsageDetail(request,oneBagUser);
                isCardHourBag = true;
            }
        }
        if (!isCardHourBag) {
            responseIPage = getResponseIPage(request);
        }

        List<DescribeGaapCostResponse> list = responseIPage.getRecords();
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        if (CollectionUtil.isNotEmpty(list) && isUs) {
            list.forEach(e -> {
                e.setConfiguration(this.configurationToUs(e.getConfiguration()));
                e.setUsageCount(StringUtils.replaceEach(e.getUsageCount(), new String[]{"天", "小时", "分", "秒"}, new String[]{"day", "h", "m", "s"}));
            });
        }

        return BeanConvertUtil.convertPage(responseIPage, DescribeGaapCostSimpleResponse.class);
    }

    private static String configurationToUs(String configuration) {
        String[] zhs = {"标准存储", "低频存储", "低频访问存储", "归档存储", "默认规格", "AI全流程开发"};
        String[] ens = {"Standard storage", "Low frequency storage", "Low frequency access storage", "archival storage", "Default specifications", "Full process development of AI"};
        return StringUtils.replaceEach(configuration, zhs, ens);
    }

    /**
     * 获取账单明细总行数
     * @param request
     * @return
     */
    private IPage getResponseIPageCount(DescribeGaapCostRequest request) {
        return instanceGaapCostService.listBillsCount(request);
    }


    /**
     * 获取账单明细数据总行数
     * @param request
     * @return
     */
    private IPage<DescribeGaapCostResponse> getResponseIPage(DescribeGaapCostRequest request) {
        //获取账单
        IPage<DescribeGaapCostResponse> responseIPage = instanceGaapCostService.listBills(request);
        // 用户真实姓名
        List<UserDto> userDtos = sysUserService.selectAllUserAccount();
        Map<String, String> userRealNameMaps = userDtos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserDto::getAccount))),
                        ArrayList::new))
                .stream()
                .filter(user -> !ObjectUtils.isEmpty(user.getAccount()) && !ObjectUtils.isEmpty(user.getRealName()))
                .collect(Collectors.toMap(UserDto::getAccount, UserDto::getRealName));
        // 分销商
        Set<Long> accountIdsSet = responseIPage.getRecords().stream().map(record -> Long.valueOf(record.getUserAccountId())).collect(Collectors.toSet());


//        Set<Long> accountIdsSet = instanceGaapCostService.findBillingAccountByCurrentUserRole(null);
        Map<String, Object> param = new HashMap<>(128);
        param.put("ids", accountIdsSet);
        List<BizBillingAccount> accounts = bizBillingAccountService.getSimpleAccountAndDistributorInfo(
                param);
        Map<Long, String> accountAndDistributorInfoMaps = accounts.stream()
                                                                  .filter(a -> Objects.nonNull(a.getId())
                                                                          && Objects.nonNull(a.getDistributorName()))
                                                                  .collect(Collectors.toMap(
                                                                          BizBillingAccount::getId,
                                                                          BizBillingAccount::getDistributorName));
        responseIPage.getRecords().forEach(costResponse -> {
            costResponse.setPretaxGrossAmount(preventNPE(costResponse.getPretaxGrossAmount()));
            costResponse.setEraseZeroAmount(preventNPE(costResponse.getEraseZeroAmount()));
            costResponse.setPretaxAmount(preventNPE(costResponse.getPretaxAmount()));
            costResponse.setCashAmount(preventNPE(costResponse.getCashAmount()));
            costResponse.setCouponAmount(preventNPE(costResponse.getCouponAmount()));
            costResponse.setCouponDiscount(preventNPE(costResponse.getCouponDiscount()));
            costResponse.setCreditAmount(preventNPE(costResponse.getCreditAmount()));
            costResponse.setPricingDiscount(preventNPE(costResponse.getPricingDiscount()));

            costResponse.setDistributorName(
                    accountAndDistributorInfoMaps.get(Long.valueOf(costResponse.getUserAccountId())));
            if (StringUtil.isNullOrEmpty(costResponse.getDistributorName())) {
                costResponse.setDistributorName(BizConstants.DISTRIBUTOR_NAME);
            }
            //获取当前产品信息
            Map<String, String> map = billProductInfoMap();
            //设置账单产品名称
            if (StringUtils.isBlank(costResponse.getProductName())) {
                if(map.containsKey(costResponse.getProductCode())){
                    costResponse.setProductName(map.get(costResponse.getProductCode())+" "+costResponse.getProductCode());
                }
            }
            // 用户名（姓名）
            String realName = userRealNameMaps.get(costResponse.getUserAccountName());
            if (StringUtils.isNotBlank(realName)) {
                realName = CCSPCacheUtil.verifyAndCCSPDecrypt(realName);
                realName = DesensitizationUtil.desensitizedByStr(realName, DesensitizedType.NAME);
                costResponse.setUserAccountName(costResponse.getUserAccountName() + "（" + realName + "）");
            }
        });

        if (Objects.equals(request.getSortdatafield(), CUE_VALUE)) {
            if (Objects.equals(ASC, request.getSortorder())) {
                responseIPage.setRecords(responseIPage.getRecords().stream().map(cost -> {
                    if (Objects.isNull(cost.getCueValue())) {
                        cost.setCueValue("");
                    }
                    return cost;
                }).sorted(Comparator.comparing(DescribeGaapCostResponse::getCueValue)).collect(Collectors.toList()));
            } else {
                responseIPage.setRecords(responseIPage.getRecords().stream().map(cost -> {
                    if (Objects.isNull(cost.getCueValue())) {
                        cost.setCueValue("");
                    }
                    return cost;
                }).sorted(Comparator.comparing(DescribeGaapCostResponse::getCueValue).reversed()).collect(Collectors.toList()));
            }
        }
        return responseIPage;
}


}
