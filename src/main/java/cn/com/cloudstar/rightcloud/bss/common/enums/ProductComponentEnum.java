/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import cn.hutool.core.util.StrUtil;

/**
 * DESC:产品组件
 *
 * <AUTHOR>
 * @date 2020/3/19 14:47
 */
public enum ProductComponentEnum {
    /**
     * 全部
     */
    ALL("全部", "all"),

    /**
     * 总览
     */
    OVERVIEW("总览", "overview"),

    /**
     * 云主机
     */
    COMPUTE("云主机", "compute", "ecs", "ecs-x86"),

    /**
     * 弹性IP
     */
    NETWORK("弹性IP", "floatingIp", "floatingip","eip", "eip-inner"),
    /**
     * RDS
     */
    RDS("RDS", "rds"),

    /**
     * OBS
     */
    OBS("对象存储OBS", "obs"),

    /**
     * 私有网络
     */
    VPC("私有网络", "vpc"),

    /**
     * 负载均衡
     */
    LB("负载均衡", "lb"),

    /**
     * 费用中心
     */
    BILLING("费用中心", "billcenter", "billing"),

    /**
     * 访问控制
     */
    ACCESS("访问控制", "iam"),

    /**
     * 租户
     */
    TENANT("租户", "tenant"),

    /**
     * 项目
     */
    PROJECT("项目", "project"),

    /**
     * 工单
     */
    TICKET("工单", "ticket"),

    /**
     * 消息中心
     */
    MESSAGE("消息中心", "message"),

    /**
     * 工单
     */
    QUOTA("配额", "quota"),

    /**
     * 权限配置
     */
    AUTHORIZE("权限配置", "authorize"),

    /**
     * 云硬盘
     */
    VOLUME("云硬盘", "disk", "ebs", "evs"),

    /**
     * 快照
     */
    VBS("快照", "vbs","snapshot"),
    /**
     * 镜像
     */
    IMS("镜像", "ims","image"),

    /**
     * 负载均衡服务
     */
    ELB("负载均衡服务", "elb","lb"),

    /**
     * 裸金属服务
     */
    BMS("裸金属服务", "bms"),

    /**
     * 弹性文件服务
     */
    SFS("弹性文件服务", "sfs"),

    /**
     * modelarts
     */
    MODELARTS("AI开发平台共享资源池", "modelarts"),

    /**
     * 分布式缓存服务
     */
    DCS("分布式缓存服务", "dcs"),

    /**
     * NAT网关服务
     */
    NATGateway("NAT网关服务", "natgateway"),

    /**
     * 云容器引擎
     */
    CCE("云容器引擎", "cce"),

    /**
     * 云备份服务
     */
    CBR("云备份服务", "cbr"),

    /**
     * 网络ACL
     */
    ACL("网络ACL", "acl"),

    /**
     * 分布式消息服务
     */
    DMS("分布式消息服务", "dms"),

    /**
     * 安全组
     */
    SECURITYGROUP("安全组", "securitygroup"),

    /**
     * 应用管理服务
     */
    AMS("应用管理服务", "ams"),

    /**
     * 密钥对
     */
    KEK_PAIR("密钥对", "keypair"),

    /**
     * 高性能计算
     */
    HPC("HPC高性能计算", "hpc"),

    /**
     * HPC共享资源池-SAAS
     */
    HPC_SAAS("HPC共享资源池-SAAS", "hpc-saas"),

    /**
     *基础服务
     */
    INNERPRODUCT("基础服务", "innerproduct"),
    /**
     * 第三方服务
     */
    OTHER_SERVICE("第三方服务", "otherservice"),
    VPN("虚拟专用网络", "vpn"),
    DC("云专线", "dc" ),
    CES("云监控服务", "ces"),
    SWR( "容器镜像服务", "swr"),
    IAAS( "昇腾IAAS专属云服务", "iaas"),
    DEDICATEDRESOURCEPOOL( "AI开发平台专属资源池", "drp"),
    MODELARTS_SERVERS( "ModelArts服务", "modelartsservice"),
    HPC_DRP( "HPC专属资源池", "hpc-drp"),
    MYMIRROR( "容器镜像服务", "mymirror"),
    AI_MARKET( "模型集市", "ai-market"),
    SFS2( "弹性文件服务2.0", "sfs2.0"),
    MA_BMS("弹性裸金属", "ma-bms"),
    RS_BMS("裸金属服务器", "rs-bms"),
    CSS("云搜索服务", "css");

    private String desc;
    private List<String> key;

    ProductComponentEnum(String desc, String... key) {
        this.desc = desc;
        this.key = Lists.newArrayList(key);
    }

    public String getDesc() {
        return desc;
    }

    public List<String> getKey() {
        return key;
    }

    public static List<String> transformDesc(String... keys) {
        if (keys == null) {
            return Collections.emptyList();
        }
        ProductComponentEnum[] components = ProductComponentEnum.values();
        HashSet<String> desc = new HashSet<String>();
        for (String s : keys) {
            for (ProductComponentEnum component : components) {
                if (component.getKey().contains(s.toLowerCase())) {
                    desc.add(component.getDesc());
                }
            }
        }
        return new ArrayList<>(desc);
    }


    public static String keyFromDesc(String key) {
        if (key == null) {
            return StrUtil.EMPTY;
        }

        for (ProductComponentEnum value : ProductComponentEnum.values()) {
            if (value.getKey().contains(key.toLowerCase())) {
                return value.desc;
            }
        }

        return StrUtil.EMPTY;
    }

    public enum ProductDescUsEnum {
        /**
         * 全部
         */
        ALL("全部", "all"),

        /**
         * 总览
         */
        OVERVIEW("总览", "overview"),

        /**
         * 云主机
         */
        COMPUTE("云主机", "ecs"),

        /**
         * 弹性IP
         */
        NETWORK("弹性IP",  "floating ip"),
        /**
         * RDS
         */
        RDS("RDS", "rds"),

        /**
         * OBS
         */
        OBS("对象存储OBS", "obs"),

        /**
         * 私有网络
         */
        VPC("私有网络", "vpc"),

        /**
         * 负载均衡
         */
        LB("负载均衡", "lb"),

        /**
         * 费用中心
         */
        BILLING("费用中心",  "billing"),

        /**
         * 访问控制
         */
        ACCESS("访问控制", "iam"),

        /**
         * 租户
         */
        TENANT("租户", "tenant"),

        /**
         * 项目
         */
        PROJECT("项目", "project"),

        /**
         * 工单
         */
        TICKET("工单", "ticket"),

        /**
         * 消息中心
         */
        MESSAGE("消息中心", "message"),

        /**
         * 工单
         */
        QUOTA("配额", "quota"),

        /**
         * 权限配置
         */
        AUTHORIZE("权限配置", "authorize"),

        /**
         * 云硬盘
         */
        VOLUME("云硬盘", "Cloud hard drive"),

        /**
         * 快照
         */
        VBS("快照", "snapshot"),
        /**
         * 镜像
         */
        IMS("镜像", "image"),

        /**
         * 负载均衡服务
         */
        ELB("负载均衡服务", "elb"),

        /**
         * 裸金属服务
         */
        BMS("裸金属服务", "bms"),

        /**
         * 弹性文件服务
         */
        SFS("弹性文件服务", "sfs"),

        /**
         * modelarts
         */
        MODELARTS("AI开发平台共享资源池", "modelarts"),

        /**
         * 分布式缓存服务
         */
        DCS("分布式缓存服务", "dcs"),

        /**
         * NAT网关服务
         */
        NATGateway("NAT网关服务", "nat gateway"),

        /**
         * 云容器引擎
         */
        CCE("云容器引擎", "cce"),

        /**
         * 云备份服务
         */
        CBR("云备份服务", "cbr"),

        /**
         * 网络ACL
         */
        ACL("网络ACL", "acl"),

        /**
         * 分布式消息服务
         */
        DMS("分布式消息服务", "dms"),

        /**
         * 安全组
         */
        SECURITYGROUP("安全组", "securitygroup"),

        /**
         * 应用管理服务
         */
        AMS("应用管理服务", "ams"),

        /**
         * 密钥对
         */
        KEK_PAIR("密钥对", "keypair"),

        /**
         * 高性能计算
         */
        HPC("HPC高性能计算", "hpc"),

        /**
         * HPC共享资源池-SAAS
         */
        HPC_SAAS("HPC共享资源池-SAAS", "hpc-saas"),

        /**
         *基础服务
         */
        INNERPRODUCT("基础服务", "innerproduct"),
        /**
         * 第三方服务
         */
        OTHER_SERVICE("第三方服务", "otherservice"),
        VPN("虚拟专用网络", "vpn"),
        DC("云专线", "dc" ),
        CES("云监控服务", "ces"),
        SWR( "容器镜像服务", "swr"),
        IAAS( "昇腾IAAS专属云服务", "iaas"),
        DEDICATEDRESOURCEPOOL( "AI开发平台专属资源池", "drp"),
        MODELARTS_SERVERS( "ModelArts服务", "modelartsservice"),
        HPC_DRP( "HPC专属资源池", "hpc-drp"),
        MYMIRROR( "容器镜像服务", "mymirror"),
        SFS2( "弹性文件服务2.0", "sfs2.0"),
        MA_BMS("弹性裸金属", "ma-bms"),
        RS_BMS("裸金属服务器", "rs-bms");

        private String desc;
        private String descUs;

        ProductDescUsEnum(String desc, String descUs) {
            this.desc = desc;
            this.descUs = descUs;
        }

        public String getDescUs() {
            return descUs;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 这个key上面枚举的desc
         * @param desc desc
         * @return
         */
        public static String keyFromDescUs(String desc) {
            if (desc == null) {
                return StrUtil.EMPTY;
            }

            for (ProductDescUsEnum value : ProductDescUsEnum.values()) {
                if (value.getDesc().contains(desc)) {
                    return value.descUs;
                }
            }

            return StrUtil.EMPTY;
        }

        public static List<String> transformDescUs(List<String> descs) {
            if (descs == null) {
                return Collections.emptyList();
            }
            ProductDescUsEnum[] components = ProductDescUsEnum.values();
            HashSet<String> desc = new HashSet<String>();
            for (String s : descs) {
                for (ProductDescUsEnum component : components) {
                    if (component.getDesc().contains(s)) {
                        desc.add(component.getDescUs());
                    }
                }
            }
            return new ArrayList<>(desc);
        }

    }



}
