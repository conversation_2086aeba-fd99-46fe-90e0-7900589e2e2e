package cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户消息接收人联系人配置表(SysMMsgReceiveContact)实体类
 *
 * <AUTHOR>
 * @since 2022-11-09 10:43:38
 */
@Data
public class SysMMsgReceiveContact implements Serializable {
    private static final long serialVersionUID = -61641708149900126L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 配置ID
     */
    private Long configId;
    /**
     * 消息ID
     */
    private String msgIds;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 联系人电话
     */
    private String phone;
    /**
     * 电话验证标识;未验证：0，已验证：1
     */
    private Integer phoneValidate;
    /**
     * 联系人邮件
     */
    private String email;
    /**
     * 邮件验证标识;未验证：0，已验证：1
     */
    private Integer emailValidate;
    /**
     * 描述备注
     */
    private String description;
    /**
     * 所有者ID
     */
    private String ownerId;
    /**
     * 组织ID
     */
    private Long orgSid;
    /**
     * 创建者组织ID
     */
    private Long createdOrgSid;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;


    /**
     * 默认接收人
     */
    private Integer defaultContact;

    /**
     * 用户表ID
     */
    private Long userSid;
    /**
     * 用户表ID
     */
    private String account;
}

