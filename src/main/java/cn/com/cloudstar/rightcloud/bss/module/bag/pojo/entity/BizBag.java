/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;

/**
 * <p>
 * 运营_套餐包
 * </p>
 *
 * <AUTHOR>
 * @since 2022-3-7
 */
@Data
public class BizBag implements Serializable {

  private static final long serialVersionUID = 1L;
  /**
   * ID
   */
  @TableId(value = "id")
  private String id;
  /**
   *套餐名称
   */
  @NotNull(message = "名称不能为空")
  @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,32}$", message = "名称长度为2-32个字符,并且不能包含特殊符号")
  private String name;
  /**
   *套餐包ID
   */
  private String bagId;
  /**
   *套餐包计费类型,包年包月：PrePaid，按量付费：PostPaid
   */
  @EnumValue(strValues = {"PrePaid"})
  private String billingType;
  /**
   *套餐包类型,折扣包：discount 资源包：resource
   */
  @EnumValue(strValues = {"discount"})
  private String type;
  /**
   *状态 ,上架：online 下架： offline
   */
  @EnumValue(strValues = {"offline","online"})
  private String status;
  /**
   *适用产品
   */
  @EnumValue(strValues = {"HPC", "HPC-SAAS"})
  private String productType;
  /**
   *过期清除策略，0不自动清除，1自动清除
   */
  private Long clearPolicy;
  /**
   *描述
   */
  @Length(max = 256)
  private String description;
  /**
   *组织ID
   */
  private String orgSid;
  /**
   *所有者ID
   */
  private Long ownerId;
  /**
   *版本号 默认为 1
   */
  private Long version;
  /**
   *创建人
   */
  private String createdBy;
  /**
   * 创建时间
   */
  private Date createdDt;
  /**
   * 更新人
   */
  private String updatedBy;
  /**
   * 更新时间
   */
  private Date updatedDt;
  /**
   * 删除标识;0，删除 1未删除
   */
  private Long delFlag;
  /**
   * HPC共享资源池clusterId
   */
  private String clusterId;
  /**
   * 运营实体ID
   */
  private Long entityId;

  /**
   * 产品名
   */
  @TableField(exist = false)
  private String productName;
}
