/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.entity.request;

import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ActionParam;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/28 11:32
 */
@Data
@ApiModel("变更资源配置")
public class ResizeResourceRequest {

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    @NotBlank
    @Length(max = 64, message = "项目ID超过限制")
    private String projectId;

    /**
     * 变更资源详情
     */
    @ApiModelProperty("变更资源详情")
    @Valid
    private ResizeResourceInfo productInfo;

    /**
     * 代客下单管理员sid
     */
    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    /**
     * 用户sid
     */
    @ApiModelProperty("下单用户ID")
    private Long userSid;

    /**
     * 用户组织sid
     */
    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

    /**
     * 资源信息
     */
    @ApiModelProperty("资源信息")
    private ActionParam resourceInfo;

}
