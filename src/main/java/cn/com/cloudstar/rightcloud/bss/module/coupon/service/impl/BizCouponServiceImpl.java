/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.enums.DistributeStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponAccount;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponDelRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CouponResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.CashCouponFeignService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponAccountService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.message.service.ISysMsgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.NotificationUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.UserStatus;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.validation.Valid;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * <p>
 * 优惠劵 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@Service
@Slf4j
public class BizCouponServiceImpl extends ServiceImpl<BizCouponMapper, BizCoupon> implements
    IBizCouponService {

    private static final String ALL_ACCOUNTS = "all";

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IBizCouponAccountService bizCouponAccountService;

    @Autowired
    private ISysMsgService sysMsgService;

    @Autowired
    private ThreadPoolTaskExecutor cloudExecutor;

    @Autowired
    private CashCouponFeignService cashCouponFeignService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private IBizCouponService  bizCouponService;

    @Autowired
    private BizCouponResourceMapper bizCouponResourceMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    @Override
    public boolean createCoupon(CreateCouponRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        User user = userMapper.selectByPrimaryKey(authUserInfo.getUserSid());
        if(!UserStatus.AVAILABILITY.equals(user.getStatus())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_146081217));
        }
        if (request.getReductionCondition().compareTo(BigDecimal.ZERO) > 0
                && request.getReductionCondition().compareTo(request.getDiscountAmount()) <= 0 ) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_3));
        }
        BizCoupon bizCoupon = Objects
                .requireNonNull(BeanConvertUtil.convert(request, BizCoupon.class), "参数不能为空");
        if (new BigDecimal(0).compareTo(request.getReductionCondition()) < 0) {
            if (request.getReductionCondition().compareTo(request.getDiscountAmount()) <= 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }

        Date startTime = bizCoupon.getStartTime();
        Date endTime = bizCoupon.getEndTime();
        if (cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.getDayDiff(startTime, new Date()) < 0
                || startTime.after(endTime)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_799737574));
        } else {
            bizCoupon.setStartTime(startTime);
            bizCoupon.setEndTime(endTime);
        }
        bizCoupon.setCouponNo(NoUtil.generateNo());
        bizCoupon.setDistributeStatus(DistributeStatusEnum.UNDISTRIBUTED.getCode());
        WebUserUtil.prepareInsertParams(bizCoupon);
        //增加运营实体
        Long entityId = RequestContextUtil.getEntityId();
        bizCoupon.setEntityId(entityId);
        bizCoupon.setEntityName(entityUserMapper.getByEntityId(entityId).getEntityName());

        boolean success = this.save(bizCoupon);
        if (success) {
            request.setCouponSid(bizCoupon.getCouponSid());
        }

        return success;
    }

    @Override
    public IPage<DescribeCouponResponse> listCoupons(DescribeCouponRequest request) {
        Page<BizCoupon> pageParams = PageUtil.preparePageParams(request, "createdDt", "desc");
        String status = request.getStatus();
        if (Objects.nonNull(status)) {
            // 因为优惠劵状态时逻辑字段(没存) 这里去掉它
            request.setStatus(null);
        }
        Criteria  criteria = new Criteria();
        criteria.setConditionObject(request);
        IPage<BizCoupon>  iPage = PageUtil.preparePageParams(request);
        if (CouponStatusEnum.UNAVAILABLE.getAlia().equals(status)) {
            criteria.put("coupon_status", status);
            criteria.put("endTime",DateUtil.date().toJdkDate());
            criteria.put("deleted",0);
        } else if (CouponStatusEnum.AVAILABLE.getAlia().equals(status)) {
            criteria.put("coupon_status", status);
            criteria.put("endTime",DateUtil.date().toJdkDate());
            criteria.put("deleted",0);
        } else if (CouponStatusEnum.DELETED.getAlia().equals(status)) {
            criteria.put("deleted",1);
        }

        // 按状态排序
        criteria.put("orderByClause", "distribute_status desc, created_dt desc");

        IPage<BizCoupon> coupons = this.baseMapper.pageBizCouponList(iPage,criteria.getCondition());
        IPage<DescribeCouponResponse> describeCouponResponsePage = BeanConvertUtil
            .convertPage(coupons, DescribeCouponResponse.class);
        describeCouponResponsePage.getRecords().forEach(data -> {
            // 每次查询的判断状态
            Date now = DateUtil.date().toJdkDate();
            if (Objects.equals(1, data.getDeleted())) {
                data.setStatus(CouponStatusEnum.DELETED.getAlia());
            } else if (now.after(data.getEndTime())) {
                data.setStatus(CouponStatusEnum.UNAVAILABLE.getAlia());
            } else {
                data.setStatus(CouponStatusEnum.AVAILABLE.getAlia());
            }
            List<BizCouponAccount> bizCouponAccounts = bizCouponAccountService.list(
                Wrappers.<BizCouponAccount>lambdaQuery()
                    .eq(BizCouponAccount::getCouponSid, data.getCouponSid()));
            data.setReceivedNum(
                bizCouponAccounts.stream().map(BizCouponAccount::getAccountId).distinct().count());
            data.setUsedNum(bizCouponAccounts.stream().map(BizCouponAccount::getCouponStatus)
                .filter(s -> Objects.equals(s, CouponStatusEnum.USED.getCode())).count());
        });
        describeCouponResponsePage.getRecords().sort(Comparator
            .comparing(data -> CouponStatusEnum.getStatusOrders().indexOf(data.getStatus())));

        return describeCouponResponsePage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeCoupon(Long sid, List<Long> accountIds) {
        // 安全校验
        if (CollectionUtils.isNotEmpty(accountIds)) {
            Set<Long> setAccountIds = new HashSet<>(accountIds);
            List<BizBillingAccount> allAccounts = this.bizBillingAccountMapper
                    .selectByList(MapsKit.of("status", "1", "certificationStatus", "authSucceed", "entityId",
                                             RequestContextUtil.getEntityId(),"accountIds", accountIds));
            if (setAccountIds.size() != allAccounts.size()) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        BizCoupon bizCoupon = this.getById(sid);
        if (Objects.isNull(bizCoupon)) {
            log.info("优惠劵不存在,sid=[{}]", sid);
            return false;
        }
        if (bizCoupon.getDeleted() == 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_304025433));
        }
        if (new Date().after(bizCoupon.getEndTime())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_438123602));
        }
        //账户状态是否冻结
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectList(
                Wrappers.<BizBillingAccount>lambdaQuery()
                        .in(BizBillingAccount::getId, accountIds));
        if (CollectionUtils.isEmpty(bizBillingAccounts) || accountIds.size() != bizBillingAccounts.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        bizBillingAccounts.stream().forEach(e -> {
            if (e.getStatus().equals("freeze")){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2070382864));
            }
        });
        DescribeCouponAccountRequest describeCouponAccountRequest = new DescribeCouponAccountRequest();
        describeCouponAccountRequest.setSid(sid);
        describeCouponAccountRequest.setAccountType("all");
        IPage<DescribeCouponAccountResponse> describeCouponAccountResponseIPage = bizCouponService.listCouponAccounts(
                describeCouponAccountRequest);
        List<DescribeCouponAccountResponse> records = describeCouponAccountResponseIPage.getRecords();
        for (DescribeCouponAccountResponse record : records) {
            for (Long accountId : accountIds) {
                if (accountId.equals(record.getId())) {
                    if (DistributeStatusEnum.DISTRIBUTED.getName().equals(record.getDistributeStatus())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1496059777));
                    }
                }
            }
        }

        List<BizCouponAccount> entityList = accountIds.stream().map(id -> {
            BizCouponAccount bizCouponAccount = new BizCouponAccount();
            bizCouponAccount.setCouponSid(bizCoupon.getCouponSid());
            bizCouponAccount.setCouponNo(bizCoupon.getCouponNo());
            bizCouponAccount.setAccountId(id);
            bizCouponAccount.setCouponStatus(CouponStatusEnum.UNUSED.getCode());
            WebUserUtil.prepareInsertParams(bizCouponAccount);
            return bizCouponAccount;
        }).collect(Collectors.toList());

        boolean success = bizCouponAccountService.saveBatch(entityList);
        if (success) {

            try {
                CompletableFuture.runAsync(() -> {
                    bizBillingAccounts.forEach(bizBillingAccount -> {
                        User user1 = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
                        Map<String, String> messageContent = new HashMap<>();
                        messageContent.put("entityName",bizBillingAccount.getEntityName());
                        messageContent.put("accountType","现金账户");
                        messageContent.put("amount", BigDecimalUtil.remainTwoPointAmount(bizCoupon.getDiscountAmount()).toPlainString());
                        messageContent.put("startDate",DateUtil.format(bizCoupon.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                        messageContent.put("endDate",DateUtil.format(bizCoupon.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                        sysMsgService.sendBssMessage(user1.getUserSid(),messageContent,NotificationConsts.ConsoleMsg.FinanceMsg.TENANT_COUPON_GRANT,NotificationConsts.PlatformMsg.FinanceMsg.BSSMGT_COUPON_GRANT,bizBillingAccount.getEntityId());

                    });
                }, cloudExecutor);
            } catch (Exception e) {
                log.error("发送站内信失败", e.getMessage());
            }

            bizCoupon.setDistributeStatus(DistributeStatusEnum.DISTRIBUTED.getCode());
            this.updateById(bizCoupon);
        }

        return success;
    }
    /**
     * 发送信息
     * @param
     */
    private void sendMessage(User user, Map<String, String> messageContent, String msg1, String msg2 ,Long entityId) {

        messageContent.put("userAccount", user.getAccount());
        NotificationUtil.assembleBaseMessageContent(messageContent);

        // 系统名称
        if (msg1!=null) {
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(msg1);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.setEntityId(entityId);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setEntityId(entityId);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }catch (Exception e){
                log.error("共享资源池发送信息异常：",e.getMessage());
            }
        }
        //运营管理员发送消息

        if (msg2!=null) {
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            if(adminstrators !=null){

                try {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().map(User::getUserSid).collect(Collectors.toSet()));
                    baseNotificationMqBean.setEntityId(entityId);
                    baseNotificationMqBean.setMsgId(msg2);
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
                }catch (Exception e){
                    log.error("共享资源池发送信息异常：",e.getMessage());
                }
            }
        }
    }
    @Override
    public IPage<DescribeCouponAccountResponse> listCouponAccounts(
        DescribeCouponAccountRequest request) {

        BizCoupon bizCoupon = this.getById(request.getSid());
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if(bizCoupon == null || !authUser.getEntityId().equals(bizCoupon.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729462609));
        }
        if (ALL_ACCOUNTS.equals(request.getAccountType())) {
            List<BizBillingAccount> allAccounts = this.bizBillingAccountMapper
                    .selectByList(MapsKit.of("status", "1", "certificationStatus", "authSucceed", "entityId",
                                             RequestContextUtil.getEntityId()));
            List<BizCouponAccount> bizCouponAccounts = bizCouponAccountService.list(
                Wrappers.<BizCouponAccount>lambdaQuery()
                    .eq(BizCouponAccount::getCouponSid, request.getSid()));
            List<Long> receivedAccountIds = bizCouponAccounts.stream()
                .map(BizCouponAccount::getAccountId).collect(Collectors.toList());
            List<CouponResourceResponse> couponResourceResponses = bizCouponResourceMapper.selectOrderByAccountId(request.getSid());
            Map<Long,Long> orderIdMap = new HashMap<>(16);
            couponResourceResponses.forEach(resourceResponse -> {
                orderIdMap.put(resourceResponse.getAccountId(),resourceResponse.getOrderId());
            });
            IPage<BizBillingAccount> page = PageUtil.emptyPage();
            page.setRecords(allAccounts);
            page.setTotal(allAccounts.size());

            IPage<DescribeCouponAccountResponse> responsePage = BeanConvertUtil
                .convertPage(page, DescribeCouponAccountResponse.class);
            responsePage.getRecords().forEach(data -> {
                data.setOrderId(orderIdMap.get(data.getId()));
                if (receivedAccountIds.contains(data.getId())) {
                    data.setDistributeStatus(DistributeStatusEnum.DISTRIBUTED.getName());
                } else {
                    data.setDistributeStatus(DistributeStatusEnum.UNDISTRIBUTED.getName());
                }
                if(Objects.nonNull(data.getCouponStatus())){
                    data.setCouponStatus(EnumUtil.likeValueOf(CouponStatusEnum.class,data.getCouponStatus()).getAlia());
                }
            });
            // 去除流程审批中的数据
            RestResult process = cashCouponFeignService.getProcessList(String.valueOf(request.getSid()), "coupons-distribute");
            if (!ObjectUtils.isEmpty(process.getData())) {
                Arrays.asList(process.getData().toString().split(",")).forEach(accountId -> {
                    responsePage.setRecords(responsePage.getRecords().stream().filter(r -> !r.getId().equals(Long.valueOf(accountId))).collect(Collectors.toList()));
                });
            }
            responsePage.setTotal(responsePage.getRecords().size());

            return responsePage;
        } else {
            return getReceivedCouponAccountPage(request);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCouponByLogic(Long sid,DescribeCouponDelRequest describeCouponDelRequest) {
        boolean retBol = false;
        Long entityId = RequestContextUtil.getEntityId();
        BizCoupon bizCoupon = this.getById(sid);
        if(!entityId.equals(bizCoupon.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_729462609));
        }
        if (Objects.isNull(bizCoupon)) {
            log.info("优惠劵不存在,sid=[{}]", sid);
            return true;
        }
        List<Long> accountIdList = describeCouponDelRequest.getAccountIdList();
        if (CollectionUtil.isEmpty(accountIdList) && Objects.equals(bizCoupon.getDistributeStatus(),"distributed")){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1697650142));
        }
        if (bizCoupon.getDeleted() == 1){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1941166630));
        }
        if (CollectionUtil.isNotEmpty(accountIdList)) {
            List<BizCouponAccount> list = bizCouponAccountService.lambdaQuery().eq(BizCouponAccount::getCouponSid, sid)
                .in(BizCouponAccount::getAccountId, accountIdList)
                .eq(BizCouponAccount::getCouponStatus, CouponStatusEnum.UNUSED.getCode()).list();
            if (CollectionUtils.isEmpty(list)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1219130789));
            }
            list.stream().forEach(couponAccount -> {
                couponAccount.setCouponStatus(CouponStatusEnum.DELETED.getCode());
                couponAccount.setRemark(describeCouponDelRequest.getRemark());
            });
            retBol = bizCouponAccountService.updateBatchById(list);
        } else {
            bizCoupon.setDeleted(1);
            bizCoupon.setRemark(describeCouponDelRequest.getRemark());
            retBol = this.updateById(bizCoupon);
        }
        return retBol;
    }

    @Override
    public BizCoupon getCoupon(Long id) {
        return this.getById(id);
    }

    private IPage<DescribeCouponAccountResponse> getReceivedCouponAccountPage(
        DescribeCouponAccountRequest request) {
        LambdaQueryWrapper<BizCouponAccount> couponAccountQuery = Wrappers.lambdaQuery();
        List<BizCouponAccount> bizCouponAccounts = bizCouponAccountService
            .list(couponAccountQuery.eq(BizCouponAccount::getCouponSid, request.getSid()));

        List<Long> accountIds = bizCouponAccounts.stream().map(BizCouponAccount::getAccountId)
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(accountIds)) {
            Page<BizBillingAccount> pageParams = PageUtil.preparePageParams(request);
            List<CouponResourceResponse> couponResourceResponses = bizCouponResourceMapper.selectOrderByAccountId(request.getSid());
            Map<Long,Long> orderIdMap = new HashMap<>(16);
            couponResourceResponses.forEach(resourceResponse -> {
                orderIdMap.put(resourceResponse.getAccountId(),resourceResponse.getOrderId());
            });
            Map<String,Object> conditionMap = new HashMap<>();
            conditionMap.put("accountIds",accountIds);
            conditionMap.put("accountNameLike",request.getAccountNameLike());
            IPage<BizBillingAccount> accountPage = this.bizBillingAccountMapper
                .selectByParams(pageParams, conditionMap);

            IPage<DescribeCouponAccountResponse> responsePage = BeanConvertUtil
                .convertPage(accountPage, DescribeCouponAccountResponse.class);
            responsePage.getRecords().forEach(data -> {
                BizCouponAccount one = CollectionUtil.findOne(bizCouponAccounts,
                    bizCouponAccount -> Objects
                        .equals(bizCouponAccount.getAccountId(), data.getId()));
                if (Objects.nonNull(one)) {
                    data.setOrderId(orderIdMap.get(data.getId()));
                    Optional.ofNullable(
                        EnumUtil.likeValueOf(CouponStatusEnum.class, one.getCouponStatus()))
                        .ifPresent(
                            couponStatusEnum -> data.setCouponStatus(couponStatusEnum.getAlia()));
                }
            });

            return responsePage;
        }

        return PageUtil.emptyPage();
    }
}
