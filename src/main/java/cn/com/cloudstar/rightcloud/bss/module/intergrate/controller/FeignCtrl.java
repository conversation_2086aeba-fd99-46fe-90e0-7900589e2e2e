/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.controller;


import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.DescribeOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.*;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CD;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;
import com.google.common.net.HttpHeaders;

import cn.hutool.core.collection.CollectionUtil;
import feign.Response;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 数据字典控制器
 *
 * <AUTHOR>
 * @date 2023/01/14
 */
@RestController
@RequestMapping()
public class FeignCtrl {

    @Autowired
    private FeignService feignService;

    @Autowired
    private SysMFilePathMapper sysMFilePathMapper;

    @Autowired
    private OrgService orgService;


    /**
     * 根据数据字典类型查询数据字典数据
     *
     * @param codeCategory                 类别代码
     * @param statusAll                    状态都
     * @param codeValue                    代码值
     * @param excludeCodeValue             排除代码值
     * @param includeDedicatedResourcePool 包括专用资源池
     * @param mark                         马克
     * @return {@link List}<{@link DescribeCodeFullResponse}>
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C102)
    @RequestMapping(value = "/code/{codeCategory}", method = RequestMethod.GET)
    @ApiOperation(httpMethod = "GET", value = "按类别获取数据字典", notes = "通过codeCategory获取数据字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeCategory", value = "数据字典类别", dataType = "string", paramType = "path", required = true),
            @ApiImplicitParam(name = "statusAll", value = "是否查询所有状态", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "codeValue", value = "数据字典值", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "includeDedicatedResourcePool", value = "是否查询出专属资源池", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "mark", value = "查询ModelArts", dataType = "string", paramType = "query")

    })

    public RestResult<List<DescribeCodeFullResponse>> getCodesByCategory(@PathVariable("codeCategory") String codeCategory,
                                                                         @RequestParam(value = "statusAll", required = false) boolean statusAll,
                                                                         @RequestParam(value = "codeValue", required = false) String codeValue,
                                                                         @RequestParam(value = "excludeCodeValue", required = false) String excludeCodeValue,
                                                                         @RequestParam(value = "includeDedicatedResourcePool", defaultValue = "false") boolean includeDedicatedResourcePool,
                                                                         @RequestParam(value = "mark", defaultValue = "false") String mark) {
        return feignService.getCodesByCategoryByFeign(codeCategory,statusAll,codeValue,excludeCodeValue,includeDedicatedResourcePool,mark);
    }


    /**
     * 显示所有信息
     *
     * @param request 请求
     * @return {@code RestResult<List<DescribeSysConfigResponse>>}
     */
    @AuthorizeBss(action = AuthModule.CA.CA)
    @GetMapping("/configs")
    @ApiOperation(httpMethod = "GET", value = "显示所有信息")
    public RestResult<List<DescribeSysConfigResponse>> displayAllMessageByFeign(@Validated DescribeSysConfigRequest request) {
        if (!"company_auth".equals(request.getConfigType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return feignService.displayAllMessageByFeign(request);
    }

    /**
     * 显示图片(zip)
     *
     * @param response 响应
     * @param fileId   文件标识
     * @throws BizException 业务异常
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C104)
    @ApiOperation(httpMethod = "GET", value = "显示图片(zip)", notes = "显示图片(zip)")
    @GetMapping("/upload/image/zip")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'图片显示(zip)'", bizId = "#fileId", resource = OperationResourceEnum.PICTURE_DISPLAY, tagNameUs ="'Picture display'")
    public void downloadFile(HttpServletResponse response, @RequestParam String fileId) throws BizException {
        try {
            SysMFilePath filePath = sysMFilePathMapper.selectByPrimaryKey(fileId);
            if (Objects.isNull(filePath)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            }
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (ObjectUtils.isEmpty(authUserInfo)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }

            if (!filePath.getAccountId().equals(authUserInfo.getUserSid())) {
                Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());
                if (!CertificationStatus.AUTHSUCCEED.equals(org.getCertificationStatus()) || !filePath.getAccountId().equals(authUserInfo.getParentSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.xlsx'").format(new Date());
            response.setContentType("application/octet-stream");
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
            response.setHeader("Content-Disposition",
                    "attachment;" + "filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadFile(fileId),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 身份验证公司信息通过id
     *
     * @param companyId 公司标识
     *
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C104)
    @ApiOperation(httpMethod = "GET", value = "获取企业认证信息", notes = "通过企业id获取企业认证信息")
    @GetMapping("/users/getAuthCompanyInfoById/{companyId}")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'企业认证信息'", bizId = "#companyId", resource = OperationResourceEnum.COMPANY_UDIT, tagNameUs ="'Enterprise certification information'")
    public RestResult getAuthCompanyInfoById(@PathVariable Long companyId) {
       return feignService.getAuthCompanyInfoById(companyId);
    }

    /**
     * 查询流程处理记录
     *【Since v2.5.0】
     * @param orderId 订单id
     * @return {@link ProcessHistoryRecordResponse}
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C109)
    @ApiOperation(httpMethod = "GET", value = "查询流程处理记录", notes = "查询流程处理记录")
    public RestResult<ProcessHistoryRecordResponse> getHistoryRecordByFeign(
            @ApiParam(name = "orderId", value = "订单ID", required = true) @PathVariable String orderId) {
        return feignService.getHistoryRecordByFeign(orderId);
    }

    /**
     * 重置用户密码(用户管理)
     *
     * @param request 请求
     * @return {@link cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = " 重置用户密码")
    @PutMapping("/users/password/reset")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'重置用户密码'", bizId = "#request.userIds", resource = OperationResourceEnum.RESET_USER_PASSWORD, tagNameUs ="'Reset User Password'")
    @Encrypt
    @AuthorizeBss(action = AuthModule.CH.CH0108)
    public RestResult resetUserPassword(@RequestBody @Valid ResetPwdRequest request) {
        return feignService.resetUserPassword(request);
    }

    /**
     * 修改个人信息
     *
     * @param request 请求
     * @return {@link cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = " 个人信息-修改")
    @PutMapping("/users/self")
   // @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'个人信息-修改'", bizId = "#request.userSid", resource = OperationResourceEnum.UPDATE_COMPANY_USER)
    @Encrypt
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C112)
    public RestResult updateUserBySelf(@RequestBody @Valid UpdateCompanyUserRequest request) {
        return feignService.updateUserBySelf(request);
    }

    /**
     * 批量获取配置(租户控制台)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/configs/multi")
    @ApiOperation(httpMethod = "GET", value = "批量获取配置", notes = "通过传入configType列表获取配置")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C110)
    public RestResult<List<DescribeSysConfigResponse>> getConfigsByTypeList(@Validated DescribeSysConfigByTypeListRequest request) {
        return feignService.getConfigsByTypeList(request);
    }

    /**
     * 查询订单中的金额详情
     *
     * @param orderId    订单id
     * @param chargeType 费用类型
     * @return {@link List}<{@link DescribeAmountDetailResponse}>
     */
    @GetMapping("/audit/approval/amount/detail")
    @ApiOperation(httpMethod = "GET", value = "查询订单中的金额详情", notes = "查询订单中的金额详情")
    @AuthorizeBss(action = AuthModule.CC.CC0101)
    public RestResult<List<DescribeAmountDetailResponse>> getOrderAmountDetail(
            @ApiParam("订单id") @RequestParam(value = "orderId") Long orderId,
            @ApiParam("付费类型") @RequestParam(value = "chargeType") String chargeType) {
        return feignService.getOrderAmountDetail(orderId, chargeType);
    }

    /**
     * 得到类别
     *
     * @return {@link List}<{@link DescribeTicketCategoryResponse}>
     */
    @AuthorizeBss(action = AuthModule.CD.CD01)
    @GetMapping("/ticket/category")
    @ApiOperation("获取工单的分类")
    public RestResult<List<DescribeTicketCategoryResponse>> getCategory() {
        return feignService.getCategory();
    }



    /**
     * 导出订单列表
     *
     * @param describeOrderRequest 描述订单请求
     * @param request              请求
     * @return {@link cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "导出订单列表")
    @GetMapping("/audit/approval/asyn/export")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'订单列表'", resource = OperationResourceEnum.EXPORT_ORDER_LIST, tagNameUs ="'Order List'")
    @AuthorizeBss(action = AuthModule.CC.CC0101)
    public RestResult expertOrderList(DescribeOrderRequest describeOrderRequest,
                                      HttpServletRequest request) {
        return feignService.expertOrderList(describeOrderRequest);
    }

    /**
     * 获取配置信息
     */
    @GetMapping("/sys_config/config_data")
    @ApiOperation(httpMethod = "GET", value = "获取配置信息", notes = "通过configKeys获取配置信息")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C104 + AuthModule.COMMA + CC.CC0304 + AuthModule.COMMA + CD.CD02)
    public RestResult<List<DescribeSysConfigResponse>> getConfigData(@ApiParam("多个配置Key,以','分割") @RequestParam(value = "configKeys", required = false) String configKeys) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo.getParentSid()) && "letter.template".equals(configKeys)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_465687352));
        }
        String cacheAuth = JedisUtil.INSTANCE.get(AuthConstants.USER_PERMISSION_CACHE + authUserInfo.getUserSid());
        if ("cue.show.or.hide".equals(configKeys) && !cacheAuth.contains(CC.CC0304)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_465687352));
        }
        if ("complaint.file".equals(configKeys) && !cacheAuth.contains(CD.CD02)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_465687352));
        }

        return feignService.getConfigData(null, null, configKeys);
    }

    /**
     * 校验原密码
     *
     * @param request 请求
     *
     * @return {@link cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult}
     */
    @ApiOperation("校验原密码")
    @PostMapping("/users/login")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "#request.account", resource = OperationResourceEnum.CHECK_PASSWORD)
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C112)
    public RestResult checkPassword(@RequestBody @Valid CheckPasswordRequest request) {
        return feignService.checkPassword(request);
    }

    // feign 调用流转换
    public void transformation(Response fileInputStream, HttpServletResponse response){
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = fileInputStream.body().asInputStream();
            outputStream = response.getOutputStream();
            byte[] bytes = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
            fileInputStream.close();
            outputStream.close();
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(outputStream);
        }
    }
}
