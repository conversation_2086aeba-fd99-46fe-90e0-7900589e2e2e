/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.sfs.service.impl;

import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfServiceProcessRelaMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.CloudEnvEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Policy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribePolicyResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.UserGroupMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBillingStrategyServingResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingServingConfigService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyServingService;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterPoolRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.enums.CategoryTypeEnum;
import cn.com.cloudstar.rightcloud.bss.module.sfs.enums.ProductTemplateStatusEnum;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.BizTagMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductCategoryCatalogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateRelationMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.CreateProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.DescribeProductCategoryRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.DescribeProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.RelatedProductTemplatesRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.UpdateProductStatusRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductCategoryResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeProductTemplateResponse;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response.DescribeServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfProductCategoryCatalogService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfProductTemplateService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ISfServiceCatalogRelationService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ServiceManage;
import cn.com.cloudstar.rightcloud.oss.common.enums.ClusterTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <p>
 * 产品类别 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-01
 */
@Service
@Slf4j
public class SfProductCategoryCatalogServiceImpl extends
        ServiceImpl<SfProductCategoryCatalogMapper, SfProductCategoryCatalog> implements
        ISfProductCategoryCatalogService {

    /**
     *  不需要定价的产品
     */
    private static final String DESC = "desc";
    private static final String ASC = "asc";
    private static final String CONSOLE = "console";
    private static final String ACTIVE = "ACTIVE";
    // 下架
    private static final String NOUSING = "nousing";
    /**
     * 状态：启用
     */
    private static final String ONE = "1";
    /**
     * 内置组件
     */
    private static final String BUILT_IN_COMPONENTS = "内置组件";
    /**
     * 自定义服务
     */
    private static final String CUSTOM_SERVICE = "自定义服务";
    /**
     * 未关联
     */
    private static final String NOT_ASSOCIATED = "0";
    /**
     * 已关联
     */
    private static final String ASSOCIATED = "1";
    @Autowired
    private ISfServiceCatalogRelationService catalogRelationService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private SfProductCategoryCatalogMapper sfProductCategoryCatalogMapper;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private SfProductTemplateRelationMapper productTemplateRelationMapper;

    @Autowired
    private ISfProductTemplateService productTemplateService;

    @Autowired
    private BizBillingStrategyServingService servingService;

    @Autowired
    private BizBillingServingConfigService bizBillingServingConfigService;

    @Autowired
    private CodeMapper codeMapper;

    @Autowired
    private HpcClusterService hpcClusterService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private UserGroupMapper userGroupMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private SfServiceProcessRelaMapper sfServiceProcessRelaMapper;

    @Autowired
    private BizTagMapper bizTagMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String TAG_RANKE = "tag:range";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCategory(String name, List<Long> productIds) {
        // 服务类型合法性判定
        serviceCategoryValidation(null, productIds);
        SfProductCategoryCatalog category = new SfProductCategoryCatalog();
        category.setCategoryName(name);
        category.setCategoryType(CategoryTypeEnum.CUSTOM.getCode());
        WebUserUtil.prepareInsertParams(category);
        return save(category) && updateRelations(category.getId(), productIds, false,null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean CategoryNameVerify(String name) {
        LambdaQueryWrapper<SfProductCategoryCatalog> query = Wrappers.<SfProductCategoryCatalog>lambdaQuery()
                .eq(SfProductCategoryCatalog::getCategoryName,
                        name);
        if (baseMapper.selectCount(query) > 0) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(Long categoryId, String name, List<Long> productIds,String entitySid) {
        SfProductCategoryCatalog category = getById(categoryId);
        if (Objects.isNull(category)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1230772179));
        }
        // 服务类型合法性判定
        serviceCategoryValidation(categoryId, productIds);
        category.setCategoryName(name);
        return updateById(category) && updateRelations(categoryId, productIds, true,entitySid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long categoryId) {
        return removeById(categoryId) && updateRelations(categoryId, null, true,null);
    }

    @Override
    public IPage<DescribeProductCategoryResponse> listCategories(
            DescribeProductCategoryRequest request) {
        String sortorder = "";
        if (("products").equals(request.getSortdatafield())) {
            request.setSortdatafield(null);
        }
        if (Objects.nonNull(request.getSortorder())) {
            sortorder = request.getSortorder();
            request.setSortorder(null);
        }
        /* 转化请求消息*/
        Page<SfProductCategoryCatalog> pageParams = getPageParams(request, null, null);
        /* 获取信息并设置查询排序*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        IPage<SfProductCategoryCatalog> categoryPage;
        if (!PageUtil.isPageQuery(request)) {
            List<SfProductCategoryCatalog> catalogs = sfProductCategoryCatalogMapper.sfProductCategoryCatalogList(criteria.getCondition());
            categoryPage = PageUtil.emptyPage();
            categoryPage.setTotal(catalogs.size());
            categoryPage.setRecords(catalogs);
        } else {
            IPage<SfProductCategoryCatalog>  iPage = PageUtil.preparePageParams(request);
            categoryPage = sfProductCategoryCatalogMapper.pageSfProductCategoryCatalog(iPage, criteria.getCondition());
        }

        /* 转化返回结果*/
        IPage<DescribeProductCategoryResponse> responsePage = BeanConvertUtil
                .convertPage(categoryPage, DescribeProductCategoryResponse.class);
        for (DescribeProductCategoryResponse data : responsePage.getRecords()) {
            List<Long> productIds = getProductIds(data.getId());
            if (CollectionUtil.isNotEmpty(productIds)) {
                LambdaQueryWrapper<ServiceCategory> query = getQueryWrapper(null, productIds);
                List<ServiceCategory> products = serviceCategoryService.list(query);
                filterProductsOnLicense(products);
                if (("aesc").equals(sortorder)) {
                    Collections.reverse(products);
                }
                data.setProducts(BeanConvertUtil.convert(products, DescribeProductResponse.class));
            }
        }
        return responsePage;
    }

    /**
     * 标准许可证不包含HPC、SFS
     *
     * @param products
     */
    private void filterProductsOnLicense(List<ServiceCategory> products) {
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        String versionType = licenseVo.getVersionType();
        if (!LicenseUtil.STAND.equalsIgnoreCase(versionType)) {
            return;
        }
        for (int i = products.size() - 1; i >= 0; i--) {
            ServiceCategory serviceCategory = products.get(i);
            if (ProductCodeEnum.HPC.getProductType().equalsIgnoreCase(serviceCategory.getServiceType())
                    || ProductCodeEnum.SFS.getProductType().equalsIgnoreCase(serviceCategory.getServiceType())) {
                products.remove(i);
            }
        }
    }

    @Override
    public IPage<DescribeProductResponse> listProducts(DescribeProductRequest request) {
        Criteria criteria = new Criteria();
        IPage<ServiceCategory>  iPage = PageUtil.preparePageParams(request);
        criteria.put("publishStatus","succeed");
        if(Objects.nonNull(request.getCategoryId())){
            List<Long> productIds = getProductIds(request.getCategoryId());
            if (CollectionUtil.isEmpty(productIds)) {
                return new Page<>();
            }
            if (CollectionUtil.isNotEmpty(productIds)){
                criteria.put("productIds",productIds);
            }
            if(Objects.nonNull(request.getProductNameLike())){
                criteria.put("productNameLike",request.getProductNameLike());
        }

        }else{
            if(Objects.nonNull(request.getProductNameLike())){
                criteria.put("productNameLike",request.getProductNameLike());
            }
        }

        if (StrUtil.isNotBlank(request.getServiceComponent())) {
            criteria.put("serviceComponent",request.getServiceComponent());
        }
        if (StrUtil.isNotBlank(request.getProductCode())) {
            criteria.put("productCode",request.getProductCode());
        }
        /* 检查状体查找消息*/
        if (StrUtil.isNotBlank(request.getStatus())) {
            criteria.put("status",request.getStatus());
        }
        if (StrUtil.isNotBlank(request.getTagNameLike())) {
            criteria.put("tagNameLike", request.getTagNameLike());
        }
        //当没有传pagesize时部分由
        if (request.getPagesize() == null) {
            iPage.setSize(-1L);
        }
        criteria.put("status",request.getStatus());
        // 不查询许可证未授权的产品
        criteria.put("neqStatus", CouponStatusEnum.UNAUTH.getCode());
        /* 分页查找*/
        IPage<ServiceCategory> page = serviceCategoryMapper.pageServiceCategory(iPage,criteria.getCondition());
        /* 转化为返回需求类型*/
        IPage<DescribeProductResponse> responsePage = BeanConvertUtil
                .convertPage(page, DescribeProductResponse.class);

        /* 填充产品类别消息*/
        if (PageUtil.isPageQuery(request)) {
            Map<String, Long> codeToProcessId = new HashMap<>();
            List<SfServiceProcessRela> processRelas = sfServiceProcessRelaMapper.selectAll();
            if (CollectionUtil.isNotEmpty(processRelas)) {
                codeToProcessId = processRelas.stream().collect(
                        Collectors.toMap(SfServiceProcessRela::getServiceCode, SfServiceProcessRela::getProcessId));
            }

            Map<String, Long> finalCodeToProcessId = codeToProcessId;
            responsePage.getRecords().forEach(data -> {
                data.setProcessId(finalCodeToProcessId.get(data.getServiceType()));
                List<Long> categoryIds = catalogRelationService.list(
                                Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                                        .eq(SfServiceCatalogRelation::getServiceId, data.getId())).stream()
                        .map(SfServiceCatalogRelation::getCatalogId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(categoryIds)) {
                    List<SfProductCategoryCatalog> categories = this.list(
                            Wrappers.<SfProductCategoryCatalog>lambdaQuery()
                                    .in(SfProductCategoryCatalog::getId, categoryIds));
                    data.setProductCategories(
                            categories.stream().map(SfProductCategoryCatalog::getCategoryName)
                                    .collect(Collectors.joining(StrUtil.COMMA)));
                }
                List<Long> templateIds = productTemplateRelationMapper.selectList(
                                Wrappers.<SfProductTemplateRelation>lambdaQuery()
                                        .eq(SfProductTemplateRelation::getProductId, data.getId())).stream()
                        .map(SfProductTemplateRelation::getTemplateId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(templateIds)) {
                    data.setTemplateList(BeanConvertUtil.convert(productTemplateService.list(
                                    Wrappers.<SfProductTemplate>lambdaQuery()
                                            .in(SfProductTemplate::getId, templateIds)),
                            DescribeProductTemplateResponse.class));
                }
                // 查询已关联标签
                List<TagDao> useTags = bizTagMapper.getTags(data.getId());
                data.setUseTagList(useTags);
            });
        }

        return responsePage;
    }

    @Override
    public boolean updateProduct(UpdateProductRequest request) {
        Date current = new Date();
        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getId());
        isProductExist(serviceCategory);
        if (!serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        if (CollectionUtil.isNotEmpty(request.getTemplateIds())) {
            request.getTemplateIds().stream().forEach(templateId -> {
                SfProductTemplate sfProductTemplate = productTemplateService.getById(templateId);
                if (Objects.nonNull(sfProductTemplate) && !sfProductTemplate.getEntityId().equals(RequestContextUtil.getEntityId())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
                }
            });
        }

        if (isProductCodeExist(request.getProductCode(), request.getId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE_EXIST));
        }

        if (!ServiceCategoryStatus.NOUSING.equals(serviceCategory.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_USING));
        }

        serviceCategory.setProductName(request.getProductName());
        if (serviceCategory.getPublishDt() == null) {
            serviceCategory.setProductCode(request.getProductCode());
            serviceCategory.setShowType(request.getShowType());
            serviceCategory.setShowTypeId(request.getShowTypeId());
        }
        serviceCategory.setProductDesc(request.getProductDesc());
        serviceCategory.setServiceIconPath(request.getServiceIconPath());

        serviceCategory.setUpdatedDt(current);
        serviceCategory.setUpdatedBy(AuthUtil.getAuthUser().getAccount());
        return serviceCategoryService.updateById(serviceCategory);
    }

    private boolean canNotUpdateToUsing(ServiceCategory serviceCategory, String status) {
        if (StrUtil.isEmpty(status)) {
            return false;
        }
        boolean notInnerServiceOrNotToUsing = !ServiceCategoryStatus.USING.equals(status);
        if (notInnerServiceOrNotToUsing) {
            return false;
        }
        List<String> templateProducts = Arrays.asList(ProductCodeEnum.HPC.getProductType(), ProductCodeEnum.HPC_DRP.getProductType()
                , ProductCodeEnum.MODELARTS.getProductType(), ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
        if (!templateProducts.contains(serviceCategory.getProductCode())) {
            return false;
        }
        List<SfProductTemplateRelation> sfProductTemplateRelations = productTemplateRelationMapper
                .selectList(Wrappers.<SfProductTemplateRelation>lambdaQuery()
                        .eq(SfProductTemplateRelation::getProductId, serviceCategory.getId()));
        if (sfProductTemplateRelations.isEmpty()) {
            return true;
        }
        List<SfProductTemplate> templates = productTemplateService.list(
                Wrappers.<SfProductTemplate>lambdaQuery().in(SfProductTemplate::getId,
                        sfProductTemplateRelations.stream().map(SfProductTemplateRelation::getTemplateId)
                                .collect(Collectors.toList())));
        return CollectionUtils.isEmpty(templates) || templates.stream().anyMatch(
                template -> ProductTemplateStatusEnum.DISABLE.getCode().equals(template.getStatus()));
    }

    private List<Long> getProductIds(Long categoryId) {
        List<SfServiceCatalogRelation> relations = catalogRelationService.list(
                Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                        .eq(SfServiceCatalogRelation::getCatalogId, categoryId));
        return relations.stream().map(SfServiceCatalogRelation::getServiceId)
                .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<ServiceCategory> getQueryWrapper(String productNameLike,
                                                                List<Long> productIds) {
        LambdaQueryWrapper<ServiceCategory> query = Wrappers.<ServiceCategory>lambdaQuery()
                .eq(ServiceCategory::getPublishStatus, "succeed");
        if (CollectionUtil.isNotEmpty(productIds)) {
            query.in(ServiceCategory::getId, productIds);
        }
        if (Objects.nonNull(productNameLike)) {
            query.like(ServiceCategory::getProductName, productNameLike);
        }
        if(Objects.nonNull(RequestContextUtil.getEntityId())){
            query.eq(ServiceCategory::getEntityId, RequestContextUtil.getEntityId());
        }
        return query;
    }

    /* 将请求消息转化为分页获取工具类*/
    private <T> Page<T> getPageParams(BaseRequest request, String defaultField,
                                      String defaultOrderByClause) {
        Page<T> page = PageUtil.isPageQuery(request) ? PageUtil.preparePageParams(request)
                : new Page<>(Integer.MIN_VALUE, Integer.MAX_VALUE);
        if (Objects.nonNull(defaultField)) {
            if (DESC.equals(defaultOrderByClause)) {
                page.setDesc(defaultField);
            } else if (ASC.equals(defaultOrderByClause)) {
                page.setAsc(defaultField);
            }
        }
        return page;
    }

    private boolean updateRelations(Long categoryId, List<Long> productIds, boolean isDeleteOld,String entitySid) {
        boolean retBol = true;
        List<Long> serviceIds=new ArrayList<>();
        //获取运营实体ID
        if(Objects.nonNull(getEntityId())){
            serviceIds=catalogRelationService.findServiceIdByCategory(categoryId,getEntityId());
        }
        if (isDeleteOld) {
            //清除掉原先关联的产品ID
            if(serviceIds.size()>0){
                serviceIds.stream().forEach(productId->{
            catalogRelationService.remove(Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                                                          .eq(SfServiceCatalogRelation::getCatalogId, categoryId).eq(SfServiceCatalogRelation::getServiceId,productId));
                });
            } else {
                if(Objects.nonNull(productIds) && productIds.size()>0){
                    productIds.stream().forEach(productId->{
                        catalogRelationService.remove(Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                                                              .eq(SfServiceCatalogRelation::getCatalogId, categoryId).eq(SfServiceCatalogRelation::getServiceId,productId));
                    });
                }
            }
        }

        if (CollectionUtil.isNotEmpty(productIds)) {
            List<SfServiceCatalogRelation> relations = productIds.stream().map(id -> {
                SfServiceCatalogRelation relation = new SfServiceCatalogRelation();
                relation.setCatalogId(categoryId);
                relation.setServiceId(id);
                return relation;
            }).collect(Collectors.toList());
            retBol = catalogRelationService.saveBatch(relations);
        }

        return retBol;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean relatedProductTemplate(RelatedProductTemplatesRequest request) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getProductId());
        isProductExist(serviceCategory);
        if (!serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }

        if (!NOUSING.equals(serviceCategory.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_USING));
        }

        List<Long> requestTemplateIds = request.getTemplateIds();
        if (CollectionUtil.isEmpty(requestTemplateIds)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        LambdaQueryWrapper<SfProductTemplate> qw = new LambdaQueryWrapper<>();
        qw.in(SfProductTemplate::getId, requestTemplateIds);
        List<SfProductTemplate> list = productTemplateService.list(qw);
        if (CollectionUtils.isEmpty(list) || list.size() != requestTemplateIds.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        QueryWrapper<SfProductTemplate> tWrapper = new QueryWrapper<>();
        tWrapper.eq("id", requestTemplateIds.get(0));
        SfProductTemplate template = productTemplateService.getOne(tWrapper);
        if (template == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_TEMPLATE_NO_EXIST));
        }


        if (!StringUtils.equals(template.getTemplateType(), serviceCategory.getServiceType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String templateType = template.getTemplateType();
        if (!serviceCategory.getServiceType().equals(templateType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_342329630));
        }
        if (StringUtils.isEmpty(serviceCategory.getShowType()) && StringUtils.isNotEmpty(templateType)) {
            serviceCategory.setShowType(templateType);
            serviceCategory.setShowTypeId(request.getShowTypeId());
            serviceCategory.setServiceType(templateType);
            serviceCategory.setServiceForm(templateType);

            serviceCategoryService.updateById(serviceCategory);
        }

        productTemplateRelationMapper.delete(Wrappers.<SfProductTemplateRelation>lambdaQuery()
                .eq(SfProductTemplateRelation::getProductId, serviceCategory.getId()));
        List<Long> templateIds = requestTemplateIds;
        templateIds.forEach(id -> {
            SfProductTemplateRelation relation = new SfProductTemplateRelation();
            relation.setProductId(serviceCategory.getId());
            relation.setTemplateId(id);
            productTemplateRelationMapper.insert(relation);
        });
        return true;
    }

    /**
     * 产品是否存在
     *
     * @param serviceCategory
     */
    private void isProductExist(ServiceCategory serviceCategory) {
        if (serviceCategory == null || ServiceCategoryStatus.DELETE.equals(serviceCategory.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NOT_EXIST));
        }
    }

    /**
     * 创建产品
     *
     * @param request
     * @return
     */
    @Override
    public Long createProduct(CreateProductRequest request) {

        String productCode = request.getProductCode();
        if (isProductCodeExist(productCode, null)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_VALID_PRODUCT_CODE_EXIST));
        }

        Date current = new Date();

        User authUser = AuthUtil.getAuthUser();

        if (StringUtil.isNotBlank(request.getShowType()) && !ServiceManage.list.contains(request.getShowType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_986078609));
        }

        ServiceCategory serviceCategory = new ServiceCategory();
        serviceCategory.setServiceForm(request.getShowType());
        serviceCategory.setServiceType(request.getShowType());

        serviceCategory.setServiceName(request.getProductName());
        serviceCategory.setServiceClass("public");
        serviceCategory.setProductDesc(request.getProductDesc());
        serviceCategory.setStatus(NOUSING);

        serviceCategory.setProductCode(productCode);

        serviceCategory.setServiceOwnerId(authUser.getUserSid());
        serviceCategory.setServiceOwnerName(authUser.getAccount());
        serviceCategory.setServiceIconPath(request.getServiceIconPath());
        serviceCategory.setOpenType("manual");
        serviceCategory.setServiceComponent("sefService");
        serviceCategory.setPublishStatus("succeed");
        serviceCategory.setProductName(request.getProductName());
        serviceCategory.setShowType(request.getShowType());
        serviceCategory.setShowTypeId(request.getShowTypeId());

        serviceCategory.setCreatedDt(current);
        serviceCategory.setCreatedBy(authUser.getAccount());
        serviceCategory.setUpdatedDt(current);
        serviceCategory.setUpdatedBy(authUser.getAccount());
        //多运营实体ID
        serviceCategory.setEntityId(RequestContextUtil.getEntityId());
        serviceCategoryService.save(serviceCategory);

        List<Long> templateIds = request.getTemplateIds();
        if (!CollectionUtil.isEmpty(templateIds)) {
            templateIds.stream().filter(Objects::nonNull).forEach(id -> {
                SfProductTemplate template = productTemplateService.getById(id);
                if (Objects.isNull(template)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_130599775));
                }
                SfProductTemplateRelation relation = new SfProductTemplateRelation();
                relation.setProductId(serviceCategory.getId());
                relation.setTemplateId(id);
                productTemplateRelationMapper.insert(relation);
            });
        }
        return serviceCategory.getId();
    }

    /**
     * 删除产品
     *
     * @param productId
     * @return
     */
    @Override
    public void deleteProduct(Long productId) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(productId);
        if (serviceCategory != null) {
            Date publishDt = serviceCategory.getPublishDt();
            //未上架过，做物理删除
            if (publishDt == null) {
                productTemplateRelationMapper.delete(Wrappers.<SfProductTemplateRelation>lambdaQuery()
                        .eq(SfProductTemplateRelation::getProductId, serviceCategory.getId()));

                catalogRelationService.remove(Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                        .eq(SfServiceCatalogRelation::getServiceId, serviceCategory.getId()));

                serviceCategoryService.removeById(productId);

            } else {

                serviceCategory.setStatus(ServiceCategoryStatus.DELETE);
                serviceCategory.setPublishStatus(ServiceCategoryStatus.DELETE);
                serviceCategoryService.updateById(serviceCategory);
            }


        }
    }

    /**
     * 更新产品状态（上下架）
     *
     * @param request
     * @return
     */
    @Override
    public boolean updateProductStatus(UpdateProductStatusRequest request) {
        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getId());
        isProductExist(serviceCategory);
        if (!serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }

        String status = request.getStatus();

        // 在这里判断上架和下架不能重复操作
        if (serviceCategory.getStatus().equals(status)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1404182793));
        }

        Date current = new Date();

        if (ServiceCategoryStatus.USING.equals(status) && !checkCataLog(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_CATALOG));
        }

        // 上架检查
        if (ServiceCategoryStatus.USING.equals(status)
                && checkRequirePrice(serviceCategory.getServiceType())) {
            if (!productResourcesPricingCheck(serviceCategory.getId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_PRICE));
            }
            // HPC上架前需要判断SFS产品
            if (ProductCodeEnum.HPC.getProductType().equals(serviceCategory.getServiceType())) {
                // SFS产品价格检查
                QueryWrapper<ServiceCategory> sfsSpecQuery = new QueryWrapper<>();
                sfsSpecQuery.lambda().in(ServiceCategory::getServiceType, ProductCodeEnum.SFS.getProductType());
                List<ServiceCategory> sfsServiceCategory = serviceCategoryService.list(sfsSpecQuery);
                if (CollectionUtil.isEmpty(sfsServiceCategory)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_SFS));
                }

                if (!productResourcesPricingCheck(sfsServiceCategory.get(0).getId())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_SFS_NO_PRICE));
                }
                // ModelArts上架前需要判断OBS产品和EVS产品
            }
            else if (ProductCodeEnum.MODELARTS.getProductType().equals(serviceCategory.getServiceType())) {
                // ModelArts产品价格检查
                QueryWrapper<ServiceCategory> obsSpecQuery = new QueryWrapper<>();
                obsSpecQuery.lambda().in(ServiceCategory::getServiceType, ProductCodeEnum.OBS.getProductType());
                List<ServiceCategory> obsServiceCategory = serviceCategoryService.list(obsSpecQuery);
                if (CollectionUtil.isEmpty(obsServiceCategory)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_OBS));
                }

                if (!productResourcesPricingCheck(obsServiceCategory.get(0).getId())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_OBS_NO_PRICE));
                }

                // 许可证标识
                LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
                if (!licenseVo.isMaSupportsEvsBilling()) {
                    // ModelArts产品价格检查
                    QueryWrapper<ServiceCategory> evsSpecQuery = new QueryWrapper<>();
                    evsSpecQuery.lambda().in(ServiceCategory::getServiceType, ProductCodeEnum.EVS.getProductType());
                    List<ServiceCategory> evsServiceCategory = serviceCategoryService.list(evsSpecQuery);
                    if (CollectionUtil.isEmpty(evsServiceCategory)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1*********));
                    }

                    if (!productResourcesPricingCheck(evsServiceCategory.get(0).getId())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1*********));
                    }
                }
            }
        }


        if (canNotUpdateToUsing(serviceCategory, status)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_TEMPLATE));
        }

        if (ServiceCategoryStatus.USING.equals(status)) {
            serviceCategory.setPublishDt(current);
        }

        serviceCategory.setStatus(status);
        serviceCategory.setUpdatedDt(current);
        serviceCategory.setUpdatedBy(AuthUtil.getAuthUser().getAccount());
        return serviceCategoryService.updateById(serviceCategory);
    }


    /**
     * serviceType是否需要询价
     *
     * @param serviceType
     * @return
     */
    private boolean checkRequirePrice(String serviceType) {
        Code code = codeMapper.selectByCodeCategoryAndCodeValue(TypesConstant.CHARGE_RESOURCE_TYPE, serviceType);
        if (code != null) {
            String attribute3 = code.getAttribute3();
            if (StringUtils.isNotEmpty(attribute3)) {
                JSONArray jsonArray = JSONUtil.parseArray(attribute3);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    String cloudEnvType = jsonObject.getStr("cloudEnvType");
                    if (CloudEnvEnum.HCSO.getKey().equals(cloudEnvType)) {
                        return jsonObject.getBool("charge");
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean checkProductCodeExist(String productCode, Long productId) {
        return isProductCodeExist(productCode, productId);
    }

    @Override
    public List<DescribeServiceCategory> publishProducts(String productNameLike, String entityId, String moduleType) {
        QueryWrapper<ServiceCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("publish_status", "succeed");
        queryWrapper.eq("service_component", "innerService");
        queryWrapper.eq("editable", ONE);
        queryWrapper.isNotNull("publish_dt");
        // 用户控制台只需看见已关联的产品
        if (CONSOLE.equals(moduleType)) {
            queryWrapper.isNotNull("entity_id");
            }

            // 查询sf_service_category表产品
            List<ServiceCategory> serviceCategoryList = serviceCategoryService.list(queryWrapper);
            serviceCategoryList = serviceCategoryList.stream()
                    .filter(t -> ProductCodeEnum.innerServiceProducts()
                            .contains(t.getServiceType()))
                    .collect(Collectors.toList());

            List<DescribeServiceCategory> response = new ArrayList<>();
            // HPC特殊逻辑，仅执行一次
            boolean isHPC = true;
            for (ServiceCategory data : serviceCategoryList) {
                // serviceType为HPC，但productName不为HPC共享资源池排除
                DescribeServiceCategory convert = BeanConvertUtil.convert(data, DescribeServiceCategory.class);
                if (ObjectUtils.isEmpty(convert)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
                }
                List<Long> categoryIds = catalogRelationService.list(
                                Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                                        .eq(SfServiceCatalogRelation::getServiceId, convert.getId()))
                        .stream()
                        .map(SfServiceCatalogRelation::getCatalogId)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(categoryIds)) {
                    // 赋值产品类别
                    List<SfProductCategoryCatalog> categories = this.list(
                            Wrappers.<SfProductCategoryCatalog>lambdaQuery()
                                    .in(SfProductCategoryCatalog::getId, categoryIds));
                    convert.setProductCategories(
                            categories.stream().map(SfProductCategoryCatalog::getCategoryName)
                                    .collect(Collectors.joining(StrUtil.COMMA)));
                }
                // 赋值产品组件
                convert.setServiceComponent(
                        "innerService".equals(convert.getServiceComponent()) ? BUILT_IN_COMPONENTS : CUSTOM_SERVICE);
                // HPC特殊逻辑仅执行一次，不直接从service_category中获取
                if (ProductCodeEnum.HPC.getProductType().equals(data.getServiceType()) ||
                        ProductCodeEnum.HPC_SAAS.getProductType().equals(data.getServiceType())) {
                    if (isHPC) {
                        isHPC = false;
                        // 对于HPC共享资源池特殊处理，对应hpc_cluster_pool多种
                        QueryResHpcClusterPoolRequest request = new QueryResHpcClusterPoolRequest();
                        request.setClusterType(ClusterTypeEnum.SAAS_SHARE.code());
                        request.setStatus(ACTIVE);
                        RestResult resHpcClusterResult = hpcClusterService.getResHpcCluster(request);
                        log.info("restResult...,{}", resHpcClusterResult.getStatus());
                        List<HashMap> hpcClusterPoolMap = (List) resHpcClusterResult.getData();
                        // 剔除HPC同种版本只显示产品名
                        List<Integer> sameVersion = new ArrayList<>();
                        for (HashMap hpcClusterPool : hpcClusterPoolMap) {
                            // 1武汉版本 2西安版本 3武汉二期版本
                            Integer hpcVersion = (Integer) hpcClusterPool.get("hpcVersion");
                            // sf_service_category_hpc_cluster_poolHPC共享资源池关联表，如果存在，则已关联运营实体
                            String clusterId = (String) hpcClusterPool.get("clusterId");
                            // hpc_version相同则显示同种产品
                            if (sameVersion.contains(hpcVersion)) {
                                for (DescribeServiceCategory describeServiceCategory : response) {
                                    if (hpcVersion.equals(describeServiceCategory.getHpcVersion())) {
                                        describeServiceCategory.setClusterId(
                                                describeServiceCategory.getClusterId() + "," + clusterId);
                                    }
                                }
                                continue;
                            }
                            SfServiceCategoryHpcClusterPool sfServiceCategoryHpcClusterPool = serviceCategoryMapper.selectServiceCategoryHPCByClusterId(
                                    clusterId);
                            DescribeServiceCategory hpcConvert = null;
                            if (Objects.nonNull(sfServiceCategoryHpcClusterPool)) {
                                // 产品列表查询返回对应运营实体已关联的产品
                                if (Objects.isNull(entityId) || entityId.equals(
                                        String.valueOf(sfServiceCategoryHpcClusterPool.getEntityId()))) {
                                    // 已关联
                                    hpcConvert = BeanConvertUtil.convert(convert,
                                            DescribeServiceCategory.class);
                                    if (ObjectUtils.isEmpty(hpcConvert)) {
                                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
                                    }
                                    // 拼接产品名
                                    hpcConvert.setProductName(sfServiceCategoryHpcClusterPool.getClusterName());
                                    hpcConvert.setEntityId(sfServiceCategoryHpcClusterPool.getEntityId());
                                    hpcConvert.setEntityName(sfServiceCategoryHpcClusterPool.getEntityName());
                                    hpcConvert.setStatus(
                                            Objects.isNull(sfServiceCategoryHpcClusterPool.getEntityId()) ? NOT_ASSOCIATED
                                                    : ASSOCIATED);
                                    hpcConvert.setClusterId(sfServiceCategoryHpcClusterPool.getClusterId());
                                } else {
                                    continue;
                                }
                            } else {
                                // 用户控制台只需看见已关联的产品
                                if (CONSOLE.equals(moduleType)) {
                                    continue;
                                }
                                // 未关联
                                hpcConvert = BeanConvertUtil.convert(convert,
                                        DescribeServiceCategory.class);
                                if (ObjectUtils.isEmpty(hpcConvert)) {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
                                }
                                // 拼接产品名
                                hpcConvert.setProductName(3L == hpcVersion
                                        ? ProductCodeEnum.HPC_SAAS.getProductName()
                                        : ProductCodeEnum.HPC.getProductName());
                                hpcConvert.setEntityId(null);
                                hpcConvert.setEntityName(null);
                                hpcConvert.setStatus(NOT_ASSOCIATED);
                                hpcConvert.setClusterId(clusterId);
                            }
                            if (Objects.nonNull(productNameLike) && !hpcConvert.getProductName()
                                    .contains(productNameLike)) {
                                continue;
                            }
                            hpcConvert.setHpcVersion(hpcVersion);
                            response.add(hpcConvert);
                            sameVersion.add(hpcVersion);
                        }
                    }
                    continue;
                }
                convert.setStatus(Objects.isNull(convert.getEntityId()) ? NOT_ASSOCIATED : ASSOCIATED);
                // 产品列表查询返回对应运营实体已关联的产品
                if (Objects.nonNull(convert.getEntityId()) && Objects.nonNull(entityId) && !entityId.equals(
                        String.valueOf(convert.getEntityId()))) {
                    continue;
                }
                if (Objects.nonNull(productNameLike) && !convert.getProductName().contains(productNameLike)) {
                    continue;
                }
                response.add(convert);
            }
            // 安全版本不需要ModelArts以外的其他服务，暂时过滤
            response = response.stream()
                    .filter(describeServiceCategory -> "ModelArts".equals(describeServiceCategory.getProductCode())
                            || "DRP".equals(describeServiceCategory.getProductCode()))
                    .collect(Collectors.toList());

        return response;
    }


    @Override
    public List<DescribeServiceCategory> publishProductsList(String productNameLike, String entityId, String moduleType) {
        QueryWrapper<ServiceCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("publish_status", "succeed");
        queryWrapper.eq("service_component", "innerService");
        //修复bug 47643
        queryWrapper.eq("status", "using");
        queryWrapper.eq("editable", ONE);
        queryWrapper.isNotNull("publish_dt");
        // 用户控制台只需看见已关联的产品
        if (CONSOLE.equals(moduleType)) {
            queryWrapper.isNotNull("entity_id");
        }
        // 查询sf_service_category表产品
        List<ServiceCategory> serviceCategoryList = serviceCategoryService.list(queryWrapper);
        serviceCategoryList = serviceCategoryList.stream()
                .filter(t -> ProductCodeEnum.innerServiceProducts()
                        .contains(t.getServiceType()))
                .collect(Collectors.toList());


        // 用户是否开启BMS功能
        User authUser = AuthUtil.getAuthUser();
        Long orgSid = authUser.getOrgSid();
        Org org = orgService.lambdaQuery().eq(Org::getOrgSid, orgSid).one();
        if (org != null && 1L == org.getBmsEnable()) {
            ServiceCategory one = serviceCategoryService.lambdaQuery().eq(ServiceCategory::getServiceType, ProductCodeEnum.MA_BMS.getProductType()).one();
            if(one != null){
                serviceCategoryList.add(one);
            }
        }

        List<DescribeServiceCategory> response = new ArrayList<>();
        for (ServiceCategory data : serviceCategoryList) {
            // serviceType为HPC，但productName不为HPC共享资源池排除
            DescribeServiceCategory convert = BeanConvertUtil.convert(data, DescribeServiceCategory.class);
            if (ObjectUtils.isEmpty(convert)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
            }
            List<Long> categoryIds = catalogRelationService.list(
                            Wrappers.<SfServiceCatalogRelation>lambdaQuery()
                                    .eq(SfServiceCatalogRelation::getServiceId, convert.getId()))
                    .stream()
                    .map(SfServiceCatalogRelation::getCatalogId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(categoryIds)) {
                // 赋值产品类别
                List<SfProductCategoryCatalog> categories = this.list(
                        Wrappers.<SfProductCategoryCatalog>lambdaQuery()
                                .in(SfProductCategoryCatalog::getId, categoryIds));
                convert.setProductCategories(
                        categories.stream().map(SfProductCategoryCatalog::getCategoryName)
                                .collect(Collectors.joining(StrUtil.COMMA)));
            }
            // 赋值产品组件
            convert.setServiceComponent(
                    "innerService".equals(convert.getServiceComponent()) ? BUILT_IN_COMPONENTS : CUSTOM_SERVICE);
            convert.setStatus(Objects.isNull(convert.getEntityId()) ? NOT_ASSOCIATED : ASSOCIATED);
            // 产品列表查询返回对应运营实体已关联的产品
            if (Objects.nonNull(convert.getEntityId()) && Objects.nonNull(entityId) && !entityId.equals(
                    String.valueOf(convert.getEntityId()))) {
                continue;
            }
            if (Objects.nonNull(productNameLike) && !convert.getProductName().contains(productNameLike)) {
                continue;
            }
            // 国际化处理
            if (WebUtil.getHeaderAcceptLanguage()) {
                convert.setProductName(convert.getProductCode());
            }
            response.add(convert);
        }
        return response;
    }


    @Override
    public List<DescribeServiceCategory> findProductByAccountId(Long accountId) {
        // 找到账户ID对应运营实体ID
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long entityId = bizBillingAccount.getEntityId();
        if (Objects.nonNull(authUserInfo)){
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectAccountsByOrgId(authUserInfo.getOrgSid());
            if (CollectionUtil.isNotEmpty(bizBillingAccounts)){
                if (bizBillingAccounts.stream().noneMatch(b -> b.getId().equals(accountId))) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
        }
        // 查询sf_service_category表产品
        QueryWrapper<ServiceCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("publish_status", "succeed");
        queryWrapper.eq("status", "using");
        queryWrapper.eq("editable", ONE);
        queryWrapper.isNotNull("publish_dt");
        // 查询sf_service_category表产品
        List<ServiceCategory> serviceCategoryList = serviceCategoryService.list(queryWrapper);
        List<DescribeServiceCategory> response = new ArrayList<>();
        // HPC特殊逻辑，仅执行一次
        boolean isHPC = true;
        for (ServiceCategory data : serviceCategoryList) {
            DescribeServiceCategory convert = BeanConvertUtil.convert(data, DescribeServiceCategory.class);
            if (ObjectUtils.isEmpty(convert)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
            }
            // 赋值产品组件
            convert.setServiceComponent(
                    "innerService".equals(convert.getServiceComponent()) ? BUILT_IN_COMPONENTS : CUSTOM_SERVICE);
            if (ProductCodeEnum.HPC.getProductType().equals(data.getServiceType()) ||
                    ProductCodeEnum.HPC_SAAS.getProductType().equals(data.getServiceType())) {
                if (isHPC) {
                    isHPC = false;
                    List<SfServiceCategoryHpcClusterPool> sfServiceCategoryHpcClusterPools = serviceCategoryMapper.selectServiceCategoryByHPC();
                    if (CollectionUtil.isNotEmpty(sfServiceCategoryHpcClusterPools)) {
                        Set<String> hpcCategorys = new HashSet<>();
                        for (SfServiceCategoryHpcClusterPool hpcService : sfServiceCategoryHpcClusterPools) {
                            if (!entityId.equals(hpcService.getEntityId())) {
                                continue;
                            }
                            if (hpcCategorys.contains(convert.getProductCode())) {
                                continue;
                            }
                            hpcCategorys.add(convert.getProductCode());
                            DescribeServiceCategory hpcConvert = BeanConvertUtil.convert(convert,
                                                                                         DescribeServiceCategory.class);
                            if (ObjectUtils.isEmpty(hpcConvert)) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
                            }
                            hpcConvert.setProductName(hpcService.getClusterName());
                            hpcConvert.setEntityId(hpcService.getEntityId());
                            hpcConvert.setEntityName(hpcService.getEntityName());
                            hpcConvert.setClusterId(hpcService.getClusterId());
                            response.add(hpcConvert);
                        }

                    }
                }
                continue;
            }
            if (!entityId.equals(data.getEntityId())) {
                continue;
            }
            response.add(convert);
        }
        return response;
    }

    /**
     * 是否有产品分类
     *
     * @param serviceCategory
     * @return
     */
    private boolean checkCataLog(ServiceCategory serviceCategory) {
        QueryWrapper<SfServiceCatalogRelation> query = new QueryWrapper<>();
        query.eq("service_id", serviceCategory.getId());
        int count = this.catalogRelationService.count(query);
        if (count > 0) {
            return true;
        }
        return false;
    }

    /**
     * ProductCode
     *
     * @param productId
     */
    private boolean isProductCodeExist(String productCode, Long productId) {
        QueryWrapper<ServiceCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_code", productCode);
        queryWrapper.ne("status", ServiceCategoryStatus.DELETE);
        if (productId != null) {
            queryWrapper.ne("id", productId);
        }
        int count = serviceCategoryService.count(queryWrapper);
        if (count > 0) {
            return true;
        }
        return false;
    }

    /**
     * 产品资源定价检查
     *
     * @param productId
     */
    private boolean productResourcesPricingCheck(Long productId) {

        QueryWrapper<ServiceCategory> categoryQueryWrapper = new QueryWrapper<>();
        categoryQueryWrapper.eq("publish_status", "succeed");
        categoryQueryWrapper.orderByDesc("created_dt");
        categoryQueryWrapper.eq("id", productId);
        List<DescribeBillingStrategyServingResponse> strategyServingResponses =
                bizBillingServingConfigService.queryBillingServingConfig(categoryQueryWrapper, null);
        if (CollectionUtil.isEmpty(strategyServingResponses)
                || Objects.isNull(strategyServingResponses.get(0))
                || !strategyServingResponses.get(0).getAccountPriced()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 验证服务类别的合法性
     *
     * @param productIds
     */
    private void serviceCategoryValidation(Long categoryId, List<Long> productIds) {
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<Long> queryList = productIds.stream().distinct().collect(Collectors.toList());
            // 33964 原逻辑不让已上架的产品被关联，现在已上架的产品不能被选中
            List<ServiceCategory> results = serviceCategoryService.list(
                    Wrappers.<ServiceCategory>lambdaQuery().in(
                            ServiceCategory::getId, queryList));
            List<ServiceCategory> resultList = results
                    .stream().filter(t -> !StringUtils.equalsIgnoreCase("delete", t.getStatus())).collect(Collectors.toList());
            if (resultList.stream()
                    .anyMatch(serviceCategory -> !serviceCategory.getEntityId().equals(RequestContextUtil.getEntityId()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
            if (CollectionUtil.isEmpty(resultList)) {
                if (queryList.size() == resultList.size()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
                }
            } else {
                if (queryList.size() != resultList.size()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
                }
                List<Long> serviceIds = catalogRelationService.findServiceIdByCategory(categoryId, getEntityId());
                List<Long> backup = new ArrayList();
                backup.addAll(serviceIds);

                serviceIds.removeAll(queryList);
                if (CollectionUtils.isNotEmpty(serviceIds)) {
                    List<ServiceCategory> old = serviceCategoryService.list(Wrappers.<ServiceCategory>lambdaQuery().in(ServiceCategory::getId, serviceIds));
                    if (old.stream().anyMatch(serviceCategory -> !serviceCategory.getStatus().equals("nousing"))) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_134089269));
                    }
                }
                queryList.removeAll(backup);
                if (CollectionUtils.isNotEmpty(queryList)) {
                    List<String> greyOut = Arrays.asList(ProductCodeEnum.OBS.getProductType(), ProductCodeEnum.SFS.getProductType());
                    List<ServiceCategory> newCategory = serviceCategoryService.list(Wrappers.<ServiceCategory>lambdaQuery().in(ServiceCategory::getId, queryList));
                    if (newCategory.stream().anyMatch(serviceCategory -> !serviceCategory.getStatus().equals("nousing") || greyOut.contains(serviceCategory.getProductCode()))) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_134089269));
                    }
                }
            }
        }
    }

        /**
         * 获取entityId
         */
        private Long getEntityId() {
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
            assert authUserInfo != null;
            log.info("获取当前所处运营实体id{}", authUserInfo.getEntityId());
            return authUserInfo.getEntityId();
        }



    @Override
    public RestResult checkServiceStatus(String serviceType) {
        if ("slb".equalsIgnoreCase(serviceType)) {
            serviceType = ProductCodeEnum.ELB.getProductType();
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(serviceType);
        if (Objects.isNull(serviceCategory)) {
            return new RestResult(RestResult.Status.FAILURE, "未查询到产品数据");
        } else {
            if ("using".equals(serviceCategory.getStatus())) {
                return new RestResult(RestResult.Status.SUCCESS);
            } else if (CouponStatusEnum.UNAUTH.getCode().equals(serviceCategory.getStatus())) {
                return new RestResult(RestResult.Status.FAILURE, "创建失败，产品未授权");
            } else {
                return new RestResult(RestResult.Status.FAILURE, "创建失败，产品未上架");
            }
        }
    }
}
