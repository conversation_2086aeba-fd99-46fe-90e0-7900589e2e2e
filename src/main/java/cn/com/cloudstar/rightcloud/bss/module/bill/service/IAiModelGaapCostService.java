/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.service;

import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.AiModelCollector;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.Collector;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;

import java.util.List;

/**
 * @ClassName: IAiModelGaapCostService.java
 * @Description： AiModel计费服务接口
 * @Author: system
 * @Date: 2025/01/21
 * @Version: 1.0.0
 **/
public interface IAiModelGaapCostService {
    
    /**
     * 处理AiModel话单计费
     * 
     * @param archRecords 归档记录列表
     * @param detail 话单详情
     * @param chargeType 计费类型
     * @param costs 费用列表
     * @param deals 交易明细列表
     */
    void handleAiModelBill(List<Collector> archRecords, AiModelCollector detail, String chargeType, 
                          List<InstanceGaapCost> costs, List<BizAccountDeal> deals);
}
