package cn.com.cloudstar.rightcloud.bss.module.resource.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("SWR组织查询")
@Data
public class SwrNameSpaceRequest implements Serializable {

    private static final long serialVersionUID = 48042362639329L;

    private Long id;
    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer pagesize;

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer pagenum;

    /**
     * 组织
     */
    @ApiModelProperty("组织")
    private String name;

    /**
     * iam用户
     */
    @ApiModelProperty("iam用户")
    private String creatorName;

    /**
     * 用户权限。7表示管理权限，3表示编辑权限，1表示读取权限
     */
    @ApiModelProperty("用户权限")
    private Integer auth;

    /**
     * 云环境id
     */
    @ApiModelProperty("云环境id")
    private Long cloudEnvId;

    /**
     * 所属组织ID
     */
    private Long ownerOrgId;

}
