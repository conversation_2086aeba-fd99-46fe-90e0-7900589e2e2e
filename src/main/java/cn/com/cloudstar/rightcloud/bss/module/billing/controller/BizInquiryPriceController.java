/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.controller;

import cn.com.cloudstar.rightcloud.basic.data.service.config.BasicSysConfigService;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.ISysBssEntityService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizInquiryPriceService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.InquiryPriceUtil;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorProductService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ServiceFeignService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD04;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.C1;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SysBssEntity;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.request.ProductSpecDefineFeignForm;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.result.ProductSpecDefineFeignResult;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资源计费配置管理
 *
 * <AUTHOR>
 * Created on 2019/10/31
 */
@Api(tags = "资源计费配置管理")
@Slf4j
@RestController
@RequestMapping("/billing")
@Validated
public class BizInquiryPriceController {
    private static final int DECIMAL_FIX = 3;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private BizInquiryPriceService inquiryPriceService;

    @Autowired
    private BasicSysConfigService sysConfigService;
    @Autowired
    private IBizDistributorProductService distributorProductService;

    @Autowired
    private ServiceCategoryService categoryService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    @Autowired
    private ISysBssEntityService sysBssEntityService;

    @Autowired
    private SysUserService userService;

    @Autowired
    private IServiceOrderService serviceOrderService;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;


    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private ServiceFeignService serviceFeignService;
    @Autowired
    private BizCouponMapper bizCouponMapper;

    /**
     * 计算价格
     *【Since v2.5.0】
     * @param request 实例询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @AuthorizeBss(action = BD04.BD040201)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'计算价格'", resource = OperationResourceEnum.CALCULATE_PRICE, bizId = "#request.id", tagNameUs ="'Calculate the price'")
    public InquiryPriceResponse calculatePrice(
            @Valid @RequestBody InquiryInstancePriceRequest request) {
        InquiryInstancePrice inquiryInstancePrice = BeanConvertUtil.convert(request, InquiryInstancePrice.class);
        InquiryPriceResult inquiryPriceResult = inquiryPriceService.calculateResourcePrice(inquiryInstancePrice);

        return inquiryPriceService.convertPriceResponse(inquiryPriceResult, request.getInstanceChargeType());
    }

    /**
     * 计算价格
     *
     * @param request 续费询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @PostMapping("/renew/inquiry/price")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C115 + "," + AuthModule.COMMON.PUBLIC.B1.B113)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'计算价格'", resource = OperationResourceEnum.CALCULATE_PRICE, bizId = "#request.id", tagNameUs ="'Calculate the price'")
    public InquiryPriceResponse calculatePrice(
            @Valid @RequestBody InquiryRenewPriceRequest request) {
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        InquiryPriceResponse result = inquiryPriceService.inquiryRenewPrice(BeanConvertUtil.convert(request, InquiryRenewPriceRequestDTO.class));
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return result;
    }

    /**
     * 计算价格
     *
     * @param request 续费询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @PostMapping("/renew/inquiry/price/batch")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C115 + "," + AuthModule.COMMON.PUBLIC.B1.B113)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'计算价格'", resource = OperationResourceEnum.CALCULATE_PRICE, tagNameUs ="'Calculate the price'")
    public List<InquiryPriceResponse> calculatePriceEcs(@Valid @RequestBody InquiryRenewPriceBatchRequest request) {
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        List<InquiryPriceResponse> result = Lists.newArrayList();
        boolean multiProduct = 1 < request.getProductInfo().size();
        request.getProductInfo().forEach(e -> {
            if (!multiProduct) {
                e.setCouponId(request.getCouponId());
            }
            result.add(inquiryPriceService.inquiryRenewPrice(BeanConvertUtil.convert(e, InquiryRenewPriceRequestDTO.class)));

        });
        if (multiProduct && Objects.nonNull(request.getCouponId())) {
            // 多产品优惠卷分摊
            inquiryPriceService.multipleProductCouponPrice(result, bizCouponMapper.selectById(request.getCouponId()).getDiscountAmount());
        }
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return result;
    }

    /**
     * hpc续订
     *
     * @param request 续费询价请求体
     * @return {@code List<InquiryPriceResponse>}
     */
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'hpc续订'", resource = OperationResourceEnum.HPCDRP_PRICE, bizId = "#request.id", tagNameUs ="'Renew'")
    public List<InquiryPriceResponse> hpcDrpPrice(
            @Valid @RequestBody InquiryRenewPriceRequest request) {
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }

        InquiryRenewPriceRequestDTO priceRequest = BeanConvertUtil.convert(request, InquiryRenewPriceRequestDTO.class);

        List<InquiryPriceResponse> list = inquiryPriceService.inquiryRenewPriceList(priceRequest);


        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return list;
    }

    /**
     * 计算价格
     *【Since v2.5.0】
     * [INNER API] 计算价格
     * @param request 续费询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @PostMapping("/renew/inquiry/price/feign")
    @RejectCall
    public InquiryPriceResponse calculatePriceFeign(
            @Valid @RequestBody InquiryRenewPriceRequest request) {

        if (StringUtils.isNotBlank(request.getProductCode())) {
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(request.getProductCode());
            if (!Objects.equals(serviceCategory.getEntityId(), RequestContextUtil.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
        }
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        InquiryPriceResponse result = inquiryPriceService.inquiryRenewPrice(BeanConvertUtil.convert(request, InquiryRenewPriceRequestDTO.class));
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return result;
    }

    /**
     * 判断是否关联计费产品
     * @return
     */
    @AuthorizeBss(action = AuthModule.CB.CB19)
    @GetMapping("/isNonBillProduct")
    @CrossOrigin
    public boolean isNonBillProduct(@Valid IsNonBillProductRequest request) {
        if (request.getResourceId() != null) {
            User authUser = AuthUtil.getAuthUser();
            SfProductResource sfProductResource = sfProductResourceMapper.selectById(request.getResourceId());
            if (ObjectUtil.isNull(sfProductResource)) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }

            QueryWrapper<ServiceOrderResourceRef> refQuery = new QueryWrapper<>();
            refQuery.lambda()
                    .eq(ServiceOrderResourceRef::getResourceId, sfProductResource.getId())
                    .eq(ServiceOrderResourceRef::getType, sfProductResource.getProductType());
            ServiceOrderResourceRef serviceOrderResourceRef = serviceOrderResourceRefService.getOne(refQuery);
            if (ObjectUtil.isNull(serviceOrderResourceRef)) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }

            ServiceOrderDetail orderDetail = serviceOrderDetailService.getById(serviceOrderResourceRef.getOrderDetailId());
            if (ObjectUtil.isNull(orderDetail)) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }

            ServiceOrderVo serviceOrderVo = serviceOrderMapper.selectOrderDetailById(orderDetail.getOrderId());
            if (ObjectUtil.isNull(serviceOrderVo)) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            if (!serviceOrderVo.getOwnerId().equals(String.valueOf(authUser.getUserSid()))){
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

        if (Objects.nonNull(request.getSfServiceId())) {
            Criteria criteria = new Criteria();
            criteria.put("id", request.getSfServiceId());
            List<ServiceCategory> serviceCategoryByParams = serviceCategoryMapper.getServiceCategoryByParams(criteria);
            if (CollectionUtil.isEmpty(serviceCategoryByParams)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1514956579));
            }
        }
        return distributorProductService.selectIsNonBillProduct(request);
    }

    /**
     * [INNER API] 判断是否关联计费产品
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @RejectCall
    @GetMapping("/isNonBillProduct/feign")
    public RestResult isNonBillProductFeign(IsNonBillProductRequest request) {
        return new RestResult(distributorProductService.selectIsNonBillProduct(request));
    }

    /**
     * 询价
     *
     * @param request 服务申请请求体
     * @return {@code List<InquiryPriceResponse>}
     */
    @PostMapping("/price")
    @AuthorizeBss(action = C1.C115)
    public RestResult<List<InquiryPriceResponse>> inquiryPrice(
            @Valid @RequestBody ApplyServiceRequest request) {
        convertReq(request);
        ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);
        if (StringUtils.isNotBlank(request.getPointInTime())) {
            applyServiceVO.setPointInTime(DateUtil.parseDate(request.getPointInTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        if (ObjectUtils.isEmpty(applyServiceVO)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
            AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()), cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        User authUser = AuthUtil.getAuthUser();

        EntityDTO entityDTO=null;
        log.info("applyServiceVO.getEntityId()  is ==========={}",applyServiceVO.getEntityId());
        if(applyServiceVO.getEntityId() !=null){
            SysBssEntity sysBssEntity = sysBssEntityService.getById(applyServiceVO.getEntityId());
            if (sysBssEntity != null){
                entityDTO = new EntityDTO();
                entityDTO.setEntityId(sysBssEntity.getId());
                entityDTO.setEntityName(sysBssEntity.getName());
            }
        }
        if (entityDTO == null) {
        //开通产品为hpc共享
        if (Objects.equals("HPC", applyServiceVO.getProductInfo().get(0).getProductCode())
                || Objects.equals(ProductCodeEnum.HPC_SAAS.getProductType(), applyServiceVO.getProductInfo().get(0).getProductCode())) {
            //根据clusterId与产品id查询对应运营实体
            entityDTO = categoryService.getByClusterIdAndServiceId(request.getClusterId(), applyServiceVO.getProductInfo().get(0).getServiceId());
        } else {
            entityDTO = categoryService.getEntityByCategoryId(applyServiceVO.getProductInfo().get(0).getServiceId());
            if (Objects.isNull(entityDTO)) {
                List<EntityDTO> entityList =  categoryService.getByServiceType(applyServiceVO.getProductInfo().get(0).getProductCode());
                if (!entityList.isEmpty()) {
                    entityDTO = entityList.get(0);
                    applyServiceVO.getProductInfo().get(0).setServiceId(entityDTO.getServiceId());
                }

            }
        }
        }
        if (Objects.isNull(entityDTO)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1964963679));
        }
        Long userSid;
        String userType="";
        Long orgSid=null;
        if (Objects.isNull(authUser)) {
            userSid = request.getUserSid();
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = userService.selectByPrimaryKey(request.getUserSid());
            userType=user.getUserType();
            orgSid=user.getOrgSid();
        } else {
            Long userEntityId = authUser.getEntityId();
            if (userEntityId != null && userEntityId != 0 && !entityDTO.getEntityId().equals(userEntityId)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_218235077));
            }
            if (Objects.nonNull(authUser.getParentSid())) {
                // 子用户找租户userSid
                userSid = authUser.getParentSid();
            } else {
                userSid = authUser.getUserSid();
            }
            userType=authUser.getUserType();
            orgSid=authUser.getOrgSid();
        }
        BizBillingAccount account = bizBillingAccountService.getByEntityIdAndUserId(entityDTO.getEntityId(), userSid);

        //判断当前用户是否为运营管理员
        if ((UserType.PLATFORM_USER.equals(userType) && Objects.isNull(orgSid)) || UserType.DISTRIBUTOR_USER.equals(
                userType)) {
            if(request.getUserSid() == null){
                //如果是运营管理员则到account中随便取一条数据做预览用
                account = bizBillingAccountService.getFirst();
            }else{
                if (request.getUserSid() == null) {
                    //如果是运营管理员则到account中随便取一条数据做预览用
                    account = bizBillingAccountService.getFirst();
                } else {//如果是运营管理员则为代客订购，account对应客户账户
                account = bizBillingAccountService.getByEntityIdAndUserId(entityDTO.getEntityId(), request.getUserSid());
            }
        }
        }
        if (Objects.isNull(account)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_175772512));
        }


        applyServiceVO.setAccountId(account.getId());
        applyServiceVO.setEntityId(entityDTO.getEntityId());
        applyServiceVO.setEntityName(entityDTO.getEntityName());
        applyServiceVO.setClusterId(request.getClusterId());
        List<InquiryPriceResponse> result = inquiryPriceService.inquiryProductPrice(applyServiceVO);
        // 重新获取当前管理员信息
        if (Objects.nonNull(applyServiceVO.getBehalfUserSid())) {
            AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(applyServiceVO.getBehalfUserSid()), AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        return new RestResult<>(result);
    }

    private void convertReq(@Valid ApplyServiceRequest request) {
        if (CollUtil.isEmpty(request.getProductInfo())) {
            return;
        }
        if (request.getProductInfo().get(0).getProductCode().equalsIgnoreCase("DCS")) {
            request.getProductInfo().get(0).setAmount(1);
        }
        Map<String, Object> map = BeanUtil.beanToMap(request.getProductInfo().get(0).getData());
        if (map.containsKey("DWS")) {
            Object o = map.get("DWS");
            Map<String, Object> m = BeanUtil.beanToMap(o);
            if(m.size()>1){
                return;
            }
            m.put("chargeItemCategory","compute");
            m.put("category","dws");
            m.put("spec","dws");
            map.put("DWS",m);
        }else if (map.containsKey(ProductCodeEnum.CSS.getProductType())) {
            Object o = map.get(ProductCodeEnum.CSS.getProductType());
            Map<String, Object> m = BeanUtil.beanToMap(o);
            m.put("chargeItemCategory","compute");
            m.put("category","css");
            m.put("spec","css");
            map.put(ProductCodeEnum.CSS.getProductType(),m);
        }
        if (map.containsKey("MRS")) {
            Object o = map.get("MRS");
            Map<String, Object> m = BeanUtil.beanToMap(o);
            if(m.size()>1){
                return;
            }
            m.put("chargeItemCategory","compute");
            m.put("category","mrs");
            m.put("spec","mrs");
            map.put("MRS",m);
        }
        if (map.containsKey("CBR")) {
            Object o = map.get("CBR");
            Map<String, Object> m = BeanUtil.beanToMap(o);
            if("gb".equals(m.get("sizeType"))){
                m.put("size",m.get("sizeGB"));
            }else{
                if(ObjectUtil.isNotEmpty(m.get("sizeTB"))){
                    m.put("size",Integer.parseInt(m.get("sizeTB").toString())*1024);
                }
            }
            map.put("CBR",m);
        }
        request.getProductInfo().get(0).setData(map);
        ProductSpecDefineFeignForm form = new ProductSpecDefineFeignForm();
        form.setProductCode("EBS");
        form.setStatus("enable");
        form.setRequiredAttr(true);
        cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult<List<ProductSpecDefineFeignResult>> productSpecDefine = serviceFeignService.getProductSpecDefine(form);
        Object specList = productSpecDefine.getData();
        Map<Long, String> specMap = new HashMap<>();
        if (Objects.nonNull(specList)) {
            List<ProductSpecDefineFeignResult> productSpecDefines = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
            specMap = productSpecDefines.stream().collect(Collectors.toMap(ProductSpecDefineFeignResult::getSpecTypeId, ProductSpecDefineFeignResult::getProductSpecCode));
        }
        if (map.containsKey("sysDisk") && Objects.nonNull(map.get("sysDisk"))) {
            Object disk = map.get("sysDisk");
            ProductInfoVO productInfoVO = packegeDisk(disk, specMap, request.getProductInfo().get(0));
            if (Objects.nonNull(productInfoVO)) {
                request.getProductInfo().add(productInfoVO);
            }
        }
        if (map.containsKey("dataDisk") && Objects.nonNull(map.get("dataDisk"))) {
            Object diskObj = map.get("dataDisk");
            JSONArray disks = JSON.parseArray(JSON.toJSONString(diskObj));
            for (Object disk : disks) {
                ProductInfoVO productInfoVO = packegeDisk(disk, specMap, request.getProductInfo().get(0));
                if (Objects.nonNull(productInfoVO)) {
                    request.getProductInfo().add(productInfoVO);
                }
            }
        }
    }

    private static ProductInfoVO packegeDisk(Object disk, Map<Long, String> specMap, ProductInfoVO template) {
        ProductInfoVO addProductInfo =  BeanConvertUtil.convert(template, ProductInfoVO.class);
        addProductInfo.setProductCode(ProductCodeEnum.DISK.getProductType());
        Map<String, Object> m = BeanUtil.beanToMap(disk);
        Map<String, Object> productInfo = new HashMap<>();
        Object volumeType = m.get("volumeType");
        if (Objects.isNull(volumeType)) {
            return null;
        }
        String name = specMap.get(Long.valueOf(m.get("volumeType").toString()));
        if (StringUtils.isBlank(name)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        productInfo.put("chargeItemCategory", "blockStorage");
        productInfo.put("category",  name);
        productInfo.put("spec", name);
        productInfo.put("size", m.get("volumeSize"));
        productInfo.put("productCode", ProductCodeEnum.DISK.getProductType());
        Map<String, Object> data = new HashMap<>();
        data.put(ProductCodeEnum.DISK.getProductType(), productInfo);
        addProductInfo.setData(data);
        return addProductInfo;
    }

    /**
     * [INNER API] 询价
     *
     * @param request 服务申请请求体
     * @return {@code List<InquiryPriceResponse>}`
     */
    @RejectCall
    @PostMapping("/price/feign")
    public RestResult<List<InquiryPriceResponse>> inquiryPriceByFeign(@Valid @RequestBody ApplyServiceRequest request) {
        return inquiryPrice(request);
    }

    /**
     * 询价
     *【Since v2.5.0】
     * @param request 服务申请请求体
     * @return {@code List<InquiryPriceResponse>}
     */
    public RestResult<List<InquiryPriceResponse>> inquiryPriceWhite(
            @Valid @RequestBody ApplyServiceRequest request) {
        return inquiryPriceByFeign(request);
    }
    /**
     * 获取服务价格详情
     *
     * @param id 产品id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD0402)
    @GetMapping("/service/price/{id}")
    public RestResult<ServicePriceDetailNewSimpleVO> servicePriceDetail(@PathVariable("id") Long id, String userSid) {
        ServiceCategory serviceCategory = categoryService.getById(id);
        if (Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296864863));
        }
        ServicePriceDetailNewVO servicePriceDetail = inquiryPriceService.getServicePriceDetailNew(id, userSid);
        ServicePriceDetailNewSimpleVO servicePriceDetailNewSimpleVO =
                BeanConvertUtil.convert(servicePriceDetail, ServicePriceDetailNewSimpleVO.class);
        return new RestResult(servicePriceDetailNewSimpleVO);
    }

    /**
     * 获取云账号定价详情
     *
     * @param id id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD040201)
    @GetMapping("/account/price/{id}")
    public RestResult<ServiceAccountPriceDetailVO> accountPriceDetail(@PathVariable("id") Long id) {
        ServiceAccountPriceDetailVO accountPriceDetail = inquiryPriceService.getAccountPriceDetail(id);
        return new RestResult(accountPriceDetail);
    }

    /**
     * 计算资源价格
     *【Since v2.5.0】
     * @param request 实例询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @AuthorizeBss(action = BD04.BD040201)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "#request.serviceType", resource = OperationResourceEnum.COUNT_SERVICE_PRICE, bizId = "#request.billingAccountId")
    public InquiryPriceResponse serviceconfig(
            @Valid @RequestBody InquiryInstancePriceServiceConfigRequest request) {
        InquiryInstancePrice priceQuery = InquiryPriceUtil.getPriceQuery(request);

        InquiryPriceResult inquiryPriceResult = inquiryPriceService.calculateResourcePrice(priceQuery);

        return inquiryPriceService.convertPriceResponse(inquiryPriceResult, priceQuery.getInstanceChargeType());
    }

    /**
     * 产品价格详情预览
     *
     * @param id      id
     * @param request 价格预览请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD040201)
    @GetMapping("/{id}/overview")
    @ApiOperation("产品价格详情预览")
    public RestResult<ServicePriceDetailNewSimpleVO> overviewPrice(@PathVariable("id") Long id, @Valid OverviewPriceRequest request) {
        ServicePriceDetailNewVO servicePriceDetail = inquiryPriceService.overviewServicePrice(id, request.getChargeIds(), request.getResourceType());
        ServicePriceDetailNewSimpleVO servicePriceDetailNewSimpleVO =
                BeanConvertUtil.convert(servicePriceDetail, ServicePriceDetailNewSimpleVO.class);
        return new RestResult(servicePriceDetailNewSimpleVO);
    }

    /**
     * 退订调查价格
     *
     * @param id   id
     * @param type 类型
     * @return {@code UnsubscribeResponse}
     */
    @AuthorizeBss(action = CB.SERVICE_UNSUBSCRIBE)
    @GetMapping("/unsubscribe/inquiry/price/{id}")
    public UnsubscribeResponse bssUnsubscribeInquiryPrice(@PathVariable("id") String id,
                                                       @RequestParam(value = "type", required = false) String type) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        SfProductResource sfProductResource = sfProductResourceMapper.selectById(id);

        if (StringUtils.equals(type, ProductCodeEnum.SFS2.getProductType())) {
            String refInstanceId = "[\"" + id + "\"]";
            List<ServiceOrderVo> orderList = serviceOrderMapper.selectByInstanceId(refInstanceId);
            orderList = orderList.stream().filter(e -> StringUtils.equals(e.getProductName(), "弹性文件服务2.0 SFS2.0")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(orderList) && !orderList.get(0).getOrgSid().equals(authUser.getOrgSid())) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(cn.com.cloudstar.rightcloud.common.constants.RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }else {
            if (Objects.isNull(sfProductResource) || !sfProductResource.getOrgSid().equals(authUser.getOrgSid())) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(cn.com.cloudstar.rightcloud.common.constants.RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
        return unsubscribeInquiryPrice(id, type);
    }

    /**
     * 退订调查价格feign
     * [INNER API] modarts管理侧退订
     *
     * @param id   id
     * @param type 类型
     * @return {@code UnsubscribeResponse}
     * @since 2023/04/07 17:33
     */
    @RejectCall
    @GetMapping("/unsubscribe/inquiry/price/{id}/feign")
    public UnsubscribeResponse ossUnsubscribeInquiryPrice(@PathVariable("id") String id,
                                                          @RequestParam(value = "type", required = false) String type) {
        return unsubscribeInquiryPrice(id, type);
    }

    /**
     * 退订调查价格
     *
     * @param id   id
     * @param type 类型
     * @return {@code UnsubscribeResponse}
     */
    private UnsubscribeResponse unsubscribeInquiryPrice(@PathVariable("id") String id,
                                                       @RequestParam(value = "type", required = false) String type) {
        UnsubInquiryPriceVO unsubInquiryPriceVO = inquiryPriceService.unsubInquiryPrice(id, type);
        if(ObjectUtil.isNotEmpty(unsubInquiryPriceVO.getDetail())){
            // 同类型产品汇总
            summaryProductPrice(unsubInquiryPriceVO.getDetail());
        }

        if (ProductCodeEnum.SFS2.getProductType().equals(type)) {
            serviceOrderService.checkPendingOrder(id, type);
            BigDecimal expiredUsedAmount = BigDecimalUtil.getTwoPointAmount(unsubInquiryPriceVO.getExpiredUsedAmount());
            unsubInquiryPriceVO.setExpiredUsedAmount(expiredUsedAmount);
        }else {
            List<ResourceDetailVO> details = unsubInquiryPriceVO.getDetail();
            if (CollectionUtil.isNotEmpty(details)) {
                BigDecimal totalExpiredUsed = BigDecimal.ZERO;
                //抹零后展示在前端
                for (ResourceDetailVO detail : details) {
                    BigDecimal expiredUsedAmount = detail.getExpiredUsedAmount();
                    expiredUsedAmount = BigDecimalUtil.getTwoPointAmount(expiredUsedAmount);
                    detail.setExpiredUsedAmount(expiredUsedAmount);
                    totalExpiredUsed = totalExpiredUsed.add(expiredUsedAmount);
                }
                unsubInquiryPriceVO.setExpiredUsedAmount(totalExpiredUsed);
            }
        }
        return BeanConvertUtil.convert(unsubInquiryPriceVO, UnsubscribeResponse.class);
    }

    private void summaryProductPrice(List<ResourceDetailVO> details) {
        Map<String, List<ResourceDetailVO>> detailMap =  details.stream().collect(
                Collectors.groupingBy(ResourceDetailVO::getProductCode));

        List<ResourceDetailVO> resourceDetailVOs = Lists.newArrayList();
        detailMap.forEach((key, value) -> {
            ResourceDetailVO detail = null;
            for (ResourceDetailVO detailVO : value) {
                if (Objects.isNull(detail)) {
                    detail = detailVO;
                } else {
                    detail.setTotalCashAmount(NumberUtil.add(detail.getTotalCashAmount(), detailVO.getTotalCashAmount()));
                    detail.setTotalCouponAmount(NumberUtil.add(detail.getTotalCouponAmount(), detailVO.getTotalCouponAmount()));
                    detail.setTotalCouponDiscount(NumberUtil.add(detail.getTotalCouponDiscount(), detailVO.getTotalCouponDiscount()));
                    detail.setTotalCreditAmount(NumberUtil.add(detail.getTotalCreditAmount(), detailVO.getTotalCreditAmount()));
                    detail.setTotalOrgDiscount(NumberUtil.add(detail.getTotalOrgDiscount(), detailVO.getTotalOrgDiscount()));
                    detail.setTotalOriginalCost(NumberUtil.add(detail.getTotalOriginalCost(), detailVO.getTotalOriginalCost()));
                    detail.setTotalPayment(NumberUtil.add(detail.getTotalPayment(), detailVO.getTotalPayment()));
                    detail.setTotalUsedAmount(NumberUtil.add(detail.getTotalUsedAmount(), detailVO.getTotalUsedAmount()));
                    detail.setTotalUsedCashAmount(NumberUtil.add(detail.getTotalUsedCashAmount(), detailVO.getTotalUsedCashAmount()));
                    detail.setTotalUsedCouponAmount(NumberUtil.add(detail.getTotalUsedCouponAmount(), detailVO.getTotalUsedCouponAmount()));
                    detail.setTotalUsedCreditAmount(NumberUtil.add(detail.getTotalUsedCreditAmount(), detailVO.getTotalUsedCreditAmount()));

                    detail.setUnsubAmount(NumberUtil.add(detail.getUnsubAmount(), detailVO.getUnsubAmount()));
                    detail.setUnsubCashAmount(NumberUtil.add(detail.getUnsubCashAmount(), detailVO.getUnsubCashAmount()));
                    detail.setUnsubCouponAmount(NumberUtil.add(detail.getUnsubCouponAmount(), detailVO.getUnsubCouponAmount()));
                    detail.setUnsubCreditAmount(NumberUtil.add(detail.getUnsubCreditAmount(), detailVO.getUnsubCreditAmount()));

                    if (Objects.isNull(detail.getExpiredUsedAmount()) && Objects.nonNull(detailVO.getExpiredUsedAmount())) {
                        detail.setExpiredUsedAmount(BigDecimalUtil.getTwoPointAmount(detailVO.getExpiredUsedAmount()));
                    }else if(Objects.nonNull(detail.getExpiredUsedAmount()) && Objects.nonNull(detailVO.getExpiredUsedAmount())){
                        detail.setExpiredUsedAmount(NumberUtil.add(BigDecimalUtil.getTwoPointAmount(detail.getExpiredUsedAmount()), BigDecimalUtil.getTwoPointAmount(detailVO.getExpiredUsedAmount())));
                    }
                    if (Objects.isNull(detail.getExpiredUsedCashAmount()) && Objects.nonNull(detailVO.getExpiredUsedCashAmount())) {
                        detail.setExpiredUsedCashAmount(detailVO.getExpiredUsedCashAmount());
                    }else if(Objects.nonNull(detail.getExpiredUsedCashAmount()) && Objects.nonNull(detailVO.getExpiredUsedCashAmount())){
                        detail.setExpiredUsedCashAmount(NumberUtil.add(detail.getExpiredUsedCashAmount(), detailVO.getExpiredUsedCashAmount()));
                    }
                    if (Objects.isNull(detail.getExpiredUsedCouponAmount()) && Objects.nonNull(detailVO.getExpiredUsedCouponAmount())) {
                        detail.setExpiredUsedCouponAmount(detailVO.getExpiredUsedCouponAmount());
                    }else if(Objects.nonNull(detail.getExpiredUsedCouponAmount()) && Objects.nonNull(detailVO.getExpiredUsedCouponAmount())){
                        detail.setExpiredUsedCouponAmount(NumberUtil.add(detail.getExpiredUsedCouponAmount(), detailVO.getExpiredUsedCouponAmount()));
                    }
                    if (Objects.isNull(detail.getExpiredUsedCreditAmount()) && Objects.nonNull(detailVO.getExpiredUsedCreditAmount())) {
                        detail.setExpiredUsedCreditAmount(detailVO.getExpiredUsedCreditAmount());
                    }else if(Objects.nonNull(detail.getExpiredUsedCreditAmount()) && Objects.nonNull(detailVO.getExpiredUsedCreditAmount())){
                        detail.setExpiredUsedCreditAmount(NumberUtil.add(detail.getExpiredUsedCreditAmount(), detailVO.getExpiredUsedCreditAmount()));
                    }
                }
            }
            resourceDetailVOs.add(detail);
        });
        details.clear();
        details.addAll(resourceDetailVOs);
    }
    /**
     * AI专属资源池的退订询价
     *
     * @param id id
     * @return {@code UnsubscribeInquiryPriceResponse}
     */
    @AuthorizeBss(action = AuthModule.BD.BD01.MA_RELEASE)
    @GetMapping("/ai/unsubscribe/inquiry/price/{id}")
    @DataPermission(resource = OperationResourceEnum.UNSUBSCRIBE_AI_INQUIRYPRICE,bizId = "#id")
    public UnsubscribeInquiryPriceResponse unsubscribeAIInquiryPrice(@PathVariable("id") String id) {
        UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = inquiryPriceService.unsubInquiryAIPrice(id);
        return BeanConvertUtil.convert(unsubscribeInquiryPriceVO, UnsubscribeInquiryPriceResponse.class);
    }

    /**
     * AI专属资源池的退订询价
     *
     * @param id id
     * @return {@code UnsubscribeInquiryPriceResponse}
     */
    @AuthorizeBss(action = AuthModule.BD.BD01.MA_RELEASE)
    @GetMapping("/res/unsubscribe/inquiry/price/{id}")
    @DataPermission(resource = OperationResourceEnum.UNSUBSCRIBE_AI_INQUIRYPRICE,bizId = "#id")
    public UnsubscribeInquiryPriceResponse unsubscribeRESInquiryPrice(@PathVariable("id") String id) {
        UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = inquiryPriceService.unsubInquiryResPrice(id);
        return BeanConvertUtil.convert(unsubscribeInquiryPriceVO, UnsubscribeInquiryPriceResponse.class);
    }


    /**
     * [INNER API] AI专属资源池的退订询价
     *
     * @param id id
     * @return {@code UnsubscribeInquiryPriceResponse}
     */
    @RejectCall
    @GetMapping("/ai/unsubscribe/inquiry/price/fegin/{id}")
    public UnsubscribeInquiryPriceResponse unsubscribeAIInquiryPriceFegin(@PathVariable("id") String id) {
        UnsubscribeInquiryPriceVO unsubscribeInquiryPriceVO = inquiryPriceService.unsubInquiryAIPrice(id);
        return BeanConvertUtil.convert(unsubscribeInquiryPriceVO, UnsubscribeInquiryPriceResponse.class);
    }

    /**
     * 修改查询价格
     *
     * @param request 变更询价请求体
     * @return {@code ModifyQueryPriceResponse}
     */
    @AuthorizeBss(action = BD04.BD040202 + "," + AuthModule.COMMON.PUBLIC.C1.C115)
    @GetMapping("/modify/inquiry/price")
    @DataPermission(resource = OperationResourceEnum.MODIFY_INQUIRY_PRICE,bizId = "#request.id")
    public RestResult modifyInquiryPrice(@Valid ModifyInquiryPriceRequest request) {
        if ((
                ProductCodeEnum.ECS.getProductType().equals(request.getTargetType())
                        || ProductCodeEnum.DCS.getProductType().equals(request.getTargetType())
        ) && Objects.isNull(
                request.getTargetSpec())) {
            return new RestResult(Status.FAILURE, null);
        }
        return new RestResult(BeanConvertUtil.convert(
                inquiryPriceService.modifyInquiryPrice(request),
                ModifyQueryPriceResponse.class));
    }
    /**
     * 修改查询价格
     *【Since v2.5.0】
     * [INNER API] 修改查询价格
     * @param request 变更询价请求体
     * @return {@code ModifyQueryPriceResponse}
     */
    @RejectCall
    @GetMapping("/modify/inquiry/price/feign")
    public ModifyQueryPriceResponse modifyInquiryPriceOss(@Valid ModifyInquiryPriceRequest request) {
        if (StringUtils.isNotBlank(request.getTargetType())) {
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(request.getTargetType());
            if (!Objects.equals(serviceCategory.getEntityId(), RequestContextUtil.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
        }
        // 代客下单 使用客户信息操作订单
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(request.getUserOrgSid()),
                                                                                                            cn.com.cloudstar.rightcloud.oss.common.pojo.Org.class));
        }
        ModifyInquiryPriceVO modifyInquiryPriceVO = inquiryPriceService.modifyInquiryPrice(request);
        //  modifyInquiryPriceVO.setPrice(modifyInquiryPriceVO.getPrice().multiply(modifyInquiryPriceVO.getPlatformDiscount()));
        // 重新获取当前管理员信息
        if (Objects.nonNull(request.getBehalfUserSid())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(request.getBehalfUserSid()), cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder.setAuthUser(authUserInfo);
        }
        return BeanConvertUtil.convert(modifyInquiryPriceVO, ModifyQueryPriceResponse.class);
    }
}
