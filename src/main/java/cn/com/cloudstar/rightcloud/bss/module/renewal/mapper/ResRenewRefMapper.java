/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.renewal.mapper;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageSerializable;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.ResRenewRef;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.RenewalDTO;
import cn.com.cloudstar.rightcloud.oss.common.holder.DubboHolder;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResAllDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResAllParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;

@Repository
public interface ResRenewRefMapper {

    /**
     * 根据条件查询记录总数
     */
    int countByParams(Criteria example);

    /**
     * 根据条件删除记录
     */
    int deleteByParams(Criteria example);

    /**
     * 根据主键删除记录
     */
    int deleteByPrimaryKey(Long renewSid);

    /**
     * 保存记录,不管记录里面的属性是否为空
     */
    int insert(ResRenewRef record);

    /**
     * 保存属性不为空的记录
     */
    int insertSelective(ResRenewRef record);

    /**
     * 根据条件查询记录集
     */
    List<ResRenewRef> selectByParams(Criteria example);

    /**
     * 根据主键查询记录
     */
    ResRenewRef selectByPrimaryKey(Long renewSid);

    /**
     * 根据条件更新属性不为空的记录
     */
    int updateByParamsSelective(@Param("record") ResRenewRef record, @Param("condition") Map<String, Object> condition);

    /**
     * 根据条件更新记录
     */
    int updateByParams(@Param("record") ResRenewRef record, @Param("condition") Map<String, Object> condition);

    /**
     * 根据主键更新属性不为空的记录
     */
    int updateByPrimaryKeySelective(ResRenewRef record);

    /**
     * 根据主键更新记录
     */
    int updateByPrimaryKey(ResRenewRef record);

    List<RenewalDTO> selectRenewDB(@Param("resAllDTOS")List<ResAllDTO> resAllDTOS);

    default List<RenewalDTO> selectRenew(Criteria criteria){
        DubboHolder bean = SpringContextHolder.getBean(DubboHolder.class);
        ResAllRemoteService resAllRemoteService = bean.resAllRemoteService;
        ResAllParams resAllParams = criteria.toRemoteRequest(ResAllParams.class);
        //主表 分页
        PageSerializable<ResAllDTO> resAllDTOS = resAllRemoteService.selectResAllByExample(resAllParams);
        if (CollectionUtils.isEmpty(resAllDTOS.getList())) {
            return new Page<>();
        }
        List<RenewalDTO> renewalDTOS = selectRenewDB(resAllDTOS.getList());
        Map<String, ResAllDTO> collect = resAllDTOS.getList().stream()
                                                   .collect(Collectors.toMap(ResAllDTO::getId, Function.identity()));
        renewalDTOS.forEach(a->{
            ResAllDTO resAllDTO = collect.get(a.getId());
            BeanUtil.copyProperties(resAllDTO, a,
                                    CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        });
        Page<RenewalDTO> result = new Page<>();
        result.setTotal(resAllDTOS.getTotal());
        result.addAll(renewalDTOS);
        return result;
    }

    RenewalDTO selectSimpleByPrimaryKey(@Param("id") String id,@Param("productType")String productType);

    @Select("select size from rightcloud.res_sfs_turbo where id = #{id};")
    String selectSizeById(@Param("id") Long id);

    @Select("select storage from rightcloud.res_rds where id = #{id};")
    String selectRdsSizeById(@Param("id") Long id);
}