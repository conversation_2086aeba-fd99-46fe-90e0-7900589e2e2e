/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导出账单明细
 */
@Data
public class AsyncExportBaseModel implements Serializable {

    /**
     * 订单来源
     */
    @ExcelProperty(value = "订单来源",order = 13)
    protected String orderSourceSn;
    /**
     * 使用量
     */
    @ExcelProperty(value = "资源用量",order = 10)
    protected String usageCount;
    /**
     * 资源用量单位
     */
    @ExcelProperty(value = "资源用量单位",order = 17)
    protected String usageUnit;
    /**
     * 资源使用量类型，size 大小，duration 时长，quantity 数量
     */
    @ExcelProperty(value = "资源使用量类型",order = 17)
    protected String usageType;
    /**
     * 单价
     */
    @ExcelProperty(value = "价格",order = 17)
    protected BigDecimal price;
    /**
     * 价格单位
     */
    @ExcelProperty(value = "价格单位",order = 17)
    protected String unit;


}

