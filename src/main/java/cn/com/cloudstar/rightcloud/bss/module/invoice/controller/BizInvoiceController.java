/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceMethodEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PriceTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DistributorAuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.vo.BillBillingCostVo;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IBizBillingCycleService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoice;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoiceSetting;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InstanceGaapCostVO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InvoiceDTO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InvoiceSettingDTO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.CreateInvoiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeDepositDetailRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeDepositWithResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeInvoicesRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.UpdateInvoiceSettingRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.UpdateInvoiceStatusRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response.DescribeDepositWithResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response.DescribeInvoiceDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response.DescribeInvoiceResponse;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.response.DescribeInvoiceSettingResponse;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceSettingService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BC.BC04;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.operationlog.util.OperationLogMdcUtil;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;

import static cn.com.cloudstar.rightcloud.bss.common.constants.CodeConstants.IL8T_KEY;
import static cn.com.cloudstar.rightcloud.bss.common.constants.CodeConstants.IL8T_VALUE;
import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.CC;

/**
 * 运营_发票表
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
@RestController
@RequestMapping("/invoice")
@Slf4j
public class BizInvoiceController {

    /**
     * accountId
     */
    private static final String ACCOUNT_ID = "accountId";

    /**
     * entityId
     */
    private static final String ENTITY_ID = "entityId";

    private static final Map<String, String> INVOICE_TYPE = Maps.newHashMap();

    static {
        INVOICE_TYPE.put("0", "个人普通发票");
        INVOICE_TYPE.put("1", "企业增值税专用发票");
        INVOICE_TYPE.put("2", "企业增值税普通发票");
    }


    @Autowired
    private IBizInvoiceService bizInvoiceServiceImpl;

    @Autowired
    private IBizBillingAccountService iBizBillingAccountServiceImp;

    @Autowired
    private IBizInvoiceSettingService bizInvoiceSettingServiceImpl;

    @Autowired
    private IBizDistributorService distributorService;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private FeignService feignService;

    @Autowired
    private OrgService orgService;


    @Autowired
    private IBizBillingCycleService billingCycleService;

    @Autowired
    @Lazy
    private ExportService exportService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    /**
     * 获取用户发票设置信息
     *
     * @return {@code DescribeInvoiceSettingResponse}
     */
    @AuthorizeBss(action = CC.CC05)
    @GetMapping("/setting")
    @ApiOperation("获取用户发票设置信息")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'发票设置信息'", resource = OperationResourceEnum.INVOICE_SETTING, tagNameUs ="'Invoice Settings Information'")
    @ListenExpireBack
    public DescribeInvoiceSettingResponse getInvoiceSetting(@NotBlank @Param("accountId") String accountId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        assert authUserInfo != null;
        Long userSid = authUserInfo.getUserSid();
        if (Objects.nonNull(authUserInfo.getOrgSid())) {
            // 子用户查看租户账户
            if (Objects.nonNull(authUserInfo.getParentSid())) {
                userSid = authUserInfo.getParentSid();
            }
            // 发票给默认运营实体账户
            BizBillingAccount account = iBizBillingAccountServiceImp.getByEntityIdAndUserId(1L, userSid);
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ORGANIZATION_ERROR_00001));
            }
            if (!org.apache.commons.lang3.StringUtils.equals(accountId, account.getId().toString())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        InvoiceSettingDTO invoiceSettingDTO = bizInvoiceSettingServiceImpl.selectByAccountID(accountId);
        DescribeInvoiceSettingResponse invoice = BeanConvertUtil.convert(invoiceSettingDTO, DescribeInvoiceSettingResponse.class);
        assert invoice != null;
        //计算开票金额
        if (Objects.nonNull(accountId)) {
            BizBillingAccount bizBillingAccount = iBizBillingAccountServiceImp.getById(accountId);
            invoice.setTotalRechargeAmount(bizBillingAccount.getTotalRechargeAmount());
            invoice.setInvoicedAmount(bizBillingAccount.getInvoicedAmount());
            invoice.setOfflineInvoicedAmount(bizBillingAccount.getOfflineInvoicedAmount());
            BigDecimal balance = bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) > 0 ? bizBillingAccount.getBalance() : BigDecimal.ZERO;
            balance = BigDecimalUtil.remainTwoPointAmount(balance);

            invoice.setBillInvoicableAmount(
                    bizBillingAccount.getTotalRechargeAmount()
                                     .subtract(bizBillingAccount.getInvoicedAmount())
                                     .subtract(bizBillingAccount.getOfflineInvoicedAmount())
                                     .subtract(balance));
            invoice.setRechargeInvoicableAmount(
                    bizBillingAccount.getTotalRechargeAmount()
                                     .subtract(bizBillingAccount.getInvoicedAmount())
                                     .subtract(bizBillingAccount.getOfflineInvoicedAmount()));
        }

        invoice.setBankAccount(CrytoUtilSimple.decrypt(invoice.getBankAccount()));
        invoice.setRegisterPhone(CrytoUtilSimple.decrypt(invoice.getRegisterPhone()));
        invoice.setRegisterAddress(CrytoUtilSimple.decrypt(invoice.getRegisterAddress()));
        invoice.setBankAccount(DesensitizedUtil.bankCard(invoice.getBankAccount()));
        DesensitizationUtil.desensitization(invoice);
        return invoice;
    }

    /**
     * 修改发票配置信息
     *
     * @param request 修改发票设置信息请求体
     *
     * @return {@code RestResult}
     */
    @PutMapping("/setting")
    @ApiOperation("修改发票设置信息")
    @AuthorizeBss(action = CC.CC0501)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'发票信息'", bizId = "#request.accountId", resource = OperationResourceEnum.UPDATE_INVOICE_SETTING, tagNameUs ="'Invoice information'")
    @ListenExpireBack
    public RestResult updateInvoiceSetting(@RequestBody @Valid UpdateInvoiceSettingRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long accountId = null;
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        Long userSid = authUserInfo.getUserSid();
        if (!Objects.isNull(authUserInfo.getOrgSid())) {
            if (Objects.nonNull(authUserInfo.getOrgSid())) {
                if (Objects.nonNull(authUserInfo.getParentSid())) {
                    // 子用户查看租户账户
                    userSid = authUserInfo.getParentSid();
                }
            }
            //查询accountId
            // 发票给默认运营实体账户
            BizBillingAccount account = iBizBillingAccountServiceImp.getByEntityIdAndUserId(1L, userSid);
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ORGANIZATION_ERROR_00001));
            }
            accountId = account.getId();
        }
        InvoiceDTO invoiceDTO = bizInvoiceServiceImpl.selectInvoice(accountId);
        if (!("0").equals(request.getInvoiceType()) && !("1").equals(request.getFlag())) {
            if (StringUtils.isBlank(request.getBankAccount()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(invoiceDTO.getBankAccount()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1332437736));
            }
            if (StringUtils.isBlank(request.getRegisterPhone()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(
                    invoiceDTO.getRegisterPhone()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_511838200));
            }
            if (StringUtils.isNotBlank(request.getRegisterPhone()) && !Validator.isMobile(request.getRegisterPhone())) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000339912));
            }
            if (StringUtils.isBlank(request.getRegisterAddress()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(
                    invoiceDTO.getRegisterAddress()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1086825866));
            }
        } else if (("1").equals(request.getFlag())) {
            if (StringUtils.isBlank(request.getAddress()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(invoiceDTO.getAddress()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_68099290));
            }
            if (StringUtils.isBlank(request.getPhone()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(invoiceDTO.getPhone()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_641213096));
            }
            if (StringUtils.isBlank(request.getReceiver()) && (Objects.isNull(invoiceDTO) || StringUtils.isBlank(invoiceDTO.getReceiver()))) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1456543332));
            }
            if (StringUtils.isBlank(request.getPostCode())) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1143981108));
            }
        }
        request.setAccountId(accountId.toString());
        if (!StringUtil.isNullOrEmpty(request.getBankAccount())) {
            // 银行账号加密存储
            if (!CCSPCacheUtil.ccspServiceOpen()) {
                request.setBankAccount(CrytoUtilSimple.encrypt(request.getBankAccount()));
            }
        }
        if (!StringUtil.isNullOrEmpty(request.getRegisterPhone())) {
            if (!CCSPCacheUtil.ccspServiceOpen()) {
                request.setRegisterPhone(CrytoUtilSimple.encrypt(request.getRegisterPhone()));
            }
        }
        if (!ObjectUtils.isEmpty(request.getRegisterAddress())) {
            request.setRegisterAddress(CrytoUtilSimple.encrypt(request.getRegisterAddress()));
        }
        BizInvoiceSetting convert = BeanConvertUtil.convert(request, BizInvoiceSetting.class);
        WebUserUtil.prepareUpdateParams(convert);
        if (bizInvoiceSettingServiceImpl.updateInvoiceSetting(convert)) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2005429841));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2005485260));
        }
    }

    /**
     * 修改邮件地址信息
     *
     * @param request 修改发票设置信息请求体
     *
     * @return {@code RestResult}
     */
    @PutMapping("/mailAddress/setting")
    @ApiOperation("修改邮件地址信息")
    @AuthorizeBss(action = CC.CC0502)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'邮寄地址信息'", bizId = "#request.accountId", resource = OperationResourceEnum.UPDATE_INVOICE_MAIL_SETTING, tagNameUs ="'Mailing address information'")
    @ListenExpireBack
    public RestResult updateInvoiceMailAddressSetting(@RequestBody @Valid UpdateInvoiceSettingRequest request) {

        try {
            RestResult restResult = updateInvoiceSetting(request);
            //铭感信息脱敏
            request.setAddress(DataProcessingUtil.processing(request.getAddress(), DataProcessingUtil.ADDRESS));
            request.setPhone(DataProcessingUtil.processing(request.getPhone(), DataProcessingUtil.PHONE));
            request.setReceiver(DataProcessingUtil.processing(request.getReceiver(), DataProcessingUtil.NAME));
            request.setRegisterPhone(DataProcessingUtil.processing(request.getRegisterPhone(), DataProcessingUtil.PHONE));
            request.setRegisterAddress(DataProcessingUtil.processing(request.getRegisterAddress(), DataProcessingUtil.ADDRESS));
            return restResult;
        } catch (Exception e) {
            e.printStackTrace();
            //铭感信息脱敏
            request.setAddress(DataProcessingUtil.processing(request.getAddress(), DataProcessingUtil.ADDRESS));
            request.setPhone(DataProcessingUtil.processing(request.getPhone(), DataProcessingUtil.PHONE));
            request.setReceiver(DataProcessingUtil.processing(request.getReceiver(), DataProcessingUtil.NAME));
            request.setRegisterPhone(DataProcessingUtil.processing(request.getRegisterPhone(), DataProcessingUtil.PHONE));
            request.setRegisterAddress(DataProcessingUtil.processing(request.getRegisterAddress(), DataProcessingUtil.ADDRESS));
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1679285401));
        }
    }

    /**
     * 获取资源账单金额
     *
     * @param request 获取用户可用充值记录请求体
     *
     * @return {@code DescribeDepositForInvoiceResponse}
     */
    @GetMapping("/deposit_resource")
    @ApiOperation("获取资源账单金额")
    @AuthorizeBss(action = CC.CC0503 + StrUtil.COMMA + CC.CC0507)
    public DescribeDepositWithResourceResponse selectDepositWithResource(@Valid DescribeDepositWithResourceRequest request) {
        DescribeDepositWithResourceResponse response = new DescribeDepositWithResourceResponse();
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        BizBillingAccount bizBillingAccount = iBizBillingAccountServiceImp.getById(request.getAccountId());
        if (bizBillingAccount == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ORGANIZATION_ERROR_00001));
        }
        if (!bizBillingAccount.getOrgSid().equals(authUserInfo.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
        }

        List<InstanceGaapCostVO> instanceGaapCosts = bizInvoiceServiceImpl.selectDepositWithResource(request);
        instanceGaapCosts.forEach(instanceGaapCostVO -> {
            instanceGaapCostVO.setPriceTypeName(WebUtil.getHeaderAcceptLanguage() ? PriceTypeEnum.nameFromCode(instanceGaapCostVO.getPriceTypeName()) : instanceGaapCostVO.getPriceTypeName());
            instanceGaapCostVO.setProductName(WebUtil.getHeaderAcceptLanguage() && IL8T_KEY.equals(instanceGaapCostVO.getProductName()) ?  IL8T_VALUE : instanceGaapCostVO.getProductName());
        });
        response.setDataList(instanceGaapCosts);
        response.setTotalSize((long) instanceGaapCosts.size());
        return response;
    }

    /**
     * 获取资源账单明细
     *
     * @param request 获取用户可用充值记录请求体
     *
     * @return {@code DescribeDepositForInvoiceResponse}
     */
    @PostMapping("/deposit_detail")
    @ApiOperation("获取资源账单")
    @AuthorizeBss(action = CC.CC0503 + StrUtil.COMMA + CC.CC0507)
    public IPage<BillBillingCostVo> selectDepositDetail(@RequestBody @Valid DescribeDepositDetailRequest request) {
        //获取账单
        return billingCycleService.listBillCycle(request);
    }


    /**
     * 获取发票详情
     *
     * @param invoiceSid 发票sid
     *
     * @return {@code DescribeInvoiceDetailResponse}
     */
    @GetMapping("/detail/{invoiceSid}")
    @ApiOperation("获取发票详情")
    @AuthorizeBss(action = CC.CC05)
    @DataPermission(resource = OperationResourceEnum.SELECT_INVOICE_DETAIL, bizId = "#invoiceSid")
    @ListenExpireBack
    public DescribeInvoiceDetailResponse selectDetailInvoice(@NotNull @PathVariable Long invoiceSid) {
        DescribeInvoiceDetailResponse convert = BeanConvertUtil.convert(
                bizInvoiceServiceImpl.selectDetailInvoice(invoiceSid), DescribeInvoiceDetailResponse.class);
        //做脱敏处理
        if (convert != null) {
            if (convert.getBankAccount() != null) {
                convert.setBankAccount(DesensitizedUtil.bankCard(convert.getBankAccount()));
            }
            if (convert.getRegisterAddress() != null) {
                convert.setRegisterAddress(DesensitizedUtil.address(convert.getRegisterAddress(), convert.getRegisterAddress().length() / 2));
            }
            if (convert.getRegisterPhone() != null) {
                convert.setRegisterPhone(DesensitizedUtil.mobilePhone(convert.getRegisterPhone()));
            }
            if (convert.getAddress() != null) {
                convert.setAddress(DesensitizedUtil.address(convert.getAddress(), convert.getAddress().length() / 2));
            }
            if (convert.getReceiver() != null) {
                convert.setReceiver(DesensitizedUtil.chineseName(convert.getReceiver()));
            }
            if (convert.getEmail() != null) {
                convert.setEmail(DesensitizedUtil.email(convert.getEmail()));
            }
            if (convert.getPhone() != null) {
                convert.setPhone(DesensitizedUtil.mobilePhone(convert.getPhone()));
            }
        }

        return convert;
    }

    /**
     * [INNER API] 获取发票详情
     *
     * @param invoiceSid 发票sid
     *
     * @return {@code DescribeInvoiceDetailResponse}
     */
    @RejectCall
    @GetMapping("/detail/feign/{invoiceSid}")
    @ApiOperation("获取发票详情")
    @ListenExpireBack
    @DataPermission(resource = OperationResourceEnum.SELECT_INVOICE_DETAIL, bizId = "#invoiceSid")
    public RestResult<DescribeInvoiceDetailResponse> selectDetailInvoiceByFeign(@NotNull @PathVariable Long invoiceSid) {
        BizInvoice invoice = bizInvoiceServiceImpl.getById(invoiceSid);
        if (Objects.isNull(invoice)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        if (!Objects.equals(invoice.getEntityId(), RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        if (StringUtils.isNotBlank(authUserInfo.getUserType()) && UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
            String accountId = invoice.getAccountId();
            BizBillingAccount account = iBizBillingAccountServiceImp.getById(accountId);
            Org org = orgService.getById(account.getOrgSid());
            if (!org.getTreePath().contains(String.valueOf(authUserInfo.getOrgSid()))) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
            }
        }
        DescribeInvoiceDetailResponse convert = BeanConvertUtil.convert(
                bizInvoiceServiceImpl.selectDetailInvoice(invoiceSid), DescribeInvoiceDetailResponse.class);
        //做脱敏处理
        if (convert != null) {
            if (convert.getBankAccount() != null) {
                convert.setBankAccount(DesensitizedUtil.bankCard(CrytoUtilSimple.decrypt(convert.getBankAccount())));
            }
            if (convert.getRegisterAddress() != null) {
                convert.setRegisterAddress(DesensitizedUtil.address(convert.getRegisterAddress(), convert.getRegisterAddress().length() / 2));
            }
            if (convert.getRegisterPhone() != null) {
                convert.setRegisterPhone(DesensitizedUtil.mobilePhone(convert.getRegisterPhone()));
            }
            if (convert.getAddress() != null) {
                convert.setAddress(DesensitizedUtil.address(convert.getAddress(), convert.getAddress().length() / 2));
            }
            if (convert.getReceiver() != null) {
                convert.setReceiver(DesensitizedUtil.chineseName(convert.getReceiver()));
            }
            if (convert.getEmail() != null) {
                convert.setEmail(DesensitizedUtil.email(convert.getEmail()));
            }
            if (convert.getPhone() != null) {
                convert.setPhone(DesensitizedUtil.mobilePhone(convert.getPhone()));
            }
        }
        DesensitizationUtil.desensitization(convert);
        CrytoUtilSimple.decrypt(convert.getBankAccount());
        return new RestResult(convert);
    }

    /**
     * 修改发票状态
     *
     * @param request 请求
     *
     * @return {@code RestResult}
     */
    @PutMapping("/status")
    @ApiOperation("修改发票状态")
    @AuthorizeBss(action = BC04.BC04_COMMON)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'发票'", bizId = "#request.invoiceSid", resource = OperationResourceEnum.MODIFY_INVOICE_STATUS, tagNameUs ="'Invoice'")
    @ListenExpireBack
    public RestResult updateInvoiceStatus(@RequestBody @Valid UpdateInvoiceStatusRequest request) {
        BizInvoice convert = BeanConvertUtil.convert(request, BizInvoice.class);
        WebUserUtil.prepareUpdateParams(convert);
        if (!ObjectUtils.isEmpty(request.getInvoiceStatus())) {
            switch (request.getInvoiceStatus()) {
                case "rejected":
                    OperationLogMdcUtil.saveContent("驳回申请");
                    break;
                case "done":
                    OperationLogMdcUtil.saveContent("同意申请");
                    break;
            }
        }
        boolean flag = bizInvoiceServiceImpl.updateInvoiceStatus(convert);
        if (flag) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1303424988));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1303369569));
        }
    }

    /**
     * 申请发票
     *
     * @param request 申请发票请求体
     *
     * @return {@code RestResult}
     */
    @PostMapping
    @ApiOperation("申请发票")
    @AuthorizeBss(action = CC.CC0503)
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'开票'", bizId = "#request.ids", param = "#request.invoiceType", resource = OperationResourceEnum.CREATE_INVOICE, tagNameUs ="'Invoicing'")
    @DataPermission(resource = OperationResourceEnum.CREATE_INVOICE, bizId = "#request.accountId")
    @ListenExpireBack
    public RestResult createInvoice(@Valid @RequestBody CreateInvoiceRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        if (StrUtil.isBlank(request.getAccountId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        BizBillingAccount account = iBizBillingAccountServiceImp.getById(request.getAccountId());
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!account.getAdminSid().equals(authUserInfo.getUserSid()) && !account.getAdminSid().equals(authUserInfo.getParentSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        BigDecimal invoiceableAmount =
                account.getTotalRechargeAmount().subtract(account.getInvoicedAmount()).subtract(account.getOfflineInvoicedAmount());
        if (InvoiceMethodEnum.BILLING.getCode().equals(request.getInvoiceMethod())) {
            BigDecimal balance = account.getBalance().compareTo(BigDecimal.ZERO) > 0 ? account.getBalance() : BigDecimal.ZERO;
            balance = BigDecimalUtil.remainTwoPointAmount(balance);
            invoiceableAmount = invoiceableAmount.subtract(balance);
        }
        if (invoiceableAmount.setScale(3, BigDecimal.ROUND_HALF_UP).compareTo(request.getDepositeAmount().setScale(3, BigDecimal.ROUND_HALF_UP))
                < 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.INVOICING_AMOUNT_THAN));
        }
        // 获取发票信息
        BizInvoice invoice = BeanConvertUtil.convert(request, BizInvoice.class);
        WebUserUtil.prepareInsertParams(invoice);

        BizInvoiceSetting invoiceSetting = bizInvoiceSettingServiceImpl.getById(request.getId());
        if (Objects.isNull(invoiceSetting)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        invoice.setBankAccount(CrytoUtilSimple.decrypt(invoiceSetting.getBankAccount()));
        invoice.setRegisterAddress(CrytoUtilSimple.decrypt(invoiceSetting.getRegisterAddress()));
        invoice.setRegisterPhone(CrytoUtilSimple.decrypt(invoiceSetting.getRegisterPhone()));
        invoice.setAddress(CrytoUtilSimple.decrypt(invoiceSetting.getAddress()));
        invoice.setReceiver(CrytoUtilSimple.decrypt(invoiceSetting.getReceiver()));
        invoice.setPhone(CrytoUtilSimple.decrypt(invoiceSetting.getPhone()));

        invoice.setDepositeAmount(request.getDepositeAmount());
        invoice.setInvoiceStatus(InvoiceStatusEnum.PENDING.getCode());

        String now = String.valueOf(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        String lockKey = "invoice:lock:" + request.getAccountId();
        boolean lock = JedisUtil.instance().setnx(lockKey, now, 30);
        boolean tag;
        try {
            if (lock) {
                tag = bizInvoiceServiceImpl.applyInvoice(invoice, request.getIds());
            } else {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1065179564));
            }
        } finally {
            if (lock && now.equals(JedisUtil.instance().get(lockKey))) {
                JedisUtil.instance().del(lockKey);
            }
        }
        if (tag) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_317316374));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_317371793));
        }
    }

    /**
     * 获取发票列表
     *
     * @param request 获取发票列表请求体
     * @param moduleType 模块类型
     *
     * @return {@code IPage<DescribeInvoiceResponse>}
     */
    @AuthorizeBss(action = CC.CC05)
    @GetMapping
    @ApiOperation("获取发票列表")
    @Authorize(action = "billcenter:invoices:ListInvoices")
    @ListenExpireBack
    public IPage<DescribeInvoiceResponse> selectInvoices(DescribeInvoicesRequest request, @RequestHeader String moduleType) {
        if (StringUtils.isBlank(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_18819451));
        }
        if (!Arrays.asList(Constants.BSS, Constants.CONSOLE).contains(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1319710067));
        }
        /* 创建查询对象*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.getCondition().put("entityId",RequestContextUtil.getEntityId());

        if (!Objects.isNull(request.getPagenum()) || !Objects.isNull(request.getPagesize())) {
            long offset = (request.getPagenum() - 1) * request.getPagesize();
            criteria.getCondition().put("pagenum", offset);
        }
        /* 获取当前登录用户信息*/
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        /* 检查请求头和用户信息*/
        if ("console".equals(moduleType)) {
            if (authUserInfo == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }
            if (Objects.isNull(authUserInfo.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1271298302));
            }
            //查询accountId
            BizBillingAccount account = iBizBillingAccountServiceImp.getById(request.getAccountId());
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1945067902));
            }
            if (!account.getAdminSid().equals(authUserInfo.getUserSid()) && !account.getAdminSid().equals(authUserInfo.getParentSid())) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(
                        cn.com.cloudstar.rightcloud.oss.common.constants.RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil
                        .getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            Long id = account.getId();
            if (Objects.isNull(id)) {
                return null;
            }
            criteria.getCondition().put(ACCOUNT_ID, String.valueOf(id));
        } else {
            List<Long> accountIds = new ArrayList<>();
            List<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> currentRoleList =
                    roleMapper.findRolesByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
            List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList =
                    BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
            String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
            if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equalsIgnoreCase(maxScope)) {
                QueryWrapper<BizBillingAccount> qwer = new QueryWrapper();
                qwer.eq("entity_id", RequestContextUtil.getEntityId());
                qwer.eq("distributor_id", RequestContextUtil.getAuthUserInfo().getUserSid());
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(qwer);
                if (CollectionUtil.isNotEmpty(accounts)) {
                    for (BizBillingAccount ac : accounts) {
                        accountIds.add(ac.getId());
                    }
                }
                criteria.getCondition().put("accountIds", accountIds);

            } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)) {
                QueryWrapper<BizBillingAccount> qwer = new QueryWrapper();
                qwer.eq("entity_id", RequestContextUtil.getEntityId());
                qwer.eq("salesmen_id", RequestContextUtil.getAuthUserInfo().getUserSid());
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(qwer);
                if (CollectionUtil.isNotEmpty(accounts)) {
                    for (BizBillingAccount ac : accounts) {
                        accountIds.add(ac.getId());
                    }
                }
                criteria.getCondition().put("accountIds", accountIds);
            }
        }

        /* 获取当前登录用户*/
        User user = AuthUtil.getAuthUser();

        /* 分销商用户查询*/
        RestResult restResult = DistributorAuthUtil.put(criteria, null, user, 2);
        if (restResult == null) {
            return PageUtil.emptyPage();
        }

        // 配置默认排序
        if (Strings.isNullOrEmpty(request.getSortorder())) {
            if (authUserInfo == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }

            // 普通用户时间逆序
            criteria.setOrderByClause("A.created_dt desc");

            if (authUserInfo.getAdminFlag() && "bss".equals(moduleType)) {

                // 管理员查询，以状态，时间排序
                criteria.setOrderByClause("A.created_dt desc");
            }
        }
        if (StrUtil.isNotBlank(request.getInvoiceMethod())) {
            String[] split = request.getInvoiceMethod().split(StrUtil.COMMA);
            criteria.getCondition().put("invoiceMethod", split);
        }

        String receiver = request.getReceiver();
        Criteria.putCriteria(criteria,"receiver",receiver);
        /* 分页查询*/
        IPage<InvoiceDTO> page = bizInvoiceServiceImpl.selectByParams(PageUtil.preparePageParams(request), criteria);

        /*填充发票信息*/
        page.getRecords().forEach(invoice -> {
            invoice.setDistributorName("直营");
            if (Objects.nonNull(invoice.getDistributorId())) {
                invoice.setDistributorName(distributorService.getById(invoice.getDistributorId()).getName());
            }
        });

        /* 转化类别*/
        IPage<DescribeInvoiceResponse> covert = BeanConvertUtil.convertPage(page, DescribeInvoiceResponse.class);
        List<DescribeInvoiceResponse> records = covert.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return covert;
        }

        /* 填充信息*/
        records.forEach(bizInvoice -> bizInvoice.setInvoiceStatusName(InvoiceStatusEnum.codeFromName(
                bizInvoice.getInvoiceStatus()
        )));

        covert.getRecords().forEach(describeInvoiceResponse -> {
            describeInvoiceResponse.setBankAccount(CrytoUtilSimple.decrypt(describeInvoiceResponse.getBankAccount()));
            describeInvoiceResponse.setRegisterPhone(CrytoUtilSimple.decrypt(describeInvoiceResponse.getRegisterPhone()));
            describeInvoiceResponse.setRegisterAddress(CrytoUtilSimple.decrypt(describeInvoiceResponse.getRegisterAddress()));
            // 银行账户解密
            describeInvoiceResponse.setBankAccount(DesensitizedUtil.bankCard(CrytoUtilSimple.decrypt(describeInvoiceResponse.getBankAccount())));
            if ("0".equals(describeInvoiceResponse.getInvoiceType())) {
                describeInvoiceResponse.setAddress(
                        DesensitizedUtil.address(describeInvoiceResponse.getAddress(), describeInvoiceResponse.getAddress().length() / 2));
            }
        });
        DesensitizationUtil.desensitization(covert.getRecords());
        return covert;
    }

    /**
     * [INNER API] 获取发票列表
     *
     * @param request 获取发票列表请求体
     * @param moduleType 模块类型
     *
     * @return {@code IPage<DescribeInvoiceResponse>}
     */
    @RejectCall
    @GetMapping("/feign")
    @ApiOperation("获取发票列表")
    @ListenExpireBack
    public IPage<DescribeInvoiceResponse> selectInvoicesByFeign(DescribeInvoicesRequest request, @RequestHeader String moduleType) {
        if (StringUtils.isBlank(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_18819451));
        }
        if (!Arrays.asList(Constants.BSS, Constants.CONSOLE).contains(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1319710067));
        }
        /* 创建查询对象*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.getCondition().put("entityId", RequestContextUtil.getEntityId());
        if (!Objects.isNull(request.getPagenum()) || !Objects.isNull(request.getPagesize())) {
            long offset = (request.getPagenum() - 1) * request.getPagesize();
            criteria.getCondition().put("pagenum", offset);
        }
        /* 获取当前登录用户信息*/
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        /* 检查请求头和用户信息*/
        if ("console".equals(moduleType)) {
            if (authUserInfo == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }
            if (Objects.isNull(authUserInfo.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1271298302));
            }
            //查询accountId
            BizBillingAccount account = iBizBillingAccountServiceImp.getById(request.getAccountId());
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1945067902));
            }
            Long id = account.getId();
            if (Objects.isNull(id)) {
                return null;
            }
            criteria.getCondition().put("entityId", account.getEntityId());
            criteria.getCondition().put(ACCOUNT_ID, id);
        }

        /* 获取当前登录用户*/
        User user = AuthUtil.getAuthUser();

        /* 分销商用户查询*/
        RestResult restResult = DistributorAuthUtil.put(criteria, null, user, 2);
        if (restResult == null) {
            return PageUtil.emptyPage();
        }

        // 配置默认排序
        if (Strings.isNullOrEmpty(request.getSortorder())) {
            if (authUserInfo == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }

            // 普通用户时间逆序
            criteria.setOrderByClause("A.created_dt desc");

            if (authUserInfo.getAdminFlag() && "bss".equals(moduleType)) {

                // 管理员查询，以状态，时间排序
                criteria.setOrderByClause("A.created_dt desc");
            }
        }
        if (StrUtil.isNotBlank(request.getInvoiceMethod())) {
            String[] split = request.getInvoiceMethod().split(StrUtil.COMMA);
            criteria.getCondition().put("invoiceMethod", split);
        }
        /* 分页查询*/
        IPage<InvoiceDTO> page = bizInvoiceServiceImpl.selectByParams(PageUtil.preparePageParams(request), criteria);

        /*填充发票信息*/
        page.getRecords().forEach(invoice -> {
            invoice.setDistributorName("直营");
            if (Objects.nonNull(invoice.getDistributorId())) {
                invoice.setDistributorName(distributorService.getById(invoice.getDistributorId()).getName());
            }
        });

        /* 转化类别*/
        IPage<DescribeInvoiceResponse> covert = BeanConvertUtil.convertPage(page, DescribeInvoiceResponse.class);
        List<DescribeInvoiceResponse> records = covert.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return covert;
        }

        /* 填充信息*/
        records.forEach(bizInvoice -> bizInvoice.setInvoiceStatusName(InvoiceStatusEnum.codeFromName(
                bizInvoice.getInvoiceStatus()
        )));

        covert.getRecords().forEach(describeInvoiceResponse -> {
            // 银行账户解密
            describeInvoiceResponse.setBankAccount(DesensitizedUtil.bankCard(CrytoUtilSimple.decrypt(describeInvoiceResponse.getBankAccount())));
            if ("0".equals(describeInvoiceResponse.getInvoiceType())) {
                describeInvoiceResponse.setAddress(
                        DesensitizedUtil.address(describeInvoiceResponse.getAddress(), describeInvoiceResponse.getAddress().length() / 2));
            }
        });
        DesensitizationUtil.desensitization(covert.getRecords());
        return covert;
    }

    /**
     * 导出发票列表
     *
     * @param request 导出发票列表请求体
     * @param response 响应
     * @param moduleType 模块类型
     */
    @AuthorizeBss(action = CC.CC0504)
    @ApiOperation(httpMethod = "GET", value = "导出发票列表")
    @GetMapping("/export")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'发票列表'", tagNameUs ="'Invoice List'", 
            resource = OperationResourceEnum.EXPORT_INVOICE_LIST, param = "#request")
    @Idempotent
    @ListenExpireBack
    public RestResult expertOrderList(DescribeInvoicesRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        Long accountId = request.getAccountId();
        if (Objects.nonNull(accountId)) {
            List<BizBillingAccount> billingAccounts = iBizBillingAccountServiceImp.getByAdminSid(authUserInfo.getUserSid());
            if (CollectionUtil.isEmpty(billingAccounts)) {
                throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
            }
            List<Long> accountList = billingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
            if (!accountList.contains(accountId)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ULTRA_VIRES_OPERATE));
            }
        }

        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.INVOICE.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.CURRENT_STATUS_NOT_DOWN));
        }
        String moduleType = ModuleTypeConstants.FROM_BSS;
        if (UserType.PLATFORM_USER.equals(authUserInfo.getUserType()) && Objects.nonNull(authUserInfo.getOrgSid())) {
            moduleType = ModuleTypeConstants.FROM_CONSOLE;
        }
        new ExportThreadUtil(exportService,
                             request,
                             moduleType,
                             ExportTypeEnum.INVOICE.getCode(),
                             download.getDownloadId(),
                             authUserInfo
        ).submit();

        return new RestResult(RestResult.Status.SUCCESS,WebUtil.getMessage(MsgCd.INVOICE_DOWNING) );
    }

    private BizDownload getBizDownload(BizDownload download, DescribeInvoicesRequest request, AuthUser authUserInfo) {
        // 租户在下载任务中存入accountId
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        return download;
    }

    /**
     * [INNER API] 导出发票列表
     *
     * @param request 导出发票列表请求体
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "导出发票列表")
    @GetMapping("/export/feign")
    @ListenExpireBack
    public RestResult expertOrderListByFeign(DescribeInvoicesRequest request) {
        return expertOrderList(request);
    }
}

