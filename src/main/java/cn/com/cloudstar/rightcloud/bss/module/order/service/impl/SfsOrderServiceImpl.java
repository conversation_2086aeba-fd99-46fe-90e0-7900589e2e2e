/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.BusinessError;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ApplyServiceVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.VbsDataTransVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.VbsDataDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.CreateSnapshotRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.SnapshotService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ApplyEntity;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/16.
 */
@Service
public class SfsOrderServiceImpl extends AbstractOrderService {

    @Autowired
    private SnapshotService snapshotService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String apply(ApplyServiceVO serviceVO) {

        ApplyEntity applyEntity = before();

        validateSpec(serviceVO);

        execute(serviceVO, applyEntity);

        String result = remoteInvoke(serviceVO, applyEntity);

        after(applyEntity);
        return result;
    }

    @Override
    public void validateSpec(ApplyServiceVO serviceVO) {
        if (CollectionUtil.isEmpty(serviceVO.getProductInfo())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_VALID_FAILURE));
        }
    }

    @Override
    public String remoteInvoke(ApplyServiceVO serviceVO, ApplyEntity applyEntity) {
        ProductInfoVO productInfoVO = serviceVO.getProductInfo().get(0);
        CreateSnapshotRequest request = new CreateSnapshotRequest();
        BeanUtil.copyProperties(productInfoVO, request, CopyOptions.create().ignoreNullValue());

        CreateSnapshotRequest copy = BeanConvertUtil.convert(productInfoVO.getData(), CreateSnapshotRequest.class);
        VbsDataTransVO transVO = BeanConvertUtil.convert(copy.getVbs(), VbsDataTransVO.class);
        VbsDataDTO vbsData = JSONObject.parseObject(JsonUtil.toJson(transVO, Include.NON_NULL), VbsDataDTO.class);
        request.setData(vbsData);
        request.setProjectId(serviceVO.getProjectId());
        RestResult restResult = snapshotService.create(request.getData());
        if (restResult.getStatus() && restResult.getData() != null) {
            applyEntity.setResourceId(Lists.newArrayList(Convert.toStr(restResult.getData())));
        } else {
            if (Objects.equals(BusinessError.QUOTA_ERROR, restResult.get("code"))
                || Objects.equals(BusinessError.BAD_PARAM, restResult.get("code"))) {
                throw new BizException(Convert.toStr(restResult.getMessage()));
            }
        }
        return null;
    }
}
