/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/10 11:18
 */
@Data
@ApiModel("产品详情")
public class ProductInfo implements Serializable {

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("云环境ID")
    private String cloudEnvId;

    @ApiModelProperty("资源ID")
    private String id;

    @ApiModelProperty("计费类型")
    private String chargeType;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("时长单位")
    private String priceUnit;

    @ApiModelProperty("时长")
    private BigDecimal period;

    @ApiModelProperty("产品描述")
    private String productConfigDesc;

    @ApiModelProperty("资源ID")
    private String resourceId;

    @ApiModelProperty("产品ID")
    private String serviceId;

    @ApiModelProperty("订单号")
    private Long orderId;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("实付金额")
    private BigDecimal finalCost;

    @ApiModelProperty("是否为主资源")
    private boolean principal;

    @ApiModelProperty("产品配置")
    private String config;

    @ApiModelProperty("统一结束时间")
    @Min(1)
    @Max(28)
    private Integer unifyDate;

    @ApiModelProperty("变更目标类型")
    private String targetType;

    private String currentType;

    private Integer currentSize;

    @ApiModelProperty("服务计费")
    private BigDecimal serviceAmount;

    @ApiModelProperty("额外配置计费")
    private BigDecimal extraConfigAmount;

    @ApiModelProperty("产品服务")
    private List<ProductService> services;

    @ApiModelProperty("变更目标大小")
    private Integer targetSize;
    private String targetSpec;

    private Date endTime;

    private boolean isPublic;

    /**
     * 几折
     */
    private BigDecimal platformDiscount;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;
    //客户折扣率
    private BigDecimal discountRatio;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("额外信息")
    private Map<Object, Object> options;

    private Date startTime;

    @ApiModelProperty("结算类型：标准价、合同价")
    private String settlementType;

    @ApiModelProperty("合同ID")
    private Long contractId;

    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

    @ApiModelProperty("订单名称")
    private String name;

    private Date now;
    /**
     * 当前时间，now字段在 业务逻辑中有修改的情况，无法表示当前的时间了，故加currentDate
     */
    private Date currentDate;

    /**
     * 补扣金额（过期使用金额）
     */
    private BigDecimal expiredUsedAmount = BigDecimal.ZERO;
    /**
     * 询价补扣金额对应的orderDetailId
     */
    private Long makeUpOrderDetailId;

    private String status;

    private BigDecimal giveBack = BigDecimal.ZERO;

    private BigDecimal serviceGiveBack = BigDecimal.ZERO;

    private Long orderDetailId;

    private Date computeEndDate;

    public BigDecimal getPrice() {
        return Objects.isNull(price) ? BigDecimal.ZERO : this.price;
    }

    public BigDecimal getPlatformDiscount() {
        return Objects.isNull(this.platformDiscount) ? BigDecimal.ONE : this.platformDiscount;
    }
    //组织ID
    private Long orgSid;
    /**
     * 冻结时间
     */
    private Date frozenTime;

    /**
     * 续费开始时间
     */
    private Date renewStartTime;

    /**
     * 补扣明细金额
     */
    private List<ServiceOrderPriceDetail> expiredUsedPriceDetails = new ArrayList<>();


    /**
     * HPC集群ID
     */
    private Long clusterId;
    /**
     * sfProductResource id
     */
    private Long productResourceId;

    /**
     * 变更容量时记录负数补差金额
     */
    private BigDecimal negativeAmount;

    /**
     * 折扣金额
     */
    private BigDecimal discount;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 审批类型
     */
    private String applyType;
}
