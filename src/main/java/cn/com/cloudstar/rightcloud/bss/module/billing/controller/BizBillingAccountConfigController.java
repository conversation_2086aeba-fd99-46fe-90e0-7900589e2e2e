/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.controller;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WrapperUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeBillingRegionResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response.DescribeSpecConfigResponse;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.*;
import cn.com.cloudstar.rightcloud.bss.module.env.pojo.response.CloudAccountBillingStrategy;
import cn.com.cloudstar.rightcloud.bss.module.env.pojo.response.CloudAccountBillingStrategyResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.AvailableResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD04;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 资源计费配置管理
 * <AUTHOR>
 * Created on 2019/10/25
 */
@Api(tags = "资源计费配置管理")
@Slf4j
@RestController
@RequestMapping("/billing/strategy")
public class BizBillingAccountConfigController {
    private static final String COLON_SEP = StrUtil.COLON;
    private static final String VERTICAL_SEP = " | ";
    private static final String PUBLIC_ENV = "Public";
    private static final String PRIVATE_ENV = "Private";
    private static final String INTELLECT_ENV = "Intellect";

    /**
     * 下拉框类型的数据
     */
    private static final String INPUT_TYPE_SELECT = "select";

    /**
     * 一个月的小时数
     */
    private static final BigDecimal HOURS_OF_MONTH = BigDecimal.valueOf(24 * 30);

    @DubboReference
    private CloudEnvAccountRemoteService envAccountService;

    @Autowired
    private BizBillingStrategyServingService  servingService;

    @Autowired
    private BizBillingStrategyAccountService accountService;

    @Autowired
    private BizBillingAccountConfigService accountConfigService;

    @Autowired
    private BizBillingStrategyAccountConfigService strategyAccountConfigService;

    @Autowired
    private BizBillingStrategyService strategyService;

    @Autowired
    private BizBillingTariffSpecService tariffSpecService;

    @Autowired
    private BizBillingSpecService specService;

    @Autowired
    private BizBillingSpecReferMapper specReferMapper;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @Autowired
    private BizBillingStrategyAccountMatchService strategyAccountMatchService;

    @Autowired
    private IBizBillingRegionResourceService bizBillingRegionResourceService;

    @Autowired
    private BizBillingCustomRegionResourceMapper bizBillingCustomRegionResourceMapper;

    @Autowired
    private IBizBillingCustomRegionResourceService bizBillingCustomRegionResourceService;

    @Autowired
    private IBizBillingCustomRegionChargeService bizBillingCustomRegionChargeService;

    @Autowired
    private IBizBillingRegionChargeService bizBillingRegionChargeService;

    @Autowired
    private IBizBillingSpecGroupService bizBillingSpecGroupService;

    @Autowired
    private BizBillingStrategyAccountMapper bizBillingStrategyAccountMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    @Autowired
    private BizBillingSpecSdrReferMapper bizBillingSpecSdrReferMapper;

    @Autowired
    private BizBillingSpecRefMapper bizBillingSpecRefMapper;

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    private BizBillingStrategyServingMapper bizBillingStrategyServingMapper;

    @Autowired
    private BizBillingStrategyMapper bizBillingStrategyMapper;

    @Autowired
    private BizBillingSpecMapper bizBillingSpecMapper;

    @Autowired
    private FeignService feignService;
    @Autowired
    private OrgService orgService;

    /**
     * 查询云账号计费策略配置
     *
     * @param name           云账号名称
     * @param excludeEnvType 排除env类型
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD.BD04.BD0401)
    @ApiOperation("查询云账号计费策略配置")
    @GetMapping("/account")
    public RestResult<List<CloudAccountBillingStrategyResponse>> queryBillingAccountConfig(@ApiParam("云账号名称") @RequestParam(value = "name", required = false) String name,
                                                                                           @RequestParam(value = "excludeEnvType", required = false) String excludeEnvType) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1255531007));
        }
        Pattern pattern = java.util.regex.Pattern.compile("[a-zA-Z0-9\\u4e00-\\u9fa5.,，。！!()（）-]*$");
        if(StringUtils.isNotBlank(name)){
            if(!pattern.matcher(name).matches()){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1803602344));
            }
        }
        if(StringUtils.isNotBlank(name)){
            if(!pattern.matcher(excludeEnvType).matches()){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1803602344));
            }
        }





        List<CloudEnvAccount> accounts = envAccountService.listexcludeEnvType(name, excludeEnvType);

        if (accounts.size() == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1020531595));
        }

        List<BizBillingStrategyAccount> strategyAccounts = bizBillingStrategyAccountMapper.getBizBillingStrategyAccountListByEntityId(RequestContextUtil.getEntityId());

        QueryWrapper<BizBillingStrategyAccountConfig> accountConfigQueryWrapper = new QueryWrapper<>();
        List<BizBillingStrategyAccountConfig> accountConfigs = strategyAccountConfigService.list(accountConfigQueryWrapper);

        Map<String, List<CloudEnvAccount>> accountGroup = accounts.stream()
                                                                  .filter(v -> !ObjectUtils.isEmpty(v.getEnvTypeCategory()))
                .collect(Collectors.groupingBy(CloudEnvAccount::getEnvTypeCategory));

        Map<Long, List<BizBillingStrategyAccountConfig>> accountConfigGroup =
                accountConfigs.stream()
                        .collect(Collectors.groupingBy(BizBillingStrategyAccountConfig::getAccountId));

        List<CloudAccountBillingStrategy> publics = Lists.newArrayList();
        List<CloudAccountBillingStrategy> privates = Lists.newArrayList();

        Set<Map.Entry<String, List<CloudEnvAccount>>> entries = accountGroup.entrySet();
        for (Map.Entry<String, List<CloudEnvAccount>> entry : entries) {
            String envTypeCategory = entry.getKey();
            List<CloudEnvAccount> groupedAccounts = entry.getValue();
            if (PUBLIC_ENV.equals(envTypeCategory)) {
                for (CloudEnvAccount account : groupedAccounts) {
                    CloudAccountBillingStrategy strategy = BeanConvertUtil.convert(account, CloudAccountBillingStrategy.class);

                    if (Objects.nonNull(strategy) && accountConfigGroup.containsKey(account.getId())) {
                        BizBillingStrategyAccountConfig strategyAccountConfig = accountConfigGroup.get(account.getId()).get(0);
                        strategy.setAccountBillingStrategyId(strategyAccountConfig.getId());
                        strategy.setBillingStrategy(strategyAccountConfig.getBillingStrategy());
                        strategy.setDiscount(strategyAccountConfig.getDiscount());
                        strategy.setFloatingRatio(strategyAccountConfig.getFloatingRatio());
                    }

                    publics.add(strategy);
                }
            } else if (PRIVATE_ENV.equals(envTypeCategory) ||INTELLECT_ENV.equalsIgnoreCase(envTypeCategory)) {
                for (CloudEnvAccount account : groupedAccounts) {
                    CloudAccountBillingStrategy strategy = BeanConvertUtil.convert(account, CloudAccountBillingStrategy.class);

                    Map<String, Boolean> defaultStategy = ImmutableMap.of(BillingConfigCategory.COMPUTER, Boolean.FALSE,
                            BillingConfigCategory.BLOCK_STORAGE, Boolean.FALSE,
                            BillingConfigCategory.NETWORK, Boolean.FALSE);
                    defaultStategy = Maps.newHashMap(defaultStategy);

                    Map<String, List<BizBillingStrategyAccount>> strategyGroup = strategyAccounts.stream()
                            .filter(strategyAccount -> account.getId().equals(strategyAccount.getAccountId()))
                            .collect(Collectors.groupingBy(BizBillingStrategyAccount::getCategory));

                    Set<Map.Entry<String, Boolean>> defaultStategySet = defaultStategy.entrySet();
                    for (Map.Entry<String, Boolean> stagegySet : defaultStategySet) {
                        if (strategyGroup.containsKey(stagegySet.getKey())) {
                            stagegySet.setValue(true);
                        }
                    }

                    if (Objects.nonNull(strategy)) {
                        strategy.setCategoryStrategy(defaultStategy);

                        if (accountConfigGroup.containsKey(account.getId())) {
                            BizBillingStrategyAccountConfig strategyAccountConfig = accountConfigGroup.get(account.getId()).get(0);
                            strategy.setAccountBillingStrategyId(strategyAccountConfig.getId());
                            strategy.setBillingStrategy(strategyAccountConfig.getBillingStrategy());
                            strategy.setDiscount(strategyAccountConfig.getDiscount());
                        }

                        privates.add(strategy);
                    }
                }
            }
        }
        List<CloudAccountBillingStrategy> result = Lists.newArrayList();
        result.addAll(publics);
        result.addAll(privates);

        return new RestResult(BeanConvertUtil.convert(result,CloudAccountBillingStrategyResponse.class));
    }


    /**
     * 配置云账号计费参数
     *
     * @param updateBizBillingAccountConfigRequest 更新业务账单账户配置请求
     * @return {@link RestResult}
     */
    @AuthorizeBss(action = BD04.BD040101)
    @ApiOperation("配置云账号计费参数")
    @PostMapping("/account/config")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改计费模式'", tagNameUs ="'Modify Billing Mode'", 
            resource = OperationResourceEnum.EDIT_BILLING_MODEL,bizId = "#updateBizBillingAccountConfigRequest.accountId",param = "#updateBizBillingAccountConfigRequest")
    public RestResult billingPublicAccountConfig(@Validated @RequestBody UpdateBizBillingAccountConfigRequest updateBizBillingAccountConfigRequest) {
        BizBillingStrategyAccountConfig strategyPublicAccount = BeanConvertUtil.convert(updateBizBillingAccountConfigRequest, BizBillingStrategyAccountConfig.class);
        accountConfigService.updateBillingPublicAccount(strategyPublicAccount);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }


    /**
     * 添加资源类型(区域)
     *
     * @param request 资源区域计费策略配置请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD0401)
    @ApiOperation("添加资源类型(区域)")
    @PostMapping("/resource_region")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'资源类型'", resource = OperationResourceEnum.ADD_RESOURCE_TYPE, tagNameUs ="'Resource Type'", 
            param = "#request")
    public RestResult createResourceRegion(@Validated @RequestBody CreateRegionStrategyRequest request) {

        // 应该是数据库已经存在的id、region调用resource侧的接口来查询判断
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByCloudEnvAccountId(request.getEnvAccountId());
        if (ObjectUtils.isEmpty(cloudEnvs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ENV_CLOUD_NOT_EXITS));
        }
        boolean b = cloudEnvs.stream().anyMatch(cloudEnv -> "all".equals(request.getRegion()) || cloudEnv.getRegion().equals(request.getRegion()));
        if (!b) {
            throw new BizException(WebUtil.getMessage(MsgCd.RESOURCE_AREA_MISMATCHING));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(request.getResourceType().get(0));
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
        if (Objects.isNull(authUserInfo.getOrgSid()) && !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }


        // 判断资源是否定价 定价需要先关联计费后预览，所以注释掉
//        List<String>  resourceTypes = request.getResourceType();
//        List<BizBillingStrategyServing>  strategyServings = bizBillingStrategyServingMapper.selectList(new QueryWrapper<BizBillingStrategyServing>()
//                .in("resource_type", resourceTypes));
//        if (CollectionUtil.isEmpty(strategyServings) || strategyServings.size() < resourceTypes.size()) {
//            throw new BizException(WebUtil.getMessage(MsgCd.RESOURCE_TYPE_NOT_CHARGE));
//        }

        String userSid = request.getUserSid();
        if (StringUtils.isBlank(userSid)) {
            // 保存平台资源类型
            return this.savePlatformResourceRegion(request);
        } else {
            // 保存客户资源类型
            return this.saveCustomResourceRegion(request);
        }
    }

    /**
     *  保存平台资源类型
     * @return
     */
    private RestResult savePlatformResourceRegion(CreateRegionStrategyRequest request) {
        // 资源类型合法性验证
        QueryWrapper<BizBillingRegionResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingRegionResource::getEnvAccountId, request.getEnvAccountId())
                .eq(BizBillingRegionResource::getRegion, request.getRegion())
                .eq(BizBillingRegionResource::getEntityId, RequestContextUtil.getEntityId())
                .in(BizBillingRegionResource::getResourceType, request.getResourceType());
        if (bizBillingRegionResourceService.list(queryWrapper).size() > 0) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.AREA_EXITS_REPEAT_RESOURCE_TYPE), request.getResourceType());
        }
        Long entityId = RequestContextUtil.getEntityId();
        request.getResourceType().stream().forEach(productCode->{
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(productCode);
            if(serviceCategory == null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_879190036));
        }
            if(ServiceCategoryStatus.USING.equalsIgnoreCase(serviceCategory.getStatus())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2138631237));
            }
            if(entityId != null && !entityId.equals(serviceCategory.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_599580133));
            }
        });


        request.getResourceType().forEach(resourceType -> {
            BizBillingRegionResource regionStrategy = new BizBillingRegionResource();
            BeanUtil.copyProperties(request, regionStrategy);
            regionStrategy.setResourceType(resourceType);
            regionStrategy.setEntityId(entityId);
            regionStrategy.setEntityName(entityUserMapper.getByEntityId(entityId).getEntityName());
            WebUserUtil.prepareInsertParams(regionStrategy);
            bizBillingRegionResourceService.save(regionStrategy);
        });

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 【Since v2.5.0】
     *  保存客户资源类型
     * @return
     */
    private RestResult saveCustomResourceRegion(CreateRegionStrategyRequest request) {
        String userSId = request.getUserSid();
        User user = sysUserMapper.selectById(userSId);
        if (user == null) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.CUSTOM_INFO_ABNORMAL));
        }

        // 资源类型合法性验证
        QueryWrapper<BizBillingCustomRegionResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingCustomRegionResource::getEnvAccountId, request.getEnvAccountId())
                .eq(BizBillingCustomRegionResource::getRegion, request.getRegion())
                .eq(BizBillingCustomRegionResource::getEntityId, RequestContextUtil.getEntityId())
                .eq(BizBillingCustomRegionResource::getOwnerId, request.getUserSid())
                .in(BizBillingCustomRegionResource::getResourceType, request.getResourceType());
        if (bizBillingCustomRegionResourceService.list(queryWrapper).size() > 0) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.AREA_EXITS_REPEAT_RESOURCE_TYPE), request.getResourceType());
        }
        Long entityId = RequestContextUtil.getEntityId();
        Long orgSid = user.getOrgSid();
        request.getResourceType().forEach(resourceType -> {
            BizBillingCustomRegionResource regionStrategy = new BizBillingCustomRegionResource();
            BeanUtil.copyProperties(request, regionStrategy);
            regionStrategy.setResourceType(resourceType);
            regionStrategy.setOwnerId(userSId);
            regionStrategy.setOrgSid(orgSid);
            regionStrategy.setEntityId(entityId);
            regionStrategy.setEntityName(entityUserMapper.getByEntityId(entityId).getEntityName());
            WebUserUtil.prepareInsertParams(regionStrategy);
            bizBillingCustomRegionResourceService.save(regionStrategy);
        });

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 【Since v2.5.0】
     *  修改客户资源类型状态
     */
    @AuthorizeBss(action = BD04.BD0401)
    @ApiOperation("修改客户资源类型状态")
    @PutMapping("/editCustomResource")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改客户资源类型状态'", bizId = "#request.id", resource = OperationResourceEnum.UPDATE_RESOURCE_STATUS, param = "#request", tagNameUs ="'Modify Customer Resource Type Status'")
    public RestResult editCustomResource(@RequestBody @Validated EditCustomResourceRequest request) {
        return bizBillingCustomRegionResourceService.editCustomResource(request);
    }

    /**
     * 获取云账号下资源类型(区域)
     *
     * @param accountId 帐户id
     * @param region    地区
     * @return {@code List<DescribeBillingRegionResourceResponse>}
     */
    @AuthorizeBss(action = BD04.BD040101)
    @ApiOperation("获取云账号下资源类型(区域)")
    @GetMapping("/resource_region/{accountId}")
    public List<DescribeBillingRegionResourceResponse> getAccountResourceRegion(@PathVariable("accountId") Long accountId,
                                                                                @ApiParam("区域") @RequestParam(value = "region", required = false) String region) {
        QueryWrapper<BizBillingRegionResource> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotEmpty(region)) {
            queryWrapper.lambda().eq(BizBillingRegionResource::getRegion, region);
        }

        queryWrapper.lambda().eq(BizBillingRegionResource::getEnvAccountId, accountId);
        List<BizBillingRegionResource> regionResources = bizBillingRegionResourceService.selectByParams(queryWrapper);
        // 多运营实体过滤
        Long entityId = RequestContextUtil.getEntityId();
        regionResources = regionResources.stream().filter(t -> entityId.equals(t.getEntityId())).collect(Collectors.toList());
        List<Long> regionResourceIds = regionResources.stream().map(BizBillingRegionResource::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(regionResourceIds)) {
            return Collections.emptyList();
        }
        QueryWrapper<BizBillingRegionCharge> chargeQueryWrapper = new QueryWrapper<>();
        chargeQueryWrapper.lambda().in(BizBillingRegionCharge::getRegionResourceId, regionResourceIds);
        List<BizBillingRegionCharge> regionCharges = bizBillingRegionChargeService.list(chargeQueryWrapper);
        Map<Long, List<BizBillingRegionCharge>> regionResourceMap = regionCharges.stream().collect(Collectors.groupingBy(BizBillingRegionCharge::getRegionResourceId));
        // 查找所有使用中产品，为MA，HPC关联产品做特殊处理
        List<String> usingResourceList = regionResources.stream().filter(
                res -> ServiceCategoryStatus.USING.equals(res.getStatus())).map(
                BizBillingRegionResource::getResourceType).collect(Collectors.toList());

        List<String> productCodes = Arrays.asList(
                ProductCodeEnum.MODEL_ARTS.getProductType(),
                ProductCodeEnum.OBS.getProductType(),
                ProductCodeEnum.EVS.getProductType(),
            ProductCodeEnum.MA_BMS.getProductType()
        );

        List<BizBillingSpecSdrRefer> specSdrRefers = bizBillingSpecSdrReferMapper.selectList(new QueryWrapper<BizBillingSpecSdrRefer>()
                .in("product_code", productCodes));

        // 处理状态
        regionResources.forEach(t -> {
            String resourceType = t.getResourceType();
            List<BizBillingRegionCharge> regionChargeList = regionResourceMap.get(t.getId());
            if (productCodes.contains(resourceType)) {
                regionChargeList = this.serSdrValue(resourceType, specSdrRefers, regionChargeList);
            }

            t.setRegionCharges(regionChargeList);;
            t.setChargeStrategyName(packageChargeStrategyName(regionChargeList));

            if (StringUtils.isBlank(t.getStatus())) {
                t.setStatus(ServiceCategoryStatus.NOUSING);
            }
            if(usingResourceList.contains(ProductCodeEnum.HPC_DRP.getProductType())
                    && (ProductCodeEnum.BMS.getProductType().equals(t.getResourceType())
                    || ProductCodeEnum.ECS.getProductType().equals(t.getResourceType()))) {
                t.setStatus(ServiceCategoryStatus.USING);
            } else if(usingResourceList.contains(ProductCodeEnum.MODELARTS.getProductType())
                    && (ProductCodeEnum.OBS.getProductType().equals(t.getResourceType())
                    || ProductCodeEnum.EVS.getProductType().equals(t.getResourceType()))){
                t.setStatus(ServiceCategoryStatus.USING);
            } else if(usingResourceList.contains(ProductCodeEnum.HPC.getProductType())
                    && ProductCodeEnum.SFS.getProductType().equals(t.getResourceType())){
                t.setStatus(ServiceCategoryStatus.USING);
            } else if (ProductCodeEnum.MA_BMS.getProductType().equals(resourceType)) {
                Integer bmsCount = orgService.lambdaQuery()
                        .notIn(Org::getStatus,Arrays.asList(2,4,8))
                        .eq(Org::getBmsEnable, 1).count();
                if (bmsCount >0){
                    t.setStatus(ServiceCategoryStatus.USING);
                }
            }
        });
        return BeanConvertUtil.convert(regionResources, DescribeBillingRegionResourceResponse.class);
    }

    /**
     * 获取云账号下客户资源类型(区域)
     *【Since v2.5.0】
     * @return {@code List<DescribeBillingRegionResourceResponse>}
     */
    @AuthorizeBss(action = AuthModule.BD.BD04.BD040101)
    @ApiOperation("获取云账号下资源类型(区域)")
    @GetMapping("/resource_region/{accountId}/{custom}")
    public List<DescribeBillingRegionResourceResponse> getAccountCustomResourceRegion(@PathVariable("accountId") Long accountId,
                                                                                @ApiParam("区域") @RequestParam(value = "region", required = false) String region,
                                                                                @PathVariable("custom") String custom)
    {
        QueryWrapper<BizBillingCustomRegionResource> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotEmpty(region)) {
            queryWrapper.lambda().eq(BizBillingCustomRegionResource::getRegion, region);
        }
        if (StrUtil.isNotEmpty(custom)) {
            queryWrapper.lambda().eq(BizBillingCustomRegionResource::getOwnerId, custom);
        }

        queryWrapper.lambda().eq(BizBillingCustomRegionResource::getEnvAccountId, accountId);
        List<BizBillingCustomRegionResource> regionResources = bizBillingCustomRegionResourceMapper.selectByParams(queryWrapper);

        // 多运营实体过滤
        Long entityId = RequestContextUtil.getEntityId();
        regionResources = regionResources.stream().filter(t -> entityId.equals(t.getEntityId())).collect(Collectors.toList());
        List<Long> regionResourceIds = regionResources.stream().map(BizBillingCustomRegionResource::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(regionResourceIds)) {
            return Collections.emptyList();
        }

        QueryWrapper<BizBillingCustomRegionCharge> chargeQueryWrapper = new QueryWrapper<>();
        chargeQueryWrapper.lambda().in(BizBillingCustomRegionCharge::getRegionResourceId, regionResourceIds);
        List<BizBillingCustomRegionCharge> regionCharges = bizBillingCustomRegionChargeService.list(chargeQueryWrapper);
        Map<Long, List<BizBillingCustomRegionCharge>> regionResourceMap = regionCharges.stream().collect(Collectors.groupingBy(BizBillingCustomRegionCharge::getRegionResourceId));

        // 获取产品
        Criteria criteria = new Criteria();
        criteria.put("entity_id", entityId);
        List<ServiceCategory> categories = serviceCategoryMapper.getServiceCategoryByParams(criteria);
        Map<String, Long> ServiceCategoryToId = categories.stream().collect(Collectors.toMap(ServiceCategory::getServiceType, ServiceCategory::getId, (k, v) -> k));

        regionResources.forEach(t -> {
            List<BizBillingCustomRegionCharge> regionChargeList = regionResourceMap.get(t.getId());

            t.setRegionCharges(regionChargeList);
            t.setServiceCategoryId(ServiceCategoryToId.get(t.getResourceType()));
            List<BizBillingRegionCharge> charges =  BeanConvertUtil.convert(regionChargeList, BizBillingRegionCharge.class);
            if (CollectionUtil.isNotEmpty(charges)) {
                BizBillingRegionCharge charge = charges.get(0);
                t.setChargeStrategyName(packageChargeStrategyName(charges));
                t.setCategoryName(charge.getCategoryName());
            }
        });
        return BeanConvertUtil.convert(regionResources, DescribeBillingRegionResourceResponse.class);
    }

    private List<BizBillingRegionCharge> serSdrValue(String resourceType, List<BizBillingSpecSdrRefer> specSdrRefers, List<BizBillingRegionCharge> regionCharges) {
        List<BizBillingSpecRef> specRefs = bizBillingSpecRefMapper.selectList(new QueryWrapper<BizBillingSpecRef>().eq("resource_type", resourceType));
        List<TypeFamilyVo> typeFamilyVos = null;
        if (CollectionUtil.isNotEmpty(specRefs)) {
            BizBillingSpecRef specRef = specRefs.get(0);
            typeFamilyVos = JSON.parseArray(specRef.getValue(), TypeFamilyVo.class);
        }

        if (CollectionUtil.isNotEmpty(regionCharges)) {
            for (BizBillingRegionCharge regionCharge : regionCharges) {
                String specConfig = regionCharge.getSpecConfig();
                if (StringUtils.isBlank(specConfig)) {
                    continue;
                }
                String specType = regionCharge.getSpecType();
                List<SpecConfigVO> specConfigs = JSON.parseArray(specConfig, SpecConfigVO.class);

                specConfigs.forEach(specConfigVO -> {
                    specConfigVO.setResourceType(resourceType);
                    String platformSpecName = specType + "." + specConfigVO.getType();
                    Optional<BizBillingSpecSdrRefer> optional = specSdrRefers.stream().filter(e -> e.getPlatformSpecName().equals(platformSpecName)).findFirst();
                    optional.ifPresent(bizBillingSpecSdrRefer -> {
                        specConfigVO.setChargeCode(bizBillingSpecSdrRefer.getSdrSpecName());
                        specConfigVO.setCard(bizBillingSpecSdrRefer.getCard() == null?0:bizBillingSpecSdrRefer.getCard());
                    });
                });
                specConfigs.removeIf(configVO -> configVO.getType().contains("*") || configVO.getChargeCode().contains("*"));
                if (typeFamilyVos != null && (typeFamilyVos.size() > specConfigs.size())) {
                    List<String> notNames = specConfigs.stream().map(SpecConfigVO::getTypeName).filter(io.seata.common.util.StringUtils::isNotBlank).collect(Collectors.toList());
                    typeFamilyVos = typeFamilyVos.stream().filter(e -> !notNames.contains(e.getName())).collect(Collectors.toList());
                    typeFamilyVos.forEach(e -> {
                        SpecConfigVO specConfigVO = new SpecConfigVO();
                        specConfigVO.setResourceType(resourceType);
                        specConfigVO.setType(e.getValue());
                        specConfigVO.setTypeName(e.getName());
                        specConfigVO.setChargeCode(e.getChargeCode());
                        specConfigVO.setCard(e.getCard()==null?0:e.getCard());
                        specConfigs.add(specConfigVO);
                    });
                }
                List<SpecConfigVO> specConfigList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(specConfigs)){
                    specConfigList = specConfigs.stream()
                            .filter(configVO -> {
                                String type = Optional.ofNullable(configVO.getType()).orElse(StringUtils.EMPTY);
                                String chargeCode = Optional.ofNullable(configVO.getChargeCode()).orElse(StringUtils.EMPTY);
                                return !type.contains("*") && !chargeCode.contains("*");
                            })
                                                .collect(Collectors.toList());
                }
                regionCharge.setSpecConfig(JSON.toJSONString(specConfigList));
            }
        } else {
            regionCharges = new ArrayList<>();
            BizBillingRegionCharge regionCharge = new BizBillingRegionCharge();
            List<SpecConfigVO> specConfigs = new ArrayList<>();
            if (!CollectionUtils.isEmpty(typeFamilyVos)) {
                typeFamilyVos.forEach(e -> {
                    SpecConfigVO specConfigVO = new SpecConfigVO();
                    specConfigVO.setResourceType(resourceType);
                    specConfigVO.setType(e.getValue());
                    specConfigVO.setTypeName(e.getName());
                    specConfigVO.setChargeCode(e.getChargeCode());
                    specConfigVO.setCard(e.getCard()==null?0:e.getCard());
                    specConfigs.add(specConfigVO);
                });
                List<SpecConfigVO> specConfigList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(specConfigs)){
                    specConfigList = specConfigs.stream()
                                                .filter(configVO -> !configVO.getType().contains("*") && !configVO.getChargeCode().contains("*"))
                                                .collect(Collectors.toList());
                }
                regionCharge.setSpecConfig(JSON.toJSONString(specConfigList));
                regionCharges.add(regionCharge);
            }
        }
        return regionCharges;
    }

    /**
     * 区域计费配置
     *
     * @param request 配置区域计费请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD04010101)
    @ApiOperation("区域计费配置")
    @PutMapping("/charge_config")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'计费配置'", resource = OperationResourceEnum.CONFIGURE_RESOURCE_TYPE,param = "#request",bizId = "#request.id", tagNameUs ="'Billing configuration'")
    public RestResult billingStrategyAccount(@Validated @RequestBody ConfigRegionChargeRequest request) {
        //校验计费规则族
        List<RegionConfigVO> regionConfigs = request.getRegionConfigs();
        if (!ObjectUtils.isEmpty(regionConfigs)) {
            List<BizBillingSpec> bizBillingSpecs = bizBillingSpecMapper.selectList(new QueryWrapper<>());
            List<String> specNames = bizBillingSpecs.stream()
                    .map(BizBillingSpec::getSpecName)
                    .collect(Collectors.toList());
            boolean isSpec = request.getRegionConfigs().stream().allMatch(v -> {
                // 校验specType
                if (Objects.nonNull(v.getSpecType())) {
                    List<String> split = StrUtil.split(v.getSpecType(), StrUtil.COMMA);
                    if (!specNames.containsAll(split)) {
                        return false;
                    }
                }
                //获取规格族
                List<BizBillingSpecGroup> specGroups = findSpecGroups(DescribeSpecGroupRequest.builder()
                        .strategyId(v.getStrategyId())
                        .spec(v.getSpecType())
                        .category(v.getCategory())
                        .isResult(true).build()
                );
                if (ObjectUtils.isEmpty(specGroups)) {
                    return false;
                }
                if (v.getStrategyId() != null) {
                    BizBillingStrategy bizBillingStrategy = bizBillingStrategyMapper.selectById(v.getStrategyId());
                    if (Objects.isNull(bizBillingStrategy)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                    if (bizBillingStrategy.getEntityId() != null
                            && !bizBillingStrategy.getEntityId().equals(bizBillingStrategy.getEntityId())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.NO_PERMISSION_OPERATION));
                    }
                }
                //选中的规则必须在当前规则族中，在则true，反之false
                boolean groupIdMatch = v.getSpecs().stream().allMatch(spec -> !"*".equals(spec.getType()) && specGroups.stream().anyMatch(it -> spec.getSpecGroupId().equals(it.getId())));
                if (!groupIdMatch) {
                    return false;
                }
                String resourceType;
                Long envAccountId;
                if (Objects.nonNull(request.getUserSid())) {
                    BizBillingCustomRegionResource customRegionResource = bizBillingCustomRegionResourceMapper.selectById(request.getId());
                    if (Objects.isNull(customRegionResource)) {
                        return false;
                    }
                    resourceType = customRegionResource.getResourceType();
                    envAccountId = customRegionResource.getEnvAccountId();
                } else {
                    BizBillingRegionResource resource = bizBillingRegionResourceService.getById(request.getId());
                    if (Objects.isNull(resource)) {
                        return false;
                    }
                    resourceType = resource.getResourceType();
                    envAccountId = resource.getEnvAccountId();
                }

                if (ProductCodeEnum.MODEL_ARTS.getProductType().equals(resourceType) || ProductCodeEnum.OBS.getProductType().equals(resourceType)) {
                    if (checkSpecsModelArts(v, envAccountId, resourceType)) {
                        return false;
                    }
                } else if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(resourceType)) {
                    if (checkSpecsByResource(v, envAccountId, resourceType)) {
                        return false;
                    }
                }
                if (this.checkSpecsNum(envAccountId, resourceType, v.getSpecs())) {
                    return false;
                }

                return true;
            });
            if (!isSpec) {
                throw new BizException(WebUtil.getMessage(MsgCd.SELECT_CORRECT_CHARGE_RULE));
            }
            if (CollectionUtil.isEmpty(request.getRegionConfigs())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }

            if (StringUtils.isBlank(request.getUserSid())) {
                bizBillingRegionResourceService.configRegionCharge(request);
            } else {
                bizBillingRegionResourceService.configCustomRegionCharge(request);
            }
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 校验规格定价是否全配置了规格族
     * @param envAccountId
     * @param resourceType
     * @param specs
     * @return
     */
    private boolean checkSpecsNum(Long envAccountId, String resourceType, List<SpecConfigVO> specs) {
        switch (resourceType) {
            case "ECS":
                resourceType = "RES-VM";
                break;
            case "RS-BMS":
                resourceType = "RES-BMS";
                break;
            case "DCS":
                resourceType = "RES-DCS";
                break;
            case "EBS":
                resourceType = "RES-VD";
                break;
            case "RDS":
                resourceType = "RES-RDS";
                break;
            default:
        }
        AvailableResourceRequest availableResourceRequest = new AvailableResourceRequest();
        availableResourceRequest.setType(resourceType);
        availableResourceRequest.setEnvAccountId(envAccountId);
        RestResult resourceFamilyType = feignService.getResourceFamilyType(availableResourceRequest);
        if (!resourceFamilyType.getStatus()) {
            return true;
        }
        Object data = resourceFamilyType.getData();
        if (Objects.isNull(data)) {
            return false;
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
        Set<String> keySet = jsonObject.keySet();
        if (CollectionUtils.isEmpty(keySet)) {
            return false;
        }
        String key = keySet.stream().findFirst().orElse(null);
        JSONArray jsonArray = jsonObject.getJSONArray(key);
        if (jsonArray.size() > specs.size()) {
            return true;
        }
        return false;
    }

    private List<BizBillingSpecGroup> findSpecGroups(DescribeSpecGroupRequest request) {
        QueryWrapper<BizBillingSpecGroup> queryWrapper = WrapperUtil.wrapQuery(request, "isSelect", "spec", "isResult");
        if (StrUtil.isNotEmpty(request.getSpec())) {

            List<String> specs = Arrays.asList(StrUtil.splitToArray(request.getSpec(), StrUtil.COMMA));
            QueryWrapper<BizBillingSpec> specQuery = new QueryWrapper<>();
            specQuery.lambda().in(BizBillingSpec::getSpecName, specs).eq(BizBillingSpec::getCategory, request.getCategory());
            List<BizBillingSpec> specList = specService.list(specQuery);
            if (CollectionUtil.isNotEmpty(specList)) {
                queryWrapper.lambda().eq(BizBillingSpecGroup::getSpec, specList.stream().map(BizBillingSpec::getSpecName)
                                                                               .collect(Collectors.joining(StrUtil.COMMA)));
            }
        } else if (BooleanUtil.isTrue(request.getIsResult())) {
            return null;
        }

        if (BooleanUtil.isTrue(request.getIsResult()) && Objects.isNull(request.getStrategyId())) {
            return null;
        }
        return bizBillingSpecGroupService.listSpecGroups(queryWrapper, request.getIsSelect());
    }


    /**
     * 删除资源类型(区域)
     *
     * @param id 区域资源类型id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BD04.BD04010102)
    @ApiOperation("删除资源类型(区域)")
    @DeleteMapping("/resource_region/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'资源类型名称'", resource = OperationResourceEnum.DELETE_RESOURCE_TYPE,bizId = "#id", tagNameUs ="'Resource Type Name'")
    public RestResult removeResourceRegion(@PathVariable("id")
                                           @ApiParam(value = "区域资源类型ID", type = "Long", required = true) Long id) {
        bizBillingRegionResourceService.removeRegionResource(id);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 设置资源-计费策略名称
     * @param regionCharges
     * @return
     */
    private String packageChargeStrategyName(List<BizBillingRegionCharge> regionCharges) {
        if (CollectionUtil.isEmpty(regionCharges)) {
            return null;
        }
        int size = regionCharges.size();
        StringBuilder sb = new StringBuilder();
        int n = 1;
        List<Long> strategyIds = regionCharges.stream().map(BizBillingRegionCharge::getStrategyId)
            .collect(Collectors.toList());
        QueryWrapper<BizBillingStrategy> strategyQueryWrapper = new QueryWrapper<>();
        strategyQueryWrapper.lambda().in(BizBillingStrategy::getId, strategyIds);
        IPage<BizBillingStrategy> strategies = strategyService.selectByParams(new Page<>(1, DescribeBizBillingStrategyRequest.DEFAULT_PAGE_SIZE), strategyQueryWrapper);
        Map<Long, BizBillingStrategy> strategyMap = strategies.getRecords().stream()
            .collect(Collectors.toMap(BizBillingStrategy::getId, Function.identity()));

        for (BizBillingRegionCharge regionCharge : regionCharges) {
            BizBillingStrategy strategy = strategyMap.get(regionCharge.getStrategyId());
            if (strategy == null) {
                continue;
            }
            regionCharge.setCategoryName(strategy.getCategoryName());
            sb.append(strategy.getCategoryName()).append(COLON_SEP)
                    .append(strategy.getName());

            if (n < size) {
                sb.append(VERTICAL_SEP);
            }
            n++;
        }
        return sb.toString();
    }

    /**
     * 获取计费项相关配置
     *
     * @param category     可计费资源
     * @param envAccountId 云账号id
     * @param resourceType 资源类型
     * @return {@code DescribeSpecConfigResponse}
     */
    @AuthorizeBss(action = BD04.BD04)
    @ApiOperation("获取计费项相关配置")
    @GetMapping("/spec_config")
    public DescribeSpecConfigResponse getConfig(@ApiParam("可计费资源") @RequestParam(value = "category") String category,
                                                @ApiParam("云账号ID") @RequestParam(value = "envAccountId") Long envAccountId,
                                                @ApiParam("资源类型") @RequestParam(value = "resourceType") String resourceType) {

        return BeanConvertUtil.convert(accountConfigService.getChargeConfig(category, envAccountId, resourceType),
                DescribeSpecConfigResponse.class);
    }

    private boolean checkSpecsModelArts(RegionConfigVO v, Long envAccountId, String resourceType) {
        List<SpecConfigVO> requestSpecs = v.getSpecs();
        if (ProductCodeEnum.OBS.getProductType().equals(resourceType) && !"storageUsage".equals(v.getSpecType())) {
            return  requestSpecs.size() != 1 || !"*".equals(requestSpecs.get(0).getType());
        }
        Map<String, Object> chargeConfig = accountConfigService.getChargeConfig(v.getCategory(), envAccountId, resourceType);
        Object specsObj = chargeConfig.get("specs");
        JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(specsObj));
        List<BizBillingSpec> convert = BeanConvertUtil.convert(jsonArray, BizBillingSpec.class);
        HashMap<String, String> specs = new HashMap<>();
        boolean specTypeValidate = true;
        for (BizBillingSpec bizBillingSpec : convert) {
            if (v.getSpecType().contains(bizBillingSpec.getSpecName())) {
                specTypeValidate = false;
                List<BizBillingSpecRef> specRefs = bizBillingSpec.getSpecRefs();
                specRefs.forEach(ref -> {
                    JSONArray configValue = JSON.parseArray(ref.getValue());
                    configValue.forEach(value -> {
                        String value1 = JSON.parseObject(value.toString()).get("value").toString();
                        String name = JSON.parseObject(value.toString()).get("name").toString();
                        specs.put(name, value1);
                    });
                });
            }
        }
        if (specTypeValidate) {
            return true;
        }

        for (SpecConfigVO specConfigVO : requestSpecs) {
            String type = specs.get(specConfigVO.getTypeName());
            if (StringUtils.isBlank(type) || !type.equals(specConfigVO.getType())) {
                return true;
            }
        }
        return false;
    }

    private boolean checkSpecsByResource(RegionConfigVO v, Long envAccountId, String resourceType) {
        List<SpecConfigVO> requestSpecs = v.getSpecs();
        AvailableResourceRequest request = new AvailableResourceRequest();
        request.setEnvAccountId(envAccountId);
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(resourceType)) {
            request.setType(ResourceType.MA_DRP);
            RestResult resourceFamilyType = feignService.getResourceFamilyType(request);
            Object data = resourceFamilyType.getData();
            if (Objects.isNull(data)) {
                return true;
            }
            HashMap result = BeanConvertUtil.convert(data, HashMap.class);
            Object other = result.get("other");
            if (Objects.isNull(other)) {
                return true;
            }
            HashMap<String, String> specs = new HashMap<>();
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(other));
            jsonArray.forEach(value -> {
                String value1 = JSON.parseObject(value.toString()).get("value").toString();
                String name = JSON.parseObject(value.toString()).get("name").toString();
                specs.put(name+StrUtil.UNDERLINE+value1, value1);
            });
            for (SpecConfigVO specConfigVO : requestSpecs) {
                String type = specs.get(specConfigVO.getTypeName()+StrUtil.UNDERLINE+specConfigVO.getType());
                if (StringUtils.isBlank(type) || !type.equals(specConfigVO.getType())) {
                    return true;
                }
            }
        }
        return false;
    }
}
