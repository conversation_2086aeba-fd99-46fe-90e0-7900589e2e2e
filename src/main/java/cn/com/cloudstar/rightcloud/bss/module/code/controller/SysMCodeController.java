/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.code.controller;


import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.code.service.SysMCodeService;
import cn.com.cloudstar.rightcloud.bss.module.env.pojo.response.CodeResponse;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数字词典
 *
 * <AUTHOR>
 * @createDate 2022/3/9
 */
@RestController
@RequestMapping("/sys/code")
@Api("数字词典")
public class SysMCodeController {
    @Autowired
    private SysMCodeService sysMCodeService;

    /**
     * 查询数字词典
     *
     * @param codeCategory 类别代码
     * @return {@code RestResult}
     */
    @ApiOperation("查询数字词典")
    @GetMapping("/category/{codeCategory}")
    public RestResult<List<Code>> searchCodeList(@PathVariable("codeCategory") String codeCategory) {
        List<Code> codes = sysMCodeService.selectByCodeCategory(codeCategory);
        return new RestResult(BeanConvertUtil.convert(codes, CodeResponse.class));
    }
}
