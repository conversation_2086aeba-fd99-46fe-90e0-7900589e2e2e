package cn.com.cloudstar.rightcloud.bss.module.order.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import io.swagger.annotations.ApiOperation;

import cn.com.cloudstar.rightcloud.bss.config.FeignConfig;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;

 //@FeignClient(name = "http://127.0.0.1:38180", configuration = FeignConfig.class, path = "/api/v1/resource")
@FeignClient(name = "https://cmp-resource:38180", configuration = FeignConfig.class, path = "/api/v1/resource")
public interface ResourceServiceFeign {

    @ApiOperation(httpMethod = "GET", value = "获取云环境信息")
    @GetMapping("/envs/getCloudEnvId")
    RestResult getCloudEnvId();

    @ApiOperation(httpMethod = "GET", value = "获取hcso租户信息")
    @GetMapping("/envs/hcso_user")
    RestResult findHcsoUser(@RequestParam(value = "userSid") Long userSid);
}
