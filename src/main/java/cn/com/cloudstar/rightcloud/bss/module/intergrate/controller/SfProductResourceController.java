/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.OrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.BehalfOrder;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.EditFreezingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.ResourceChangeRecordRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.StopJobRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.HpcDrpChangeRecordResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ServiceCategoryResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaPoolService;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderPriceDetailService;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.FilterOrgFunService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateRelationMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfProductTemplateRelation;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD06;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.FreezingStrategyEnum;
import cn.com.cloudstar.rightcloud.common.util.Il8lUtil;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.BssTypeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CpuArchEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.MaPoolTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.UnitsEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaFlavorVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResChangeRecordParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;

import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;


/**
 * 内置产品资源
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Slf4j
@RestController
@RequestMapping("/products/resources")
@Api("内置产品资源")
@Validated
public class SfProductResourceController {

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private ServiceCategoryService serviceCategoryService;

    @Autowired
    private SfProductTemplateRelationMapper productTemplateRelationMapper;
    @Autowired
    private ResMaPoolService resMaPoolService;
    @DubboReference
    private MaRemoteService maRemoteService;

    @Autowired
    private IBizBillingAccountService accountService;

    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;

    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;

    @Autowired
    private IServiceOrderPriceDetailService serviceOrderPriceDetailService;
    @Autowired
    private ISysMNoticeService sysMNoticeService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    @Lazy
    private ExportService exportService;
    @Autowired
    private BizDownloadMapper bizDownloadMapper;
    @Resource
    private FilterOrgFunService filterOrgFunService;
    /**
     * 查询内置产品资源
     *
     * @param request 查询内置产品资源请求体
     * @return {@code IPage<DescribeProductResourceResponse>}
     */
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C108)
    @ApiOperation("查询内置产品资源")
    @GetMapping
    @DataPermission(resource = OperationResourceEnum.LIST_RESOURCES,bizId = "#request.parentOrgSid")
    public IPage<DescribeProductResourceResponse> listResources(@Valid DescribeProductResourceRequest request) {
        //系统配置表中获取具体的到期时间提醒
        String productType = request.getProductType();
        IPage<DescribeProductResourceResponse> responsePage = sfProductResourceService.listResources(request);
        int days= Integer.parseInt(PropertiesUtil.getProperty("upcoming_expired_days"));
        DateTime now = DateUtil.date();
        List<DescribeProductResourceResponse> records = responsePage.getRecords();

        Set<String> orderResourceSn = records.stream().map(DescribeProductResourceResponse::getServiceOrderId).collect(Collectors.toSet());
        Map<String, Long> behalfOrdersMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderResourceSn)) {
            List<BehalfOrder> behalfOrders =  orderMapper.selectBehalfOrder(orderResourceSn);
            behalfOrdersMap = behalfOrders.stream().filter(e -> e.getBehalfUserSid() != null)
                    .collect(Collectors.toMap(BehalfOrder::getOrderId, BehalfOrder::getBehalfUserSid));
        }

        Map<String,String> flavorMap =new HashMap<>(16);
        for (DescribeProductResourceResponse resourceResponse : records){
            Long behalfUserSid = behalfOrdersMap.get(resourceResponse.getServiceOrderId());
            if (resourceResponse.getBehalfUserSid() == null && behalfUserSid != null) {
                resourceResponse.setBehalfUserSid(behalfUserSid);
            }

            String poolType = resourceResponse.getPoolType();
            if (StringUtils.isBlank(poolType)) {
                resourceResponse.setPoolType(MaPoolTypeEnum.DEDICATE.getType());
            }
            String cpuArch = resourceResponse.getCpuArch();
            if (StringUtils.isBlank(cpuArch)) {
                resourceResponse.setCpuArch(CpuArchEnum.X86.getType());
            }

            if( SfProductEnum.NORMAL.getStatus().equals(resourceResponse.getStatus()) && !StringUtil.isNullOrEmpty(resourceResponse.getEndTime())){
                //时间差
                if (0 <= DateUtils.addDays(now, days).compareTo(resourceResponse.getEndTime()) ) {
                    resourceResponse.setStatus(SfProductEnum.SOONEXPIRE.getStatus());
                }
            }
            //MA专属资源池
            if(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productType)){
                //判断是开发训练开始线上部署 2--开发训练，1-线上部署
                if(ApplyTypeEnum.DEPTRAIN.getType().equals(resourceResponse.getApplyType())
                        && Objects.nonNull(resourceResponse.getClusterId())) {
                    RestResult resMaPool = resMaPoolService.getResMaPoolInfo(resourceResponse.getClusterId());

                    if (Objects.nonNull(resMaPool.getData())) {
                        Object data = resMaPool.getData();
                        ResMaClusterRemoteModule resMaClusterRemoteModule = BeanConvertUtil.convert(data, ResMaClusterRemoteModule.class);
                        String flavor = resMaClusterRemoteModule.getFlavor();
                        //转换规格
                        String nodeSpec = this.getNodeSpec(flavorMap, flavor);
                        resourceResponse.setNodeSpec(nodeSpec);
                        if (Objects.nonNull(resMaClusterRemoteModule.getAvailableCount()) &&
                                Objects.nonNull(resMaClusterRemoteModule.getMaxCount())) {
                            resourceResponse.setNodeInfo(resMaClusterRemoteModule.getAvailableCount() + "/"
                                                                 + resMaClusterRemoteModule.getMaxCount());
                            resourceResponse.setMaxCount(resMaClusterRemoteModule.getMaxCount());
                        }
                        resourceResponse.setDescription(resMaClusterRemoteModule.getDescription());
                    }
                }
                //添加备注
                ServiceOrderDetail orderDetail = serviceOrderDetailService.getOne(new QueryWrapper<ServiceOrderDetail>()
                                                                                          .eq("order_id", resourceResponse.getServiceOrderId()));
                if (orderDetail != null) {
                    String configDesc = orderDetail.getProductConfigDesc();
                    JsonNode serviceConfigJsonNode = JsonUtil.fromJson(configDesc);
                    if (Objects.nonNull(serviceConfigJsonNode)) {
                        serviceConfigJsonNode.forEach(node -> {
                            if (node instanceof ArrayNode) {
                                ArrayNode arrayNode = (ArrayNode) node;
                                for (JsonNode objectNode : arrayNode) {
                                    JsonNode label = objectNode.get("label");
                                    JsonNode val = objectNode.get("value");
                                    if (label != null
                                            && val != null
                                            && "备注".equals(label.textValue())) {
                                        resourceResponse.setDescription(val.textValue());
                                    }
                                }
                                return;
                            }
                            ObjectNode objectNode = (ObjectNode) node;
                            JsonNode label = objectNode.get("label");
                            JsonNode val = objectNode.get("value");
                            if (label != null
                                    && val != null
                                    && "备注".equals(label.textValue())) {
                                resourceResponse.setDescription(val.textValue());
                            }
                        });
                    }
                }
                this.setNodeInfo(resourceResponse);
            }
            //订单在不扣区间内，不准扩缩容，需要待不扣区间后才能扩缩送
            List<ServiceOrderPriceDetail>  serviceOrderPriceDetails = serviceOrderPriceDetailService.list(new QueryWrapper<ServiceOrderPriceDetail>()
                    .eq("order_sn", resourceResponse.getOrderSn()).orderByDesc("end_time"));
            if(CollectionUtil.isNotEmpty(serviceOrderPriceDetails)){
                List<ServiceOrderPriceDetail>  expiredLists =  serviceOrderPriceDetails.stream().filter(s->s.getType().equals("expired")).collect(Collectors.toList());
                List<ServiceOrderPriceDetail>  renewLists =  serviceOrderPriceDetails.stream().filter(s->s.getType().equals("renew")).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(expiredLists)){
                    ServiceOrderPriceDetail expireOrderPriceDetail = expiredLists.get(0);
                    //过期开始时间
                    Date  expiredStartTime = expireOrderPriceDetail.getStartTime();
                    ServiceOrderPriceDetail renewOrderPriceDetail = renewLists.get(0);
                    //续订开始时间
                    Date  renewStartTime = renewOrderPriceDetail.getStartTime();
                    // 当前时间
                    Date currentTime = new Date();
                    if(currentTime.after(expiredStartTime) && currentTime.before(renewStartTime)){
                        resourceResponse.setExpansionAndContractionFlag(false);
                    }

                }
            }

            // 设置算力
            this.setNodeSpec(resourceResponse);
            this.setApplyTypeDesc(resourceResponse);
        }
        return responsePage;
    }

    private void setApplyTypeDesc(DescribeProductResourceResponse resourceResponse) {
        String configDesc = resourceResponse.getConfigDesc();
        JSONObject object = JSON.parseObject(configDesc);
        try {
            JSONObject productConfigDescObj = object.getJSONObject("productConfigDesc");
            JSONArray array = JSON.parseArray(productConfigDescObj.getString("currentConfigDesc"));
            for (int i = 0; i < array.size();  i++) {
                JSONObject obj = array.getJSONObject(i);
                String attrKey = obj.getString("attrKey");
                if (attrKey.equals("applyType")) {
                    String value = obj.getString("value");
                    resourceResponse.setApplyTypeDesc(value);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("查询MA专属列表设置类型名失败， error: {}", e.getMessage());
        }
    }

    private void setNodeInfo(DescribeProductResourceResponse resourceResponse) {
        String applyType = resourceResponse.getApplyType();
        String configDesc = resourceResponse.getConfigDesc();
        JSONObject object = JSON.parseObject(configDesc);
        if (StringUtils.isNotBlank(applyType) && applyType.equals(ApplyTypeEnum.DEPONLINE.getType())) { /* 部署上线 */
            try {
                JSONObject productConfigDescObj = object.getJSONObject("productConfigDesc");
                JSONArray array = JSON.parseArray(productConfigDescObj.getString("currentConfigDesc"));
                for (int i = 0; i < array.size();  i++) {
                    JSONObject obj = array.getJSONObject(i);
                    String attrKey = obj.getString("attrKey");
                    if (attrKey.equals("count")) {
                        String value = obj.getString("value");
                        resourceResponse.setNodeInfo(value + "/" + value);
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("查询MA专属列表设置部署上线类型节点数失败， error: {}", e.getMessage());
            }
        }
    }

    private void setNodeSpec(DescribeProductResourceResponse resourceResponse) {
        String applyType = resourceResponse.getApplyType();
        String configDesc = resourceResponse.getConfigDesc();
        JSONObject object = JSON.parseObject(configDesc);
        if (StringUtils.isNotBlank(applyType) && applyType.equals(ApplyTypeEnum.DEPONLINE.getType())) { /* 部署上线 */
            BigDecimal computingPower = object.getBigDecimal("computingPower");
            try {
                JSONObject productConfigDescObj = object.getJSONObject("productConfigDesc");
                JSONArray array = JSON.parseArray(productConfigDescObj.getString("currentConfigDesc"));
                for (int i = 0; i < array.size();  i++) {
                    JSONObject obj = array.getJSONObject(i);
                    String attrKey = obj.getString("attrKey");
                    if (attrKey.equals("calculation") || attrKey.equals("calculation_depOnline")) {
                        String value = obj.getString("value");
                        resourceResponse.setNodeSpec(value);
                        if (computingPower != null) {
                            resourceResponse.setComputingPower(computingPower.divide(BigDecimal.ONE, 2, BigDecimal.ROUND_HALF_UP) + " " + UnitsEnum.COMPUTINGPOWER.getUnit());
                        }
                        break;
                    }

                }
            } catch (Exception e) {
                log.error("查询MA专属列表设置部署上线类型专属规格失败， error: {}", e.getMessage());
            }
        } else {
            BigDecimal unitComputingPower = object.getBigDecimal("unitComputingPower");
            if (unitComputingPower != null) {
                BigDecimal amount = object.getBigDecimal("amount");
                resourceResponse.setComputingPower(unitComputingPower.multiply(amount).divide(BigDecimal.ONE, 2, BigDecimal.ROUND_HALF_UP) + " " + UnitsEnum.COMPUTINGPOWER.getUnit());
                resourceResponse.setNodeSpec(resourceResponse.getNodeSpec() + " " + unitComputingPower + UnitsEnum.COMPUTINGPOWER.getUnit());
            }
        }
    }

    /**
     * [INNER API] 查询内置产品资源
     *
     * @param request 查询内置产品资源请求体
     * @return {@code IPage<DescribeProductResourceResponse>}
     */
    @RejectCall
    @ApiOperation("查询内置产品资源")
    @GetMapping("/feign")
    public IPage<DescribeProductResourceResponse> listResourcesByFeign(
            @Valid DescribeProductResourceRequest request) {
        //系统配置表中获取具体的到期时间提醒
        String productType = request.getProductType();
        if (Objects.nonNull(request.getAccountId())) {
            BizBillingAccount account = accountService.getById(request.getAccountId());
            if (Objects.isNull(account)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            request.setParentOrgSid(account.getOrgSid());
            request.setAccountId(null);
        }
        IPage<DescribeProductResourceResponse> responsePage = sfProductResourceService.listResourcesFeign(request);
        int days = Integer.parseInt(PropertiesUtil.getProperty("upcoming_expired_days"));
        Date now = DateUtil.date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.DATE, + days);
        List<DescribeProductResourceResponse> records = responsePage.getRecords();

        Set<String> orderResourceSn = records.stream().map(DescribeProductResourceResponse::getServiceOrderId).collect(Collectors.toSet());
        Map<String, Long> behalfOrdersMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderResourceSn)) {
            List<BehalfOrder> behalfOrders =  orderMapper.selectBehalfOrder(orderResourceSn);
            behalfOrdersMap = behalfOrders.stream().filter(e -> e.getBehalfUserSid() != null)
                    .collect(Collectors.toMap(BehalfOrder::getOrderId, BehalfOrder::getBehalfUserSid));
        }

        Map<String,String> flavorMap =new HashMap<>();
        for (DescribeProductResourceResponse resourceResponse : records){
            Long behalfUserSid = behalfOrdersMap.get(resourceResponse.getServiceOrderId());
            if (resourceResponse.getBehalfUserSid() == null && behalfUserSid != null) {
                resourceResponse.setBehalfUserSid(behalfUserSid);
            }

            if (StringUtils.isBlank(resourceResponse.getFreezingStrategy())) {
                if (StringUtils.equals(ProductCodeEnum.MODEL_ARTS.getProductType(), resourceResponse.getProductType())) {
                    resourceResponse.setFreezingStrategy(FreezingStrategyEnum.CONTINUE_OPERATION.getCode());
                } else if (StringUtils.equals(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType(), resourceResponse.getProductType())) {
                    resourceResponse.setFreezingStrategy(FreezingStrategyEnum.RESERVED_RESOURCE.getCode());
                }
            }
            if( SfProductEnum.NORMAL.getStatus().equals(resourceResponse.getStatus()) && !StringUtil.isNullOrEmpty(resourceResponse.getEndTime())){
                //时间差
                if (now.getTime() <= resourceResponse.getEndTime().getTime()
                        && cal.getTime().getTime() >= resourceResponse.getEndTime().getTime()) {
                    resourceResponse.setStatus(SfProductEnum.SOONEXPIRE.getStatus());
                }
            }
            //MA专属资源池
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(productType)) {
                //判断是开发训练开始线上部署 2--开发训练，1-线上部署
                if (ApplyTypeEnum.DEPTRAIN.getType().equals(resourceResponse.getApplyType())
                        && Objects.nonNull(resourceResponse.getClusterId())) {
                    RestResult resMaPool = resMaPoolService.getResMaPoolInfo(resourceResponse.getClusterId());

                    if (Objects.nonNull(resMaPool.getData())) {
                        Object data = resMaPool.getData();
                        ResMaClusterRemoteModule resMaClusterRemoteModule = BeanConvertUtil.convert(data, ResMaClusterRemoteModule.class);
                        String flavor = resMaClusterRemoteModule.getFlavor();
                        //转换规格
                        String nodeSpec = this.getNodeSpec(flavorMap, flavor);
                        resourceResponse.setNodeSpec(nodeSpec);
                        if (Objects.nonNull(resMaClusterRemoteModule.getAvailableCount()) &&
                                Objects.nonNull(resMaClusterRemoteModule.getMaxCount())) {
                            resourceResponse.setNodeInfo(resMaClusterRemoteModule.getAvailableCount() + "/"
                                    + resMaClusterRemoteModule.getMaxCount());
                        }
                        resourceResponse.setDescription(resMaClusterRemoteModule.getDescription());
                    }
                }
                //添加备注
                ServiceOrderDetail orderDetail = serviceOrderDetailService.getOne(new QueryWrapper<ServiceOrderDetail>()
                                                                                          .eq("order_id", resourceResponse.getServiceOrderId()));
                if (orderDetail != null) {
                    String configDesc = orderDetail.getProductConfigDesc();
                    JsonNode serviceConfigJsonNode = JsonUtil.fromJson(configDesc);
                    if (Objects.nonNull(serviceConfigJsonNode)) {
                        serviceConfigJsonNode.forEach(node -> {
                            if (node instanceof ArrayNode) {
                                ArrayNode arrayNode = (ArrayNode) node;
                                for (JsonNode objectNode : arrayNode) {
                                    JsonNode label = objectNode.get("label");
                                    JsonNode val = objectNode.get("value");
                                    if (label != null
                                            && val != null
                                            && "备注".equals(label.textValue())) {
                                        resourceResponse.setDescription(val.textValue());
                                    }
                                }
                                return;
                            }
                            ObjectNode objectNode = (ObjectNode) node;
                            JsonNode label = objectNode.get("label");
                            JsonNode val = objectNode.get("value");
                            if (label != null
                                    && val != null
                                    && "备注".equals(label.textValue())) {
                                resourceResponse.setDescription(val.textValue());
                            }
                        });
            }
        }
                this.setNodeInfo(resourceResponse);
            }
            // 设置算力
            this.setNodeSpec(resourceResponse);
            this.setApplyTypeDesc(resourceResponse);
        }

        // 国际化处理
        if (WebUtil.getHeaderAcceptLanguage()) {
            records.forEach(response -> response.setConfigDesc(Il8lUtil.il8l(response.getConfigDesc())));
        }
        return responsePage;
    }


    private String getNodeSpec(Map<String, String> flavorMap, String flavor) {
        if (StringUtils.isNotEmpty(flavor)) {
            String nodeSpec = flavorMap.get(flavor);
            if (StringUtils.isNotEmpty(nodeSpec)) {
                return nodeSpec;
            }
            List<ResMaFlavorVO> resMaFalorByName = maRemoteService.getResMaFalorByName(flavor);
            if (CollectionUtil.isNotEmpty(resMaFalorByName)) {
                ResMaFlavorVO flavorVO = CollectionUtil.getFirst(resMaFalorByName);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(flavorVO.getCpuNum()).append("vCPU");
                String memory = flavorVO.getMemory();
                if (StringUtils.isNotEmpty(memory)) {
                    stringBuffer.append("、").append(memory).append("内存");
                }
                String npuNum = flavorVO.getNpuNum();
                if (StringUtils.isNotEmpty(npuNum)) {
                    stringBuffer.append("、").append(npuNum).append("NPU");
                }
                return stringBuffer.toString();
            }

        }
        return flavor;
    }



    /**
     * 删除云环境时查询是否还有未过期资源
     *
     * @param envId 云环境id
     * @return {@code RestResult}
     */
    @ApiOperation("删除云环境时查询是否还有未过期资源")
    @GetMapping("/listResourcesByEnvId/{envId}")
    @AuthorizeBss(action = AuthModule.ZG.ZG0105)
    public RestResult<Map<String, Object>> listResourcesByEnvId(
            @PathVariable Long envId) {
        return sfProductResourceService.listResourcesByEnvId(envId);
    }

    /**
     * 查询内置产品资源详情
     *
     * @param id id
     * @return {@code DescribeProductResourceSimpleResponse}
     */
    @ApiOperation("查询内置产品资源详情")
    @GetMapping("/{id}")
    @AuthorizeBss(action = AuthModule.BD.BD01.MA_RELEASE)
    public RestResult<DescribeProductResourceResponse> getDetail(@PathVariable Long id) {
        return new RestResult(BeanConvertUtil.convert(sfProductResourceService.getDetail(id), DescribeProductResourceResponse.class));
    }


    /**
     * [INNER API] 查询内置产品资源详情
     *
     * @param id id
     * @return {@code DescribeProductResourceSimpleResponse}
     */
    @RejectCall
    @ApiOperation("查询内置产品资源详情")
    @GetMapping("/feign/{id}")
    public RestResult<DescribeProductResourceResponse> getFeginDetail(@PathVariable Long id) {
        return new RestResult(BeanConvertUtil.convert(sfProductResourceService.getDetail(id), DescribeProductResourceResponse.class));
    }


    /**
     * 获取用户产品是否开通
     *
     * @param productType 产品类型
     * @param userId      用户id
     * @return boolean
     */
    @GetMapping("/status/{productType}/{userId}")
    @AuthorizeBss(action = AuthModule.CB.CB19)
    @ApiOperation(httpMethod = "GET", value = "获取用户产品是否开通", notes = "获取用户产品是否开通")
    public boolean getProductStatus(@PathVariable @ApiParam(name = "productType", required = true) String productType,
                                    @PathVariable @ApiParam(name = "userId", required = true) Long userId) {
        User authUser = AuthUtil.getAuthUser();
        if(!authUser.getUserSid().equals(userId)){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        // 默认返回false：未开通
        boolean result = false;
        ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(productType, userId);
        if (Objects.nonNull(productOrderInfo)) {
            if (ProductCodeEnum.SFS2.getProductType().equals(productType)) {
                return ProductCodeEnum.SFS2.getProductType().equals(productOrderInfo.getServiceType())
                        || sfProductResourceService.hpcExist("", AuthUtil.getAuthUser().getOrgSid(), Convert.toLong(productOrderInfo.getServiceId(), 0L));
            }
            String type = productOrderInfo.getType();
            String status = productOrderInfo.getStatus();
            // 开通成功
            if (OrderType.APPLY.equals(type) && OrderStatus.COMPLETED.equals(status)) {
                result = true;
                // 退订中
            } else if (OrderType.RELEASE.equals(type) && !OrderStatus.COMPLETED.equals(status)) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 获取用户产品是否开通
     *
     * @param productTypes 产品类型，列表逗号分隔
     * @param userId      用户id
     * @return Map
     */
    @GetMapping("/productStatus/{userId}")
    @ApiOperation(httpMethod = "GET", value = "获取用户产品是否开通", notes = "获取用户产品是否开通")
    public RestResult<Map<String, Boolean>> getProductListStatus(@RequestParam String productTypes,
                                    @PathVariable @ApiParam(name = "userId", required = true) Long userId) {
        User authUser = AuthUtil.getAuthUser();
        if(!authUser.getUserSid().equals(userId)){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        HashMap<String, Boolean> resultMap = new HashMap<>();
        for (String s : productTypes.split(",")) {
            resultMap.put(s, false);
        }
        List<ServiceOrderVo> productOrderInfos = sfProductResourceService.getProductOrderInfos(productTypes, userId);
        if (CollectionUtil.isNotEmpty(productOrderInfos)) {
            for (ServiceOrderVo productOrderInfo : productOrderInfos) {
                if (ProductCodeEnum.SFS2.getProductType().equals(productOrderInfo.getServiceType())) {
                    resultMap.put(productOrderInfo.getServiceType(), ProductCodeEnum.SFS2.getProductType().equals(productOrderInfo.getServiceType())
                            || sfProductResourceService.hpcExist("", AuthUtil.getAuthUser().getOrgSid(), Convert.toLong(productOrderInfo.getServiceId(), 0L)));
                }else {
                    String type = productOrderInfo.getType();
                    String status = productOrderInfo.getStatus();
                    // 开通成功
                    if (OrderType.APPLY.equals(type) && OrderStatus.COMPLETED.equals(status)) {
                        resultMap.put(productOrderInfo.getServiceType(), true);
                        // 退订中
                    } else if (OrderType.RELEASE.equals(type) && !OrderStatus.COMPLETED.equals(status)) {
                        resultMap.put(productOrderInfo.getServiceType(), true);
                    }
                }
            }
        }
        return new RestResult<>(resultMap);
    }

    @GetMapping("/status/{productType}/{userId}/feign")
    //@ApiOperation(httpMethod = "GET", value = "获取用户产品是否开通", notes = "获取用户产品是否开通")
    public boolean getProductStatusFeign(@PathVariable @ApiParam(name = "productType", required = true) String productType,
                                    @PathVariable @ApiParam(name = "userId", required = true) Long userId) {
        // 默认返回false：未开通
        boolean result = false;
        ServiceOrderVo productOrderInfo = sfProductResourceService.getProductOrderInfo(productType, userId);
        if (Objects.nonNull(productOrderInfo)) {
            if (ProductCodeEnum.SFS2.getProductType().equals(productType)) {
                return ProductCodeEnum.SFS2.getProductType().equals(productOrderInfo.getServiceType())
                        || sfProductResourceService.hpcExist("", AuthUtil.getAuthUser().getOrgSid(), Convert.toLong(productOrderInfo.getServiceId(), 0L));
            }
            String type = productOrderInfo.getType();
            String status = productOrderInfo.getStatus();
            // 开通成功
            if (OrderType.APPLY.equals(type) && (OrderStatus.COMPLETED.equals(status) || OrderStatus.SUCCEED.equals(status))) {
                result = true;
                // 退订中
            } else if (OrderType.RELEASE.equals(type) && !OrderStatus.COMPLETED.equals(status)) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 获取产品对应没有关闭的订单
     *
     * @param productTypes 产品类型
     * @param parentOrgSid 父组织sid
     * @return {@code List<Long>}
     */
    @GetMapping("/status/open/orders")
    @AuthorizeBss(action = AuthModule.BD.BD01.MA_RELEASE_DETAIL)
    @ApiOperation(httpMethod = "GET", value = "获取产品对应没有关闭的订单", notes = "获取产品对应没有关闭的订单")
    @DataPermission(resource = OperationResourceEnum.GET_PRODUCT_OPEN_ORDERS,bizId = "#parentOrgSid")
    public List<Long> getProductOpenOrders(
            @ApiParam(value = "产品类型") @RequestParam(value = "productType", required = true) List<String> productTypes,
            @ApiParam(value = "父级组织ID") @RequestParam(value = "parentOrgSid", required = true) Long parentOrgSid) {
        List<DescribeProductResourceResponse> describeProductResourceResponses = sfProductResourceService.actionResources(productTypes, parentOrgSid);
        return describeProductResourceResponses
                .stream()
                .map(DescribeProductResourceResponse::getId)
                .collect(Collectors.toList());
    }

    /**
     * [INNER API] 内置代客下单产品列表
     *
     * @return {@code RestResult}
     */
    @RejectCall
    @GetMapping("/behalf/service")
    @ApiOperation("内置代客下单产品列表")
    public RestResult<List<ServiceCategoryResponse>> queryBehalfService() {
        QueryWrapper<ServiceCategory> serviceCategoryQueryWrapper = new QueryWrapper<>();
        serviceCategoryQueryWrapper.lambda()
                                   .eq(ServiceCategory::getBehalfStatus, "1")
                                   .eq(ServiceCategory::getEntityId,RequestContextUtil.getEntityId() != null ? RequestContextUtil.getEntityId():-1);
        List<ServiceCategory> serviceCategories = serviceCategoryService.list(serviceCategoryQueryWrapper);

        serviceCategories.forEach(serviceCategory -> {

            Map<String, Object> params = Maps.newHashMap();
            params.put("product_id", serviceCategory.getId());
            SfProductTemplateRelation productTemplateRelations = CollectionUtil.getFirst(productTemplateRelationMapper.selectByMap(params));
            serviceCategory.setTemplateId(Objects.isNull(productTemplateRelations) ? null : productTemplateRelations.getTemplateId());
        });
        List<ServiceCategoryResponse> serviceCategoryResponseList = BeanConvertUtil.convert(serviceCategories, ServiceCategoryResponse.class);
        return new RestResult(serviceCategoryResponseList);
    }

    /**
     * 导出AI基础服务列表
     *
     * @param request  查询内置产品资源请求体
     * @param response 响应
     */
    @AuthorizeBss(action = AuthModule.BD.BD06.BD06)
    @ApiOperation(httpMethod = "GET",value = "导出AI基础服务列表")
    @GetMapping("/export_resources")
    @Idempotent
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'AI基础服务列表'", tagNameUs ="'List of basic services'",
            resource = OperationResourceEnum.EXPORT_PRODUCT_RESOURCE,param = "#request")
    public RestResult getListResources(DescribeProductResourceRequest request, HttpServletResponse response){
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }

        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.EXPORT_RESOURCES_AI.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.MODELARTS_DATA_DOWNLOAD_ERROR));
        }
        new ExportThreadUtil(exportService,
                request,
                ModuleTypeConstants.FROM_BSS,
                ExportTypeEnum.EXPORT_RESOURCES_AI.getCode(),
                download.getDownloadId(),
                authUserInfo
        ).submit();

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.MODELARTS_DATA_DOWNLOAD_WAIT));
    }

    private BizDownload getBizDownload(BizDownload download, DescribeProductResourceRequest request, AuthUser authUserInfo) {
        // 租户在下载任务中存入accountId
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam(JSON.toJSONString(request));
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        int insert = bizDownloadMapper.insert(download);
        return download;
    }

    /**
     * 【Since v2.5.0】获取资源变更记录
     *
     * @param request 请求
     *
     * @return {@link List}<{@link ?} {@link extends} {@link ResChangeRecordDTO}>
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET",value = "获取资源变更记录")
    public List<? extends ResChangeRecordDTO> changeRecord(ResourceChangeRecordRequest request){
        String resourceId = request.getResourceId();
        if (StringUtils.isEmpty(resourceId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        ResChangeRecordParams resChangeRecordParams = new ResChangeRecordParams();
        resChangeRecordParams.setResType(request.getProductType());
        resChangeRecordParams.setResourceId(resourceId);
        resChangeRecordParams.setOrderByClause(" created_dt desc ");
        List<ResChangeRecordDTO> resChangeRecordDTOS = resChangeRecordRemoteService.selectByParams(resChangeRecordParams);
        if (ProductCodeEnum.HPC_DRP.getProductType().equals(request.getProductType())) {
            List<HpcDrpChangeRecordResponse> responseList = new ArrayList<>();
            resChangeRecordDTOS.stream().forEach(resChangeRecordDTO ->{
                if(resChangeRecordDTO.getChangeEndTime() == null){
                    return;
                }
                HpcDrpChangeRecordResponse recordResponse = BeanConvertUtil.convert(resChangeRecordDTO, HpcDrpChangeRecordResponse.class);
                //设置变更的节点数
                assembleChangeExtra(recordResponse);

                if(resChangeRecordDTO.getNewType() !=null && resChangeRecordDTO.getOriginalType() !=null){
                    if (Long.valueOf(resChangeRecordDTO.getNewType()) > Long.valueOf(resChangeRecordDTO.getOriginalType())) {
                        recordResponse.setChangeType(OrderType.UPGRADE);
                    }else if (Long.valueOf(resChangeRecordDTO.getNewType()) < Long.valueOf(resChangeRecordDTO.getOriginalType())){
                      //  recordResponse.setChangeType(OrderType.DEGRADE);
                    }else {
                       // recordResponse.setChangeType(resChangeRecordDTO.getChangeType());
        }
                    recordResponse.setStatus("1");
                }else{
                   // recordResponse.setChangeType(resChangeRecordDTO.getChangeType());
                }
                responseList.add(recordResponse);
            });
            return responseList;
        }
        return resChangeRecordDTOS;
    }

    private void assembleChangeExtra(HpcDrpChangeRecordResponse recordResponse) {
        String vncNumKey = "VNC节点数";
        String cliNumKey = "登录节点数";
        String computeNumKey = "计算节点数";

        String originalExtra = recordResponse.getOriginalExtra();
        String newExtra = recordResponse.getNewExtra();
        if (StringUtils.isNotEmpty(originalExtra) && StringUtils.isNotEmpty(newExtra)) {

            JSONObject oldJsonObject = JSON.parseObject(originalExtra);

            Long vncNum = oldJsonObject.getLong(vncNumKey);
            Long cliNum = oldJsonObject.getLong(cliNumKey);
            Long computeNum = oldJsonObject.getLong(computeNumKey);

            JSONObject newJsonObject = JSON.parseObject(newExtra);
            Long newVncNum = newJsonObject.getLong(vncNumKey);
            Long newCliNum = newJsonObject.getLong(cliNumKey);
            Long newComputeNum = newJsonObject.getLong(computeNumKey);

            Long changeVncNum = Optional.ofNullable(newVncNum).orElse(0L)-Optional.ofNullable(vncNum).orElse(0L);
            Long changeCliNum =Optional.ofNullable(newCliNum).orElse(0L)-Optional.ofNullable(cliNum).orElse(0L);
            Long changeComputeNum =Optional.ofNullable(newComputeNum).orElse(0L)-Optional.ofNullable(computeNum).orElse(0L);
            Map<String,Long> nodeMap = new HashMap<>(3);
            nodeMap.put(computeNumKey,changeComputeNum);
            nodeMap.put(cliNumKey,changeCliNum);
            nodeMap.put(vncNumKey,changeVncNum);
            recordResponse.setChangeExtra(JSON.toJSONString(nodeMap));
        }else{
            Map<String,Long> nodeMap = new HashMap<>(3);
            nodeMap.put(computeNumKey,0L);
            nodeMap.put(cliNumKey,0L);
            nodeMap.put(vncNumKey,0L);
            recordResponse.setChangeExtra(JSON.toJSONString(nodeMap));

            if (StringUtils.isBlank(newExtra)) {
                recordResponse.setNewExtra(originalExtra);
            }
        }
    }

    /**
     * 专属资源产品名重复性校验
     *
     * @param name 名字
     * @return {@code Boolean}
     */
    @ApiOperation(httpMethod = "GET", value = "专属资源产品名重复性校验")
    @AuthorizeBss(action = CB.CB1901)
    public Boolean repeatabilityOfCheck(String name) {
        DescribeProductResourceRequest describeProductResourceRequest = new DescribeProductResourceRequest();
        describeProductResourceRequest.setProductType("DRP");
        QueryWrapper queryWrapper = new QueryWrapper<SfProductResource>().eq("product_type", "DRP");
        int count = sfProductResourceService.count(queryWrapper);
        describeProductResourceRequest.setPagenum(0L);
        describeProductResourceRequest.setPagesize(Long.parseLong(String.valueOf(count)));
        IPage<DescribeProductResourceResponse> responsePage = sfProductResourceService.listResources(describeProductResourceRequest);
        List<DescribeProductResourceResponse> describeProductResourceResponseList = responsePage.getRecords();
        for (DescribeProductResourceResponse describeProductResourceResponse:describeProductResourceResponseList){
            if (!StringUtil.isNullOrEmpty(describeProductResourceResponse.getName()) && describeProductResourceResponse.getName().equals(name)) {
                return false;
            }
        }
        return true;
    }

    /**
     * MA重名性校验
     */
    @AuthorizeBss(action = CB.CB1901)
    @ApiOperation(httpMethod = "GET", value = "MA专属资源池名称check")
    @GetMapping("/check/{clusterName}")
    public Boolean checkHpcClusterName(@PathVariable("clusterName") String clusterName) {
        return sfProductResourceService.checkMaName(clusterName);
    }

    /**
     * 修改实例冻结策略
     *【Since v2.5.0】
     * [INNER API] 修改实例冻结策略
     * @param request 请求
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "")
    @PutMapping("/modify/freezing_strategy")
    @RejectCall
    public RestResult<BaseGridReturn> editFreezingStrategy(@Valid @RequestBody EditFreezingStrategyRequest request) {
        if (Objects.isNull(FreezingStrategyEnum.findByCode(request.getFreezingStrategy()))) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        SfProductResource resource = sfProductResourceService.getById(request.getId());
        if (ObjectUtils.isEmpty(resource)) {
            BizException.e("该资源不存在");
        }
        if (FreezingStrategyEnum.RELEASE_RESOURCE.getCode().equals(request.getFreezingStrategy())
                && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(resource.getProductType())
                && MaPoolTypeEnum.DEDICATE
                .getType().equals(resource.getPoolType()) && 1 == resource.getMaVersion()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1966439109));
        }
        if (Objects.isNull(resource)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!ArrayUtils.contains(
                new String[]{SfProductEnum.NORMAL.status, SfProductEnum.RENEWING.status, SfProductEnum.DEGRADING.status,
                        SfProductEnum.UPGRADING.status}, resource.getStatus())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return sfProductResourceService.editFreezingStrategy(request);
    }

    /**
     * 查询是否存在资源使用释放资源策略
     * [INNER API] MA专属到期消息策略
     *
     * @param bssType hpc专属到期通知/AI专属到期通知
     * @return {@code RestResult}
     * @since 2023/04/12 15:22
     */
    @ApiOperation(httpMethod = "GET", value = "")
    @GetMapping("/exist_freezing")
    @RejectCall
    public RestResult existFreezing(String bssType) {
        LambdaQueryWrapper<SfProductResource> qw = new LambdaQueryWrapper<>();
        qw.eq(SfProductResource::getFreezingStrategy, FreezingStrategyEnum.RELEASE_RESOURCE.getCode());
        qw.notIn(SfProductResource::getStatus, SfProductEnum.UNSUBSCRIBED, SfProductEnum.DELETED);
        if (BssTypeEnum.DRP_RESOURCE_EXPIRE.getValue().equals(bssType)) {
            qw.eq(SfProductResource::getProductType, ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
        } else if (BssTypeEnum.HPC_DRP_RESOURCE_EXPIRE.getValue().equals(bssType)) {
            qw.eq(SfProductResource::getProductType, ProductCodeEnum.HPC_DRP.getProductType());
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //把冻结使用改为暂不处理，需把资源信息改为保留资源
        int count = sfProductResourceService.count(qw);
        if (0 < count) {
            LambdaUpdateWrapper<SfProductResource> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SfProductResource::getFreezingStrategy, FreezingStrategyEnum.RELEASE_RESOURCE.getCode())
                    .notIn(SfProductResource::getStatus, SfProductEnum.UNSUBSCRIBED, SfProductEnum.DELETED);
            if (BssTypeEnum.DRP_RESOURCE_EXPIRE.getValue().equals(bssType)) {
                updateWrapper.eq(SfProductResource::getProductType, ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType());
            } else if (BssTypeEnum.HPC_DRP_RESOURCE_EXPIRE.getValue().equals(bssType)) {
                updateWrapper.eq(SfProductResource::getProductType, ProductCodeEnum.HPC_DRP.getProductType());
            }
            updateWrapper.set(SfProductResource::getFreezingStrategy, FreezingStrategyEnum.RESERVED_RESOURCE.getCode()).set(SfProductResource::getStrategyBufferPeriod, null);
            sfProductResourceService.update(updateWrapper);
        }
        return new RestResult(Boolean.FALSE);
    }

    /**
     * 构建配置
     *
     * @param jsonNode json节点
     * @return {@link String}
     */
    private String buildConfig(JsonNode jsonNode) {
        StringBuilder builder = new StringBuilder();
        for (JsonNode node : jsonNode) {
            ObjectNode objectNode = (ObjectNode)node;
            String label = objectNode.get("label").textValue();
            switch (label) {
                case "资源池类型":
                    builder.append("资源池类型：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "作业类型":
                    builder.append("作业类型：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "计费类型":
                    builder.append("计费类型：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "架构":
                    builder.append("架构：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "计算规格":
                    builder.append("计算规格：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "数量":
                    builder.append("数量：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "时长":
                    builder.append("时长：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "备注":
                    builder.append("备注：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                case "资源池到期策略":
                    builder.append("资源池到期策略：").append(objectNode.get("value")).append(String.valueOf((char)10));
                    continue;
                default:
                    continue;
            }
        }
        return builder.toString();
    }

    /**
     * 【Since v2.5.0】 停止共享资源池作业
     * [INNER API] 停止共享资源池作业
     *
     * @param request 请求
     *
     * @return RestResult
     *
     * @Since 2023/04/13
     */
    @RejectCall
    @PostMapping("/stop_job")
    public RestResult stopJob(@Valid @RequestBody StopJobRequest request) {
        SfProductResource resource = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(resource) || !ArrayUtils.contains(new String[]{
                ProductCodeEnum.HPC_SAAS.getProductType(), ProductCodeEnum.HPC.getProductType()}, resource.getProductType())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!ArrayUtils.contains(
                new String[]{SfProductEnum.NORMAL.status, SfProductEnum.EXPIRED.status, SfProductEnum.UNSUBSCRIBING.status,SfProductEnum.FROZEN.status}
                , resource.getStatus())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return sfProductResourceService.stopJob(request);
    }


    /**
     * 根据当前运营实体获取运营实体所绑定的产品
     *
     * @return {@link List}<{@link String}>
     */
    @ApiOperation(httpMethod = "GET",value = "根据当前运营实体获取运营实体所绑定的产品")
    @GetMapping("/productsByEntityId")
    @AuthorizeBss(action = BD06.BD06 + AuthModule.COMMA + BQ.BQ010818)
    public List<String>  productsByEntityId(){
        List<String>  products = new ArrayList<>();
        List<String> list = serviceCategoryService.findServiceTypeByEntityId(RequestContextUtil.getEntityId());
        if(CollectionUtil.isNotEmpty(list)){
            products =  list.stream().filter(s -> ProductCodeEnum.innerServiceProducts().contains(s)).collect(Collectors.toList());
        }

        return products;
    }

}
