/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.SHA512Util;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Policy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PolicyUser;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Project;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserOrg;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserRole;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateDistributorUserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateUsersRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UpdateSubuserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSimplePolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DistributorUserDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserDto;
import cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants;
import cn.com.cloudstar.rightcloud.bss.module.access.common.enums.PasswordRuleEnum;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PasswordUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.constance.HpcConstance;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysMUserDelLogMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.HPCService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyAssertionService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IProjectService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IUserGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IamSyncService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IamUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.LdapUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.PasswordPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.UserOrgService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.distributor.mapper.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeSysConfigRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeSysConfigResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.MailService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.SysMMsgReceiveContactMapper;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.param.SysMMsgReceiveContactParam;
import cn.com.cloudstar.rightcloud.bss.module.notice.mapper.SysMNotifyRecordMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.bss.module.tcc.service.CancelService;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.CharatorTypeEnum;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityUser;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMNotifyRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.SysMUserDelLog;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.LdapPropertyKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BssRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.enums.RequirePermissionEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ResHpcClusterVO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory;
import cn.com.cloudstar.rightcloud.oss.common.pojo.UserDTO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ValidationResult;
import cn.com.cloudstar.rightcloud.oss.common.util.MailUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ValidationUtils;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.OpenLdapUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamUserParent;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.UserSyncRemoteService;

import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.FROM_GROUP;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.FROM_USER;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.LINK_USER;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.USER_EXIST;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.USER_NOT_EXIST;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.UserStatus;
import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, User> implements SysUserService {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    public static final String MIN_LENGTH = "minLength";

    public static final String CHARACTER_LIMIT = "characterLimit";

    public static final String RULE_OUT = "RuleOut";

    public static final String CHARACTER_TYPE = "characterType";

    public static final String NEED_RESET = "1";

    public static final String NOT_RESET = "0";

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private UserMapper bssUserMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private EntityUserMapper entityUserMapper;


    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserOrgService userOrgService;

    @Autowired
    private IUserGroupService userGroupService;

    @Autowired
    private IamSyncService iamSyncService;

    @Autowired
    private IPolicyUserService policyUserService;

    @Autowired
    private IProjectService projectService;
    @DubboReference
    private IamRemoteService iamRemoteService;
    @Autowired
    private OrgService orgService;

    @Autowired
    private BizDistributorMapper bizDistributorMapper;

    @Autowired
    private MailService mailService;
    @Autowired
    HPCService hpcService;
    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;
    @Autowired
    private IPolicyService policyService;

    @Value("${integrate.skip:false}")
    private Boolean integrateSkip;

    @Autowired
    private CancelService cancelService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private IPolicyAssertionService policyAssertionService;
    @Autowired
    private FeignService feignService;
    @Autowired
    private LdapUserService ldapUserService;
    @Autowired
    private SysMMsgReceiveContactMapper sysMMsgReceiveContactMapper;
    @Autowired
    private SysMUserDelLogMapper sysMUserDelLogMapper;
    @Autowired
    private SysHpcPassService sysHpcPassService;

    @Autowired
    private SysMNotifyRecordMapper sysMNotifyRecordMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private IamUserService iamUserService;

    @DubboReference
    private UserSyncRemoteService userSyncRemoteService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public  List<User> createUsers(CreateUsersRequest request, Long userSid) {
        List<User> remoteUserList = new ArrayList<>();
        User user = this.selectByPrimaryKey(userSid);
        if (Objects.nonNull(request.getOrgId()) || StringUtils.isNotEmpty(request.getRefOrgId())) {
            if(!request.getOrgId().equals(10001L)){
                Org org = orgService.cmpSelectRootOrg(request.getOrgId(), request.getRefOrgId(), true);
                if (Objects.nonNull(org)) {
                    user.setOrgSid(org.getOrgSid());
                    Org requestOrg = orgService.cmpSelectRootOrg(request.getOrgId(), request.getRefOrgId(), false);
                    request.setOrgId(requestOrg.getOrgSid());
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ORG_NOT_EXIST));
                }
            }

        }
        if(Objects.nonNull(request.getCompanyId())){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(request.getCompanyId());
            if(Objects.isNull(bizBillingAccount)){
                throw new BizException("当前租户不存在");
            }
            request.setOrgId(bizBillingAccount.getOrgSid());
        }

        //子用户创建用户 挂到父用户下边
        if (user.getParentSid() != null) {
            user = this.selectByPrimaryKey(user.getParentSid());
        }
        if (Objects.isNull(user)) {
            throw new BizException(USER_NOT_EXIST);
        }
        //登录账号
        String loginAccount = user.getAccount();
        if(Objects.isNull(request.getOrgId()) && Objects.nonNull(user.getOrgSid())){
            request.setOrgId(user.getOrgSid());
        }
        // 获取hpc状态
        boolean isHpc = false;
        List<SfProductResource> hpcList = hpcService.getAllHpc(user.getOrgSid());
        if (null != hpcList && 0 < hpcList.size()) {
            SfProductResource sfProductResource = hpcList.get(0);
            if ("normal".equals(sfProductResource.getStatus())) {
                Integer hpcVersion = sfProductResource.getHpcVersion();
                if (!Integer.valueOf(3).equals(hpcVersion)) {
                    isHpc = true;
                }
            }
        }

        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSidSubUser(user.getOrgSid());
        Org org = orgService.selectRootOrg(user.getOrgSid());
        List<String> userSids = new ArrayList<>();
        final User finalUser = user;
        boolean finalIsHpc = isHpc;
        boolean anyMatch = request.getUsers().stream().anyMatch(userVO -> {
            List<User> byAccount = userMapper.findByAccount(userVO.getAccount());
            long count = byAccount.stream().filter(u1 -> !"4".equals(u1.getStatus()) && !UserStatus.DELETED.equals(u1.getStatus())).count();
            return count > 0;
        });
        if (anyMatch) {
            throw new BizException(USER_EXIST);
        }

        if (Objects.isNull(request.getCompanyId())) {
            Org currentOrg = orgMapper.selectById(request.getOrgId());
            Org rootOrg = orgService.selectRootNewOrg(currentOrg,true);
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByOrgIdAndEntity(rootOrg.getOrgSid());
            request.setCompanyId(bizBillingAccount.getId());
        }

        request.getUsers().forEach(u -> {
            User userDO = new User();

            //子用户和租户的冻结状态保持一致
            userDO.setFreezeStatus(finalUser.getFreezeStatus());
            userDO.setUnfreezeType(finalUser.getUnfreezeType());
            String account = u.getAccount();
            checkUserAccount(account);
            checkEmail(u.getEmail());
            checkMobile(u.getMobile());
            checkRetentionAccount(account);
            userDO.setSkip2FA(org.getSkip2FA() + "");
            userDO.setAccount(account);
            userDO.setRealName(account);

            // 进行加密存储
            userDO.setMobile(u.getMobile());
            userDO.setEmail(u.getEmail());
            List<User> byAccount = userMapper.findByAccount(userDO.getAccount());
            long count = byAccount.stream().filter(u1 -> !"4".equals(u1.getStatus()) && !UserStatus.DELETED.equals(u1.getStatus())).count();
            String ldapEnable = PropertiesUtil.getProperty(LdapPropertyKey.LDAP_ENABLE);
            if (!cn.com.cloudstar.rightcloud.oss.common.constants.Constants.ZERO.equals(ldapEnable)) {
                count = count + sysMUserDelLogMapper.countSelectByAccount(userDO.getAccount());
            }
            if (count > 0) {
                throw new BizException(USER_EXIST);
            }
            String password = request.getPassword();
            if (Strings.isNullOrEmpty(request.getPassword()) || PasswordRuleEnum.RANDOM_PASSWORD.equalsKey(
                    request.getPasswordRule())) {

                // 生成随机密码
                log.info("生成随机密码");
                password = PasswordUtil.generatRandomPassword(policy);

                // 密码使用hash加密存入数据库
                userDO.setPassword(CrytoUtilSimple.encodeHash(password));
            } else {
                if (PasswordUtil.validPasswordPolicy(policy, password)) {

                    // 密码使用hash加密存入数据库
                    userDO.setPassword(CrytoUtilSimple.encodeHash(password));
                } else {
                    throw new BizException(
                            cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                    cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
                }

            }

            // 密码使用hash加密存入数据库
            String hashPassword = CrytoUtilSimple.encodeHash(password);
            userDO.setPassword(hashPassword);
            if(CCSPCacheUtil.ccspServiceOpen()) {
                // 开启国密需要更新创建时间
                cn.hutool.json.JSONObject object = JSONUtil.parseObj(hashPassword);
                String ccspPass = CCSPCacheUtil.verifyAndCCSPEncrypt(password);
                object.set("secretData", ccspPass);
                userDO.setPassword(JSONUtil.toJsonStr(object));
            }

            WebUserUtil.prepareInsertParams(userDO);
            userDO.setUserType("03");
            userDO.setOrgSid(finalUser.getOrgSid());
            userDO.setCompanyId(finalUser.getCompanyId());
            userDO.setParentSid(finalUser.getUserSid());
            userDO.setForceresetpwd(request.isForceResetPwd() ? "true" : "false");
            userDO.setAuthType("local");

            // 判断当前用户是否已经开通了HPC服务
            log.info("判断当前用户是否已经开通了HPC服务");
            // 租户未企业认证，则将子用户置为待审核
            if (finalIsHpc && Objects.isNull(org.getCertificationStatus())) {
                // 创建的用户暂时设为处理中(待审核)
                userDO.setStatus(Constants.UserStatus.NOTAPPROVE);
            } else {
                userDO.setStatus(Constants.UserStatus.AVAILABILITY);
            }
            //租户子用户创建时，状态与租户状态一致
            userDO.setStatus(finalUser.getStatus());

            // 设置账号有效期
            if (policy.getAccountValidity()) {
                Date nowDate = new Date();
                userDO.setStartTime(nowDate);
                userDO.setEndTime(DateUtil.getDateByDay(nowDate, policy.getExpireTime()));
            }

            Calendar calendar = Calendar.getInstance();
            if (policy.getPwdExpireTimeValidity()) {
                calendar.add(Calendar.DATE, Math.toIntExact(policy.getPwdExpireTime()));
                userDO.setPwdEndTime(calendar.getTime());
            }

            // 云运营
            log.info("insertSelective：存入用户");

            // 设置密码有效期
            if (BooleanUtil.isTrue(policy.getPwdExpireTimeValidity())) {
                userDO.setPwdEndTime(DateUtil.getDateByDay(new Date(), Convert.toInt(policy.getPwdExpireTime(), 30)));
            }
            this.insertSelective(userDO);

            // 将密码存储密码历史表
            this.insertSelectivePasswordHistory(userDO.getUserSid(), userDO.getPassword());
            log.info("存入用户结束");

            userDO.setPassword(password);
            remoteUserList.add(userDO);
            UserOrg userOrg = new UserOrg();
            userOrg.setUserSid(userDO.getUserSid());
            userOrg.setOrgSid(finalUser.getOrgSid());
            iamRemoteService.insertUserOrg(userDO.getUserSid(), finalUser.getOrgSid());
            userOrgService.save(userOrg);

            // 加入到默认项目中
            log.info("加入到默认项目中");
            QueryWrapper<Project> projectQuery = new QueryWrapper<>();
            projectQuery.lambda().eq(Project::getParentId, finalUser.getOrgSid()).eq(Project::getOrgCode, "default");
            List<Project> defaultProjects = projectService.list(projectQuery);
            if (CollectionUtil.isNotEmpty(defaultProjects)) {
                UserOrg userProject = new UserOrg();
                userProject.setUserSid(userDO.getUserSid());
                userProject.setOrgSid(defaultProjects.get(0).getOrgSid());
                iamRemoteService.insertUserOrg(userDO.getUserSid(), defaultProjects.get(0).getOrgSid());
                userOrgService.save(userProject);
            } else {
                projectService.createDefaultProject(finalUser.getOrgSid(), userDO.getUserSid());
            }
            UserRole userRole = new UserRole();
            userRole.setOrgSid(finalUser.getOrgSid());
            userRole.setUserSid(userDO.getUserSid());
            userRole.setRoleSid(306L);
            userMapper.insertUserRole(userRole);

            // 生成一份和主用户一致的密码策略
            PasswordPolicy p = BeanConvertUtil.convert(policy, PasswordPolicy.class);
            p.setUserSid(userDO.getUserSid());
            passwordPolicyService.insertSelective(p);

            // 往iam插入父子用户关系表
            IamUserParent iamUserParent = new IamUserParent();
            iamUserParent.setUserSid(userDO.getUserSid());
            iamUserParent.setUserParentSid(userDO.getParentSid());
            iamRemoteService.insertUserParent(iamUserParent);

            // 插入用户组用户关联
            userGroupService.addUser(Lists.newArrayList(userDO.getUserSid()), request.getGroupIds(), finalUser.getOrgSid());

            //没开 hpc 才 创建 ldapuser
            if (!finalIsHpc) {
                try {
                    log.info("创建子用户,插入 ldap");
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(userDO.getAccount())
                            .setPassword(SHA512Util.LdapEncoderBySHA512(PasswordUtil.generatRandomPassword(policy)))
                            .setOrgNumber(org.getOrgSid()+"")
                            .setLdapOu(org.getLdapOu())
                            .setUid(userDO.getUserSid()+"")
                            .setHomePhone("disabled");
                    ldapUserService.createLdapUser(openLdapUser);
                } catch (Exception e) {
                    log.error("创建 ldap 用户失败[{}]",userDO.getAccount());
                    log.error("创建 ldap 异常：",e.getMessage());
                    userMapper.deleteByPrimaryKey(userDO.getUserSid());
                    throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_684305191),userDO.getAccount()));
                }
            }

            // 判断当前用户是否已经开通了HPC服务
            if (finalIsHpc && !integrateSkip) {

                // 发送邮件到管理员,通知其创建用户目录
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User paramUser = BeanConvertUtil.convert(userDO,
                        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User.class);
                try{
                mailService.sendMail(paramUser);
                }catch (Exception e){
                    log.error("邮件发送失败",e.getStackTrace());
                }

            }

            // 发送注册成功邮件。
            String systemName = "【" + PropertiesUtil.getProperty("system.name") + "】";
            boolean sendStatus = MailUtil.sendMail(new HashSet<>(Collections.singleton(userDO.getEmail())), null, null, systemName + "子用户密码",
                    StrUtil.format(systemName + "你账号的密码为：{}", password),
                    null);
            Date nowDate = new Date();
            SysMNotifyRecord  sysMNotifyRecord = new SysMNotifyRecord();

            sysMNotifyRecord.setContent("尊敬的"+loginAccount+"您好: 账号"+account+"已创建成功,密码已发送至您的邮箱，请登录查看。");
            sysMNotifyRecord.setSendCount(1);
            sysMNotifyRecord.setNotifyType(SysMNotifyConfigConstant.NotifyTypeEnum.MAIL.getValue());
            //接收人
            sysMNotifyRecord.setReceiver(loginAccount);
            //通知策略
            sysMNotifyRecord.setExpireStrategy("none");
            sysMNotifyRecord.setCreatedDt(nowDate);
            sysMNotifyRecord.setUpdatedDt(nowDate);
            sysMNotifyRecord.setVersion(1L);
            //如果发送成功记录通知记录
            if(sendStatus){
                //发送消息状态
                sysMNotifyRecord.setSmsSendDt(nowDate);
                sysMNotifyRecord.setSmsSendStatus("1");
                sysMNotifyRecord.setMailSendDt(nowDate);
                sysMNotifyRecord.setMailSendStatus("1");
                sysMNotifyRecordMapper.insert(sysMNotifyRecord);
            }else{
                //发送消息状态
                sysMNotifyRecord.setSmsSendDt(nowDate);
                sysMNotifyRecord.setSmsSendStatus("0");
                sysMNotifyRecord.setMailSendDt(nowDate);
                sysMNotifyRecord.setMailSendStatus("0");
                sysMNotifyRecordMapper.insert(sysMNotifyRecord);
            }
        });
        return remoteUserList;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<UserDTO> batchCreateUsers(CreateUsersRequest request, Long userSid) {
        List<UserDTO> remoteUserList = new ArrayList<>();
        User user = this.selectByPrimaryKey(userSid);
        if (Objects.isNull(user)) {
            throw new BizException(USER_NOT_EXIST);
        }

        //子用户创建用户 挂到父用户下边
        if (user.getParentSid() != null) {
            user = this.selectByPrimaryKey(user.getParentSid());
        }
        if (Objects.isNull(user)) {
            throw new BizException(USER_NOT_EXIST);
        }

        String loginAccount = user.getAccount();
        // 获取hpc状态
        boolean isHpc = false;
        List<SfProductResource> hpcList = hpcService.getAllHpc(user.getOrgSid());
        if (null != hpcList && 0 < hpcList.size()) {
            SfProductResource sfProductResource = hpcList.get(0);
            if ("normal".equals(sfProductResource.getStatus())) {
                Integer hpcVersion = sfProductResource.getHpcVersion();
                if (!Integer.valueOf(3).equals(hpcVersion)) {
                    isHpc = true;
                }
            }
        }
        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSid(user.getOrgSid());
        Org org = orgService.selectRootOrg(user.getOrgSid());
        List<String> userSids = new ArrayList<>();
        final User finalUser = user;
        boolean finalIsHpc = isHpc;
        request.getUsers().forEach(u -> {
            ValidationResult result = ValidationUtils.validateEntity(u);
            if (result.isHasErrors()) {
                BizException.e(result.getErrorMsg());
            }
            UserDTO userDO = new UserDTO();

            // 子用户和租户的冻结状态保持一致
            if(u.getIsPreOpen() == null){
                u.setIsPreOpen(false);
            }
            userDO.setFreezeStatus(finalUser.getFreezeStatus());
            userDO.setUnfreezeType(finalUser.getUnfreezeType());
            String account = u.getAccount();
            checkUserAccount(account);

            if (!u.getIsPreOpen()) {
            checkEmail(u.getEmail());
            checkMobile(u.getMobile());
            } else {
                if (finalUser.getBusinessTag() != null && !finalUser.getBusinessTag().contains(BusinessTagEnum.preOpen.getTag())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1254398416));
                }
                userDO.setBusinessTag(BusinessTagEnum.preOpen.getTag());
            }

            checkRetentionAccount(account);
            userDO.setSkip2FA(org.getSkip2FA() + "");
            userDO.setAccount(account);
            userDO.setRealName(account);
            userDO.setMobile(u.getMobile());
            userDO.setIsPreOpen(u.getIsPreOpen());
            if (ObjectUtils.isNotEmpty(u.getMobile())) {
                userDO.setMobile(u.getMobile());
            }
            if (ObjectUtils.isNotEmpty(u.getEmail())) {
                userDO.setEmail(u.getEmail());
            }
            if(!u.getIsPreOpen()){

                List<User> byAccount = userMapper.findByAccount(userDO.getAccount());
           //     long count = byAccount.stream().filter(u1 -> !"4".equals(u1.getStatus()) && !UserStatus.DELETED.equals(u1.getStatus())).count();
                if(byAccount.size()>0){
                    throw new BizException(USER_EXIST);
                }
            } else {
                int b = userMapper.selectCount(new QueryWrapper<User>().eq("account", u.getAccount()));
                if (b > 0) {
                    throw new BizException(USER_EXIST);
                }
            }

            if (ObjectUtils.isNotEmpty(request.getPassword())) {
                String error = this.validPasswordPolicyToError(policy, request.getPassword(), null, true);
                if (!ObjectUtils.isEmpty(error)) {
                    throw new BizException(error);
                }
            }
            int b = userMapper.userDataExist(BeanConvertUtil.convert(userDO, User.class));
            if (b > 0) {
                throw new BizException(USER_EXIST);
            }
            String password = request.getPassword();
            if (Strings.isNullOrEmpty(request.getPassword()) || PasswordRuleEnum.RANDOM_PASSWORD.equalsKey(
                    request.getPasswordRule())) {

                // 生成随机密码
                password = PasswordUtil.generatRandomPassword(policy);
                userDO.setPassword(CrytoUtilSimple.encodeHash(password));
            } else {

                // 如果传进来的是密文那么就解密否则就是原文
                String decrypt = CrytoUtilSimple.decrypt(password);
                password = !password.equals(decrypt) ? decrypt : password;
                if (PasswordUtil.validPasswordPolicy(policy, password)) {
                    userDO.setPassword(CrytoUtilSimple.encodeHash(password));
                } else {
                    throw new BizException(
                            cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                    cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }

            // 密码使用hash加密存入数据库
            String hashPassword = CrytoUtilSimple.encodeHash(password);
            userDO.setPassword(hashPassword);
            if(CCSPCacheUtil.ccspServiceOpen()) {
                // 开启国密需要更新创建时间
                cn.hutool.json.JSONObject object = JSONUtil.parseObj(hashPassword);
                String ccspPass = CCSPCacheUtil.verifyAndCCSPEncrypt(password);
                object.set("secretData", ccspPass);
                userDO.setPassword(JSONUtil.toJsonStr(object));
            }

            WebUserUtil.prepareInsertParams(userDO);
            userDO.setUserType("03");
            userDO.setOrgSid(finalUser.getOrgSid());
            userDO.setCompanyId(finalUser.getCompanyId());
            userDO.setParentSid(finalUser.getUserSid());
            userDO.setForceresetpwd(request.isForceResetPwd());
            userDO.setAuthType("local");

            // 判断当前用户是否已经开通了HPC服务
            if (finalIsHpc) {

                // 创建的用户暂时设为处理中(待审核)
                userDO.setStatus(Constants.UserStatus.NOTAPPROVE);
            } else {
                userDO.setStatus(Constants.UserStatus.AVAILABILITY);
            }
            //租户子用户创建时，状态与租户状态一致
            userDO.setStatus(finalUser.getStatus());
            // 设置账号有效期
            if (policy.getAccountValidity()) {
                Date nowDate = new Date();
                userDO.setStartTime(nowDate);
                userDO.setEndTime(DateUtil.getDateByDay(nowDate, policy.getExpireTime()));
            }
            // 创建子用户, 密码有效期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, Math.toIntExact(policy.getPwdExpireTime()));
            userDO.setPwdEndTime(calendar.getTime());
            User userTemp = BeanConvertUtil.convert(userDO, User.class);

            // 1.主账号为预开通，子账号是预开通
            // 2.主账号不是预开通，子账号跟随主账号
            // 3.主账号不是预开通，子账号是预开通， 子账号是预开通
            // add_xiongwei ********
            String parentBusinessTag = finalUser.getBusinessTag();
            userTemp.setBusinessTag(parentBusinessTag);
            if (Objects.nonNull(userDO.getIsPreOpen()) &&  userDO.getIsPreOpen()) {
                userTemp.setBusinessTag(BusinessTagEnum.preOpen.getTag());
            }

            // 设置密码有效期
            if (policy.getPwdExpireTimeValidity()) {
                userTemp.setPwdEndTime(DateUtil.getDateByDay(new Date(), Convert.toInt(policy.getPwdExpireTime())));
            }

            // 云运营
            this.insertSelective(userTemp);
            this.insertSelectivePasswordHistory(userTemp.getUserSid(), userTemp.getPassword());

            // insert成功后把useridset到userdo
            userDO.setUserSid(userTemp.getUserSid());
            UserOrg userOrg = new UserOrg();
            userOrg.setUserSid(userDO.getUserSid());
            userOrg.setOrgSid(finalUser.getOrgSid());
            iamRemoteService.insertUserOrg(userDO.getUserSid(), finalUser.getOrgSid());
            userOrgService.save(userOrg);

            // 加入到默认项目中
            QueryWrapper<Project> projectQuery = new QueryWrapper<>();
            projectQuery.lambda().eq(Project::getParentId, finalUser.getOrgSid()).eq(Project::getOrgCode, "default");
            List<Project> defaultProjects = projectService.list(projectQuery);
            if (CollectionUtil.isNotEmpty(defaultProjects)) {
                UserOrg userProject = new UserOrg();
                userProject.setUserSid(userDO.getUserSid());
                userProject.setOrgSid(defaultProjects.get(0).getOrgSid());
                iamRemoteService.insertUserOrg(userDO.getUserSid(), defaultProjects.get(0).getOrgSid());
                userOrgService.save(userProject);
            } else {
                projectService.createDefaultProject(finalUser.getOrgSid(), userDO.getUserSid());
            }
            UserRole userRole = new UserRole();
            userRole.setOrgSid(finalUser.getOrgSid());
            userRole.setUserSid(userDO.getUserSid());
            userRole.setRoleSid(306L);
            userMapper.insertUserRole(userRole);

            // 生成一份和主用户一致的密码策略
            PasswordPolicy p = BeanConvertUtil.convert(policy, PasswordPolicy.class);
            p.setUserSid(userDO.getUserSid());
            passwordPolicyService.insertSelective(p);

            // 往iam插入父子用户关系表
            IamUserParent iamUserParent = new IamUserParent();
            iamUserParent.setUserSid(userDO.getUserSid());
            iamUserParent.setUserParentSid(userDO.getParentSid());
            iamRemoteService.insertUserParent(iamUserParent);

            // 插入用户组用户关联
//            userGroupService.addUser(Lists.newArrayList(userDO.getUserSid()), request.getGroupIds(), finalUser.getOrgSid());

            // 没开hpc才创建ldapuser
            if (!finalIsHpc) {
                try {
                    log.info("创建子用户,插入 ldap");
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(userDO.getAccount())
                            .setPassword(SHA512Util.LdapEncoderBySHA512(PasswordUtil.generatRandomPassword(policy)))
                            .setOrgNumber(org.getOrgSid() + "")
                            .setLdapOu(org.getLdapOu())
                            .setUid(userDO.getUserSid() + "")
                            .setHomePhone("disabled");
                    userDO.setPassword(password);
                    ldapUserService.createLdapUser(openLdapUser);
                    remoteUserList.add(BeanConvertUtil.convert(userDO,UserDTO.class));
                    // 判断子用户有没有进行企业认证，如果进行了企业认证需要同步资源到ladp
                    if ("authSucceed".equals(org.getCertificationStatus())) {
                        log.info("企业认证-同步资源到ldap:{}", org.getOrgSid());
                    }
                } catch (Exception e) {
                    log.error("创建 ldap 用户失败[{}]", userDO.getAccount());
                    log.error("创建 ldap 异常：", e.getMessage());
                    userDO.setLdapFalg(Boolean.FALSE);
                    userDO.setFailInfo("创建 ldap 用户失败[{" + userDO.getAccount() + "}]");
                    remoteUserList.add(BeanConvertUtil.convert(userDO, UserDTO.class));
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return;
                }
            } else {
                userDO.setPassword(password);
                remoteUserList.add(BeanConvertUtil.convert(userDO,UserDTO.class));
            }

            // 判断当前用户是否已经开通了HPC服务
            if (finalIsHpc && !integrateSkip) {

                // 发送邮件到管理员,通知其创建用户目录
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User paramUser = BeanConvertUtil.convert(userDO,
                        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User.class);
                try{
                mailService.sendMail(paramUser);
                }catch(Exception e){
                    log.error("邮件发送失败:",e.getStackTrace());
                }

            }

            String system = PropertiesUtil.getProperty("system.name");
            Map<String, String> messageContent = new HashMap(8);
            messageContent.put("systemName", system);
            messageContent.put("password", password);
            MailNotificationMq mailNotificationMq = new MailNotificationMq();
            mailNotificationMq.setMsgId(NotificationConsts.ConsoleMsg.AccountMsg.TENANT_SUBUSER_REGISTER_SUCCESS);
            mailNotificationMq.getMails().add(userDO.getEmail());
            mailNotificationMq.setMap(messageContent);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, mailNotificationMq);
        });
        return remoteUserList;
    }

    @Override
    public void createSubUser(CreateUsersRequest request) {
        cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = createUsers(request,authUser.getUserSid() );
        Org org = orgService.selectRootOrg(authUser.getOrgSid());
        List<String> failUserNames = Lists.newArrayList();
        userList.forEach(user -> {
            try {
                log.info("创建子用户-createSubUser-远程添加:[{}]", user.getUserSid());
                //远程添加
                iamRemoteService.insertUser(
                        BeanConvertUtil.convert(user, IamUser.class));
                log.info("创建子用户-createSubUser-远程添加完成:[{}]", user.getUserSid());
            } catch (Exception e) {
                log.error("创建子用户远程添加失败:[{}]、[{}]", user.getUserSid(), e.getMessage());
                try {
                    iamRemoteService.deleteUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("创建子用户出错 iam 用户回滚失败",exception);
                }
                try {
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(user.getAccount())
                            .setPassword(request.getPassword())
                            .setLdapOu(org.getLdapOu())
                            .setUid(user.getUserSid() + "")
                            .setOrgNumber(org.getOrgSid() + "");
                    ldapUserService.deleteLdapUser(openLdapUser);
                } catch (Exception exception) {
                    log.error("创建子用户出错 ldap 用户回滚失败",exception);
                }
                try {
                    cancelService.cancelUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("创建子用户出错 删除本地用户回滚失败",exception);
                }
                failUserNames.add(user.getAccount());
            }
        });
        if (CollectionUtil.isNotEmpty(failUserNames)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1315590976) + failUserNames.toString() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_23127917));
        }
    }

    @Override
    public List<UserDTO> createSubUsers(CreateUsersRequest request) {
//       已注释 log.info("--------------批量创建子用户:{}", JSON.toJSON(request));
        List<UserDTO> userDTOS = batchCreateUsers(request, request.getParentUserSid());
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = userDTOS.stream().filter(e -> e.getLdapFalg()).map(e -> BeanConvertUtil.convert(e, cn.com.cloudstar.rightcloud.bss.module.access.bean.User.class)).collect(Collectors.toList());
        Org org = orgService.selectRootOrg(request.getOrgSid());
        List<String> failUserNames = Lists.newArrayList();
        userList.forEach(user -> {
            try {
                if(user.getIsPreOpen()){
                    user.setEmail(null);
                }
                log.info("创建子用户-createSubUsers-远程添加:[{}]", user.getUserSid());
                //远程添加
                iamRemoteService.insertUser(
                        BeanConvertUtil.convert(user, IamUser.class));
                log.info("创建子用户-createSubUsers-远程添加完成:[{}]", user.getUserSid());
            } catch (Exception e) {
                log.error("创建子用户远程添加失败:[{}]、[{}]", user.getUserSid(), e.getMessage());
                try {
                    iamRemoteService.deleteUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("创建子用户出错 iam 用户回滚失败",exception);
                }
                try {
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(user.getAccount())
                            .setPassword(request.getPassword())
                            .setLdapOu(org.getLdapOu())
                            .setUid(user.getUserSid() + "")
                            .setOrgNumber(org.getOrgSid() + "");
                    ldapUserService.deleteLdapUser(openLdapUser);
                } catch (Exception exception) {
                    log.error("创建子用户出错 ldap 用户回滚失败",exception);
                }
                try {
                    cancelService.cancelUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("创建子用户出错 删除本地用户回滚失败",exception);
                }
                failUserNames.add(user.getAccount());
            }
        });
        if (CollectionUtil.isNotEmpty(failUserNames)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1315590976) + failUserNames.toString() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_23127917));
        }
        return userDTOS;
    }

    private void checkEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_684266669));
        }
        if(email.length()>80){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_671607287));
        }
        Validator.validateEmail(email,"邮箱格式不正确");

        String emailNew = CCSPCacheUtil.verifyAndCCSPEncrypt(email);
        LambdaQueryWrapper<User> tWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.equals(emailNew, email)) {
            tWrapper.eq(User::getEmailHash, DigestUtils.sha256Hex(email));
        } else {
            tWrapper.eq(User::getEmail, emailNew);
        }
        tWrapper.ne(User::getStatus,8);
        tWrapper.ne(User::getStatus,4);
        List<User> userList = userMapper.selectList(tWrapper);
        if (!CollectionUtils.isEmpty(userList)) {
            throw new BizException (cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_MSG_00013));
        }
    }

    @Override
    public IPage<User> getProjectUsers(IPage<User> page, Criteria criteria) {
        return this.userMapper.selectByProjectUsers(page, criteria.getCondition());
    }

    @Override
    public int countByParams(Criteria example) {
        int count = this.userMapper.countByParams(example);
        return count;
    }

    @Override
    public User selectByPrimaryKey(Long userSid) {
        return this.userMapper.selectByPrimaryKey(userSid);
    }

    @Override
    public int countUser(Criteria example) {
        return userMapper.countUser(example);
    }

    @Override
    public List<User> selectByParams(Criteria example) {
        return this.userMapper.selectByParams(example);
    }

    @Override
    public List<User> selectByParam(Criteria example) {
        return this.userMapper.selectByParam(example);
    }

    @Override
    public int deleteByPrimaryKey(Long userSid) {
        userSyncRemoteService.deleteUser(userSid);
        return this.userMapper.deleteByPrimaryKey(userSid);
    }

    @Override
    public int updateByPrimaryKeySelective(User record) {
        userSyncRemoteService.updateUser(BeanUtil.copyProperties(record, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        return this.userMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        userMapper.selectByParam(example).forEach(user -> {
           userSyncRemoteService.deleteUser(user.getUserSid());
        });
        return this.userMapper.deleteByParams(example);
    }




    @Override
    public int insert(User record) {
        int insert = this.userMapper.insert(record);
        userSyncRemoteService.createUser(BeanUtil.copyProperties(record, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        return insert;
    }

    @Override
    public int insertSelective(User record) {
        int insert = this.userMapper.insertSelective(record);
        userSyncRemoteService.createUser(BeanUtil.copyProperties(record, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        return insert;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateSubuser(UpdateSubuserRequest request, Long userSid) {
        User user = BeanConvertUtil.convert(request, User.class);
        if (!StringUtil.isNullOrEmpty(user.getEmail())) {
            user.setEmail(user.getEmail());
        }
        if (!StringUtil.isNullOrEmpty(user.getMobile())) {
            user.setMobile(user.getMobile());
        }
        user.setUserSid(userSid);
        this.updateByPrimaryKeySelective(user);
        userSyncRemoteService.updateUser(BeanUtil.copyProperties(user, cn.com.cloudstar.rightcloud.common.pojo.User.class));
        // iam同步修改
        try {
            iamRemoteService.updateUser(cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil.convert(user, IamUser.class));
        } catch (Exception e) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1135008995));
        }
    }

    @Override
    public List<DescribeSimplePolicy> listPolicyForUser(Long userSid) {
        List<DescribeSimplePolicy> returnData = Lists.newArrayList();
        Set<String> code = new HashSet<>();
        // 查询用户自身权限
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        List<Policy> policies = policyMapper.selectByUserSid(userSid);
        if (CollectionUtil.isNotEmpty(policies)) {
            policies.forEach(p -> {
                DescribeSimplePolicy describeSimplePolicy = new DescribeSimplePolicy();
                describeSimplePolicy.setPolicySid(p.getPolicySid());
                describeSimplePolicy.setOrigin(isUs ? "User group association" : LINK_USER);
                describeSimplePolicy.setPolicyName(p.getPolicyName());
                List<String> resourceTypes = ProductComponentEnum.transformDesc(p.getResourceTypes().split(","));
                if (isUs) {
                    resourceTypes = ProductComponentEnum.ProductDescUsEnum.transformDescUs(resourceTypes);
                }
                describeSimplePolicy.setResourceTypes(resourceTypes);
                describeSimplePolicy.setOriginType(FROM_USER);
                describeSimplePolicy.setAuthorizedTime(p.getAuthorizedTime());
                String desc = p.getDescription();
                String descUs = p.getDescriptionUs();
                if (isUs && StringUtils.isNotBlank(descUs)) {
                    desc = descUs;
                }
                describeSimplePolicy.setDescription(desc);
                returnData.add(describeSimplePolicy);
                code.add(p.getPolicyName());
            });
        }

        List<Policy> policiesFromGroup = policyMapper.selectByGroup(userSid);
        if (CollectionUtil.isNotEmpty(policiesFromGroup)) {
            policiesFromGroup.forEach(p -> {
                if (code.contains(p.getPolicyName())) {
                    return;
                }
                DescribeSimplePolicy describeSimplePolicy = new DescribeSimplePolicy();
                describeSimplePolicy.setPolicySid(p.getPolicySid());
                describeSimplePolicy.setOrigin(isUs ? "User group association" : LINK_USER);
                describeSimplePolicy.setPolicyName(p.getPolicyName());
                List<String> resourceTypes = ProductComponentEnum.transformDesc(p.getResourceTypes().split(","));
                if (isUs) {
                    resourceTypes = ProductComponentEnum.ProductDescUsEnum.transformDescUs(resourceTypes);
                }
                describeSimplePolicy.setResourceTypes(resourceTypes);
                describeSimplePolicy.setOriginType(FROM_GROUP);
                describeSimplePolicy.setAuthorizedTime(p.getAuthorizedTime());
                String desc = p.getDescription();
                String descUs = p.getDescriptionUs();
                if (isUs && StringUtils.isNotBlank(descUs)) {
                    desc = descUs;
                }
                describeSimplePolicy.setDescription(desc);
                returnData.add(describeSimplePolicy);
            });
        }
        policiesFromGroup.addAll(policies);
        List<Policy> queryPolicyList = CollectionUtils.isEmpty(policiesFromGroup) ? Collections.EMPTY_LIST :
                policiesFromGroup.stream().distinct().collect(Collectors.toList());
        List<ResHpcClusterVO> resHpcClusterList = policyAssertionService.queryByHpcResource(queryPolicyList);
        if (CollectionUtils.isNotEmpty(resHpcClusterList)) {

            resHpcClusterList = resHpcClusterList.stream().filter(cluster -> !ResHpcClusterStatus.DELETED.equalsIgnoreCase(cluster.getStatus())
                    && !ResHpcClusterStatus.UNSUBSCRIBED.equalsIgnoreCase(cluster.getStatus())
                    && !ResHpcClusterStatus.REJECTED.equalsIgnoreCase(cluster.getStatus())).collect(Collectors.toList());

            Map<Long, List<ResHpcClusterVO>> relationMap =
                    resHpcClusterList.stream().collect(Collectors.groupingBy(ResHpcClusterVO::getPolicyId));
            relationMap.forEach((k, v) -> returnData.stream().filter(
                    r -> r.getPolicySid().equals(k)).forEach(re -> {
                re.setHpcClusterList(v);
                if (CollectionUtil.isNotEmpty(v)) {
                    re.setHpcClusterName(v.stream().map(ResHpcClusterVO::getName).collect(Collectors.joining(",")));
                }
            }));
        }
        return returnData;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void deleteSubuser(Long userSid) {
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = this.selectByPrimaryKey(userSid);
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1473130476));
        }
        Org rootOrg = orgService.selectRootOrg(user.getOrgSid());
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        // 开通HPC检查
        if ("1".equals(configMapper.selectConfigValue("ldap.enable"))) {
        //不是待审批状态且有HPC操作权限的，才检查hpc作业
        List<Long> hpcUsers = policyService.findHpcUsers(Arrays.asList(userSid));
        if (!Constants.UserStatus.NOTAPPROVE.equals(user.getStatus()) && !CollectionUtils.isEmpty(hpcUsers)
                    && ("authSucceed".equals(user.getCertificationStatus()) || "authSucceed".equals(
                    rootOrg.getCertificationStatus()))) {
            QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
            queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_STATUS);
            queryResHpcClusterRequest.setStatus(ResHpcClusterStatus.AVALIABLE);
            queryResHpcClusterRequest.setOrgSid(authUserInfo.getOrgSid());
            List<ResHpcClusterVO> hpcList = policyAssertionService.findResourceRelationByUser(
                    HpcConstance.TYPE_HPC, user.getUserSid(), authUserInfo.getOrgSid(), Function.identity(),
                    queryResHpcClusterRequest);
                String password = sysHpcPassService.findPassword(user.getUserSid());
                if (StringUtils.isNotBlank(password)) {
                    ldapUserService.checkHPCJob(hpcList, user.getAccount(), authUserInfo.getUserSid());
                }
        }
        }
        // ldap删除
        Org org = null;
        try {
            org = orgService.getById(user.getOrgSid());
            OpenLdapUser openLdapUser = new OpenLdapUser();
            openLdapUser.setUserName(user.getAccount())
                    .setLdapOu(org.getLdapOu())
                    .setUid(user.getUserSid()+"");
            ldapUserService.deleteLdapUser(openLdapUser);

        }catch (Exception e){
            log.error("删除 ldap 用户失败[{}]",user.getAccount());
            log.error("删除 ldap 用户异常：",e.getMessage());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1273081760));
        }

        // 华为IAM子用户删除
        if (iamUserService.checkCreateIamSubUser()) {
            boolean flag = iamUserService.deleteSubUser(null, user.getAccount(), userSid);
            if (!flag) {
                cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                criteria.put("refUserId", user.getParentSid());
                List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria);

                HashMap<String, String> messageContent = new HashMap<>(5);
                messageContent.put("accountName", CollectionUtil.isNotEmpty(hcsoUsers) ? hcsoUsers.get(0).getAccountName() : "");
                messageContent.put("iamUser", user.getAccount());

                List<cn.com.cloudstar.rightcloud.bss.common.pojo.User> adminstrators = bssUserMapper.findAdminstratorsByEntityId(1L);
                if(adminstrators !=null){
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().map(cn.com.cloudstar.rightcloud.bss.common.pojo.User::getUserSid).collect(Collectors.toSet()));

                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_DELETE_IAM_USER_FAIL);
                    baseNotificationMqBean.setEntityId(1L);
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
                }
            }
        }

        // 用户与用户组的删除
        QueryWrapper<UserGroup> qw = new QueryWrapper<>();
        qw.eq("user_sid", userSid);
        userGroupService.remove(qw);

        // 用户权限删除
        QueryWrapper<PolicyUser> qw2 = new QueryWrapper<>();
        qw2.eq("user_sid", userSid);
        policyUserService.remove(qw2);

        // 用户组织删除
        QueryWrapper<UserOrg> qw3 = new QueryWrapper<>();
        qw3.eq("user_sid", userSid);
        iamRemoteService.deleteUserOrgByUser(Collections.singletonList(userSid));
        userOrgService.remove(qw3);

        // 底层删除
        iamRemoteService.deleteUser(userSid);

        //逻辑删除
        user.setStatus(UserStatus.DELETED);
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<User>().set(User::getMobile, null)
                                                                                    .set(User::getEmail, null)
                                                                                    .set(User::getPassword, null)
                                                                                    .eq(User::getUserSid, userSid);
        this.userMapper.update(user, updateWrapper);
        userSyncRemoteService.deleteUser(userSid);

        SysMUserDelLog sysMUserDelLog = new SysMUserDelLog();
        sysMUserDelLog.setAccount(user.getAccount());
        sysMUserDelLog.setCreateDt(new Date());
        sysMUserDelLog.setCreateBy(RequestContextUtil.getCurrentUserName());
        sysMUserDelLogMapper.insert(sysMUserDelLog);

        SysMMsgReceiveContactParam sysMMsgReceiveContactParam = new SysMMsgReceiveContactParam();
        sysMMsgReceiveContactParam.setUserSid(user.getUserSid());
        sysMMsgReceiveContactMapper.deleteByParam(sysMMsgReceiveContactParam);
        LdapSyncRequest request=new LdapSyncRequest();
        request.setOrgId(RequestContextUtil.getAuthUserInfo().getOrgSid());
        request.setIsReturnException(Boolean.TRUE);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean unlockUsers(List<Long> userIdList) {
        //判断是否开启解锁配置
        cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_135037097));
        }
        List<PasswordPolicy> policy = passwordPolicyService.selectByParams(
                new Criteria("orgSid", authUser.getOrgSid()));

        if (CollectionUtil.isEmpty(policy) || !policy.get(0).getLoginfailureEnable()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1990741850));
        }
        int result = 0;
        for (Long userId : userIdList) {
            User user = userMapper.selectByPrimaryKey(userId);
            if (user != null) {

                user.setStatus(Constants.UserStatus.AVAILABILITY);
                user.setErrorCount(0);
                result += userMapper.updateByPrimaryKeySelective(user);
            }
        }

        if (result > 0 && result == userIdList.size()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean assertIamUser(Long userSid) {
        return this.userMapper.assertIamUser(userSid);
    }

    @Override
    public boolean checkUserHasSomeAccess(Long userSid, List<String> policies) {
        //是否拥有租户权限
        List<DescribeSimplePolicy> userPolicies = this.listPolicyForUser(userSid);
        Set<String> policySet = userPolicies.stream()
                                            .map(DescribeSimplePolicy::getPolicyName)
                                            .collect(Collectors.toSet());
        return CollectionUtil.containsAny(policySet, policies);
    }

    /**
     * 创建分销商账户
     * @param request
     */
    @Override
    public Long createDistributorUser(CreateDistributorUserRequest request) {
        cn.com.cloudstar.rightcloud.bss.common.pojo.User user = AuthUtil.getAuthUser();
        if (user == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        BizDistributor bizDistributor = bizDistributorMapper.selectById(request.getDistributorId());
        if (ObjectUtils.isEmpty(bizDistributor)) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_1));
        }
        List<Long> roles = userRoleMapper.selectByParams(new Criteria().put("userSid", user.getUserSid())).stream().map(
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole::getRoleSid).collect(Collectors.toList());
        if (roles.stream().anyMatch(r -> r == 301L)) {
            if (Objects.nonNull(user.getEntityId()) && !user.getEntityId().equals(bizDistributor.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1152410115));
            }
        }
        User userDO = new User();
        // 如果是系统控制台创建分销商用户，则没有account
        String account = request.getAccount();
        checkUserAccount(account);
        checkEmail(request.getEmail());
        checkMobile(request.getMobile());
        // 验证分销商账号id
        if (Objects.isNull(bizDistributorMapper.selectById(request.getDistributorId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_662451026));
        }


            // 如果是运营控制台创建分销商用户，则有account
            userDO.setAccount(account);
        userDO.setRealName(request.getRealName());
        Criteria criteria = new Criteria("account", userDO.getAccount());
        int b = userMapper.countByParams(criteria);
        if (b > 0) {
            throw new BizException(USER_EXIST);
        }
        String password = request.getPassword();
        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSid(user.getOrgSid());
        String error = this.validPasswordPolicyToError(policy, password, null, true);
        if (!ObjectUtils.isEmpty(error)) {
            throw new BizException(error);
        }
        userDO.setPassword(CrytoUtilSimple.encodeHash(password));
        WebUserUtil.prepareInsertParams(userDO);
        userDO.setUserType(UserType.DISTRIBUTOR_USER);
        userDO.setOrgSid(request.getDistributorId());
        userDO.setCompanyId(request.getDistributorId());
        userDO.setAuthType("local");
        if (!ObjectUtils.isEmpty(request.getEmail())) {
            userDO.setEmail(request.getEmail());
        }
        if (!ObjectUtils.isEmpty(request.getMobile())) {
            userDO.setMobile(request.getMobile());
        }
        if(NEED_RESET.equals(request.getForceResetPwd())){
            userDO.setForceresetpwd("true");
        }else if(NOT_RESET.equals(request.getForceResetPwd())){
            userDO.setForceresetpwd("false");
        }
        if (SysRoleEnum.DISTRIBUTOR_ADMIN.getRoleSid().equals(request.getRoleId())) {
            userDO.setCertificationStatus(CertificationStatus.AUTHSUCCEED);
        }
        this.insertSelective(userDO);
        UserOrg userOrg = new UserOrg();
        userOrg.setUserSid(userDO.getUserSid());
        userOrg.setOrgSid(userDO.getOrgSid());
        iamRemoteService.insertUserOrg(userDO.getUserSid(), userDO.getOrgSid());
        userOrgService.save(userOrg);

        UserRole userRole = new UserRole();
        userRole.setOrgSid(userDO.getOrgSid());
        userRole.setUserSid(userDO.getUserSid());
        userRole.setRoleSid(request.getRoleId());
        userMapper.insertUserRole(userRole);
        ////创建运营实体跟用户的关系表
        if(request.getRoleId() != null){
            Role role = roleMapper.selectByPrimaryKey(request.getRoleId());
            if(role != null && RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope().equals(role.getDataScope()) ){
                if (StringUtils.isNotBlank(request.getEntityIds())) {
                    String entityIds = request.getEntityIds();
                    String[] entityIdList = entityIds.split(",");
                    Arrays.stream(entityIdList).forEach(entityId -> {
        EntityUser entityUser = new EntityUser();
                        entityUser.setUserSid(userDO.getUserSid());
                        entityUser.setOrgSid(userDO.getOrgSid());
                        entityUser.setBssEntityId(Long.valueOf(entityId));
                        entityUserMapper.insertEntityUser(entityUser);

                    });
        } else {
                    HttpServletRequest req = RequestContextUtil.getRequest();
                    String entityIds = req.getHeader("ENTITY_id");
                    log.info("header request获取到的实体id:{}", entityIds);
                    if (entityIds != null) {
                        String[] entityIdList = entityIds.split(",");
                        Arrays.stream(entityIdList).forEach(entityId -> {
                            EntityUser entityUser = new EntityUser();
                            entityUser.setUserSid(userDO.getUserSid());
                            entityUser.setOrgSid(userDO.getOrgSid());
                            entityUser.setBssEntityId(Long.valueOf(entityId));
                            entityUserMapper.insertEntityUser(entityUser);

                        });
                    }
                }
            }else{
                if (StringUtils.isNotBlank(request.getEntityIds())) {
                    String entityIds = request.getEntityIds();
                    String[] entityIdList = entityIds.split(",");
                    Arrays.stream(entityIdList).forEach(entityId -> {
                        EntityUser entityUser = new EntityUser();
                        entityUser.setUserSid(userDO.getUserSid());
                        entityUser.setOrgSid(userDO.getOrgSid());
                        entityUser.setBssEntityId(Long.valueOf(entityId));
                        entityUserMapper.insertEntityUser(entityUser);

                    });
                }else if (request.getDistributorId() != null) {
                    bizDistributor = bizDistributorMapper.findDistributorsById(request.getDistributorId());
                    if (bizDistributor != null && bizDistributor.getEntityId() != null) {
                        String entityIds = bizDistributor.getEntityId().toString();
                        String[] entityIdList = entityIds.split(",");
                        Arrays.stream(entityIdList).forEach(entityId -> {
                            EntityUser entityUser = new EntityUser();
        entityUser.setUserSid(userDO.getUserSid());
                            entityUser.setOrgSid(userDO.getOrgSid());
                            entityUser.setBssEntityId(Long.valueOf(entityId));
        entityUserMapper.insertEntityUser(entityUser);
                            request.setEntityIds(entityIds);

                        });
                    }
                }
            }
        }

        try {
            //远程添加
            userDO.setPassword(password);

            userSyncRemoteService.createUser(BeanUtil.copyProperties(userDO, cn.com.cloudstar.rightcloud.common.pojo.User.class));

            iamRemoteService.insertUser(
                    BeanConvertUtil.convert(userDO, IamUser.class));
        }catch (Exception e){
            cancelService.cancelUser(userDO.getUserSid());
            throw new BizException(e.getMessage());
        }

        return userDO.getUserSid();
    }

    private void checkMobile(String mobile) {
        if (StringUtils.isNotEmpty(mobile)) {
            String mobileNew = CCSPCacheUtil.verifyAndCCSPEncrypt(mobile);
            LambdaQueryWrapper<User> tWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.equals(mobileNew, mobile)) {
                tWrapper.eq(User::getMobileHash, DigestUtils.sha256Hex(mobile));
            } else {
                tWrapper.eq(User::getMobile, mobileNew);
            }
            tWrapper.ne(User::getStatus,"8");
            tWrapper.ne(User::getStatus,"4");
            Integer count = userMapper.selectCount(tWrapper);
            if(count>0){
                throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_MSG_00012));
            }
        }

    }

    /**
     * 检查是否是平台保留账号
     * @param account 账号
     */
    private void checkRetentionAccount(String account) {
        DescribeSysConfigRequest req = new DescribeSysConfigRequest();
        req.setConfigType("platform_account_and_ou");
        RestResult restResult = feignService.displayAllMessageByFeign(req);
        String jsonString = JSON.toJSONString(restResult.getData());
        List<DescribeSysConfigResponse> responses = JSON.parseArray(jsonString, DescribeSysConfigResponse.class);
        StringBuilder retentionInfoSb = new StringBuilder();
        if (responses != null && 0 < responses.size()) {
            responses.forEach(sysconfig -> retentionInfoSb.append(sysconfig.getConfigValue()));
        } else {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.PLATFORM_RETENTION_CONFIGURATION_ABNORMAL));
        }
        String retentionInfoStr = retentionInfoSb.toString();
        if (StringUtils.isBlank(retentionInfoStr)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.PLATFORM_RETENTION_CONFIGURATION_ABNORMAL));
        }
        List<String> retentionInfo = Arrays.asList(retentionInfoStr.split(";"));
        if (retentionInfo.contains(account)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED));
        }
    }

    private void checkPassword(String password){
        //判断密码是不是在8-12位之间，并含有字母、数字和符号
        if (password.length() < 8 || password.length() > 12) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_222974645));
        } else {
            // 含有字母
            String regex1 = ".*[a-zA-z].*";
            boolean result3 = password.matches(regex1);
            if (!result3) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_222974645));
            }
            //【含有数字】true
            String regex2 = ".*[0-9].*";
            boolean result4 = password.matches(regex2);
            if (!result4) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_222974645));
            }
            //【含有符号】true
            String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
            Pattern p = Pattern.compile(regEx);
            boolean m = p.matcher(password).find();
            if (!m) {
                throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_222974645));
            }
        }
    }

    @Override
    public int updateDistributorUserRole(Long userSid, Long roleSid) {
        return userMapper.updateDistributorUserRole(userSid, roleSid);
    }

    /**
     * 查询运营管理员
     */
    @Override
    public List<User> selectOperationUser() {

        return userMapper.selectOperationUser();
    }

    /**
     * 查询用户角色
     */
    @Override
    public List<Long> selectUserRole(Long userSid) {

        return userMapper.selectUserRole(userSid);
    }

    //检查用户名(账户名)
    private void checkUserAccount(String insertAccount) {
        if(StringUtil.isEmpty(insertAccount)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609490706));
        }
        String reg = "^[a-zA-Z][a-zA-Z0-9-_.]{3,15}$";
        if (!insertAccount.matches(reg)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1595981882));
        }

        if(StringUtils.startsWithIgnoreCase(insertAccount,"admin")
                || StringUtils.startsWithIgnoreCase(insertAccount,"test")){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_112246097));
        }

        List<User> byAccount = userMapper.findByAccount(insertAccount);
        long count = byAccount.stream().filter(u -> !"4".equals(u.getStatus()) && !UserStatus.DELETED.equals(u.getStatus())).count();
        if(count>0){
            throw new BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED));
        }
    }


    private String getGenerAccountByEmail(String email) {
        StringBuffer insertAccount = new StringBuffer(email.substring(0, email.lastIndexOf('@')));
        List<User> list = userMapper.findByAccountLike(insertAccount.toString());
        if (list != null && !list.isEmpty()) {
            List<User> resList = containsNum(list, insertAccount);
            Set<String> accountNames = Sets.newHashSet();
            for (User user : list) {
                accountNames.add(user.getAccount());
            }
            if (resList.isEmpty()) {
                insertAccount.append(1);
            } else {
                if (accountNames.contains(insertAccount.toString())) {
                    String originAccount = insertAccount.toString();
                    insertAccount.append(1);
                    generateNum(originAccount, insertAccount, accountNames);
                }
            }
        }
        return insertAccount.toString();
    }

    private void generateNum(String originAccount, StringBuffer insertAccount, Set<String> accountNames) {
        if (accountNames.contains(insertAccount.toString())) {
            String[] arr = insertAccount.toString().split(originAccount);
            insertAccount.delete(0, insertAccount.length())
                         .append(originAccount)
                         .append(Integer.parseInt(arr[1]) + 1);
            generateNum(originAccount, insertAccount, accountNames);
        }
    }

    private List<User> containsNum(List<User> userList, StringBuffer insertAccount) {
        List resList = new LinkedList();
        for (User user : userList) {
            //本身是数字组成
            boolean accountIsNum = NUMBER_PATTERN.matcher(user.getAccount()).matches();
            if (accountIsNum) {
                resList.add(user);
            } else {
                String[] arr = user.getAccount().split(insertAccount.toString());
                if (arr.length > 1 && StringUtils.isNotBlank(arr[1])) {
                    boolean isNum = NUMBER_PATTERN.matcher(arr[1]).matches();
                    if (isNum) {
                        resList.add(user);
                    }
                }
            }
        }
        return resList;
    }

    @Override
    public int insertSelectivePasswordHistory(Long userSid, String passwordHistory) {
        SysMUserPasswordHistory sysMUserPasswordHistory = new SysMUserPasswordHistory();
        sysMUserPasswordHistory.setUserId(userSid);
        sysMUserPasswordHistory.setPassword(passwordHistory);
        WebUserUtil.prepareInsertParams(sysMUserPasswordHistory);
        return this.userMapper.insertSelectivePasswordHistory(sysMUserPasswordHistory);
    }


    /**
     * 密码规则校验，密码天数校验，密码历史重复校验（抛错版）
     */
    @Override
    public String validPasswordPolicyToError(PasswordPolicyDTO policy, String password, Long userSid, Boolean firstLogin){
        String valid = validPasswordPolicy(policy, password);
        if(!ObjectUtils.isEmpty(valid)){
            return valid;
        }
        if (!ObjectUtils.isEmpty(userSid)) {
            User user = userMapper.selectById(userSid);
            if (ObjectUtils.isEmpty(user)) {
                return "用户不存在";
            }
            //校验老密码
            if (CrytoUtilSimple.validateHash(password, user.getPassword())) {
                return "新密码与历史密码相同";
            }
        }
        if (BooleanUtil.isTrue(firstLogin) || Objects.isNull(firstLogin)) {
            return null;
        }
        //校验历史密码
        return passwordPolicyService.validPasswordLeastUsedDaysAndRepeatNumToError(userSid, password, policy, firstLogin);
    }

    @Override
    public List<Role> selectUserRoleByUserSid(Long userSid) {
        return userMapper.selectUserRoleByUserSid(userSid);
    }

    /**
     * 是否是运营管理员
     * @param userSid
     * @return
     */
    @Override
    public boolean checkOperationAdmin(Long userSid) {
        List<Long> roleIdList = this.selectUserRole(userSid);
        if(roleIdList.contains(BssRoleEnum.OPERATION_ADMIN.getRoleSid())){
            return true;
        }
        return false;
    }

    /**
     * 密码规则校验
     */
    @Override
    public String validPasswordPolicy(PasswordPolicyDTO policy, String password, String... needCheckParts) {
        if (ArrayUtil.isEmpty(needCheckParts)){
            needCheckParts = new String[]{MIN_LENGTH, CHARACTER_LIMIT, RULE_OUT, CHARACTER_TYPE};
        }
        for (String needCheckPart : needCheckParts) {
            String error = checkPasswordByPart(needCheckPart, policy, password);
            if (!ObjectUtils.isEmpty(error)){
                return error;
            }
        }
        return null;
    }

    /**
     * 密码规则校验
     */
    private String checkPasswordByPart(String checkPart, PasswordPolicyDTO policy, String password){
        switch (checkPart){
            case MIN_LENGTH:
                //长度校核
                if (password.length() > 12 || password.length() < policy.getMinLength()) {
                    return "密码长度须在" + policy.getMinLength() +"_12位之间";
                }
                break;
            case CHARACTER_LIMIT:
                //不同字符数校核
                Set<Character> strings = new HashSet<>();
                for (char ch : password.toCharArray()) {
                    strings.add(ch);
                }
                if (strings.size() < policy.getCharactorLimit()) {
                    return "密码最少相异字符数：" + policy.getCharactorLimit();
                }
                break;
            case RULE_OUT:
                // 剔除常用密码
                if (CollectionUtil.isNotEmpty(policy.getRuleOut())) {
                    HashSet<String> ruleOut = new HashSet<>(policy.getRuleOut());
                    if (ruleOut.contains(password)) {
                        return "常用密码，请变更密码后再尝试";
                    }
                }
                break;
            case CHARACTER_TYPE:
                // 校验包含规则
                if (CollectionUtil.isEmpty(policy.getCharactorType())){
                    break;
                }
                boolean flag = policy.getCharactorType().stream().allMatch(v -> {
                    Pattern pattern = Pattern.compile(CharatorTypeEnum.getExampleByType(v));
                    return pattern.matcher(password).find();
                });
                if (!flag) {
                    String policyStr = policy.getCharactorType()
                                             .stream().map(v -> CharatorTypeEnum.tranFromType(v).getDesc())
                                             .collect(Collectors.joining("，"));
                    return "密码不符合规则，必须包含" + policyStr + "。";
                }
                break;
            default:
        }
        return null;
    }

    @Override
    public void replacementUser(Long userSid, Long behalfUserSid) {
        if (Objects.nonNull(userSid)) {
            cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
//            behalfUserSid = authUser.getUserSid();
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(userSid),
                    cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
            AuthUserHolder.setAuthUser(authUserInfo);
        }
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        log.info("当前用户名称：[{}]", authUserInfo.getAccount());
    }


    @Override
    public List<DistributorUserDTO> getAllDistributotUser(String status, String userType) {
        return userMapper.getAllDistributotUser(status,userType);
    }

    @Override
    public List<DistributorUserDTO> getUserByDistributotId(Long userSid) {
        return userMapper.getUserByDistributotId(userSid);
    }

    /**
     * 所有
     * @return
     */
    @Override
    public List<UserDto> selectAllUserAccount() {
        return userMapper.selectAllUserAccount();
    }

    /**
     * 是否个人认证或企业认证
     */
    @Override
    public boolean  checkCertification(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        Org org = orgService.selectRootOrg(user.getOrgSid());
        return CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus()) || CertificationStatus.AUTHSUCCEED.equals(Optional.ofNullable(org).map(Org::getCertificationStatus).orElse(null));
    }


}
