/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@TableName("sys_m_org")
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt =true)
public class Org extends BaseCCSP implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构SID
     */
    @TableId
    private Long orgSid;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 机构简称
     */
    private String orgCode;

    /**
     * 机构类型：企业company、部门department、项目project
     */
    private String orgType;

    /**
     * 负责人
     */
    private Long owner;

    /**
     * 树路径
     */
    private String treePath;

    /**
     * 父机构ID
     */
    private Long parentId;

    /**
     * 图标
     */
    private String orgIcon;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;
    /**
     * LDAP_OU
     */
    private String ldapOu;

    @TableField("skip2FA")
    private Boolean skip2FA;

    /**
     * 企业营业执照类型
     */
    private String businessLicenseType;

    /**
     * 企业营业执照地址
     */
    private String businessLicenseUrl;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 法定代表人身份证
     */
    private String legalPersonCard;

    /**
     * 授权书
     */
    private String powerAttorneyUrl;

    private String certificationStatus;

    private String applyStatus;

    private String remark;

    /**
     * 应用场景
     */
    private String applicationScenario;

    /**
     * 人员规模
     */
    private String personnelSize;
    /**
     * 所属行业
     */
    private String industryType;
    /**
     * 解决方案
     */
    private String solution;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 自定义信息
     */
    private String customizationInfo;
    /**
     * 联系人电话
     */
    @EncryptDecryptField
    @CCSPEncryptDecrypt
    private String contactPhone;


    /**
     * bsm是否开启
     */
    private Long bmsEnable;

    /**
     * 策略保留周期
     */
    private Long strategyBufferPeriod;

    /**
     * reservedResource 保留资源；releaseResource 释放资源；
     */
    private String freezingStrategy;

    /**
     * 浦发机构SID
     */
    private String refOrgId;

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    /**
     * 联系人邮件
     */
    private String contactEmail;


    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }




    public Boolean getSkip2FA() {
        return skip2FA;
    }

    public void setSkip2FA(Boolean skip2FA) {
        this.skip2FA = skip2FA;
    }

    /**
     * 分销商
     */
    @TableField(exist = false)
    private String distributorName;

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getOrgSid() {
        return orgSid;
    }

    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Long getOwner() {
        return owner;
    }

    public void setOwner(Long owner) {
        this.owner = owner;
    }

    public String getTreePath() {
        return treePath;
    }

    public void setTreePath(String treePath) {
        this.treePath = treePath;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getOrgIcon() {
        return orgIcon;
    }

    public void setOrgIcon(String orgIcon) {
        this.orgIcon = orgIcon;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLdapOu() {
        return ldapOu;
    }

    public void setLdapOu(String ldapOu) {
        this.ldapOu = ldapOu;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getBusinessLicenseType() {
        return businessLicenseType;
    }

    public void setBusinessLicenseType(String businessLicenseType) {
        this.businessLicenseType = businessLicenseType;
    }

    public String getBusinessLicenseUrl() {
        return businessLicenseUrl;
    }

    public void setBusinessLicenseUrl(String businessLicenseUrl) {
        this.businessLicenseUrl = businessLicenseUrl;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPersonCard() {
        return legalPersonCard;
    }

    public void setLegalPersonCard(String legalPersonCard) {
        this.legalPersonCard = legalPersonCard;
    }

    public String getPowerAttorneyUrl() {
        return powerAttorneyUrl;
    }

    public void setPowerAttorneyUrl(String powerAttorneyUrl) {
        this.powerAttorneyUrl = powerAttorneyUrl;
    }

    public String getCertificationStatus() {
        return certificationStatus;
    }

    public void setCertificationStatus(String certificationStatus) {
        this.certificationStatus = certificationStatus;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getApplicationScenario() {
        return applicationScenario;
    }

    public void setApplicationScenario(String applicationScenario) {
        this.applicationScenario = applicationScenario;
    }

    public String getPersonnelSize() {
        return personnelSize;
    }

    public void setPersonnelSize(String personnelSize) {
        this.personnelSize = personnelSize;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getCustomizationInfo() {
        return customizationInfo;
    }

    public void setCustomizationInfo(String customizationInfo) {
        this.customizationInfo = customizationInfo;
    }
    public Long getBmsEnable() {
        return bmsEnable;
    }

    public void setBmsEnable(Long bmsEnable) {
        this.bmsEnable = bmsEnable;
    }

    public Long getStrategyBufferPeriod() {
        return strategyBufferPeriod;
    }

    public void setStrategyBufferPeriod(Long strategyBufferPeriod) {
        this.strategyBufferPeriod = strategyBufferPeriod;
    }

    public String getFreezingStrategy() {
        return freezingStrategy;
    }

    public void setFreezingStrategy(String freezingStrategy) {
        this.freezingStrategy = freezingStrategy;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
}
