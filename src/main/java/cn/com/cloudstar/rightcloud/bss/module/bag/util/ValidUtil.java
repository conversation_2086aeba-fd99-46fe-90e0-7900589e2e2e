/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.util;

import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.hutool.core.util.NumberUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Objects;
import java.util.UUID;

public class ValidUtil {


    public static  void  validRequest(CreateBizBagRequest request){

        String type = request.getType();
        if (!ArrayUtils.contains(new String[]{StatusType.DISCOUNT,StatusType.RESOURCE,StatusType.CARD_HOUR}, type)) {
            throw  new BizException("套餐类型错误!");
        }
        if (!ArrayUtils.contains(new String[]{StatusType.POSTPAID,StatusType.PREPAID}, request.getBillingType())) {
            throw  new BizException("计费类型错误!");
        }
        if (CollectionUtils.isEmpty(request.getBizBagRequests())){
            throw  new BizException("规格不能为空!");
        }
        request.getBizBagRequests().forEach(spec -> {
            if (Objects.isNull(spec.getSpecValue())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1765666893));
            }
            if (Objects.isNull(spec.getPrice())){
                throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1773415156));
            }
            if (Objects.isNull(spec.getPeriod())){
                throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2000742214));
            }
            if (NumberUtil.isLessOrEqual(spec.getSpecValue(), BigDecimal.ZERO) || NumberUtil.isGreaterOrEqual(spec.getSpecValue(), new BigDecimal("10000"))){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1263298054));
            }
            if (spec.getPrice() <= 0){
                throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1091843789));
            }
            if (1.0E9 < spec.getPrice()){
                throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1423982624));
            }
            if (spec.getPeriod() <= 0){
                throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_450952005));
            }
            String periodStr = spec.getPeriod().toString();
            if (!periodStr.matches("^[1-9]\\d*$")){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1291505766));
            }
            String priceStr = spec.getPrice().toString();
            //有1~2位小数的正实数
            if(priceStr.matches("^[0-9]*$")){
                if(!priceStr.matches("^[0-9]+(\\.[0-9]{1,2})?$")){
                    throw  new BizException("请填写正确价格!");
                }
            }
            String specValueStr = spec.getSpecValue().toString();
            if (!StatusType.CARD_HOUR.equals(type)) {
                //0-1之间有1-2位小的数数
                if (!specValueStr.matches("^[0]{1}([.]([0-9]){1,2})?$")){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1611807411));
                }
            }
        });
    }

    public static  void  validMun(CreateBizBagSpecRequest request, String type) {
        String s1 = request.getPrice().toString();
        //有1~2位小数的正实数
        if(!s1.matches("^[0-9]+(\\.[0-9]{1,2})?$")){
            throw  new BizException("请填写正确价格");
        }
        String s2 = request.getSpecValue().toString();
        if (!StatusType.CARD_HOUR.equals(type)) {
        //0-1之间有1-2位小数的数
        if (!s2.matches("^[0]{1}([.]([0-9]){1,2})?$")){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1611807411));
        }
    }
    }

    public  static  String  getUUID(){
        return String.format("%040d", new BigInteger(UUID.randomUUID().toString().replace("-", ""), 16));
    }
}
