/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateDistributorUserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateUsersRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UpdateSubuserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSimplePolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DistributorUserDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserDto;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.oss.common.pojo.UserDTO;

public interface SysUserService extends IService<User> {
    int countByParams(Criteria example);

    User selectByPrimaryKey(Long userSid);
    int countUser(Criteria example);

    List<User> selectByParams(Criteria example);

    List<User> selectByParam(Criteria example);

    int deleteByPrimaryKey(Long userSid);

    int updateByPrimaryKeySelective(User record);


    int deleteByParams(Criteria example);



    int insert(User record);

    int insertSelective(User record);

    List<User> createUsers(CreateUsersRequest request, Long userSid);

    /**
     * 批量创建用户
     * @param request
     * @param userSid
     * @return
     */
    List<UserDTO> batchCreateUsers(CreateUsersRequest request, Long userSid);

    void updateSubuser(UpdateSubuserRequest request, Long userSid);

    List<DescribeSimplePolicy> listPolicyForUser(Long userSid);

    IPage<User> getProjectUsers(IPage<User> page, Criteria criteria);

    void deleteSubuser(Long userSid);

    boolean unlockUsers(List<Long> userIdList);

    boolean assertIamUser(Long userSid);

    boolean checkUserHasSomeAccess(Long userSid, List<String> policies);

    /**
     * 创建分销商账户
     * @param request
     */
    Long createDistributorUser(CreateDistributorUserRequest request);

    int updateDistributorUserRole(Long userSid, Long roleSid);

    /**
     * 查询运营管理员
     */
    List<User> selectOperationUser();

    /**
     * 查询用户角色
     */
    List<Long> selectUserRole(Long userSid);

    int insertSelectivePasswordHistory(Long userSid, String passwordHistory);


    /**
     * 校验密码规则
     */
    String validPasswordPolicy(PasswordPolicyDTO policy, String password, String... needCheckParts);

    /**
     * 验证密码规则，返回错误信息
     */
    String validPasswordPolicyToError(PasswordPolicyDTO policy, String password, Long userSid, Boolean firstLogin);


    /**
     * 是否是运营管理员
     * @param userSid
     * @return
     */
    boolean checkOperationAdmin(Long userSid);


    /**
     * 根据销售组织查询用户
     *
     * @return
     */
    List<DistributorUserDTO> getUserByDistributotId(Long userSid);

    List<DistributorUserDTO> getAllDistributotUser(String status, String userType);

    void replacementUser(Long userSid, Long behalfUserSid);

    List<Role> selectUserRoleByUserSid(Long userSid);

    List<UserDTO> createSubUsers(CreateUsersRequest request);

    void createSubUser(CreateUsersRequest request);

    List<UserDto> selectAllUserAccount();

    /**
     * 校验用户是否实名认证 或企业认证
     * @param userId
     * @return
     */
    boolean  checkCertification(Long userId);
}
