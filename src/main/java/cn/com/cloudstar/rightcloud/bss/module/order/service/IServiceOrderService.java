/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.service;

import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ActionParam;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.OperateNodeRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.UpgradeServiceRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.HpcDrpDeleteNodeTipVO;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.request.ResizeResourceRequest;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.ClusterNodeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.NodeInfo;

/**
 * <p>
 * 申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
public interface IServiceOrderService extends IService<ServiceOrder> {

    /**
     *
     * @param id
     * @param accountId
     * @param behalfUserSid
     * @param unsubAmount
     * @param type
     * @param originStatus
     * @param applyType
     * @param entityId
     * @param actionParam 申请单参数
     * @return
     */
    RestResult unsubscribe(String id, Long accountId, Long behalfUserSid, BigDecimal unsubAmount,
                           String type, String originStatus, String applyType, Long entityId,
                           ActionParam actionParam);

    RestResult queryVmType(String id);

    RestResult resizeResource(ResizeResourceRequest request);

    List<ServiceOrderVo> selectCloudProductInfo(List<Long> accountIds);

    List<ServiceOrderVo> selectCloudProductTotal();

    List<MarketCatalogVO> selectProductCatalogWithService();

    /**
     * 验证代客订购名称不重复
     */
    List<ServiceOrderVo> checkDuplicateNames(Criteria criteria);


    List<ServiceOrderDetail> selectDetailByResId(String id);

    /**
     * 修改节点状态
     *
     * @Author: wangcheng
     * @date: 2022/5/7
     */
    void updateNodeStatus(OperateNodeRequest request);

    void deleteNode(OperateNodeRequest request);

    HpcDrpDeleteNodeTipVO deleteNodeTip(OperateNodeRequest request);

    List<HpcBizContractDTO> getHpcBizContractDTOList(Long clusterId);

    /**
     * 获取合同剩余节点数
     * @param hpcBizContractDTOS
     * @param type 0 计算节点 1登录节点 2可视化节点 3其他管理节点
     * @return
     */
    int getAllContractNode(List<HpcBizContractDTO> hpcBizContractDTOS, int type);



    /**
     * hpc扩容服务
     * @param request
     * @return
     */
    RestResult executeUpgrade(UpgradeServiceRequest request);

    RestResult executeUpgradeByHpcRenewContract(BizContract contract);

    /**
     * 根据资源id查询订单
     * @Param:  id
     * <AUTHOR>
     * Created on 2022/6/22
     */
    ServiceOrder selectOrderDetailByResourceId(String id,String mainProductCode);

    Map<String, Object> productDetail(String id);

    List<ServiceOrderVo> selectOrderByCluster(Long clusterId, String productCode);

    /**
     *  删除节点数量校验
     * @param nodeInfos
     * @param removeComputeList 删除的计算节点
     * @param removeCliList 删除的登录节点
     */
    default void checkDeleteNodeNum(List<NodeInfo> nodeInfos, List<String> removeComputeList, List<String> removeCliList) {
        long removeComputeNum = removeComputeList == null ? 0 : removeComputeList.size();
        long removeCliNum = removeCliList == null ? 0 : removeCliList.size();

        if (removeComputeNum > 0L || removeCliNum > 0L) {
            long totalComputeNum = nodeInfos.stream().filter(node -> ClusterNodeType.COMPUTE.equals(node.getNodeType())).count();
            long totalCliNum = nodeInfos.stream().filter(node -> HpcPointType.CCS_CLI.equals(node.getHpcPointType())).count();
            if (removeComputeNum == totalComputeNum && removeCliNum == totalCliNum) { //至少保留一个登录节点和一个计算节点
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_COMPUTE_AND_CLI_NODE_ERROR));
            } else if (removeComputeNum == totalComputeNum) { //计算节点至少保留一个
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_COMPUTE_NODE_ERROR));
            } else if (removeCliNum == totalCliNum) { //登录节点至少保留一个
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_CLI_NODE_ERROR));
            }
        }
    }

    /**
     * 校验产品是否存在未完成订单
     * @param id
     * @param productCode
     */
    void checkPendingOrder(String id, String productCode);
}
