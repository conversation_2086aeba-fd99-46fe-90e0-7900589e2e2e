/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/6/15
 */
@ApiModel(description = "服务申请")
@Data
public class ApplyServiceRequest {

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型,如购买（apply），续订（renew）,变更（modify）")
    private String orderType;

    /**
     * 项目id
     */
    @ApiModelProperty("项目ID")
    private Long projectId;

    /**
     * 优惠券id
     */
    @ApiModelProperty("优惠券ID")
    private Long couponId;

    /**
     * 代表用户sid
     */
    @ApiModelProperty("代客下单管理员ID")
    private Long behalfUserSid;

    /**
     * 用户sid
     */
    @ApiModelProperty("下单用户ID")
    private Long userSid;

    /**
     * 用户组织sid
     */
    @ApiModelProperty("下单用户组织ID")
    private Long userOrgSid;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("资源ID")
    private Long resourceId;

    @ApiModelProperty("产品类型")
    private String proudctType;

    /**
     * 相同产品是否汇总
     */
    @ApiModelProperty("相同产品是否汇总")
    private boolean summaryFlg = false;

    @ApiModelProperty("多产品优惠分摊")
    private boolean shareFlg = false;

    /**
     * 产品信息
     */
    @NotNull
    @ApiModelProperty("产品信息")
    @Valid
    private List<ProductInfoVO> productInfo;

    /**
     * 资源信息
     */
    @ApiModelProperty("资源信息")
    private ActionParam resourceInfo;

    @ApiModelProperty("底层资源池名称")
    private String clusterName;
    @ApiModelProperty("底层节点ID")
    private String clusterId;



    private String clusterUuid;
    /**
     * 运营实体ID
     */
    private Long entityId;

    @ApiModelProperty("冻结策略")
    private String freezingStrategy;

    /**
     * 询价时间点
     */
    private String pointInTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 模型集市的规格id
     */
    private Long priceJoinId;
}
