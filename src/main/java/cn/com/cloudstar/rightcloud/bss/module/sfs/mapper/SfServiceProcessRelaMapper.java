package cn.com.cloudstar.rightcloud.bss.module.sfs.mapper;

import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.SfServiceProcessRela;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-09-29 16:55
 * @Desc
 */
@Repository
public interface SfServiceProcessRelaMapper {

    @Select("select id, service_code, process_id, created_by, created_dt, updated_by, updated_dt from sf_service_process_rela")
    List<SfServiceProcessRela> selectAll();
}
