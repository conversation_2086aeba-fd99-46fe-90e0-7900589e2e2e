/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.service;

import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserExportDTO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.DescribeRechargeAccountInfoResponse;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import cn.com.cloudstar.rightcloud.bss.common.enums.PayStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizDeposit;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;

/**
 * <p>
 * 账户  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-22
 */
public interface IBizBillingAccountService extends IService<BizBillingAccount> {

    /**
     * 获取账户列表，根据分页参数是否执行分页
     * @param request 入参
     * @return
     */
    IPage<BizBillingAccount> getBillingAccountList(DescribeBillingAccountRequest request);

    /**
     * 获取账户折扣列表
     * @param page 入参
     * @return
     */
    IPage<BizBillingAccount> getAccountDiscountList(Page<BizBillingAccount> page, Criteria criteria);

    /**
     * 获取账户详情
     * @param id 账户ID
     * @return
     */
    BizBillingAccount getBillingAccountDetail(Long id);

    /**
     * 账户充值
     * @param request 入参
     * @return
     */
    Boolean recharge(RechargeBillingAccountRequest request);


    /**
     * 订单入账
     *
     * @param request 入参
     */
    Boolean orderPosting(MarketOrderPostingRequest request);

    /**
     * 按id允许开具发票
     *
     * @param id id
     * @return {@link Boolean}
     */
    Boolean allowInvoicingById(String id);

    /**
     * 修改信用额度
     * @param request 入参
     * @return
     */
    Boolean updateCreditLine(UpdateCreditLineRequest request);

    /**
     * 充值明细查询
     * @param request 入参
     * @return
     */
    IPage<BizDeposit> getDepositList(DescribeDepositRequest request);

    /**
     * 更新充值明细
     * @param request 入参
     * @return
     */
    Boolean updateDeposit(UpdateDepositRequest request);

    /**
     * 获取帐户概述
     * 获取账户概览信息
     *
     * @param isShieldedProducts 是否屏蔽未上的产品
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    Map<String, Object> getAccountOverviews(Boolean isShieldedProducts);

    /**
     * 根据当前登录用户获取账户信息
     * @param orgId
     * @return
     */
    BizBillingAccount getBizBillingAccountByOrgId(Long orgId);

    User getUserBaseInfo();

    /**
     * 扣减余额
     * @param bizBillingAccountId 账户ID
     * @param subtractAmount 扣减金额
     */
    void reduceBalance(Long bizBillingAccountId, BigDecimal subtractAmount);

    /**
     * 增加余额
     * @param bizBillingAccountId 账户ID
     * @param addAmount 扣减金额
     */
    void increaseBalance(Long bizBillingAccountId, BigDecimal addAmount);

    boolean updateAccount(UpdateDistributorRequest request);

    BizDeposit addPlatformDepositRecord(Long accountSid, BigDecimal amount, String contractSid, String description,
                                        BizBillingAccount billingAccount, PayStatusEnum payStatusEnum,
                                        RechargeTypeEnum rechargeTypeEnum);

    BizAccountDeal makeAccountDeal(BizDeposit deposit, BizBillingAccount billingAccount,
                                   RechargeTypeEnum rechargeTypeEnum, boolean existToken);

    Long countCustomers();

    RestResult doUnfreezeUser(Long accountId);
    RestResult doUnfreezeUser(BizBillingAccount bizBillingAccount);

    List<BizBillingAccount> getSimpleAccountAndDistributorInfo(Map param);

    List<BizBillingAccount> getByDistributorNameAndIds(String distributorName,Set<Long> ids);

    /**
     * 根据请求参数获取账号信息
     */
    List<BizBillingAccount> selectAccountInfoByCustomerName(DescribeGaapCostRequest request);

    /**
     * 客户信息 导出
     */
    List<BizBillingAccount> accountInfExport(DescribeBillingAccountRequest request);

    void downloadTemplate(HttpServletResponse response,String fileType);

    /**
     * 信用额度增加到延迟队列
     * @param request
     */
    void sendUpdateCreditLineSchedule(UpdateCreditLineRequest request);

    /**
     * 根据实体和用户id查询账户
     * @param entitySid
     * @param userSid
     * @return
     */
    BizBillingAccount getByEntityIdAndUserId(Long entitySid,Long userSid);

    /**
     *
     * @param adminSid
     * @return
     */
    List<BizBillingAccount> getByAdminSid(Long adminSid);

    BizBillingAccount getFirst();

    /**
     * 根据组织查询账户
     * @param orgSid
     * @return
     */
    List<BizBillingAccount> getByOrgSid(Long orgSid);

    /**
     * 调整调整业务标识Tag
     * @param request 参数
     * @return boolean
     */
    boolean updateBusinessTag(UpdateBusinessTagRequest request);

    /**
     * 根据分销商id获取所有租户信息
     */
    List<BizBillingAccount> getBizBillingAccountsByOrgId(Long orgSid);

    /**
     * 获取充值账户信息详情
     */
    DescribeRechargeAccountInfoResponse getRechargeAccountDetail(Long accountId);

    Map<String, Object> getCollectorInfo();


    /**
     * 通过组织id或实体id获取业务计费帐户id
     *
     * @param authUserInfo 用户信息
     * @return {@link Object} 如果只有一个就返回id 多个为list
     */
    Object getBizBillingAccountIdByOrgIdOrEntityId(AuthUser authUserInfo);

    /**
     * 账户金额清理
     * @param request 清理请求，包含清理类型
     * @Since 2023/05/03 11:16
     */
    void amountClearance(AmountClearanceRequest request);

    /**
     * 设置到期时间
     * @param request
     * @return boolean
     */
    boolean setExpirationTime(SetExpirationTimeRequest request);
    /**
     * 账户开票升级
     */
    void invoicingUpgrade();
    /**
     * 导出账户折扣列表
     * @param  入参
     * @return
     */
    List<BizBillingAccount> getAccountDiscountListExport(Criteria criteria);

    /**
     * 异步导出客户信息
     * @param request
     */
    RestResult asyncExport(DescribeBillingAccountRequest request);


    boolean updateFreeCapacity(UpdateFreeCapacityRequest request);

    IPage<BizBillingAccount> getBillingAccountOpenApiList(DescribeBillingAccountRequest request);

    void exportAccountWithCustomInfo(List<BizBillingAccount> bizBillingAccounts, HttpServletResponse response, List<UserExportDTO> resUser,
                                     String customInfoTemplate);

    XSSFWorkbook createWorkbookAccountWithCustomInfo(List<BizBillingAccount> bizBillingAccounts, List<UserExportDTO> resUser,
                                                     String customInfoTemplate);
}
