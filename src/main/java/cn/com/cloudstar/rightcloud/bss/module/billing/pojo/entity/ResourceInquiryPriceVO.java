/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity;

import java.math.BigDecimal;

import lombok.Data;

/**
 * @Auther: 张淇囿
 * @Date: 2022/03/10
 */
@Data
public class ResourceInquiryPriceVO {

    /**
     * 原价
     */
    private BigDecimal originalCost;

    /**
     * 折扣优惠金额
     */
    private BigDecimal orgDiscount;

    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponDiscount;

    /**
     * 充值现金券支付金额
     */
    private BigDecimal couponAmount;

    /**
     * 现金支付金额
     */
    private BigDecimal cashAmount;

    /**
     * 信用额度支付金额
     */
    private BigDecimal creditAmount;
}