/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 账号收支记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Data
public class BizAccountDeal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SID
     */
    @TableId(value = "deal_sid")
    private Long dealSid;

    /**
     * 交易编号
     */
    private String flowNo;
    /**
     * 云环境类型 HuaweiCloud，Qcloud,Aliyun
     */
    private String envType;

    /**
     * 云环境名称
     */
    private String envName;
    /**
     * 收支类型 in（收入） out （支出）
     */
    private String type;

    /**
     * 交易类型 charge（充值）pay（消费）
     */
    private String tradeType;

    /**
     * 交易渠道 alipay（支付宝）platform（平台）acctCash（用户余额）accCredit（信用额度余额）coupon（优惠卷）wechatPay（微信支付）cashBalance（现金券）
     */
    private String tradeChannel;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 备注 主要为明细针对的产品信息如：云主机，弹性公网IP等
     */
    private String remark;

    /**
     * 账期 账期为年-月格式YYYY-MM
     */
    private String billingCycle;

    /**
     * 现金消费金额
     */
    private BigDecimal amount;

    /**
     * 余额 余额
     */
    private BigDecimal balance;

    /**
     * 现金券余额
     */
    private BigDecimal balanceCash;

    /**
     * 信用额度 信用额度
     */
    private BigDecimal balanceCredit;

    /**
     * 充值现金券消费金额
     */
    private BigDecimal cashAmount;

    /**
     * 当前抵扣现金券余额
     */
    private BigDecimal  deductBalanceCash;


    /**
     * 账户
     */
    private Long accountSid;

    /**
     * 账户名称 账户名称
     */
    private String accountName;

    /**
     * 组织ISD
     */
    private Long orgSid;

    /**
     * 用户SID
     */
    private Long userSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 资金监管状态
     */
    private String superviseStatus;

    /**
     * 交易时间
     */
    @TableField(exist = false)
    private String tradeTime;

    /**
     * 账户
     */
    @TableField(exist = false)
    private String account;

    /**
     * 交易时间
     */
    private Long dealTime;

    /**
     * 所属分销商
     */
    @TableField(exist = false)
    private String distributorName;

    /**
     * 实体id
     */
    @ExcelIgnore
    private Long entityId;

    /**
     * 实体名称
     */
    @ExcelProperty(value = "账户名称", order = 4)
    private String entityName;

    @ExcelIgnore
    private String chargingType;

    @ExcelProperty(value = "收费规则", order = 18)
    @TableField(exist = false)
    private String chargingTypeForExcel;

    /**
     * 账户ID
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Long accountId;

    /**
     * 信用额度消费金额
     */
    @TableField(exist = false)
    private BigDecimal balanceCreditAmount;

    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;

    /**
     * 自定义信息
     */
    @TableField(exist = false)
    private String customizationInfo;

    /**
     * 用户名，导出使用
     */
    @TableField(exist = false)
    private String userName;

}
