/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PasswordUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PasswordPolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.PasswordPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeSysConfigRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeSysConfigResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class PasswordPolicyServiceImpl implements PasswordPolicyService {
    @Autowired
    private PasswordPolicyMapper passwordPolicyMapper;
    @Autowired
    private FeignService feignService;
    @Autowired
    private UserMapper userMapper;

    private static final Logger logger = LoggerFactory.getLogger(PasswordPolicyServiceImpl.class);

    @Override
    public int countByParams(Criteria example) {
        int count = this.passwordPolicyMapper.countByParams(example);
        return count;
    }

    @Override
    public PasswordPolicy selectByPrimaryKey(Long id) {
        return this.passwordPolicyMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PasswordPolicy> selectByParams(Criteria example) {
        return this.passwordPolicyMapper.selectByParams(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.passwordPolicyMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PasswordPolicy record) {
        return this.passwordPolicyMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PasswordPolicy record) {
        return this.passwordPolicyMapper.updateByPrimaryKey(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.passwordPolicyMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(PasswordPolicy record, Criteria example) {
        return this.passwordPolicyMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(PasswordPolicy record, Criteria example) {
        return this.passwordPolicyMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(PasswordPolicy record) {
        return this.passwordPolicyMapper.insert(record);
    }

    @Override
    public int insertSelective(PasswordPolicy record) {
        return this.passwordPolicyMapper.insertSelective(record);
    }


    @Override
    public PasswordPolicyDTO getPasswordPolicyByOrgSid(Long orgSid) {
        //orgSid有值，parentSid有值，说明是子用户，也就根据orgSid去找密码策略
        //此方法需根据orgSid查找，所以parentId随便给个值
        return initPasswordPolicy(orgSid, 0L);
    }

    @Override
    public PasswordPolicyDTO getPasswordPolicy(Long orgSid, Long parentId) {
        return initPasswordPolicy(orgSid, parentId);
    }

    /**
     * 获取密码策略列表
     */
    private PasswordPolicy getPasswordPolicyList(Long orgSid, Long parentId) {
        if (ObjectUtils.isEmpty(parentId) && ObjectUtils.isEmpty(orgSid)) {
            return null;
        }
        //没有parentId，说明不是子用户，那密码策略走系统控制台
        if (ObjectUtils.isEmpty(parentId)) {
            return selectRootAccountPolicy().stream().findFirst().orElse(null);
        }
        //根据orgSid获取子用户密码策略
        return selectByParams(new Criteria("orgSid", orgSid)).stream().findFirst().orElse(null);
    }

    /**
     * 构建密码策略详情
     */
    private PasswordPolicyDTO initPasswordPolicy(Long orgSid, Long parentId) {
        //获取密码策略列表
        PasswordPolicy passwordPolicy = getPasswordPolicyList(orgSid, parentId);
        if (ObjectUtils.isEmpty(passwordPolicy)) {
            // 采用默认配置
            return PasswordUtil.defaultPolicy();
        }
        PasswordPolicyDTO policy = BeanConvertUtil.convert(passwordPolicy, PasswordPolicyDTO.class);
        if (Strings.isNullOrEmpty(passwordPolicy.getRuledOut())) {
            policy.setRuleOut(Lists.newArrayList());
        } else {
            policy.setRuleOut(JSONUtil.parseArray(passwordPolicy.getRuledOut())
                                                 .stream().map(Object::toString)
                                                 .filter(v -> ObjectUtil.isNotEmpty(v) && !"null".equals(v))
                                                 .collect(Collectors.toList()));
        }

        if (Strings.isNullOrEmpty(passwordPolicy.getRule())) {
            policy.setCharactorType(Lists.newArrayList());
        } else {
            policy.setCharactorType(JSONUtil.parseArray(passwordPolicy.getRule())
                                                       .stream().map(Object::toString)
                                                       .filter(v -> ObjectUtil.isNotEmpty(v) && !"null".equals(v))
                                                       .collect(Collectors.toList()));
        }
        if (StringUtil.isNullOrEmpty(policy.getPwdExpireTimeValidity())) {
            policy.setPwdExpireTimeValidity(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME_VALIDITY);
        }
        if (StringUtil.isNullOrEmpty(policy.getPwdExpireTime())) {
            policy.setPwdExpireTime(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME);
        }
        if (StringUtil.isNullOrEmpty(policy.getPwdLeastUsedDay())) {
            policy.setPwdLeastUsedDay(PasswordUtil.DEFAULT_CREDENTIAL_LEAST_USED_DAY);
        }
        if (StringUtil.isNullOrEmpty(policy.getPwdRepeatNum())) {
            policy.setPwdRepeatNum(PasswordUtil.DEFAULT_CREDENTIAL_REPEAT_NUM);
        }
        return policy;
    }




    /**
     * 校验密码最少使用天数和前N个历史密码
     */
    @Override
    public boolean validPasswordLeastUsedDaysAndRepeatNum(Long userSid, String password, PasswordPolicyDTO policy) {
        if (Objects.isNull(userSid)) {
            return true;
        }
        User user = userMapper.selectByPrimaryKey(userSid);
        List<SysMUserPasswordHistory> histories = getPasswordHistory(userSid);
        if (!ObjectUtils.isEmpty(histories)){
            boolean leastUserDay = checkLeastUsedDay(policy, histories.get(0));
            if (!leastUserDay) {
                return false;
            }
        }
        return checkRepeat(policy, histories, password);
    }

    @Override
    public String validPasswordLeastUsedDaysAndRepeatNumToError(Long userSid, String password, PasswordPolicyDTO policy, Boolean skipLeastUsedDay){
        if (Objects.isNull(userSid)) {
            return null;
        }
        List<SysMUserPasswordHistory> histories = getPasswordHistory(userSid);
        if (!skipLeastUsedDay && !ObjectUtils.isEmpty(histories)) {
            if (!checkLeastUsedDay(policy, histories.get(0))){
                return "密码未达到最少天数";
            }
        }
        if (!checkRepeat(policy, histories, password)){
            return "新密码与历史密码相同，请重新设置";
        }
        return null;
    }



    /**
     * 获取用户历史密码
     */
    private List<SysMUserPasswordHistory> getPasswordHistory(Long userSid){
        Criteria criteria = new Criteria();
        criteria.put("userId", userSid);
        criteria.setOrderByClause("created_dt desc");
        return passwordPolicyMapper.selectPasswordHistoryByParams(criteria);
    }

    /**
     * 验证是否密码最少使用天数
     */
    private boolean checkLeastUsedDay(PasswordPolicyDTO policy, SysMUserPasswordHistory history){
        if (ObjectUtils.isEmpty(policy.getPwdLeastUsedDay())) {
            return true;
        }
        if (policy.getPwdLeastUsedDay().equals(0L)) {
            return true;
        }
        Calendar instance = Calendar.getInstance();
        instance.setTime(history.getCreatedDt());
        instance.add(Calendar.DATE, + Math.toIntExact(policy.getPwdLeastUsedDay()));
        return instance.getTime().getTime() < DateUtil.date().getTime();
    }


    /**
     * 验证是否与第N个密码重复
     */
    private boolean checkRepeat(PasswordPolicyDTO policy, List<SysMUserPasswordHistory> histories, String password){
        if (ObjectUtils.isEmpty(policy.getPwdRepeatNum())){
            return true;
        }
        if (ObjectUtils.isEmpty(histories)) {
            return true;
        }
        if (policy.getPwdRepeatNum() <= histories.size()){
            histories = histories.subList(0, Math.toIntExact(policy.getPwdRepeatNum()));
        }
        return histories.stream().noneMatch(it -> CrytoUtilSimple.validateHash(password,it.getPassword()));
    }


    /**
     * 查询系统控制台的策略
     *
     * @return {@link List}<{@link PasswordPolicy}>
     */
    private List<PasswordPolicy> selectRootAccountPolicy() {
        DescribeSysConfigRequest request = new DescribeSysConfigRequest();
        request.setConfigType("credential_strategy_config");
        RestResult restResult = feignService.displayAllMessageByFeign(request);
        Object data = restResult.getData();
        List<DescribeSysConfigResponse> convert = (List<DescribeSysConfigResponse>) data;
        List<DescribeSysConfigResponse> configs = BeanConvertUtil.convert(convert, DescribeSysConfigResponse.class);
        PasswordPolicy passwordPolicy = new PasswordPolicy();
        for (DescribeSysConfigResponse c : configs) {
            switch (c.getConfigKey()) {
                case "credential.least.used.day":
                    passwordPolicy.setPwdLeastUsedDay(Long.valueOf(c.getConfigValue()));
                    break;
                case "credential.repeat.num":
                    passwordPolicy.setPwdRepeatNum(Long.valueOf(c.getConfigValue()));
                    break;
                case "credential.expire.time.validity":
                    passwordPolicy.setPwdExpireTimeValidity(Boolean.valueOf(c.getConfigValue()));
                    break;
                case "credential.expire.time":
                    passwordPolicy.setPwdExpireTime(Long.valueOf(c.getConfigValue()));
                    break;
                case "credential.length.min":
                    passwordPolicy.setMinLength(Integer.valueOf(c.getConfigValue()));
                    break;
                case "credential.character.limit":
                    passwordPolicy.setCharactorLimit(Convert.toInt(c.getConfigValue()));
                    break;
                case "credential.rule":
                    passwordPolicy.setRule(c.getConfigValue());
                    break;
                case "credential.rule.out":
                    passwordPolicy.setRuledOut(c.getConfigValue());
            }
        }
        return Collections.singletonList(passwordPolicy);
    }

    @Override
    public PasswordPolicyDTO getPasswordPolicyByOrgSidSubUser(Long orgSid) {
        PasswordPolicyDTO passwordPolicyDTO = null;

        List<PasswordPolicy> policyList = null;

        policyList = this.selectByParams(new Criteria("orgSid", orgSid));

        if (CollectionUtils.isEmpty(policyList)) {
            // 采用默认配置
            return PasswordUtil.defaultPolicy();
        }
        PasswordPolicy passwordPolicy = policyList.get(0);
        passwordPolicyDTO = BeanConvertUtil.convert(passwordPolicy, PasswordPolicyDTO.class);
        if (Strings.isNullOrEmpty(passwordPolicy.getRuledOut())) {
            passwordPolicyDTO.setRuleOut(Lists.newArrayList());
        } else {
            passwordPolicyDTO.setRuleOut(Arrays.asList(passwordPolicy.getRuledOut().replace("\"","").replace("[","").replace("]","").split(",")));
        }

        if (Strings.isNullOrEmpty(passwordPolicy.getRule())) {
            passwordPolicyDTO.setCharactorType(Lists.newArrayList());
        } else {
            passwordPolicyDTO.setCharactorType(Arrays.asList(passwordPolicy.getRule().replace("\"","").replace("[","").replace("]","").split(",")));
        }
        if (StringUtil.isNullOrEmpty(passwordPolicyDTO.getPwdExpireTimeValidity())) {
            passwordPolicyDTO.setPwdExpireTimeValidity(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME_VALIDITY);
        }
        if (StringUtil.isNullOrEmpty(passwordPolicyDTO.getPwdExpireTime())) {
            passwordPolicyDTO.setPwdExpireTime(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME);
        }
        if (StringUtil.isNullOrEmpty(passwordPolicyDTO.getPwdLeastUsedDay())) {
            passwordPolicyDTO.setPwdLeastUsedDay(PasswordUtil.DEFAULT_CREDENTIAL_LEAST_USED_DAY);
        }
        if (StringUtil.isNullOrEmpty(passwordPolicyDTO.getPwdRepeatNum())) {
            passwordPolicyDTO.setPwdRepeatNum(PasswordUtil.DEFAULT_CREDENTIAL_REPEAT_NUM);
        }
        if (StrUtil.isBlank(passwordPolicy.getSecAuth())) {
            passwordPolicyDTO.setSecAuth("enable");
        }
        return passwordPolicyDTO;
    }
}
