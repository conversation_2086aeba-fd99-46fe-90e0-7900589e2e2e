/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;

import cn.com.cloudstar.rightcloud.bss.module.order.service.impl.*;

/**
 * The type OrderServiceEnum.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/6/16
 */
public enum OrderServiceEnum {
    /**
     * 云主机
     */
    ECS("ECS", EcsOrderServiceImpl.class),

    /**
     * 云硬盘
     */
    EBS("EBS", FederationProductOrderServiceImpl.class),


    /**
     * 快照
     */
    VBS_VM("VBS-VM", VbsOrderServiceImpl.class),

    /**
     * 快照
     */
    VBS_VD("VBS-VD", VbsOrderServiceImpl.class),

    /**
     * RDS
     */
    RDS("RDS", FederationProductOrderServiceImpl.class),

    /**
     * SFS2.0 弹性文件服务
     */
    SFS2("SFS2.0", ShareOrderServiceImpl.class),
    /**
     * HPC
     */
    HPC("HPC",HPCOrderServiceImpl .class),
    /**
     * HPC-SAAS
     */
    HPC_SAAS("HPC-SAAS",HPCOrderServiceImpl .class),

    /**
     * 'OBS', 'ModelArts', 'HPC', 'ECS-X86', 'EVS', 'VPC', 'VPN', 'EIP-INNER', 'DC', 'IMS', 'CBR',
     * 'ELB', 'CES', 'CloudSecurity', 'SWR', 'DedicatedResourcePool'
     */
    OBS("OBS", InnerProductOrderServiceImpl.class),
    MODEL_ARTS("ModelArts", InnerProductOrderServiceImpl.class),
    ECS_X86("ECS-X86", InnerProductOrderServiceImpl.class),
    EVS("EVS", InnerProductOrderServiceImpl.class),
    VPC("VPC", InnerProductOrderServiceImpl.class),
    VPN("VPN", InnerProductOrderServiceImpl.class),
    EIP_INNER("EIP-INNER", InnerProductOrderServiceImpl.class),
    DC("DC", InnerProductOrderServiceImpl.class),
    IMS("IMS", InnerProductOrderServiceImpl.class),
    CBR("CBR", FederationProductOrderServiceImpl.class),
    SFS_TURBO("SFS_TURBO", FederationProductOrderServiceImpl.class),
    ELB("ELB", InnerProductOrderServiceImpl.class),
    //CES("CES", InnerProductOrderServiceImpl.class),
    CLOUD_SECURITY("CloudSecurity", InnerProductOrderServiceImpl.class),
    SWR("SWR", InnerProductOrderServiceImpl.class),
    DEDICATED_RESOURCE_POOL("DRP", InnerProductOrderServiceImpl.class),
    IAAS("IAAS", InnerProductOrderServiceImpl.class),
    HPC_DRP("HPC-DRP", HPCDrpOrderServiceImpl.class),
    HSS("HSS", FederationProductOrderServiceImpl.class),
    DWS("DWS", FederationProductOrderServiceImpl.class),
    AI_MARKET("AI-MARKET", AiMarketOrderServiceImpl.class),
    WAF("WAF", FederationProductOrderServiceImpl.class),
    SEC_MASTER("SEC-MASTER", FederationProductOrderServiceImpl.class),
    DNS("DNS", FederationProductOrderServiceImpl.class),
    DBSS("DBSS", FederationProductOrderServiceImpl.class),
    CFW("CFW", FederationProductOrderServiceImpl.class),
    DEW("DEW", FederationProductOrderServiceImpl.class),
    DS("DS", FederationProductOrderServiceImpl.class),
    CSS("CSS", FederationProductOrderServiceImpl.class),
    AS_GROUP("AS", InnerProductOrderServiceImpl.class),
    CES("CES", FederationProductOrderServiceImpl.class),
    MRS("MRS", FederationProductOrderServiceImpl.class),
    /**
     * 弹性IP
     */
    EIP("EIP", FederationProductOrderServiceImpl.class),

    CCE("CCE", FederationProductOrderServiceImpl.class),

    GIT("GIT", FederationProductOrderServiceImpl.class),
    IIT("IIT", FederationProductOrderServiceImpl.class),
    DCS("DCS", FederationProductOrderServiceImpl.class),
    RS_BMS("RS-BMS", RSBmsOrderServiceImpl.class),
    NAT("NAT", InnerProductOrderServiceImpl.class),
    SDRS("SDRS", FederationProductOrderServiceImpl.class),
    ROMA_CONNECT("ROMA-CONNECT", FederationProductOrderServiceImpl.class),
    AOM("AOM", FederationProductOrderServiceImpl.class),
    APM("APM", FederationProductOrderServiceImpl.class),
    ER("ER", FederationProductOrderServiceImpl.class),
    VPCEP("VPCEP", FederationProductOrderServiceImpl.class),
    CTS("CTS", FederationProductOrderServiceImpl.class),
    LTS("LTS", FederationProductOrderServiceImpl.class),
    CBH("CBH", FederationProductOrderServiceImpl.class),
    VSS("VSS", FederationProductOrderServiceImpl.class),
    AIkits("AIkits", FederationProductOrderServiceImpl.class),
    Modelarts_studio("Modelarts_Studio", FederationProductOrderServiceImpl.class),
    APPStage("AppStage", FederationProductOrderServiceImpl.class),
    DSC("DSC", FederationProductOrderServiceImpl.class),
    SMN("SMN", FederationProductOrderServiceImpl.class),
    ;

    private String productCode;
    private Class clazz;

    OrderServiceEnum(String productCode, Class clazz) {
        this.productCode = productCode;
        this.clazz = clazz;
    }

    public Class getClazz() {
        return clazz;
    }


    public String getProductCode() {
        return productCode;
    }
}
