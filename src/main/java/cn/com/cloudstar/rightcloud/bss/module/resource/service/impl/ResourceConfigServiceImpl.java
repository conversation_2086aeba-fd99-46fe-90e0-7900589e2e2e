/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.ShareSupportClusterTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResContainerCluster;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResDcs;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRds;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.bss.common.constants.CloudEnvType;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingTariffSpecChargeMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.ResourceDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaPoolService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderResourceRefService;
import cn.com.cloudstar.rightcloud.bss.module.renewal.mapper.ResRenewRefMapper;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.entity.RenewalDTO;
import cn.com.cloudstar.rightcloud.bss.module.resource.service.ResourceConfigService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfServiceCatalogRelationMapper;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResAllDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResFloatingIpByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cce.CceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.dcs.ResDcsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.eip.EipRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.rds.ResRdsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.snapshot.ResSnapshotRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/10 14:20
 */
@Service
@Slf4j
public class ResourceConfigServiceImpl implements ResourceConfigService {

    public static ArrayList<String> PRODUCT_CODE = Lists.newArrayList(ProductCodeEnum.ECS.getProductType());

    @DubboReference
    private ResVmRemoteService resVmRemoteService;

    @DubboReference
    private ResDcsRemoteService resDcsRemoteService;

    @DubboReference
    private CceRemoteService cceRemoteService;

    @DubboReference
    private EipRemoteService eipRemoteService;

    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;
    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @DubboReference
    private ResVdRemoteService resVdRemoteService;

    @Autowired
    private SfServiceCatalogRelationMapper sfServiceCatalogRelationMapper;

    @DubboReference
    private ResFloatingIpRemoteService resFloatingIpRemoteService;

    @Autowired
    private ResRenewRefMapper resRenewRefMapper;

    @DubboReference
    private ResSnapshotRemoteService resSnapshotRemoteService;

    @DubboReference
    private ResRdsRemoteService resRdsRemoteService;
    @DubboReference
    private ResAllRemoteService resAllRemoteService;
    @DubboReference
    private ShareRemoteService shareRemoteService;
    @Autowired
    private IServiceOrderResourceRefService serviceOrderResourceRefService;
    @Autowired
    private IServiceOrderDetailService serviceOrderDetailService;
    @Autowired
    private ResMaPoolService resMaPoolService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private BizBillingTariffSpecChargeMapper bizBillingTariffSpecChargeMapper;

    @Override
    public Map<String, Object> selectResource(String id) {
        String type = resAllRemoteService.selectProductTypeById(id);
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(id);
        if (!Objects.isNull(resVd)) {
            type = ProductCodeEnum.DISK.getProductType();
        }
        if (Strings.isNullOrEmpty(type)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        switch (type) {
            case "ECS":
                return this.select4Vm(id);
            case "EIP":
                return this.select4EIP(id);
            case "EBS":
                return this.select4Vd(id);
            case "VBS-VD":
                return this.select4VBSVD(id);
            case "RDS":
                return this.select4RDS(id);
            case "SFS2.0":
                return this.select4SFS(id);
            default:
                return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> select4Vm(String id) {
        HashMap<String, Object> returnData = new HashMap<>();
        if (Strings.isNullOrEmpty(id)) {
            return returnData;
        }
        ResVm vm = resVmRemoteService.selectByPrimaryKey(id);
        if (Objects.isNull(vm)) {
            return returnData;
        }
        ArrayList<Map<String, String>> config = Lists.newArrayList();
        returnData.put("productCode", ProductCodeEnum.ECS.getProductType());
        returnData.put("chargeType", vm.getInstanceChargeType());

        // 询价参数
        JSONObject inquiryJson = new JSONObject();
        inquiryJson.put("productCode", ProductCodeEnum.ECS.getProductType());
        inquiryJson.put("chargeType", vm.getInstanceChargeType());
        inquiryJson.put("cloudEnvId", vm.getCloudEnvId());
        inquiryJson.put("regionId", vm.getRegion());
        inquiryJson.put("amount", 1);

        JSONObject data = new JSONObject();

        JSONObject instance = new JSONObject();

        instance.put("chargeItemCategory", "compute");
        instance.put("zoneId", vm.getZone());

        returnData.put("id", vm.getId());
        returnData.put("resourceId", vm.getInstanceId());
        config.add(createMap("ID", vm.getId()));
        config.add(createMap("名称", vm.getInstanceName()));
        config.add(createMap("资源ID", vm.getInstanceId()));
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(vm.getInstanceChargeType())));
        config.add(createMap("内网IP", vm.getInnerIp()));
        config.add(createMap("公网IP", vm.getPublicIp()));
        config.add(createMap("操作系统", vm.getOsName()));
        config.add(createMap("区域", vm.getZoneName()));
        config.add(createMap("镜像", vm.getImageName()));
        if (Objects.nonNull(vm.getEndTime())) {
            returnData.put("endTime", DateUtil.format(vm.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(vm.getStartTime())) {
            returnData.put("startTime", DateUtil.format(vm.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
        queryResVmTypeByParamsRequest.setUuid(vm.getInstanceType());
        ResVmType resVmType = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
        String vmType = vm.getCpu() + " vCPU " + vm.getMemory() / 1020 + " GiB";
        if (Objects.nonNull(resVmType)) {
            instance.put("instanceType", resVmType.getId());
            data.put("instance", instance);
            returnData.put("family", resVmType.getFamily());
            returnData.put("familyName", resVmType.getFamilyName());
            returnData.put("cpu", resVmType.getCpu());
            returnData.put("ram", resVmType.getRam());
            returnData.put("instanceType", resVmType.getUuid());
            vmType = vmType + " ("
                    + (Objects.isNull(resVmType.getFamilyName()) ? "" : (resVmType.getFamilyName() + ","))
                    + (Objects.isNull(resVmType.getFamily()) ? "" : (resVmType.getFamily() + ","))
                    + resVmType.getUuid() + ")";
        }
        config.add(createMap("实例类型", vmType));

        CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(vm.getCloudEnvId());
        config.add(createMap("云环境", cloudEnv.getCloudEnvName()));
        returnData.put("cloudEnvId", cloudEnv.getId());


        config.add(createMap("私有网络", vm.getNetworkName()));
        config.add(createMap("子网", vm.getSubnetName()));

        List<ResVd> resVds = resVdRemoteService.selectByHostId(vm.getId());
        if (CollectionUtil.isNotEmpty(resVds)) {
            StringBuilder dataDisk = new StringBuilder();
            ArrayList<JSONObject> dataDiskJson = Lists.newArrayList();
            resVds.forEach(vd -> {
                if ("01".equals(vd.getStoragePurpose())) {
                    config.add(createMap("系统盘", vd.getAllocateDiskSize() + " GiB " + vd.getVolumeTypeName()));
                    JSONObject sysDisk = new JSONObject();
                    sysDisk.put("chargeItemCategory", "blockStorage");
                    sysDisk.put("category", vd.getVolumeTypeUuid());
                    sysDisk.put("size", vd.getAllocateDiskSize());
                    data.put("systemDisk", sysDisk);
                }
                if ("02".equals(vd.getStoragePurpose())) {
                    dataDisk.append(", " + vd.getAllocateDiskSize() + " GiB " + vd.getVolumeTypeName());
                    JSONObject temp = new JSONObject();
                    temp.put("chargeItemCategory", "blockStorage");
                    temp.put("category", vd.getVolumeTypeUuid());
                    temp.put("size", vd.getAllocateDiskSize());
                    dataDiskJson.add(temp);
                }
            });
            if (dataDisk.length() > 0) {
                dataDisk.delete(0, 1);
            }
            config.add(createMap("数据盘", dataDisk.toString()));
        }
        returnData.put("serviceId", sfServiceCatalogRelationMapper.selectServiceIdByTarget(vm.getId()));
        QueryResFloatingIpByParamsRequest queryResFloatingIpByParamsRequest = new QueryResFloatingIpByParamsRequest();
        queryResFloatingIpByParamsRequest.setInstanceId(vm.getId());
        List<ResFloatingIp> floatingIps
                = resFloatingIpRemoteService.selectByParams(queryResFloatingIpByParamsRequest);
        if (CollectionUtil.isNotEmpty(floatingIps)) {
            ResFloatingIp resFloatingIp = floatingIps.get(0);
            JSONObject network = new JSONObject();
            network.put("chargeItemCategory", "network");
            if ("mov per".equals(resFloatingIp.getInternetChargeType())) {
                network.put("category", "5_bgp");
                network.put("internetChargeType", "5_bgp");
            } else if ("sta per".equals(resFloatingIp.getInternetChargeType())) {
                network.put("category", "5_sbgp");
                network.put("internetChargeType", "5_sbgp");
            } else {
                network.put("category", resFloatingIp.getInternetChargeType());
                network.put("internetChargeType", resFloatingIp.getInternetChargeType());
            }
            network.put("internetMaxBandwidthOut", resFloatingIp.getBandWidth());
        }

        inquiryJson.put("data", data);
        returnData.put("inquiryJson", inquiryJson.toString());

        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    @Override
    public Map<String, Object> select4Vd(String id) {
        HashMap<String, Object> returnData = new HashMap<>();
        if (Strings.isNullOrEmpty(id)) {
            return returnData;
        }
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(id);
        if (Objects.isNull(resVd)) {
            return returnData;
        }
        ArrayList<Map<String, String>> config = Lists.newArrayList();
        returnData.put("productCode", ProductCodeEnum.DISK.getProductType());
        returnData.put("chargeType", resVd.getChargeType());
        returnData.put("size", resVd.getAllocateDiskSize());
        // 询价参数
        JSONObject inquiryJson = new JSONObject();
        inquiryJson.put("productCode", ProductCodeEnum.DISK.getProductType());
        inquiryJson.put("chargeType", resVd.getChargeType());
        inquiryJson.put("cloudEnvId", resVd.getCloudEnvId());
        inquiryJson.put("amount", 1);

        JSONObject data = new JSONObject();
        JSONObject dataDisk = new JSONObject();
        dataDisk.put("chargeItemCategory", "blockStorage");
        dataDisk.put("category", resVd.getVolumeTypeUuid());
        dataDisk.put("size", resVd.getAllocateDiskSize());
        JSONArray array = new JSONArray();
        array.add(dataDisk);
        data.put("dataDisks", array);
        returnData.put("instanceType", resVd.getVolumeTypeUuid());
        returnData.put("size", resVd.getAllocateDiskSize());
        returnData.put("serviceId", sfServiceCatalogRelationMapper.selectServiceIdByTarget(resVd.getResVdSid()));
        config.add(createMap("ID", resVd.getResVdSid()));
        config.add(createMap("名称", resVd.getVdName()));
        config.add(createMap("资源ID", resVd.getUuid()));
        returnData.put("resourceId", resVd.getUuid());
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(resVd.getChargeType())));
        config.add(createMap("区域", resVd.getZoneName()));
        config.add(createMap("云环境", resVd.getCloudEnvName()));
        config.add(createMap("硬盘类型", resVd.getVolumeTypeName()));
        config.add(createMap("大小", resVd.getAllocateDiskSize().toString() + " GB"));
        returnData.put("cloudEnvId", resVd.getCloudEnvId().toString());
        if (Objects.nonNull(resVd.getEndTime())) {
            returnData.put("endTime", DateUtil.format(resVd.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(resVd.getStartTime())) {
            returnData.put("startTime", DateUtil.format(resVd.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        inquiryJson.put("data", data);
        returnData.put("inquiryJson", inquiryJson.toString());
        CloudEnv cloudEnv =  cloudEnvRemoteService.selectByPrimaryKey(resVd.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    @Override
    public Map<String, Object> select4VBSVD(String id) {
        HashMap<String, Object> returnData = new HashMap<>();
        if (Strings.isNullOrEmpty(id)) {
            return returnData;
        }
        ResSnapshot resSnapshot = resSnapshotRemoteService.selectByPrimaryKey(Long.valueOf(id));
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(resSnapshot.getResVdId());
        if (Objects.isNull(resSnapshot)) {
            return returnData;
        }
        Integer size = (int) (resSnapshot.getSnapshotSize() / 1);
        ArrayList<Map<String, String>> config = Lists.newArrayList();
        returnData.put("productCode", ProductCodeEnum.VBS_VD.getProductType());
        returnData.put("chargeType", resSnapshot.getChargeType());
        returnData.put("size", size);
        // 询价参数
        JSONObject inquiryJson = new JSONObject();
        inquiryJson.put("productCode", ProductCodeEnum.VBS_VD.getProductType());
        inquiryJson.put("chargeType", resSnapshot.getChargeType());
        inquiryJson.put("cloudEnvId", resSnapshot.getCloudEnvId());
        inquiryJson.put("amount", 1);

        JSONObject data = new JSONObject();
        JSONObject dataDisk = new JSONObject();
        dataDisk.put("chargeItemCategory", "blockStorage");
        dataDisk.put("category", resSnapshot.getSnapshotType());
        dataDisk.put("size", size);
        JSONArray array = new JSONArray();
        array.add(dataDisk);
        data.put("dataDisks", array);
        returnData.put("instanceType", resSnapshot.getSnapshotType());
        returnData.put("serviceId", sfServiceCatalogRelationMapper.selectServiceIdByTarget(resSnapshot.getId().toString()));
        config.add(createMap("ID", resSnapshot.getId().toString()));
        config.add(createMap("名称", resSnapshot.getName()));
        config.add(createMap("资源ID", resSnapshot.getUuid()));
        returnData.put("resourceId", resSnapshot.getUuid());
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(resSnapshot.getChargeType())));
        config.add(createMap("大小", size.toString() + " GB"));
        config.add(createMap("资源类型", "硬盘(" + resVd.getVdName() + ")"));
        returnData.put("cloudEnvId", resSnapshot.getCloudEnvId().toString());
        if (Objects.nonNull(resSnapshot.getEndTime())) {
            returnData.put("endTime", DateUtil.format(resSnapshot.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(resSnapshot.getStartTime())) {
            returnData.put("startTime", DateUtil.format(resSnapshot.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        inquiryJson.put("data", data);
        returnData.put("inquiryJson", inquiryJson.toString());
        CloudEnv cloudEnv =  cloudEnvRemoteService.selectByPrimaryKey(resSnapshot.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    @Override
    public Map<String, Object> select4RDS(String id) {
        HashMap<String, Object> returnData = new HashMap<>();
        if (StrUtil.isEmpty(id)) {
            return returnData;
        }
        ResRds rds = resRdsRemoteService.selectByPrimaryKey(Long.valueOf(id));
        if (rds == null) {
            return returnData;
        }

        ArrayList<Map<String, String>> config = Lists.newArrayList();
        returnData.put("productCode", ProductCodeEnum.RDS.getProductType());
        returnData.put("chargeType", rds.getPayType());

        // 询价参数
        JSONObject inquiryJson = new JSONObject();
        inquiryJson.put("productCode", ProductCodeEnum.RDS.getProductType());
        inquiryJson.put("chargeType", rds.getPayType());
        inquiryJson.put("cloudEnvId", rds.getCloudEnvId());
        inquiryJson.put("regionId", rds.getRegion());
        inquiryJson.put("amount", 1);

        JSONObject data = new JSONObject();

        JSONObject instance = new JSONObject(rds);
        instance.put("chargeItemCategory", "db");
        instance.put("zoneId", rds.getZoneId());
        data.put("rds", instance);
        returnData.put("id", rds.getId());
        returnData.put("resourceId", rds.getUuid());
        config.add(createMap("ID", rds.getId().toString()));
        config.add(createMap("名称", rds.getDescription()));
        config.add(createMap("资源ID", rds.getUuid()));
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(rds.getPayType())));
        config.add(createMap("数据库类型", rds.getEngine()));
        config.add(createMap("版本", rds.getEngineVersion()));
        config.add(createMap("实例类型", rds.getCategory()));
        config.add(createMap("实例规格", String.format("%s核 %sGB", rds.getCpu(),
            rds.getMemory() / 1024)));
        config.add(createMap("存储类型", rds.getStorageType()));
        config.add(createMap("存储空间", Convert.toStr(rds.getStorage())));
        config.add(createMap("私有网络", rds.getResVpcName()));
        config.add(createMap("安全组/交换机", rds.getResSecurityGroupName()));
        if (Objects.nonNull(rds.getExpireTime())) {
            returnData.put("endTime", DateUtil.format(rds.getExpireTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(rds.getCreationTime())) {
            returnData.put("startTime", DateUtil.format(rds.getCreationTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        CloudEnv cloudEnv =  cloudEnvRemoteService.selectByPrimaryKey(rds.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        config.add(createMap("云环境", cloudEnv.getCloudEnvName()));
        returnData.put("cloudEnvId", cloudEnv.getId());

        inquiryJson.put("data", data);
        returnData.put("inquiryJson", inquiryJson.toString());

        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    @Override
    public Map<String, Object> select4EIP(String id) {
        Map<String, Object> returnData = new HashMap<>();
        if (Strings.isNullOrEmpty(id)) {
            return returnData;
        }
        ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(Long.parseLong(id));
        if (Objects.isNull(resFloatingIp)) {
            return returnData;
        }
        returnData.put("productCode", ProductCodeEnum.FLOATING_IP.getProductType());
        returnData.put("chargeType", resFloatingIp.getChargeType());
        returnData.put("instanceType", resFloatingIp.getInternetChargeType());
        returnData.put("size", resFloatingIp.getBandWidth());
        // 询价参数
        JSONObject inquiryJson = new JSONObject();
        ArrayList<Map<String, String>> config = Lists.newArrayList();
        inquiryJson.put("productCode", ProductCodeEnum.FLOATING_IP.getProductType());
        inquiryJson.put("chargeType", resFloatingIp.getChargeType());
        inquiryJson.put("cloudEnvId", resFloatingIp.getEnvId());
        inquiryJson.put("amount", 1);

        JSONObject data = new JSONObject();

        JSONObject network = new JSONObject();
        network.put("chargeItemCategory", "network");
        network.put("internetMaxBandwidthOut", resFloatingIp.getBandWidth());
        returnData.put("serviceId", sfServiceCatalogRelationMapper.selectServiceIdByTarget(resFloatingIp.getId().toString()));
        returnData.put("bandwidth", resFloatingIp.getBandWidth());
        config.add(createMap("ID", resFloatingIp.getId().toString()));
        config.add(createMap("IP", resFloatingIp.getIp()));
        config.add(createMap("资源ID", resFloatingIp.getUuid()));
        returnData.put("resourceId", resFloatingIp.getUuid());
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(resFloatingIp.getChargeType())));
        config.add(createMap("云环境", resFloatingIp.getCloudEnvName()));
        config.add(createMap("带宽", Objects.isNull(resFloatingIp.getBandWidth())
                ? "" : (resFloatingIp.getBandWidth().toString()) + "Mbps"));
        returnData.put("cloudEnvId", resFloatingIp.getEnvId().toString());
        if (Objects.nonNull(resFloatingIp.getEndTime())) {
            returnData.put("endTime", DateUtil.format(resFloatingIp.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(resFloatingIp.getStartTime())) {
            returnData.put("startTime", DateUtil.format(resFloatingIp.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (!Strings.isNullOrEmpty(resFloatingIp.getInternetChargeType())) {
            if ("mov per".equals(resFloatingIp.getInternetChargeType())) {
                config.add(createMap("网络类型", "全动态BGP"));
                config.add(createMap("带宽类型", "独享带宽"));
                network.put("category", "5_bgp");
                network.put("internetChargeType", "5_bgp");
            } else if ("sta per".equals(resFloatingIp.getInternetChargeType())) {
                config.add(createMap("网络类型", "全静态BGP"));
                config.add(createMap("带宽类型", "独享带宽"));
                network.put("category", "5_sbgp");
                network.put("internetChargeType", "5_sbgp");
            } else {
                network.put("category", resFloatingIp.getInternetChargeType());
                network.put("internetChargeType", resFloatingIp.getInternetChargeType());
            }
        }
        data.put("network", network);
        inquiryJson.put("data", data);
        returnData.put("inquiryJson", inquiryJson.toString());
        CloudEnv cloudEnv =  cloudEnvRemoteService.selectByPrimaryKey(resFloatingIp.getEnvId());
        returnData.put("cloudEnvId", cloudEnv.getId().toString());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    @Override
    public Map<String, Object> select4SFS(String id) {
        HashMap<String, Object> returnData = new HashMap<>();
        if (Strings.isNullOrEmpty(id)) {
            return returnData;
        }
        ResShare resShare = shareRemoteService.selectByPrimaryKey(Convert.toLong(id));
        if (Objects.isNull(resShare) || !BillingConstants.ChargeType.PRE_PAID.equals(resShare.getChargeType())) {
            return returnData;
        }
        String supportClusterType = resShare.getSupportClusterType();
        if (ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(supportClusterType) && resShare.getIsClusterDefault()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1449425777));
        }

        ArrayList<Map<String, String>> config = Lists.newArrayList();
        returnData.put("productCode", ProductCodeEnum.SFS2.getProductType());
        returnData.put("chargeType", resShare.getChargeType());

        returnData.put("id", resShare.getId());
        returnData.put("resourceId", resShare.getUuid());
        returnData.put("size", resShare.getSize());
        returnData.put("usedSize", resShare.getUsedSize());
        returnData.put("name", resShare.getName());
        returnData.put("status", resShare.getStatus());
        DateTime now = DateUtil.date();
        DateTime endTime = DateUtil.date(resShare.getEndTime());
        if (endTime.before(now)) {
            DateTime date;
            if (ShareStatus.FROZEN.equals(resShare.getStatus())) {
                DateTime endTimeBeforeRenew = DateTime.of(resShare.getEndTime());
                Integer offDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(resShare.getEndTime(), now);
                Integer makeUpOffDay = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffDay(resShare.getEndTime(),resShare.getFreezingTime());
                if(makeUpOffDay>=offDay){
                    date = endTimeBeforeRenew.offsetNew(DateField.HOUR,makeUpOffDay*24);
                }else{
                    date = now;
                }
            }else {
                date = DateUtil.dateNew(now);
            date.setField(DateField.HOUR_OF_DAY, endTime.getField(DateField.HOUR_OF_DAY));
            date.setField(DateField.MINUTE, endTime.getField(DateField.MINUTE));
            date.setField(DateField.SECOND, endTime.getField(DateField.SECOND));
            date.setField(DateField.MILLISECOND, endTime.getField(DateField.MILLISECOND));
            while (date.before(now)) {
                date = DateUtil.offsetDay(date, 1);
            }
            }
            returnData.put("now", DateUtil.format(date, "yyyy年MM月dd日 HH:mm:ss"));
            Integer offDay = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.calculateOffDay(resShare.getEndTime(),
                                                                                               "frozen".equalsIgnoreCase(
                                                                                                       resShare.getStatus())
                                                                                                       ? resShare.getFreezingTime()
                                                                                                       : date);
            returnData.put("makeUpDays", offDay + "天");
        }
        config.add(createMap("名称", resShare.getName()));
        config.add(createMap("资源ID", resShare.getUuid()));
        config.add(createMap("计费类型", ChargeTypeEnum.typeFromName(resShare.getChargeType())));
        config.add(createMap("协议类型", resShare.getShareProto()));
        config.add(createMap("存储类型", resShare.getShareType()));
        if(resShare.getUsedSize()!=null) {
        config.add(createMap("已使用(GB)", NumberUtil.decimalFormat("0.00", resShare.getUsedSize().doubleValue())));
        }
        else {
            config.add(createMap("已使用(GB)", NumberUtil.decimalFormat("0.00", BigDecimal.ZERO.doubleValue())));
        }
        config.add(createMap("容量(GB)", NumberUtil.decimalFormat("#.00", resShare.getSize())));
        if (Objects.nonNull(resShare.getEndTime())) {
            returnData.put("endTime", DateUtil.format(resShare.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(resShare.getStartTime())) {
            returnData.put("startTime", DateUtil.format(resShare.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        if (Objects.nonNull(resShare.getFreezingTime())) {
            returnData.put("frozenTime", DateUtil.format(resShare.getFreezingTime(), "yyyy年MM月dd日 HH:mm:ss"));
        }
        CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resShare.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2129638482));
        }
        returnData.put("cloudEnvId", cloudEnv.getId());
        ServiceOrderDetail orderDetail = getSfsApplyOrderDetail(id);
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject sfs = data.getJSONObject(ProductCodeEnum.SFS2.getProductType());
            sfs.put("size", resShare.getSize());
            serviceConfig.put("data", data);
            serviceConfig.put("serviceId", serviceConfig.getStr("serviceId"));
            returnData.put("inquiryJson", serviceConfig.toString());
            returnData.put("serviceId", orderDetail.getServiceId());
            returnData.put("projectId", orderDetail.getOrgSid());
        }
        if ("HCSO".equals(cloudEnv.getCloudEnvType())) {
            returnData.put("isPublic", "false");
        } else {
            returnData.put("isPublic", String.valueOf(CloudEnvType.from(cloudEnv.getCloudEnvType()).isPublic()));
        }
        returnData.put("config", config);
        return returnData;
    }

    private ServiceOrderDetail getSfsApplyOrderDetail(String id) {
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
            Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                .eq(ServiceOrderResourceRef::getType, ProductCodeEnum.SFS2.getProductType())
                .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    private ServiceOrderDetail getSfsTurboApplyOrderDetail(String id) {
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getType, ProductCodeEnum.SFS_TURBO.getProductType())
                        .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    private ServiceOrderDetail getRdsApplyOrderDetail(String id) {
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getType, ProductCodeEnum.DISK.getProductType())
                        .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    /**
     * 查订单详情
     * @param id resourceId
     * @param code code
     * @return
     */
    private ServiceOrderDetail getApplyOrderDetail(String id,String code) {
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getType,code)
                        .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    @Override
    public JSONArray selectSfsChange(String id, ProductInfo productInfo) {
        JSONArray array = new JSONArray();
        ResShare resShare = shareRemoteService.selectByPrimaryKey(Long.parseLong(id));
        if (Objects.isNull(resShare)) {
            return array;
        }
        if (Objects.nonNull(productInfo.getTargetSize())) {
            if (!Objects.equals(productInfo.getTargetSize(), resShare.getSize())) {
                String size = Convert.toStr(resShare.getSize(), "0");
                array.add(this.createMap4Change("容量(GB)", size,
                    Convert.toStr(productInfo.getTargetSize())));
            }
        }
        return array;
    }

    @Override
    public JSONArray selectDrpChange(ProductInfo productInfo) {
        SfProductResource sfProductResource = sfProductResourceService.getById(productInfo.getResourceId());
        RestResult restResult = resMaPoolService.getResMaPoolAvailableCount(sfProductResource.getClusterId());
        Integer availableCount = Integer.parseInt(restResult.getData().toString());
        JSONArray array = new JSONArray();
        if (Objects.nonNull(productInfo.getTargetSize())) {
            if (!Objects.equals(productInfo.getTargetSize(), availableCount)) {
                String size = Convert.toStr(availableCount, "0");
                array.add(this.createMap4Change("节点数量", size,
                        Convert.toStr(productInfo.getTargetSize())));
            }
        }
        return array;
    }

    @Override
    public JSONArray selectEbsChange(String id, ProductInfo productInfo) {
        JSONArray array = new JSONArray();
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(id);
        if (Objects.isNull(resVd)) {
            return array;
        }
        if (Objects.nonNull(productInfo.getTargetSize())) {
            if (!Objects.equals(productInfo.getTargetSize(), resVd.getSize())) {
                String size = Convert.toStr(resVd.getSize(), "0");
                array.add(this.createMap4Change("容量(GB)", size,
                                                Convert.toStr(productInfo.getTargetSize())));
            }
        }
        return array;
    }

    @Override
    public String updateCurrentSpec(String serviceConfig, String productType, Long resourceId) {
        JSONObject serviceConfigObj = JSONUtil.parseObj(serviceConfig);
        if (ProductCodeEnum.RS_BMS.getProductType().equals(productType)) {
            serviceConfigObj.set("productResourceId", resourceId);
            return serviceConfigObj.toString();
        }
        JSONObject data = serviceConfigObj.getJSONObject("data");
        if (Strings.isNullOrEmpty(productType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        JSONObject jsonObject = data.getJSONObject(productType);
        if (Objects.isNull(jsonObject)) {
            return serviceConfig;
        }
        String spec = "";
        switch (productType) {
            case "ECS":
                spec = this.getCurrentVmSpec(resourceId);
                break;
            case "CCE":
                spec = this.getCurrentCceSpec(resourceId);
                break;
            case "DCS":
                spec = getCurrentDcsSpec(resourceId);
        }
        if (StringUtils.isBlank(spec)) {
            return serviceConfig;
        }

        jsonObject.set("category", spec);
        jsonObject.set("spec", spec);
        data.set(productType, jsonObject);
        return serviceConfigObj.toString();
    }

    @Override
    public List<ResourceDetailVO> generateResourceDtail(String id) {
        ArrayList<ResourceDetailVO> list = Lists.newArrayList();
        ResAllDTO resAllDTO = resAllRemoteService.selectSimpleByPrimaryKey(id);
        if (Objects.isNull(resAllDTO)) {
            return list;
        }
        //填充其他信息
        RenewalDTO renewalDTO = resRenewRefMapper.selectSimpleByPrimaryKey(resAllDTO.getId(),resAllDTO.getProductType());
        BeanUtil.copyProperties(resAllDTO, renewalDTO,
                                CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        ResourceDetailVO detailVO = new ResourceDetailVO();
        detailVO.init();
        detailVO.setId(renewalDTO.getId());
        detailVO.setResourceId(renewalDTO.getResourceId());
        detailVO.setChargeType(renewalDTO.getChargeType());
        detailVO.setStatus(renewalDTO.getStatus());
        detailVO.setProductCode(renewalDTO.getProductType());
        list.add(detailVO);
        if (ProductCodeEnum.ECS.getProductType().equalsIgnoreCase(renewalDTO.getProductType())) {
            selectComponent4Vm(renewalDTO, list);
        }
        return list;
    }

    @Override
    public JSONArray selectVmChange(String id, String target) {
        JSONArray array = new JSONArray();
        ResVm vm = resVmRemoteService.selectByPrimaryKey(id);
        if (Objects.isNull(vm)) {
            return array;
        }
        QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
        queryResVmTypeByParamsRequest.setUuid(vm.getInstanceType());
        ResVmType oldType = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
        queryResVmTypeByParamsRequest.setUuid(target);
        ResVmType newType = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);

        if (Objects.isNull(oldType) || Objects.isNull(newType)) {
            return array;
        }
        if (!oldType.getCpu().equals(newType.getCpu())) {
            array.add(this.createMap4Change("CPU", oldType.getCpu() + "核", newType.getCpu() + "核"));
        }
        if (!oldType.getRam().equals(newType.getRam())) {
            array.add(this.createMap4Change("内存", oldType.getRam() / 1024 + "GB", newType.getRam() / 1024 + "GB"));
        }
        return array;
    }

    @Override
    public JSONArray selectDcsChange(String id, String target) {
        JSONArray array = new JSONArray();
//        ResDcs vm = resDcsRemoteService.selectByPrimaryKey(Long.parseLong(id));
//        if (Objects.isNull(vm)) {
//            return array;
//        }
//        QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
//        queryResVmTypeByParamsRequest.setUuid(vm.getInstanceType());
//        ResVmType oldType = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
//        queryResVmTypeByParamsRequest.setUuid(target);
//        ResVmType newType = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
//
//        if (Objects.isNull(oldType) || Objects.isNull(newType)) {
//            return array;
//        }
//        if (!oldType.getCpu().equals(newType.getCpu())) {
//            array.add(this.createMap4Change("CPU", oldType.getCpu() + "核", newType.getCpu() + "核"));
//        }
//        if (!oldType.getRam().equals(newType.getRam())) {
//            array.add(this.createMap4Change("内存", oldType.getRam() / 1024 + "GB", newType.getRam() / 1024 + "GB"));
//        }
        return array;
    }

    @Override
    public String selectVdChange(String id, String target) {
        return null;
    }

    @Override
    public JSONArray selectEIPChange(String id, ProductInfo productInfo) {
        JSONArray array = new JSONArray();
        ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(Long.parseLong(id));
        if (Objects.isNull(resFloatingIp)) {
            return array;
        }
        if (Objects.nonNull(productInfo.getTargetSize())) {
            if (!Objects.equals(String.valueOf(productInfo.getTargetSize()), resFloatingIp.getBandWidth())) {
                long bandwidth = Objects.isNull(resFloatingIp.getBandWidth()) ? 0 : Long.parseLong(resFloatingIp.getBandWidth());
                array.add(this.createMap4Change("带宽", bandwidth + "Mbps", productInfo.getTargetSize() + "Mbps"));
            }
        }
        return array;
    }


    @Override
    public ProductInfoVO generateModifyInquiryBody(ModifyInquiryPriceRequest request) {
        String type;
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.ECS.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.FLOATING_IP.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.DISK.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.SFS_TURBO.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.RDS.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.DCS.getProductType().equals(request.getTargetType())
                || ProductCodeEnum.CCE.getProductType().equals(request.getTargetType())) {
            type = request.getTargetType();
        } else {
            type = resAllRemoteService.selectProductTypeById(request.getId());
        }
        System.out.println("generateModifyInquiryBody:" + type);
        if (Strings.isNullOrEmpty(type)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        switch (type) {
            case "ECS":
                return this.generateModifyVmInquiryBody(request);
            case "EIP":
                return this.generateModifyEIPInquiryBody(request);
            case "EBS":
                return this.generateModifyVdInquiryBody(request);
            case "RDS":
                return this.generateModifyRdsInquiryBody(request);
            case "SFS2.0":
                return this.generateModifySfsInquiryBody(request);
            case "SFS_TURBO":
                return this.generateModifySfsTurboInquiryBody(request);
            case "DRP":
                return this.generateModifyDrpInquiryBody(request);
            case "CCE":
                return this.generateModifyCceInquiryBody(request);
            case "DCS":
                return this.generateModifyDcsInquiryBody(request);
            default:
                return new ProductInfoVO();
        }
    }

    private ProductInfoVO generateModifyDrpInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        SfProductResource sfProductResource = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(sfProductResource)) {
            return productInfoVO;
        }
        productInfoVO.setProductCode(sfProductResource.getProductType());
        productInfoVO.setCloudEnvId(sfProductResource.getCloudEnvId());
        productInfoVO.setPeriod(BigDecimal.valueOf(1));
        ServiceOrderDetail orderDetail = getDrpApplyOrderDetail(request.getId());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            productInfoVO.setData(data.toString());
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setServiceId(orderDetail.getServiceId());
            productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));

            productInfoVO.setAmount(request.getTargetSize());
        }
        return productInfoVO;
    }

    private ServiceOrderDetail getEcsApplyOrderDetail(String id) {
        List<String> collect = Stream.of(ProductCodeEnum.ECS.getProductType(),
                                         ProductCodeEnum.CCE.getProductType(),
                                         ProductCodeEnum.DCS.getProductType()
                ).collect(Collectors.toList());
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .in(ServiceOrderResourceRef::getType, collect)
                        .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    private ServiceOrderDetail getDrpApplyOrderDetail(String id) {
        ServiceOrderResourceRef orderResourceRef = serviceOrderResourceRefService.getOne(
                Wrappers.<ServiceOrderResourceRef>lambdaQuery()
                        .eq(ServiceOrderResourceRef::getType, ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductType())
                        .eq(ServiceOrderResourceRef::getResourceId, id), false);
        if (Objects.nonNull(orderResourceRef)) {
            return serviceOrderDetailService.getById(orderResourceRef.getOrderDetailId());
        }
        return null;
    }

    private ProductInfoVO generateModifySfsInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        ResShare resShare = shareRemoteService.selectByPrimaryKey(Convert.toLong(request.getId()));
        if (Objects.isNull(resShare)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.SFS2.getProductType());
        productInfoVO.setCloudEnvId(resShare.getCloudEnvId());
        productInfoVO.setChargeType(resShare.getChargeType());
        productInfoVO.setPeriod(BigDecimal.valueOf(1));
        ServiceOrderDetail orderDetail = getSfsApplyOrderDetail(request.getId());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject sfs = data.getJSONObject(productInfoVO.getProductCode());
            sfs.put("size", request.getTargetSize());
            productInfoVO.setData(data.toString());
            productInfoVO.setServiceId(orderDetail.getServiceId());
            productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));
        }
        return productInfoVO;
    }

    private ProductInfoVO generateModifySfsTurboInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        ServiceOrderDetail orderDetail = getSfsTurboApplyOrderDetail(request.getId());


        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.SFS_TURBO.getProductType());
        if (Objects.isNull(orderDetail) || StrUtil.isBlank(orderDetail.getServiceConfig())) {
            log.error("解析云环境id失败resourceId:{}", request.getId());
            return productInfoVO;
        }
        productInfoVO.setCloudEnvId(JSONUtil.parseObj(orderDetail.getServiceConfig()).getLong("cloudEnvId"));
        productInfoVO.setChargeType("PrePaid");
        productInfoVO.setPeriod(BigDecimal.valueOf(1));

        JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
        JSONObject data = serviceConfig.getJSONObject("data");
        JSONObject sfs = data.getJSONObject(productInfoVO.getProductCode());
        sfs.set("size", request.getTargetSize());
        productInfoVO.setData(data.toString());
        productInfoVO.setServiceId(orderDetail.getServiceId());
        productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));

        return productInfoVO;
    }

    private ProductInfoVO generateModifyDcsInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        SfProductResource prodcut = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResDcs dcs = resDcsRemoteService.selectByPrimaryKey(prodcut.getClusterId());
        if (Objects.isNull(dcs)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.DCS.getProductType());
        productInfoVO.setCloudEnvId(dcs.getCloudEnvId());
//        productInfoVO.setRegionId(dcs.getRegion());
        productInfoVO.setOldPrice(
                BigDecimal.valueOf(bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(dcs.getSpecCode())));
        Integer unitMonthPriceByConfig = bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(request.getTargetSpec());
        if (Objects.isNull(unitMonthPriceByConfig)) {
            throw new BizException(WebUtil.getMessage(WebUtil.getMessage(MsgCd.SPECIFICATIONS_DO_NOT_MATCH_PRICES,
                                                                         new String[]{request.getTargetSpec()})));
        }
        productInfoVO.setTargetPrice(BigDecimal.valueOf(unitMonthPriceByConfig));
        ServiceOrderDetail orderDetail = getEcsApplyOrderDetail(request.getId());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject jsonObject = data.getJSONObject(ProductCodeEnum.DCS.getProductType());
            jsonObject.set("category", request.getTargetSpec());
            jsonObject.set("spec", request.getTargetSpec());
            data.set(ProductCodeEnum.DCS.getProductType(), jsonObject);
            productInfoVO.setData(data.toString());
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setServiceId(orderDetail.getServiceId());
        }
        return productInfoVO;
    }

    private ProductInfoVO generateModifyVmInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        SfProductResource prodcut = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResVm vm = resVmRemoteService.selectByPrimaryKey(String.valueOf(prodcut.getClusterId()));
        if (Objects.isNull(vm)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.ECS.getProductType());
        productInfoVO.setCloudEnvId(vm.getCloudEnvId());
        productInfoVO.setRegionId(vm.getRegion());
        productInfoVO.setOldPrice(
                BigDecimal.valueOf(bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(vm.getVmTypeUuid())));
        Integer unitMonthPriceByConfig = bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(
                request.getTargetSpec());
        if (Objects.isNull(unitMonthPriceByConfig)) {
            throw new BizException(WebUtil.getMessage(WebUtil.getMessage(MsgCd.SPECIFICATIONS_DO_NOT_MATCH_PRICES,
                                                                         new String[]{request.getTargetSpec()})));
        }
        productInfoVO.setTargetPrice(BigDecimal.valueOf(unitMonthPriceByConfig));
        ServiceOrderDetail orderDetail = getEcsApplyOrderDetail(request.getId());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject jsonObject = data.getJSONObject(ProductCodeEnum.ECS.getProductType());
            jsonObject.set("category", request.getTargetSpec());
            jsonObject.set("spec", request.getTargetSpec());
            data.set(ProductCodeEnum.ECS.getProductType(), jsonObject);
            productInfoVO.setData(data.toString());
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setServiceId(orderDetail.getServiceId());
        }
        return productInfoVO;
    }

    private ProductInfoVO generateModifyCceInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        SfProductResource prodcut = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResContainerCluster containerCluster = cceRemoteService.selectByPrimaryKey(prodcut.getClusterId());
        if (Objects.isNull(containerCluster)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.CCE.getProductType());
        productInfoVO.setCloudEnvId(containerCluster.getCloudEnvId());
//        productInfoVO.setOldPrice(
//                BigDecimal.valueOf(bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(containerCluster.getFlavor())));
//        Integer unitMonthPriceByConfig = bizBillingTariffSpecChargeMapper.getUnitMonthPriceByConfig(
//                request.getTargetSpec());
//        if (Objects.isNull(unitMonthPriceByConfig)) {
//            throw new BizException(WebUtil.getMessage(WebUtil.getMessage(MsgCd.SPECIFICATIONS_DO_NOT_MATCH_PRICES,
//                    new String[]{request.getTargetSpec()})));
//        }
//        productInfoVO.setTargetPrice(BigDecimal.valueOf(unitMonthPriceByConfig));
        ServiceOrderDetail orderDetail = getEcsApplyOrderDetail(request.getId());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject jsonObject = data.getJSONObject(ProductCodeEnum.CCE.getProductType());
            jsonObject.set("category", request.getTargetSpec());
            jsonObject.set("spec", request.getTargetSpec());
            data.set(ProductCodeEnum.CCE.getProductType(), jsonObject);
            productInfoVO.setData(data.toString());
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setServiceId(orderDetail.getServiceId());
        }
        return productInfoVO;
    }

    private ProductInfoVO generateModifyVdInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        if (Strings.isNullOrEmpty(request.getId())) {
            return productInfoVO;
        }
        SfProductResource prodcut = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(String.valueOf(prodcut.getClusterId()));
        if (Objects.isNull(resVd)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.DISK.getProductType());
        productInfoVO.setCloudEnvId(resVd.getCloudEnvId());
        productInfoVO.setChargeType(resVd.getChargeType());
        productInfoVO.setPeriod(BigDecimal.ONE);

        ServiceOrderDetail orderDetail = getApplyOrderDetail(request.getId(),ProductCodeEnum.DISK.getProductType());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject jsonObject = data.getJSONObject(ProductCodeEnum.DISK.getProductType());
            jsonObject.set("category", request.getTargetSpec());
            jsonObject.set("spec", request.getTargetSpec());
            jsonObject.put("size", request.getTargetSize());
            data.set(ProductCodeEnum.DISK.getProductType(), jsonObject);
            productInfoVO.setData(data.toString());
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setServiceId(orderDetail.getServiceId());
            productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));
            productInfoVO.setSize(resVd.getSize());
        }
        return productInfoVO;
    }

    /**
     * EIP询价
     * @param request
     * @return
     */

    private ProductInfoVO generateModifyEIPInquiryBody(ModifyInquiryPriceRequest request) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        SfProductResource prodcut = sfProductResourceService.getById(request.getId());
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(prodcut.getClusterId());
        if (Objects.isNull(resFloatingIp)) {
            return productInfoVO;
        }
        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.FLOATING_IP.getProductType());
        productInfoVO.setCloudEnvId(resFloatingIp.getCloudEnvId());
        productInfoVO.setPeriod(BigDecimal.valueOf(1));
        ServiceOrderDetail orderDetail = getApplyOrderDetail(request.getId(),ProductCodeEnum.FLOATING_IP.getProductType());
        if (Objects.nonNull(orderDetail)) {
            JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
            JSONObject data = serviceConfig.getJSONObject("data");
            JSONObject eip = data.getJSONObject(productInfoVO.getProductCode());
            eip.put("bandwidth", request.getTargetSize());
            data.set(ProductCodeEnum.FLOATING_IP.getProductType(),eip);
            productInfoVO.setData(data.toString());
            productInfoVO.setServiceId(orderDetail.getServiceId());
            productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));
            productInfoVO.setChargeType(orderDetail.getChargeType());
            productInfoVO.setSize(Long.getLong(resFloatingIp.getBandWidth(),1));
        }
        return productInfoVO;
    }


    private ProductInfoVO generateModifyRdsInquiryBody(ModifyInquiryPriceRequest request) {
//        ProductInfoVO productInfoVO = new ProductInfoVO();
//        if (Strings.isNullOrEmpty(request.getId())) {
//            return productInfoVO;
//        }
//        ResRds resRds = resRdsRemoteService.selectByPrimaryKey(Convert.toLong(request.getId()));
//        if (Objects.isNull(resRds)) {
//            return productInfoVO;
//        }
//        productInfoVO.setAmount(1);
//        productInfoVO.setProductCode(ProductCodeEnum.RDS.getProductType());
//        productInfoVO.setCloudEnvId(resRds.getCloudEnvId());
//        productInfoVO.setChargeType(resRds.getPayType());
//        productInfoVO.setPeriod(BigDecimal.ONE);
//        JSONObject data = new JSONObject();
//        JSONObject rds = new JSONObject(resRds);
//        rds.put("chargeItemCategory", "db");
//        data.put("rds", rds);
//        productInfoVO.setData(data);
//        return productInfoVO;
        ProductInfoVO productInfoVO = new ProductInfoVO();
        ServiceOrderDetail orderDetail = getRdsApplyOrderDetail(request.getId());


        productInfoVO.setAmount(1);
        productInfoVO.setProductCode(ProductCodeEnum.DISK.getProductType());
        if (Objects.isNull(orderDetail) || StrUtil.isBlank(orderDetail.getServiceConfig())) {
            log.error("解析云环境id失败resourceId:{}", request.getId());
            return productInfoVO;
        }
        productInfoVO.setCloudEnvId(JSONUtil.parseObj(orderDetail.getServiceConfig()).getLong("cloudEnvId"));
        productInfoVO.setChargeType("PrePaid");
        productInfoVO.setPeriod(BigDecimal.valueOf(1));

        JSONObject serviceConfig = JSONUtil.parseObj(orderDetail.getServiceConfig());
        JSONObject data = serviceConfig.getJSONObject("data");
        JSONObject sfs = data.getJSONObject(productInfoVO.getProductCode());
        sfs.set("size", request.getTargetSize());
        productInfoVO.setData(data.toString());
        productInfoVO.setServiceId(orderDetail.getServiceId());
        productInfoVO.setPeriod(BigDecimal.valueOf(orderDetail.getDuration()));

        return productInfoVO;
    }


    private void selectComponent4Vm(RenewalDTO renewalDTO, ArrayList<ResourceDetailVO> list) {
        // 查询主机关联的弹性IP
//        fix:25453
        if (StringUtils.isNotBlank(renewalDTO.getResourceId())) {
            QueryResFloatingIpByParamsRequest queryResFloatingIpByParamsRequest = new QueryResFloatingIpByParamsRequest();
            queryResFloatingIpByParamsRequest.setInstanceId(renewalDTO.getResourceId());
            List<ResFloatingIp> resFloatingIps = resFloatingIpRemoteService.selectByParams(queryResFloatingIpByParamsRequest);
            if (CollectionUtil.isNotEmpty(resFloatingIps)) {
                resFloatingIps.forEach(ip -> {
                    ResourceDetailVO d = new ResourceDetailVO();
                    d.init();
                    d.setId(ip.getId().toString());
                    d.setResourceId(ip.getUuid());
                    d.setChargeType(ip.getChargeType());
                    d.setStatus(ip.getStatus());
                    list.add(d);
                });
            }
        }
        // 查询关联数据盘
        List<ResVd> resVds = resVdRemoteService.selectByHostId(renewalDTO.getId());
        if (CollectionUtil.isNotEmpty(resVds)) {
            resVds.forEach(vd -> {
                ResourceDetailVO d = new ResourceDetailVO();
                d.init();
                d.setId(vd.getResVdSid());
                d.setResourceId(vd.getUuid());
                d.setChargeType(vd.getChargeType());
                d.setStatus(vd.getStatus());
                list.add(d);
            });
        }
    }

    private HashMap<String, String> createMap(String label, String value) {
        HashMap<String, String> temp = new HashMap<>();
        temp.put("label", label);
        temp.put("value", value);
        return temp;
    }

    private JSONObject createMap4Change(String label, String oldValue, String newValue) {
        JSONObject temp = new JSONObject();
        temp.put("label", label);
        temp.put("oldValue", oldValue);
        temp.put("newValue", newValue);
        return temp;
    }

    private String tranForJsonObject(Object o) {
        if (Objects.isNull(o)) {
            return "";
        }
        if (JSONNull.NULL.equals(o)) {
            return "";
        }
        return o.toString();
    }

    private String getCurrentVmSpec(Long resourceId) {
        SfProductResource prodcut = sfProductResourceService.getById(resourceId);
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResVm vm = resVmRemoteService.selectByPrimaryKey(String.valueOf(prodcut.getClusterId()));
        if (Objects.isNull(vm)) {
            return "";
        }
        return vm.getVmTypeUuid();
    }

    private String getCurrentCceSpec(Long resourceId) {
        SfProductResource prodcut = sfProductResourceService.getById(resourceId);
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResContainerCluster containerCluster = cceRemoteService.selectByPrimaryKey(prodcut.getClusterId());
        if (Objects.isNull(containerCluster)) {
            return "";
        }
        return containerCluster.getFlavor();
    }

    private String getCurrentDcsSpec(Long resourceId) {
        SfProductResource prodcut = sfProductResourceService.getById(resourceId);
        if (Objects.isNull(prodcut)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_318480099));
        }
        ResDcs resDcs = resDcsRemoteService.selectByPrimaryKey(prodcut.getClusterId());
        if (Objects.isNull(resDcs)) {
            return "";
        }
        return resDcs.getSpecCode();
    }
}
