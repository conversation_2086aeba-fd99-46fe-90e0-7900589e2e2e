/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.entity;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-15
 */
@Data
public class ServiceOrder extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String orderSn;

    /**
     * 申请单名称
     */
    private String name;

    /**
     * 申请单类型
     */
    private String type;

    private Long orgSid;

    /**
     * 所有者ID
     */
    private String ownerId;

    private String extraAttr;


    private String resourceInfo;

    /**
     * 状态
     */
    @CCSPIntegralityHashAndVerify(segment = 1)
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    private BigDecimal totalAmt;

    private BigDecimal totalAmount;

    @CCSPIntegralityHashAndVerify(segment = 2,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal originalCost;

    private BigDecimal discount;

    private BigDecimal amountReceived;

    /**
     * 支付/开通时间
     */
    private Date payTime;

    private Date noticeDt;

    private String orderId;

    private String serviceId;

    /**
     * 01:项目用户申请；02:项目管理员申请; 03: 企业管理员申请
     */
    private String processFlag;

    /**
     * 流程步骤名称
     */
    private String stepName;

    /**
     * 流程编号
     */
    private String processCode;

    /**
     * 账户id
     */
    private Long bizBillingAccountId;

    /**
     * 平台优惠
     */
    @CCSPIntegralityHashAndVerify(segment = 3,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal orgDiscount;

    /**
     * 优惠券优惠
     */
    @CCSPIntegralityHashAndVerify(segment = 4,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal couponDiscount;

    /**
     * 最终交易价
     */
    @CCSPIntegralityHashAndVerify(segment = 5,defineBigDecimalScale = true,bigDecimalScale = 2)
    private BigDecimal finalCost;

    /**
     * 当前扣除金额
     */
    @CCSPIntegralityHashAndVerify(segment = 6,defineBigDecimalScale = true,bigDecimalScale = 2)
    private BigDecimal currAmount;

    /**
     * 底层平台订单id
     */
    private String platformOrderId;

    /**
     * 折扣系数
     */
    @TableField(exist = false)
    private BigDecimal discountRatio;

    /**
     * 上浮点数
     */
    @TableField(exist = false)
    private BigDecimal floatingRatio;

    /**
     * 资源价格
     */
    @TableField(exist = false)
    private BigDecimal resourcePrice;

    /**
     * 产品服务价格
     */
    @TableField(exist = false)
    private BigDecimal servicePrice;

    /**
     * 服务是否周期计费
     */
    @TableField(exist = false)
    private Integer cycle;

    /**
     * 单次服务价格
     */
    @TableField(exist = false)
    private BigDecimal oncePrice;

    /**
     * 购买数量
     */
    @TableField(exist = false)
    private Integer quantity;

    /**
     * 申请时长
     */
    @TableField(exist = false)
    private Integer duration;

    @TableField(exist = false)
    private List<ServiceOrderDetail> orderDetails = Lists.newArrayList();

    @TableField(exist = false)
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 客户名称
     */
    private String accountName;

    /**
     * 类别名称
     */
    @TableField(exist = false)
    private String categoryName;
    /**
     * 服务类型
     */
    @TableField(exist = false)
    private String serviceType;
    /**
     * 服务名称
     */
    @TableField(exist = false)
    private String serviceName;

    /**
     * 结算类型：标准价、合同价
     */
    private String settlementType;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 代客下单，管理员ID
     */
    private Long behalfUserSid;

    /**
     * hpc集群id
     */
    private Long clusterId;


    /**
     * hpc集群UUID
     */
    private String clusterUuid;

    /**
     * 补偿天数
     */
    private Long compensationDays;

    /**
     * 补偿相关的合同
     */
    private String compensationContractId;

    /**
     * 收费规则
     */
    private String chargingType;

    private Long entityId;

    private String entityName;

    private String freezingStrategy;

    /**
     * 订单来源
     */
    private String orderSourceSn;
}
