/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.coupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import javax.validation.Valid;

import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.CreateCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponAccountRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponDelRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponAccountResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeCouponResponse;

/**
 * <p>
 * 优惠劵 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
public interface IBizCouponService extends IService<BizCoupon> {

    /**
     * 创建优惠劵
     *
     * @param request 创建参数
     * @return 创建是否成功
     */
    boolean createCoupon(CreateCouponRequest request);

    /**
     * 分页查询优惠劵
     *
     * @param request 参数
     * @return 优惠劵
     */
    IPage<DescribeCouponResponse> listCoupons(DescribeCouponRequest request);

    /**
     * 分发优惠劵
     *
     * @param sid 优惠劵sid
     * @param accountIds 账户id
     * @return 分发结果
     */
    boolean distributeCoupon(Long sid, List<Long> accountIds);

    /**
     * 查询该优惠劵下发的账户
     *
     * @param request 参数
     * @return 下发的账户
     */
    IPage<DescribeCouponAccountResponse> listCouponAccounts(DescribeCouponAccountRequest request);

    /**
     * 作废某张优惠劵(逻辑删除)
     *
     * @param sid 优惠劵sid
     * @param remark 备注
     * @return 作废结果
     */
    boolean deleteCouponByLogic(Long sid, DescribeCouponDelRequest remark);

    BizCoupon getCoupon(Long id);
}
