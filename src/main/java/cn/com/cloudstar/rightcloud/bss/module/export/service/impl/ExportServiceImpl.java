/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.export.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.CustomExportCellValueConstants;
import cn.com.cloudstar.rightcloud.bss.common.handler.DynamicCellStyleWriteHandler;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.CustomizationInfo;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.CustomizationInfoTemplate;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.converters.NullableObjectConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.io.outputstream.ZipOutputStream;
import net.lingala.zip4j.model.ZipParameters;

import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import camundajar.impl.scala.language;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.service.config.BasicSysConfigService;
import cn.com.cloudstar.rightcloud.bss.common.constants.BillingConstants.ChargingType;
import cn.com.cloudstar.rightcloud.bss.common.constants.ConfigConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.enums.ApplicationTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.CloudEnvEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.DealTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceMethodEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.OriginTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PayTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PersonnelSizEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.PriceTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.ScopeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.TradeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.DefaultUtil;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DistributorAuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WrapperUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserExportDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.EntityUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.CustomizationEntity;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.EvsCollectorArchived;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.ExportBizAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.ObsCollectorArchived;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.response.ProductResponse;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagUser;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.BizBagUserService;
import cn.com.cloudstar.rightcloud.bss.module.bag.service.IBizBagCardHourUsageDetailService;
import cn.com.cloudstar.rightcloud.bss.module.bill.mapper.OrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.AsyncExportBaseModel;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeBizBagOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeBizBagGaapCostResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeBizBagUserOrderResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeGaapCostResponse;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.impl.InstanceGaapCostServiceImpl;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.response.DiscountExportResponse;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.ExclusiveResourcePoolBill;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.JobBill;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.StorageBill;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request.GenerateMeteringFileRequest;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportBaseService;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeShareRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.SendZipCompressPasswordRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.NodeInfo;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ShareService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InvoiceDTO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeInvoicesRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.SysMFilePathService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.CustomerTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SubUserTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.enums.FreezingStrategyEnum;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.service.ExcelExportService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysMFileTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.UserStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.CustomBusinessTag;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;

/**
 * <p>
 * 导出 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Slf4j
@Service
public class ExportServiceImpl extends ExportBaseService implements ExportService {
    public static final String SYNC = "sync";
    public static final String PLATFORM = "platform";
    public static final String SHOW = "show";
    public static final String HPC = "HPC";
    public static final String TYPE_ONE = "01";
    public static final String TYPE_TWO = "02";
    public static final String CYCLE = "cycle";
    public static final String BSS = "bss";
    public static final String POST_PAID = "PostPaid";
    public static final String PRE_PAID = "PrePaid";
    private static final Integer BIZ_DOWN_SUCCESS_STATUS = 1;

    private static final Integer BIZ_DOWN_FAIL_STATUS = 4;

    private static final String OBS = "obsUsed";

    private static final String EVS = "evsUsed";

    /**
     *  订单编号对应收费规则KEY
     */
    private static final String ORDERNO_TO_CHARGINGTYPE = "orderNo_to_chargingType";

    /**
     * console
     */
    public static final String FROM_CONSOLE = "console";

    /**
     * /
     */
    public static final String SYMBOL = "/";

    private static final Integer SHEET_LIMIT = 200000;

    private static final Integer LIMIT = 1000;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    private static final Map<String, Map<Boolean, String>> INVOICE_TYPE = Maps.newHashMap();

    static {
        INVOICE_TYPE.put("0", MapsKit.of(true, "Personal Ordinary Invoice", false, "个人普通发票"));
        INVOICE_TYPE.put("1", MapsKit.of(true, "Special corporate VAT invoice", false, "企业增值税专用发票"));
        INVOICE_TYPE.put("2", MapsKit.of(true, "General value-added tax invoice for enterprises", false, "企业增值税普通发票"));
    }

    @Value("${upload.base.path}")
    private String uploadBasePath;

    @Autowired
    private OrgService orgService;

    @Autowired
    private InstanceGaapCostServiceImpl instanceGaapCostService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private FeignService feignService;

    @Autowired
    private IBizAccountDealService bizAccountDealService;

    @Autowired
    private IBizBillingAccountService bizBillingAccountService;

    @Autowired
    private BizBagUserService bizBagUserService;

    @Autowired
    private UserMapper userMapper;


    @Autowired
    private StorageService storageService;

    @Autowired
    private BasicSysConfigService sysConfigService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    private EntityUserMapper entityUserMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ShareService shareService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SysMFilePathService sysMFilePathService;

    @Autowired
    ISysMNoticeService sysMNoticeService;

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @Autowired
    private IBizBagCardHourUsageDetailService bizBagCardHourUsageDetailService;


    @Autowired
    private IBizBillingAccountService iBizBillingAccountServiceImp;
    @Autowired
    private IBizInvoiceService bizInvoiceServiceImpl;

    @Autowired
    private IBizDistributorService distributorService;
    @Autowired
    private IBizDiscountService bizDiscountService;

    @Override
    public void doAsynExportDetail(DescribeGaapCostRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);

        String nameSuffix = fileNameSuffix();
        String taskName = console ? ExportTypeEnum.BILLDETAIL.getNameIsUs(acceptLanguage) : ExportTypeEnum.BILL_DETAIL_MANAGEMENT.getNameIsUs(acceptLanguage);
        String destFileName = taskName + "_" + nameSuffix + XLSX;
        String zipFileName = taskName + "_" + nameSuffix + ZIP;


        List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectDistinctByParams();
        Map<String, String> map = serviceCategories.stream().collect(Collectors.toMap(ServiceCategory::getServiceType, ServiceCategory::getProductName));
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream compress = null;
        ZipFile zipFile = null;
        try {
            out = new ByteArrayOutputStream();
            // 查询CUE是否显示
            String cueShowOrHide = ConfigConstants.CUE_SHOW;
            SysConfig config = sysConfigService.findByConfigTypeAndKey("other_config", "cue.show.or.hide");
            if (config != null) {
                cueShowOrHide = config.getConfigValue();
            }

            Set<Long> accountIdsSet = instanceGaapCostService.findBillingAccountByCurrentUserRole(authUserInfo);
            if (CollectionUtil.isNotEmpty(request.getAccountIds())) {
                Set<Long> accountIds = request.getAccountIds().stream().filter(accountIdsSet::contains).collect(Collectors.toSet());
                request.setAccountIds(accountIds);
            } else {
                request.setAccountIds(accountIdsSet);
            }

            Long allCount = instanceGaapCostService.getCountExportBills(request, null,authUserInfo);
            if (allCount == 0){
                up.setStatus(4);
                up.setRemark("账单明细为空，请核查数据!");
                this.bizDownloadMapper.updateById(up);
                return;
            }

            NullableObjectConverter<BigDecimal> converterRegister = new NullableObjectConverter<BigDecimal>() {
                @Override
                public Class<BigDecimal> supportJavaTypeKey() {
                    return BigDecimal.class;
                }

                @Override
                public CellDataTypeEnum supportExcelTypeKey() {
                    return CellDataTypeEnum.STRING;
                }

                @Override
                public BigDecimal convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws
                        ParseException {
                    return NumberUtils.parseBigDecimal(cellData.getStringValue(), contentProperty);
                }

                @Override
                public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
                    if (value == null) {
                        return new WriteCellData<>("--");
                    } else {
                        return NumberUtils.formatToCellDataString(value, contentProperty);
                    }
                }
            };
            String sheetName = console ? "我的账单" : "账单管理";
            if (isUs) {
                sheetName = console ? "My bill" : "Bill management";
            }
            String className =  console ? "AsyncExportConsoleBill" : "AsyncExportManagementBill";
            if (isUs) {
                className = className + "Us";
            }
            if (ConfigConstants.CUE_HIDE.equals(cueShowOrHide)) {
                className = className + "HideCue";
            }
            LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
            if (LicenseUtil.CLOSE_AI.equals(licenseVo.getVersionType()) || licenseVo.isPackageEnhancements()) {
                className = className + "HideUsedCardHourAmount";
            }
            className = String.format("cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.%sModel", className);

            List<InstanceGaapCost> instanceGaapCosts;
            // 用户真实姓名
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users = sysUserMapper.selectByParams(new Criteria());
            Map<String, String> userRealNameMaps = users.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getAccount))), ArrayList::new)).stream().collect(Collectors.toMap(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getAccount,cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getRealName));

            //压缩密码
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            ZipParameters zipParameters = ZipUtil.initZipParameters(obfuscatePassword, true);
            zipParameters.setFileNameInZip(destFileName);
            zipFile = ZipUtil.getZipFile(zipParameters,obfuscatePassword, true);
            ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile.getFile()), obfuscatePassword.toCharArray());
            zipOutputStream.putNextEntry(zipParameters);
            Class <? extends AsyncExportBaseModel> baseModel = (Class<? extends AsyncExportBaseModel>) Class.forName(className);
            ExcelExportService exportService = ExcelUtil.write().registerConverter(converterRegister).buildWriter(zipOutputStream, baseModel).buildSheet(sheetName);

            long num = 0L;
            long pageNum = 0L;
            int sheetNo = 0;
            request.setPagesize(20000L);
            String unit = isUs ? "Card time" : "卡时";
            do {
                request.setPagenum(pageNum);
                instanceGaapCosts = instanceGaapCostService.exportBills(request);
                if (CollectionUtil.isNotEmpty(instanceGaapCosts)) {
                    if (num % 1000000L == 0) {
                        sheetNo ++;
                        exportService = exportService.buildSheet(sheetName + sheetNo);
                    }
                    pageNum++;
                    log.info("ExportServiceImpl.doAsynExportDetail 本次处理数：【{}】 已处理数: {}  总数： {}", instanceGaapCosts.size(), (instanceGaapCosts.size() + num), instanceGaapCosts.size());
                    List<DescribeGaapCostResponse> instances = new ArrayList<>();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Set<String> accountIds = new HashSet<>();
                    String finalCueShowOrHide = cueShowOrHide;
                    instanceGaapCosts.forEach(instanceGaapCost -> {
                        DescribeGaapCostResponse costResponse = new DescribeGaapCostResponse();
                        BeanUtils.copyProperties(instanceGaapCost, costResponse);
                        costResponse.setBillNo(StringUtil.isNotBlank(instanceGaapCost.getBillNo())?instanceGaapCost.getBillNo():"--");
                        costResponse.setDescription(StringUtil.isNotBlank(instanceGaapCost.getDescription())?instanceGaapCost.getDescription():"--");
                        costResponse.setConfiguration(StringUtil.isNotBlank(instanceGaapCost.getConfiguration())?instanceGaapCost.getConfiguration():"--");
                        costResponse.setPaymentCurrency(instanceGaapCost.getCurrency());
                        //设置账单产品名称
                        if(map.containsKey(costResponse.getProductCode())){
                            costResponse.setProductName(map.get(costResponse.getProductCode())+" "+costResponse.getProductCode());
                        }
                        if ("BIZ-BAG".equals(costResponse.getProductCode())) {
                            costResponse.setProductName(costResponse.getProductName() + " " + costResponse.getProductCode());
                        }
                        costResponse.setInstanceName(StringUtils.isBlank(instanceGaapCost.getInstanceName()) ? instanceGaapCost.getProductName() : instanceGaapCost.getInstanceName());
                        if (isUs) {
                            costResponse.setProductName(StringUtils.replaceEach(costResponse.getProductName(), new String[]{"弹性裸金属", "对象存储", "AI开发平台共享资源池", "AI开发平台专属资源池", "云硬盘"}, new String[]{"Elastic bare metal","Object Storage","Shengteng Modelarts Shared Resource Pool","Shengteng Modelarts Exclusive Resource Pool","Cloud Disk Service"}));
                            costResponse.setInstanceName(StringUtils.replaceEach(costResponse.getInstanceName(), new String[]{"弹性裸金属BMS", "对象存储", "AI开发平台共享资源池", "AI开发平台专属资源池", "云硬盘", "套餐包-卡时包"}, new String[]{"Elastic bare metal","Object Storage","Shengteng Modelarts Shared Resource Pool","Shengteng Modelarts Exclusive Resource Pool","Cloud Disk Service", "Package Package - Card Time Package"}));

                        }
                        costResponse.setSubscriptionType(instanceGaapCost.getBillType());
                        costResponse.setInstanceId(instanceGaapCost.getResourceId());

                        costResponse.setOrderId(instanceGaapCost.getOrderSn());
                        costResponse.setPriceType(PriceTypeEnum.code2NameWithI18n(instanceGaapCost.getPriceType(), isUs));
                        costResponse.setBillType(PayTypeEnum.code2NameWithI18n(instanceGaapCost.getBillType(), isUs));
                        if (SYNC.equals(instanceGaapCost.getBillSource())) {
                                costResponse.setBillSource(isUs ? "Billing Synchronization" : "账单同步");
                        }
                        if (PLATFORM.equals(instanceGaapCost.getBillSource())) {
                                costResponse.setBillSource(isUs ? "Platform checkout" : "平台出账");
                        }
                        costResponse.setDeductCouponDiscount(
                                BigDecimalUtil.remainTwoPointAmount(instanceGaapCost.getDeductCouponDiscount()).setScale(5,BigDecimal.ROUND_DOWN));
                        costResponse.setCloudEnvName(instanceGaapCost.getCloudEnvName());
                        costResponse.setOfficialAmountString(
                                Objects.nonNull(instanceGaapCost.getOfficialAmount()) ? String.valueOf(
                                        BigDecimalUtil.remainTwoPointAmount(instanceGaapCost.getOfficialAmount()))
                                        : "--");
                        costResponse.setUsedCardHourAmountString(
                                Objects.nonNull(instanceGaapCost.getUsedCardHourAmount()) ?
                                        BigDecimalUtil.remainTwoPointAmount(instanceGaapCost.getUsedCardHourAmount()) + unit
                                        : ("0" + unit));
                        costResponse.setJobId(StringUtil.isNotBlank(instanceGaapCost.getJobId())?instanceGaapCost.getJobId():"--");
                        costResponse.setJobName(StringUtil.isNotBlank(instanceGaapCost.getJobName())?instanceGaapCost.getJobId():"--");
                        //优惠金额包括 充值现金券 抵扣现金券  优惠券抵扣 折扣包优惠  折扣优惠
                        costResponse.setAllDiscount(
                                NumberUtil.add(costResponse.getCouponDiscount(),
                                               costResponse.getDeductCouponDiscount(),
                                               costResponse.getPricingDiscount(),
                                               costResponse.getBagDiscountAmount(),
                                               costResponse.getCouponAmount()));
                        if (Objects.nonNull(costResponse.getPayTime())) {
                            costResponse.setPayTimeStr(sdf.format(costResponse.getPayTime()));
                        }
                        if (Objects.nonNull(instanceGaapCost.getUsageEndDate())) {
                             costResponse.setUsageEndDateStr(sdf.format(instanceGaapCost.getUsageEndDate()));
                        }
                        if (Objects.nonNull(instanceGaapCost.getUsageStartDate())) {
                            costResponse.setUsageStartDateStr(sdf.format(costResponse.getUsageStartDate()));
                        }
                        costResponse.setRegion(StringUtils.isEmpty(costResponse.getRegion()) ? "" : costResponse.getRegion());
                        costResponse.setDescription(
                        StringUtils.isEmpty(costResponse.getDescription()) ? "" : costResponse.getDescription());

                        //设置抹零字段
                        if(Objects.isNull(costResponse.getEraseZeroAmount())) {
                            costResponse.setEraseZeroAmount(BigDecimal.ZERO.setScale(3, BigDecimal.ROUND_HALF_UP));
                        }
                        costResponse.setPretaxGrossAmount(costResponse.getPretaxGrossAmount());
                        costResponse.setPretaxAmount(costResponse.getPretaxAmount().setScale(5,BigDecimal.ROUND_DOWN));
                        costResponse.setCashAmount(BigDecimalUtil.remainTwoPointAmount(costResponse.getCashAmount()).setScale(5,BigDecimal.ROUND_DOWN));
                        costResponse.setCouponAmount(BigDecimalUtil.remainTwoPointAmount(costResponse.getCouponAmount()).setScale(5,BigDecimal.ROUND_DOWN));
                        costResponse.setPricingDiscount(BigDecimalUtil.remainTwoPointAmount(costResponse.getPricingDiscount()));
                        costResponse.setCouponDiscount(BigDecimalUtil.remainTwoPointAmount(costResponse.getCouponDiscount()));
                        costResponse.setCreditAmount(BigDecimalUtil.remainTwoPointAmount(costResponse.getCreditAmount()).setScale(5,BigDecimal.ROUND_DOWN));

                        if (SHOW.equals(finalCueShowOrHide)) {
                            String cueValue = instanceGaapCost.getCueValue() == null ? "--" : instanceGaapCost.getCueValue();
                            costResponse.setCueValue(cueValue);
                        }
                        String computingPower = costResponse.getComputingPower();
                        costResponse.setComputingPower(StringUtils.isNotBlank(computingPower) ? computingPower : "--");

                        if (Objects.nonNull(instanceGaapCost.getSubmitTime())) {
                            costResponse.setSubmitTimeStr(sdf.format(costResponse.getSubmitTime()));
                        }
                        if (Objects.nonNull(instanceGaapCost.getStartTime())) {
                            costResponse.setStartTimeStr(sdf.format(costResponse.getStartTime()));
                         }
                        if (Objects.nonNull(instanceGaapCost.getEndTime())) {
                            costResponse.setEndTimeStr(sdf.format(costResponse.getEndTime()));
                        }
                        //非HPC的账单字段，设置为不适用
                        if (!StringUtils.startsWithIgnoreCase(costResponse.getProductCode(), HPC)) {
                            assembleNotApplicableCopy(costResponse);
                        } else {
                            //给HPC账单字段没有提交时间等设置默认值
                            setDefaultValueCopy(costResponse);
                        }
                        String cloudEnvName = costResponse.getCloudEnvName();
                        if (StringUtils.isEmpty(cloudEnvName)) {
                            costResponse.setCloudEnvName(isUs ? "Other" : "其他");
                        }
                        costResponse.setChargingType(isUs ? "Normal billing" : "正常计费");
                        if (StringUtil.isNotEmpty(instanceGaapCost.getChargingType()) && TYPE_TWO.equals(instanceGaapCost.getChargingType())){
                            costResponse.setChargingType(isUs ? "Sales billing" : "销售计费");
                        }
                        String usageCount = costResponse.getUsageCount();
                        if (StringUtils.isBlank(usageCount)) {
                            costResponse.setUsageCount("--");
                         }
                        // 用户id
                        if (!Objects.isNull(instanceGaapCost.getUserAccountId())){
                            costResponse.setUserAccountId(instanceGaapCost.getUserAccountId().toString());
                        }
                        // 用户名（姓名）
                        String realName = userRealNameMaps.get(costResponse.getUserAccountName());
                        if (StringUtils.isNotBlank(realName)) {
                            costResponse.setUserAccountName(costResponse.getUserAccountName() + "（" + realName + "）");
                        }
                        // 订单类型
                        if (Objects.nonNull(costResponse.getType())) {
                            costResponse.setType(OrderType.orderType2NameWithI18n(costResponse.getType(), isUs));
                        } else {
                            costResponse.setType("--");
                        }
                        // 订单来源
                        if (StringUtils.isBlank(costResponse.getOrderSourceSn())) {
                            costResponse.setOrderSourceSn("--");
                        }
                        // 订单号
                        if (StringUtils.isBlank(costResponse.getOrderId())) {
                            costResponse.setOrderId("--");
                        }
                        // 统计小时和统计天数
                        if (Objects.isNull(costResponse.getStatisticHours())){
                            costResponse.setStatisticHours(new BigDecimal(0));
                        }
                        if (Objects.isNull(costResponse.getStatisticDays())){
                            costResponse.setStatisticDays(new BigDecimal(0));
                        }
                        instances.add(costResponse);
                        accountIds.add(String.valueOf(instanceGaapCost.getUserAccountId()));
                    });

                    List<BizBillingAccount> info = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(accountIds)) {
                        Map<String, Object> param = new HashMap<>(16);
                        param.put("ids", accountIds);
                        info = this.bizBillingAccountService.getSimpleAccountAndDistributorInfo(param);
                    }
                    Map<String, BizBillingAccount> billingAccountMap = info.stream().collect(Collectors.toMap(account -> account.getId().toString(), i -> i));
                    List<AsyncExportBaseModel> exportInstanceList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(instances) && CollectionUtil.isNotEmpty(info)) {
                        for (DescribeGaapCostResponse instance : instances) {
                            String invoiceStatus = instance.getInvoiceStatus();
                            String invoiceName = InvoiceStatusEnum.code2NameByI18n(invoiceStatus, isUs);
                            if (StringUtils.isNotEmpty(invoiceName)) {
                                instance.setInvoiceStatus(invoiceName);
                            } else {
                                instance.setInvoiceStatus(isUs ? "Not invoiced" : "未开票");
                            }
                            if (Objects.nonNull(instance.getUserAccountId())) {
                                BizBillingAccount bizBillingAccount = billingAccountMap.get(instance.getUserAccountId());
                                if (Objects.isNull(bizBillingAccount)) {
                                    continue;
                                }
                                instance.setOrgName(bizBillingAccount.getAccountName());
                                instance.setProjectName(bizBillingAccount.getAccountName());
                                String defaultName = isUs ? "Not invoiced" : "直营";
                                instance.setDistributorName(StringUtils.isBlank(bizBillingAccount.getDistributorName()) ? defaultName : bizBillingAccount.getDistributorName());
                            }
                            if (isUs) {
                                instance.setEntityName(StringUtils.replaceEach(instance.getEntityName(), new String[]{"默认运营实体"}, new String[]{"Default operating entity"}));
                                instance.setConfiguration(configurationToUs(instance.getConfiguration()));
                                instance.setUsageCount(StringUtils.replaceEach(instance.getUsageCount(), new String[]{"天", "小时", "分", "秒"}, new String[]{"day", "h", "m", "s"}));
                            }


                            exportInstanceList.add(BeanUtil.toBean(instance,baseModel));
                        }
                    }
                    int size = exportInstanceList.size();
                    exportService.write(exportInstanceList);
                    CollUtil.clear(instances);
                    num = num + (long) size;
                }
            } while (CollectionUtil.isNotEmpty(instanceGaapCosts));
            exportService.finish();
            zipOutputStream.closeEntry();
            zipOutputStream.close();

            //minio上传
            String type = console ? "UserBillDetail" : "ManagementBillDetail";
            StorageResult result = storageService.saveFile(FileUtil.getInputStream(zipFile.getFile()), StoragePathEnum.EXCEL.getPath(type), zipFileName, true, true, null);
            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BILLDETAIL, up.getDownloadId(), authUserInfo);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("doAsynExportDetail:Exception:",e);
            log.error("ExportServiceImpl_doAsynExportDetail_error: {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(compress);
            IOUtils.closeQuietly(zipFile);
        }
    }

    /**
     * 客户信息导出
     * @param describeBillingAccountRequest
     * @param moduleType
     * @param taskId
     * @param authUserInfo
     */
    @Override
    public void doAsynExportBizBillingAccount(DescribeBillingAccountRequest describeBillingAccountRequest, String moduleType, Long taskId, AuthUser authUserInfo) {
        String type = "bizBillingAccount";
        //安全随机数
        String format = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
            + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        String taskName = ExportTypeEnum.BILLING_ACCOUNT.getNameIsUs(acceptLanguage);
        String destFileName = taskName + format + ".xlsx";
        String zipFileName = taskName + format + ".zip";
        List<BizBillingAccount> bizBillingAccountList = bizBillingAccountService.accountInfExport(describeBillingAccountRequest);
        List<UserExportDTO> resUser = new ArrayList<>();

        // 获取自定义tag
        String customBusinessTagStr =
                PropertiesUtil.getProperty(cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.CUSTOM_BUSINESS_TAG);
        Map<String, String >customBusinessMap = new HashMap<>();
        if (StringUtils.isNotBlank(customBusinessTagStr)) {
            List<CustomBusinessTag> customBusinessTags = JSON.parseArray(customBusinessTagStr, CustomBusinessTag.class);
            customBusinessMap = customBusinessTags.stream().collect(Collectors.toMap(CustomBusinessTag::getTagKey, CustomBusinessTag::getTagName));
        }

        for (BizBillingAccount bizBillingAccount : bizBillingAccountList) {
            bizBillingAccount.setContactName(bizBillingAccount.getContactName());
            bizBillingAccount.setAdminName(bizBillingAccount.getAdminName());
            bizBillingAccount.setEmail(bizBillingAccount.getEmail());
            bizBillingAccount.setMobile(bizBillingAccount.getMobile());
            bizBillingAccount.setStatus(UserStatusEnum.status2DescByI18n(bizBillingAccount.getUserStatus(), isUs));
            Criteria criteria = new Criteria();
            criteria.put("orgSid", bizBillingAccount.getOrgSid());
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users = sysUserMapper.selectByParams(criteria).stream()
                .filter(item -> !item.getUserSid().equals(bizBillingAccount.getUserSid()))
                .collect(Collectors.toList());
            String businessTag = bizBillingAccount.getBusinessTag();
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>();
                String[] split = businessTag.split(";");
                for (String tag : split) {
                    if (tag.contains(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.ARREARAGE.getTag())) {
                        String[] splitTag = tag.replaceAll(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.ARREARAGE.getTag(), "")
                            .replaceAll("\\[", "")
                            .replaceAll("]", "")
                            .split(",");
                        List<String> accountIdList = Arrays.asList(splitTag);
                        if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                            tagList.add(cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum.ARREARAGE.getName());
                        }
                    } else {
                        String tagName = BusinessTagEnum.tag2NameByI18n(tag, isUs);
                        if (StringUtils.isBlank(tagName)) {
                            tagName = customBusinessMap.get(tag);
                        }

                        tagList.add(tagName);

                    }
                }
                bizBillingAccount.setBusinessTag(StringUtils.join(tagList, ","));
            }
            users.stream().map(user -> {
                user.setBusinessTag(bizBillingAccount.getBusinessTag());
                return user;
            }).collect(Collectors.toList());
            List<UserExportDTO> exportUser = BeanConvertUtil.convert(users, UserExportDTO.class);
            exportUser.forEach(e -> {
                e.setAccountName(bizBillingAccount.getAccountName());
                e.setContactName(bizBillingAccount.getContactName());
                e.setAdminName(bizBillingAccount.getAdminName());
                e.setAccountName(bizBillingAccount.getAccountName());
                e.setAccount(e.getAccount());
            });
            resUser.addAll(exportUser);
        }

        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream compress = null;
        try {
            out = new ByteArrayOutputStream();
            String obsFree = PropertiesUtil.getProperty("obs_free_capacity");
            List<ExportBizAccount> list = bizBillingAccountList.stream().map(e -> {
                ExportBizAccount bean = BeanUtil.copyProperties(e, ExportBizAccount.class);
                bean.setBalance(Objects.nonNull(e.getBalance()) ? String.valueOf(BigDecimalUtil.getTwoPointAmount(e.getBalance()).doubleValue()) : "0.0");
                bean.setBalanceCash(String.valueOf(e.getBalanceCash().doubleValue()));
                bean.setCreditLine(String.valueOf(e.getCreditLine().doubleValue()));
                bean.setOfflineInvoicedAmount(Objects.nonNull(e.getOfflineInvoicedAmount()) ? String.valueOf(e.getOfflineInvoicedAmount().doubleValue()) : "0.0");
                BigDecimal obs = e.getObsFreeCapacity();
                bean.setObsFreeCapacity(obs == null ? obsFree + " GB" : obs + " GB");

                if (isUs) {
                    bean.setDistributorName(bean.getDistributorName().equals("直营") ? "be run directly by a manufacturer" : bean.getDistributorName());
                    bean.setFreezeStatusName(StringUtils.replaceEach(bean.getFreezeStatusName(), new String[]{"是", "否"}, new String[]{"YES", "NO"}));
                    bean.setIndustryName(StringUtils.replaceEach(bean.getIndustryName(), new String[]{"安全","互联网","制造","交通","能源","运营商","金融","数字政府","军工","教育","医疗","科研机构","其他"}, new String[]{"Safety","Internet","Manufacturing","Traffic","Energy","Carrier","Finance","Digital government","Military industry","Education","Medical treatment","Scientific research institution","Others"}));
                    bean.setApplicationScenarioName(ApplicationTypeEnum.code2NameByI18n(bean.getApplicationScenario(), isUs));
                    bean.setPersonnelSizeName(PersonnelSizEnum.code2NameByI18n(bean.getPersonnelSize(), isUs));
                }
                return bean;
            }).collect(Collectors.toList());

            String customInfoTemplate = PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_TEMPLATE);
            if (CommonPropertyKeyEnum.ONE.getCode().equals(PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_SWITCH))
                && org.apache.commons.lang3.StringUtils.isNotEmpty(customInfoTemplate) && !org.springframework.util.CollectionUtils.isEmpty(bizBillingAccountList)) {
                XSSFWorkbook workbookAccountWorkBook = bizBillingAccountService.createWorkbookAccountWithCustomInfo(bizBillingAccountList, resUser, customInfoTemplate);
                workbookAccountWorkBook.write(out);
                in = new ByteArrayInputStream(out.toByteArray());
            } else {
                String template = isUs ?  "template/customer-export-template-us.xlsx" : "template/customer-export-template.xlsx";
                ExcelExportService exportService = ExcelUtil.write().buildWriter(out, template);
                exportService.buildSheet(taskName).fill(list);
                List<UserExportDTO> convert = BeanConvertUtil.convert(resUser, UserExportDTO.class);
                exportService.buildSheet(isUs ? "list_of_subusers" : "子用户信息列表").fill(convert).finish();
                in = new ByteArrayInputStream(out.toByteArray());
            }
            //压缩密码
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            // 压缩文件
            compress= ZipUtil.compress(in, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(compress, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BIZ_BILLING_ACCOUNT, up.getDownloadId(), authUserInfo);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportDetail_error: {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(compress);
        }
    }

    private void setDefaultValue(DescribeGaapCostResponse costResponse) {
        if(Objects.isNull(costResponse.getAccount())){
            costResponse.setAccount(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getClusterName())){
            costResponse.setClusterName(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getJobName())){
            costResponse.setJobName(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getSubmitTimeStr())){
            costResponse.setSubmitTimeStr(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getStartTimeStr())){
            costResponse.setStartTimeStr(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getEndTimeStr())){
            costResponse.setEndTimeStr(NOT_APPLICABLE);
        }
        if(Objects.isNull(costResponse.getJobId())){
            costResponse.setJobId(NOT_APPLICABLE);
                }
        if(Objects.isNull(costResponse.getCount())){
            costResponse.setCount(NOT_APPLICABLE);
            }
        if(Objects.isNull(costResponse.getUserGroup1())){
            costResponse.setUserGroup1(NOT_APPLICABLE);
        }
    }


    private void setDefaultValueCopy(DescribeGaapCostResponse costResponse) {
        if(Objects.isNull(costResponse.getAccount())){
            costResponse.setAccount("--");
        }
        if(Objects.isNull(costResponse.getClusterName())){
            costResponse.setClusterName("--");
        }
        if(Objects.isNull(costResponse.getJobName())){
            costResponse.setJobName("--");
        }
        if(Objects.isNull(costResponse.getSubmitTimeStr())){
            costResponse.setSubmitTimeStr("--");
        }
        if(Objects.isNull(costResponse.getStartTimeStr())){
            costResponse.setStartTimeStr("--");
        }
        if(Objects.isNull(costResponse.getEndTimeStr())){
            costResponse.setEndTimeStr("--");
        }
        if(Objects.isNull(costResponse.getJobId())){
            costResponse.setJobId("--");
        }
        if(Objects.isNull(costResponse.getCount())){
            costResponse.setCount("--");
        }
        if(Objects.isNull(costResponse.getUserGroup1())){
            costResponse.setUserGroup1("--");
        }
    }
    private void assembleNotApplicable(DescribeGaapCostResponse costResponse) {
        costResponse.setEndTimeStr(NOT_APPLICABLE);
        costResponse.setJobId(NOT_APPLICABLE);
        costResponse.setJobName(NOT_APPLICABLE);
        costResponse.setStartTimeStr(NOT_APPLICABLE);
        costResponse.setSubmitTimeStr(NOT_APPLICABLE);
        costResponse.setAccount(NOT_APPLICABLE);
        costResponse.setCount(NOT_APPLICABLE);
        costResponse.setClusterName(NOT_APPLICABLE);
        costResponse.setUserGroup1(NOT_APPLICABLE);
    }

    private void assembleNotApplicableCopy(DescribeGaapCostResponse costResponse) {
        costResponse.setEndTimeStr("--");
        costResponse.setJobId("--");
        costResponse.setJobName("--");
        costResponse.setStartTimeStr("--");
        costResponse.setSubmitTimeStr("--");
        costResponse.setAccount("--");
        costResponse.setCount("--");
        costResponse.setClusterName("--");
        costResponse.setUserGroup1("--");
    }

    @Override
    public void doAsynExportCycle(DescribeGaapCostRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUserInfo.getAcceptLanguage());
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        List<BillBillingCycleCostVo> voList = instanceGaapCostService.listExportCycle(request, authUserInfo);
        if (CollectionUtil.isEmpty(voList)) {
            log.info("ExportServiceImpl-doAsynExportCycle-账单周期为空，请核查数据");
            up.setStatus(4);
            up.setRemark("账单周期为空，请核查数据!");
            this.bizDownloadMapper.updateById(up);
            return;
        }
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream compress = null;
        try {
            for (BillBillingCycleCostVo costVo : voList) {
                BigDecimal eraseZeroAmount = costVo.getEraseZeroAmount();
                if (eraseZeroAmount == null) {
                    eraseZeroAmount = BigDecimal.ZERO;
                }
                costVo.setBillType(PayTypeEnum.code2NameWithI18n(costVo.getBillType(), isUs));
                costVo.setPriceType(PriceTypeEnum.code2NameWithI18n(costVo.getPriceType(), isUs));
                costVo.setPretaxGrossAmount(scaleFive(costVo.getPretaxGrossAmount()));
                costVo.setDiscountTotalAmount(scaleFive(costVo.getDiscountTotalAmount()));
                costVo.setDeductCouponDiscount(scaleFive(costVo.getDeductCouponDiscount()));
                costVo.setCashAmount(scaleTwoDown(costVo.getCashAmount()));
                costVo.setCreditAmount(scaleTwoDown(costVo.getCreditAmount()));
                costVo.setPricingDiscount(scaleFive(costVo.getPricingDiscount()));
                costVo.setCouponDiscount(scaleTwoDown(costVo.getCouponDiscount()));
                costVo.setVoucherAmount(scaleTwoDown(costVo.getVoucherAmount()));
                costVo.setAmount(scaleTwoDown(costVo.getAmount()));
                costVo.setEraseZeroAmount(eraseZeroAmount);
            }
            boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
            String type = console ? "UserBillCycle" : "ManagementBillCycle";

            String destFileName;
            String zipFileName;
            String xlsTemplateFileName;
            String nameSuffix = fileNameSuffix();
            if (console) {
                String consoleName = isUs ? "billing_cycle_" : "账单周期_";
                destFileName = consoleName + nameSuffix + XLSX;
                zipFileName = consoleName + nameSuffix + ZIP;
                xlsTemplateFileName = isUs ? "template/console-cycle-bill-us.xlsx" : "template/console-cycle-bill.xlsx";
            } else {
                if (Objects.isNull(request.getGroupByFlag()) || CYCLE.equals(request.getGroupByFlag())) {
                    String cycleName = isUs ? "management_cycle_bill_" : "账单周期_账期_";
                    destFileName = cycleName + nameSuffix + XLSX;
                    zipFileName = cycleName + nameSuffix + ZIP;
                    xlsTemplateFileName = isUs ? "template/management-cycle-bill-us.xlsx" : "template/management-cycle-bill.xlsx";
                } else {
                    String customerName = isUs ? "management_customer_bill_" : "账单周期_客户_";
                    destFileName = customerName + nameSuffix + XLSX;
                    zipFileName = customerName + nameSuffix + ZIP;
                    xlsTemplateFileName = isUs ? "template/management-customer-bill-us.xlsx" : "template/management-customer-bill.xlsx";
                }
            }
            out = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(out, xlsTemplateFileName)
                    .buildSheet()
                    .fill(voList)
                    .finish();

            //压缩密码
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            in = new ByteArrayInputStream(out.toByteArray());
            // 压缩文件
            compress= ZipUtil.compress(in, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(compress, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                                                           true, null);
            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(destFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BILLCYCLE, up.getDownloadId(), authUserInfo);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);

        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportDetail_error : {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(compress);
            IOUtils.closeQuietly(in);
        }

    }

    @Override
    public void doAsynExportBizBagBill(DescribeGaapCostRequest request, String moduleType, Long taskId, AuthUser authUser) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUser.getUserSid());
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream inZip = null;
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage());
        try {
            String nameSuffix = fileNameSuffix();
            boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
            String type = console ? "UserBizBagBillDetail" : "ManagementBizBagBillDetail";
            String xlsTemplateFileName = "template/bizbag-bill.xlsx";
            String fileName = (console ? "套餐包使用明细_" : "套餐包使用明细管理_") + nameSuffix;
            if (isUs) {
                xlsTemplateFileName = StringUtils.replace(xlsTemplateFileName, ".xlsx", "-us.xlsx");
                fileName = StringUtils.replaceEach(fileName, new String[]{"套餐包使用明细_", "套餐包使用明细管理_"}, new String[]{"package_usage_details_", "package_use_detail_management_"});
            }
            String destFileName = fileName + XLSX;;
            String zipFileName = fileName + ZIP;
            List<DescribeBizBagGaapCostResponse> instances = new ArrayList<>();
            //如果是卡时包查询卡时包明细
            String bagInstUuid = request.getBagInstUuid();
            boolean isCardHourBag = false;
            BizBagUser oneBagUser = null;
            long count =0;
            if (StringUtils.isNotEmpty(bagInstUuid)) {
                oneBagUser = bizBagUserService.lambdaQuery()
                                              .eq(BizBagUser::getBagInstUuid, bagInstUuid)
                                              .eq(BizBagUser::getBagType, StatusType.CARD_HOUR).one();
                if (oneBagUser != null) {
                    count = bizBagCardHourUsageDetailService.countResponseFromUsageDetail(request, oneBagUser);
                    isCardHourBag = true;
                }

            }
            if (!isCardHourBag) {
                count = instanceGaapCostService.getCountExportBills(request, null, RequestContextUtil.getAuthUserInfo());
            }
            //是否有数据
            if (count < 1) {
                up.setStatus(4);
                up.setRemark(isUs ? "Usage details are empty, please check the data!" : "使用明细为空，请核查数据!");
                this.bizDownloadMapper.updateById(up);
                return;
            }

            NullableObjectConverter<BigDecimal> register = new NullableObjectConverter<BigDecimal>() {
                public Class<BigDecimal> supportJavaTypeKey() {
                    return BigDecimal.class;
                }

                public CellDataTypeEnum supportExcelTypeKey() {
                    return CellDataTypeEnum.STRING;
                }

                public BigDecimal convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) throws
                        ParseException {
                    return NumberUtils.parseBigDecimal(cellData.getStringValue(), contentProperty);
                }

                public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty,
                                                           GlobalConfiguration globalConfiguration) {
                    if (value == null) {
                        return new WriteCellData<>("--");
                    } else {
                        return NumberUtils.formatToCellDataString(value, contentProperty);
                    }
                }
            };
            out = new ByteArrayOutputStream();
            ExcelExportService exportService = ExcelUtil.write()
                    .registerConverter(register)
                    .buildWriter(out, xlsTemplateFileName)
                    .buildSheet();
            long num = 0L;
            request.setPagesize(20000L);
            long pageNum = 0L;
            do {
                request.setPagenum(pageNum);
                if (!isCardHourBag) {
                    request.setPageFlag("Y");
                    List<InstanceGaapCost> instanceGaapCosts =
                            instanceGaapCostService.listExportBills(request, null, false, RequestContextUtil.getAuthUserInfo());
                    instances = new ArrayList<>();
                    Set<Long> accountIds = new HashSet<>();
                    for (InstanceGaapCost instanceGaapCost : instanceGaapCosts) {
                        DescribeBizBagGaapCostResponse costResponse = new DescribeBizBagGaapCostResponse();
                        costResponse.setProductName(instanceGaapCost.getProductName());
                        costResponse.setBillNo(instanceGaapCost.getBillNo());
                        costResponse.setRebate(
                                BigDecimal.valueOf(instanceGaapCost.getBagDiscount()).setScale(5, BigDecimal.ROUND_HALF_UP));
                        costResponse.setRebateDiscount(
                                instanceGaapCost.getBagDiscountAmount().setScale(5, BigDecimal.ROUND_HALF_UP));
                        costResponse.setPayTimeStr(
                                DateUtil.format(instanceGaapCost.getPayTime(), DatePattern.NORM_DATETIME_PATTERN));
                        costResponse.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(instanceGaapCost.getPretaxAmount()));
                        costResponse.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(instanceGaapCost.getPretaxAmount()));
                        costResponse.setBeforeBagDiscountAmount(
                                instanceGaapCost.getBeforeBagDiscountAmount().setScale(5, BigDecimal.ROUND_HALF_UP));
                        costResponse.setAccount(instanceGaapCost.getOrgName());
                        costResponse.setUserAccountId(instanceGaapCost.getUserAccountId());
                        instances.add(costResponse);
                        accountIds.add(instanceGaapCost.getUserAccountId());
                    }
                } else {
                    IPage<DescribeGaapCostResponse> responseFromUsageDetailPage =
                            bizBagCardHourUsageDetailService.getResponseFromUsageDetail(request, oneBagUser);
                    List<DescribeGaapCostResponse> records = responseFromUsageDetailPage.getRecords();
                    instances = BeanConvertUtil.convert(records, DescribeBizBagGaapCostResponse.class);
                }
                log.info("ExportServiceImpl.doAsynExportBizBagBill 已导出[{}],待导出[{}],",num,instances.size());
                if (CollectionUtil.isNotEmpty(instances)) {
                    if (isUs) {
                        instances.forEach(e -> {
                            e.setProductName(StringUtils.replaceEach(e.getProductName(), new String[]{"AI开发平台共享资源池"}, new String[]{"Ascending Modelarts Shared Resource Pool"}));
                            e.setBagSpecName(StringUtils.replaceEach(e.getBagSpecName(), new String[]{"卡时"}, new String[]{"Card time"}));
                        });
                    }
                    pageNum++;
                    exportService.fill(instances);
                    num = num + Long.valueOf(instances.size());
                } else {
                    instances = new ArrayList<>();
                }
            } while (CollectionUtil.isNotEmpty(instances));

            exportService.finish();
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            in = new ByteArrayInputStream(out.toByteArray());
            //压缩文件
            inZip = ZipUtil.compress(in, destFileName, obfuscatePassword, true);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName,
                                                           true, true, null);

            up.setStatus(BIZ_DOWN_SUCCESS_STATUS);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUser.getEntityId());
            this.bizDownloadMapper.updateById(up);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BIZBAG_BILLDETAIL, up.getDownloadId());
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark((isUs ? "Download exception, abnormal cause:" : "下载异常，异常原因：") + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportBizBagBill_error: {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
        }
    }


    @Override
    public void doAsynExportBizBagOrder(DescribeBizBagOrderRequest request, String moduleType, Long taskId, AuthUser authUser) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUser.getUserSid());
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream inZip = null;
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage());
        try {
            List<BizBagUser> list = bizBagUserService.lambdaQuery()
                                                     .gt(null != request.getStartTime(), BizBagUser::getStartTime,
                        request.getStartTime())
                                                     .lt(null != request.getEndTime(), BizBagUser::getEndTime,
                        request.getEndTime())
                .eq(StringUtils.isNoneBlank(request.getOrgName()),
                        BizBagUser::getOrgName, request.getOrgName())
                                                     .eq(BizBagUser::getBagId, request.getBagId())
                                                     .list();
            if (CollectionUtil.isEmpty(list)) {
                up.setStatus(4);
                up.setRemark(isUs ? "Package order is empty, please check the data!" : "套餐包订单为空，请核查数据!");
                this.bizDownloadMapper.updateById(up);
                return;
            }
            List<DescribeBizBagUserOrderResponse> convert = BeanConvertUtil.convert(list,
                                                                                    DescribeBizBagUserOrderResponse.class);
            convert.forEach(e -> {
                   //根据当前订单查询订单表数据
                   Long accountId = serviceOrderMapper.findAccountIdByOrderSn(Long.valueOf(e.getOrderSn() == null ? "0" : e.getOrderSn()));
                   e.setAccountId(accountId == null ? "0" : accountId.toString());
                if (isUs) {
                    e.setEntityName(StringUtils.replaceEach(e.getEntityName(), new String[]{"默认运营实体"}, new String[]{"Default operating entity"}));
                    e.setBagSpecName(StringUtils.replaceEach(e.getBagSpecName(), new String[]{"卡时"}, new String[]{"Card time"}));
                }
            });

            boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
            String type = console ? "UserBizBagOrderDetail" : "ManagementBizBagOrderDetail";

            out = new ByteArrayOutputStream();

            String xlsTemplateFileName  = "template/bizbag-order.xlsx";
            if (isUs) {
                xlsTemplateFileName  = "template/bizbag-order-us.xlsx";
            }
            // 这里需要设置不关闭流
            ExcelUtil.write()
                    .buildWriter(out, xlsTemplateFileName)
                    .buildSheet()
                    .fill(convert)
                    .finish();

            String fileName = (console ? "套餐包订购明细_" : "套餐包订购明细管理_") + fileNameSuffix();
            if (isUs) {
                fileName = StringUtils.replaceEach(fileName, new String[]{"套餐包订购明细_", "套餐包订购明细管理_"}, new String[]{"package_ordering_details_", "package_ordering_details_management_"});
            }
            String destFileName = fileName + XLSX;;
            String zipFileName = fileName + ZIP;

            in = new ByteArrayInputStream(out.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(in, destFileName, obfuscatePassword, true);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName,
                                                           true, true, null);

            up.setStatus(BIZ_DOWN_SUCCESS_STATUS);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            this.bizDownloadMapper.updateById(up);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BIZBAG_BILL_ORDER, up.getDownloadId());
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark((isUs ? "Download exception, abnormal cause:" : "下载异常，异常原因：") + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportBizBagOrder_error : {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(inZip);
        }
    }

    @Override
    public void doAsynExportCustomer(List<CustomerTemplate> customers, List<SubUserTemplate> subUsers, String type, Long taskId, AuthUser authUser) {
        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUser.getUserSid());
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream compress = null;
        try {
            out = new ByteArrayOutputStream();
            boolean customer = Objects.equals("customer", type);
            String nameSuffix = fileNameSuffix();
            String destFileName = (customer ? "批量注册客户_" : "批量注册账户_") + nameSuffix + XLSX;
            String zipFileName = (customer ? "批量注册客户_" : "批量注册账户_") + nameSuffix + ZIP;
            String template = "";
            if (CollectionUtil.isNotEmpty(customers) && CollectionUtil.isNotEmpty(subUsers)) {
                template = "template/customer-subUser.xlsx";
                if (WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage())) {
                    template = "template/customer-subUser-us.xlsx";
                }
                ExcelExportService exportService = ExcelUtil.write().buildWriter(out, template);
                exportService.buildSheet(0).fill(customers);
                exportService.buildSheet(1).fill(subUsers);
                exportService.finish();
            } else if (CollectionUtil.isNotEmpty(customers) && CollectionUtil.isEmpty(subUsers)) {
                template = "template/customer.xlsx";
                if (WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage())) {
                    template = "template/customer.xlsx-us.xlsx";
                }
                // 这里需要设置不关闭流
                ExcelUtil.write()
                        .buildWriter(out, template)
                        .buildSheet()
                        .fill(customers)
                        .finish();
            } else if (CollectionUtil.isEmpty(customers) && CollectionUtil.isNotEmpty(subUsers)) {
                template = customer ? "template/customer.xlsx" : "template/subUser.xlsx";
                if (customer && WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage()) ) {
                    template = "template/customer-us.xlsx";
                }
                if (!customer && WebUtil.getHeaderAcceptLanguage(authUser.getAcceptLanguage()) ) {
                    template = "template/subUser-us.xlsx";
                }
                // 这里需要设置不关闭流
                ExcelUtil.write()
                        .buildWriter(out, template)
                        .buildSheet()
                        .fill(subUsers)
                        .finish();
            }

            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            in = new ByteArrayInputStream(out.toByteArray());
            // 压缩文件
            compress = ZipUtil.compress(in, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(compress, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                                                           true, null);

            up.setStatus(1);
            up.setUpdatedDt(new Date());
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUser.getEntityId());
            this.bizDownloadMapper.updateById(up);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.CUSTOMER, up.getDownloadId());
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportCustomer_error: {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(compress);
        }
    }

    @Override
    public void doAsynExportBizAccountDetail(DescribeDealsRequest describeDealsRequest, String moduleType, Long taskId,
                                             User user, AuthUser authUserInfo) {
        log.info("ExportServiceImpl_doAsynExportBizAccountDetail_start__{}_{}", JSON.toJSONString(describeDealsRequest), moduleType);

        boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
        String type = console ? "UserDeal" : "ManagementDeal";

        String destFileName;
        String zipFileName;
        String nameSuffix = fileNameSuffix();
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        String name = ExportTypeEnum.BIZ_ACCOUNT_DEAL.getNameIsUs(acceptLanguage) + "_";
        if (console) {
            destFileName = name + nameSuffix + XLSX;
            zipFileName = name + nameSuffix + ZIP;
        } else {
            destFileName = name + nameSuffix + XLSX;
            zipFileName = name + nameSuffix + ZIP;
        }

        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);

        ZipFile zipFile = null;
        ZipOutputStream zipOutputStream = null;
        try {
            //收支明细
            String customizationInfo = describeDealsRequest.getCustomizationInfo();

            QueryWrapper<BizAccountDeal> queryWrapper = criteria(describeDealsRequest, user, authUserInfo.getEntityId());
            if (null != authUserInfo.getEntityId() && authUserInfo.getEntityId() != 0) {
                queryWrapper.eq("entity_id", authUserInfo.getEntityId());
            }
            //设置密码
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String customInfoTemplate = PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_TEMPLATE);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            ZipParameters zipParameters = ZipUtil.initZipParameters(obfuscatePassword, true);
            zipParameters.setFileNameInZip(destFileName);
            zipFile = ZipUtil.getZipFile(zipParameters, obfuscatePassword, true);
            zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile.getFile()), obfuscatePassword.toCharArray());
            zipOutputStream.putNextEntry(zipParameters);



            boolean isCustonInfoEnable = CommonPropertyKeyEnum.ONE.getCode().equals(PropertiesUtil.getProperty(SysConfigConstants.CUSTOM_INFO_SWITCH))
                && StringUtils.isNotEmpty(customInfoTemplate);
            Map<Long, String> customizationInfoMap = null;
            if (BSS.equals(moduleType) && isCustonInfoEnable) {
                // 自定义用户信息，先取得所有用户的自定义信息，防止收支明细数据过多导致循环调用MySQL服务
                List<Org> orgs = orgService.selectOrgsByCustomize(customizationInfo);
                if (StringUtils.isNotEmpty(customizationInfo)) {
                    List<Long> orgIds = orgs.stream().map(Org::getOrgSid).collect(Collectors.toList());
                    queryWrapper.in("org_sid", orgIds);
                }
                customizationInfoMap = orgs.stream()
                    .filter(org -> StringUtils.isNotEmpty(org.getCustomizationInfo()))
                    .collect(Collectors.toMap(Org::getOrgSid, Org::getCustomizationInfo));
                this.exportBizAccountDetailWithCusTomInfo(queryWrapper, customizationInfoMap, zipOutputStream, customInfoTemplate);
            }else {
                int allCount = bizAccountDealService.count(queryWrapper);
                if (allCount <= 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_205376413));
                }

                // redis取订单对应收费规则数据
                Map<String, String> typeList = JedisUtil.INSTANCE.hgetall(ORDERNO_TO_CHARGINGTYPE);
                Map<Long, String> distributorNameMap = new HashMap<>();

                //模板类
                String packageName = "cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.";
                String className = console ? packageName + "AsyncConsoleAccountDeal" : packageName + "AsyncExportBssAccountDeal";
                if (isUs) {
                    className = console ? packageName + "AsyncConsoleAccountDealUs" : packageName + "AsyncExportBssAccountDealUs";
                }
                if (StringUtils.isNotEmpty(describeDealsRequest.getAccountSid())) {
                    className = packageName + (isUs ? "AsyncExportBssAccountDealPersonal" : "AsyncExportBssAccountDealPersonalUs");
                }



                Class<? extends AsyncExportBaseModel> baseModel = (Class<? extends AsyncExportBaseModel>) Class.forName(className + "Model");

                String sheetName = ExportTypeEnum.BIZ_ACCOUNT_DEAL.getNameIsUs(acceptLanguage);
                ExcelExportService exportService = ExcelUtil.write()
                    .buildWriter(zipOutputStream, baseModel)
                    .buildSheet(sheetName);

                List<BizAccountDeal> instances = new ArrayList<>();
                List<AsyncExportBaseModel> exportInstanceList = new ArrayList<>();
                int size = 0;
                long num = 0L;
                long pageNum = 0L;
                long sheetNo = 0L;
                do {
                    pageNum++;
                    IPage<BizAccountDeal> pages = bizAccountDealService.page(new Page<>(pageNum, 10000L), queryWrapper);
                    instances = pages.getRecords();
                    size = instances.size();
                    log.info("ExportServiceImpl.doAsynExportBizAccountDetail 本次处理数：【{}】 已处理数: {}  总数： {}",
                        size, num, allCount);
                    if (CollectionUtil.isNotEmpty(instances)) {
                        if (num % 1000000L == 0) {
                            sheetNo++;
                            exportService.buildSheet(sheetName + sheetNo);
                        }
                        Set<Long> accountSids = instances.stream().map(BizAccountDeal::getAccountSid).collect(Collectors.toSet());
                        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectBatchIds(accountSids);
                        Set<Long> adminSids = bizBillingAccounts.stream().map(BizBillingAccount::getAdminSid).collect(Collectors.toSet());
                        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users = sysUserMapper.selectBatchIds(adminSids);
                        Map<Long, String> userNameMap = users.stream().collect(Collectors.toMap(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getUserSid, cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getAccount));
                        Map<Long, String> map = new HashMap<>();
                        bizBillingAccounts.forEach(e -> map.put(e.getId(), userNameMap.get(e.getAdminSid())));
                        instances.forEach(deal -> {
                            // 获取所属分销商
                            if (BSS.equals(moduleType)) {
                                Long account = deal.getAccountSid();
                                String distributorName = distributorNameMap.get(account);
                                if (StringUtils.isBlank(distributorName)) {
                                    distributorName = DistributorAuthUtil.seachDistributor(account);
                                    String directSales = isUs ? "be run directly by a manufacturer" : "直营";
                                    distributorName = StringUtils.isBlank(distributorName) ? directSales : distributorName;
                                    distributorNameMap.put(account, distributorName);
                                }
                                deal.setDistributorName(distributorName);
                            }
                            deal.setUserName(StringUtils.isBlank(map.get(deal.getAccountSid())) ? "--" : map.get(deal.getAccountSid()));
                            deal.setFlowNo(deal.getFlowNo() == null ? "--" : deal.getFlowNo());
                            String tradeChannel = deal.getTradeChannel();
                            deal.setTradeTime(DateUtil.format(deal.getCreatedDt(),
                                DatePattern.NORM_DATETIME_FORMAT));
                            BigDecimal money = deal.getAmount() == null ? BigDecimal.ZERO : deal.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
                            //当前金额
                            deal.setAmount(
                                "acctCash".equals(deal.getTradeChannel()) || "platform".equals(deal.getTradeChannel()) ? money : BigDecimal.ZERO);
                            deal.setAccount(String.valueOf(deal.getAccountSid() == null ? "--" : deal.getAccountSid()));
                            deal.setEntityName(StrUtil.isEmpty(deal.getEntityName()) ? "--" : deal.getEntityName());
                            deal.setAccountName(StrUtil.isEmpty(deal.getAccountName()) ? "--" : deal.getAccountName());
                            deal.setType(DealTypeEnum.code2NameByI18n(deal.getType(), isUs));
                            deal.setTradeType(TradeTypeEnum.code2NameByI18n(deal.getTradeType(), isUs));
                            deal.setBalanceCreditAmount("accCredit".equals(deal.getTradeChannel()) ? money : BigDecimal.ZERO);
                            deal.setTradeChannel(RechargeTypeEnum.code2NameByI18n(deal.getTradeChannel(), isUs));
                            deal.setBalanceCash(deal.getBalanceCash() == null ? BigDecimal.ZERO : deal.getBalanceCash().setScale(2, BigDecimal.ROUND_HALF_UP));
                            deal.setTradeNo(deal.getTradeNo() == null ? "--" : deal.getTradeNo());
                            deal.setOrderNo(deal.getOrderNo() == null ? "--" : deal.getOrderNo());
                            deal.setBillNo(deal.getBillNo() == null ? "--" : deal.getBillNo());
                            deal.setRemark(deal.getRemark() == null ? "--" : deal.getRemark());
                            deal.setBillingCycle(StrUtil.isEmpty(deal.getBillingCycle()) ? "--" : deal.getBillingCycle());
                            deal.setBalance(deal.getBalance() == null ? BigDecimal.ZERO : deal.getBalance().setScale(2, BigDecimal.ROUND_HALF_UP));
                            deal.setCashAmount(deal.getCashAmount() == null ? BigDecimal.ZERO : deal.getCashAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                            if ("balanceCash".equals(tradeChannel)) {
                                deal.setCashAmount(money);
                            }
                            if ("deductBalanceCash".equals(tradeChannel)) {
                                deal.setDeductBalanceCash(money);
                            }
                            deal.setBalanceCredit(deal.getBalanceCredit() == null ? BigDecimal.ZERO : deal.getBalanceCredit().setScale(2, BigDecimal.ROUND_HALF_UP));
                            deal.setDeductBalanceCash(
                                deal.getDeductBalanceCash() == null ? BigDecimal.ZERO : deal.getDeductBalanceCash().setScale(2, BigDecimal.ROUND_HALF_UP));
                            deal.setCouponAmount(deal.getCouponAmount() == null ? BigDecimal.ZERO : deal.getCouponAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                            deal.setChargingType(deal.getChargingType());
                            if (TYPE_TWO.equals(typeList.get(deal.getOrderNo()))) {
                                deal.setChargingTypeForExcel("销售计费");
                            } else {
                                deal.setChargingTypeForExcel("正常计费");
                            }
                            if (isUs) {
                                deal.setEntityName(StringUtils.replaceEach(deal.getEntityName(), new String[]{"默认运营实体"}, new String[]{"Default operating entity"}));
                                deal.setRemark(this.remarkToUs(deal.getRemark()));
                                deal.setChargingTypeForExcel(StringUtils.replaceEach(deal.getChargingTypeForExcel(), new String[]{"正常计费", "销售计费"}, new String[]{"Normal billing", "Sales billing"}));
                            }
                            exportInstanceList.add(BeanUtil.toBean(deal, baseModel));
                        });


                        exportService.write(exportInstanceList);
                        num = num + size;
                    }
                } while (size > 0);
                exportService.finish();
            }
            zipOutputStream.closeEntry();
            zipOutputStream.close();

           // 压缩文件

            //minio上传
            long startTime = System.currentTimeMillis();
            log.info("ExportServiceImpl.doAsynExportBizAccountDetail 上传收支明细文件到minio 开始");
            StorageResult result = storageService.saveFile(FileUtil.getInputStream(zipFile.getFile()), StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                                                           true, null);
            long endTime = System.currentTimeMillis();
            log.info("ExportServiceImpl.doAsynExportBizAccountDetail 上传收支明细文件到minio耗时【{}】ms", endTime - startTime);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(destFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.BIZ_ACCOUNT_DEAL, taskId, authUserInfo);

            up.setStatus(1);
            up.setUpdatedDt(new Date());
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(user.getEntityId());
            this.bizDownloadMapper.updateById(up);

        } catch (Exception e) {
            up.setStatus(4);
            up.setUpdatedDt(new Date());
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doAsynExportBizAccountDetail_error : {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(zipFile);
            IOUtils.closeQuietly(zipOutputStream);
        }
    }

    private void exportBizAccountDetailWithCusTomInfo(QueryWrapper<BizAccountDeal> queryWrapper, Map<Long, String> customizationInfoMap,
                                                      ZipOutputStream zipOutputStream, String customInfoTemplate) {
        int allCount = bizAccountDealService.count(queryWrapper);
        if (allCount<=0){
            throw new BizException("生成异常：收支明细为空！");
        }

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet workbookSheet = workbook.createSheet("收支明细");

        List<CustomizationInfoTemplate> customizationInfoTemplates = JSON.parseArray(customInfoTemplate, CustomizationInfoTemplate.class);
        List<String> customKeys = customizationInfoTemplates.stream().map(CustomizationInfoTemplate::getAttrKey).collect(Collectors.toList());
        List<String> customNames = customizationInfoTemplates.stream().map(CustomizationInfoTemplate::getAttrName).collect(Collectors.toList());
        this.setSheetHeaderAndStyle(workbookSheet, workbook, customNames);
//        if (allCount > SHEET_LIMIT) {
//            int count =
//                    allCount % SHEET_LIMIT == 0 ? (int) (allCount / SHEET_LIMIT) : (int) (allCount / SHEET_LIMIT) + 1;
//            for (int i = 1; i < count; i++) {
//                SXSSFSheet sheet = workbook.createSheet("收支明细" + i);
//                this.setSheetHeaderAndStyle(sheet, workbook, customNames);
//            }
//        }

        Map<String,String> typeList = JedisUtil.INSTANCE.hgetall(ORDERNO_TO_CHARGINGTYPE);
        Map<Long, String> distributorNameMap = new HashMap<>();
        long num = 0L;
        long pageNum = 0L;
        long sheetIndex = 1L;
        int sheetNum = 1;
        List<BizAccountDeal> instances;
        do {
            pageNum++;
            IPage<BizAccountDeal> pages = bizAccountDealService.page(new Page<>(pageNum, 50000), queryWrapper);
            instances = pages.getRecords();

            log.info("ExportServiceImpl.doAsynExportBizAccountDetail 本次处理数：【{}】 已处理数: {}  总数： {}",
                instances.size(), num, allCount);

            this.disposeDeal(instances, BSS, distributorNameMap, customizationInfoMap, typeList);

            List<Map<String, String>> instanceMaps = this.convertListToMap(instances);
            if (num > sheetIndex * SHEET_LIMIT) {
                workbookSheet = workbook.createSheet("收支明细" + sheetIndex);
                this.setSheetHeaderAndStyle(workbookSheet, workbook, customNames);
                sheetNum = 1;
                sheetIndex++;
            }
            setContentData(instanceMaps, workbookSheet, customKeys, workbook, sheetNum);
            num = num + (long) instances.size();
            sheetNum = sheetNum + instances.size();
        } while (CollectionUtil.isNotEmpty(instances));

        try {
            workbook.write(zipOutputStream);
            workbook.close();
        } catch (IOException e) {
            throw new BizException("下载异常，异常原因：" + e.getMessage());
        }
    }

    private void setContentData(List<Map<String, String>> dataMaps, SXSSFSheet sheet, List<String> customKeys, SXSSFWorkbook workbook, int sheetNum) {
        //设置数据格式
        CellStyle style = workbook.createCellStyle();
        // 设置水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //开始填充数据
        for (int rowNum = sheetNum; rowNum <= dataMaps.size(); rowNum++) {
            Row dataRow = sheet.createRow(rowNum);
            //1.填充固定列数据（固定列数据可以抽象为一个对象）
            int size = CustomExportCellValueConstants.BSS_ACCOUNT_DEAL_FIELD.size() - 1;
            for (int i = 0; i < size; i++) {
                Cell cell = dataRow.createCell(i);
                cell.setCellStyle(style);
                cell.setCellValue(dataMaps.get(rowNum - 1).get(CustomExportCellValueConstants.BSS_ACCOUNT_DEAL_FIELD.get(i)));
            }
            //2.填充动态列数据（注意这里的起始列是固定列的最后一行加1）
            for (int i = size; i <= size + customKeys.size(); i++) {
                Cell cell = dataRow.createCell(i);
                cell.setCellStyle(style);
                if (i == size + customKeys.size()) {
                    cell.setCellValue(dataMaps.get(rowNum - 1).get(CustomExportCellValueConstants.BSS_ACCOUNT_DEAL_FIELD.get(size)));
                }else {
                    cell.setCellValue(dataMaps.get(rowNum - 1).get(customKeys.get(i - size)));
                }
            }
        }
    }

    private <T> List<Map<String, String>> convertListToMap(List<T> dataList) {
        List<Map<String, String>> dataMaps = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataList)) {
            return dataMaps;
        }
        dataList.forEach(data -> {
            Map<String, String> map = new HashMap<>();
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    if ("customizationInfo".equals(field.getName())) {
                        Object value = field.get(data);
                        if (Objects.nonNull(value)) {
                            List<CustomizationInfo> infoDetails = JSONArray.parseArray(value.toString(), CustomizationInfo.class);
                            for (CustomizationInfo infoDetail : infoDetails) {
                                map.put(infoDetail.getAttrKey(), infoDetail.getAttrValue());
                            }
                        }
                    } else {
                        Object value = field.get(data);
                        if (value != null) {
                            map.put(field.getName(), value.toString());
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            dataMaps.add(map);
        });
        return dataMaps;
    }

    private void setSheetHeaderAndStyle(SXSSFSheet sheet, SXSSFWorkbook workbook,
                                        List<String> customKeys) {
        //设置第1列单元格列宽,默认1/256字符宽度
        sheet.setColumnWidth(0, 20 * 256);

        // 3.头部设置样式
        CellStyle styleHeader = workbook.createCellStyle();
        // 设置水平居中
        styleHeader.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        styleHeader.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体加粗
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 9);
        font.setColor(IndexedColors.BLUE_GREY.getIndex());
        styleHeader.setFont(font);
        //下边框
        styleHeader.setBorderBottom(BorderStyle.DOUBLE);
        //左边框
        styleHeader.setBorderLeft(BorderStyle.THIN);
        //上边框
        styleHeader.setBorderTop(BorderStyle.THIN);
        //右边框
        styleHeader.setBorderRight(BorderStyle.THIN);
        //设置背景色
        styleHeader.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        styleHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //写表头（多个sheet类似）
        setHeaderData(styleHeader, sheet, customKeys);
    }

    private void setHeaderData(CellStyle styleHeader, SXSSFSheet sheet, List<String> customKeys) {
        //确定行：假如第一个sheet有4行（其中有一行是表头行），其余需要从库中查出数据
        //确定列：假如第一个sheet有8列，需要从库中查出数据
        //假如有3行，8列，其中8是固定的4列加上动态的列数4(这里4是动态的，可以是5，6，7或者8)
        Row row = sheet.createRow(0);

        //确定首行中的固定列，也就是表头固定列（excel中列是从第0列开始的）
        int size = CustomExportCellValueConstants.BSS_ACCOUNT_DEAL_VALUE.size() - 1;
        for (int j = 0; j < size; j++) {
            Cell cell = row.createCell(j);
            //设置表头样式
            cell.setCellStyle(styleHeader);
            sheet.setColumnWidth(j, 20 * 256);
            cell.setCellValue(CustomExportCellValueConstants.BSS_ACCOUNT_DEAL_VALUE.get(j));
        }

        for (int i = size; i <= size + customKeys.size(); i++) {
            Cell cell = row.createCell(i);
            //设置表头样式
            cell.setCellStyle(styleHeader);
            //设置每一列的宽度（每列的宽度只需要在首行中设置就行）
            sheet.setColumnWidth(i, 20 * 256);
            if (i == size + customKeys.size()) {
                cell.setCellValue("备注");
            }else {
                cell.setCellValue(customKeys.get(i - size));
            }
        }
    }

    private void disposeDeal(List<BizAccountDeal> instances, String moduleType, Map<Long, String> distributorNameMap,
                             Map<Long, String> customizationInfoMap, Map<String, String> typeList) {
        Map<Long, String> finalCustomizationInfoMap = customizationInfoMap;
        instances.forEach(deal -> {
            // 获取所属分销商
            if(BSS.equals(moduleType)){
                Long account = deal.getAccountSid();
                String distributorName = distributorNameMap.get(account);
                if (StringUtils.isBlank(distributorName)) {
                    distributorName = DistributorAuthUtil.seachDistributor(account);
                    distributorName = StringUtils.isBlank(distributorName) ? "直营" : distributorName;
                    distributorNameMap.put(account, distributorName);
                }
                deal.setDistributorName(distributorName);
                if (Objects.nonNull(finalCustomizationInfoMap)) {
                    deal.setCustomizationInfo(finalCustomizationInfoMap.get(deal.getOrgSid()));
                }
            }

            deal.setFlowNo(deal.getFlowNo() == null ? "--" : deal.getFlowNo());
            deal.setTradeTime(DateUtil.format(deal.getCreatedDt(),
                DatePattern.NORM_DATETIME_FORMAT));
            BigDecimal money = deal.getAmount() == null ? BigDecimal.ZERO : deal.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
            //当前金额
            deal.setAmount(
                "acctCash".equals(deal.getTradeChannel()) || "platform".equals(deal.getTradeChannel()) ? money : BigDecimal.ZERO);
            deal.setAccount(String.valueOf(deal.getAccountSid() == null ? "--" : deal.getAccountSid()));
            deal.setEntityName(StrUtil.isEmpty(deal.getEntityName()) ? "--" : deal.getEntityName());
            deal.setAccountName(StrUtil.isEmpty(deal.getAccountName()) ? "--" : deal.getAccountName());
            deal.setType(DealTypeEnum.codeFromName(deal.getType()));
            deal.setTradeType(TradeTypeEnum.codeFromName(deal.getTradeType()));
            deal.setBalanceCreditAmount("accCredit".equals(deal.getTradeChannel()) ? money : BigDecimal.ZERO);
            deal.setTradeChannel(RechargeTypeEnum.codeFromName(deal.getTradeChannel()));
            deal.setBalanceCash(deal.getBalanceCash() == null ? BigDecimal.ZERO : deal.getBalanceCash().setScale(2, BigDecimal.ROUND_HALF_UP));
            deal.setTradeNo(deal.getTradeNo() == null ? "--" : deal.getTradeNo());
            deal.setOrderNo(deal.getOrderNo() == null ? "--" : deal.getOrderNo());
            deal.setBillNo(deal.getBillNo() == null ? "--" : deal.getBillNo());
            deal.setRemark(deal.getRemark() == null ? "--" : deal.getRemark());
            deal.setBillingCycle(StrUtil.isEmpty(deal.getBillingCycle()) ? "--" : deal.getBillingCycle());
            deal.setBalance(deal.getBalance() == null ? BigDecimal.ZERO : deal.getBalance().setScale(2, BigDecimal.ROUND_HALF_UP));
            deal.setCashAmount(deal.getCashAmount() == null ? BigDecimal.ZERO : deal.getCashAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            deal.setBalanceCredit(deal.getBalanceCredit() == null ? BigDecimal.ZERO : deal.getBalanceCredit().setScale(2, BigDecimal.ROUND_HALF_UP));
            deal.setDeductBalanceCash(
                deal.getDeductBalanceCash() == null ? BigDecimal.ZERO : deal.getDeductBalanceCash().setScale(2, BigDecimal.ROUND_HALF_UP));
            deal.setChargingType(deal.getChargingType());
            if (TYPE_TWO.equals(typeList.get(deal.getOrderNo()))) {
                deal.setChargingTypeForExcel("销售计费");
            } else {
                deal.setChargingTypeForExcel("正常计费");
            }
        });
    }

    public HashMap<Long, String> getAccountDistributorNameMap(List<BizAccountDeal> result) {
        HashMap<Long, List<BizBillingAccount>> effectiveOrgIdAccountIdMap = new HashMap<>(16);
        HashMap<Long, String> accountDistributorNameMap = new HashMap<>(16);

        List<Long> accountSids = result.stream().map(BizAccountDeal::getAccountSid).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(accountSids)){
            return accountDistributorNameMap;
        }
        List<BizBillingAccount> bizBillingAccounts =
                bizBillingAccountService.list(Wrappers.lambdaQuery(BizBillingAccount.class)
                                                      .select()
                                                      .in(BizBillingAccount::getId, accountSids));
        List<Long> orgIds =
                bizBillingAccounts.stream()
                                  .filter(bizBillingAccount -> bizBillingAccount != null && bizBillingAccount.getOrgSid() != null)
                                  .map(BizBillingAccount::getOrgSid)
                                  .distinct()
                                  .collect(Collectors.toList());

        if(CollectionUtil.isEmpty(orgIds)){
            return accountDistributorNameMap;
    }
        List<Org> orgs = orgService.list(Wrappers.lambdaQuery(Org.class).select().in(Org::getOrgSid, orgIds));

        Map<Long, List<BizBillingAccount>> orgIdAccountIdMap =
                bizBillingAccounts.stream()
                                  .filter(bizBillingAccount -> bizBillingAccount != null && bizBillingAccount.getOrgSid() != null)
                                  .collect(Collectors.groupingBy(BizBillingAccount::getOrgSid));

        for (Org org : orgs) {
            effectiveOrgIdAccountIdMap.put(org.getParentId(), orgIdAccountIdMap.get(org.getOrgSid()));
        }
        if(CollectionUtil.isEmpty(effectiveOrgIdAccountIdMap)){
            return accountDistributorNameMap;
        }

        List<Long> orgParentSids =
                orgs.stream()
                    .filter(org -> org != null && org.getParentId() != null)
                    .map(Org::getParentId)
                    .collect(Collectors.toList());

        if(CollectionUtil.isEmpty(orgParentSids)){
            return accountDistributorNameMap;
        }
        List<Org> parentOrgs = orgService.list(Wrappers.lambdaQuery(Org.class).select().in(Org::getOrgSid, orgParentSids));

        for (Org distributorOrg : parentOrgs) {
            List<BizBillingAccount> list = effectiveOrgIdAccountIdMap.get(distributorOrg.getOrgSid());
            for (BizBillingAccount bizBillingAccount : list) {
                accountDistributorNameMap.put(bizBillingAccount.getId(), distributorOrg.getOrgName());
            }
        }
        return accountDistributorNameMap;
    }

    private BigDecimal scaleThree(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(3, BigDecimal.ROUND_HALF_UP);
        }

    private BigDecimal scaleTwo(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(2, BigDecimal.ROUND_HALF_UP);
        }

    private BigDecimal scaleTwoDown(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(2, BigDecimal.ROUND_DOWN);
        }

    private BigDecimal scaleFive(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value.setScale(5, BigDecimal.ROUND_HALF_UP);
    }


    private QueryWrapper<BizAccountDeal> criteria(DescribeDealsRequest request, User user, Long entityId) {
        if (user == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED,
                    WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        request.setCustomizationInfo(null);
        QueryWrapper<BizAccountDeal> queryWrapper = WrapperUtil.wrapQuery(request, "startTime", "endTime");
        RestResult restResult = DistributorAuthUtil.put(null, queryWrapper, user, 4);
        if (restResult == null) {
            queryWrapper.in("account_sid", Collections.singletonList(-1));
        }
        if (FROM_CONSOLE.equals(user.getRemark())) {
            queryWrapper.lambda().in(BizAccountDeal::getOrgSid, orgService.selectCustomerOrgSids(user.getOrgSid()));
        }
        if (StrUtil.isNotEmpty(request.getStartTime())) {
            queryWrapper.lambda().ge(BizAccountDeal::getCreatedDt, request.getStartTime());
        }
        if (StrUtil.isNotEmpty(request.getEndTime())) {
            queryWrapper.lambda().le(BizAccountDeal::getCreatedDt, request.getEndTime());
        }
        queryWrapper.orderByDesc("deal_time");
        return queryWrapper;
    }

    @Override
    public void doAsynGenerateMeteringFile(GenerateMeteringFileRequest request, String moduleType,
                                           Long taskId, User authUser, AuthUser authUserInfo) {
        log.info("ExportServiceImpl_doasynGenerateMeteringFile_start__{}_{}", JSON.toJSONString(request),
                 moduleType);
        authUser = AuthUtil.getAuthUser();
        String tenant = request.getTenant();
        String time = request.getTime();
        String exportType = request.getExportType();
        List<ExclusiveResourcePoolBill> exclusiveResourcePoolBills = new ArrayList<>();
        List<JobBill> jobBills = new ArrayList<>();
        List<StorageBill> storageBills = new ArrayList<>();
        //租户orgId和userId集合
        List<User> sysUserIds = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //1、根据查询Sid,账号为空时返回该用户下所有租户计量数据
        if (StringUtil.isNotEmpty(tenant)) {
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserMapper.selectOrgSidByAccount(tenant);
            User user1 = new User();
            BeanUtil.copyProperties(user,user1);
            sysUserIds.add(user1);
        } else {
            Long orgSid = authUser.getOrgSid();
            //根据分销商id获取所有租户的OrgsId
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountService.getBizBillingAccountsByOrgId(orgSid);
            if (CollectionUtil.isEmpty(bizBillingAccounts)) {
                BizDownload up = new BizDownload();
                up.setUpdatedDt(new Date());
                up.setDownloadId(taskId);
                up.setAccountId(authUser.getAccountId());
                up.setStatus(BIZ_DOWN_FAIL_STATUS);
                up.setRemark("下载异常，异常原因：该分销商没有找到租户信息，分销商orgSid:" + orgSid);
                this.bizDownloadMapper.updateById(up);
                return;
            }
            bizBillingAccounts.forEach(bizAccount -> {
                User user = new User();
                user.setOrgSid(bizAccount.getOrgSid());
                user.setUserSid(bizAccount.getAdminSid());
                sysUserIds.add(user);
            });
        }
        //2、查询该分销商租户信息
        for (User sysUser : sysUserIds) {
            List<DescribeGaapCostResponse> tempStorageBills = new ArrayList<>();
            List<StorageBill> tempStorages = new ArrayList<>();
            //调用接口查询账单信息
            DescribeGaapCostRequest queryRequest = new DescribeGaapCostRequest();
            queryRequest.setBillingCycle(time);
            queryRequest.setOrgSid(sysUser.getOrgSid());
            queryRequest.setHpcDrpCostFlag("1");
            log.info("orgSid:{},time:{},开始查询账单信息", sysUser.getOrgSid(), time);
            IPage<DescribeGaapCostResponse> responseIPage = instanceGaapCostService.listCalculateBills(queryRequest);
            List<DescribeGaapCostResponse> records = responseIPage.getRecords();

            //查询弹性文件相关信息
            DescribeShareRequest shareRequest = new DescribeShareRequest();
            shareRequest.setCreatedOrgSid(sysUser.getOrgSid());
            RestResult restResult = shareService.getShareListByOrgId(shareRequest);
            List<ResShare> shareList = new LinkedList<>();
            if (restResult != null && restResult.getData()!=null) {
                String jsonStr = JSONUtil.toJsonStr(restResult.getData());
                shareList = JSONUtil.toList(JSONUtil.parseArray(jsonStr), ResShare.class);
            }

            //3、构建存储账单、作业账单、专属资源池3个sheet
            if (CollectionUtil.isNotEmpty(records)) {
                for (DescribeGaapCostResponse describeGaapCost : records) {
                    String productName = describeGaapCost.getProductName();
                    String productCode = describeGaapCost.getProductCode();
                    if (StringUtil.containsIgnoreCase(productName, HPC)) {
                        //3.1、只采集有作业信息的数据，此数据包含HPC专属资源池和HPC共享资源池
                        if (StringUtil.isNotEmpty(describeGaapCost.getJobId())) {
                            describeGaapCost.getProductName();
                            //构建作业账单
                            JobBill jobBill = new JobBill();
                            BeanUtils.copyProperties(describeGaapCost, jobBill);
                            jobBill.setBillTime(time);
                            if (Objects.nonNull(describeGaapCost.getInstanceId()) && describeGaapCost.getInstanceId()
                                                                                                     .startsWith("hws")) {
                                jobBill.setResourceType(describeGaapCost.getInstanceId());
                            }
                            jobBill.setSubmitTime(DateUtil.format(describeGaapCost.getSubmitTime(), format));
                            jobBill.setStartTime(DateUtil.format(describeGaapCost.getStartTime(), format));
                            jobBill.setEndTime(DateUtil.format(describeGaapCost.getEndTime(), format));
                            DefaultUtil.setFieldValueNotNull(jobBill);
                            jobBills.add(jobBill);
                            continue;
                        }
                        if ((StringUtil.containsIgnoreCase(productName, "HPC专属资源池")
                                || StringUtil.containsIgnoreCase(productCode, ProductCodeEnum.HPC_DRP.getProductType()))
                                && describeGaapCost.getPretaxGrossAmount().compareTo(BigDecimal.ZERO) > 0) {
                            //3.2构建专属资源池账单
                            ExclusiveResourcePoolBill exclusiveResourcePoolBill = new ExclusiveResourcePoolBill();
                            BeanUtils.copyProperties(describeGaapCost, exclusiveResourcePoolBill);
                            exclusiveResourcePoolBill.setBillType(billTypeFormat(exclusiveResourcePoolBill.getBillType()));
                            exclusiveResourcePoolBill.setBillTime(time);
                            String instanceId = describeGaapCost.getInstanceId();
                            if (StringUtils.isEmpty(instanceId)) {
                                continue;
                            }
                            //过滤掉因为续订、扩容等情况产生相同的数据
                            long count = exclusiveResourcePoolBills.stream().filter(e -> {
                                String instanceName = e.getInstanceName();
                                return instanceId.equals(instanceName.substring(instanceName.indexOf('/') + 1));
                            }).count();
                            if (count > 0) {
                                continue;
                            }
                            exclusiveResourcePoolBill.setInstanceName(
                                    exclusiveResourcePoolBill.getInstanceName() + "/" + instanceId);

                            Long clusterId = describeGaapCost.getClusterId();
                            if (Objects.isNull(clusterId)){
                                ServiceOrder serviceOrder = serviceOrderMapper.selectOne(new QueryWrapper<ServiceOrder>().eq("order_sn", describeGaapCost.getOrderId()));
                                if (serviceOrder != null) {
                                    clusterId = serviceOrder.getClusterId();
                                }

                            }
                            //查询专属资源池集群详细信息
                            ResHpcClusterDetailVO hpcClusterDetail = null;
                            try {
                                if (Objects.nonNull(clusterId)) {
                                    hpcClusterDetail = sfProductResourceService.getHpcClusterDetailByClusterId(
                                            describeGaapCost.getOrgSid(), clusterId, sysUser.getUserSid());
                                }
                            } catch (cn.com.cloudstar.rightcloud.common.exception.BizException e) {
                                //没有查询到HPC专属资源池其他相关信息,打印日志，不做操作
                                log.info(e.getMessage());
                            }

                            if (hpcClusterDetail != null) {
                                //专属资源池账单赋值
                                setExclusiveResourcePoolBill(hpcClusterDetail, exclusiveResourcePoolBill, format);

                                //查询退订时间
                                String orderSn = describeGaapCost.getOrderId();
                                ServiceOrderDetail detail = orderMapper.getEndTimeByOrderSn(orderSn);
                                if (detail != null && detail.getEndTime() != null) {
                                    exclusiveResourcePoolBill.setClusterUnsubTime(DateUtil.format(detail.getEndTime(), format));
                                }
                            }
                            DefaultUtil.setFieldValueNotNull(exclusiveResourcePoolBill);
                            exclusiveResourcePoolBills.add(exclusiveResourcePoolBill);

                            //3.3构建存储配额,hpc专属资源池对应相关的存储信息
                            setStorageBill(tempStorages, clusterId, shareList, format,time);
                        } else if (StringUtil.containsIgnoreCase(productName, "弹性文件服务")) {
                            tempStorageBills.add(describeGaapCost);
                        }
                    }
                }
                setStorageBills(tempStorageBills,tempStorages,format);
                storageBills.addAll(tempStorages);
            }
        }
        exportFile(moduleType, exclusiveResourcePoolBills, jobBills, storageBills, taskId, time, authUser,authUserInfo.getAcceptLanguage());
    }

    @Override
    public void doResAnalysisExportDetail(DateSummerRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {

        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        //分组
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("accountName").first("accountName").as("accountName"),
                Aggregation.sort(Sort.Direction.DESC, "accountName")
        );
        List<CustomizationEntity> accountNameList =
                mongoTemplate.aggregate(aggregation, "biz_customization_entity", CustomizationEntity.class).getMappedResults();
        this.doCriteria(request, criteria);
        List<ProductResponse> productResponses = preparationList(criteria, accountNameList);
        if (CollectionUtil.isEmpty(productResponses)) {
            log.info("ExportServiceImpl-doAsynExportCycle-客户资源分析为空，请核查数据");
            up.setStatus(4);
            up.setRemark("客户资源分析为空，请核查数据!");
            this.bizDownloadMapper.updateById(up);
            return;
        }
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String nameSuffix = fileNameSuffix();
        String taskName = ExportTypeEnum.RESOURCE_ANALYSIS.getNameIsUs(acceptLanguage);
        String destFileName = taskName + nameSuffix + XLSX;
        String zipFileName = taskName + nameSuffix + ZIP;
        String xlsTemplateFileName = WebUtil.getHeaderAcceptLanguage(acceptLanguage) ? "template/summary-export-template-us.xlsx" : "template/summary-export-template.xlsx";

        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        InputStream compress = null;
        try {
            out = new ByteArrayOutputStream();
            ExcelUtil.write()
                     .buildWriter(out, xlsTemplateFileName)
                     .buildSheet()
                     .fill(productResponses)
                     .finish();

            //压缩密码
            String password = secureRandomUUID().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            in = new ByteArrayInputStream(out.toByteArray());
            // 压缩文件
            compress= ZipUtil.compress(in, destFileName, obfuscatePassword, true);

            //发送压缩密码至用户
            AuthUser user = RequestContextUtil.getAuthUserInfo();
            if (!ObjectUtils.isEmpty(user) && !ObjectUtils.isEmpty(user.getEmail())) {
                SendZipCompressPasswordRequest sendRequest = new SendZipCompressPasswordRequest();
                sendRequest.setUserSid(user.getUserSid());
                sendRequest.setFileName(zipFileName);
                sendRequest.setPassword(password);
                log.info("客户资源分析发送压缩密码给用户,用户id[{}]", user.getUserSid());
                feignService.sendZipCompressPassword(sendRequest);
            }

            //minio上传
            StorageResult result = storageService.saveFile(compress, StoragePathEnum.EXCEL.getPath("ResourceAnalysis"), zipFileName, true,
                                                           true, null);
            log.info("客户资源分析minio上传完成！");
            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(destFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.RESOURCE_ANALYSIS, up.getDownloadId(), authUserInfo);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);

        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("ExportServiceImpl_doResAnalysisExportDetail_error : {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(compress);
            IOUtils.closeQuietly(in);
        }
    }

    @Override
    public void doAsynExportMaResources(DescribeProductResourceRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUserInfo.getAcceptLanguage());
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(
                ProductCodeEnum.MODEL_ARTS.getProductType());
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
        if (Objects.nonNull(authUserInfo.getOrgSid()) || !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        IPage<DescribeProductResourceResponse> responsePage = sfProductResourceService.exportListResources(request);
        List<DescribeProductResourceResponse> records = responsePage.getRecords();
        records.forEach(r ->{
            String configDesc = r.getConfigDesc();
            JsonNode serviceConfigJsonNode = JsonUtil.fromJson(configDesc);
            JsonNode productConfigDesc = serviceConfigJsonNode.findPath("productConfigDesc");
            JsonNode currentDescJsonNode = productConfigDesc.findPath("currentConfigDesc");
            if (currentDescJsonNode != null) {
                String descArryStr = currentDescJsonNode.textValue();
                JsonNode jsonNode = JsonUtil.fromJson(descArryStr);
                if(Objects.nonNull(jsonNode)){
                    String str = buildConfig(jsonNode, isUs);
                    r.setConfigDesc(str);
                }
            }
            r.setFreezingStrategy(FreezingStrategyEnum.code2NameByI18n(r.getFreezingStrategy(), isUs));
            if (isUs) {
                r.setName(StringUtils.replace(r.getName(), "AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool"));
                r.setProductName(StringUtils.replaceEach(r.getProductName(), new String[]{"AI开发平台共享资源池","AI开发平台专属资源池"}, new String[]{"Ascending Modelarts Shared Resource Pool ModelArts", "Ascend Modelarts Exclusive Resource Pool"}));
                r.setChargeType(ChargeTypeEnum.convertDesc(r.getChargeType(), isUs));
                r.setOrgName(StringUtils.replace(r.getOrgName(), "默认项目", "Default Project"));
                r.setDistributorName(StringUtils.replace(r.getDistributorName(), "直营", "be run directly by a manufacturer"));
                r.setStatus(StringUtils.replaceEach(r.getStatus(), new String[]{"即将到期","正常", "错误", "申请中", "已到期", "删除中", "已删除", "续订中", "已退订", "已冻结", "扩容中"}, new String[]{"About to expire","Normal", "Mistake", "Applying", "Have expired", "Deleting", "Removed", "Under renewal", "Unsubscribed", "Frozen", "Expanding"}));
            }
        });

        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());

        //设置响应
        InputStream inExcel = null;
        InputStream inZip = null;
        ByteArrayOutputStream outExcel = null;
        try {
            //导出到excel
            //安全随机数
            String format = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                    + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
            String fileName = isUs ? "List of Modelarts services" : "Modelarts服务列表";
            String destFileName = fileName + format + ".xlsx";
            String zipFileName = fileName + format + ".zip";
            String xlsTemplateFileName = isUs ? "template/Modelarts服务列表-us.xlsx" : "template/Modelarts服务列表.xlsx";

            log.info("getListResources:{}",records.size());
            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(records)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath("maResources"), zipFileName, true,
                    true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.EXPORT_RESOURCES_AI, up.getDownloadId(), authUserInfo);

            up.setUpdatedDt(new Date());
            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);

        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error("SfProductResourceController.getListResources error: ", e);
        } finally {
            IOUtils.closeQuietly(inExcel);
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
        }
    }

    @Override
    public void doAsynExportInvoice(DescribeInvoicesRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        String type = "invoice";
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        if ("console".equals(moduleType)) {
            if (authUserInfo == null) {
                throw new BizException("获取当前登录用户失败");
            }
            if (Objects.isNull(authUserInfo.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00023));
            }
            BizBillingAccount bizBillingAccount;
            //根据传入的accountId
            if(request.getAccountId() != null){
                bizBillingAccount = iBizBillingAccountServiceImp.getById(request.getAccountId());
            } else {
                //查询accountId
                bizBillingAccount = iBizBillingAccountServiceImp
                        .getBizBillingAccountByOrgId(authUserInfo.getOrgSid());
            }
            if (Objects.isNull(bizBillingAccount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00024));
            }
            if(!bizBillingAccount.getOrgSid().equals(authUserInfo.getOrgSid())){
                throw new BizException("越权导出");
            }
            criteria.getCondition().put("accountId", bizBillingAccount.getId());
        }
        else{
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())
                    && !UserTypeConstants.CONSOLE.equals(authUserInfo.getRemark())){
                List<Long> orgSids = orgService.list(new QueryWrapper<Org>().lambda().eq(Org::getParentId, authUserInfo.getOrgSid()))
                        .stream().map(Org::getOrgSid).collect(Collectors.toList());
                List<Long> accountIds = iBizBillingAccountServiceImp.list(new QueryWrapper<BizBillingAccount>().lambda().in(BizBillingAccount::getOrgSid, orgSids))
                        .stream().map(BizBillingAccount::getId).collect(Collectors.toList());
                criteria.getCondition().put("accountIds", accountIds);
            }
            criteria.getCondition().put("entityId", authUserInfo.getEntityId());
        }

        // 配置默认排序
        if (Strings.isNullOrEmpty(request.getSortorder())) {
            if (authUserInfo == null) {
                throw new BizException("获取当前登录用户失败");
            }
            if (authUserInfo.getAdminFlag() && "bss".equals(moduleType)) {
                // 管理员查询，以状态，时间排序
                criteria.setOrderByClause("A.INVOICE_STATUS DESC,A.created_dt Asc");
            } else {
                // 普通用户时间逆序
                criteria.setOrderByClause("A.created_dt desc");
            }
        }
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUserInfo.getAcceptLanguage());

        List<InvoiceDTO> invoiceDTOS = bizInvoiceServiceImpl.exportInvoice(criteria);
        invoiceDTOS.forEach(invoice -> {
            invoice.setDistributorName(isUs ? "be run directly by a manufacturer" : "直营");
            if (Objects.nonNull(invoice.getDistributorId())) {
                invoice.setDistributorName(distributorService.getById(invoice.getDistributorId()).getName());
            }
            invoice.setBankAccount(CrytoUtilSimple.decrypt(invoice.getBankAccount()));
            invoice.setInvoiceStatusName(InvoiceStatusEnum.code2NameByI18n(invoice.getInvoiceStatus(), isUs));
            invoice.setInvoiceTypeName(INVOICE_TYPE.get(invoice.getInvoiceType()).get(isUs));
            invoice.setInvoiceMethodName(InvoiceMethodEnum.code2NameByI18n(invoice.getInvoiceMethod(), isUs));
            if (ObjectUtils.isEmpty(invoice.getRemark())) {
                invoice.setRemark("--");
            }
            invoice.setTaxId(StringUtil.isNotBlank(invoice.getTaxId())?invoice.getTaxId():"--");
            invoice.setBankAccount(StringUtil.isNotBlank(invoice.getBankAccount()) ? CrytoUtilSimple.decrypt(invoice.getBankAccount()) : "--");
            invoice.setRegisterAddress(StringUtil.isNotBlank(invoice.getRegisterAddress()) ? CrytoUtilSimple.decrypt(invoice.getRegisterAddress()) : "--");
            invoice.setRegisterPhone(StringUtil.isNotBlank(invoice.getRegisterPhone()) ? CrytoUtilSimple.decrypt(invoice.getRegisterPhone()) : "--");
            invoice.setDepositBank(StringUtil.isNotBlank(invoice.getDepositBank())?invoice.getDepositBank():"--");
        });
        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;

        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        //设置响应
        try {
            //导出到excel
            //安全随机数
            String format = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                    + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
            String destFileName = "发票列表" + format + ".xlsx";
            String zipFileName = "发票列表" + format + ".zip";
            String xlsTemplateFileName = "template/invoice-deal.xlsx";
            if ("console".equals(moduleType)) {
                xlsTemplateFileName = "template/invoiceConsole-deal.xlsx";
                destFileName = "我的发票" + format + ".xlsx";
            }
            if (isUs) {
                destFileName = StringUtils.replaceEach(destFileName, new java.lang.String[]{"发票列表", "我的发票"}, new java.lang.String[]{"invoice_list", "my_invoice"});
                xlsTemplateFileName = StringUtils.replace(xlsTemplateFileName, ".xlsx", "-us.xlsx");
                zipFileName = StringUtils.replace(zipFileName, "发票列表", "invoice_list");
            }

            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(invoiceDTOS)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                    true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.INVOICE, up.getDownloadId(), authUserInfo);

            up.setUpdatedDt(new Date());
            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);

        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
            IOUtils.closeQuietly(inZip);
        }
    }

    @Override
    public void doAsynPlatformDiscount(DescribeDiscountRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        String type = "platform_discount";
        if (("all").equals(request.getProductScopeLike())) {
            request.setProductScopeLike(null);
        }
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);

        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        String key = AuthConstants.USER_PERMISSION_CACHE + authUser.getUserSid();
        //获取当前用户权限
        List<String> cacheModules = JSONUtil.toList(redisTemplate.opsForValue().get(key), String.class);
        //防止越权
        boolean b = ObjectUtils.isEmpty(cacheModules)
                || ("platform".equals(request.getDiscountType()) && !cacheModules.contains(AuthModule.BR.BR01.BR0106));
        if (b || ("customer".equals(request.getDiscountType()) && !cacheModules.contains(AuthModule.BR.BR02.BR0206))) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        request.setDiscountType(Objects.nonNull(request.getDiscountType()) ? request.getDiscountType().trim() : null);
        QueryWrapper<BizDiscount> queryWrapper = WrapperUtil.wrapQuery(request);
        queryWrapper.eq("entity_id", RequestContextUtil.getEntityId());
        List<BizDiscount> list = bizDiscountService.list(queryWrapper);
        list.forEach(this::convertToDesc);

        Pattern pattern = Pattern.compile("[0-9]*");
        List<DiscountExportResponse> exportResponses = list.stream().map(bizDiscount -> {
            DiscountExportResponse discountExportResponse = new DiscountExportResponse();
            BeanUtil.copyProperties(bizDiscount, discountExportResponse);
            discountExportResponse.setProductScopes(discountExportResponse.getProductScopes().replace("[","").replace("]",""));
            discountExportResponse.setCloudEnvScopes(discountExportResponse.getCloudEnvScopes().replace("[","").replace("]",""));
            discountExportResponse.setDescription(StringUtil.isBlank(discountExportResponse.getDescription())?"--":discountExportResponse.getDescription());
            Matcher isNum = pattern.matcher(discountExportResponse.getScopeValue().charAt(discountExportResponse.getScopeValue().length()-1)+"");
            if (!isNum.matches()){
                discountExportResponse.setScopeValue(discountExportResponse.getScopeValue() + (isUs ? "Unlimited" : "无限制"));
            }
            if (isUs) {
                discountExportResponse.setScopeType(StringUtils.replaceEach(discountExportResponse.getScopeType(), new String[]{"数量","金额","时间","无限制"}, new String[]{"Quantity","Amount","Time","Unlimited"}));
                discountExportResponse.setProductScopes(StringUtils.replaceEach(discountExportResponse.getProductScopes(), new String[]{"弹性裸金属", "对象存储OBS", "AI开发平台共享资源池", "AI开发平台专属资源池", "云硬盘"}, new String[]{"Elastic bare metal","Object Storage OBS","Shengteng Modelarts Shared Resource Pool","Shengteng Modelarts Exclusive Resource Pool","Cloud Disk Service"}));
            }
            return discountExportResponse;
        }).collect(Collectors.toList());

        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;
        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        String taskName = ExportTypeEnum.PLATFORM_DISCOUNT.getNameIsUs(acceptLanguage);
        //设置响应
        try {
            //导出到excel
            //安全随机数
            String format = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                    + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
            String destFileName = taskName + format + ".xlsx";
            String zipFileName = taskName + format + ".zip";
            String xlsTemplateFileName = "template/platform-discount-template.xlsx";
            if (isUs) {
                xlsTemplateFileName = "template/platform-discount-template-us.xlsx";
            }
            //写入sheet
            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(exportResponses)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                    true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.PLATFORM_DISCOUNT, up.getDownloadId(), authUserInfo);

            up.setUpdatedDt(new Date());
            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark((isUs ? "Download exception, abnormal cause:" : "下载异常，异常原因：") + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            e.printStackTrace();
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
        }
    }

    /**
     * 转换描述
     * @param bizDiscount
     */
    private BizDiscount convertToDesc(BizDiscount bizDiscount) {
        bizDiscount.setScopeType(ScopeTypeEnum.codeFromName(bizDiscount.getScopeType()));
        bizDiscount.setProductScopes(ProductComponentEnum.transformDesc(StrUtil.splitToArray(bizDiscount.getProductScope(), StrUtil.COMMA)));
        bizDiscount.setCloudEnvScopes(CloudEnvEnum.transformDesc(StrUtil.splitToArray(bizDiscount.getCloudEnvScope(), StrUtil.COMMA)));
        bizDiscount.setOriginType(OriginTypeEnum.codeFromName(bizDiscount.getOriginType()));
        return bizDiscount;
    }

    @Override
    public void doAsynCustomerDiscount(DescribeDiscountRequest request, String moduleType, Long taskId, AuthUser authUserInfo) {
        String type = "customer_discount";
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        criteria.put("orderByClause", "created_dt desc");
        if (request.getUserSid() != null) {
            List<Long> acountList = new ArrayList<Long>();
            acountList.add(request.getUserSid());
            criteria.getCondition().put("accountIds", acountList);
        }
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);

        List<BizBillingAccount> accountIPage = bizBillingAccountService.getAccountDiscountListExport(criteria);
        accountIPage.forEach(account -> {
            account.getDiscounts().forEach(this::convertToDesc);
        });
        Pattern pattern = Pattern.compile("[0-9]*");
        List<DiscountExportResponse> exportResponses = new ArrayList<>();
        accountIPage.forEach(bizBillingAccount -> {
            for (BizDiscount discount : bizBillingAccount.getDiscounts()) {
                BigDecimal creditLine = Objects.isNull(bizBillingAccount.getCreditLine()) ? BigDecimal.ZERO : bizBillingAccount.getCreditLine();
                DiscountExportResponse discountExportResponse = new DiscountExportResponse();
                BeanUtil.copyProperties(discount, discountExportResponse);
                discountExportResponse.setAccountName(bizBillingAccount.getAccountName());
                discountExportResponse.setBalance(bizBillingAccount.getBalance().setScale(2, RoundingMode.HALF_UP));
                discountExportResponse.setCreditLine(creditLine.setScale(2, RoundingMode.HALF_UP));
                discountExportResponse.setProductScopes(discountExportResponse.getProductScopes().replace("[","").replace("]",""));
                discountExportResponse.setCloudEnvScopes(discountExportResponse.getCloudEnvScopes().replace("[","").replace("]",""));
                discountExportResponse.setDescription(StringUtil.isBlank(discountExportResponse.getDescription())?"--":discountExportResponse.getDescription());
                Matcher isNum = pattern.matcher(discountExportResponse.getScopeValue().charAt(discountExportResponse.getScopeValue().length()-1)+"");
                if (!isNum.matches()){
                    discountExportResponse.setScopeValue(discountExportResponse.getScopeValue() + (isUs ? "Unlimited" : "无限制"));
                }
                if (isUs) {
                    discountExportResponse.setScopeType(StringUtils.replaceEach(discountExportResponse.getScopeType(), new String[]{"数量","金额","时间","无限制"}, new String[]{"Quantity","Amount","Time","Unlimited"}));
                    discountExportResponse.setProductScopes(StringUtils.replaceEach(discountExportResponse.getProductScopes(), new String[]{"弹性裸金属", "对象存储OBS", "AI开发平台共享资源池", "AI开发平台专属资源池", "云硬盘"}, new String[]{"Elastic bare metal","Object Storage OBS","Shengteng Modelarts Shared Resource Pool","Shengteng Modelarts Exclusive Resource Pool","Cloud Disk Service"}));
                }
                exportResponses.add(discountExportResponse);
            }
        });

        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;
        //设置响应
        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        try {
            //导出到excel
            String taskName = ExportTypeEnum.CUSTOMER_DISCOUNT_.getNameIsUs(acceptLanguage);
            String format = new SimpleDateFormat("yyyyMMddHHmm").format(new Date());
            String destFileName = taskName+ format + ".xlsx";
            String zipFileName = taskName + format + ".zip";
            String xlsTemplateFileName = "template/customer-discount-template.xlsx";
            if (isUs) {
                xlsTemplateFileName = "template/customer-discount-template-us.xlsx";
            }

            //写入sheet
            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(exportResponses)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());

            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                    true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.CUSTOMER_DISCOUNT, up.getDownloadId(), authUserInfo);

            up.setUpdatedDt(new Date());
            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
        }
    }

    /**
     * 构建配置
     *
     * @param jsonNode json节点
     * @return {@link String}
     */
    private String buildConfig(JsonNode jsonNode, boolean en) {
        StringBuilder builder = new StringBuilder();
        for (JsonNode node : jsonNode) {
            ObjectNode objectNode = (ObjectNode)node;
            String value = objectNode.get("value").toString();
            if (en) {
                value = StringUtils.replaceEach(value, new String[] {"物理资源池", "逻辑资源池", "开发环境/训练", "部署上线", "包年包月", "个月", "年"}, new String[] {"Physical Resource Pool", "Logical Resource Pool", "Development Environment/Training", "Deploy and go live", "Yearly Subscription", "month", "years"});
            }
            String label = objectNode.get("label").textValue();
            switch (label) {
                case "资源池类型":
                    builder.append(en ? "Resource Pool Types:" : "资源池类型：").append(value).append((char) 10);
                    continue;
                case "作业类型":
                    builder.append(en ? "Job Type:" : "作业类型：").append(value).append((char) 10);
                    continue;
                case "计费类型":
                    builder.append(en ? "Charge Type:" : "计费类型：").append(value).append((char) 10);
                    continue;
                case "架构":
                    builder.append(en ? "architecture:" : "架构：").append(value).append((char) 10);
                    continue;
                case "计算规格":
                    builder.append(en ? "Calculation specification:" : "计算规格：").append(value).append((char) 10);
                    continue;
                case "数量":
                    builder.append(en ? "Number:" : "数量：").append(value).append((char) 10);
                    continue;
                case "时长":
                    builder.append(en ? "Duration:" : "时长：").append(value).append((char) 10);
                    continue;
                case "备注":
                    builder.append(en ? "Remark:" : "备注：").append(value).append((char) 10);
                    continue;
                case "资源池到期策略":
                    builder.append(en ? "Resource pool expiration policy:" : "资源池到期策略：").append(FreezingStrategyEnum.convertNameByI18n(value, en)).append((char) 10);
                    continue;
                default:
                    continue;
            }
        }
        return builder.toString();
    }

    private void doCriteria(DateSummerRequest request, org.springframework.data.mongodb.core.query.Criteria criteria) {
        if (Objects.nonNull(request.getAccountName())) {
            criteria.and("accountName").is(request.getAccountName());
        }
        if (Objects.nonNull(request.getStarTime())) {
            String starTime = request.getStarTime().replace("-", "");
            String endTime = request.getEndTime().replace("-", "");
            criteria.and("dateFormat").gte(starTime).lte(endTime);
        }
    }

    private List<ProductResponse> preparationList(org.springframework.data.mongodb.core.query.Criteria criteria, List<CustomizationEntity> accountNameList) {
        //集合数据
        List<CustomizationEntity> customizationEntities = mongoTemplate.find(Query.query(criteria), CustomizationEntity.class);
        //根据名称分组
        Map<String, List<CustomizationEntity>> collect =
                customizationEntities.stream().collect(Collectors.groupingBy(CustomizationEntity::getAccountName));
        List<ProductResponse> finalList = new ArrayList<>();
        Date before = DateUtils.addHours(new Date(), -1);
        for (CustomizationEntity name : accountNameList) {
            Long userSid = userMapper.selectIdByUserAccount(name.getAccountName());
            //根据客户名称查询obs最新数据
            ProductResponse productResponse = new ProductResponse();
            String accountName = name.getAccountName();
            List<CustomizationEntity> typeList = collect.get(accountName);
            if (CollectionUtil.isEmpty(typeList)) {
                continue;
            }
            Map<String, BigDecimal> decimalMap =
                    typeList.stream().collect(Collectors.groupingBy(CustomizationEntity::getType, Collectors
                            .mapping(CustomizationEntity::getValue, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            //拼装返回
            productResponse.setAccountName(accountName);
            if (Objects.nonNull(decimalMap.get("DRP"))) {
                productResponse.setDrpUsed(decimalMap.get("DRP").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setDrpUsed(BigDecimal.ZERO.setScale(2));
            }
            if (Objects.nonNull(decimalMap.get("MODELARTS"))) {
                productResponse.setShareUsed(decimalMap.get("MODELARTS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setShareUsed(BigDecimal.ZERO.setScale(2));
            }
            String key = accountName + OBS;
            String value = redisTemplate.opsForValue().get(key);
            if (Objects.nonNull(value)) {
                productResponse.setObsUsed(value);
            } else {
                doList(userSid, before, productResponse);
            }

            String evskey = accountName + EVS;
            String evsValue = redisTemplate.opsForValue().get(evskey);
            if (Objects.nonNull(evsValue)) {
                productResponse.setEvsUsed(evsValue);
            } else {
                doEvsList(userSid, before, productResponse);
            }

            if (Objects.nonNull(decimalMap.get("work"))) {
                productResponse.setWorkUsed(decimalMap.get("work").setScale(0, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setWorkUsed(BigDecimal.ZERO);
            }
            if (Objects.nonNull(decimalMap.get("billing"))) {
                productResponse.setBillingUsed(decimalMap.get("billing").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_DRP"))) {
                productResponse.setDrpBillingUsed(decimalMap.get("billing_DRP").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setDrpBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_MODELARTS"))) {
                productResponse.setShareBillingUsed(decimalMap.get("billing_MODELARTS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setShareBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_OBS"))) {
                productResponse.setObsBillingUsed(decimalMap.get("billing_OBS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setObsBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            if (Objects.nonNull(decimalMap.get("billing_EVS"))) {
                productResponse.setEvsBillingUsed(decimalMap.get("billing_EVS").setScale(2, BigDecimal.ROUND_DOWN));
            } else {
                productResponse.setEvsBillingUsed(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN));
            }
            finalList.add(productResponse);
        }
        return finalList;
    }

    /**
     * 查询obs当前使用量
     *
     * @param userSid
     * @param before
     * @param productResponse
     */
    private void doList(Long userSid, Date before, ProductResponse productResponse) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria1 =
                new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria1.put("status", "used");
        List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria1);
        List<HcsoUser> hcsoUserList = hcsoUsers.stream().filter(h -> Objects.nonNull(h.getRefUserId()) && h.getRefUserId().equals(userSid)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(hcsoUserList)) {
            HcsoUser hcsoUser = hcsoUserList.get(0);
            Query query = new Query();
            org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
            criteria2.and("user_id").is(hcsoUser.getObsProjectId())
                     .and("billing_success").is("YES")
                     .and("archived_time").gte(before);
            query.addCriteria(criteria2);
            List<ObsCollectorArchived> obsCollectorArchiveds = mongoTemplate.find(query, ObsCollectorArchived.class);
            //每小时的obs使用量
            Long aLong = 0L;
            if (CollectionUtil.isNotEmpty(obsCollectorArchiveds)) {
                for (ObsCollectorArchived collectorArchived : obsCollectorArchiveds) {
                    String value = collectorArchived.getAccumulate_factor_value();
                    aLong = aLong + Long.valueOf(value);
                }
            }
            BigDecimal bigDecimal = bytes2Gb(aLong, 2);
            productResponse.setObsUsed(bigDecimal.toString());
        } else {
            productResponse.setObsUsed(BigDecimal.ZERO.setScale(2).toString());
        }
    }

    /**
     * 查询evs当前使用量
     *
     * @param userSid
     * @param before
     * @param productResponse
     */
    private void doEvsList(Long userSid, Date before, ProductResponse productResponse) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria1 =
                new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria1.put("status", "used");
        List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria1);
        List<HcsoUser> hcsoUserList = hcsoUsers.stream().filter(h -> Objects.nonNull(h.getRefUserId()) && h.getRefUserId().equals(userSid)).collect(Collectors.toList());        if (CollectionUtil.isNotEmpty(hcsoUserList)) {
            HcsoUser hcsoUser = hcsoUserList.get(0);
            Query query = new Query();
            org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
            criteria2.and("user_id").is(hcsoUser.getObsProjectId())
                     .and("billing_success").is("YES")
                     .and("archived_time").gte(before);
            query.addCriteria(criteria2);
            List<EvsCollectorArchived> evsCollectorArchiveds = mongoTemplate.find(query, EvsCollectorArchived.class);
            //每小时得obs使用量
            Long aLong = 0L;
            if (CollectionUtil.isNotEmpty(evsCollectorArchiveds)) {
                for (EvsCollectorArchived collectorArchived : evsCollectorArchiveds) {
                    String value = collectorArchived.getAccumulate_factor_value();
                    aLong = aLong + Long.valueOf(value);
                }
            }
            BigDecimal bigDecimal = bytes2Gb(aLong, 2);
            productResponse.setEvsUsed(bigDecimal.toString());
        } else {
            productResponse.setEvsUsed(BigDecimal.ZERO.setScale(2).toString());
        }
    }

    /**
     * 字节转GB
     *
     * @param bytes
     *
     * @return
     */
    private BigDecimal bytes2Gb(long bytes, int scale) {
        BigDecimal filesize = new BigDecimal(bytes);
        BigDecimal gibibyte = new BigDecimal(1024 * 1024 * 1024);
        /*divide(BigDecimal divisor, int scale, int roundingMode) 返回一个BigDecimal，其值为（this/divisor），其标度为指定标度*/
        return filesize.divide(gibibyte, scale, BigDecimal.ROUND_HALF_UP);
    }


    private String billTypeFormat(String billType) {
        String billFormat = "";
        switch (billType) {
            case "SubscriptionOrder":
                billFormat = "预付费";
                break;
            case "PayAsYouGoBill":
                billFormat = "后付费";
                break;
            case "Refund":
                billFormat = "退款";
                break;
            case "Adjustment":
                billFormat = "调账";
                break;
            case "PrePaid":
                billFormat = "预付费";
                break;
            case "PostPaid":
                billFormat = "后付费";
                break;
            default:
                break;
        }
        return billFormat;
    }

    private void setExclusiveResourcePoolBill(ResHpcClusterDetailVO hpcClusterDetail, ExclusiveResourcePoolBill exclusiveResourcePoolBill,SimpleDateFormat format) {
        Integer computeNodeNum = hpcClusterDetail.getComputeNodeNum();
        Integer vncNodeNum = hpcClusterDetail.getVNCNodeNum();
        Integer cliNum = hpcClusterDetail.getCliNum();
        String loginNodeExternalAddress = hpcClusterDetail.getLoginNodeExternalAddress();
        Integer managerNodeNum = hpcClusterDetail.getVNCNodeNum();
        String clusterType = hpcClusterDetail.getClusterType();
        Date startTime = hpcClusterDetail.getStartTime();
        Date endTime = hpcClusterDetail.getEndTime();
        List<NodeInfo> nodeList = hpcClusterDetail.getNodeList();

        //计算节点核心和内存数量
        int computeCPUNum = 0;
        int computeMENNum = 0;
        int vncCPUNum = 0;
        int vncMENNum = 0;
        int loginCPUNum = 0;
        int loginMENNum = 0;
        if (CollectionUtil.isNotEmpty(nodeList)) {
            for (NodeInfo nodeInfo : nodeList) {
                String name = nodeInfo.getName();
                if (StringUtils.equalsIgnoreCase("compute", nodeInfo.getHpcPointType())) {
                    try {
                        computeCPUNum += Integer.parseInt(name.substring(0, name.indexOf('核')));
                        computeMENNum += Integer.parseInt(name.substring(name.indexOf('核') + 1, name.indexOf('G')));
                    } catch (Exception e) {
                        log.error("数据转换异常,异常信息{},", e.getMessage());
                    }
                }
                if (StringUtils.equalsIgnoreCase("ccs_cli", nodeInfo.getHpcPointType())) {
                    try {
                        loginCPUNum += Integer.parseInt(name.substring(0, name.indexOf('核')));
                        loginMENNum += Integer.parseInt(name.substring(name.indexOf('核') + 1, name.indexOf('G')));
                    } catch (Exception e) {
                        log.error("数据转换异常,异常信息{},", e.getMessage());
                    }
                }
                if (StringUtils.equalsIgnoreCase(HpcPointType.VNC, nodeInfo.getHpcPointType())) {
                    try {
                        vncCPUNum += Integer.parseInt(name.substring(0, name.indexOf('核')));
                        vncMENNum += Integer.parseInt(name.substring(name.indexOf('核') + 1, name.indexOf('G')));
                    } catch (Exception e) {
                        log.error("数据转换异常,异常信息{},", e.getMessage());
                    }
                }
            }
        }
        String computeNodeStandard = computeNodeNum.toString();
        if (computeNodeNum > 0) {
            computeNodeStandard = computeCPUNum + "c" + computeMENNum + "G/" + computeNodeNum;
        }
        String vncNodeStandard = vncNodeNum.toString();
        if (vncNodeNum > 0) {
            vncNodeStandard = vncCPUNum + "c" + vncMENNum + "G/" + vncNodeNum;
        }
        String loginStandard = cliNum.toString();
        if (cliNum > 0) {
            loginStandard = loginCPUNum + "c" + loginMENNum + "G/" + cliNum;
        }
        exclusiveResourcePoolBill.setClusterType(hpcClusterDetail.getOsArchitectureType());
        exclusiveResourcePoolBill.setComputeNodeNum(computeNodeStandard);
        exclusiveResourcePoolBill.setVNCNode(vncNodeStandard);
        exclusiveResourcePoolBill.setLoginNode(loginStandard);
        exclusiveResourcePoolBill.setClusterBeginTime(DateUtil.format(startTime, format));
        exclusiveResourcePoolBill.setClusterEndTime(DateUtil.format(endTime, format));
        long usageCountDay = DateUtil.between(startTime, endTime, DateUnit.DAY);
        exclusiveResourcePoolBill.setUsageCount(String.valueOf(usageCountDay));
    }

    private void setStorageBill(List<StorageBill> tempStorage,Long clusterId, List<ResShare> shareList, SimpleDateFormat format,String time) {
        //弹性文件账单相关信息赋值
        if (CollectionUtil.isNotEmpty(shareList)) {
            for (ResShare resShare : shareList) {
                String chargeType = resShare.getChargeType();
                if (Objects.nonNull(resShare.getClusterId()) && resShare.getClusterId().equals(clusterId)) {
                    StorageBill storageBill = new StorageBill();
                    storageBill.setBillTime(time);
                    storageBill.setProductName(productTypeFormat(chargeType));
                    String sfsInstanceId = resShare.getUuid();
                    if (StringUtils.isNotEmpty(sfsInstanceId)) {
                        storageBill.setInstanceName(resShare.getName() + "/" + sfsInstanceId);
                    }
                    if ("PrePaid".equals(chargeType)){
                        storageBill.setConfiguration("NFS("+ resShare.getSize()+")");
                    }else {
                        storageBill.setConfiguration("SFS_HPCMIX("+ resShare.getSize()+")");
                    }
                    storageBill.setUsageCount(resShare.getSize() + "GB");
                    storageBill.setBillType(billTypeFormat(chargeType));
                    storageBill.setShareType(resShare.getShareProto());
                    storageBill.setStorageType(resShare.getShareType());
                    storageBill.setBelongingHPCCluster(resShare.getClusterName());
                    storageBill.setOrgName(resShare.getOrgName());
                    storageBill.setStartTime(DateUtil.format(resShare.getStartTime(), format));
                    storageBill.setEndTime(DateUtil.format(resShare.getEndTime(), format));
                    DefaultUtil.setFieldValueNotNull(storageBill);
                    tempStorage.add(storageBill);
                }
            }
        }
    }

    private String productTypeFormat(String productType) {
        String typeFormat = "";
        if (StringUtil.containsIgnoreCase(productType, POST_PAID)) {
            return "弹性文件服务 SFS";
        } else if (StringUtil.containsIgnoreCase(productType, PRE_PAID)) {
            return "弹性文件服务 SFS2.0";
        } else {
            return productType;
        }
    }

    private void setStorageBills(List<DescribeGaapCostResponse> tempStorageBills, List<StorageBill> tempStorage,SimpleDateFormat format) {
        if (CollectionUtil.isEmpty(tempStorageBills) || CollectionUtil.isEmpty(tempStorage)){
            return;
        }
        //根据HPC专属资源账单查询所属存储账单
        for (StorageBill storageBill : tempStorage) {
            int lastIndexOf = storageBill.getInstanceName().lastIndexOf("/");
            if (lastIndexOf > 0){
                String substring = storageBill.getInstanceName().substring(lastIndexOf + 1);
                List<DescribeGaapCostResponse> costs = tempStorageBills.stream().filter(e -> substring.equals(e.getInstanceId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(costs)){
                    break;
                }
                //查询退订时间
                String orderSn = costs.get(0).getOrderId();
                ServiceOrderDetail detail = orderMapper.getEndTimeByOrderSn(orderSn);
                if (detail != null && detail.getEndTime() != null) {
                    storageBill.setUnsubscribeTime(DateUtil.format(detail.getEndTime(), format));
                }
                //storageBill.setUsageCount(costs.get(0).getUsageCount());
            }
        }
    }

    private void exportFile(String moduleType, List<ExclusiveResourcePoolBill> exclusiveResourcePoolBills,
                            List<JobBill> jobBills, List<StorageBill> storageBills, Long taskId, String time, User authUser,String
        language) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUser.getUserSid());

        InputStream inZip = null;
        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        String taskName = "租户计量数据明细";
        try {
            String path = StoragePathEnum.EXCEL.getPath("statistic");
            String xlsTemplateFileName = "template/statistical-data-template.xlsx";
            if (WebUtil.getHeaderAcceptLanguage(language)) {
                xlsTemplateFileName = "template/statistical-data-template-us.xlsx";
            }
            String format = fileNameSuffix();
            String fileName = taskName + "_" + time + "-" + format + XLSX;;
            String zipName = taskName + "_" + time + "-" + format + ZIP;

            //写入sheet
            outExcel = new ByteArrayOutputStream();
            ExcelExportService exportService = ExcelUtil.write().buildWriter(outExcel, xlsTemplateFileName);
            exportService.buildSheet(0).write(exclusiveResourcePoolBills);
            exportService.buildSheet(1).write(storageBills);
            exportService.buildSheet(2).write(jobBills);
            exportService.finish();

            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, fileName, obfuscatePassword, true);

            StorageResult result = storageService.saveFile(inZip, path, zipName,
                                                           true, true, null);

            up.setStatus(BIZ_DOWN_SUCCESS_STATUS);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipName);
            this.bizDownloadMapper.updateById(up);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFileName(zipName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.USER_STATISTICAL_DATA, up.getDownloadId());

            inExcel.close();
            outExcel.close();
            log.info("用户计量数据文件生成成功！");
        } catch (Exception e) {
            up.setStatus(BIZ_DOWN_FAIL_STATUS);
            up.setRemark("生成异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateById(up);
            errorLog(taskName, taskId, moduleType, e.getMessage());
        } finally {
            IoUtil.close(inZip);
            IoUtil.close(outExcel);
            IoUtil.close(inExcel);
        }
    }

    private String remarkToUs(String remark) {
        remark = remark.replaceAll("平台充值", "Platform recharge")
                       .replaceAll("信用余额充值", "Credit balance recharge")
                       .replaceAll("合同调整信用额度", "Contract adjustment of credit limit")
                       .replaceAll("微信支付充值", "WeChat Pay Recharge")
                       .replaceAll("支付宝充值", "Alipay recharge")
                       .replaceAll("充值现金券充值", "Recharge with cash coupons")
                       .replaceAll("调整信用额度", "Adjust credit limit")
                       .replaceAll("修改欠费余额", "Modify outstanding balance")
                       .replaceAll("余额欠费修改信用额度", "Balance arrears, modify credit limit")
                       .replaceAll("AI开发平台专属资源池", "Ascend Modelarts Exclusive Resource Pool")
                       .replaceAll("资源费用", "Resource costs")
                       .replaceAll("AI开发平台共享资源池", "Ascend Modelarts Shared Resource Pool")
                       .replaceAll("套餐包费用", "Package fee")
                       .replaceAll("对象存储", "Object Storage")
                       .replaceAll("优惠券抵扣", "Coupon deduction")
                       .replaceAll("元", "yuan")
                       .replaceAll("云硬盘", "Cloud hard drive")
                       .replaceAll("信用额度过期清零", "Credit limit expired")
                       .replaceAll("系统", "SYSTEM")
                       .replaceAll("平台清理", "Platform cleaning")
                       .replaceAll("在线充值", "Online recharge");
        return remark;
    }

    private static String configurationToUs(String configuration) {
        String[] zhs = {"标准存储", "低频存储", "低频访问存储", "归档存储", "默认规格", "AI全流程开发"};
        String[] ens = {"Standard storage", "Low frequency storage", "Low frequency access storage", "archival storage", "Default specifications", "Full process development of AI"};
        return StringUtils.replaceEach(configuration, zhs, ens);
    }

    @Override
    public void doAsyncExportOperationalAnalysisOverview(CostBillExport costBillExport, String moduleType, Long taskId, AuthUser authUserInfo) {
        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;
        //设置响应
        BizDownload up = new BizDownload();
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String type = costBillExport.getType();
        String timeType = costBillExport.getTimeType();
        List<CostBillItem> items = costBillExport.getItems();
//        if (CollectionUtil.isNotEmpty(items)) {
//           items.forEach(e -> {
//               String rise = e.getRise() ? "(上升)" : "(下降)";
//               e.setQoq(e.getQoq() + rise);
//               String preRanking = e.getPreRanking();
//               if (!"--".equals(preRanking)) {
//                   int preRankingInt = Integer.parseInt(preRanking);
//                   rise = preRankingInt > 0 ? "(上升)" : "(下降)";
//                   e.setPreRanking(e.getPreRanking() + rise);
//               }
//           });
//        }

        try {
            //导出到excel
            String taskName = ExportTypeEnum.CONSUME_DETAIL.getNameIsUs(acceptLanguage)
                    + "_" + costBillExport.getStartTime() + "_" + costBillExport.getEndTime();
            String prefix = "客户";
            String xlsTemplateFileName = "template/consume-detail-tenant-year.xlsx";
            if ("product".equals(type)) {
                prefix = "产品";
                xlsTemplateFileName = "template/consume-detail-product-year.xlsx";
            }
            if ("month".equals(timeType)) {
                xlsTemplateFileName = "template/consume-detail-tenant-month.xlsx";
                if ("product".equals(type)) {
                    xlsTemplateFileName = "template/consume-detail-product-month.xlsx";
                }
            }
            taskName = prefix + taskName;
            String format = new SimpleDateFormat("yyyyMMddHHmm").format(new Date());
            String destFileName = taskName+ format + ".xlsx";
            String zipFileName = taskName + format + ".zip";

            //写入sheet
            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName, new DynamicCellStyleWriteHandler())
                    .buildSheet()
                    .fill(items)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());

            //压缩密码
            String password = UUID.randomUUID().toString().substring(0, 8);
            String encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);

            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), zipFileName, true,
                    true, null);

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePath.setFileName(zipFileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.CONSUME_DETAIL, up.getDownloadId(), authUserInfo);

            up.setUpdatedDt(new Date());
            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(zipFileName);
            up.setEntityId(authUserInfo.getEntityId());
            this.bizDownloadMapper.updateById(up);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
        }

    }
}

