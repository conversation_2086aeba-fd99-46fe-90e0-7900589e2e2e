/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.constants.ansible;

/**
 * DESC:
 *
 * <AUTHOR>
 * @date 2019/07/15 10:29
 */
public interface AnsibleTaskTypeConfig {

    String NODE_CONTROL_AGENT = "agent";

    String NODE_CONTROL_MONITOR = "monitor";

    String NODE_CONTROL_NODEEXPORTER = "nodexporter";

    String NODE_CONTROL_UNINSTALL_NODEEXPORTER = "uninstalle.nodexporter";

    String NODE_CONTROL_DOCKER = "docker";

    String NODE_CONTROL_UNINSTALL_AGENT = "uninstall";

    String NODE_CONTROL_SCRIPT = "script";

    String AUTO_OPS_SCRIPT = "autoops";

    String APP_OPS_RELEASE_CONTAINER = "release.container";

    String APP_OPS_RELEASE_NATIVE = "release.native";

    String APP_OPS_RELEASE_COMPONENT = "release.component";

    String APP_OPS_RELEASE_K8SNODE = "release.k8snode";

    String APP_OPS_STOP_CONTAINER = "stop.container";

    String APP_OPS_STOP_NATIVE = "stop.native";

    String APP_OPS_START_CONTAINER = "start.container";

    String CLUSTRT_CONTROL_DEPLOY = "deploy";
}
