/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class JSONNullToBlankSerializer extends JsonSerializer<Object> {
    @Override
    public void serialize(Object object, JsonGenerator paramJsonGenerator, SerializerProvider paramSerializerProvider)
            throws IOException {
        paramJsonGenerator.writeString("");
    }
}
