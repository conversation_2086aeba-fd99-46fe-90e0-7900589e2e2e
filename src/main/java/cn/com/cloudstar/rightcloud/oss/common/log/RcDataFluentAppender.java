/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.log;


import org.fluentd.logger.FluentLogger;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

import ch.qos.logback.core.Layout;
import ch.qos.logback.core.encoder.Encoder;
import ch.qos.logback.core.encoder.LayoutWrappingEncoder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RcDataFluentAppender<E> extends RcFluentdAppenderBase<E> {

    private FluentLogger fluentLogger;
    private String tag;
    private String label;
    private String remoteHost;
    private int port;
    private boolean useEventTime;
    private boolean enable;

    public RcDataFluentAppender() {
    }

    public void setEncoder(Encoder<E> encoder) {
        this.encoder = encoder;
    }

    public void addAdditionalField(Field field) {
        if (this.additionalFields == null) {
            this.additionalFields = new HashMap();
        }

        this.additionalFields.put(field.getKey(), field.getValue());
    }

    public boolean isFlattenMapMarker() {
        return this.flattenMapMarker;
    }

    public void setFlattenMapMarker(boolean flattenMapMarker) {
        this.flattenMapMarker = flattenMapMarker;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    @Override
    public void start() {
        super.start();
        this.fluentLogger = FluentLogger.getLogger(this.label != null ? this.tag : null, this.remoteHost, this.port);
    }

    @Override
    public void stop() {
        try {
            super.stop();
        } finally {
            try {
                FluentLogger.closeAll();
            } catch (Exception var7) {
            }

        }

    }

    @Override
    protected void append(E event) {
        if(enable){
            Map<String, Object> data = this.createData(event, tag);
            try {
                if (this.useEventTime) {
                    this.fluentLogger.log(this.label == null ? this.tag : this.label, data, LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000L);
                } else {
                    this.fluentLogger.log(this.label == null ? this.tag : this.label, data);
                }
            }catch (Throwable t){
            }
        }
    }
}
