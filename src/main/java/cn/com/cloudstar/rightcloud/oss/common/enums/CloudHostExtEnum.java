/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.enums;

/**
 * The type CloudHostExtEnum.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/5/22
 */
public enum CloudHostExtEnum {
    /**
     * 子网
     */
    SUBNET("subnet"),

    /**
     * 安全组
     */
    SECURITY_GROUP("sg");

    String type;

    CloudHostExtEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
