package com.cloudstar.service.util;



import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cloudstar.ConfigService;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.service.pojo.vo.responsevo.higress.HigressLoginResp;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import com.cloudstar.bean.enums.ConfigType;
import net.minidev.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@Slf4j
public class HigressHttpUtil {

    RestTemplate restTemplate;

    ConfigService configService;



    /**
     * GET请求封装
     * @param path 路径
     * @param uriVariables 参数
     * @param cookie cookie
     * @return 结果
     */
    public String get(String path, Map<String, Object> uriVariables, String cookie) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + path;
        if (MapUtil.isNotEmpty(uriVariables)) {
            url = splitPath(url, uriVariables);
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(tokenHeader(cookie));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new BizException("请求失败");
        }
        return response.getBody();
    }

    /**
     * POST请求封装
     * @param path 路径
     * @param uriVariables 参数
     * @param cookie cookie
     * @return 结果
     */
    public String post(String path, Map<String, Object> uriVariables, String cookie) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + path;
        // 发起请求
        HttpEntity<String> requestEntity = new HttpEntity<>(JSONObject.toJSONString(uriVariables), tokenHeader(cookie));
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        if (!HttpStatus.OK.equals(response.getStatusCode()) && !HttpStatus.CREATED.equals(response.getStatusCode())) {
            throw new BizException("请求失败");
        }
        // 登录接口返回cookie
        if ("/session/login".equals(path)) {
            String body = response.getBody();
            HigressLoginResp bean = JSONUtil.parseObj(body).toBean(HigressLoginResp.class);
            String firstCookie = response.getHeaders().getFirst("Set-Cookie");
            if (firstCookie != null) {
                bean.setSetCookie(firstCookie.split(";")[0]);
            }
            return JSONUtil.toJsonStr(bean);
        }
        return response.getBody();
    }

    /**
     * PUT请求封装
     * @param path 路径
     * @param uriVariables 参数
     * @param cookie cookie
     * @return 结果
     */
    public String put(String path, Map<String, Object> uriVariables, String cookie) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + path;
        // 发起请求
        HttpEntity<String> requestEntity = new HttpEntity<>(JSONObject.toJSONString(uriVariables), tokenHeader(cookie));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new BizException("请求失败");
        }
        return response.getBody();
    }

    /**
     * DELETE请求封装
     * @param path 路径
     * @param uriVariables 参数
     * @param cookie cookie
     * @return 结果
     */
    public String delete(String path, Map<String, Object> uriVariables, String cookie) {
        String url = configService.getConfig(ConfigType.HIGRESS_CONFIG).getHigressUrl().stringValue() + path;
        if (MapUtil.isNotEmpty(uriVariables)) {
            url = splitPath(url, uriVariables);
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(tokenHeader(cookie));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, String.class);
        if (!HttpStatus.OK.equals(response.getStatusCode())
                && !HttpStatus.NO_CONTENT.equals(response.getStatusCode())) {
            throw new BizException("请求失败");
        }
        return response.getBody();
    }


    /**
     * 拼接get请求url
     */
    public static String splitPath(String url, Map<String, Object> source) {
        Map<String, Object> params = new HashMap<>(source);
        Iterator<String> it = params.keySet().iterator();

        StringBuilder paramStr = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            if (StrUtil.isBlank(String.valueOf(params.get(key)))) {
                continue;
            }
            paramStr.append("&").append(key).append("=").append(params.get(key));
            it.remove(); // 移除已处理的参数
        }
        if (paramStr.length() == 0) {
            return url;
        }

        String sub = paramStr.substring(1);
        if (url.contains("?")) {
            url += ("&" + sub);
        } else {
            url += ("?" + sub);
        }
        return url;
    }

    /**
     * 添加token头
     */
    public static HttpHeaders tokenHeader(String cookie) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", "application/json");
        headers.add("Content-Encoding", "UTF-8");
        headers.add("Content-Type", "application/json; charset=UTF-8");
        if (StrUtil.isNotBlank(cookie)) {
            headers.add("Cookie", cookie);
        }
        return headers;
    }


}
