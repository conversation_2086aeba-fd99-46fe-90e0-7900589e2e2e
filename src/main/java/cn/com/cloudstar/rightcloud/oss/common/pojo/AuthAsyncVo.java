/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 12:40 2019/04/11
 */
@Data
public class AuthAsyncVo {
    private String createdBy;
    private String updatedBy;
    private Long orgSid;

    public AuthAsyncVo(String createdBy, String updatedBy, Long orgSid){
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.orgSid = orgSid;
    }


}
