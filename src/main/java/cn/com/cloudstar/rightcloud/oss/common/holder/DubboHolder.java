/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.holder;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResAllRemoteService;

/**
 * <AUTHOR>
 * @date 2021/1/15 4:07 下午
 * 静态变量注入 dubbo 服务 供静态方法调用
 *
 */
@Component
public class DubboHolder {

    @DubboReference
    public CloudEnvRemoteService cloudEnvRemoteService;
    @DubboReference
    public CloudEnvAccountRemoteService cloudEnvAccountRemoteService;
    @DubboReference
    public ResAllRemoteService resAllRemoteService;
    @DubboReference
    public HcsoUserRemoteService hcsoUserRemoteService;
}
