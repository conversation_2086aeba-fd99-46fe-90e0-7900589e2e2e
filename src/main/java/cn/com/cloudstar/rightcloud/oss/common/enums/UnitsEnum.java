package cn.com.cloudstar.rightcloud.oss.common.enums;

/**
 * <AUTHOR>
 * @date 规格单位
 */
public enum UnitsEnum {
    /**
     * HPC基础单位判定
     */
    CPUUSAGE("cpuUsage", "/核/小时"),
    /**
     * HPC基础单位判定
     */
    GPUUSAGE("gpuUsage", "/卡/小时"),

    /**
     * ModelArts共享资源池基础单位判定
     */
    RESOURCEUSAGE("resourceUsage", "/个/小时"),

    /**
     * 存储使用量基础单位判定
     */
    STORAGEUSAGE("storageUsage", "/GB/小时"),

    /**
     * 存储使用量基础单位判定
     */
    CAPACITY("capacity", "/GB/小时"),

    /**
     * 算力时
     */
    PFLOPSRESOURCEUSAGE("pflopsResourceUsage", "/PFLOPS•小时"),

    COMPUTINGPOWER("COMPUTINGPOWER", "PFLOPS"),

    /**
     * 带宽使用量基础单位判定
     */
    NETWORKUSAGE("networkUsage", "/Mbps/小时"),


    /**
     * 数据安全中心
     */
    DSCUSAGE("dscUsage", "/月"),
    ;


    public String spec;

    public String unit;

    UnitsEnum(String spec, String unit) {
        this.spec = spec;
        this.unit = unit;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
