/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.util;

import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.pojo.AuthAsyncVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.ClassUtils;
import org.owasp.esapi.ESAPI;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * Web层相关的实用工具类
 *
 * <AUTHOR>
@Slf4j
public class WebUserUtil {



    /**
     * 下拉列表框显示值Key
     */
    public static final String COMBOX_TEXT_FIELD = "textFieldKey";

    /**
     * 下拉列表框实际值Key
     */
    public static final String COMBOX_VALUE_FIELD = "valueFieldKey";

    public static final String LOCAL_IP="local.ip";
    public static final String LOCAL_IPV6="local.ipv6";

    private static final List<String> charList = Arrays.asList("`~!@#$%^&*()+=|{}':;',[].<>/?~！@#￥%……&*（）——+|{}【】‘；：'\"’。，、？".split(""));


    /**
     * 是否是英文
     * @return true false
     */
    public static boolean getHeaderAcceptLanguage() {
        return "en-US".equals(LocaleContextHolder.getLocale().toLanguageTag());
    }

    /**
     * 设置分页查询条件相关参数。
     *
     * @param request 共通查询条件
     * @param criteria 查询条件
     * @param defaultOrderByClause 默认排序条件
     */
    public static <T extends BaseRequest> void preparePageParams(T request, Criteria criteria, String defaultOrderByClause) {

        if (request == null || criteria == null) {
            return;
        }

        // 取得分页信息
        String strPageNum = request.getPagenum();
        String strPageSize = request.getPagesize();

        // 设置分页信息
        setPaginationParam(criteria, strPageNum, strPageSize);

        // 取得排序信息
        String sortDataField = request.getSortdatafield();
        String sortOrder = request.getSortorder();

        // 排序信息
        setOrderParams(criteria, defaultOrderByClause, sortDataField, sortOrder);
    }

    private static void setPaginationParam(Criteria criteria, String strPageSum, String strPageSize) {
        if (!StringUtil.isNullOrEmpty(strPageSum) && !StringUtil.isNullOrEmpty(strPageSize)) {
            int pageSize = Integer.parseInt(strPageSize);
            int pageNum = Integer.parseInt(strPageSum);
            // 当前的前端是页码开始是0，这里插件是从1开始的页面，所以默认+1
            criteria.setPageNum(pageNum + 1);
            criteria.setPageSize(pageSize);
        }
    }

    private static void setOrderParams(Criteria criteria, String defaultOrderByClause, String sortDatafield,
                                       String sortOrder) {
        criteria.setOrderByClause(getOrderByClause(sortDatafield, sortOrder, defaultOrderByClause));
    }

    public static String getOrderByClause(String sortDatafield, String sortOrder, String defaultOrderByClause) {
        if (!Strings.isNullOrEmpty(sortDatafield) && !Strings.isNullOrEmpty(sortOrder)) {
            String order = WebUtil.toClumn(sortDatafield) + " " + sortOrder;
            if (!Strings.isNullOrEmpty(defaultOrderByClause)) {
                order += ", " + defaultOrderByClause;
            }
            return order;
        }
        return defaultOrderByClause;
    }

    /**
     * 把pojo字段转为数据库字段<br> fileName -> FILE_NAME
     *
     * @param field 变量名
     * @return 字段名
     */
    public static String toClumn(String field) {

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < field.length(); i++) {
            char c = field.charAt(i);
            if (Character.isUpperCase(c) && i > 0) {
                sb.append("_").append(Character.toUpperCase(c));
            } else {
                sb.append(Character.toUpperCase(c));
            }
        }
        return sb.toString();
    }

    /**
     * 直接输出字符串.
     */
    public static void renderText(HttpServletResponse response, String text) {

        render(response, text, "text/plain;charset=UTF-8");
    }

    /**
     * 使用Response输出指定格式内容.
     */
    protected static void render(HttpServletResponse response, String text, String contentType) {

        try {
            response.setContentType(contentType);
            if (StringUtil.isNullOrEmpty(text)) {
                text = "";
            }
            response.getWriter().write(StringEscapeUtils.unescapeHtml(ESAPI.encoder().encodeForHTML(text)));
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 直接输出JSON.
     */
    public static void renderJson(HttpServletResponse response, String text) {

        render(response, text, "application/json; charset=utf-8");
    }

    /**
     * 直接输出HTML.
     */
    public static void renderHtml(HttpServletResponse response, String html) {

        render(response, html, "text/html;charset=UTF-8");
    }

    /**
     * 直接输出XML.
     */
    public static void renderXML(HttpServletResponse response, String xml) {

        render(response, xml, "text/xml;charset=UTF-8");
    }

    /**
     *
     * @param data 数据值
     * @param salt 加密添加字符串
     * @return 加密后字符串
     */
    public static String encrypt(String data, String salt) {

        // 可以更换算法:sha512Hex
        return DigestUtils.sha256Hex(data + "{" + salt.toLowerCase() + "}");
    }

    /**
     *
     * @param data 数据值 加密添加字符串
     * @return 加密后字符串
     */
    public static String encrypt(String data) {

        // 可以更换算法:sha512Hex
        return DigestUtils.sha256Hex(data);
    }

    /**
     * 中文编码
     */
    public static String urlEncode(String name) {
        try {
            return URLEncoder.encode(name, "UTF-8");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return "";
    }


    /**
     * 新增时添加用户以及当前时间信息
     */
    public static <T> void prepareInsertParams(T obj) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser != null && authUser.getAccount() != null) {
            // 创建人
            invokeSet(obj, "createdBy", authUser.getAccount());
            // 更新人
            invokeSet(obj, "updatedBy", authUser.getAccount());
        } else {
            if(!ObjectUtils.isEmpty(RequestContextUtil.getAuthUserInfo())
                    && !ObjectUtils.isEmpty(RequestContextUtil.getAuthUserInfo().getAccount())){
                invokeSet(obj, "createdBy", RequestContextUtil.getAuthUserInfo().getAccount());
                invokeSet(obj, "updatedBy", RequestContextUtil.getAuthUserInfo().getAccount());
            }
        }
        Date date = new Date();
        // 更新时间
        invokeSet(obj, "createdDt", date);
        // 更新时间
        invokeSet(obj, "updatedDt", date);
        // 初始版本号
        invokeSet(obj, "version", 1L);
    }

    /**
     * 新增时添加用户以及当前时间信息
     */
    public static <T> void prepareInsertParamsNoVersion(T obj) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser != null && authUser.getAccount() != null) {
            // 创建人
            invokeSet(obj, "createdBy", authUser.getAccount());
            // 更新人
            invokeSet(obj, "updatedBy", authUser.getAccount());
        } else {
            invokeSet(obj, "createdBy", null);
            invokeSet(obj, "updatedBy", null);
        }
        Date date = new Date();
        // 更新时间
        invokeSet(obj, "createdDt", date);
        // 更新时间
        invokeSet(obj, "updatedDt", date);
    }


    /**
     * 新增用户是写入时间及操作人信息
     */
    public static <T, A> void prepareInsertParams(T obj, A authUserInfo) {
        Object account;
        if (authUserInfo != null && (account = BeanUtil.getBeanProperty(authUserInfo, "account")) != null) {
            // 创建人
            invokeSet(obj, "createdBy", account.toString());
            // 更新人
            invokeSet(obj, "updatedBy", account.toString());
        } else {
            invokeSet(obj, "createdBy", "admin");
            invokeSet(obj, "updatedBy", "admin");
        }
        Date date = new Date();
        // 更新时间
        invokeSet(obj, "createdDt", date);
        // 更新时间
        invokeSet(obj, "updatedDt", date);
        // 初始版本号
        invokeSet(obj, "version", 1L);
    }

    /**
     * 新增时添加用户以及当前时间信息(For Activiti)
     */
    public static <T> void prepareInsertParams(T obj, String user) {
        if (user != null && !StringUtil.EMPTY.equals(user)) {
            // 创建人
            invokeSet(obj, "createdBy", user);
            // 更新人
            invokeSet(obj, "updatedBy", user);
        }
        Date date = new Date();
        // 更新时间
        invokeSet(obj, "createdDt", date);
        // 更新时间
        invokeSet(obj, "updatedDt", date);
        // 初始版本号
        invokeSet(obj, "version", 1L);
    }

    /**
     * 新增时添加管理员用户以及当前时间信息
     */
    public static <T> void prepareInsertAdminParams(T obj) {
        // 创建人
        invokeSet(obj, "createdBy", "admin");
        // 更新人
        invokeSet(obj, "updatedBy", "admin");

        Date date = new Date();
        // 更新时间
        invokeSet(obj, "createdDt", date);
        // 更新时间
        invokeSet(obj, "updatedDt", date);
        // 初始版本号
        invokeSet(obj, "version", 1L);
    }


    /**
     * 新增时添加管理员用户以及当前时间信息
     */
    public static <T> void prepareUpdateAdminParams(T obj) {
        // 更新人
        invokeSet(obj, "updatedBy", "admin");

        Date date = new Date();
        // 更新时间
        invokeSet(obj, "updatedDt", date);
    }

    /**
     * 更新时添加用户以及当前时间信息
     */
    public static <T> void prepareUpdateParams(T obj) {
        if (AuthUtil.getAuthUser() != null && AuthUtil.getAuthUser().getAccount() != null) {
            // 更新人
            invokeSet(obj, "updatedBy", AuthUtil.getAuthUser().getAccount());
        } else {
            invokeSet(obj, "updatedBy", "admin");
        }
        // 更新时间
        Date date = new Date();
        invokeSet(obj, "updatedDt", date);
    }

    /**
     * 更新时添加用户以及当前时间信息
     */
    public static <T> void prepareUpdateParams(T obj, String user) {
        if (user != null && !StringUtil.EMPTY.equals(user)) {
            // 更新人
            invokeSet(obj, "updatedBy", user);
        }
        // 更新时间
        Date date = new Date();
        invokeSet(obj, "updatedDt", date);
    }

    /**
     * java反射bean的set方法
     */
    @SuppressWarnings("rawtypes")
    private static Method getSetMethod(Class<? extends Object> objectClass, String fieldName) {

        try {
            Class[] parameterTypes = new Class[1];
            Field field = getField(objectClass, fieldName);
            parameterTypes[0] = field.getType();
            StringBuffer sb = new StringBuffer();
            sb.append("set");
            sb.append(fieldName.substring(0, 1).toUpperCase());
            sb.append(fieldName.substring(1));
            return objectClass.getMethod(sb.toString(), parameterTypes);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;

    }

    /**
     * 获取类已经类的父的某一声明变量
     *
     * @param fieldName 变量名
     * @return Field 变量
     */
    @SuppressWarnings("rawtypes")
    private static Field getField(Class clazz, String fieldName) throws NoSuchFieldException {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            List<Class<?>> allSuperclasses = ClassUtils.getAllSuperclasses(clazz);
            Class superClass = null;
            if (!CollectionUtils.isEmpty(allSuperclasses)){
                superClass = allSuperclasses.get(0);
            }
            if (superClass == null) {
                throw e;
            } else {
                return getField(superClass, fieldName);
            }
        }
    }

    /**
     * 执行set方法
     *
     * @param o 执行对象
     * @param fieldName 属性
     * @param value 值
     */
    private static void invokeSet(Object o, String fieldName, Object value) {

        Method method = getSetMethod(org.springframework.util.ClassUtils.getUserClass(o), fieldName);
        try {
            method.invoke(o, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }






    /**
     * 取得前后N年分下拉列表Map
     *
     * @param yearCount 指定年数
     * @param addPreYear 是否取得前N年份(true:是;false:否)
     * @param addNextYear 是否取得后N年份(true:是;false:否)
     *
     * @return 下拉列表Map
     */
    public static Map<String, String> getYearMap(int yearCount, boolean addPreYear, boolean addNextYear) {

        Map<String, String> yearMap = new LinkedHashMap<String, String>();

        // 日历取得
        Calendar cder = Calendar.getInstance();

        int cYear = cder.get(Calendar.YEAR);

        // 前N年
        if (addPreYear) {
            for (int i = yearCount; i >= 1; i--) {
                yearMap.put(String.valueOf(cYear - i), String.valueOf(cYear - i));
            }
        }
        // 今年
        yearMap.put(String.valueOf(cYear), String.valueOf(cYear));
        // 后N年
        if (addNextYear) {
            for (int j = 1; j <= yearCount; j++) {
                yearMap.put(String.valueOf(cYear + j), String.valueOf(cYear + j));
            }
        }

        return yearMap;
    }

    /**
     * 取得月份下拉列表Map
     *
     * @return 下拉列表Map
     */
    public static Map<String, String> getMonthMap() {

        Map<String, String> monthMap = new LinkedHashMap<String, String>();

        // 12月份
        monthMap.put("1", "01");
        monthMap.put("2", "02");
        monthMap.put("3", "03");
        monthMap.put("4", "04");
        monthMap.put("5", "05");
        monthMap.put("6", "06");
        monthMap.put("7", "07");
        monthMap.put("8", "08");
        monthMap.put("9", "09");
        monthMap.put("10", "10");
        monthMap.put("11", "11");
        monthMap.put("12", "12");

        return monthMap;
    }

    /**
     * 取得前后N年年份中文下拉列表Map
     *
     * @param yearCount 指定年数
     * @param addPreYear 是否取得前N年份(true:是;false:否)
     * @param addNextYear 是否取得后N年份(true:是;false:否)
     * @return 下拉列表Map
     */
    public static Map<String, String> getYearChineseMap(int yearCount, boolean addPreYear, boolean addNextYear) {

        Map<String, String> yearMap = new LinkedHashMap<String, String>();

        // 日历取得
        Calendar cder = Calendar.getInstance();

        int cYear = cder.get(Calendar.YEAR);

        // 前N年
        if (addPreYear) {
            for (int i = yearCount; i >= 1; i--) {
                yearMap.put(String.valueOf(cYear - i), String.valueOf(cYear - i) + "年");
            }
        }
        // 今年
        yearMap.put(String.valueOf(cYear), String.valueOf(cYear) + "年");
        // 后N年
        if (addNextYear) {
            for (int j = 1; j <= yearCount; j++) {
                yearMap.put(String.valueOf(cYear + j), String.valueOf(cYear + j) + "年");
            }
        }

        return yearMap;
    }

    public static Map<String, String> getMonthChineseMap() {
        Map<String, String> monthMap = new LinkedHashMap<String, String>();

        // 12月份
        monthMap.put("1", "1月");
        monthMap.put("2", "2月");
        monthMap.put("3", "3月");
        monthMap.put("4", "4月");
        monthMap.put("5", "5月");
        monthMap.put("6", "6月");
        monthMap.put("7", "7月");
        monthMap.put("8", "8月");
        monthMap.put("9", "9月");
        monthMap.put("10", "10月");
        monthMap.put("11", "11月");
        monthMap.put("12", "12月");

        return monthMap;
    }

    public static Map<String, String> getWeekMap() {
        Map<String, String> weekMap = new LinkedHashMap<String, String>();
        weekMap.put("0", "星期日");
        weekMap.put("1", "星期一");
        weekMap.put("2", "星期二");
        weekMap.put("3", "星期三");
        weekMap.put("4", "星期四");
        weekMap.put("5", "星期五");
        weekMap.put("6", "星期六");
        return weekMap;
    }

    public static Map<String, String> getQuarterMap() {
        Map<String, String> quarterMap = new LinkedHashMap<String, String>();
        quarterMap.put("1", "第一季度");
        quarterMap.put("2", "第二季度");
        quarterMap.put("3", "第三季度");
        quarterMap.put("4", "第四季度");
        return quarterMap;
    }

    /**
     * 根据当前系统时间生成ID
     *
     * @return 精确到毫秒
     */
    public static long IdGenerator() {

        long baseId;
        long t = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 52~43
        baseId = t;
        baseId &= 0x1FF0000000L;
        baseId <<= 14;
        // 28~15
        t &= 0xFFFC000L;
        baseId |= t;
        // 42~29
        SecureRandom ng = new SecureRandom();
        t = ng.nextLong();
        t &= 0x3FFF0000000L;
        // 14~1
        baseId |= t;
        baseId |= t;
        baseId /= 10000;
        baseId *= 10000;
        baseId &= 0x1FFFFFFFFFFFFL;
        return baseId;

    }

    /**
     * 生成指定位数随机密码
     *
     * @return 随机密码
     */
    public static String randomPwd(int length) {
        String pwd = createPwd(length);
        if (pwdMatch(pwd) && charList.parallelStream().anyMatch(pwd::contains) && adjoinVerify(pwd)) {
            return pwd;
        }
        return randomPwd(length);
    }

    private static boolean pwdMatch(String pwd) {
        return pwd.matches(".*[a-z]+.*")
            && pwd.matches(".*[A-Z]+.*")
            && pwd.matches(".*\\d+.*");
    }

    /**
     * 生成随机密码，包含数字、小写字母、符号
     */
    private static String createPwd(int length) {
        StringBuilder randomPwd = new StringBuilder();
        StringBuilder buffer = new StringBuilder(
                "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`~!@#$%^&*()+=|{}");
        SecureRandom r = new SecureRandom();
        int range = buffer.length();
        for (int i = 0; i < length; i++) {
            randomPwd.append(buffer.charAt(r.nextInt(range)));
        }
        return randomPwd.toString();
    }


    /**
     * 验证是否连续字符
     */
    private static boolean adjoinVerify(String pwd) {
        boolean verify = true;
        for (int i = 0; i < pwd.length(); i++) {
            if (i == pwd.length() - 1) {
                break;
            }
            if (String.valueOf(pwd.charAt(i)).equals(String.valueOf(pwd.charAt(i + 1)))) {
                verify = false;
                break;
            }
        }
        return verify;
    }



    /**
     * 小时转换成微秒
     */
    public static long convertTime(String hour) throws NullPointerException, NumberFormatException {
        double t = Double.parseDouble(hour);
        return (long) (t * 60 * 60 * 1000);
    }

    public static boolean isNotBlank(Object obj) {
        if (obj == null) {
            return false;
        } else if (org.springframework.util.ClassUtils.getUserClass(obj) == String.class) {
            return Strings.isNullOrEmpty((String) obj);
        } else {
            return true;
        }
    }

    /**
     * 生成指定位数随机帐号+日期
     *
     * @return 随机帐号+日期
     */
    public static String randomAccount(int length) {
        String randomAccount = "";
        StringBuffer buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyz");
        SecureRandom r = new SecureRandom();
        int range = buffer.length();
        for (int i = 0; i < length; i++) {
            randomAccount += buffer.charAt(r.nextInt(range));
        }
        //使用日历类
        Calendar cal = Calendar.getInstance();
        //得到年
        String year = cal.get(Calendar.YEAR) + "";
        //得到月，因为从0开始的，所以要加1
        String month = (cal.get(Calendar.MONTH) + 1) + "";
        if (month.length() == 1) {
            month = "0" + month;
        }
        //得到天
        String day = cal.get(Calendar.DAY_OF_MONTH) + "";
        if (day.length() == 1) {
            day = "0" + day;
        }
        String h = cal.get(Calendar.HOUR_OF_DAY) + "";
        if (h.length() == 1) {
            h = "0" + h;
        }
        String m = cal.get(Calendar.MINUTE) + "";
        if (m.length() == 1) {
            m = "0" + m;
        }
        String s = cal.get(Calendar.SECOND) + "";
        if (s.length() == 1) {
            s = "0" + s;
        }
        randomAccount =
                PropertiesUtil.getProperty("user.account.prefix") + year.substring(2, 4) + month + day + h + m + s;
        return randomAccount;
    }

    /**
     * 就近舍入
     */
    public static String parseRate(Double rate) {
        if (rate == null) {
            return "";
        }
        return Math.round(rate) + "";
    }

    /**
     * 取得HttpRequest的简化函数.
     */
    public static HttpServletRequest getRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 对double类型的数值保留指定位数的小数
     * 该方法舍入模式：向“最接近的”数字舍入，如果与两个相邻数字的距离相等，则为向上舍入的舍入模式
     *
     * @param number  要保留小数的数字
     * @param precision 小数位数
     * @return double
     */
    public static double keepPrecision(double number, int precision) {
        BigDecimal bg = BigDecimal.valueOf(number);
        return bg.setScale(precision, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static <T> void prepareInsertParamsNoException(T obj) {
        prepareInsertParamsNoException(obj, null);
    }
    public static <T> void prepareInsertParamsNoException(T obj, AuthAsyncVo authAsyncVo) {
        try {
            prepareInsertParams(obj);
        } catch (Exception e) {
            if (authAsyncVo != null) {
                invokeSet(obj, "createdBy", authAsyncVo.getCreatedBy());
                // 更新人
                invokeSet(obj, "updatedBy", authAsyncVo.getUpdatedBy());
                Date date = new Date();
                // 更新时间
                invokeSet(obj, "createdDt", date);
                // 更新时间
                invokeSet(obj, "updatedDt", date);
                // 初始版本号
                invokeSet(obj, "version", 1L);
            }
        }
    }
}
