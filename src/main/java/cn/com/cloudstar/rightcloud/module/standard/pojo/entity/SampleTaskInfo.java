package cn.com.cloudstar.rightcloud.module.standard.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 月平均算力填充率数据视图采样表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = "original_view_hpcop_sample_task_info")
public class SampleTaskInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value="id",type = IdType.AUTO)
    @MongoId
    private String id;
    /**
     *集群标识
     */
    @Field("besuness_category")
    private String besunessCategory;

    /**
     * 采样时间
     */
    @Field("sample_time")
    private Date sampleTime;

    /**
     * 任务状态
     */
    @Field("task_state")
    private String taskState;
    /**
     * 任务提交时间
     */
    @Field("submit_time")
    private Date submintTime;
    /**
     * 任务开始时间
     */
    @Field("task_start_time")
    private Date taskStartTime;
    /**
     * 任务完成时间
     */
    @Field("task_end_time")
    private Date taskEndTime;
    /**
     * 任务执行节点
     */
    @Field("exec_node")
    private String execNode;
    /**
     * 集群GPU总卡数
     */
    @Field("resource_pool")
    private String resourcePool;
    /**
     * 集群名称
     */
    @Field("cluster_name")
    private String clusterName;
    /**
     * 统计标记（0：未统计，1：已统计，默认值0）
     */
    @Field("stats_flag")
    private String statsFlag = "0";
    /**
     * 集群类型：SAASPrivate专属, SAASShare共享
     */
    @Field("cluster_type")
    private String  clusterType;
    /**
     * 运营实体ID
     */
    @Field("entity_id")
    private Long entityId;
}
