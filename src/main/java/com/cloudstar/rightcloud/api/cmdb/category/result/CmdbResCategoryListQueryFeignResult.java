package com.cloudstar.rightcloud.api.cmdb.category.result;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 配置分类列表查询结果
 *
 * @author: fuxingwang
 * @date: 2023/04/06  14:32
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class CmdbResCategoryListQueryFeignResult implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 所属组织id
     */
    private Long orgId;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源编码
     */
    private String code;

    /**
     * 父级分类编码
     */
    private String parentCode;

    /**
     * 父级分类名称
     */
    private String parentName;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 资源类型
     */
    private String resType;

    /**
     * 资源表名
     */
    private String tableName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 树节点图标
     */
    private String treeIcon;

    /**
     * 类型图标
     */
    private String icon;

    /**
     * 排序号，同级别排序，值越大越靠后
     */
    private Integer sortRank;

    /**
     * TREE路径
     */
    private String treePath;

    /**
     * 启用禁用状态,Y启用，N未启用，null未设置
     */
    private String enabled;

    /**
     * 资源变更记录 Y开启  N 不开启
     */
    private String resourceChangeRecord;

    /**
     * 备注
     */
    private String description;

    /**
     * 供应方式
     */
    private String provideType;
}
