DELETE
FROM
	sys_m_role_module_default
WHERE
	role_sid = 301
	AND module_sid IN (
		'BH',
		'BH01',
		'BH0101',
		'BH0102',
		'BH0103',
		'BH0104',
		'BH02',
		'BH0201',
		'BH0202',
		'BH0203',
		'BH0204',
		'BH03',
		'BH0301',
		'BH0302',
		'BH0303',
		'BH0304',
		'BH04',
		'BH0401',
		'BH0402',
		'BH05',
		'BH0501',
		'BH0502',
		'BH0503',
		'BH0504',
		'BH0505',
		'BH06',
		'BH0601',
	'BH0604');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH01');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0101');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0102');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0103');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0104');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH02');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0201');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0202');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0203');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0204');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH03');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0301');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0302');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0303');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0304');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH04');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0401');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0402');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH05');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0501');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0502');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0503');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0504');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0505');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH06');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0601');
INSERT INTO `bss`.`sys_m_role_module_default`(`role_sid`, `module_sid`) VALUES (301, 'BH0604');