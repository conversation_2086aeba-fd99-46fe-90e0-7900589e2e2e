delete
from sys_m_config
where CONFIG_TYPE = 'sso_config'
  and CONFIG_KEY in ('sso.redirect.url.rg', 'sso.idp.name', 'sso.idp.flag');

INSERT INTO sys_m_config (`CONFIG_TYPE`, `CONFIG_NAME`, `CONFIG_KEY`, `CONFIG_VALUE`, `DATA_TYPE`, `DISPLAY_TYPE`,
                          `UNIT`, `VALUE_DOMAIN`, `VALUE_INCREMENT`, `SORT_RANK`, `DESCRIPTION`, `CREATED_BY`,
                          `CREATED_DT`, `UPDATED_BY`, `UPDATED_DT`, `VERSION`, `display`)
VALUES ('sso_config', '获取跳转地址的正则表达式', 'sso.redirect.url.rg', '', 'string', NULL, NULL, NULL, NULL,
        9, NULL, 'admin', current_timestamp, 'admin', current_timestamp, 1, 1);

INSERT INTO sys_m_config (`CONFIG_TYPE`, `CONFIG_NAME`, `CONFIG_KEY`, `CONFIG_VALUE`, `DATA_TYPE`, `DISPLAY_TYPE`,
                          `UNIT`, `VALUE_DOMAIN`, `VALUE_INCREMENT`, `SORT_RANK`, `DESCRIPTION`, `CREATED_BY`,
                          `CREATED_DT`, `UPDATED_BY`, `UPDATED_DT`, `VERSION`, `display`)
VALUES ('sso_config', '为用户配置的idp名称', 'sso.idp.name', '', 'string', NULL, NULL, NULL, NULL,
        9, NULL, 'admin', current_timestamp, 'admin', current_timestamp, 1, 1);

INSERT INTO sys_m_config (`CONFIG_TYPE`, `CONFIG_NAME`, `CONFIG_KEY`, `CONFIG_VALUE`, `DATA_TYPE`, `DISPLAY_TYPE`,
                          `UNIT`, `VALUE_DOMAIN`, `VALUE_INCREMENT`, `SORT_RANK`, `DESCRIPTION`, `CREATED_BY`,
                          `CREATED_DT`, `UPDATED_BY`, `UPDATED_DT`, `VERSION`, `display`)
VALUES ('sso_config', '是否为创建的用户配置idp', 'sso.idp.flag', '', 'string', NULL, NULL, NULL, NULL,
        9, NULL, 'admin', current_timestamp, 'admin', current_timestamp, 1, 1);