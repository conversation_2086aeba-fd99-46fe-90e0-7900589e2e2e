####1.菜单配置sql（bss、rightcloud）
delete
from sys_auth_module
where auth_key like 'DB04%';
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB04', '云备份', 'CBR', 'DB', '', 'menu', 'route', '/appcmp/console/storage/cbr',
        '/framework/second-level-menu-style.vue', 'current', '/framework/second-level-menu-style.vue', '',
        1391680243441664, 4, 'enable', null, null, 'admin', '2024-09-26 18:13:27', 'admin', '2024-09-26 18:13:27', 1,
        'sidebar', null, null, '', 'display', '', '', 'prc');
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB0401', '存储库', 'memory pool', 'DB04', '', 'menu', 'route', '/appcmp/console/storage/cbr/cck',
        '/common/dynamic-grid/index.vue', 'current', '/framework/null-style.vue', null, 1391680243441664, 999, 'enable',
        '', '', null, null, null, null, null, 'sidebar', null, 0, null, 'display', null, null, 'prc');
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB040101', '申请', 'apply', 'DB0401', '', 'info', 'route', '/appcmp/console/storage/cbr/cck/apply/:id',
        '/service/apply/index.vue', 'current', '/framework/null-style.vue', '', null, 99, 'enable', null, null, 'admin',
        '2024-02-23 11:13:43', 'admin', '2024-02-23 11:13:43', 1, 'sidebar', null, 0, null, 'hide', null, null, 'prc');
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB0402', '备份', 'backups', 'DB04', '', 'menu', 'route', '/appcmp/console/storage/cbr/bf',
        '/common/dynamic-grid/index.vue', 'current', '/framework/null-style.vue', null, 1397057926938624, 999, 'enable',
        null, null, null, null, null, null, null, 'sidebar', null, 0, null, 'display', null, null, 'prc');
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB0403', '策略', 'strategy', 'DB04', '', 'menu', 'route', '/appcmp/console/storage/cbr/cl',
        '/common/dynamic-grid/index.vue', 'current', '/framework/null-style.vue', null, 1397058206752768, 999, 'enable',
        null, null, null, null, null, null, null, 'sidebar', null, 0, null, 'display', null, null, 'prc');
INSERT INTO sys_auth_module (auth_key, name, name_en, parent_auth_key, icon, module_type, request_type, request_url,
                             module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status,
                             description, description_en, created_by, created_dt, updated_by, updated_dt, version,
                             display_location, shortcut_name, collapse_menu, router_query, display_status,
                             service_directory_id, console_link, module_category)
VALUES ('DB040301', '申请', 'apply', 'DB0403', '', 'info', 'route', '/appcmp/console/storage/cbr/cl/apply/:id',
        '/service/apply/index.vue', 'current', '/framework/null-style.vue', '', null, 999, 'enable', null, null,
        'admin', '2024-02-23 13:59:26', 'admin', '2024-02-23 13:59:26', 1, 'sidebar', null, 0, null, 'hide', null, null,
        'prc');

delete
FROM sys_group_module_rela
WHERE group_sid = 1
  AND auth_key like 'DB04%';

INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB04', 'module', 'DB', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1, 'whiteList');
INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB0401', 'module', 'DB04', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1, 'whiteList');
INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB040101', 'module', 'DB0401', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1,
        'whiteList');
INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB0402', 'module', 'DB04', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1, 'whiteList');
INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB0403', 'module', 'DB04', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1, 'whiteList');
INSERT INTO sys_group_module_rela(group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (1, 'DB040301', 'module', 'DB0403', 0, 'zhou', '2024-10-12 13:59:37', 'zhou', '2024-10-12 13:59:37', 1,
        'whiteList');

delete
FROM sys_role_module_rela
WHERE role_id = 302
  AND auth_key like 'DB04%';

INSERT INTO sys_role_module_rela(role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                 updated_by, updated_dt, version, auth_type)
VALUES (302, 'DB04', 'module', 'DB', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);
INSERT INTO sys_role_module_rela(role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                 updated_by, updated_dt, version, auth_type)
VALUES (302, 'DB0401', 'module', 'DB04', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);
INSERT INTO sys_role_module_rela(role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                 updated_by, updated_dt, version, auth_type)
VALUES (302, 'DB040101', 'module', 'DB0401', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);
INSERT INTO sys_role_module_rela(role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                 updated_by, updated_dt, version, auth_type)
VALUES (302, 'DB0403', 'module', 'DB04', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);
INSERT INTO sys_role_module_rela(role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                 updated_by, updated_dt, version, auth_type)
VALUES (302, 'DB040301', 'module', 'DB0403', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt, updated_by, updated_dt, version, auth_type) VALUES (302, 'DB0402', 'module', 'DB04', 0, 'zhou', '2024-07-04 16:57:44', 'zhou', '2024-07-04 16:57:44', 1, null);

###2.配置接口权限--bss
delete
from biz_billing_spec_ref
where resource_type = 'CBR';
INSERT INTO biz_billing_spec_ref (resource_type, spec_id, type, value, invoke_url, invoke_param,
                                  created_by, created_dt, updated_by, updated_dt, version)
VALUES ('CBR', 4, 'static', '[{"chargeCode":"cbr","name":"默认规格","value":"cbr"}]', null, null, 'admin',
        '2021-02-23 14:11:50', 'admin', '2021-02-23 14:11:57', 1);

delete
from biz_billing_strategy_serving
where resource_type = 'CBR';
INSERT INTO biz_billing_strategy_serving (id, strategy_org_id, billing_strategy_id, service_id, spec_charge_ids,
                                          extra_charge, extra_charge_strategy, extra_hour_price, extra_month_price,
                                          extra_once_price, org_discount, org_sid, created_by, created_dt, updated_by,
                                          updated_dt, version, resource_type)
VALUES (13, null, null, 38, '', 0, '', null, null, null, 0, null, 'right', '2024-10-22 14:32:39', 'right',
        '2024-10-22 14:32:39', 1, 'CBR');

# 定时任务--bss
delete
from sys_m_job
where quartz_job_name = 'query-cbr-status-task'
  and sys_job_name = '定时查询CBR状态';
INSERT INTO `sys_m_job` (`sys_job_name`, `sys_job_type`, `sys_job_group`, `quartz_job_name`, `job_fully_qualified_name`,
                         `cron_expression`, `misfire_policy`, `job_param`, `state`, `last_fire_time`, `user_sid`,
                         `created_by`, `create_dt`, `updated_by`, `updated_dt`, `version`, `sys_job_name_us`)
VALUES ('定时查询CBR状态', 'TASK', 'RESOURCE_SERVICE', 'query-cbr-status-task',
        'cn.com.cloudstar.rightcloud.schedule.system.task.cbr.QueryCbrStatusTask', '0 0/1 * * * ? *',
        'MISFIRE_INSTRUCTION_FIRE_ONCE_NOW', '', 'normal', NULL, 1, 'admin', '2024-09-10 11:00:15', 'admin',
        '2024-10-17 09:58:00', 1, 'Query CBR status');





#接口权限--（bss、rightcloud）
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'atom', '云资源列表分页查询',
        'cloudResourceListPaginationQuery', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918079)
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918079, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'atom', '云资源列表详情查询', 'cloudResourceListDetailsQuery',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918083)
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918083, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryFormTemplatePagination', 'atom', '查询表单模板分页', 'queryFormTemplatePagination',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918110)
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918110, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryFormTemplatePagination', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryFormTemplateDetails', 'atom', '查询表单模板详情', 'queryFormTemplateDetails',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918114)
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918114, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryFormTemplateDetails', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'atom', '查询云平台定义列表',
        'queryListCloudPlatformDefinitions', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918119)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918119, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'atom', '根据条件获取单个资源操作的apiParam',
        'obtainApiparamsSingleResourceOperationBasedConditions', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918145)
  and action_auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918145, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'atom', '查询当前用户下托管的云环境列表',
        'queryListCloudEnvironmentsHostedByCurrentUser', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup',
        'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918322)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918322, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'admin', current_timestamp,
        'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'atom', '查询云环境详情', 'queryCloudEnvironmentDetails',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918324)
  and action_auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918324, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pageQueryRepository', 'atom', '分页查询存储库', 'pageQueryRepository', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918435)
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918435, 'res:DB04:cloudBackup:pageQueryRepository', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pageQueryRepository', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pageQueryRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pageQueryRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pageQueryBackup', 'atom', '分页查询备份', 'pageQueryBackup', 'res:DB04:cloudBackup', 'resource',
        '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918445)
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918445, 'res:DB04:cloudBackup:pageQueryBackup', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pageQueryBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pageQueryBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pageQueryBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:vmlist', 'atom', 'vmList', 'vmlist', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918448)
  and action_auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918448, 'res:DB04:cloudBackup:vmlist', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:vmlist', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:vmlist', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:vmlist', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pagingQueryBackupStrategy', 'atom', '分页查询备份策略', 'pagingQueryBackupStrategy',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918449)
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918449, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:policy', 'atom', 'policy', 'policy', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918450)
  and action_auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918450, 'res:DB04:cloudBackup:policy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:policy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:policy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:policy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'atom', '分页查询服务配置', 'pagingQueryServiceConfiguration',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918664)
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918664, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'atom', '服务申请查询指定服务配置详情',
        'serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918667)
  and action_auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918667, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'atom', '查询服务配置与模板节点',
        'queryServiceConfigurationTemplateNodes', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918679)
  and action_auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918679, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'admin', current_timestamp,
        'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'atom', '查询服务申请共通配置属性list',
        'queryListCommonConfigurationAttributesServiceApplication', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918682)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918682, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup', 'atom', '云备份', 'cloudBackup', 'res:DB04', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in
      (11728370918079, 11728370918083, 11728370918110, 11728370918114, 11728370918119, 11728370918145, 11728370918322,
       11728370918324, 11728370918435, 11728370918445, 11728370918448, 11728370918449, 11728370918450, 11728370918664,
       11728370918667, 11728370918679, 11728370918682)
  and action_auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918079, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918083, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918110, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918114, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918119, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918145, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918322, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918324, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918435, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918445, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918448, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918449, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918450, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918664, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918667, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918679, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918682, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp, 1,
        'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp, 1,
        'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:createRepository';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:createRepository', 'atom', '创建存储库', 'createRepository', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918437)
  and action_auth_key = 'res:DB04:cloudBackup:createRepository';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918437, 'res:DB04:cloudBackup:createRepository', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:createRepository';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:createRepository', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:createRepository';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:createRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:createRepository';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:createRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'atom', '云资源列表分页查询',
        'cloudResourceListPaginationQuery', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918079)
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918079, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListPaginationQuery';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:cloudResourceListPaginationQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'atom', '云资源列表详情查询', 'cloudResourceListDetailsQuery',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918083)
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918083, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:cloudResourceListDetailsQuery';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:cloudResourceListDetailsQuery', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryFormTemplatePagination', 'atom', '查询表单模板分页', 'queryFormTemplatePagination',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918110)
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918110, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryFormTemplatePagination', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplatePagination';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryFormTemplatePagination', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryFormTemplateDetails', 'atom', '查询表单模板详情', 'queryFormTemplateDetails',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918114)
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918114, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryFormTemplateDetails', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryFormTemplateDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryFormTemplateDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'atom', '查询云平台定义列表',
        'queryListCloudPlatformDefinitions', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918119)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918119, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCloudPlatformDefinitions', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'atom', '根据条件获取单个资源操作的apiParam',
        'obtainApiparamsSingleResourceOperationBasedConditions', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918145)
  and action_auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918145, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:obtainApiparamsSingleResourceOperationBasedConditions', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'atom', '查询当前用户下托管的云环境列表',
        'queryListCloudEnvironmentsHostedByCurrentUser', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup',
        'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918322)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918322, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'admin', current_timestamp,
        'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCloudEnvironmentsHostedByCurrentUser', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'atom', '查询云环境详情', 'queryCloudEnvironmentDetails',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918324)
  and action_auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918324, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryCloudEnvironmentDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryCloudEnvironmentDetails', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:getCloudEnvironmentView';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:getCloudEnvironmentView', 'atom', '获取云环境视图', 'getCloudEnvironmentView',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918328)
  and action_auth_key = 'res:DB04:cloudBackup:getCloudEnvironmentView';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918328, 'res:DB04:cloudBackup:getCloudEnvironmentView', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:getCloudEnvironmentView';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:getCloudEnvironmentView', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:getCloudEnvironmentView';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:getCloudEnvironmentView', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:getCloudEnvironmentView';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:getCloudEnvironmentView', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pageQueryRepository', 'atom', '分页查询存储库', 'pageQueryRepository', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918435)
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918435, 'res:DB04:cloudBackup:pageQueryRepository', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pageQueryRepository', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pageQueryRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryRepository';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pageQueryRepository', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:repositoryDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:repositoryDetails', 'atom', '存储库详情', 'repositoryDetails', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918439)
  and action_auth_key = 'res:DB04:cloudBackup:repositoryDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918439, 'res:DB04:cloudBackup:repositoryDetails', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:repositoryDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:repositoryDetails', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:repositoryDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:repositoryDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:repositoryDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:repositoryDetails', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:bindingStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:bindingStrategy', 'atom', '绑定策略', 'bindingStrategy', 'res:DB04:cloudBackup', 'resource',
        '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918441)
  and action_auth_key = 'res:DB04:cloudBackup:bindingStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918441, 'res:DB04:cloudBackup:bindingStrategy', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:bindingStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:bindingStrategy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:bindingStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:bindingStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:bindingStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:bindingStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:bindServer';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:bindServer', 'atom', '绑定服务器', 'bindServer', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918442)
  and action_auth_key = 'res:DB04:cloudBackup:bindServer';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918442, 'res:DB04:cloudBackup:bindServer', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:bindServer';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:bindServer', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:bindServer';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:bindServer', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:bindServer';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:bindServer', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:serverList';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:serverList', 'atom', '服务器列表', 'serverList', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918444)
  and action_auth_key = 'res:DB04:cloudBackup:serverList';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918444, 'res:DB04:cloudBackup:serverList', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:serverList';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:serverList', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serverList';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:serverList', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serverList';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:serverList', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pageQueryBackup', 'atom', '分页查询备份', 'pageQueryBackup', 'res:DB04:cloudBackup', 'resource',
        '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918445)
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918445, 'res:DB04:cloudBackup:pageQueryBackup', 'admin', current_timestamp, 'admin', current_timestamp,
        1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pageQueryBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pageQueryBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pageQueryBackup';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pageQueryBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:vmlist', 'atom', 'vmList', 'vmlist', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918448)
  and action_auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918448, 'res:DB04:cloudBackup:vmlist', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:vmlist', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:vmlist', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:vmlist';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:vmlist', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pagingQueryBackupStrategy', 'atom', '分页查询备份策略', 'pagingQueryBackupStrategy',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918449)
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918449, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryBackupStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pagingQueryBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:policy', 'atom', 'policy', 'policy', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918450)
  and action_auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918450, 'res:DB04:cloudBackup:policy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:policy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:policy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:policy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:policy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp,
        1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:updateBackupStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:updateBackupStrategy', 'atom', '更新备份策略', 'updateBackupStrategy', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918451)
  and action_auth_key = 'res:DB04:cloudBackup:updateBackupStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918451, 'res:DB04:cloudBackup:updateBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:updateBackupStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:updateBackupStrategy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:updateBackupStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:updateBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:updateBackupStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:updateBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:createBackupStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:createBackupStrategy', 'atom', '创建备份策略', 'createBackupStrategy', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918452)
  and action_auth_key = 'res:DB04:cloudBackup:createBackupStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918452, 'res:DB04:cloudBackup:createBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:createBackupStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:createBackupStrategy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:createBackupStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:createBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:createBackupStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:createBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:deleteBackupStrategy';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:deleteBackupStrategy', 'atom', '删除备份策略', 'deleteBackupStrategy', 'res:DB04:cloudBackup',
        'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918453)
  and action_auth_key = 'res:DB04:cloudBackup:deleteBackupStrategy';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918453, 'res:DB04:cloudBackup:deleteBackupStrategy', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:deleteBackupStrategy';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:deleteBackupStrategy', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:deleteBackupStrategy';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:deleteBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:deleteBackupStrategy';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:deleteBackupStrategy', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin',
        current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'atom', '分页查询服务配置', 'pagingQueryServiceConfiguration',
        'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918664)
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918664, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:pagingQueryServiceConfiguration';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:pagingQueryServiceConfiguration', 'action', 'DB04', 0, 'admin', current_timestamp,
        'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'atom', '服务申请查询指定服务配置详情',
        'serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918667)
  and action_auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918667, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:serviceApplicationQuerySpecifiesServiceConfigurationDetails', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'atom', '查询服务配置与模板节点',
        'queryServiceConfigurationTemplateNodes', 'res:DB04:cloudBackup', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918679)
  and action_auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918679, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'admin', current_timestamp,
        'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'admin', current_timestamp, 'admin',
        current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryServiceConfigurationTemplateNodes', 'action', 'DB04', 0, 'admin',
        current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'atom', '查询服务申请共通配置属性list',
        'queryListCommonConfigurationAttributesServiceApplication', 'res:DB04:cloudBackup', 'resource', '云备份',
        'cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in (11728370918682)
  and action_auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918682, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup:queryListCommonConfigurationAttributesServiceApplication', 'action', 'DB04', 0,
        'admin', current_timestamp, 'admin', current_timestamp, 1, 'atom');
delete
from sys_auth_action
where auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code, description,
                             description_en, created_by, created_dt, updated_by, updated_dt, version)
VALUES ('res:DB04:cloudBackup', 'atom', '云备份', 'cloudBackup', 'res:DB04', 'resource', '云备份', 'cloudBackup', 'admin',
        current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_action_url_rela
where interface_url_id in
      (11728370918079, 11728370918083, 11728370918110, 11728370918114, 11728370918119, 11728370918145, 11728370918322,
       11728370918324, 11728370918328, 11728370918435, 11728370918439, 11728370918441, 11728370918442, 11728370918444,
       11728370918445, 11728370918448, 11728370918449, 11728370918450, 11728370918451, 11728370918452, 11728370918453,
       11728370918664, 11728370918667, 11728370918679, 11728370918682)
  and action_auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                 version)
VALUES (11728370918079, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918083, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918110, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918114, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918119, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918145, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918322, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918324, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918328, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918435, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918439, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918441, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918442, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918444, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918445, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918448, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918449, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918450, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918451, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918452, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918453, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918664, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918667, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918679, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1),
       (11728370918682, 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
FROM sys_module_action_rela
WHERE module_auth_key = 'DB04'
  and action_auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by, updated_dt,
                                    version)
VALUES ('DB04', 'res:DB04:cloudBackup', 'admin', current_timestamp, 'admin', current_timestamp, 1);
delete
from sys_group_module_rela
where group_sid = 1
  and rela_type = 'action'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                   updated_by, updated_dt, version, auth_type)
VALUES (1, 'res:DB04:cloudBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp, 1,
        'whiteList');
delete
from sys_role_module_rela
where role_id = 302
  and rela_type = 'action'
  and auth_type = 'atom'
  and parent_module = 'DB04'
  and auth_key = 'res:DB04:cloudBackup';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by, created_dt,
                                  updated_by, updated_dt, version, auth_type)
VALUES (302, 'res:DB04:cloudBackup', 'action', 'DB04', 0, 'admin', current_timestamp, 'admin', current_timestamp, 1,
        'atom');

-- 折扣 --bss
delete
from sys_m_code
where CODE_CATEGORY = 'DISCOUNT_PRODUCT_TYPE'
  and CODE_VALUE = 'CBR';
INSERT INTO sys_m_code (`CODE_CATEGORY`, `CODE_VALUE`, `CODE_DISPLAY`, `PARENT_CODE_VALUE`, `ENABLED`, `SORT`,
                        `ATTRIBUTE_1`, `ATTRIBUTE_2`, `ATTRIBUTE_3`, `ATTRIBUTE_4`, `ATTRIBUTE_5`,
                        `ATTRIBUTE_6`, `DESCRIPTION`, `CREATED_BY`, `CREATED_DT`, `UPDATED_BY`, `UPDATED_DT`,
                        `VERSION`, `code_display_us`)
VALUES ('DISCOUNT_PRODUCT_TYPE', 'CBR', '云备份', '', 1, 17, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        NULL, 1, 'Cloud Backup');

delete
from sys_m_code
where CODE_CATEGORY = 'DISCOUNT_PRODUCT_TYPE'
  and CODE_VALUE = 'MRS';
INSERT INTO sys_m_code (`CODE_CATEGORY`, `CODE_VALUE`, `CODE_DISPLAY`, `PARENT_CODE_VALUE`, `ENABLED`, `SORT`,
                        `ATTRIBUTE_1`, `ATTRIBUTE_2`, `ATTRIBUTE_3`, `ATTRIBUTE_4`, `ATTRIBUTE_5`,
                        `ATTRIBUTE_6`, `DESCRIPTION`, `CREATED_BY`, `CREATED_DT`, `UPDATED_BY`, `UPDATED_DT`,
                        `VERSION`, `code_display_us`)
VALUES ('DISCOUNT_PRODUCT_TYPE', 'MRS', 'MapReduce服务', '', 1, 18, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        NULL,
        NULL, 1, 'Cloud Backup');


delete
from sys_m_code
where CODE_CATEGORY = 'CHARGE_RESOURCE_TYPE'
  and CODE_VALUE = 'CBR';
INSERT INTO sys_m_code (CODE_CATEGORY, CODE_VALUE, CODE_DISPLAY, PARENT_CODE_VALUE, ENABLED, SORT, ATTRIBUTE_1,
                        ATTRIBUTE_2, ATTRIBUTE_3, ATTRIBUTE_4, ATTRIBUTE_5, ATTRIBUTE_6, DESCRIPTION, CREATED_BY,
                        CREATED_DT, UPDATED_BY, UPDATED_DT, VERSION, code_display_us)
VALUES ('CHARGE_RESOURCE_TYPE', 'CBR', '云备份', '', 1, 23, null, '', '
[{"cloudEnvType":"HuaweiCloud","support":true,"charge":true,"chargeType":["PrePaid","PostPaid"]},
{"cloudEnvType":"HCSO","support":true,"charge":true,"chargeType":["PrePaid","PostPaid"]}]', null, null, null, null,
        null, null, null, null, 1, 'Cloud Backup');


delete
from sys_m_code
where CODE_CATEGORY = 'BILLING_RESOURCE_TYPE'
  and PARENT_CODE_VALUE = 'CBR';
INSERT INTO sys_m_code (CODE_CATEGORY, CODE_VALUE, CODE_DISPLAY, PARENT_CODE_VALUE, ENABLED, SORT, ATTRIBUTE_1,
                        ATTRIBUTE_2, ATTRIBUTE_3, ATTRIBUTE_4, ATTRIBUTE_5, ATTRIBUTE_6, DESCRIPTION, CREATED_BY, CREATED_DT, UPDATED_BY, UPDATED_DT, VERSION, code_display_us) VALUES ( 'BILLING_RESOURCE_TYPE', 'blockStorage', '存储资源', 'CBR', 1, 1, null, null, null, null, null, null, null, null, null, null, null, 1, 'Storage Resources');


delete
from sf_service_category
where service_type = 'SEC-MASTER' or service_name='安全云脑';
INSERT INTO sf_service_category (id,service_form, service_type, service_class, service_owner_id, service_owner_name,
                                 service_name, service_desc, status, open_type, service_icon_path, service_config,
                                 service_workflow_id, created_by, created_dt, updated_by, updated_dt, version,
                                 org_sid, price, service_details, res_flag, service_component, resource_visible,
                                 instance_visible, publish_status, publish_dt, product_name, product_desc,
                                 show_type, sort_number, show_type_id, behalf_status, product_code, editable,
                                 policy, entity_id, entity_name, charging_type, ccsp_mac, service_url)
VALUES (1837335709049163782,'SEC-MASTER', 'SEC-MASTER', 'public', 100, 'admin', '安全云脑', null, 'using', 'manual', null, null, null, 'admin',
        '2023-12-11 11:11:11', 'shupfadmin', '2024-10-08 14:05:46', 1, null, null, null, null, 'innerService', null,
        null, 'succeed', '2024-10-08 14:05:46', '安全云脑', null, 'SEC-MASTER', null, null, '0', 'SEC-MASTER', 1,
        '{"internal":[{"permissionType":"project","name":"SecMaster FullAccess"}]}', 1, '默认运营实体', null, '',
        '/appcmp/console/storage/secmaster');

