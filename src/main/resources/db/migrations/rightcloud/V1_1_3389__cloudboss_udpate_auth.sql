-- DE01 ROMACONNECT
delete from sys_auth_action where auth_key ='res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_auth_action (auth_key, auth_type, name, name_en, parent_auth_key, service_code,description, description_en, created_by, created_dt, updated_by,updated_dt, version) VALUES ('res:DE01:romaconnect:obtainWhetherUsersProductActivated','atom','获取用户产品是否开通','obtainWhetherUsersProductActivated','res:DE01:romaconnect','resource','','','admin',current_timestamp,'admin',current_timestamp,1);

delete from sys_action_url_rela where interface_url_id in (11728370916930) and action_auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (11728370916930,'res:DE01:romaconnect:obtainWhetherUsersProductActivated','admin',current_timestamp,'admin',current_timestamp,1);

delete FROM sys_module_action_rela WHERE module_auth_key = 'DE01' and action_auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_module_action_rela (module_auth_key, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES ('DE01','res:DE01:romaconnect:obtainWhetherUsersProductActivated','admin',current_timestamp,'admin',current_timestamp,1);

delete from sys_group_module_rela where group_sid = 1 and rela_type = 'action' and parent_module = 'DE01' and auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by,created_dt, updated_by, updated_dt, version, auth_type) VALUES (1,'res:DE01:romaconnect:obtainWhetherUsersProductActivated','action','DE01',0,'admin',current_timestamp,'admin',current_timestamp,1,'whiteList');

delete from sys_group_module_rela where group_sid = 5 and rela_type = 'action' and parent_module = 'DE01' and auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by,created_dt, updated_by, updated_dt, version, auth_type) VALUES (5,'res:DE01:romaconnect:obtainWhetherUsersProductActivated','action','DE01',0,'admin',current_timestamp,'admin',current_timestamp,1,'whiteList');

delete from sys_group_module_rela where group_sid = 6 and rela_type = 'action' and parent_module = 'DE01' and auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_group_module_rela (group_sid, auth_key, rela_type, parent_module, critical, created_by,created_dt, updated_by, updated_dt, version, auth_type) VALUES (6,'res:DE01:romaconnect:obtainWhetherUsersProductActivated','action','DE01',0,'admin',current_timestamp,'admin',current_timestamp,1,'whiteList');

delete from sys_role_module_rela where role_id = 302 and rela_type = 'action' and auth_type = 'atom' and parent_module = 'DE01' and auth_key = 'res:DE01:romaconnect:obtainWhetherUsersProductActivated';
INSERT INTO sys_role_module_rela (role_id, auth_key, rela_type, parent_module, critical, created_by,created_dt, updated_by, updated_dt, version, auth_type) VALUES (302,'res:DE01:romaconnect:obtainWhetherUsersProductActivated','action','DE01',0,'admin',current_timestamp,'admin',current_timestamp,1,'atom');


UPDATE `res_define_grid` SET `res_type_code` = 'NAT', `name` = '【勿删】通用NAT网关列表-pf', `name_en` = NULL, `code` = 'nat', `description` = '通用NAT网关列表', `status` = 'enable', `base_info` = '{\"gridName\":\"【勿删】通用NAT网关列表\",\"rowKey\":\"id\",\"rowSelect\":\"none\",\"operateWidth\":150,\"operateWidthEn\":200,\"operateFixed\":true,\"isStripe\":false}', `datasource_info` = '{\"sourceType\":\"api\",\"pageType\":\"server\",\"apiPath\":\"/resource/v1/nat_gateway\",\"apiParams\":[],\"staticData\":\"[]\"}', `column_info` = '[{\"id\":\"1689076157831\",\"fieldName\":\"名称\",\"fieldKey\":\"name\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":true,\"fixPostion\":\"left\",\"isSorted\":true,\"sortAttr\":\"\",\"renderMode\":\"link\",\"customScript\":\"\",\"relationOperate\":\"17428893484480\"},{\"id\":\"1689076325605\",\"fieldName\":\"区域Region\",\"fieldKey\":\"cloudEnvName\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"env\",\"customScript\":\"\"},{\"id\":\"1689076377997\",\"fieldName\":\"状态\",\"fieldKey\":\"status\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"status\",\"customScript\":\"\"},{\"id\":\"1689076177885\",\"fieldName\":\"虚拟私有云\",\"fieldKey\":\"vpcName\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1689076208159\",\"fieldName\":\"子网名称\",\"fieldKey\":\"subnetName\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1689076227428\",\"fieldName\":\"子网网段\",\"fieldKey\":\"cidr\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1689076246389\",\"fieldName\":\"规格\",\"fieldKey\":\"natTypeName\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1689076277797\",\"fieldName\":\"资源组织\",\"fieldKey\":\"orgName\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1689076309053\",\"fieldName\":\"开始时间\",\"fieldKey\":\"createdDt\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1742956486957\",\"fieldName\":\"结束时间\",\"fieldKey\":\"endTime\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"}]', `search_info` = '[{\"id\":\"1689076086413\",\"searchName\":\"名称\",\"searchField\":\"nameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"}]', `action_info` = '[{\"id\":\"17043392796480\",\"operateName\":\"申请服务\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:create\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-plus-o-1\",\"operateTheme\":\"primary\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"route\",\"modalWidth\":600,\"routePath\":\"/appcmp/console/storage/nat/apply/${selfId}\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/nat\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"NAT\\\",\\r\\n    \\\"action\\\": \\\"create\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"name\\\":\\\"${name}\\\",\\r\\n        \\\"natTypeId\\\":\\\"${vmTypeId.0.id}\\\",\\r\\n        \\\"chargeType\\\":\\\"${chargeType}\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\",\\r\\n        \\\"port\\\":{\\r\\n            \\\"vpc\\\": {\\r\\n                \\\"id\\\":\\\"${network.vpcId}\\\"\\r\\n            },\\r\\n            \\\"subnet\\\":{\\r\\n                \\\"id\\\":\\\"${network.subnetId}\\\"\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"selfId\":1394649305169921,\"btnGroup\":\"env\"},{\"id\":\"17049576344930\",\"operateName\":\"编辑\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:update\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n  return record.status === \\\"active\\\" || record.status === \\\"updateError\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"700px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/nat_gateway\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"NAT\\\",\\r\\n    \\\"action\\\": \\\"modify\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\":\\\"${id}\\\",\\r\\n        \\\"name\\\":\\\"${name}\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\",\"popupForm\":1396701860372480},{\"id\":\"17429742746870\",\"operateName\":\"调整配置\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:reize\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n  return (record.status === \\\"active\\\" || record.status === \\\"updateError\\\")&&record.resourceStatus===\'normal\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427844289830912,\"modalWidth\":\"700px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/resize_resource\",\"submitParams\":\"{\\r\\n\\t\\\"productInfo\\\": {\\r\\n\\t\\t\\\"id\\\": \\\"${resourceId}\\\",\\r\\n\\t\\t\\\"resourceType\\\": \\\"NAT\\\",\\r\\n\\t\\t\\\"size\\\": 1,\\r\\n\\t\\t\\\"spec\\\": \\\"${vmTypeId.0.name}\\\"\\r\\n\\t},\\r\\n\\t\\\"projectId\\\": \\\"${orgId}\\\",\\r\\n\\t\\\"resourceInfo\\\": {\\r\\n\\t\\t\\t\\\"action\\\": \\\"update\\\",\\r\\n\\t        \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n\\t        \\\"data\\\": {\\r\\n\\t\\t        \\\"description\\\": \\\"${description}\\\",\\r\\n\\t\\t        \\\"id\\\": \\\"${id}\\\",\\r\\n\\t\\t        \\\"name\\\": \\\"${name}\\\",\\r\\n\\t\\t        \\\"natTypeId\\\":\\\"${vmTypeId.0.id}\\\"\\r\\n\\t        },\\r\\n\\t    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n\\t    \\\"resTypeCode\\\": \\\"NAT\\\"\\r\\n\\t}\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"change\",\"inquiryType\":\"get\",\"inquiryPath\":\"/v1/bss/billing/modify/inquiry/price\",\"inquiryParams\":\"  {\\r\\n    \\\"id\\\": \\\"${resourceId}\\\",\\r\\n    \\\"targetType\\\": \\\"NAT\\\",\\r\\n    \\\"targetSize\\\": 1,\\r\\n    \\\"targetSpec\\\": \\\"${vmTypeId.0.name}\\\"\\r\\n}\",\"popupType\":\"modal\"},{\"id\":\"17050278317480\",\"operateName\":\"设置规则\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:createRule\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n  return record.status === \\\"active\\\" || record.status === \\\"updateError\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1396758802243584,\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17428833157840\",\"operateName\":\"续订\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:renew\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\nreturn record.resourceId && record.status === \\\"active\\\"&&record.resourceStatus===\'normal\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/renew\",\"submitParams\":\"{\\r\\n\\t\\\"projectId\\\": \\\"${orgId}\\\",\\r\\n    \\\"couponSid\\\":\\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": \\\"${productInfo}\\\"\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"renew\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/renew/inquiry/price\",\"inquiryParams\":\"{\\n    \\\"couponId\\\": \\\"${couponId}\\\",\\n    \\\"id\\\": \\\"${id}\\\",\\n    \\\"period\\\":\\\"${period}\\\",\\n    \\\"productCode\\\":\\\"NAT\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1427773658988544},{\"id\":\"17049576437440\",\"operateName\":\"退订\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:delete\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return record.resourceId &&record.resourceStatus===\'normal\'&& (record.status === \\\"active\\\" || record.status === \\\"updateError\\\")\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    return record.resourceId\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/v1/bss/service/unsubscribe/${resourceId}?type=NAT\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\",\"popupForm\":1427773718233088},{\"id\":\"17344871600480\",\"operateName\":\"删除\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:delete\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return !record.resourceId && (record.status === \\\"active\\\" || record.status === \\\"updateError\\\")\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    return !record.resourceId\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/nat_gateway\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"NAT\\\",\\r\\n    \\\"action\\\": \\\"delete\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\":\\\"${id}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\"},{\"id\":\"17050446460300\",\"operateName\":\"创建DNAT规则\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:createDnat\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"hide\",\"operateIcon\":\"icon-plus-o-1\",\"operateTheme\":\"primary\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427832547745792,\"modalWidth\":\"750px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/dnat_rules\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DnatRule\\\",\\r\\n    \\\"action\\\": \\\"create\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"natGatewayId\\\":\\\"${id}\\\",\\r\\n        \\\"elasticIpid\\\":\\\"${eipId}\\\",\\r\\n        \\\"portId\\\":\\\"${privateIp}\\\",\\r\\n        \\\"internalServicePort\\\":\\\"${internalServicePort}\\\",\\r\\n        \\\"externalServicePort\\\":\\\"${externalServicePort}\\\",\\r\\n        \\\"protocolStr\\\": \\\"${protocol}\\\",\\r\\n        \\\"sourceTypeStr\\\": \\\"${source_type}\\\",\\r\\n        \\\"portTypeStr\\\":\\\"${port_type}\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17050448534340\",\"operateName\":\"创建SNAT规则\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:createSnat\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"hide\",\"operateIcon\":\"icon-plus-o-1\",\"operateTheme\":\"primary\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427782251921408,\"modalWidth\":\"750px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/snat_rules\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"SnatRule\\\",\\r\\n    \\\"action\\\": \\\"create\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"natGatewayId\\\":\\\"${id}\\\",\\r\\n        \\\"elasticIpIds\\\":\\\"${eipId}\\\",\\r\\n        \\\"port\\\":{\\r\\n            \\\"subnet\\\":{\\r\\n                \\\"id\\\":\\\"${subnetId}\\\"\\r\\n            }\\r\\n        },\\r\\n        \\\"cidr\\\": \\\"${cidr}\\\",\\r\\n        \\\"sourceTypeStr\\\": \\\"0\\\",\\r\\n        \\\"cidrTypeStr\\\":\\\"0\\\",\\r\\n        \\\"sourceCidr\\\": \\\"${source_cidr}\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17428893484480\",\"operateName\":\"查看详情\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:get\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427774932320256,\"modalWidth\":\"1200px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17430434079620\",\"operateName\":\"编辑SNAT规则\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:updateSnat\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427901074448384,\"modalWidth\":\"750px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/snat_rules\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"SnatRule\\\",\\r\\n    \\\"action\\\": \\\"update\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\":\\\"${id}\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\",\\r\\n        \\\"elasticIpIds\\\":\\\"${eipId}\\\",\\r\\n        \\\"natGatewayId\\\": \\\"${natGatewayId}\\\",\\r\\n        \\\"sourceCidr\\\": \\\"${cidr}\\\",\\r\\n        \\\"sourceTypeStr\\\": \\\"0\\\"\\r\\n        \\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"modal\"},{\"id\":\"17430434079621\",\"operateName\":\"编辑DNAT规则\",\"operateNameEn\":\"\",\"operateCode\":\"res:NAT:updateDnat\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1427901105815552,\"modalWidth\":\"750px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/dnat_rules\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DnatRule\\\",\\r\\n    \\\"action\\\": \\\"update\\\",\\r\\n    \\\"data\\\": {\\r\\n\\t\\t\\\"description\\\": \\\"${description}\\\",\\r\\n\\t\\t\\\"exampleType\\\": \\\"vms\\\",\\r\\n\\t\\t\\\"externalServicePort\\\": \\\"${externalServicePort}\\\",\\r\\n\\t\\t\\\"elasticIpid\\\": \\\"${eipId}\\\",\\r\\n\\t\\t\\\"id\\\": \\\"${id}\\\",\\r\\n\\t\\t\\\"internalServicePort\\\": \\\"${internalServicePort}\\\",\\r\\n\\t\\t\\\"natGatewayId\\\": \\\"${natGatewayId}\\\",\\r\\n\\t\\t\\\"portId\\\": \\\"${privateIp}\\\",\\r\\n\\t\\t\\\"protocolStr\\\": \\\"${protocol}\\\"\\r\\n\\t}\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"modal\"}]', `operable` = 'enable', `created_by` = 'lqy001', `created_dt` = '2023-07-11 19:47:42', `updated_by` = 'lqy001', `updated_dt` = '2023-07-11 19:47:42', `version` = NULL WHERE `id` = 1383691110367232;
UPDATE `res_define_grid` SET `res_type_code` = 'ECS', `name` = '【勿删】弹性云服务器ECS-pf', `name_en` = NULL, `code` = 'SERVICE ECS', `description` = NULL, `status` = 'enable', `base_info` = '{\"gridName\":\"【勿删】弹性云服务器ECS\",\"rowKey\":\"id\",\"rowSelect\":\"checkbox\",\"operateWidth\":150,\"operateWidthEn\":230,\"operateFixed\":true,\"isStripe\":true}', `datasource_info` = '{\"sourceType\":\"api\",\"pageType\":\"server\",\"apiPath\":\"/resource/v1/vms\",\"apiParams\":[{\"name\":\"isRecycle\",\"type\":\"fixed\",\"value\":\"false\"}],\"staticData\":\"[]\"}', `column_info` = '[{\"id\":\"1686971428465\",\"fieldName\":\"名称\",\"fieldKey\":\"name\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":true,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"link\",\"customScript\":\"\",\"fixPostion\":\"left\",\"relationOperate\":\"16885583347960\"},{\"id\":\"1686971729066\",\"fieldName\":\"IP地址\",\"fieldKey\":\"ipAddress\",\"fieldEnName\":\"\",\"columnWidth\":130,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n const innerTxt = record.innerIp ? \\\"(内)\\\" + record.innerIp : \'\'\\n const publicTxt = record.publicIp ? \\\"(外)\\\" + record.publicIp : \'\'\\n return innerTxt +\\\"<br/>\\\"+ publicTxt\\n}\"},{\"id\":\"1686972747432\",\"fieldName\":\"状态\",\"fieldKey\":\"status\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"status\",\"customScript\":\"(text, record) => {\\r\\n    if(record.isCce){\\r\\n        record.statusAppendInfo = \'CCE使用中\'\\r\\n    }\\r\\n    return \'\'\\r\\n}\"},{\"id\":\"1686971473457\",\"fieldName\":\"主机规格\",\"fieldKey\":\"hostSpec\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n if(record.cpu && record.memory){\\n return \'cpu：\'+record.cpu + \'核，内存：\'+ record.memory +\'GB\'\\n } else if(!record.cpu && record.memory){\\n return \'cpu：--，内存：\'+ record.memory+\'GB\'\\n } else if(record.cpu && !record.memory){\\n return \'cpu：\'+record.cpu + \'核，内存：--\'\\n } else {\\n return \'--\'\\n }\\n}\"},{\"id\":\"1687140111196\",\"fieldName\":\"主机硬盘\",\"fieldKey\":\"diskSize\",\"fieldEnName\":\"\",\"columnWidth\":190,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n let sysInfo = record.systemDiskInfo?\'系统盘：\'+record.systemDiskInfo:\\\"\\\"\\n let dataInfo = record.dataDiskInfo?\'数据盘：\'+ record.dataDiskInfo:\\\"\\\"\\n let symbol = sysInfo&&dataInfo?\\\",\\\":\\\"\\\"\\n return sysInfo || dataInfo? sysInfo +symbol+ dataInfo : \\\"--\\\"\\n}\"},{\"id\":\"1724997851226\",\"fieldName\":\"磁盘总容量\",\"fieldKey\":\"diskCapacity\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1686971376719\",\"fieldName\":\"操作系统\",\"fieldKey\":\"imageName\",\"fieldEnName\":\"\",\"columnWidth\":180,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1686973154869\",\"fieldName\":\"所属网络\",\"fieldKey\":\"network\",\"fieldEnName\":\"Network\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n if(record.vpcName && record.subnetName){\\n return record.vpcName +\'/\'+ record.subnetName\\n } else if(!record.vpcName && record.subnetName){\\n return \'--/\'+ record.subnetName\\n } else if(record.vpcName && !record.subnetName){\\n return record.vpcName + \'/--\'\\n } else {\\n return \'--\'\\n }\\n}\"},{\"id\":\"16966494317250\",\"fieldName\":\"区域Region\",\"fieldKey\":\"cloudEnvName\",\"fieldEnName\":\"\",\"columnWidth\":240,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"env\",\"customScript\":\"\"},{\"id\":\"1729079339187\",\"fieldName\":\"计费类型\",\"fieldKey\":\"chargeType\",\"fieldEnName\":\"\",\"columnWidth\":200,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"chargeType\",\"customScript\":\"(text,record)=>{\\n    const types={\\n        PrePaid: \'包年包月\',\\n        PostPaid: \'按量计费\',\\n        noPaid: \'不计费\'\\n    }\\n    return types[record.chargeType] || record.chargeType\\n}\"},{\"id\":\"1686969695619\",\"fieldName\":\"可用区\",\"fieldKey\":\"zoneName\",\"fieldEnName\":\"\",\"columnWidth\":140,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1686969548073\",\"fieldName\":\"资源组织\",\"fieldKey\":\"orgName\",\"fieldEnName\":\"\",\"columnWidth\":140,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1724997899322\",\"fieldName\":\"创建人\",\"fieldKey\":\"createdBy\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1729080639235\",\"fieldName\":\"开始时间\",\"fieldKey\":\"startTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1729079605725\",\"fieldName\":\"结束时间\",\"fieldKey\":\"endTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"\"}]', `search_info` = '[{\"id\":\"1686970408443\",\"searchName\":\"名称\",\"searchField\":\"nameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"},{\"id\":\"1724637011038\",\"searchName\":\"区域Region\",\"searchField\":\"cloudEnvId\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"api\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[{\"name\":\"name\",\"type\":\"fixed\",\"value\":\"cloudEnvName\"},{\"name\":\"value\",\"type\":\"fixed\",\"value\":\"id\"}],\"staticData\":\"\",\"defaultValue\":\"\",\"apiPath\":\"/resource/v1/cloud/envs/current_list\"},{\"id\":\"1686970469134\",\"searchName\":\"状态\",\"searchField\":\"status\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"api\",\"apiType\":\"get\",\"apiParams\":[{\"name\":\"resTypeCode\",\"type\":\"fixed\",\"value\":\"ECS\"},{\"name\":\"bind\",\"type\":\"fixed\",\"value\":\"true\"}],\"fieldMapping\":[{\"name\":\"name\",\"type\":\"fixed\",\"value\":\"codeDisplay\"},{\"name\":\"value\",\"type\":\"fixed\",\"value\":\"codeValue\"}],\"staticData\":\"[\\n {\\n \\\"label\\\": \\\"1\\\",\\n \\\"value\\\": \\\"1\\\"\\n }\\n]\",\"apiPath\":\"/resource/v1/define/resource_types/status\"},{\"id\":\"1715320835246\",\"searchName\":\"资源组织\",\"searchField\":\"orgId\",\"searchType\":\"treeSelect\",\"searchPosition\":\"dimension\",\"searchSource\":\"api\",\"apiType\":\"get\",\"apiParams\":[{\"name\":\"isCurrentUser\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"pagesize\",\"type\":\"fixed\",\"value\":\"100\"},{\"name\":\"pagenum\",\"type\":\"fixed\",\"value\":\"0\"}],\"fieldMapping\":[{\"name\":\"value\",\"type\":\"fixed\",\"value\":\"id\"},{\"name\":\"lable\",\"type\":\"fixed\",\"value\":\"name\"},{\"name\":\"name\",\"type\":\"fixed\",\"value\":\"name\"},{\"name\":\"title\",\"type\":\"fixed\",\"value\":\"name\"}],\"staticData\":\"\",\"defaultValue\":\"\",\"apiPath\":\"/v1/system/org/whole_org\"},{\"id\":\"1715320857693\",\"searchName\":\"IP地址\",\"searchField\":\"ip\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"}]', `action_info` = '[{\"id\":\"16869705206020\",\"operateName\":\"申请服务\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:create\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-plus-o-1\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"route\",\"modalWidth\":\"900\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"create\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"zoneId\\\": \\\"${zoneId}\\\",\\r\\n \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\\"projectUuid\\\":\\\"${projectUuid}\\\",\\r\\n \\\"imageId\\\": \\\"${imageId}\\\",\\r\\n \\\"vmTypeId\\\": \\\"${vmTypeId.0.id}\\\",\\r\\n \\\"name\\\": \\\"${name}\\\",\\r\\n \\\"loginType\\\": \\\"${loginType}\\\",\\r\\n \\\"managementAccount\\\": \\\"${loginuser.userName}\\\",\\r\\n \\\"managementPassword\\\": \\\"${password}\\\",\\r\\n \\\"diskConfigUseImage\\\": false,\\r\\n \\\"credentialId\\\": \\\"${keypair}\\\",\\r\\n \\\"systemDisk\\\": {\\r\\n \\\"size\\\": \\\"${systemDisk.volumeSize}\\\",\\r\\n \\\"vdType\\\": {\\r\\n \\\"id\\\": \\\"${systemDisk.volumeType}\\\"\\r\\n }\\r\\n },\\r\\n \\\"dataDisks[dataDisk]\\\": {\\r\\n \\\"size\\\": \\\"${item.volumeSize}\\\",\\r\\n \\\"releaseMode\\\": \\\"${item.isDeleteWithInstance}\\\",\\r\\n \\\"vdType\\\": {\\r\\n \\\"id\\\": \\\"${item.volumeType}\\\"\\r\\n }\\r\\n },\\r\\n \\\"securityGroups\\\": [{\\r\\n \\\"id\\\": \\\"${sgId}\\\"\\r\\n }],\\r\\n \\\"ports\\\": [\\r\\n {\\r\\n \\\"vpc\\\": {\\r\\n \\\"id\\\": \\\"${network.vpcId}\\\"\\r\\n },\\r\\n \\\"subnet\\\": {\\r\\n \\\"id\\\": \\\"${network.subnetId}\\\"\\r\\n },\\r\\n \\\"allocateIpv6\\\": false\\r\\n }\\r\\n ],\\r\\n \\\"elasticIp\\\": {\\r\\n \\\"allocate\\\": \\\"${allocate}\\\",\\r\\n \\\"allocateType\\\": \\\"${allocateType}\\\",\\r\\n \\\"id\\\": \\\"${eipId}\\\"\\r\\n },\\r\\n \\\"period\\\": \\\"${period}\\\",\\r\\n \\\"quantity\\\": \\\"${quantity}\\\",\\r\\n \\\"extra\\\": {\\r\\n \\\"productId\\\":\\\"${productId}\\\"\\r\\n }\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\",\"popupForm\":\"auto\",\"operateTheme\":\"primary\",\"refreshMode\":\"polling\",\"operateSubmitBtn\":\"createBtn\",\"submitFormConfig\":true,\"btnGroup\":\"env\",\"selfId\":1390111494152192,\"routePath\":\"/appcmp/console/storage/ecs/apply/${selfId}\"},{\"id\":\"17131492155530\",\"operateName\":\"回收站\",\"operateNameEn\":\"recycleBin\",\"operateCode\":\"res:ECS:list\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"hide\",\"operateIcon\":\"icon-delete-o\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"1000\",\"routePath\":\"/appcmp/console/storage/ecs/recycleBin\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"operateTheme\":\"default\",\"popupType\":\"drawer\",\"popupForm\":1403423940927488},{\"id\":\"16885580935560\",\"operateName\":\"导出\",\"operateNameEn\":\"Export\",\"operateCode\":\"res:ECS:export\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-public-export\",\"operateTheme\":\"default\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"buildIn\",\"modalWidth\":600,\"openConfirm\":true,\"buildInOperate\":\"export\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/export\",\"submitParams\":\"{\\n \\\"cloudEnvId\\\": \\\"${envId}\\\",\\n \\\"excelField\\\": \\\"name,cloudEnvName,uuid,zoneName,cpu,memory,imageName,systemDiskInfo,dataDiskInfo,diskCapacity,vpcName,subnetName,innerIp,publicIp,owner,createdBy,startTime,orgName\\\"\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16886341329150\",\"operateName\":\"重启\",\"operateNameEn\":\"Restart\",\"operateCode\":\"res:ECS:restart\",\"operateGroup\":\"状态操作\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status === \\\"active\\\" && \\n record.lockStatus !== \\\"locked\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"restart\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vmIds\\\": [\\r\\n \\\"${id}\\\"\\r\\n ],\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"name\\\": \\\"${name}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"重启云主机\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"refreshMode\":\"direct\"},{\"id\":\"16886341329151\",\"operateName\":\"启动\",\"operateNameEn\":\"Start\",\"operateCode\":\"res:ECS:start\",\"operateGroup\":\"状态操作\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status === \\\"stopped\\\" && \\n record.lockStatus !== \\\"locked\\\"&&record.systemDiskSize!==0\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"start\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vmIds\\\": [\\r\\n \\\"${id}\\\"\\r\\n ],\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"name\\\": \\\"${name}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"启动云主机\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"refreshMode\":\"direct\"},{\"id\":\"16886341329152\",\"operateName\":\"停止\",\"operateNameEn\":\"Stop\",\"operateCode\":\"res:ECS:stop\",\"operateGroup\":\"状态操作\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status === \\\"active\\\" && \\n record.lockStatus !== \\\"locked\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"stop\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vmIds\\\": [\\r\\n \\\"${id}\\\"\\r\\n ],\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"name\\\": \\\"${name}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"停止云主机\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"refreshMode\":\"direct\"},{\"id\":\"16886341329154\",\"operateName\":\"调整配置\",\"operateNameEn\":\"Resize\",\"operateCode\":\"res:ECS:change\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    const notExpired = record.chargeType === \'PrePaid\'?(record.endTime && (new Date(record.endTime) > new Date())) : true\\n    return notExpired && (record.status === \\\"stopped\\\") && record.lockStatus!==\\\"locked\\\"&&record.systemDiskSize!==0\\n}\",\"enableDesc\":\"主机未停止或已过期\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode) && record.resourceId\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/resize_resource\",\"submitParams\":\"{\\n    \\\"projectId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"productInfo\\\": {\\n        \\\"id\\\": \\\"${resourceId}\\\",\\n        \\\"resourceType\\\": \\\"ECS\\\",\\n        \\\"size\\\": 1,\\n        \\\"spec\\\": \\\"${vmTypeId.value}\\\"\\n    },\\n    \\\"resourceInfo\\\": {\\n         \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n         \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n         \\\"resTypeCode\\\": \\\"ECS\\\",\\n         \\\"action\\\": \\\"resize\\\",\\n         \\\"data\\\": \\\"${orderConfig.data}\\\",\\n         \\\"actionOrderInfo\\\": {\\n         \\\"orgId\\\": \\\"${orgId}\\\",\\n         \\\"projectId\\\": \\\"${projectId}\\\",\\n         \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\n         \\\"name\\\": \\\"云主机(ECS)\\\",\\n         \\\"type\\\": \\\"resource_change\\\",\\n         \\\"objectId\\\": \\\"ECS\\\",\\n         \\\"objectName\\\": \\\"云主机\\\",\\n         \\\"comment\\\": \\\"${comment}\\\",\\n         \\\"svcOrderDetails\\\": [{\\n            \\\"resourceId\\\": \\\"${id}\\\",\\n         \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\n         \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\n         \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\n         \\t\\\"displayParams-json\\\": \\\"${displayConfig}\\\",\\n         \\t\\\"excuteParams\\\": \\\"\\\",\\n         \\t\\\"quantity\\\": \\\"1\\\",\\n         \\t\\\"duration\\\": \\\"1\\\"\\n         }]\\n         }\\n    }\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\",\"refreshMode\":\"direct\",\"inquiry\":\"enable\",\"inquiryParams\":\"{\\r\\n    \\\"id\\\": \\\"${resourceId}\\\",\\r\\n    \\\"targetType\\\": \\\"ECS\\\",\\r\\n    \\\"targetSize\\\": 1,\\r\\n    \\\"targetSpec\\\": \\\"${changeVmType.value}\\\"\\r\\n}\",\"inquiryPath\":\"/v1/bss/billing/modify/inquiry/price\",\"inquiryType\":\"get\",\"operateSubmitBtn\":\"submitBtn\"},{\"id\":\"17302557311730\",\"operateName\":\"调整配置\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:change\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n   return (record.status === \\\"stopped\\\") && record.lockStatus!==\\\"locked\\\"&&record.systemDiskSize!==0\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode) && !record.resourceId\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/resize\",\"submitParams\":\"{\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\n \\\"action\\\": \\\"resize\\\",\\n \\\"data\\\": \\\"${orderConfig.data}\\\",\\n \\\"actionOrderInfo\\\": {\\n \\\"orgId\\\": \\\"${orgId}\\\",\\n \\\"projectId\\\": \\\"${projectId}\\\",\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\n \\\"name\\\": \\\"云主机(ECS)\\\",\\n \\\"type\\\": \\\"resource_change\\\",\\n \\\"objectId\\\": \\\"ECS\\\",\\n \\\"objectName\\\": \\\"云主机\\\",\\n \\\"comment\\\": \\\"${comment}\\\",\\n \\\"svcOrderDetails\\\": [{\\n \\\"resourceId\\\": \\\"${id}\\\",\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\n \\t\\\"displayParams-json\\\": \\\"${displayConfig}\\\",\\n \\t\\\"excuteParams\\\": \\\"\\\",\\n \\t\\\"quantity\\\": \\\"1\\\",\\n \\t\\\"duration\\\": \\\"1\\\"\\n }]\\n }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\"},{\"id\":\"**************\",\"operateName\":\"调整实例名称\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:rename\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return (record.status === \\\"stopped\\\"||record.status === \'active\') && \\n record.lockStatus !== \\\"locked\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"500px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"rename\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vmIds\\\": [\\r\\n \\\"${id}\\\"\\r\\n ],\\r\\n \\\"name\\\": \\\"${name}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"调整实例名称\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"refreshMode\":\"direct\"},{\"id\":\"16885597878180\",\"operateName\":\"通过VNC链接访问\",\"operateNameEn\":\"Remote VNC\",\"operateCode\":\"res:ECS:vncConsole\",\"operateGroup\":\"远程连接\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status === \\\"active\\\" && \\n record.lockStatus !== \\\"locked\\\"\\n}\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"buildIn\",\"modalWidth\":600,\"openConfirm\":true,\"buildInOperate\":\"routeConsole\",\"operateSubmit\":\"enable\",\"submitType\":\"get\",\"submitPath\":\"/resource/v1/vms\",\"submitParams\":\"\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16886341329166\",\"operateName\":\"重置密码\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:resetPassword\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    //record.status === \\\"stopped\\\"\\n return ![\'creating\',\'deleting\',\'createFailure\',\'deleteing\'].includes(record.status)\\n \\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\',\'HCS-MO-Tenant\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"500px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action/pwd\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"resetPassword\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vmId\\\": \\\"${id}\\\",\\r\\n \\\"newPassword\\\": \\\"${password}\\\",\\r\\n \\\"cloudSupport\\\": true,\\r\\n \\\"name\\\": \\\"${name}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"重置云主机密码\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"popupForm\":\"auto\",\"refreshMode\":\"direct\"},{\"id\":\"16885583347960\",\"operateName\":\"查看云主机详情\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:get\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1383107115130880,\"modalWidth\":\"80%\",\"openConfirm\":true,\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\"},{\"id\":\"16920049936591\",\"operateName\":\"创建云硬盘\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:vdCreate\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"toolbar\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1405190385197056,\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/ebs/res_apply\",\"submitParams\":\"{\\r\\n    \\\"orderType\\\": \\\"apply\\\",\\r\\n    \\\"projectId\\\": \\\"${orgId}\\\",\\r\\n    \\\"productName\\\": \\\"${nameDisk}\\\",\\r\\n    \\\"couponId\\\": \\\"${couponId}\\\",\\r\\n    \\\"productInfo\\\": [\\r\\n        {\\r\\n            \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n            \\\"serviceId\\\": null,\\r\\n            \\\"productCode\\\": \\\"EBS\\\",\\r\\n            \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n            \\\"periodUnit\\\": \\\"month\\\",\\r\\n            \\\"period\\\": \\\"${period}\\\",\\r\\n            \\\"amount\\\": 1,\\r\\n            \\\"data\\\": {\\r\\n                \\\"EBS\\\": {\\r\\n                    \\\"chargeItemCategory\\\": \\\"blockStorage\\\",\\r\\n                    \\\"productCode\\\": \\\"EBS\\\",\\r\\n                    \\\"category\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"spec\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"size\\\": \\\"${allocDiskSize}\\\"\\r\\n                }\\r\\n            },\\r\\n            \\\"productConfigDesc\\\": {\\r\\n                \\\"currentConfigDesc\\\": {\\r\\n                    \\\"type\\\": \\\"api\\\",\\r\\n                    \\\"productConfigDesc\\\": [\\r\\n                        { \\\"label\\\": \\\"计费类型\\\", \\\"value\\\": \\\"${displayValues.chargeType.valueText}\\\" },\\r\\n                        { \\\"label\\\": \\\"资源组织\\\", \\\"value\\\": \\\"${orgName}\\\" },\\r\\n                        { \\\"label\\\": \\\"区域Region\\\", \\\"value\\\": \\\"${cloudEnvName}\\\" },\\r\\n                        { \\\"label\\\": \\\"名称\\\", \\\"value\\\": \\\"${nameDisk}\\\" },\\r\\n                        { \\\"label\\\": \\\"释放模式\\\", \\\"value\\\": \\\"${displayValues.releaseMode.valueText}\\\" },\\r\\n                        { \\\"label\\\": \\\"可用区\\\", \\\"value\\\": \\\"${zoneName}\\\" },\\r\\n                        { \\\"label\\\": \\\"磁盘类型\\\", \\\"value\\\": \\\"${displayValues.ebsTypeModel.valueText}\\\" },\\r\\n                        { \\\"label\\\": \\\"存储大小(GB)\\\", \\\"value\\\": \\\"${allocDiskSize}\\\" },\\r\\n                        { \\\"label\\\": \\\"描述\\\", \\\"value\\\": \\\"${description}\\\" }\\r\\n                    ]\\r\\n                }\\r\\n            }\\r\\n        }\\r\\n    ],\\r\\n    \\\"resourceInfo\\\": {\\r\\n        \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n        \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n        \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n        \\\"action\\\": \\\"create\\\",\\r\\n        \\\"data\\\": {\\r\\n            \\\"name\\\": \\\"${nameDisk}\\\",\\r\\n            \\\"productId\\\": \\\"${productId}\\\",\\r\\n            \\\"snapshotModel\\\":{\\r\\n                \\\"id\\\": \\\"${snapshotModel}\\\"  \\r\\n            },\\r\\n            \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n            \\\"period\\\": \\\"${period}\\\",\\r\\n            \\\"size\\\": \\\"${allocDiskSize}\\\",\\r\\n            \\\"ebsTypeModel\\\": {\\r\\n                \\\"code\\\": \\\"${ebsTypeModel}\\\"\\r\\n            },\\r\\n            \\\"releaseMode\\\": \\\"${releaseMode}\\\",\\r\\n            \\\"size\\\": \\\"${allocDiskSize}\\\",\\r\\n            \\\"allocDiskSize\\\": \\\"${allocDiskSize}\\\",\\r\\n            \\\"diskType\\\": \\\"normal\\\",\\r\\n            \\\"description\\\": \\\"${description}\\\",\\r\\n            \\\"zone\\\": {\\r\\n                \\\"uuid\\\": null,\\r\\n                \\\"id\\\": \\\"${zoneId}\\\"\\r\\n            },\\r\\n            \\\"ecsModel\\\": {\\r\\n                \\\"id\\\": \\\"${id}\\\"\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"operateSubmitBtn\":\"createBtn\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"create\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/price\",\"inquiryParams\":\"{\\r\\n\\t\\\"couponId\\\": \\\"\\\",\\r\\n\\t\\\"productInfo\\\": [\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n\\t\\t\\t\\\"serviceId\\\": \\\"\\\",\\r\\n\\t\\t\\t\\\"productCode\\\": \\\"EBS\\\",\\r\\n\\t\\t\\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n\\t\\t\\t\\\"amount\\\": \\\"1\\\",\\r\\n\\t\\t\\t\\\"period\\\": \\\"${period}\\\",\\r\\n\\t\\t\\t\\\"data\\\": {\\r\\n\\t\\t\\t\\t\\\"EBS\\\": {\\r\\n                    \\\"chargeItemCategory\\\": \\\"blockStorage\\\",\\r\\n                    \\\"productCode\\\": \\\"EBS\\\",\\r\\n                    \\\"category\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"spec\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"size\\\": \\\"${allocDiskSize}\\\"\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t]\\r\\n}\"},{\"id\":\"16920049936592\",\"operateName\":\"云硬盘挂载\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:vdAttach\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"toolbar\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/disks/action/ecs\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n \\\"action\\\": \\\"attach\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"vdId\\\": \\\"${vdId}\\\",\\r\\n \\\"id\\\":\\\"${vdId}\\\",\\r\\n \\\"evsType\\\": \\\"${evsType}\\\",\\r\\n \\\"ecsModel\\\": {\\r\\n \\\"id\\\": \\\"${id}\\\"\\r\\n },\\r\\n \\\"releaseMode\\\": \\\"${releaseMode}\\\"\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云硬盘(EBS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"EBS\\\",\\r\\n \\\"objectName\\\": \\\"云硬盘\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"actionName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"挂载云硬盘\\\"\\r\\n \\t },\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"osType\\\": {\\r\\n \\t \\\"label\\\": \\\"操作系统\\\",\\r\\n \\t \\\"value\\\": \\\"${osType}\\\"\\r\\n \\t },\\r\\n \\t \\\"systemDiskInfo\\\": {\\r\\n \\t \\\"label\\\": \\\"系统盘信息\\\",\\r\\n \\t \\\"value\\\": \\\"${systemDiskInfo}\\\"\\r\\n \\t },\\r\\n \\t \\\"innerIp\\\": {\\r\\n \\t \\\"label\\\": \\\"内网IP\\\",\\r\\n \\t \\\"value\\\": \\\"${innerIp}\\\"\\r\\n \\t },\\r\\n \\t \\\"imageName\\\": {\\r\\n \\t \\\"label\\\": \\\"镜像\\\",\\r\\n \\t \\\"value\\\": \\\"${imageName}\\\"\\r\\n \\t },\\r\\n \\t \\\"zoneName\\\": {\\r\\n \\t \\\"label\\\": \\\"可用区\\\",\\r\\n \\t \\\"value\\\": \\\"${zoneName}\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\"},{\"id\":\"16939029834350\",\"operateName\":\"重启\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:restart\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"toolbarMore\",\"operateIcon\":\"\",\"enableScript\":\"(rows)=>{\\n let allowEnvCodeArr = [\'HuaweiCloud\',\'HCSO\',\'HCS-MO-Tenant\'] \\n let array = rows.filter(item=>item.status!==\\\"active\\\"||item.lockStatus===\'locked\'||!allowEnvCodeArr.includes(item.cloudEnvCode))\\n return array&&array.length>0?false:true\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${rows.0.cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"vmIds[rows]\\\": \\\"${item.id}\\\"\\r\\n },\\r\\n \\\"action\\\": \\\"restart\\\",\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${rows.0.orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${rows.0.projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${rows.0.ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\t\\\"chargeType\\\": \\\"${rows.0.chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json[rows]\\\": {\\r\\n \\t \\\"name\\\": \\\"${item.name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${item.chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${item.uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${item.cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"optObject\\\": {\\r\\n \\t \\\"label\\\": \\\"操作对象\\\",\\r\\n \\t \\\"value\\\": \\\"${item.name}\\\"\\r\\n \\t },\\r\\n \\t \\\"optName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"重启\\\"\\r\\n \\t },\\r\\n \\t \\\"status\\\": {\\r\\n \\t \\\"label\\\": \\\"状态\\\",\\r\\n \\t \\\"value\\\": \\\"重启中\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t},\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16939030752450\",\"operateName\":\"启动\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:start\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"toolbarMore\",\"operateIcon\":\"\",\"enableScript\":\"(rows)=>{\\n let allowEnvCodeArr = [\'HuaweiCloud\',\'HCSO\',\'HCS-MO-Tenant\'] \\n let array = rows.filter(item=>item.status!==\\\"stopped\\\"||item.lockStatus===\'locked\'||item.systemDiskSize===0||!allowEnvCodeArr.includes(item.cloudEnvCode))\\n return array&&array.length>0?false:true\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${rows.0.cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"vmIds[rows]\\\": \\\"${item.id}\\\"\\r\\n },\\r\\n \\\"action\\\": \\\"start\\\",\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${rows.0.orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${rows.0.projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${rows.0.ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\t\\\"chargeType\\\": \\\"${rows.0.chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json[rows]\\\": {\\r\\n \\t \\\"name\\\": \\\"${item.name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${item.chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${item.uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${item.cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"optObject\\\": {\\r\\n \\t \\\"label\\\": \\\"操作对象\\\",\\r\\n \\t \\\"value\\\": \\\"${item.name}\\\"\\r\\n \\t },\\r\\n \\t \\\"optName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"启动\\\"\\r\\n \\t },\\r\\n \\t \\\"status\\\": {\\r\\n \\t \\\"label\\\": \\\"状态\\\",\\r\\n \\t \\\"value\\\": \\\"启动中\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t},\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16939031292370\",\"operateName\":\"停止\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:stop\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"toolbarMore\",\"operateIcon\":\"\",\"enableScript\":\"(rows)=>{\\n let allowEnvCodeArr = [\'HuaweiCloud\',\'HCSO\',\'HCS-MO-Tenant\'] \\n let array = rows.filter(item=>item.status!==\\\"active\\\"||item.lockStatus===\'locked\'||!allowEnvCodeArr.includes(item.cloudEnvCode))\\n return array&&array.length>0?false:true\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/action\",\"submitParams\":\"{\\r\\n \\\"cloudEnvId\\\": \\\"${rows.0.cloudEnvId}\\\",\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"data\\\": {\\r\\n \\\"stopCharging\\\": true,\\r\\n \\\"vmIds[rows]\\\": \\\"${item.id}\\\"\\r\\n },\\r\\n \\\"action\\\": \\\"stop\\\",\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${rows.0.orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${rows.0.projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${rows.0.ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\t\\\"chargeType\\\": \\\"${rows.0.chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json[rows]\\\": {\\r\\n \\t \\\"name\\\": \\\"${item.name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${item.chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${item.uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${item.cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"optObject\\\": {\\r\\n \\t \\\"label\\\": \\\"操作对象\\\",\\r\\n \\t \\\"value\\\": \\\"${item.name}\\\"\\r\\n \\t },\\r\\n \\t \\\"optName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"停止\\\"\\r\\n \\t },\\r\\n \\t \\\"status\\\": {\\r\\n \\t \\\"label\\\": \\\"状态\\\",\\r\\n \\t \\\"value\\\": \\\"停止中\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t},\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16939032619630\",\"operateName\":\"删除\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:delete\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return !record.isCce\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let disableStatus = [\'stopped\']\\n return disableStatus.includes(record.status) && !record.resourceId\\n}\",\"operateType\":\"confirm\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/vms\",\"submitParams\":\"{\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"resTypeCode\\\": \\\"ECS\\\",\\n    \\\"action\\\": \\\"delete\\\",\\n    \\\"data\\\": {\\n        \\\"vmIds\\\": [\\\"${id}\\\"]\\n    }\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\"},{\"id\":\"16956317834520\",\"operateName\":\"关联凭证\",\"operateNameEn\":\"\",\"operateCode\":\"res:credentials:res:create\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status !== \\\"stopped\\\" && \\n record.lockStatus !== \\\"locked\\\" && \\n record.systemDiskSize!==0 &&\\n record.status !== \\\"createFailure\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\',\'HCS-MO-Tenant\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/credentials/rela_resource\",\"submitParams\":\"{\\n \\\"resTypeCode\\\": \\\"Credential\\\",\\n \\\"resourceId\\\": \\\"${id}\\\",\\n \\\"credentialId\\\": \\\"${credentialId}\\\",\\n \\\"configMethod\\\": \\\"${configMethod}\\\",\\n \\\"connectMethod\\\": \\\"${connectMethod}\\\",\\n \\\"username\\\": \\\"${username}\\\",\\n \\\"password\\\": \\\"${password}\\\",\\n \\\"credentialInject\\\": \\\"${credentialInject.0}\\\",\\n \\\"connectCredentialId\\\": \\\"${connectCredentialId}\\\",\\n \\\"action\\\": \\\"create\\\",\\n \\\"actionOrderInfo\\\": {\\n \\\"orgId\\\": \\\"${orgId}\\\",\\n \\\"projectId\\\": \\\"${projectId}\\\",\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\n \\\"name\\\": \\\"云主机(ECS)\\\",\\n \\\"type\\\": \\\"resource_operation\\\",\\n \\\"objectId\\\": \\\"ECS\\\",\\n \\\"objectName\\\": \\\"云主机\\\",\\n \\\"comment\\\": \\\"${comment}\\\",\\n \\\"svcOrderDetails\\\": [{\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\n \\t\\\"displayParams-json\\\": [\\n \\t {\\n \\t \\\"name\\\": \\\"${name}\\\",\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\n \\t \\\"resInfo\\\": {\\n \\t \\\"cloudEnvName\\\": {\\n \\t \\\"label\\\": \\\"云环境\\\",\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\n \\t },\\n \\t \\\"optObject\\\": {\\n \\t \\\"label\\\": \\\"操作对象\\\",\\n \\t \\\"value\\\": \\\"${name}\\\"\\n \\t },\\n \\t \\\"optName\\\": {\\n \\t \\\"label\\\": \\\"操作类型\\\",\\n \\t \\\"value\\\": \\\"关联凭证\\\"\\n \\t },\\n \\t \\\"status\\\": {\\n \\t \\\"label\\\": \\\"状态\\\",\\n \\t \\\"value\\\": \\\"正常\\\"\\n \\t }\\n \\t }\\n \\t }\\n \\t],\\n \\t\\\"excuteParams\\\": \\\"\\\",\\n \\t\\\"quantity\\\": \\\"1\\\",\\n \\t\\\"duration\\\": \\\"1\\\"\\n }]\\n }\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\"},{\"id\":\"16958074770160\",\"operateName\":\"执行脚本\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:execscript\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status !== \\\"createFailure\\\"\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n let noShowCode = [\'HCS-MO-Admin\',\'HCS-MO-Tenant\']\\n return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/actions/ECS\",\"submitParams\":\"{\\r\\n \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\\"action\\\": \\\"executeScript\\\",\\r\\n \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n \\\"inventory\\\": [\\r\\n {\\r\\n \\\"all\\\": [\\r\\n {\\r\\n \\\"resourceId\\\": \\\"${id}\\\",\\r\\n \\\"credentialId\\\":\\\"${credentialId}\\\",\\r\\n \\\"authUser\\\":\\\"${authUser}\\\",\\r\\n \\\"authPassword\\\":\\\"${authPassword}\\\"\\r\\n }\\r\\n ]\\r\\n }\\r\\n ],\\r\\n \\\"data\\\": {\\r\\n \\\"id\\\": \\\"${id}\\\",\\r\\n \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\\"scriptParam\\\": \\\"${formConfig}\\\",\\r\\n \\\"scriptInfo\\\": {\\r\\n \\\"scriptExecuteCategory\\\": \\\"${scriptExecuteCategory}\\\",\\r\\n \\\"scriptFileContent\\\": \\\"${scriptFileContent}\\\",\\r\\n \\\"scriptFileSuffix\\\": \\\"${scriptFileSuffix}\\\",\\r\\n \\\"scriptId\\\": \\\"${scriptId}\\\"\\r\\n }\\r\\n },\\r\\n \\\"actionOrderInfo\\\": {\\r\\n \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n \\\"name\\\": \\\"云主机(ECS)\\\",\\r\\n \\\"type\\\": \\\"resource_operation\\\",\\r\\n \\\"objectId\\\": \\\"ECS\\\",\\r\\n \\\"objectName\\\": \\\"云主机\\\",\\r\\n \\\"comment\\\": \\\"${comment}\\\",\\r\\n \\\"svcOrderDetails\\\": [{\\r\\n \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n \\t\\\"displayParams-json\\\": [\\r\\n \\t {\\r\\n \\t \\\"name\\\": \\\"${name}\\\",\\r\\n \\t \\\"resTypeCode\\\": \\\"ECS\\\",\\r\\n \\t \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n \\t \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n \\t \\\"resInfo\\\": {\\r\\n \\t \\\"cloudEnvName\\\": {\\r\\n \\t \\\"label\\\": \\\"云环境\\\",\\r\\n \\t \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n \\t },\\r\\n \\t \\\"optObject\\\": {\\r\\n \\t \\\"label\\\": \\\"操作对象\\\",\\r\\n \\t \\\"value\\\": \\\"${name}\\\"\\r\\n \\t },\\r\\n \\t \\\"optName\\\": {\\r\\n \\t \\\"label\\\": \\\"操作类型\\\",\\r\\n \\t \\\"value\\\": \\\"关联凭证\\\"\\r\\n \\t },\\r\\n \\t \\\"status\\\": {\\r\\n \\t \\\"label\\\": \\\"状态\\\",\\r\\n \\t \\\"value\\\": \\\"正常\\\"\\r\\n \\t }\\r\\n \\t }\\r\\n \\t }\\r\\n \\t],\\r\\n \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n \\t\\\"quantity\\\": \\\"1\\\",\\r\\n \\t\\\"duration\\\": \\\"1\\\"\\r\\n }]\\r\\n }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\"},{\"id\":\"17343318761330\",\"operateName\":\"重装操作系统\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:reinstallOs\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.status === \\\"stopped\\\"\\n}\",\"enableDesc\":\"只有已停止的主机才能重装操作系统\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1797964604178433,\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/vms/reinstallOs\",\"submitParams\":\"{\\r\\n    \\\"action\\\":\\\"reinstallOs\\\",\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"data\\\":{\\r\\n        \\\"vmId\\\":\\\"${id}\\\",\\r\\n        \\\"adminPass\\\":\\\"${password}\\\",\\r\\n        \\\"keyName\\\":\\\"${keypair}\\\"\\r\\n    },\\r\\n    \\\"resTypeCode\\\":\\\"ECS\\\"\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"modal\"},{\"id\":\"17132502745090\",\"operateName\":\"退订\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:unsubscribe\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n let disableStatus = [\'stopped\']\\n return disableStatus.includes(record.status) && !record.isCce\\n}\",\"enableDesc\":\"请先执行关机操作\",\"showScript\":\"(text,record)=>{\\nlet noShowCode = [\'HCS-MO-Admin\']\\n return !noShowCode.includes(record.cloudEnvCode) && record.resourceId\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1000\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/v1/bss/service/unsubscribe/${resourceId}?type=ECS\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\",\"popupForm\":1390267882078208,\"routePath\":\"/appmain/unsubscribe\"},{\"id\":\"16969188253231\",\"operateName\":\"续订\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:renew\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\nreturn record.endTime && record.chargeType === \\\"PrePaid\\\" && ![\'deleting\',\'creating\',\'createFailure\',\'deleted\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\nlet noShowCode = [\'HCS-MO-Admin\']\\nreturn !noShowCode.includes(record.cloudEnvCode) && record.resourceId\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1100px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/renew\",\"submitParams\":\"{\\r\\n\\t\\\"projectSId\\\": \\\"${orgId}\\\",\\r\\n    \\\"couponSid\\\":\\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": \\\"${productInfo}\\\"\\r\\n}\",\"refreshMode\":\"polling\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\",\"popupForm\":\"auto\",\"inquiry\":\"enable\",\"inquiryPath\":\"/v1/bss/billing/renew/inquiry/price/batch\",\"inquiryType\":\"post\",\"inquiryParams\":\"{\\n    \\\"couponId\\\": \\\"${couponId}\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\"},{\"id\":\"17010740704480\",\"operateName\":\"控制台\",\"operateNameEn\":\"\",\"operateCode\":\"sys:config:integration:idp:link\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"hide\",\"operateIcon\":\"icon-platform-monitoring-o\",\"operateTheme\":\"primary\",\"btnGroup\":\"console\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"route\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRouter\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\"},{\"id\":\"17029755531950\",\"operateName\":\"同步\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:scan\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"buildIn\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/sync/ECS\",\"submitParams\":\"{\\n \\\"cloudEnvId\\\": \\\"${envId}\\\",\\n \\\"action\\\": \\\"scan\\\",\\n \\\"data\\\": {}\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"buildInOperate\":\"sync\"},{\"id\":\"17410760092230\",\"operateName\":\"即时转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:chargeTypeToPostPaid:now\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1200px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPostPaid/change\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"pretopost\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPostPaid/price\",\"inquiryParams\":\"{\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\",\\n    \\\"imageID\\\": \\\"${ecsResourceId}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1426294833872896},{\"id\":\"17410760092241\",\"operateName\":\"到期转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:chargeTypeToPostPaid:end\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"route\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"routePath\":\"/appmain/renew/expiredconvertpostpaid/${record.resourceId}?renewalPolicy=nonAuto\"},{\"id\":\"17410807314400\",\"operateName\":\"转包年包月\",\"operateNameEn\":\"\",\"operateCode\":\"res:ECS:chargeTypeToPrePaid\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_prepaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PostPaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPrePaid/change\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"couponSid\\\": \\\"\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"posttopre\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPrePaid/price\",\"inquiryParams\":\"{\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1426360611971072}]', `operable` = 'enable', `created_by` = 'hy', `created_dt` = '2023-10-08 15:00:45', `updated_by` = 'hy', `updated_dt` = '2023-10-08 15:00:45', `version` = NULL WHERE `id` = 1389976326512640;
UPDATE `res_define_grid` SET `res_type_code` = 'DCS', `name` = '【勿删】分布式缓存服务-pf', `name_en` = NULL, `code` = 'dcs', `description` = NULL, `status` = 'enable', `base_info` = '{\"gridName\":\"缓存服务\",\"rowKey\":\"id\",\"rowSelect\":\"none\",\"operateWidth\":150,\"operateWidthEn\":200,\"operateFixed\":true,\"isStripe\":true}', `datasource_info` = '{\"sourceType\":\"api\",\"pageType\":\"server\",\"apiPath\":\"/resource/v1/dcs\",\"apiParams\":[],\"staticData\":\"[]\"}', `column_info` = '[{\"id\":\"16883739965620\",\"fieldName\":\"名称\",\"fieldKey\":\"name\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"link\",\"customScript\":\"\",\"relationOperate\":\"17120394075850\"},{\"id\":\"1711853151440\",\"fieldName\":\"区域region\",\"fieldKey\":\"cloudEnvName\",\"fieldEnName\":\"\",\"columnWidth\":200,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"env\",\"customScript\":\"\"},{\"id\":\"1730187920837\",\"fieldName\":\"计费类型\",\"fieldKey\":\"chargeType\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    const types={\\n        PrePaid: \'包年包月\',\\n        prePaid: \'包年包月\',\\n        PostPaid: \'按量计费\',\\n        postPaid: \'按量计费\',\\n        None: \'不计费\'\\n    }\\n    return types[record.chargeType] || record.chargeType\\n}\"},{\"id\":\"16883739965621\",\"fieldName\":\"状态\",\"fieldKey\":\"status\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"status\",\"customScript\":\"\"},{\"id\":\"16883739965624\",\"fieldName\":\"缓存类型\",\"fieldKey\":\"engine\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(txt,record)=>{\\n    return record.engine+record.engineVersion\\n}\"},{\"id\":\"1688541825878\",\"fieldName\":\"实例类型\",\"fieldKey\":\"cacheMode\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(txt,record)=>{\\n    const obj={\\n        ha:\'主备\',\\n        single:\'单机\',\\n        proxy:\'集群\',\\n        cluster:\'Cluster集群\'\\n    }\\n    return obj[record.cacheMode]|| \'--\'\\n}\"},{\"id\":\"1688541788797\",\"fieldName\":\"CPU\",\"fieldKey\":\"cpuType\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(txt,record)=>{\\n    // 组装参数，变配用\\n    record.tableAttrValues = [record.cpuType, record.cacheMode, record.engineVersion].join()\\n    let types = {\\n        x86_64: \'x86\'\\n    }\\n    return types[txt] || txt\\n}\"},{\"id\":\"1688541766284\",\"fieldName\":\"规格（GB）\",\"fieldKey\":\"capacity\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1715843821284\",\"fieldName\":\"所属组织\",\"fieldKey\":\"orgName\",\"fieldEnName\":\"\",\"columnWidth\":120,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1688541664089\",\"fieldName\":\"连接地址\",\"fieldKey\":\"privateIp\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(txt,record)=>{\\n    if(!record.privateIp)return \'--\'\\n    return `${record.privateIp}:${record.port}`\\n}\"},{\"id\":\"1730187921608\",\"fieldName\":\"开始时间\",\"fieldKey\":\"startTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1730188005210\",\"fieldName\":\"结束时间\",\"fieldKey\":\"endTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"}]', `search_info` = '[{\"id\":\"1688378321299\",\"searchName\":\"名称\",\"searchField\":\"nameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"},{\"id\":\"1688378397698\",\"searchName\":\"状态\",\"searchField\":\"status\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"static\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"[\\n {\\n \\\"name\\\": \\\"正常\\\",\\n \\\"value\\\": \\\"active\\\"\\n },\\n {\\n \\\"name\\\": \\\"运行中\\\",\\n \\\"value\\\": \\\"RUNNING\\\"\\n },\\n {\\n    \\\"name\\\":\\\"创建中\\\",\\n    \\\"value\\\":\\\"CREATING\\\"\\n },\\n  {\\n    \\\"name\\\":\\\"创建失败\\\",\\n    \\\"value\\\":\\\"createFailure\\\"\\n }\\n ]\"},{\"id\":\"1688378448930\",\"searchName\":\"缓存类型\",\"searchField\":\"engine\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"},{\"id\":\"1688378298495\",\"searchName\":\"规格\",\"searchField\":\"capacity\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"static\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"[\\n {\\n \\\"name\\\": \\\"0.125GB\\\",\\n \\\"value\\\": \\\"0.125\\\"\\n },\\n  {\\n \\\"name\\\": \\\"0.25GB\\\",\\n \\\"value\\\": \\\"0.25\\\"\\n },\\n  {\\n \\\"name\\\": \\\"0.5GB\\\",\\n \\\"value\\\": \\\"0.5\\\"\\n },\\n  {\\n \\\"name\\\": \\\"1GB\\\",\\n \\\"value\\\": \\\"1\\\"\\n },\\n  {\\n \\\"name\\\": \\\"2GB\\\",\\n \\\"value\\\": \\\"2\\\"\\n },\\n  {\\n \\\"name\\\": \\\"4GB\\\",\\n \\\"value\\\": \\\"4\\\"\\n },\\n  {\\n \\\"name\\\": \\\"8GB\\\",\\n \\\"value\\\": \\\"8\\\"\\n },\\n  {\\n \\\"name\\\": \\\"16GB\\\",\\n \\\"value\\\": \\\"16\\\"\\n },\\n  {\\n \\\"name\\\": \\\"24GB\\\",\\n \\\"value\\\": \\\"24\\\"\\n },\\n  {\\n \\\"name\\\": \\\"32GB\\\",\\n \\\"value\\\": \\\"32\\\"\\n },\\n {\\n \\\"name\\\": \\\"48GB\\\",\\n \\\"value\\\": \\\"48\\\"\\n },\\n  {\\n \\\"name\\\": \\\"64GB\\\",\\n \\\"value\\\": \\\"64\\\"\\n },\\n  {\\n \\\"name\\\": \\\"96GB\\\",\\n \\\"value\\\": \\\"96\\\"\\n },\\n  {\\n \\\"name\\\": \\\"128GB\\\",\\n \\\"value\\\": \\\"128\\\"\\n },\\n {\\n \\\"name\\\": \\\"192GB\\\",\\n \\\"value\\\": \\\"192\\\"\\n },\\n  {\\n \\\"name\\\": \\\"256GB\\\",\\n \\\"value\\\": \\\"256\\\"\\n },\\n   {\\n \\\"name\\\": \\\"384GB\\\",\\n \\\"value\\\": \\\"384\\\"\\n },\\n  {\\n \\\"name\\\": \\\"512GB\\\",\\n \\\"value\\\": \\\"512\\\"\\n },\\n   {\\n \\\"name\\\": \\\"768GB\\\",\\n \\\"value\\\": \\\"768\\\"\\n },\\n  {\\n \\\"name\\\": \\\"1024GB\\\",\\n \\\"value\\\": \\\"1024\\\"\\n }\\n]\"},{\"id\":\"1688378290492\",\"searchName\":\"连接地址\",\"searchField\":\"privateIp\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"}]', `action_info` = '[{\"id\":\"16883777684580\",\"operateName\":\"创建\",\"operateNameEn\":\"create\",\"operateCode\":\"res:actionForm:create\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-plus-o-1\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"route\",\"modalWidth\":\"900\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"submitAfter\":\"closeRouter\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\",\"operateTheme\":\"primary\",\"popupForm\":1396564547297280,\"refreshMode\":\"direct\",\"operateSubmitBtn\":\"createBtn\",\"btnGroup\":\"env\",\"selfId\":1894649305169924,\"routePath\":\"/appcmp/console/storage/dcs/apply/${selfId}\"},{\"id\":\"17062363426330\",\"operateName\":\"编辑\",\"operateNameEn\":\"\",\"operateCode\":\"res:actionForm:update\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/dcs\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DCS\\\",\\r\\n    \\\"action\\\": \\\"update\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"name\\\": \\\"${name}\\\",\\r\\n        \\\"id\\\":\\\"${id}\\\"\\r\\n        \\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\",\"popupForm\":1797958346014720},{\"id\":\"16883777684583\",\"operateName\":\"重启\",\"operateNameEn\":\"restart\",\"operateCode\":\"res:dcs:action\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"row\",\"operateIcon\":\"icon-refresh-o\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\'].includes(record.status)\\n}\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"900px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/dcs/status\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DCS\\\",\\r\\n    \\\"action\\\": \\\"restart\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"action\\\": \\\"restart\\\",\\r\\n        \\\"id\\\":\\\"${id}\\\",\\r\\n        \\\"instances\\\": [\\r\\n            \\\"${uuid}\\\"\\r\\n        ]\\r\\n        \\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"popupForm\":1797964604178432},{\"id\":\"17054738910510\",\"operateName\":\"变更规格\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:change\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"rowMore\",\"operateIcon\":\"icon-plus-o-1\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\'].includes(record.status) && ![\'None\',\'\',null].includes(record.chargeType)\\n}\\n\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"1200px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/dcs/resize_resource\",\"submitParams\":\"{\\n    \\\"projectId\\\": \\\"${orgId}\\\",\\n    \\\"productInfo\\\": {\\n        \\\"id\\\": \\\"${resourceId}\\\",\\n        \\\"resourceType\\\": \\\"DCS\\\",\\n        \\\"size\\\": \\\"0\\\",\\n        \\\"spec\\\": \\\"${table.0.productSpecCode}\\\"\\n    },\\n    \\\"resourceInfo\\\": {\\n        \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n        \\\"orgId\\\": \\\"${orgId}\\\",\\n        \\\"resTypeCode\\\": \\\"DCS\\\",\\n        \\\"action\\\": \\\"upgrade\\\",\\n        \\\"data\\\": {\\n            \\\"id\\\":\\\"${id}\\\",\\n            \\\"newCapacity\\\": \\\"${table.0.capacity}\\\",\\n            \\\"newCapacityString\\\": \\\"${table.0.capacity}\\\",\\n            \\\"specCode\\\": \\\"${table.0.productSpecCode}\\\",\\n            \\\"specId\\\":\\\"${table.0.id}\\\"\\n        }\\n    }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"operateTheme\":\"primary\",\"popupType\":\"drawer\",\"popupForm\":1702351942230016,\"inquiry\":\"enable\",\"inquiryOperationType\":\"change\",\"inquiryType\":\"get\",\"inquiryPath\":\"/v1/bss/billing/modify/inquiry/price\",\"inquiryParams\":\"{\\r\\n    \\\"id\\\": \\\"${resourceId}\\\",\\r\\n    \\\"targetType\\\": \\\"DCS\\\",\\r\\n    \\\"targetSize\\\": 0,\\r\\n    \\\"targetSpec\\\": \\\"${table.0.productSpecCode}\\\"\\r\\n}\"},{\"id\":\"17062362774680\",\"operateName\":\"重置密码\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:resetPassword\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\",\"popupForm\":1397959697817600},{\"id\":\"17062362774681\",\"operateName\":\"修改密码\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:editPassword\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return record.noPasswordAccess !==\'true\' && [\'active\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/dcs/password\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DCS\\\",\\r\\n    \\\"action\\\": \\\"resetPassword\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\":\\\"${id}\\\",\\r\\n        \\\"oldPassword\\\": \\\"${oldPassword}\\\",\\r\\n        \\\"newPassword\\\": \\\"${newPassword}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\",\"popupForm\":1797959733583872},{\"id\":\"17301918446081\",\"operateName\":\"续订\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:renew\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\',\'expired\'].includes(record.status) && ![\'None\',\'\',null].includes(record.chargeType)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/renew/dcs\",\"submitParams\":\"{\\r\\n\\t\\\"projectId\\\":  \\\"${orgId}\\\",\\r\\n    \\\"couponId\\\":\\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": [\\r\\n\\t\\t{\\r\\n            \\\"id\\\":\\\"${resourceId}\\\",\\r\\n            \\\"name\\\":\\\"${name}\\\",\\r\\n\\t\\t\\t\\\"serviceId\\\": \\\"${serviceId}\\\",\\r\\n\\t\\t\\t\\\"productCode\\\": \\\"DCS\\\",\\r\\n\\t\\t\\t\\\"periodUnit\\\": \\\"month\\\",\\r\\n\\t\\t\\t\\\"period\\\": \\\"${period}\\\", \\r\\n\\t\\t\\t\\\"quantity\\\": \\\"${dataCopyNum}\\\"\\r\\n\\t\\t}\\r\\n\\t]\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"renew\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/renew/inquiry/price\",\"inquiryParams\":\"{\\n    \\\"id\\\":  \\\"${resourceId}\\\",\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"serviceId\\\":  \\\"${serviceId}\\\",\\n    \\\"productCode\\\": \\\"${resTypeCode}\\\", \\n    \\\"couponId\\\": \\\"${couponId}\\\",\\n    \\\"unifyDate\\\": \\\"\\\", \\n    \\\"quantity\\\": \\\"${dataCopyNum}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1418004000014336},{\"id\":\"17301918446070\",\"operateName\":\"退订\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:unsubscribe\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\',\'expired\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    return !!record.resourceId\\n}\",\"operateType\":\"popup\",\"popupForm\":1418005150007296,\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/v1/bss/service/unsubscribe/res/dcs/${resourceId}?type=DCS\",\"submitParams\":\"{\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n    \\\"orgId\\\": \\\"${orgId}\\\",\\n    \\\"resTypeCode\\\": \\\"DCS\\\",\\n    \\\"action\\\": \\\"delete\\\",\\n    \\\"data\\\": {\\n        \\\"id\\\": \\\"${id}\\\",\\n        \\\"isFormat\\\": \\\"false\\\",\\n        \\\"selfServiceEqual\\\":\\\"false\\\"\\n    }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17062360668110\",\"operateName\":\"删除\",\"operateNameEn\":\"\",\"operateCode\":\"res:actionForm:delete\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return ![\'CREATING\',\'creating\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    return !record.resourceId\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/dcs\",\"submitParams\":\"{\\n  \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n  \\\"resTypeCode\\\": \\\"DCS\\\",\\n  \\\"action\\\": \\\"delete\\\",\\n  \\\"data\\\": {\\n    \\\"id\\\": \\\"${id}\\\"\\n  }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\"},{\"id\":\"17064992964110\",\"operateName\":\"数据清空\",\"operateNameEn\":\"\",\"operateCode\":\"res:actionForm:deleteData\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'active\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1797961895886848,\"modalWidth\":\"900px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/dcs/flush\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"DCS\\\",\\r\\n    \\\"action\\\": \\\"flush\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"action\\\": \\\"flush\\\",\\r\\n        \\\"id\\\":\\\"${id}\\\",\\r\\n        \\\"instances\\\": [\\r\\n            \\\"${uuid}\\\"\\r\\n        ]\\r\\n        \\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\"},{\"id\":\"17120394075850\",\"operateName\":\"查看分布式缓存详情\",\"operateNameEn\":\"\",\"operateCode\":\"res:DCS:get\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1402502597279744,\"modalWidth\":\"80%\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"drawer\"}]', `operable` = 'enable', `created_by` = 'admin', `created_dt` = '2024-01-09 16:41:25', `updated_by` = 'admin', `updated_dt` = '2024-01-09 16:41:25', `version` = NULL WHERE `id` = 1396563709976576;

UPDATE `res_define_action` SET `res_type_code` = 'ECS', `auth_id` = 'res:ECS:create', `name` = '创建云主机', `name_en` = 'Create', `type` = 'create', `action_code` = 'create', `process_type` = 'async', `implement_type` = 'api', `api_param` = '{\r\n    \"orderType\": \"apply\",\r\n    \"projectId\": \"${orgId}\",\r\n    \"productName\": \"${name}\",\r\n    \"couponId\":\"${couponId}\",\r\n	\"productInfo\": [\r\n		{\r\n			\"cloudEnvId\": \"${envId}\",\r\n			\"serviceId\": \"${serviceId}\",\r\n			\"productCode\": \"ECS\",\r\n			\"chargeType\": \"${chargeType}\",\r\n			\"periodUnit\": \"month\",\r\n			\"period\": \"${period}\",\r\n			\"amount\": \"${quantity}\",\r\n			\"data\": {\r\n				\"ECS\": {\r\n					\"chargeItemCategory\": \"compute\",\r\n					\"productCode\": \"ECS\",\r\n					\"category\": \"${vmTypeId.0.name}\",\r\n					\"spec\": \"${vmTypeId.0.name}\"\r\n				},\r\n				\"sysDisk\": \"${systemDisk}\",\r\n				\"dataDisk\": \"${dataDisk}\",\r\n				\"IMS\": {\r\n					\"imageID\": \"${imageId}\"\r\n				}\r\n			}\r\n		}\r\n	],\r\n	\"resourceInfo\": {\r\n        \"cloudEnvId\": \"${envId}\",\r\n        \"orgId\": \"${orgId}\",\r\n        \"resTypeCode\": \"ECS\",\r\n        \"action\": \"create\",\r\n        \"data\": {\r\n            \"zoneId\": \"${zoneId}\",\r\n            \"owner\": \"${owner}\",\r\n            \"projectUuid\": \"${projectId}\",\r\n            \"chargeType\": \"${chargeType}\",\r\n            \"imageId\": \"${imageId}\",\r\n            \"vmTypeId\": \"${vmTypeId.0.id}\",\r\n            \"name\": \"${name}\",\r\n            \"suffix\": \"${suffix}\",\r\n            \"loginType\": \"${loginType}\",\r\n            \"managementAccount\": \"${loginuser}\",\r\n            \"managementPassword\": \"${password}\",\r\n            \"diskConfigUseImage\": false,\r\n            \"managementKeypairId\": \"${keypair}\",\r\n            \"systemDisk\": {\r\n                \"size\": \"${systemDisk.volumeSize}\",\r\n                \"vdType\": {\r\n                    \"id\": \"${systemDisk.volumeType}\"\r\n                }\r\n            },\r\n            \"dataDisks\": \"${dataDisk}\",\r\n            \"securityGroups\": [{\r\n               \"id\": \"${sgId}\"\r\n            }],\r\n            \"ports\": [\r\n                {\r\n                    \"vpc\": {\r\n                      \"id\": \"${network.vpcId}\"\r\n                    },\r\n                    \"subnet\": {\r\n                      \"id\": \"${network.subnetId}\"\r\n                    },\r\n                    \"allocateIpv6\": false\r\n                }\r\n            ],\r\n            \"elasticIp\": {\r\n                 \"allocate\": \"${allocate}\",\r\n                 \"allocateType\": \"${allocateType}\",\r\n                 \"id\": \"${eipId}\"\r\n            },\r\n            \"period\": \"${period}\",\r\n            \"quantity\": \"${quantity}\",\r\n            \"extra\": {\r\n                \"productId\":\"${productId}\",\r\n                \"singleStorage\":\"${singleStorage}\"\r\n            },\r\n            \"serverGroupId\": \"${antiAffinityId}\"\r\n        }\r\n    }\r\n}', `api_url` = '/v1/bss/service/res_apply', `api_type` = 'post', `script_file_path` = NULL, `execute_type` = NULL, `action_form_template_id` = 1381399903313920, `in_operation_action` = 'creating', `operation_success_action` = 'active', `operation_failure_action` = 'restarting', `group_param` = NULL, `warehousing` = 0, `sort_rank` = 1, `record_task_log_flag` = 1, `action_intercept_flag` = 1, `description` = '创建', `created_by` = 'zq03', `created_dt` = '2023-06-06 11:39:16', `updated_by` = 'administrator', `updated_dt` = '2023-11-27 15:56:36', `version` = 1 WHERE `id` = 1381189841362944;
UPDATE `res_define_action` SET `res_type_code` = 'NAT', `auth_id` = 'res:NAT:create', `name` = '创建网关服务', `name_en` = 'Create', `type` = 'create', `action_code` = 'create', `process_type` = 'async', `implement_type` = 'api', `api_param` = '{\r\n    \"orderType\": \"apply\",\r\n    \"projectId\": \"${orgId}\",\r\n    \"productName\": \"${name}\",\r\n    \"couponId\":\"${couponId}\",\r\n	\"productInfo\": [\r\n		{\r\n			\"cloudEnvId\": \"${envId}\",\r\n			\"serviceId\": \"${serviceId}\",\r\n			\"productCode\": \"NAT\",\r\n			\"chargeType\": \"${chargeType}\",\r\n			\"periodUnit\": \"month\",\r\n			\"period\": \"1\",\r\n			\"amount\": \"1\",\r\n			\"data\": {\r\n				\"NAT\": {\r\n					\"chargeItemCategory\": \"network\",\r\n					\"productCode\": \"NAT\",\r\n					\"category\": \"${vmTypeId.0.name}\",\r\n					\"natType\": \"${vmTypeId.0.name}\"\r\n				}\r\n			}\r\n		}\r\n	],\r\n	\"resourceInfo\": {\r\n        \"cloudEnvId\": \"${envId}\",\r\n        \"orgId\": \"${orgId}\",\r\n        \"resTypeCode\": \"NAT\",\r\n        \"action\": \"create\",\r\n        \"data\": {\r\n            \"description\": \"${description}\",\r\n            \"name\": \"${name}\",\r\n            \"natTypeId\": \"${vmTypeId.0.id}\",\r\n            \"chargeType\": \"${chargeType}\",\r\n            \"port\":{\r\n                    \"vpc\": {\r\n                      \"id\": \"${network.vpcId}\"\r\n                    },\r\n                    \"subnet\": {\r\n                      \"id\": \"${network.subnetId}\"\r\n                    }\r\n                }\r\n        }\r\n    }\r\n}', `api_url` = '/v1/bss/service/res_apply', `api_type` = 'post', `script_file_path` = NULL, `execute_type` = NULL, `action_form_template_id` = 1396702389575680, `in_operation_action` = 'creating', `operation_success_action` = 'active', `operation_failure_action` = 'restarting', `group_param` = NULL, `warehousing` = 0, `sort_rank` = 1, `record_task_log_flag` = 1, `action_intercept_flag` = 1, `description` = '创建', `created_by` = 'zq03', `created_dt` = '2023-06-06 11:39:16', `updated_by` = 'admin', `updated_dt` = '2024-01-11 17:56:36', `version` = 1 WHERE `id` = 1396052093042712;
UPDATE `res_define_action` SET `res_type_code` = 'AS-GROUP', `auth_id` = 'res:AS:create', `name` = '创建', `name_en` = 'Create', `type` = 'create', `action_code` = 'create', `process_type` = 'sync', `implement_type` = 'api', `api_param` = '{\r\n    \"orderType\": \"apply\",\r\n    \"projectId\": \"${orgId}\",\r\n    \"productName\": \"${name}\",\r\n    \"productInfo\": [\r\n        {\r\n            \"productCode\": \"AS\",\r\n            \"cloudEnvId\": \"${envId}\",\r\n            \"serviceId\": \"${serviceId}\",\r\n            \"chargeType\": \"None\",\r\n            \"amount\": 1,\r\n            \"priceUnit\": \"month\",\r\n            \"period\": 1,\r\n            \"data\": {}\r\n        }\r\n    ],\r\n    \"resourceInfo\": {\r\n        \"cloudEnvId\": \"${envId}\",\r\n        \"orgId\": \"${orgId}\",\r\n        \"resTypeCode\": \"AS-GROUP\",\r\n        \"action\": \"create\",\r\n        \"data\": {\r\n            \"zones\": \"${zoneId}\",\r\n            \"scalingGroupName\": \"${name}\",\r\n            \"maxInstanceNumber\": \"${maxInstanceNumber}\",\r\n            \"desireInstanceNumber\": \"${desireInstanceNumber}\",\r\n            \"minInstanceNumber\": \"${minInstanceNumber}\",\r\n            \"scalingConfigurationId\": \"${sspzID.0.uuid}\",\r\n            \"vpcId\": \"${network.vpcId}\", \r\n            \"subnetId\": \"${network.subnetId}\",\r\n            \"instanceTerminatePolicy\": \"${instanceTerminatePolicy}\",\r\n            \"deletePublicip\": \"${deletePublicip}\",\r\n            \"healthPeriodicAuditMethod\": \"${healthPeriodicAuditMethod}\",\r\n            \"healthPeriodicAuditTime\": \"${healthPeriodicAuditTime}\",\r\n            \"deleteVolume\": \"${deleteVolume}\",\r\n            \"securityGroups\": [\"${sgId}\"],\r\n            \"healthPeriodicAuditGracePeriod\": \"${healthPeriodicAuditGracePeriod}\"\r\n        }\r\n    }\r\n}', `api_url` = '/v1/bss/service/asgroup/res_apply', `api_type` = 'post', `script_file_path` = NULL, `execute_type` = 'local', `action_form_template_id` = 1391536032194560, `in_operation_action` = NULL, `operation_success_action` = NULL, `operation_failure_action` = NULL, `group_param` = NULL, `warehousing` = 0, `sort_rank` = 1, `record_task_log_flag` = 1, `action_intercept_flag` = 0, `description` = '', `created_by` = 'admin', `created_dt` = '2025-02-07 14:07:17', `updated_by` = 'admin', `updated_dt` = '2025-02-07 14:07:17', `version` = 1 WHERE `id` = 1491541934678016;

