CALL setcolumn ('res_lb','elb_virsubnet_ids','add',' text COMMENT \'elb后端子网id列表,最大64个\'');

CREATE TABLE IF NOT EXISTS `res_lb_virsubnet_ips` (
                                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `lb_id` bigint DEFAULT NULL COMMENT '负载均衡id',
                                                      `type` varchar(64) DEFAULT NULL COMMENT 'l4：4层，l7：7层',
                                                      `ip` varchar(32) DEFAULT NULL COMMENT 'ip',
                                                      `subnet_id` bigint DEFAULT NULL COMMENT '子网id',
                                                      `version` bigint NOT NULL DEFAULT '1' COMMENT '版本号',
                                                      `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                                      `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                                      `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
                                                      PRIMARY KEY (`id`)
) COMMENT='混合负载均衡后端子网';

CREATE TABLE IF NOT EXISTS `res_lb_addresspool` (
                                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `lb_id` bigint DEFAULT NULL COMMENT '负载均衡id',
                                                    `uuid` varchar(128) DEFAULT NULL COMMENT 'uuid',
                                                    `type` varchar(64) DEFAULT NULL COMMENT '固定iptarget',
                                                    `name` varchar(128) DEFAULT NULL COMMENT 'name',
                                                    `addresses` text DEFAULT NULL COMMENT 'addresses',
                                                    `tenant_id` varchar(128) DEFAULT NULL COMMENT '项目ID',
                                                    `description` varchar(128) DEFAULT NULL COMMENT 'description',
                                                    `version` bigint NOT NULL DEFAULT '1' COMMENT '版本号',
                                                    `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                                    `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
                                                    `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                                    `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
                                                    PRIMARY KEY (`id`)
) COMMENT='混合负载均衡地址池';


DELETE FROM sys_interface_url WHERE id = 19981415345354510 AND url_method = 'POST' AND module_url = 'api/resource/v1/slb/addresspool';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354510, '创建地址池', 'resource', 'internal', 'POST', 'api/resource/v1/slb/addresspool', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354510) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354510,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354510) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354510,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);



DELETE FROM sys_interface_url WHERE id = 19981415345354511 AND url_method = 'PUT' AND module_url = 'api/resource/v1/slb/addresspool';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354511, '更新地址池', 'resource', 'internal', 'PUT', 'api/resource/v1/slb/addresspool', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354511) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354511,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354511) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354511,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);





DELETE FROM sys_interface_url WHERE id = 19981415345354512 AND url_method = 'GET' AND module_url = 'api/resource/v1/slb/subnets/page';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354512, '查询后端子网', 'resource', 'internal', 'GET', 'api/resource/v1/slb/subnets/page', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354512) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354512,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354512) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354512,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);


DELETE FROM sys_interface_url WHERE id = 19981415345354513 AND url_method = 'DELETE' AND module_url = 'api/resource/v1/slb/addresspool';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354513, '删除地址池', 'resource', 'internal', 'DELETE', 'api/resource/v1/slb/addresspool', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354513) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354513,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354513) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354513,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);


DELETE FROM sys_interface_url WHERE id = 19981415345354514 AND url_method = 'POST' AND module_url = 'api/resource/v1/slb/address/allocations';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354514, '添加地址分配', 'resource', 'internal', 'POST', 'api/resource/v1/slb/address/allocations', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354514) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354514,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354514) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354514,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);



DELETE FROM sys_interface_url WHERE id = 19981415345354515 AND url_method = 'GET' AND module_url = 'api/resource/v1/slb/address/allocations';
INSERT INTO `sys_interface_url`(`id`, `module_url_name`, `services_code`, `module_url_type`, `url_method`, `module_url`, `sensitive_flag`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`) VALUES (19981415345354515, '查询地址分配', 'resource', 'internal', 'GET', 'api/resource/v1/slb/address/allocations', 0, 'admin', '2025-02-07 14:07:21', 'admin', '2025-02-07 14:07:21', '1');

delete from sys_action_url_rela where interface_url_id in (19981415345354515) and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354515,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id in (19981415345354515) and action_auth_key = 'res:SLB:get';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES  (19981415345354515,'res:SLB:get','admin',current_timestamp,'admin',current_timestamp,1);