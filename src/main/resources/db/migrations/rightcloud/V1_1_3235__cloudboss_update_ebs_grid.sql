UPDATE `res_define_grid` SET `res_type_code` = 'EBS', `name` = '【勿删】HCSO云硬盘-pf', `name_en` = NULL, `code` = 'HCSO EBS', `description` = NULL, `status` = 'enable', `base_info` = '{\"gridName\":\"vblist\",\"rowKey\":\"id\",\"rowSelect\":\"none\",\"operateWidth\":170,\"operateWidthEn\":200,\"operateFixed\":true,\"isStripe\":true,\"resizable\":true}', `datasource_info` = '{\"sourceType\":\"api\",\"pageType\":\"server\",\"apiPath\":\"/resource/v1/disks\",\"apiParams\":[],\"staticData\":\"[]\"}', `column_info` = '[{\"id\":\"1686979936019\",\"fieldName\":\"名称\",\"fieldKey\":\"name\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":true,\"isSorted\":true,\"sortAttr\":\"\",\"renderMode\":\"link\",\"customScript\":\"\",\"fixPostion\":\"left\",\"relationOperate\":\"16885385698651\",\"resizable\":false},{\"id\":\"1686979947407\",\"fieldName\":\"资源ID\",\"fieldKey\":\"uuid\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":false,\"isFixed\":false,\"isSorted\":true,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\",\"resizable\":false},{\"id\":\"1702987127094\",\"fieldName\":\"区域Region\",\"fieldKey\":\"cloudEnvName\",\"fieldEnName\":\"\",\"columnWidth\":270,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"env\",\"customScript\":\"\",\"resizable\":false},{\"id\":\"1729821297450\",\"fieldName\":\"计费类型\",\"fieldKey\":\"chargeType\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    const types={\\n        PrePaid: \'包年包月\',\\n        prePaid: \'包年包月\',\\n        PostPaid: \'按量计费\',\\n        postPaid: \'按量计费\',\\n        None: \'不计费\'\\n    }\\n    return types[record.chargeType] || record.chargeType\\n}\"},{\"id\":\"1710224816564\",\"fieldName\":\"资源组织\",\"fieldKey\":\"orgName\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\",\"resizable\":false},{\"id\":\"1686992701381\",\"fieldName\":\"可用区\",\"fieldKey\":\"zoneName\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text ? text : \'--\'\\n}\",\"resizable\":false},{\"id\":\"1686980076880\",\"fieldName\":\"存储类型\",\"fieldKey\":\"volumeTypeName\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":true,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text ? text : \'--\'\\n}\",\"resizable\":false},{\"id\":\"1686980063786\",\"fieldName\":\"容量（GB）\",\"fieldKey\":\"size\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":true,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\",\"resizable\":false},{\"id\":\"16889582465020\",\"fieldName\":\"磁盘类型\",\"fieldKey\":\"vdType\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":true,\"sortAttr\":\"storage_purpose\",\"renderMode\":\"default\",\"customScript\":\"(text,record)=>{\\n    let str = text === \'withInstance\' ? \'随实例释放\' : \'不随实例释放\'\\n    return str\\n}\",\"resizable\":false},{\"id\":\"16889584252171\",\"fieldName\":\"磁盘模式\",\"fieldKey\":\"diskMode\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":false,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text ? text : \'--\'\\n}\",\"resizable\":false},{\"id\":\"1719997981091\",\"fieldName\":\"挂载云服务器\",\"fieldKey\":\"attachments\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text.length ? text[0].resVmName : \'--\'\\n}\",\"resizable\":false},{\"id\":\"1686979998859\",\"fieldName\":\"状态\",\"fieldKey\":\"status\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"status\",\"customScript\":\"\",\"resizable\":false},{\"id\":\"1686979986789\",\"fieldName\":\"挂载到\",\"fieldKey\":\"mountPoint\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text ? text : \'--\'\\n}\",\"resizable\":false},{\"id\":\"1729821258375\",\"fieldName\":\"开始时间\",\"fieldKey\":\"startTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1729821257759\",\"fieldName\":\"结束时间\",\"fieldKey\":\"endTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"\"},{\"id\":\"16889584252172\",\"fieldName\":\"描述\",\"fieldKey\":\"description\",\"fieldEnName\":\"\",\"columnWidth\":200,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text ? text : \'--\'\\n}\",\"resizable\":false}]', `search_info` = '[{\"id\":\"1688367865889\",\"searchName\":\"名称\",\"searchField\":\"nameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"},{\"id\":\"1724638454636\",\"searchName\":\"区域Region\",\"searchField\":\"cloudEnvId\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"api\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[{\"name\":\"name\",\"type\":\"fixed\",\"value\":\"cloudEnvName\"},{\"name\":\"value\",\"type\":\"fixed\",\"value\":\"id\"}],\"staticData\":\"\",\"defaultValue\":\"\",\"apiPath\":\"/resource/v1/cloud/envs/current_list\"},{\"id\":\"1688367852716\",\"searchName\":\"状态\",\"searchField\":\"status\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"static\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"[\\n    {\\n        \\\"name\\\": \\\"正常\\\",\\n        \\\"value\\\": \\\"active\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"创建中\\\",\\n        \\\"value\\\": \\\"creating\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"删除中\\\",\\n        \\\"value\\\": \\\"deleting\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"配置中\\\",\\n        \\\"value\\\": \\\"status\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"使用中\\\",\\n        \\\"value\\\": \\\"inUse\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"扩容中\\\",\\n        \\\"value\\\": \\\"extending\\\"\\n    },\\n    {\\n        \\\"name\\\": \\\"异常\\\",\\n        \\\"value\\\": \\\"error\\\"\\n    }\\n]\",\"defaultValue\":\"\"},{\"id\":\"1723604497307\",\"searchName\":\"挂载云服务器\",\"searchField\":\"attachments\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"}]', `action_info` = '[{\"id\":\"16878639374610\",\"operateName\":\"创建\",\"operateNameEn\":\"Create\",\"operateCode\":\"EBS:create:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-plus-o-1\",\"operateTheme\":\"primary\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1382742951518208,\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/res_apply\",\"submitParams\":\"\\r\\n\\r\\n{\\r\\n    \\\"orderType\\\": \\\"apply\\\",\\r\\n    \\\"projectId\\\":  \\\"${orgId}\\\",\\r\\n    \\\"productName\\\": \\\"${name}\\\",\\r\\n    \\\"couponId\\\": \\\"${couponId}\\\",\\r\\n    \\\"productInfo\\\": [\\r\\n        {\\r\\n            \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n            \\\"serviceId\\\": null,\\r\\n            \\\"productCode\\\": \\\"EBS\\\",\\r\\n            \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n            \\\"periodUnit\\\": \\\"month\\\",\\r\\n            \\\"period\\\": \\\"${period}\\\",\\r\\n            \\\"amount\\\": 1,\\r\\n            \\\"data\\\": {\\r\\n                \\\"EBS\\\": {\\r\\n                    \\\"chargeItemCategory\\\": \\\"blockStorage\\\",\\r\\n                    \\\"productCode\\\": \\\"EBS\\\",\\r\\n                    \\\"category\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"spec\\\": \\\"${ebsTypeModel}\\\",\\r\\n                    \\\"size\\\": \\\"${allocDiskSize}\\\"\\r\\n                }\\r\\n            }\\r\\n        }\\r\\n    ],\\r\\n    \\\"resourceInfo\\\": {\\r\\n        \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n        \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n        \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n        \\\"action\\\": \\\"create\\\",\\r\\n        \\\"data\\\": {\\r\\n          \\\"name\\\": \\\"${name}\\\",\\r\\n          \\\"productId\\\": \\\"${productId}\\\",\\r\\n          \\\"snapshotModel\\\":{\\r\\n            \\\"id\\\": \\\"${snapshotModel}\\\"  \\r\\n          },\\r\\n          \\\"ebsTypeModel\\\": {\\r\\n            \\\"code\\\": \\\"${ebsTypeModel}\\\"\\r\\n          },\\r\\n          \\\"size\\\": \\\"${allocDiskSize}\\\",\\r\\n          \\\"allocDiskSize\\\": \\\"${allocDiskSize}\\\",\\r\\n          \\\"diskType\\\": \\\"normal\\\",\\r\\n          \\\"description\\\": \\\"${description}\\\",\\r\\n          \\\"period\\\": \\\"${period}\\\",\\r\\n          \\\"zone\\\": {\\r\\n            \\\"uuid\\\": \\\"cn-east-3a\\\",\\r\\n            \\\"id\\\": \\\"${zone}\\\"\\r\\n          },\\r\\n          \\\"chargeType\\\": \\\"${chargeType}\\\"\\r\\n        }\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"btnGroup\":\"env\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"create\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/price\",\"inquiryParams\":\"{\\r\\n\\t\\\"couponId\\\": \\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": [\\r\\n\\t\\t{\\r\\n\\t\\t\\t\\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n\\t\\t\\t\\\"serviceId\\\": \\\"\\\",\\r\\n\\t\\t\\t\\\"productCode\\\": \\\"EBS\\\",\\r\\n\\t\\t\\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n\\t\\t\\t\\\"amount\\\": \\\"1\\\",\\r\\n\\t\\t\\t\\\"period\\\": \\\"${period}\\\",\\r\\n\\t\\t\\t\\\"data\\\": {\\r\\n\\t\\t\\t\\t\\\"EBS\\\": {\\r\\n                    \\\"chargeItemCategory\\\": \\\"blockStorage\\\",\\r\\n                    \\\"productCode\\\": \\\"EBS\\\",\\r\\n                    \\\"category\\\": \\\"SSD\\\",\\r\\n                    \\\"spec\\\": \\\"SSD\\\",\\r\\n                    \\\"size\\\": \\\"${allocDiskSize}\\\"\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t]\\r\\n}\",\"operateSubmitBtn\":\"createBtn\"},{\"id\":\"16883871656910\",\"operateName\":\"导出\",\"operateNameEn\":\"export\",\"operateCode\":\"EBS:search:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"btn\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-public-export\",\"operateTheme\":\"default\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"buildIn\",\"modalWidth\":600,\"openConfirm\":true,\"buildInOperate\":\"export\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/disks/export\",\"submitParams\":\"{\\n    \\\"excelField\\\":\\\"name,cloudEnvName,orgName,zoneName,volumeTypeName,vmName,chargeType,size,vdType,status,mountPoint,startTime,endTime,description\\\"\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16914650472500\",\"operateName\":\"编辑\",\"operateNameEn\":\"\",\"operateCode\":\"EBS:update:HCSO\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    return ![\'creating\', \'createFailure\', \'notAvailable\', \'error\'].includes(record.status) // && [\'normal\', \'renewing\', null].includes(record.resourceStatus)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/disks\",\"submitParams\":\"{\\n\\t\\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n\\t\\\"orgId\\\": \\\"${orgId}\\\",\\n\\t\\\"resTypeCode\\\": \\\"EBS\\\",\\n\\t\\\"action\\\": \\\"update\\\",\\n\\t\\\"data\\\": {\\n        \\\"id\\\": \\\"${id}\\\",\\n\\t\\t\\\"name\\\": \\\"${name}\\\",\\n\\t\\t\\\"description\\\": \\\"${description}\\\"\\n\\t}\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"popupForm\":1385648371310592,\"refreshMode\":\"direct\"},{\"id\":\"16880090166110\",\"operateName\":\"挂载\",\"operateNameEn\":\"object upload\",\"operateCode\":\"EBS:object upload:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    let array = [\'creating\', \'createFailure\', \'notAvailable\', \'inUse\', \'error\']\\n    let inMount = !!record.devicePath\\n    return !(array.includes(record.status) || inMount) //  && [\'normal\', \'renewing\', null].includes(record.resourceStatus)\\n}\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/disks/action\",\"submitParams\":\"{\\r\\n  \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n  \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n  \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n  \\\"action\\\": \\\"attach\\\",\\r\\n  \\\"data\\\": {\\r\\n    \\\"vdId\\\": \\\"${id}\\\",\\r\\n    \\\"id\\\": \\\"${id}\\\",\\r\\n    \\\"evsType\\\": \\\"${evsType}\\\",\\r\\n    \\\"ecsModel\\\": {\\r\\n        \\\"id\\\": \\\"${vmId}\\\"\\r\\n    },\\r\\n    \\\"releaseMode\\\": \\\"${releaseMode}\\\"\\r\\n  }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"popupForm\":1382743818371072},{\"id\":\"16880102925450\",\"operateName\":\"卸载\",\"operateNameEn\":\"object download\",\"operateCode\":\"EBS:object download:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    return ([\'inUse\'].includes(record.status) || !!record.devicePath) && !(record.attachments || []).find(item => item.attachResource === \\\"BMS\\\")\\n}\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/disks/action\",\"submitParams\":\"{\\r\\n\\t\\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n\\t\\\"orgId\\\": \\\"${orgId}\\\",\\r\\n\\t\\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n\\t\\\"action\\\": \\\"detach\\\",\\r\\n\\t\\\"data\\\": {\\r\\n        \\\"vdId\\\": \\\"${id}\\\",\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"vmName\\\":\\\"${vmName}\\\",\\r\\n\\t\\t\\\"ecsModel\\\":{\\r\\n\\t\\t\\t\\\"id\\\": \\\"${vmId}\\\"\\r\\n\\t\\t},\\r\\n\\t\\t\\\"description\\\": \\\"测试硬盘\\\"\\r\\n\\t}\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"enableDesc\":\"已经挂载的云硬盘才支持卸载\",\"popupType\":\"modal\",\"popupForm\":1412330333036544},{\"id\":\"16884718028150\",\"operateName\":\"创建快照\",\"operateNameEn\":\"\",\"operateCode\":\"EBS:objectcreate:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    return ![\'creating\', \'createFailure\', \'notAvailable\', \'error\'].includes(record.status) // && [\'normal\', \'renewing\', null].includes(record.resourceStatus)\\n}\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":1385648060997632,\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/snapshot/ebs\",\"submitParams\":\"{\\r\\n\\t\\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n\\t\\\"orgId\\\": \\\"${orgId}\\\",\\r\\n\\t\\\"resTypeCode\\\":\\\"SHT\\\",\\r\\n\\t\\\"action\\\":\\\"createEbsSht\\\",\\r\\n    \\\"data\\\": {\\r\\n\\t\\t\\\"resType\\\": \\\"EBS\\\",\\r\\n        \\\"vdId\\\": \\\"${id}\\\",\\r\\n        \\\"name\\\": \\\"${name}\\\",\\r\\n        \\\"chargeType\\\": \\\"PostPaide\\\",\\r\\n        \\\"description\\\":\\\"${description}\\\"\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"modal\",\"enableDesc\":\"创建成功的云硬盘才支持创建快照\"},{\"id\":\"17301090753370\",\"operateName\":\"扩容\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:expand\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    return ![\'creating\', \'createFailure\', \'notAvailable\', \'error\'].includes(record.status) && [\'normal\'].includes(record.resourceStatus) && !(record.attachments || []).find(item => item.attachResource === \\\"BMS\\\")\\n}\",\"enableDesc\":\"硬盘已过期\",\"showScript\":\"(text,record)=>{\\n    // 按量计费\\n    return record.resourceId && record.chargeType === \'PostPaid\'\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/disks/extend\",\"submitParams\":\"{\\r\\n      \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n      \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n      \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n      \\\"action\\\": \\\"expand\\\",\\r\\n      \\\"data\\\": {\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"size\\\": \\\"${size}\\\"\\r\\n      }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"change\",\"inquiryType\":\"get\",\"inquiryPath\":\"/v1/bss/billing/modify/inquiry/price\",\"inquiryParams\":\"{\\r\\n    \\\"id\\\": \\\"${resourceId}\\\",\\r\\n    \\\"targetType\\\": \\\"EBS\\\",\\r\\n    \\\"targetSize\\\": \\\"${size.value}\\\",\\r\\n    \\\"targetSpec\\\": \\\"${vdTypeUuid}\\\"\\r\\n}\",\"popupType\":\"drawer\"},{\"id\":\"17320032376690\",\"operateName\":\"扩容\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:expand\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    const notExpired = record.endTime && (new Date(record.endTime) > new Date())\\n    return notExpired && ![\'creating\', \'createFailure\', \'notAvailable\', \'error\'].includes(record.status) && [\'normal\'].includes(record.resourceStatus) && !(record.attachments || []).find(item => item.attachResource === \\\"BMS\\\")\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    // 包年包月\\n    return record.resourceId && record.chargeType === \'PrePaid\'\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/service/resize_resource\",\"submitParams\":\"{\\r\\n    \\\"projectId\\\": \\\"${orgId}\\\",\\r\\n    \\\"productInfo\\\": {\\r\\n        \\\"id\\\": \\\"${resourceId}\\\",\\r\\n        \\\"resourceType\\\": \\\"EBS\\\",\\r\\n        \\\"size\\\": \\\"${size}\\\",\\r\\n        \\\"spec\\\": \\\"${vdTypeUuid}\\\"\\r\\n    },\\r\\n    \\\"resourceInfo\\\": {\\r\\n      \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n      \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n      \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n      \\\"action\\\": \\\"expand\\\",\\r\\n      \\\"data\\\": {\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"size\\\": \\\"${size}\\\"\\r\\n      }\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"change\",\"inquiryType\":\"get\",\"inquiryPath\":\"/v1/bss/billing/modify/inquiry/price\",\"inquiryParams\":\"{\\r\\n    \\\"id\\\": \\\"${resourceId}\\\",\\r\\n    \\\"targetType\\\": \\\"EBS\\\",\\r\\n    \\\"targetSize\\\": \\\"${size.value}\\\",\\r\\n    \\\"targetSpec\\\": \\\"${vdTypeUuid}\\\"\\r\\n}\",\"popupType\":\"drawer\"},{\"id\":\"16885385698651\",\"operateName\":\"查看详情\",\"operateNameEn\":\"view detail\",\"operateCode\":\"EBS:search:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"80%\",\"openConfirm\":true,\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\",\"popupForm\":1383250911076352},{\"id\":\"16891622664101\",\"operateName\":\"删除快照\",\"operateNameEn\":\"\",\"operateCode\":\"EBS:delete:HuaweiCloud\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/disks\",\"submitParams\":\"{\\r\\n\\t\\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n\\t\\\"orgId\\\": \\\"${orgId}\\\",\\r\\n\\t\\\"resTypeCode\\\":\\\"SHT\\\",\\r\\n\\t\\\"action\\\":\\\"delete\\\",\\r\\n    \\\"data\\\": {\\r\\n\\t\\t\\\"resSnapshotId\\\": \\\"${id}\\\"\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"16914945551850\",\"operateName\":\"同步\",\"operateNameEn\":\"\",\"operateCode\":\"EBS:scan:HCSO\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"buildIn\",\"modalWidth\":600,\"openConfirm\":true,\"buildInOperate\":\"sync\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/sync/EBS\",\"submitParams\":\"{\\n    \\\"action\\\": \\\"scan\\\",\\n    \\\"data\\\": {}\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\"},{\"id\":\"17297663145771\",\"operateName\":\"续订\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:renew\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.endTime && [\\\"PrePaid\\\", \\\"prePaid\\\"].includes(record.chargeType) && ![\'deleting\',\'creating\',\'createFailure\',\'deleted\'].includes(record.status) && [\'normal\'].includes(record.resourceStatus) && !(record.attachments || []).find(item => item.attachResource === \\\"BMS\\\")\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.resourceId && ![\'error\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/renew\",\"submitParams\":\"{\\r\\n\\t\\\"projectId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"couponSid\\\":\\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": [\\r\\n\\t\\t{\\r\\n            \\\"id\\\":\\\"${resourceId}\\\",\\r\\n            \\\"name\\\":\\\"${name}\\\",\\r\\n\\t\\t\\t\\\"serviceId\\\": \\\"${serviceId}\\\",\\r\\n\\t\\t\\t\\\"productCode\\\": \\\"EBS\\\",\\r\\n\\t\\t\\t\\\"periodUnit\\\": \\\"month\\\",\\r\\n\\t\\t\\t\\\"period\\\": \\\"${period}\\\"\\r\\n\\t\\t\\t\\r\\n\\t\\t}\\r\\n\\t]\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"renew\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/renew/inquiry/price\",\"inquiryParams\":\"{\\n    \\\"id\\\": \\\"${resourceId}\\\",\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"productCode\\\": \\\"${resTypeCode}\\\",\\n    \\\"couponId\\\": \\\"${couponId}\\\"\\n}\",\"popupType\":\"drawer\"},{\"id\":\"17297663145760\",\"operateName\":\"退订\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:unsubscribe\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'down\', \'active\'].includes(record.status) && [\'normal\'].includes(record.resourceStatus) && !(record.attachments || []).find(item => item.attachResource === \\\"BMS\\\")\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.resourceId && ![\'error\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/v1/bss/service/unsubscribe/res/${resourceId}?type=EBS\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n    \\\"action\\\": \\\"delete\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"isFormat\\\": \\\"false\\\",\\r\\n        \\\"selfServiceEqual\\\":\\\"false\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17303413000290\",\"operateName\":\"删除\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:delete\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return [\'down\', \'active\', \'error\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return !record.resourceId || [\'error\'].includes(record.status)\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/disks\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"EBS\\\",\\r\\n    \\\"action\\\": \\\"delete\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"isFormat\\\": \\\"false\\\",\\r\\n        \\\"selfServiceEqual\\\":\\\"false\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\"},{\"id\":\"17029905747990\",\"operateName\":\"销毁\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:delete\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"(text, record) => {\\n    let array = [\'creating\', \'inUse\']\\n    return array.includes(record.status) ? false : true\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/disks\",\"submitParams\":\"{\\n\\t\\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n\\t\\\"orgId\\\": \\\"${orgId}\\\",\\n\\t\\\"resTypeCode\\\": \\\"EBS\\\",\\n\\t\\\"action\\\": \\\"delete\\\",\\n\\t\\\"data\\\": {\\n       \\\"id\\\":\\\"${id}\\\",\\n\\t\\t\\\"isFormat\\\": \\\"false\\\",\\n\\t\\t\\\"selfServiceEqual\\\": \\\"false\\\"\\n\\t}\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\"},{\"id\":\"17415724614720\",\"operateName\":\"即时转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:chargeTypeToPostPaid:now\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"popupForm\":1426654443921408,\"modalWidth\":\"1200px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPostPaid/change\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"pretopost\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPostPaid/price\",\"inquiryParams\":\"{\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\"\\n}\",\"popupType\":\"drawer\"},{\"id\":\"17415724614731\",\"operateName\":\"到期转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:chargeTypeToPostPaid:end\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"route\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\",\"routePath\":\"/appmain/renew?tabVal=manualRenewal\"},{\"id\":\"17415724614732\",\"operateName\":\"转包年包月\",\"operateNameEn\":\"\",\"operateCode\":\"res:EBS:chargeTypeToPrePaid\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_prepaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PostPaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPrePaid/change\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"couponSid\\\": \\\"\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"posttopre\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPrePaid/price\",\"inquiryParams\":\"{\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1426654524956672}]', `operable` = 'enable', `created_by` = 'admin', `created_dt` = '2024-08-29 09:21:25', `updated_by` = 'admin', `updated_dt` = '2024-08-29 09:21:25', `version` = NULL WHERE `id` = 1385103086772224;