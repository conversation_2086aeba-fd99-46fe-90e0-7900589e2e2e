UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/ams/metricBrowse' WHERE `product_type_code` = 'AOM_metricBrowse';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/alarm/alarmTemplate' WHERE `product_type_code` = 'AOM_template';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/ams/containerInsights' WHERE `product_type_code` = 'AOM_containerInsights';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/ams/K8SclusterMonitor' WHERE `product_type_code` = 'AOM_clusterMonitor';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/prometheusMonitoring/list' WHERE `product_type_code` = 'AOM_prometheusMonitoring';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#aom2/prometheusMonitoring/consumes' WHERE `product_type_code` = 'AOM_prometheusResource';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom/als/logPath' WHERE `product_type_code` = 'AOM_logPath';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom/als/logStores' WHERE `product_type_code` = 'AOM_logStores';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/authManagement' WHERE `product_type_code` = 'AOM_authManagement';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/alarm/batchAlarm' WHERE `product_type_code` = 'AOM_batchAlarm';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/alarm/alarmNotification' WHERE `product_type_code` = 'AOM_alarmNotification';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom/ams/server' WHERE `product_type_code` = 'AOM_server';

UPDATE `sys_idp_product_config` SET `product_link` = '/aom/?region=%s#/aom2/cloudServiceAuth' WHERE `product_type_code` = 'AOM_serverAuth';
