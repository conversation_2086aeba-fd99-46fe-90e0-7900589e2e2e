-- 94162
delete from sys_action_url_rela where interface_url_id = 19981415345353571 and action_auth_key = 'res:SLB:*';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES
( 19981415345353571 ,'res:SLB:*','admin',current_timestamp,'admin',current_timestamp,1);
delete from sys_action_url_rela where interface_url_id = 19981415345353571 and action_auth_key = 'res:SLB:delete';
INSERT INTO sys_action_url_rela (interface_url_id, action_auth_key, created_by, created_dt, updated_by,updated_dt, version) VALUES
( 19981415345353571 ,'res:SLB:delete','admin',current_timestamp,'admin',current_timestamp,1);

-- 94025
delete from res_action_form_template where id = 1426221885882368;
INSERT INTO `res_action_form_template` (`id`, `res_type_code`, `env_id`, `res_action_id`, `name`, `name_en`, `type`, `internal_path`, `status`, `form_info`, `description`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`)
VALUES (1426221885882368, 'GAUSSDB-NOSQL', '1382675901689761,1382675901689762', 1397054108164096, '【勿删】云数据库 GaussDB-创建', 'GaussDB-A created', 'custom', '', 'enable', '{\"list\":[{\"id\":\"radio_1741250545225\",\"type\":\"radio\",\"label\":\"计费类型\",\"model\":\"chargeType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"包年包月\",\"value\":\"PrePaid\"}],\"isRefresh\":false,\"default\":\"PrePaid\"},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"treeSelect_1740996007799\",\"type\":\"treeSelect\",\"label\":\"资源组织\",\"model\":\"orgId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":false,\"checkable\":false,\"maxTagCount\":3,\"checkStrictly\":true,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/v1/system/org/whole_org\",\"paramsType\":\"mapping\",\"params\":[{\"name\":\"id\",\"type\":\"common\",\"value\":\"user.currentOrg.orgSid\"}],\"paramsSchema\":\"\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n  function transformData(obj) {\\n  const { id, name, children, ...rest } = obj;\\n  const transformedObj = {\\n    value: id,\\n    title: name,\\n    key: id,\\n    children\\n  }\\n  if (children && children.length > 0) {\\n    transformedObj.children = children.map(child => transformData(child));\\n  }\\n  return transformedObj;\\n}\\nconst transformedData = data.map(obj => transformData(obj));\\nreturn transformedData\\n}\"},\"isRefresh\":false},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteUrl\":\"\",\"remoteType\":\"get\",\"regexp\":null,\"value\":\"\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"orgChange\\\",formData.treeSelect_1740996007799)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"orgChange\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"select_1730356755149\",\"type\":\"select\",\"label\":\"区域\",\"model\":\"projectUuid\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择区域\",\"labelExtra\":\"\",\"extraHtmlFor\":\"不同区域的资源之间内网不互通。请选择靠近您客户的区域，可以降低网络时延、提高访问速度。\",\"allowClear\":false,\"disabled\":false,\"showSearch\":false,\"isFollowScroll\":false,\"isLoadImmediate\":true,\"matchWidth\":false,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true,\"refreshText\":\"\"},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/cloud/envs/current_list\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"envTypeIds\\\": \\\"preparams.envTypeId\\\"\\n}\",\"handleType\":\"mapping\",\"mapping\":[{\"field\":\"value\",\"mappingField\":\"id\"},{\"field\":\"label\",\"mappingField\":\"cloudEnvName\"}],\"customScript\":\"\"},\"isRefresh\":false,\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"projectUuid-zoneId\\\",\'radio_1681380624842\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-logZoneId\\\",\'radio_1730454945689\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"changeProjectUUid\\\",formData[curItem.id])\\r\\n  \\r\\n  _this.$bus.$emit(\\\"projectUuid-productId\\\",\'select_1694682570219\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-network\\\",\'networkPicker_1681382980712\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-sgId\\\",\'multiSelect_1716429787632\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-eipId\\\",\'select_1681384028767\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuidChange\\\",formData[curItem.id])\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"projectUuid-zoneId\\\",\'radio_1681380624842\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-logZoneId\\\",\'radio_1730454945689\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"changeProjectUUid\\\",formData[curItem.id])\\r\\n   \\r\\n  _this.$bus.$emit(\\\"projectUuid-productId\\\",\'select_1694682570219\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-network\\\",\'networkPicker_1681382980712\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-sgId\\\",\'multiSelect_1716429787632\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-systemDisk\\\",\'volumePicker_1681381663546\',curItem.id)\\r\\n_this.$bus.$emit(\\\"projectUuidChange\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"input_1713841636847\",\"type\":\"input\",\"label\":\"用途\",\"model\":\"tag\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"60%\",\"placeholder\":\"请输入\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":null,\"allowClear\":false,\"readOnly\":false,\"disabled\":false,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"formBackfillItem\":false,\"specialConfigItem\":false,\"modifiableItem\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    curItem.hidden = true\\r\\n    formData[curItem.id] = []\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$on(\\\"tag-data\\\",(value)=>{\\r\\n        formData[curItem.id] = [value]\\r\\n    })\\r\\n}\"},{\"id\":\"input_1721811813863\",\"type\":\"input\",\"label\":\"实例名称\",\"model\":\"name\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入实例名称\",\"labelExtra\":\"以字母或中文开头，可以包含字母、中文、数字、中划线和下划线，长度4到64\",\"extraHtmlFor\":\"\",\"maxLength\":64,\"allowClear\":false,\"readOnly\":false,\"disabled\":false,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"实例名称能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"custom\",\"message\":\"必须以字母开头，可以包含字母、数字、中划线或下划线，长度4到64\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":\"^[a-zA-Z][a-zA-Z0-9-_]{3,63}$\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"radio_1730357210958\",\"type\":\"radio\",\"label\":\"数据库引擎版本\",\"model\":\"dsVersion\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"HCE操作系统的实例，无法与欧拉操作系统的实例搭建容灾。\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"formBackfillItem\":false,\"specialConfigItem\":false,\"modifiableItem\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"8.202\",\"value\":\"8.202\"}],\"isRefresh\":false,\"default\":\"8.202\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    let curData = _this.controlData.find(item => formData[curItem.id] === item.value)\\r\\n    _this.$bus.$emit(\'dsVersionChange\',formData[curItem.id])\\r\\n    \\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\n    if (value === \'8.102\'||value===\'8.104\') {\\n        _this.itemData.options.extraHtmlFor = \'HCE操作系统的实例，无法与欧拉操作系统的实例搭建容灾。\'\\n    }  else {\\n        _this.itemData.options.extraHtmlFor = \'\'\\n    }\\n      _this.$bus.$emit(\'dsVersionChange\',value)\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  \\r\\n}\"},{\"id\":\"radio_1730357838079\",\"type\":\"radio\",\"label\":\"资源类型\",\"model\":\"resourceType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"裸金属\",\"value\":\"bms\"}],\"isRefresh\":false,\"default\":\"bms\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"resourceType\\\",formData.radio_1730357838079)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"resourceType\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n//   {\\r\\n//     \\\"label\\\": \\\"云服务器\\\",\\r\\n//     \\\"value\\\": \\\"ecs\\\"\\r\\n//   },\\r\\n}\"},{\"id\":\"radio_1730357903959\",\"type\":\"radio\",\"label\":\"实例类型\",\"model\":\"instanceMode\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"主备版\",\"value\":\"centralization_standard\"}],\"isRefresh\":false,\"default\":\"centralization_standard\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n//   {\\r\\n//     \\\"label\\\": \\\"分布式版\\\",\\r\\n//     \\\"value\\\": \\\"combined\\\"\\r\\n//   },\\r\\n  // TODO\\r\\n  let value=formData[curItem.id]\\r\\n  setTimeout(()=>{\\r\\n      _this.$bus.$emit(\'instanceModeChange\',value)\\r\\n  }, 0);\\r\\n \\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\'instanceModeChange\',value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n   _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n       if(value===\'1.0\'){\\r\\n           _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"主备版\\\",\\r\\n                  \\\"value\\\":\\\"centralization_standard\\\"\\r\\n              }\\r\\n            ]\\r\\n       }else{\\r\\n        _this.controlData=[\\r\\n              {\\r\\n                \\\"label\\\": \\\"分布式版\\\",\\r\\n                \\\"value\\\": \\\"combined\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"主备版\\\",\\r\\n                \\\"value\\\": \\\"centralization_standard\\\"\\r\\n              }    \\r\\n        ]\\r\\n       }\\r\\n         curItem.source.config = _this.controlData\\r\\n        let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n    //   formData[curItem.id]=_this.controlData[0].value\\r\\n    //   _this.handleChange()\\r\\n   })\\r\\n}\"},{\"id\":\"radio_1730357997903\",\"type\":\"radio\",\"label\":\"部署形态\",\"model\":\"solution\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"企业版\",\"value\":\"hcs2\"}],\"isRefresh\":false,\"default\":\"hcs2\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  let value=formData.radio_1730357997903\\r\\n formData[\'input_1730962475454\']=nodeMap[value]||3\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n     if (value === \'single\') {\\r\\n        _this.itemData.options.extraHtmlFor = \'由于部署在单台机器上，因此无法保障可用性（SLA）。建议用于研发测试环境和作为异地容灾集群使用。\'\\r\\n    }  else {\\r\\n        _this.itemData.options.extraHtmlFor = \'\'\\r\\n    }\\r\\n    _this.$bus.$emit(\\\"solutionChange\\\",value)\\r\\n    let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  formData[\'input_1730962475454\']=nodeMap[value]||3\\r\\n  _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'8.102\'||value===\'3.202\'||value===\'8.104\'){\\r\\n          _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"企业版\\\",\\r\\n                  \\\"value\\\":\\\"hcs2\\\"\\r\\n              }\\r\\n            ]\\r\\n      }else{\\r\\n           _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"1主2备\\\",\\r\\n                  \\\"value\\\":\\\"triset\\\"\\r\\n              }\\r\\n            ]\\r\\n      }\\r\\n      curItem.source.config = _this.controlData\\r\\n    let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n      \\r\\n       let nodeMap={\\r\\n              \'hcs2\':3,\\r\\n              \'triset\':3,\\r\\n              \'logger\':3,\\r\\n              \'single\':1\\r\\n            }\\r\\n      let valueText=formData[curItem.id]\\r\\n      formData[\'input_1730962475454\']=nodeMap[valueText]||3\\r\\n      _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n  })\\r\\n  _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n          if(value===\'combined\'){\\r\\n              _this.controlData=[\\r\\n                  {\\r\\n                      \\\"label\\\":\\\"企业版\\\",\\r\\n                      \\\"value\\\":\\\"hcs2\\\"\\r\\n                  }\\r\\n                    ]\\r\\n              }else{\\r\\n                   _this.controlData=[\\r\\n                        {\\r\\n                        //   \\\"label\\\":\\\"1主1备1日志\\\",\\r\\n                        //   \\\"value\\\":\\\"logger\\\"\\r\\n                        //  },\\r\\n                        //   {\\r\\n                              \\\"label\\\":\\\"单副本\\\",\\r\\n                              \\\"value\\\":\\\"single\\\"\\r\\n                        //   },\\r\\n                        //   {\\r\\n                        //       \\\"label\\\":\\\"1主2备\\\",\\r\\n                        //       \\\"value\\\":\\\"triset\\\"\\r\\n                           }\\r\\n                    ]\\r\\n          }\\r\\n        curItem.source.config = _this.controlData\\r\\n        let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n        // formData[curItem.id]=_this.controlData[0].value\\r\\n           let nodeMap={\\r\\n                  \'hcs2\':3,\\r\\n                  \'triset\':3,\\r\\n                  \'logger\':3,\\r\\n                  \'single\':1\\r\\n                }\\r\\n          let valueText=formData[curItem.id]\\r\\n          formData[\'input_1730962475454\']=nodeMap[valueText]||3\\r\\n          _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n  })\\r\\n}\"},{\"id\":\"input_1730962475454\",\"type\":\"input\",\"label\":\"节点数\",\"model\":\"nodeCount\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":null,\"allowClear\":false,\"readOnly\":false,\"disabled\":true,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n    let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  let value=formData[curItem.id]\\r\\n  formData.input_1730962475454= nodeMap[value]||3\\r\\n  _this.handleBlur()\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\'solution-nodeCount\',(value)=>{\\r\\n      _this.handleBlur()\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358026023\",\"type\":\"radio\",\"label\":\"是否作为容灾备实例\",\"model\":\"disasterRecovery\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"否\",\"value\":\"false\"}],\"isRefresh\":false,\"default\":\"false\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n     _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'1.0\'){\\r\\n            curItem.hidden=true\\r\\n      }else{\\r\\n            curItem.hidden=false\\r\\n      }\\r\\n    \\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n          if(value===\'combined\'){\\r\\n                curItem.hidden=false\\r\\n                formData[curItem.id]=_this.controlData[0].value\\r\\n             }else{\\r\\n               curItem.hidden=true\\r\\n          }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358071502\",\"type\":\"radio\",\"label\":\"事务一致性\",\"model\":\"haConsistency\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"强一致性\",\"value\":\"strong\"},{\"label\":\"最终一致性\",\"value\":\"eventual\"}],\"isRefresh\":false,\"default\":\"strong\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n    _this.$bus.$emit(\\\"haConsistencyChange\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n   _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'1.0\'){\\r\\n            curItem.hidden=true\\r\\n            formData[curItem.id]=\'\'\\r\\n      }else{\\r\\n            curItem.hidden=false\\r\\n             formData[curItem.id]=\'strong\'\\r\\n      }\\r\\n    \\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n      if(value===\\\"centralization_standard\\\"){\\r\\n          curItem.hidden=true\\r\\n      }else{\\r\\n          curItem.hidden=false\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358092760\",\"type\":\"radio\",\"label\":\"副本一致性协议\",\"model\":\"haConsistencyProtocol\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"Quorum\",\"value\":\"quorum\"},{\"label\":\"共享存储\",\"value\":\"syncStorage\"}],\"isRefresh\":false,\"default\":\"quorum\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n//   console.log(_this,\'thisssss\')\\r\\n//   console.log(formData,\'formData\')\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\\\"haConsistencyChange\\\",(value)=>{\\r\\n      if(value===\'eventual\'){\\r\\n          curItem.hidden=true\\r\\n      }else{\\r\\n          curItem.hidden=false\\r\\n      }\\r\\n  })\\r\\n  _this.$bus.$on(\\\"solutionChange\\\",(value)=>{\\r\\n        if(value===\'logger\'){\\r\\n            _this.controlData=[\\r\\n                   {\\r\\n                    \\\"label\\\": \\\"Paxos\\\",\\r\\n                    \\\"value\\\": \\\"paxos\\\"\\r\\n                  }\\r\\n            ]\\r\\n        }else if(value===\'single\'){\\r\\n            _this.controlData=[\\r\\n                      {\\r\\n                        \\\"label\\\": \\\"Quorum\\\",\\r\\n                        \\\"value\\\": \\\"quorum\\\"\\r\\n                      },\\r\\n                        {\\r\\n                             \\\"label\\\": \\\"Paxos\\\",\\r\\n                             \\\"value\\\": \\\"paxos\\\"\\r\\n                        }\\r\\n                ]\\r\\n        }else{\\r\\n            _this.controlData=[\\r\\n                        {\\r\\n                            \\\"label\\\": \\\"Quorum\\\",\\r\\n                            \\\"value\\\": \\\"quorum\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"共享存储\\\",\\r\\n                            \\\"value\\\": \\\"syncStorage\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"Paxos\\\",\\r\\n                            \\\"value\\\": \\\"paxos\\\"\\r\\n                          }\\r\\n                ]\\r\\n        }\\r\\n         curItem.source.config = _this.controlData\\r\\n         let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n        // formData[curItem.id]=_this.controlData[0].value\\r\\n  })\\r\\n  _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n      if(value===\'centralization_standard\'){\\r\\n            _this.controlData=[\\r\\n                   {\\r\\n                    \\\"label\\\": \\\"Paxos\\\",\\r\\n                    \\\"value\\\": \\\"paxos\\\"\\r\\n                  }\\r\\n            ]\\r\\n      }else{\\r\\n          _this.controlData=[\\r\\n              {\\r\\n                \\\"label\\\": \\\"Quorum\\\",\\r\\n                \\\"value\\\": \\\"quorum\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"共享存储\\\",\\r\\n                \\\"value\\\": \\\"syncStorage\\\"\\r\\n              }\\r\\n              ]\\r\\n      }\\r\\n        curItem.source.config = _this.controlData\\r\\n       let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n    // formData[curItem.id]=_this.controlData[0].value\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1681380624842\",\"type\":\"radio\",\"label\":\"可用区\",\"model\":\"zoneId\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[]},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/zones/list\",\"params\":[{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"computeEnable\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"storageEnable\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"zoneEnvRelaStatus\",\"type\":\"fixed\",\"value\":\"enable\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    const list=data||[]\\n    let zoneData=[]\\n    for(let i=0;i<list.length;i++){\\n          if([\'az3.dc1\'].includes( list[i].uuid)){\\n       zoneData.push({\\n           ... list[i],\\n         label: list[i].name,\\n        value:list[i].id,\\n        cpuArc:list[i].extra? JSON.parse( list[i].extra).cpuArch:\\\"\\\"\\n       })\\n      }\\n    }\\n    return zoneData\\n}\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n//   _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n    let zoneName = _this.controlData.find(item=>item.value === formData[curItem.id])?.label\\r\\n    _this.$bus.$emit(\\\"zoneId-zoneName\\\",zoneName)\\r\\n     _this.$bus.$emit(\\\"zoneId\\\",formData[curItem.id])\\r\\n    // _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n    _this.$bus.$emit(\\\"zoneId-slaveZoneId\\\",formData[curItem.id],_this.controlData)\\r\\n       _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n   let zoneItem = _this.controlData.find(item=>item.value===value)\\r\\n   formData.input_1726052207491 = zoneItem?zoneItem.uuid:\\\"\\\"\\r\\n   _this.$bus.$emit(\\\"zoneId-zoneName\\\",zoneItem? zoneItem.label:\'\')\\r\\n   _this.$bus.$emit(\\\"zoneId-slaveZoneId\\\",value,_this.controlData)\\r\\n   _this.$bus.$emit(\\\"zoneId\\\",formData[curItem.id])\\r\\n   _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"zoneId-diskType\\\",\'volumePicker_1681381663546\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n \\r\\n\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"projectUuid-zoneId\\\")\\r\\n    _this.$bus.$on(\\\"projectUuid-zoneId\\\",(id,value)=>{\\r\\n        if(curItem.id === id){\\r\\n            _this.loadData().then(() => {\\r\\n                _this.handleChange()\\r\\n            })\\r\\n            // let params = {\\r\\n            //     // cloudEnvId: formData[\'input_1726034309054\'],\\r\\n            //     cloudEnvId:_this.formConfig.preParamsValue.envId,\\r\\n            //     projectUuid: formData[\'select_1730356755149\']\\r\\n            // }\\r\\n            // _this.axios.get(\'/resource/v1/zones/list\', {params}).then(res => {\\r\\n            //     if (res.status && res.code === 200) {\\r\\n            //         _this.loadData().then(() => {\\r\\n            //             formData[curItem.id] = undefined\\r\\n            //             let filterZoneId = res.data.map(item => item.id)\\r\\n            //             let newControlData = _this.controlData.filter(item => filterZoneId.includes(item.value))\\r\\n            //             _this.controlData = newControlData\\r\\n            //             _this.initControlValue()\\r\\n            //             _this.handleChange()\\r\\n            //         })\\r\\n            //     }\\r\\n            // })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1721813306051\",\"type\":\"radio\",\"label\":\"性能规格\",\"model\":\"propertySpec\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"超高IO\",\"value\":\"LOCALSSD\"}],\"isRefresh\":false,\"default\":\"LOCALSSD\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"table_1681381200803\",\"type\":\"table\",\"label\":\"规格列表\",\"model\":\"specCode\",\"modelAlias\":\"\",\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"90%\",\"height\":176,\"bordered\":true,\"rowKey\":\"id\",\"selectMode\":\"radio\",\"selectFormat\":\"{instanceTypeName}|{cpu}vCPUs|{memory}GB\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"isLoadImmediate\":false,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"isPaging\":true,\"billParamsScript\":\"(controlValue,controlData)=>{\\r\\n    if(controlValue && controlValue.length > 0){\\r\\n        return {\\r\\n            type: \'split\',\\r\\n            list: [\\r\\n                {key: \'specCode\', value:controlValue[0].id},\\r\\n                {key: \'cpu\', value:controlValue[0].cpu},\\r\\n                {key: \'memory\', value:controlValue[0].memory}\\r\\n            ]\\r\\n        } \\r\\n    }\\r\\n    return null\\r\\n}\"},\"columns\":[{\"title\":\"规格名称\",\"dataIndex\":\"name\"}],\"source\":{\"type\":\"api\",\"config\":{\"type\":\"post\",\"url\":\"/resource/v1/actions/GaussDB-A\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"cloudEnvId\\\": \\\"preparams.envId\\\",\\n    \\\"resTypeCode\\\": \\\"fixed.GaussDB-A\\\",\\n    \\\"action\\\": \\\"fixed.engineQuery\\\",\\n    \\\"data\\\": {\\n        \\\"projectUuid\\\":\\\"form.select_1730356755149\\\",\\n        \\\"instanceMode\\\":\\\"form.radio_1730357903959\\\",\\n        \\\"dsVersion\\\":\\\"form.radio_1730357210958\\\",\\n        \\\"zoneId\\\":\\\"form.radio_1681380624842\\\",\\n        \\\"type\\\":\\\"fixed.normal\\\"\\n    }\\n}\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    let insNameMap={\\n        \'normal\':\'通用增强型\'\\n    }\\n    let array = data.map(item=>{\\n        item.id = item.specCode\\n        item.cpu = parseFloat(item.cpu)\\n        item.memory = parseFloat(item.memory)\\n        item.instanceTypeName=insNameMap[item.groupType]\\n        item.name =  parseFloat(item.cpu)+\'vCPUs | \'+parseFloat(item.memory)+\'GB\'\\n        return item\\n    })\\n    return array\\n}\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-cpu\\\",formData[curItem.id])\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-memory\\\",formData[curItem.id])\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-cpu\\\",formData[curItem.id])\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-memory\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"zoneId-vmTypeId\\\")\\r\\n   _this.$bus.$on(\\\"zoneId-vmTypeId\\\",(id,value)=>{\\r\\n       let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n       _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n   \\r\\n   _this.$bus.$on(\\\"changeProjectUUid\\\",(value)=>{\\r\\n        let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n         _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n   _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n        let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n           _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n     _this.$bus.$on(\\\"dsVersionChange\\\",(value)=>{\\r\\n       let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return \\r\\n        \\r\\n           _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n}\\r\\n\"},{\"id\":\"ecsSpec_1741141647999\",\"type\":\"ecsSpec\",\"label\":\"规格\",\"model\":\"vmTypeId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"90%\",\"height\":176,\"bordered\":true,\"rowKey\":\"id\",\"selectMode\":\"radio\",\"selectFormat\":\"{productSpecName} | {cpu} 核数 | {memory} GB\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"isLoadImmediate\":false,\"isBilling\":true,\"productIds\":[\"GAUSSDB-NOSQL\"],\"isPaging\":true,\"billParamsScript\":\"(data) => {\\n    return data && data.length > 0 ? data[0].name : null\\n}\"},\"columns\":[{\"title\":\"规格名称\",\"dataIndex\":\"productSpecName\"},{\"title\":\"vCpu（核数）\",\"dataIndex\":\"cpu\",\"filterAble\":true},{\"title\":\"内存（GB）\",\"dataIndex\":\"memory\",\"filterAble\":true}],\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/service/v1/product/spec_define/list\",\"params\":[{\"name\":\"productCode\",\"type\":\"fixed\",\"value\":\"GAUSSDB-NOSQL\"},{\"name\":\"status\",\"type\":\"fixed\",\"value\":\"enable\"},{\"name\":\"requiredAttr\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"attrCodes\",\"type\":\"form\",\"value\":\"\"},{\"name\":\"attrValues\",\"type\":\"form\",\"value\":\"radio_1715579939068\"},{\"name\":\"arch\",\"type\":\"form\",\"value\":\"radio_1710904921428\"},{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"zoneId\",\"type\":\"form\",\"value\":\"radio_1741141207934\"},{\"name\":\"productSpecCode\",\"type\":\"form\",\"value\":\"radio_1730357838079\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    let tableData = data.map(item=>{\\n        item.name = item.productSpecName\\n        item.attrsData = JSON.parse(item.attrs)\\n         if(item.attrsData&&item.attrsData.length>0){\\n        item.id=item.attrsData[0].specTypeId\\n        }\\n        let cpuObj = item.attrsData.find(cpuItem=>\\n            cpuItem.attrCode === \\\"vcpus\\\"\\n        )\\n        item.cpu = cpuObj&&cpuObj.attrValue?cpuObj.attrValue:\\\"\\\"\\n         let memoryObj = item.attrsData.find(memoryItem=>\\n            memoryItem.attrCode === \\\"ram\\\"\\n        )\\n        item.memory = memoryObj&&memoryObj.attrValue?memoryObj.attrValue:\\\"\\\"\\n        return item\\n    })\\n    \\nfor(var i=0;i<tableData.length;i++){\\n\\tfor(var j=i+1;j<tableData.length;j++){\\n\\t\\t//如果第一个比第二个大，就交换他们两个位置\\n\\t\\tif(Number(tableData[i].cpu)>Number(tableData[j].cpu)||(Number(tableData[i].cpu)==Number(tableData[j].cpu)&&Number(tableData[i].memory)>Number(tableData[j].memory))){\\n\\t\\t\\tvar temp = tableData[i]\\n\\t\\t\\ttableData[i] = tableData[j]\\n\\t\\t\\ttableData[j] = temp\\n\\t\\t}\\n\\t}\\n}\\n    return tableData\\n}\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n    _this.$bus.$on(\\\"zoneId\\\",(value)=>{\\r\\n        const val=formData.radio_1730357838079\\r\\n        if(value&&val){\\r\\n    _this.reLoadGrid()\\r\\n        }\\r\\n     })\\r\\n      _this.$bus.$on(\\\"resourceType\\\",(value)=>{\\r\\n          const val=formData.radio_1681380624842\\r\\n          console.log(value,val)\\r\\n        if(value&&val){\\r\\n            _this.reLoadGrid()\\r\\n        }\\r\\n     })\\r\\n     \\r\\n}\"},{\"id\":\"radio_1721813061982\",\"type\":\"radio\",\"label\":\"存储类型\",\"model\":\"vdType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"增强型高性能：最大吞吐量350MB/s\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"增强型高性能\",\"value\":\"SSD\"}],\"isRefresh\":false,\"default\":\"SSD\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"事务一致性不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"number_1730790286324\",\"type\":\"number\",\"label\":\"存储空间\",\"model\":\"vdSize\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入存储空间\",\"labelExtra\":\"您申请的存储空间会有必要的文件系统开销，这些开销包括索引节点和保留块，以及数据库运行必需的空间。存储空间的数值必须为120的整数倍。\",\"extraHtmlFor\":\"\",\"min\":10640,\"max\":72000,\"precision\":null,\"step\":120,\"disabled\":false,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null,\"default\":\"480\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n      let newVal = Math.floor(value/120)\\r\\n      if(value<480){\\r\\n          formData[curItem.id]=480\\r\\n      }else{\\r\\n         formData[curItem.id] = newVal*120 \\r\\n      }\\r\\n    _this.$bus.$emit(\\\"storageSizeBase-storageSize\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"networkPicker_1681382980712\",\"type\":\"networkPicker\",\"label\":\"私有网络\",\"model\":\"network\",\"modelAlias\":\"\",\"defaultValue\":[],\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"parentPlaceholder\":\"请选择私有网络\",\"subPlaceholder\":\"请选择子网\",\"parentWidth\":\"50%\",\"subWidth\":\"50%\",\"spacer\":\"10px\",\"relationAttr\":\"children\",\"labelExtra\":\"虚拟私有云可以方便的管理、配置内部网络，进行安全、快捷的网络变更，不同虚拟私有云里面的弹性云服务器网络默认不通。\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[],\"isRefresh\":true,\"isFollowScroll\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/vpcs/subnets\",\"params\":[{\"name\":\"envId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"projectEnvType\",\"type\":\"preparams\",\"value\":\"projectEnvType\"},{\"name\":\"enableDhcp\",\"type\":\"fixed\",\"value\":\"true\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\r\\n    let parseData = []\\r\\n    data.forEach(item=>{\\r\\n        let obj = {\\r\\n            label: item.name,\\r\\n            value: item.id,\\r\\n            children: []\\r\\n        }\\r\\n        if(item.subnets && item.subnets.length > 0){\\r\\n            item.subnets.forEach(subItem=>{\\r\\n                let subObj = {\\r\\n                    label: subItem.name+\\\"(\\\"+subItem.cidr+\\\")\\\",\\r\\n                    value: subItem.id,\\r\\n                    cidr:subItem.cidr\\r\\n                }\\r\\n                obj.children.push(subObj)\\r\\n            })\\r\\n        }\\r\\n        parseData.push(obj)\\r\\n    })\\r\\n    return parseData\\r\\n}\\r\\n\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    setTimeout(function() {\\r\\n        let subnetItem =_this.subData.find(item=>item.value === _this.subValue)\\r\\n        // formData.input_1726049588959 = subnetItem?subnetItem.cidr:\\\"\\\"\\r\\n    }, 2000);\\r\\n    \\r\\n}\",\"blured\":\"(value,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"projectUuid-network\\\")\\r\\n    _this.$bus.$on(\\\"projectUuid-network\\\",(id,value)=>{\\r\\n        if(curItem.id === id){\\r\\n            _this.loadData().then(() => {\\r\\n                // 查找当前表单值，已经私有网络数据\\r\\n                let value = formData.networkPicker_1681382980712.vpcId\\r\\n                let array = _this._data.controlData\\r\\n                let cuurentValue = null\\r\\n                if (array.length && value) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === value) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                        _this.vpcValue = undefined\\r\\n                    _this.subValue = undefined\\r\\n                        formData.networkPicker_1681382980712.vpcId = undefined\\r\\n                        formData.networkPicker_1681382980712.subnetId = undefined\\r\\n                    }\\r\\n                } else {\\r\\n                    // 清空值时需将 vpcValue、subValue置空\\r\\n                    _this.vpcValue = undefined\\r\\n                    _this.subValue = undefined\\r\\n                    formData.networkPicker_1681382980712.vpcId = undefined\\r\\n                    formData.networkPicker_1681382980712.subnetId = undefined\\r\\n                }\\r\\n                _this.handleChange()\\r\\n            })\\r\\n        }\\r\\n    })\\r\\n}\",\"changed\":\"(_this, vpcValue, subValue, curItem, formData)=>{\\n    let subnetItem =_this.subData.find(item=>item.value === _this.subValue)\\n    // formData.input_1726049588959 = subnetItem?subnetItem.cidr:\\\"\\\"\\n}\"},{\"id\":\"select_1681384028767\",\"type\":\"select\",\"label\":\"安全组\",\"model\":\"securityId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择安全组\",\"labelExtra\":\"安全组用来实现安全组内和组间虚拟机的访问控制，加强虚拟机的安全保护。用户可以在安全组中定义各种访问规则，当虚拟机加入该安全组后，即受到这些访问规则的保护。\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[],\"isFollowScroll\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/security_groups/list\",\"params\":[{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"}],\"handleType\":\"mapping\",\"mapping\":[{\"field\":\"label\",\"mappingField\":\"name\"},{\"field\":\"value\",\"mappingField\":\"id\"}],\"customScript\":\"\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$off(\\\"projectUuid-sgId\\\")\\r\\n  _this.$bus.$on(\\\"projectUuid-sgId\\\",(id,value)=>{\\r\\n      if(value){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1681384028767\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1681384028767\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1681384028767\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"input_1722392048296\",\"type\":\"input\",\"label\":\"管理员账户名\",\"model\":\"userName\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入管理员账户名\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":64,\"allowClear\":false,\"readOnly\":false,\"disabled\":true,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null,\"default\":\"root\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"password_1704352281902\",\"type\":\"password\",\"label\":\"管理员密码\",\"model\":\"userPassword\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入数据库密码\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"maxLength\":null,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"数据库密码不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"custom\",\"message\":\"密码应为8~32个字符需要包含大写字母、小写字母、数字或特殊字符(~ ! @ # % ^ * - _ = + ? ,)中的三种长度为8~32个字符\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":\"^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#%^*-_+=?,])|(?=.*[a-z])(?=.*\\\\d)(?=.*[~!@#%^*-_+=?,])|(?=.*[A-Z])(?=.*\\\\d)(?=.*[~!@#%^*-_+=?,]))[A-Za-z\\\\d~!@#%^*-_+=?,]{8,32}$\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"password_1721815069354\",\"type\":\"password\",\"label\":\"确认密码\",\"model\":\"comfirmPassword\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入确认密码\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"maxLength\":null,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"确认密码不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"equal\",\"message\":\"两次输入的数据值不相同\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"password_1704352281902\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"select_1725865969300\",\"type\":\"select\",\"label\":\"参数模板\",\"model\":\"configurationId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isFollowScroll\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[\"HRDS\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"post\",\"url\":\"/resource/v1/actions/GAUSSDB-NOSQL\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"cloudEnvId\\\": \\\"preparams.envId\\\",\\n    \\\"resTypeCode\\\": \\\"fixed.GaussDB-A\\\",\\n    \\\"action\\\": \\\"fixed.configQuery\\\",\\n    \\\"data\\\": {\\n        \\\"projectUuid\\\":\\\"form.select_1730356755149\\\",\\n        \\\"dsVersion\\\":\\\"form.radio_1730357210958\\\",\\n        \\\"instanceMode\\\":\\\"form.radio_1730357903959\\\"\\n    }\\n}\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    let array = data.map(item=>{\\n        item.value = item.id\\n        item.label=item.name\\n        return item\\n    })\\n    return array\\n}\"},\"isRefresh\":false,\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    // let value = formData.select_1704351506003\\r\\n    //   if(value === \'5.6\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-5.6\\\",\\r\\n    //         \\\"value\\\": \\\"07fc12a8e0e94df7a3fcf53d0b5e1605pr01\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }else if(value === \'5.7\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-5.7\\\",\\r\\n    //         \\\"value\\\": \\\"3bc1e9cc0d34404b9225ed7a58fb284epr01\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }else if(value === \'8.0\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-8.0\\\",\\r\\n    //         \\\"value\\\": \\\"6a906cd03be84aff81cd41c4c61234e0pr02\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }\\r\\n    //   curItem.source.config = _this.controlData\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$on(\\\"projectUuidChange\\\",(value)=>{\\r\\n      const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n    _this.$bus.$on(\\\"dsVersionChange\\\",(id,value)=>{\\r\\n        const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(id,value)=>{\\r\\n        const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1741250613965\",\"type\":\"radio\",\"label\":\"时长\",\"model\":\"period\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":true,\"productIds\":[\"GAUSSDB-NOSQL\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"1个月\",\"value\":1},{\"label\":\"2个月\",\"value\":2},{\"label\":\"3个月\",\"value\":3},{\"label\":\"4个月\",\"value\":4},{\"label\":\"5个月\",\"value\":5},{\"label\":\"6个月\",\"value\":6},{\"label\":\"9个月\",\"value\":9},{\"label\":\"1年\",\"value\":12},{\"label\":\"2年\",\"value\":24},{\"label\":\"3年\",\"value\":36}],\"isRefresh\":false,\"default\":1},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"}],\"config\":{\"mode\":\"pc\",\"layout\":\"horizontal\",\"labelPosition\":\"left\",\"labelMode\":\"custom\",\"labelWidth\":150,\"labelCol\":{\"span\":4,\"offset\":0},\"wrapperCol\":{\"span\":20,\"offset\":0},\"hideRequiredMark\":false,\"colon\":false,\"customStyle\":{\"padding\":\"0 15px 0 0\",\"overflow-y\":\"auto\"},\"preParams\":[{\"filedKey\":\"envId\",\"filedValue\":\"envId\",\"default\":\"\"},{\"filedKey\":\"projectEnvType\",\"filedValue\":\"envCode\",\"default\":\"\"},{\"filedKey\":\"projectId\",\"filedValue\":\"projectId\",\"default\":\"\"},{\"filedKey\":\"envTypeId\",\"filedValue\":\"envTypeId\",\"default\":\"\"}],\"preParamsValue\":{\"envId\":\"\",\"projectEnvType\":\"\",\"projectId\":\"\",\"envTypeId\":\"\"},\"isAutoMatch\":true,\"isOpenInquiry\":false}}', '', 'bwadmin3', '2025-04-20 16:10:45', 'bwadmin3', '2025-04-20 16:10:45', 1);
-- 94144
delete from res_define_grid where id = 1389976033615872;
INSERT INTO `res_define_grid` (`id`, `res_type_code`, `name`, `name_en`, `code`, `description`, `status`, `base_info`, `datasource_info`, `column_info`, `search_info`, `action_info`, `operable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `version`)
VALUES (1389976033615872, 'SLB', '【勿删】自服务负载均衡管理列表-pf', NULL, 'SERVICE SLB', '【勿删】自服务负载均衡管理列表', 'enable', '{\"gridName\":\"【勿删】自服务负载均衡管理列表\",\"rowKey\":\"\",\"rowSelect\":\"none\",\"operateWidth\":150,\"operateWidthEn\":260,\"operateFixed\":true,\"isStripe\":false}', '{\"sourceType\":\"api\",\"pageType\":\"server\",\"apiPath\":\"/resource/v1/slb\",\"apiParams\":[],\"staticData\":\"[]\"}', '[{\"id\":\"16883848953041\",\"fieldName\":\"名称\",\"fieldKey\":\"name\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":true,\"fixPostion\":\"left\",\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"link\",\"customScript\":\"\",\"relationOperate\":\"16910423106340\"},{\"id\":\"16883645135804\",\"fieldName\":\"状态\",\"fieldKey\":\"status\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"status\",\"customScript\":\"\"},{\"id\":\"16883848953040\",\"fieldName\":\"资源ID\",\"fieldKey\":\"uuid\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1696661217370\",\"fieldName\":\"区域Region\",\"fieldKey\":\"cloudEnvName\",\"fieldEnName\":\"\",\"columnWidth\":240,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"env\",\"customScript\":\"\"},{\"id\":\"1742205337305\",\"fieldName\":\"兼容独享型\",\"fieldKey\":\"guaranteed\",\"fieldEnName\":\"\",\"columnWidth\":90,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    return text?\'是\':\'否\'\\n}\"},{\"id\":\"1729763722432\",\"fieldName\":\"计费类型\",\"fieldKey\":\"chargeType\",\"fieldEnName\":\"\",\"columnWidth\":100,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"custom\",\"customScript\":\"(text,record)=>{\\n    const types={\\n        PrePaid: \'包年包月\',\\n        prePaid: \'包年包月\',\\n        PostPaid: \'按量计费\',\\n        postPaid: \'按量计费\'\\n    }\\n    return types[record.chargeType] || record.chargeType\\n}\"},{\"id\":\"1710224572487\",\"fieldName\":\"资源组织\",\"fieldKey\":\"ownerOrgName\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1725608726390\",\"fieldName\":\"规格\",\"fieldKey\":\"info\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"16883645135803\",\"fieldName\":\"地址\",\"fieldKey\":\"address\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"17010637841220\",\"fieldName\":\"弹性ip\",\"fieldKey\":\"publicIp\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"1729763663875\",\"fieldName\":\"开始时间\",\"fieldKey\":\"startTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"*************\",\"fieldName\":\"结束时间\",\"fieldKey\":\"endTime\",\"fieldEnName\":\"\",\"columnWidth\":160,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"wordwrap\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"},{\"id\":\"**************\",\"fieldName\":\"所有者\",\"fieldKey\":\"ownerAccount\",\"fieldEnName\":\"\",\"columnWidth\":150,\"columnTips\":\"\",\"columnPosition\":\"left\",\"columnMode\":\"ellipsis\",\"columnGroup\":\"\",\"isShow\":true,\"isFixed\":false,\"isSorted\":false,\"sortAttr\":\"\",\"renderMode\":\"default\",\"customScript\":\"\"}]', '[{\"id\":\"*************\",\"searchName\":\"名称\",\"searchField\":\"nameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"},{\"id\":\"*************\",\"searchName\":\"区域Region\",\"searchField\":\"cloudEnvId\",\"searchType\":\"select\",\"searchPosition\":\"dimension\",\"searchSource\":\"api\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[{\"name\":\"name\",\"type\":\"fixed\",\"value\":\"cloudEnvName\"},{\"name\":\"value\",\"type\":\"fixed\",\"value\":\"id\"}],\"staticData\":\"\",\"defaultValue\":\"\",\"apiPath\":\"/resource/v1/cloud/envs/current_list\"},{\"id\":\"1714035551224\",\"searchName\":\"资源组织\",\"searchField\":\"orgNameLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"},{\"id\":\"*************\",\"searchName\":\"弹性ip\",\"searchField\":\"floatingIpLike\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"},{\"id\":\"*************\",\"searchName\":\"所有者\",\"searchField\":\"ownerAccount\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\"},{\"id\":\"*************\",\"searchName\":\"地址\",\"searchField\":\"address\",\"searchType\":\"input\",\"searchPosition\":\"dimension\",\"searchSource\":\"none\",\"apiType\":\"get\",\"apiParams\":[],\"fieldMapping\":[],\"staticData\":\"\",\"defaultValue\":\"\"}]', '[{\"id\":\"**************\",\"operateName\":\"申请服务\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:create\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"toolbar\",\"operateIcon\":\"icon-plus-o-1\",\"enableScript\":\"\",\"showScript\":\"\",\"operateType\":\"route\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/resource/v1/slb\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${envId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"SLB\\\",\\r\\n    \\\"action\\\": \\\"create\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"chargeType\\\":\\\"${chargeType}\\\",\\r\\n        \\\"loadBalancerName\\\": \\\"${loadBalancerName}\\\",\\r\\n        \\\"addressType\\\": \\\"${addressType}\\\",\\r\\n        \\\"networkType\\\": \\\"vpc\\\",\\r\\n        \\\"vpcId\\\": \\\"${network.vpcId}\\\",\\r\\n        \\\"networkId\\\": \\\"${network.subnetId}\\\",\\r\\n        \\\"floatingIpId\\\":\\\"${floatingIpId}\\\",\\r\\n        \\\"projectUuid\\\": \\\"${projectUuid}\\\",\\r\\n        \\\"subnetId\\\": \\\"${network.subnetId}\\\",\\r\\n        \\\"address\\\":\\\"${address}\\\",\\r\\n        \\\"extra\\\":{\\r\\n            \\\"productId\\\":\\\"${productId}\\\"\\r\\n        }\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"operateTheme\":\"primary\",\"popupForm\":\"auto\",\"popupType\":\"modal\",\"operateSubmitBtn\":\"createBtn\",\"refreshMode\":\"direct\",\"btnGroup\":\"env\",\"routePath\":\"/appcmp/console/storage/elb/balancer/apply/${selfId}\",\"selfId\":1390115071893504},{\"id\":\"17422076521800\",\"operateName\":\"编辑\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:update\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"row\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return ![\'creating\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/slb\",\"submitParams\":\"{\\r\\n    \\\"action\\\": \\\"update\\\",\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"SLB\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"projectUuid\\\": \\\"${projectId}\\\",\\r\\n        \\\"id\\\": \\\"${id}\\\",\\r\\n        \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n        \\\"name\\\": \\\"${name}\\\",\\r\\n        \\\"description\\\": \\\"${description}\\\"\\r\\n    }\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"modal\",\"popupForm\":1389259239587840},{\"id\":\"16901933302110\",\"operateName\":\"绑定弹性IP\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:bind\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return ![\'unbinding\'].includes(record.status)\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    let noShowCode = [\'HCS-MO-Admin\']\\n    return !noShowCode.includes(record.cloudEnvCode)&&record.addressType ===\'intranet\'\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"600px\",\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/slb/bind/floatingip\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"EIP\\\",\\r\\n    \\\"action\\\": \\\"bind\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"lbId\\\": \\\"${id}\\\",\\r\\n        \\\"floatIpId\\\": \\\"${floatIpId}\\\"\\r\\n    },\\r\\n    \\\"actionOrderInfo\\\": {\\r\\n        \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n        \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n        \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n        \\\"name\\\": \\\"负载均衡(SLB)\\\",\\r\\n        \\\"type\\\": \\\"resource_operation\\\",\\r\\n        \\\"objectId\\\": \\\"SLB\\\",\\r\\n        \\\"objectName\\\": \\\"负载均衡\\\",\\r\\n        \\\"comment\\\": \\\"${comment}\\\",\\r\\n        \\\"svcOrderDetails\\\": [{\\r\\n            \\\"resourceId\\\": \\\"${id}\\\",\\r\\n        \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n        \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n        \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n        \\t\\\"displayParams-json\\\": [\\r\\n        \\t    {\\r\\n        \\t        \\\"name\\\": \\\"${name}\\\",\\r\\n        \\t        \\\"resTypeCode\\\": \\\"SLB\\\",\\r\\n        \\t        \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n        \\t        \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n        \\t        \\\"resInfo\\\": {\\r\\n        \\t            \\\"actionName\\\": {\\r\\n        \\t                \\\"label\\\": \\\"操作类型\\\",\\r\\n        \\t                \\\"value\\\": \\\"绑定弹性IP\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"cloudEnvName\\\": {\\r\\n        \\t                \\\"label\\\": \\\"云环境\\\",\\r\\n        \\t                \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"address\\\": {\\r\\n        \\t                \\\"label\\\": \\\"地址\\\",\\r\\n        \\t                \\\"value\\\": \\\"${address}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"networkType\\\": {\\r\\n        \\t                \\\"label\\\": \\\"网络类型\\\",\\r\\n        \\t                \\\"value\\\": \\\"${networkType}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"bandwidth\\\": {\\r\\n        \\t                \\\"label\\\": \\\"带宽峰值\\\",\\r\\n        \\t                \\\"value\\\": \\\"${bandwidth}\\\"\\r\\n        \\t            }\\r\\n        \\t        }\\r\\n        \\t    }\\r\\n        \\t],\\r\\n        \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n        \\t\\\"quantity\\\": \\\"1\\\",\\r\\n        \\t\\\"duration\\\": \\\"1\\\"\\r\\n        }]\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupForm\":\"auto\",\"popupType\":\"modal\",\"refreshMode\":\"polling\"},{\"id\":\"16911347966500\",\"operateName\":\"解绑弹性IP\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:unBind\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    let noShowCode = [\'HCS-MO-Admin\']\\n    return !noShowCode.includes(record.cloudEnvCode)&&record.addressType === \\\"internet\\\"\\n}\",\"operateType\":\"confirm\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/resource/v1/slb/unbind/floatingip\",\"submitParams\":\"{\\r\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\r\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"resTypeCode\\\": \\\"EIP\\\",\\r\\n    \\\"action\\\": \\\"unBind\\\",\\r\\n    \\\"data\\\": {\\r\\n        \\\"lbId\\\": \\\"${id}\\\"\\r\\n    },\\r\\n    \\\"actionOrderInfo\\\": {\\r\\n        \\\"orgId\\\": \\\"${orgId}\\\",\\r\\n        \\\"projectId\\\": \\\"${projectId}\\\",\\r\\n        \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\r\\n        \\\"name\\\": \\\"负载均衡(SLB)\\\",\\r\\n        \\\"type\\\": \\\"resource_operation\\\",\\r\\n        \\\"objectId\\\": \\\"SLB\\\",\\r\\n        \\\"objectName\\\": \\\"负载均衡\\\",\\r\\n        \\\"comment\\\": \\\"${comment}\\\",\\r\\n        \\\"svcOrderDetails\\\": [{\\r\\n            \\\"resourceId\\\": \\\"${id}\\\",\\r\\n        \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n        \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\r\\n        \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\r\\n        \\t\\\"displayParams-json\\\": [\\r\\n        \\t    {\\r\\n        \\t        \\\"name\\\": \\\"${name}\\\",\\r\\n        \\t        \\\"resTypeCode\\\": \\\"SLB\\\",\\r\\n        \\t        \\\"chargeType\\\": \\\"${chargeType}\\\",\\r\\n        \\t        \\\"uuid\\\": \\\"${uuid}\\\",\\r\\n        \\t        \\\"resInfo\\\": {\\r\\n        \\t            \\\"actionName\\\": {\\r\\n        \\t                \\\"label\\\": \\\"操作类型\\\",\\r\\n        \\t                \\\"value\\\": \\\"解绑弹性IP\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"cloudEnvName\\\": {\\r\\n        \\t                \\\"label\\\": \\\"云环境\\\",\\r\\n        \\t                \\\"value\\\": \\\"${cloudEnvName}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"address\\\": {\\r\\n        \\t                \\\"label\\\": \\\"地址\\\",\\r\\n        \\t                \\\"value\\\": \\\"${address}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"networkType\\\": {\\r\\n        \\t                \\\"label\\\": \\\"网络类型\\\",\\r\\n        \\t                \\\"value\\\": \\\"${networkType}\\\"\\r\\n        \\t            },\\r\\n        \\t            \\\"bandwidth\\\": {\\r\\n        \\t                \\\"label\\\": \\\"带宽峰值\\\",\\r\\n        \\t                \\\"value\\\": \\\"${bandwidth}\\\"\\r\\n        \\t            }\\r\\n        \\t        }\\r\\n        \\t    }\\r\\n        \\t],\\r\\n        \\t\\\"excuteParams\\\": \\\"\\\",\\r\\n        \\t\\\"quantity\\\": \\\"1\\\",\\r\\n        \\t\\\"duration\\\": \\\"1\\\"\\r\\n        }]\\r\\n    }\\r\\n}\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"refreshMode\":\"polling\"},{\"id\":\"16910423106340\",\"operateName\":\"查看详情\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:get\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"popupForm\":\"auto\",\"modalWidth\":\"1280px\",\"openConfirm\":true,\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"popupType\":\"drawer\"},{\"id\":\"17297541520431\",\"operateName\":\"续订\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:renew\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return ![\'renewing\', \'unsubscribing\', \'pending\'].includes(record.resourceStatus)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"/v1/bss/renew/elb\",\"submitParams\":\"{\\r\\n\\t\\\"projectId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\r\\n    \\\"couponId\\\":\\\"${couponId}\\\",\\r\\n\\t\\\"productInfo\\\": [\\r\\n\\t\\t{\\r\\n            \\\"id\\\":\\\"${resourceId}\\\",\\r\\n            \\\"name\\\":\\\"${name}\\\",\\r\\n\\t\\t\\t\\\"serviceId\\\": \\\"${serviceId}\\\",\\r\\n\\t\\t\\t\\\"productCode\\\": \\\"ELB\\\",\\r\\n\\t\\t\\t\\\"periodUnit\\\": \\\"month\\\",\\r\\n\\t\\t\\t\\\"period\\\": \\\"${period}\\\"\\r\\n\\t\\t\\t\\r\\n\\t\\t}\\r\\n\\t]\\r\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"renew\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/billing/renew/inquiry/price\",\"inquiryParams\":\"{\\n    \\\"id\\\": \\\"${resourceId}\\\",\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"productCode\\\": \\\"ELB\\\",\\n    \\\"couponId\\\": \\\"${couponId}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":\"auto\"},{\"id\":\"17297541520430\",\"operateName\":\"退订\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:unsubscribe\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n    return ![\'renewing\', \'unsubscribing\', \'pending\'].includes(record.resourceStatus)\\n}\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/v1/bss/service/elb/unsubscribe/res/${resourceId}?type=ELB\",\"submitParams\":\"{\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n    \\\"resTypeCode\\\": \\\"SLB\\\",\\n    \\\"action\\\": \\\"delete\\\",\\n    \\\"data\\\": {\\n        \\\"ids\\\": [\\n            \\\"${id}\\\"\\n        ]\\n    }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"popupType\":\"drawer\",\"popupForm\":\"auto\"},{\"id\":\"16969410433620\",\"operateName\":\"删除\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:unsubscribe\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"hide\",\"operateIcon\":\"\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n    let noShowCode = [\'HCS-MO-Admin\']\\n    return !noShowCode.includes(record.cloudEnvCode)\\n}\",\"operateType\":\"confirm\",\"modalWidth\":\"800px\",\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"delete\",\"submitPath\":\"/resource/v1/slb\",\"submitParams\":\"{\\n    \\\"cloudEnvId\\\": \\\"${cloudEnvId}\\\",\\n    \\\"orgId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"resTypeCode\\\": \\\"SLB\\\",\\n    \\\"action\\\": \\\"delete\\\",\\n    \\\"data\\\": { \\\"id\\\": \\\"${id}\\\" },\\n    \\\"actionOrderInfo\\\": {\\n        \\\"orgId\\\": \\\"${orgId}\\\",\\n        \\\"projectId\\\": \\\"${projectId}\\\",\\n        \\\"ownerAccount\\\": \\\"${ownerAccount}\\\", \\n        \\\"name\\\": \\\"负载均衡(SLB)\\\",\\n        \\\"type\\\": \\\"resource_unsubscribe\\\",\\n        \\\"objectId\\\": \\\"SLB\\\",\\n        \\\"objectName\\\": \\\"负载均衡\\\",\\n        \\\"comment\\\": \\\"${comment}\\\",\\n        \\\"svcOrderDetails\\\": [{\\n            \\\"resourceId\\\": \\\"${id}\\\",\\n        \\t\\\"chargeType\\\": \\\"${chargeType}\\\",\\n        \\t\\\"chargeCycle\\\": \\\"${chargeCycle}\\\",\\n        \\t\\\"inquiryParams\\\": \\\"${inquiryParams}\\\",\\n        \\t\\\"displayParams-json\\\": \\\"${displayConfig}\\\",\\n        \\t\\\"excuteParams\\\": \\\"\\\",\\n        \\t\\\"quantity\\\": \\\"1\\\",\\n        \\t\\\"duration\\\": \\\"1\\\"\\n        }]\\n    }\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"popupType\":\"modal\",\"popupForm\":\"auto\"},{\"id\":\"**************\",\"operateName\":\"控制台\",\"operateNameEn\":\"\",\"operateCode\":\"sys:config:integration:idp:link\",\"operateGroup\":\"\",\"showType\":\"btnGroup\",\"showPosition\":\"hide\",\"operateIcon\":\"icon-platform-monitoring-o\",\"operateTheme\":\"primary\",\"btnGroup\":\"console\",\"enableScript\":\"\",\"enableDesc\":\"\",\"showScript\":\"\",\"operateType\":\"popup\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"disable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRouter\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\"},{\"id\":\"17427868232840\",\"operateName\":\"即时转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:chargeTypeToPostPaid:now\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1200px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPostPaid/change/elb\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"pretopost\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPostPaid/price\",\"inquiryParams\":\"{\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1427691366817792},{\"id\":\"17427868232841\",\"operateName\":\"到期转按量\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:chargeTypeToPostPaid:end\",\"operateGroup\":\"转按量\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_postpaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PrePaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"route\",\"modalWidth\":600,\"openConfirm\":true,\"operateSubmitBtn\":\"updateBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"post\",\"submitPath\":\"\",\"submitParams\":\"\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"disable\",\"inquiryOperationType\":\"\",\"inquiryType\":\"\",\"inquiryPath\":\"\",\"inquiryParams\":\"\",\"routePath\":\"/appmain/renew/expiredconvertpostpaid/${record.resourceId}?renewalPolicy=nonAuto&resType=elb\"},{\"id\":\"17427868232842\",\"operateName\":\"转包年包月\",\"operateNameEn\":\"\",\"operateCode\":\"res:SLB:chargeTypeToPrePaid\",\"operateGroup\":\"\",\"showType\":\"link\",\"showPosition\":\"rowMore\",\"operateIcon\":\"\",\"enableScript\":\"(text,record)=>{\\n return record.resourceStatus !== \'to_prepaid\'\\n}\",\"enableDesc\":\"\",\"showScript\":\"(text,record)=>{\\n return record.chargeType === \'PostPaid\' && ![\'creating\',\'createFailure\'].includes(record.status)\\n}\",\"operateType\":\"popup\",\"modalWidth\":\"1000px\",\"openConfirm\":true,\"operateSubmitBtn\":\"submitBtn\",\"operateSubmit\":\"enable\",\"submitType\":\"put\",\"submitPath\":\"/v1/bss/order/chargeType/toPrePaid/change/elb\",\"submitParams\":\"{\\n    \\\"projectSId\\\": \\\"${store.state.user.currentOrg.orgSid}\\\",\\n    \\\"couponSid\\\": \\\"\\\",\\n    \\\"productInfo\\\": \\\"${productInfo}\\\"\\n}\",\"refreshMode\":\"direct\",\"submitAfter\":\"closeRefresh\",\"closeRoutePath\":\"\",\"submitFormConfig\":false,\"getFormConfig\":false,\"getFormConfigPath\":\"\",\"inquiry\":\"enable\",\"inquiryOperationType\":\"posttopre\",\"inquiryType\":\"post\",\"inquiryPath\":\"/v1/bss/order/chargeType/toPrePaid/price\",\"inquiryParams\":\"{\\n    \\\"period\\\": \\\"${period}\\\",\\n    \\\"resourceIdList\\\": \\\"${resourceIdList}\\\"\\n}\",\"popupType\":\"drawer\",\"popupForm\":1427691400323072}]', 'enable', 'hy', '2023-10-08 14:54:48', 'hy', '2023-10-08 14:54:48', 1);


UPDATE `res_action_form_template` SET `res_type_code` = 'GAUSSDB-MYSQL', `env_id` = '1382675901689761,1382675901689762', `res_action_id` = 1501054108164096, `name` = '【勿删】GaussDB for MySQL-创建', `name_en` = 'GaussDB for MySQL create', `type` = 'custom', `internal_path` = '', `status` = 'enable', `form_info` = '{\"list\":[{\"id\":\"radio_1741250545225\",\"type\":\"radio\",\"label\":\"计费类型\",\"model\":\"chargeType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"包年包月\",\"value\":\"PrePaid\"}],\"isRefresh\":false,\"default\":\"PrePaid\"},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"treeSelect_1740996007799\",\"type\":\"treeSelect\",\"label\":\"资源组织\",\"model\":\"orgId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":false,\"checkable\":false,\"maxTagCount\":3,\"checkStrictly\":true,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/v1/system/org/whole_org\",\"paramsType\":\"mapping\",\"params\":[{\"name\":\"id\",\"type\":\"common\",\"value\":\"user.currentOrg.orgSid\"}],\"paramsSchema\":\"\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n  function transformData(obj) {\\n  const { id, name, children, ...rest } = obj;\\n  const transformedObj = {\\n    value: id,\\n    title: name,\\n    key: id,\\n    children\\n  }\\n  if (children && children.length > 0) {\\n    transformedObj.children = children.map(child => transformData(child));\\n  }\\n  return transformedObj;\\n}\\nconst transformedData = data.map(obj => transformData(obj));\\nreturn transformedData\\n}\"},\"isRefresh\":false},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteUrl\":\"\",\"remoteType\":\"get\",\"regexp\":null,\"value\":\"\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"orgChange\\\",formData.treeSelect_1740996007799)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"orgChange\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"select_1730356755149\",\"type\":\"select\",\"label\":\"区域\",\"model\":\"projectUuid\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择区域\",\"labelExtra\":\"\",\"extraHtmlFor\":\"不同区域的资源之间内网不互通。请选择靠近您客户的区域，可以降低网络时延、提高访问速度。\",\"allowClear\":false,\"disabled\":false,\"showSearch\":false,\"isFollowScroll\":false,\"isLoadImmediate\":true,\"matchWidth\":false,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true,\"refreshText\":\"\"},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/cloud/envs/current_list\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"envTypeIds\\\": \\\"preparams.envTypeId\\\"\\n}\",\"handleType\":\"mapping\",\"mapping\":[{\"field\":\"value\",\"mappingField\":\"id\"},{\"field\":\"label\",\"mappingField\":\"cloudEnvName\"}],\"customScript\":\"\"},\"isRefresh\":false,\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"projectUuid-zoneId\\\",\'radio_1681380624842\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-logZoneId\\\",\'radio_1730454945689\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"changeProjectUUid\\\",formData[curItem.id])\\r\\n  \\r\\n  _this.$bus.$emit(\\\"projectUuid-productId\\\",\'select_1694682570219\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-network\\\",\'networkPicker_1681382980712\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-sgId\\\",\'multiSelect_1716429787632\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-eipId\\\",\'select_1681384028767\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuidChange\\\",formData[curItem.id])\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"projectUuid-zoneId\\\",\'radio_1681380624842\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-logZoneId\\\",\'radio_1730454945689\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"changeProjectUUid\\\",formData[curItem.id])\\r\\n   \\r\\n  _this.$bus.$emit(\\\"projectUuid-productId\\\",\'select_1694682570219\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-network\\\",\'networkPicker_1681382980712\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-sgId\\\",\'multiSelect_1716429787632\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"projectUuid-systemDisk\\\",\'volumePicker_1681381663546\',curItem.id)\\r\\n_this.$bus.$emit(\\\"projectUuidChange\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"input_1713841636847\",\"type\":\"input\",\"label\":\"用途\",\"model\":\"tag\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"60%\",\"placeholder\":\"请输入\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":null,\"allowClear\":false,\"readOnly\":false,\"disabled\":false,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"formBackfillItem\":false,\"specialConfigItem\":false,\"modifiableItem\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    curItem.hidden = true\\r\\n    formData[curItem.id] = []\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$on(\\\"tag-data\\\",(value)=>{\\r\\n        formData[curItem.id] = [value]\\r\\n    })\\r\\n}\"},{\"id\":\"input_1721811813863\",\"type\":\"input\",\"label\":\"实例名称\",\"model\":\"name\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入实例名称\",\"labelExtra\":\"以字母或中文开头，可以包含字母、中文、数字、中划线和下划线，长度4到64\",\"extraHtmlFor\":\"\",\"maxLength\":64,\"allowClear\":false,\"readOnly\":false,\"disabled\":false,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"实例名称能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"custom\",\"message\":\"必须以字母开头，可以包含字母、数字、中划线或下划线，长度4到64\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":\"^[a-zA-Z][a-zA-Z0-9-_]{3,63}$\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"radio_1744354346112\",\"type\":\"radio\",\"label\":\"数据库版本\",\"model\":\"gaussdbformysqllite\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"formBackfillItem\":false,\"specialConfigItem\":false,\"modifiableItem\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"GaussDB(for mysql)\",\"value\":\"GaussDB(for mysql)\"}],\"isRefresh\":false,\"default\":\"GaussDB(for mysql)\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    let curData = _this.controlData.find(item => formData[curItem.id] === item.value)\\r\\n    _this.$bus.$emit(\'dsVersionChange\',formData[curItem.id])\\r\\n    \\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\n    if (value === \'8.102\'||value===\'8.104\') {\\n        _this.itemData.options.extraHtmlFor = \'HCE操作系统的实例，无法与欧拉操作系统的实例搭建容灾。\'\\n    }  else {\\n        _this.itemData.options.extraHtmlFor = \'\'\\n    }\\n      _this.$bus.$emit(\'dsVersionChange\',value)\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  \\r\\n}\"},{\"id\":\"radio_1730357210958\",\"type\":\"radio\",\"label\":\"数据库引擎版本\",\"model\":\"dsVersion\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"HCE操作系统的实例，无法与欧拉操作系统的实例搭建容灾。\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"formBackfillItem\":false,\"specialConfigItem\":false,\"modifiableItem\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"8.0\",\"value\":\"8.0\"}],\"isRefresh\":false,\"default\":\"8.0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    let curData = _this.controlData.find(item => formData[curItem.id] === item.value)\\r\\n    _this.$bus.$emit(\'dsVersionChange\',formData[curItem.id])\\r\\n    \\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\n    if (value === \'8.102\'||value===\'8.104\') {\\n        _this.itemData.options.extraHtmlFor = \'HCE操作系统的实例，无法与欧拉操作系统的实例搭建容灾。\'\\n    }  else {\\n        _this.itemData.options.extraHtmlFor = \'\'\\n    }\\n      _this.$bus.$emit(\'dsVersionChange\',value)\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  \\r\\n}\"},{\"id\":\"radio_1730357838079\",\"type\":\"radio\",\"label\":\"资源类型\",\"model\":\"resourceType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[\"GAUSSDB-MYSQL\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"云服务器\",\"value\":\"ecs\"},{\"label\":\"裸金属\",\"value\":\"bms\"}],\"isRefresh\":false,\"default\":\"ecs\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"resourceType\\\",formData.radio_1730357838079)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\\\"resourceType\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"radio_1730357903959\",\"type\":\"radio\",\"label\":\"实例类型\",\"model\":\"instanceMode\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":true,\"productIds\":[\"GAUSSDB-MYSQL\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"主备\",\"value\":\"HA\"},{\"label\":\"单机\",\"value\":\"cluster\"}],\"isRefresh\":false,\"default\":\"HA\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  let value=formData[curItem.id]\\r\\n  setTimeout(()=>{\\r\\n      _this.$bus.$emit(\'instanceModeChange\',value)\\r\\n  }, 0);\\r\\n \\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$emit(\'instanceModeChange\',value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n   _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n       if(value===\'1.0\'){\\r\\n           _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"主备版\\\",\\r\\n                  \\\"value\\\":\\\"centralization_standard\\\"\\r\\n              }\\r\\n            ]\\r\\n       }else{\\r\\n        _this.controlData=[\\r\\n              {\\r\\n                \\\"label\\\": \\\"分布式版\\\",\\r\\n                \\\"value\\\": \\\"combined\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"主备版\\\",\\r\\n                \\\"value\\\": \\\"centralization_standard\\\"\\r\\n              }    \\r\\n        ]\\r\\n       }\\r\\n         curItem.source.config = _this.controlData\\r\\n        let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n    //   formData[curItem.id]=_this.controlData[0].value\\r\\n    //   _this.handleChange()\\r\\n   })\\r\\n}\"},{\"id\":\"radio_1730357997903\",\"type\":\"radio\",\"label\":\"部署形态\",\"model\":\"solution\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"企业版\",\"value\":\"hcs2\"}],\"isRefresh\":false,\"default\":\"hcs2\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  let value=formData.radio_1730357997903\\r\\n formData[\'input_1730962475454\']=nodeMap[value]||3\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n     if (value === \'single\') {\\r\\n        _this.itemData.options.extraHtmlFor = \'由于部署在单台机器上，因此无法保障可用性（SLA）。建议用于研发测试环境和作为异地容灾集群使用。\'\\r\\n    }  else {\\r\\n        _this.itemData.options.extraHtmlFor = \'\'\\r\\n    }\\r\\n    _this.$bus.$emit(\\\"solutionChange\\\",value)\\r\\n    let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  formData[\'input_1730962475454\']=nodeMap[value]||3\\r\\n  _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'8.102\'||value===\'3.202\'||value===\'8.104\'){\\r\\n          _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"企业版\\\",\\r\\n                  \\\"value\\\":\\\"hcs2\\\"\\r\\n              }\\r\\n            ]\\r\\n      }else{\\r\\n           _this.controlData=[\\r\\n              {\\r\\n                  \\\"label\\\":\\\"1主2备\\\",\\r\\n                  \\\"value\\\":\\\"triset\\\"\\r\\n              }\\r\\n            ]\\r\\n      }\\r\\n      curItem.source.config = _this.controlData\\r\\n    let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n      \\r\\n       let nodeMap={\\r\\n              \'hcs2\':3,\\r\\n              \'triset\':3,\\r\\n              \'logger\':3,\\r\\n              \'single\':1\\r\\n            }\\r\\n      let valueText=formData[curItem.id]\\r\\n      formData[\'input_1730962475454\']=nodeMap[valueText]||3\\r\\n      _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n  })\\r\\n  _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n          if(value===\'combined\'){\\r\\n              _this.controlData=[\\r\\n                  {\\r\\n                      \\\"label\\\":\\\"企业版\\\",\\r\\n                      \\\"value\\\":\\\"hcs2\\\"\\r\\n                  }\\r\\n                    ]\\r\\n              }else{\\r\\n                   _this.controlData=[\\r\\n                        {\\r\\n                          \\\"label\\\":\\\"1主1备1日志\\\",\\r\\n                          \\\"value\\\":\\\"logger\\\"\\r\\n                         },\\r\\n                          {\\r\\n                              \\\"label\\\":\\\"单副本\\\",\\r\\n                              \\\"value\\\":\\\"single\\\"\\r\\n                          },\\r\\n                          {\\r\\n                              \\\"label\\\":\\\"1主2备\\\",\\r\\n                              \\\"value\\\":\\\"triset\\\"\\r\\n                           }\\r\\n                    ]\\r\\n          }\\r\\n        curItem.source.config = _this.controlData\\r\\n        let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n        // formData[curItem.id]=_this.controlData[0].value\\r\\n           let nodeMap={\\r\\n                  \'hcs2\':3,\\r\\n                  \'triset\':3,\\r\\n                  \'logger\':3,\\r\\n                  \'single\':1\\r\\n                }\\r\\n          let valueText=formData[curItem.id]\\r\\n          formData[\'input_1730962475454\']=nodeMap[valueText]||3\\r\\n          _this.$bus.$emit(\\\"solution-nodeCount\\\")\\r\\n  })\\r\\n}\"},{\"id\":\"input_1730962475454\",\"type\":\"input\",\"label\":\"节点数\",\"model\":\"nodeCount\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":null,\"allowClear\":false,\"readOnly\":false,\"disabled\":true,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n    let nodeMap={\\r\\n      \'hcs2\':3,\\r\\n      \'triset\':3,\\r\\n      \'logger\':3,\\r\\n      \'single\':1\\r\\n  }\\r\\n  let value=formData[curItem.id]\\r\\n  formData.input_1730962475454= nodeMap[value]||3\\r\\n  _this.handleBlur()\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\'solution-nodeCount\',(value)=>{\\r\\n      _this.handleBlur()\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358026023\",\"type\":\"radio\",\"label\":\"是否作为容灾备实例\",\"model\":\"disasterRecovery\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"否\",\"value\":\"false\"}],\"isRefresh\":false,\"default\":\"false\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n     _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'1.0\'){\\r\\n            curItem.hidden=true\\r\\n      }else{\\r\\n            curItem.hidden=false\\r\\n      }\\r\\n    \\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n          if(value===\'combined\'){\\r\\n                curItem.hidden=false\\r\\n                formData[curItem.id]=_this.controlData[0].value\\r\\n             }else{\\r\\n               curItem.hidden=true\\r\\n          }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358071502\",\"type\":\"radio\",\"label\":\"事务一致性\",\"model\":\"haConsistency\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"强一致性\",\"value\":\"strong\"},{\"label\":\"最终一致性\",\"value\":\"eventual\"}],\"isRefresh\":false,\"default\":\"strong\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n    _this.$bus.$emit(\\\"haConsistencyChange\\\",value)\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n   _this.$bus.$on(\'dsVersionChange\',(value)=>{\\r\\n      if(value===\'1.0\'){\\r\\n            curItem.hidden=true\\r\\n            formData[curItem.id]=\'\'\\r\\n      }else{\\r\\n            curItem.hidden=false\\r\\n             formData[curItem.id]=\'strong\'\\r\\n      }\\r\\n    \\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n      if(value===\\\"centralization_standard\\\"){\\r\\n          curItem.hidden=true\\r\\n      }else{\\r\\n          curItem.hidden=false\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1730358092760\",\"type\":\"radio\",\"label\":\"副本一致性协议\",\"model\":\"haConsistencyProtocol\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"Quorum\",\"value\":\"quorum\"},{\"label\":\"共享存储\",\"value\":\"syncStorage\"}],\"isRefresh\":false,\"default\":\"quorum\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n//   console.log(_this,\'thisssss\')\\r\\n//   console.log(formData,\'formData\')\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n  _this.$bus.$on(\\\"haConsistencyChange\\\",(value)=>{\\r\\n      if(value===\'eventual\'){\\r\\n          curItem.hidden=true\\r\\n      }else{\\r\\n          curItem.hidden=false\\r\\n      }\\r\\n  })\\r\\n  _this.$bus.$on(\\\"solutionChange\\\",(value)=>{\\r\\n        if(value===\'logger\'){\\r\\n            _this.controlData=[\\r\\n                   {\\r\\n                    \\\"label\\\": \\\"Paxos\\\",\\r\\n                    \\\"value\\\": \\\"paxos\\\"\\r\\n                  }\\r\\n            ]\\r\\n        }else if(value===\'single\'){\\r\\n            _this.controlData=[\\r\\n                      {\\r\\n                        \\\"label\\\": \\\"Quorum\\\",\\r\\n                        \\\"value\\\": \\\"quorum\\\"\\r\\n                      },\\r\\n                        {\\r\\n                             \\\"label\\\": \\\"Paxos\\\",\\r\\n                             \\\"value\\\": \\\"paxos\\\"\\r\\n                        }\\r\\n                ]\\r\\n        }else{\\r\\n            _this.controlData=[\\r\\n                        {\\r\\n                            \\\"label\\\": \\\"Quorum\\\",\\r\\n                            \\\"value\\\": \\\"quorum\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"共享存储\\\",\\r\\n                            \\\"value\\\": \\\"syncStorage\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"Paxos\\\",\\r\\n                            \\\"value\\\": \\\"paxos\\\"\\r\\n                          }\\r\\n                ]\\r\\n        }\\r\\n         curItem.source.config = _this.controlData\\r\\n         let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n        // formData[curItem.id]=_this.controlData[0].value\\r\\n  })\\r\\n  _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n      if(value===\'centralization_standard\'){\\r\\n            _this.controlData=[\\r\\n                   {\\r\\n                    \\\"label\\\": \\\"Paxos\\\",\\r\\n                    \\\"value\\\": \\\"paxos\\\"\\r\\n                  }\\r\\n            ]\\r\\n      }else{\\r\\n          _this.controlData=[\\r\\n              {\\r\\n                \\\"label\\\": \\\"Quorum\\\",\\r\\n                \\\"value\\\": \\\"quorum\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"共享存储\\\",\\r\\n                \\\"value\\\": \\\"syncStorage\\\"\\r\\n              }\\r\\n              ]\\r\\n      }\\r\\n        curItem.source.config = _this.controlData\\r\\n       let array = _this.controlData\\r\\n        let cuurentValue = null\\r\\n        if (array.length && formData[curItem.id]) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === formData[curItem.id]) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                         formData[curItem.id]=_this.controlData[0].value\\r\\n                    }\\r\\n                }\\r\\n    // formData[curItem.id]=_this.controlData[0].value\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1681380624842\",\"type\":\"radio\",\"label\":\"可用区\",\"model\":\"zoneId\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[]},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/zones/list\",\"params\":[{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"computeEnable\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"storageEnable\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"zoneEnvRelaStatus\",\"type\":\"fixed\",\"value\":\"enable\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    const list=data||[]\\n    let zoneData=[]\\n    for(let i=0;i<list.length;i++){\\n          if([\'az0.dc1\'].includes( list[i].uuid)){\\n       zoneData.push({\\n           ... list[i],\\n         label: list[i].name,\\n        value:list[i].id,\\n        cpuArc:list[i].extra? JSON.parse( list[i].extra).cpuArch:\\\"\\\"\\n       })\\n      }\\n    }\\n    return zoneData\\n}\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n//   _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n    let zoneName = _this.controlData.find(item=>item.value === formData[curItem.id])?.label\\r\\n    _this.$bus.$emit(\\\"zoneId-zoneName\\\",zoneName)\\r\\n     _this.$bus.$emit(\\\"zoneId\\\",formData[curItem.id])\\r\\n    // _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n    _this.$bus.$emit(\\\"zoneId-slaveZoneId\\\",formData[curItem.id],_this.controlData)\\r\\n       _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n   let zoneItem = _this.controlData.find(item=>item.value===value)\\r\\n   formData.input_1726052207491 = zoneItem?zoneItem.uuid:\\\"\\\"\\r\\n   _this.$bus.$emit(\\\"zoneId-zoneName\\\",zoneItem? zoneItem.label:\'\')\\r\\n   _this.$bus.$emit(\\\"zoneId-slaveZoneId\\\",value,_this.controlData)\\r\\n   _this.$bus.$emit(\\\"zoneId\\\",formData[curItem.id])\\r\\n   _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"zoneId-diskType\\\",\'volumePicker_1681381663546\',curItem.id)\\r\\n   _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n \\r\\n\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"projectUuid-zoneId\\\")\\r\\n    _this.$bus.$on(\\\"projectUuid-zoneId\\\",(id,value)=>{\\r\\n        if(curItem.id === id){\\r\\n            _this.loadData().then(() => {\\r\\n                _this.handleChange()\\r\\n            })\\r\\n            // let params = {\\r\\n            //     // cloudEnvId: formData[\'input_1726034309054\'],\\r\\n            //     cloudEnvId:_this.formConfig.preParamsValue.envId,\\r\\n            //     projectUuid: formData[\'select_1730356755149\']\\r\\n            // }\\r\\n            // _this.axios.get(\'/resource/v1/zones/list\', {params}).then(res => {\\r\\n            //     if (res.status && res.code === 200) {\\r\\n            //         _this.loadData().then(() => {\\r\\n            //             formData[curItem.id] = undefined\\r\\n            //             let filterZoneId = res.data.map(item => item.id)\\r\\n            //             let newControlData = _this.controlData.filter(item => filterZoneId.includes(item.value))\\r\\n            //             _this.controlData = newControlData\\r\\n            //             _this.initControlValue()\\r\\n            //             _this.handleChange()\\r\\n            //         })\\r\\n            //     }\\r\\n            // })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1721813306051\",\"type\":\"radio\",\"label\":\"性能规格\",\"model\":\"propertySpec\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"超高IO\",\"value\":\"ULTRAHIGH\"}],\"isRefresh\":false,\"default\":\"ULTRAHIGH\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"table_1681381200803\",\"type\":\"table\",\"label\":\"规格列表\",\"model\":\"specCode\",\"modelAlias\":\"\",\"hidden\":true,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"90%\",\"height\":176,\"bordered\":true,\"rowKey\":\"id\",\"selectMode\":\"radio\",\"selectFormat\":\"{instanceTypeName}|{cpu}vCPUs|{memory}GB\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"isLoadImmediate\":false,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"isPaging\":true,\"billParamsScript\":\"(controlValue,controlData)=>{\\r\\n    if(controlValue && controlValue.length > 0){\\r\\n        return {\\r\\n            type: \'split\',\\r\\n            list: [\\r\\n                {key: \'specCode\', value:controlValue[0].id},\\r\\n                {key: \'cpu\', value:controlValue[0].cpu},\\r\\n                {key: \'memory\', value:controlValue[0].memory}\\r\\n            ]\\r\\n        } \\r\\n    }\\r\\n    return null\\r\\n}\"},\"columns\":[{\"title\":\"规格名称\",\"dataIndex\":\"name\"}],\"source\":{\"type\":\"api\",\"config\":{\"type\":\"post\",\"url\":\"/resource/v1/actions/GaussDB-A\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"cloudEnvId\\\": \\\"preparams.envId\\\",\\n    \\\"resTypeCode\\\": \\\"fixed.GaussDB-A\\\",\\n    \\\"action\\\": \\\"fixed.engineQuery\\\",\\n    \\\"data\\\": {\\n        \\\"projectUuid\\\":\\\"form.select_1730356755149\\\",\\n        \\\"instanceMode\\\":\\\"form.radio_1730357903959\\\",\\n        \\\"dsVersion\\\":\\\"form.radio_1730357210958\\\",\\n        \\\"zoneId\\\":\\\"form.radio_1681380624842\\\",\\n        \\\"type\\\":\\\"fixed.normal\\\"\\n    }\\n}\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    let insNameMap={\\n        \'normal\':\'通用增强型\'\\n    }\\n    let array = data.map(item=>{\\n        item.id = item.specCode\\n        item.cpu = parseFloat(item.cpu)\\n        item.memory = parseFloat(item.memory)\\n        item.instanceTypeName=insNameMap[item.groupType]\\n        item.name =  parseFloat(item.cpu)+\'vCPUs | \'+parseFloat(item.memory)+\'GB\'\\n        return item\\n    })\\n    return array\\n}\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-cpu\\\",formData[curItem.id])\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-memory\\\",formData[curItem.id])\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-cpu\\\",formData[curItem.id])\\r\\n  _this.$bus.$emit(\\\"vmTypeUuid-memory\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"zoneId-vmTypeId\\\")\\r\\n   _this.$bus.$on(\\\"zoneId-vmTypeId\\\",(id,value)=>{\\r\\n       let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n       _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n   \\r\\n   _this.$bus.$on(\\\"changeProjectUUid\\\",(value)=>{\\r\\n        let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n         _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n   _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n        let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return\\r\\n       \\r\\n           _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n     _this.$bus.$on(\\\"dsVersionChange\\\",(value)=>{\\r\\n       let projectUuid = formData[\'select_1730356755149\']\\r\\n       let instanceMode = formData[\'radio_1730357903959\']\\r\\n       let dsVersion =formData[\'radio_1730357210958\']\\r\\n       let zoneId = formData[\'radio_1681380624842\']\\r\\n       if(!projectUuid||!instanceMode||!dsVersion||!zoneId) return \\r\\n        \\r\\n           _this.reLoadGrid().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.table_1681381200803\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.id === value[0].id) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'table_1681381200803\', null)\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'table_1681381200803\', null)\\r\\n        }\\r\\n        // _this.reLoadGrid()\\r\\n    })\\r\\n   })\\r\\n}\\r\\n\"},{\"id\":\"ecsSpec_1741141647999\",\"type\":\"ecsSpec\",\"label\":\"规格\",\"model\":\"vmTypeId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"90%\",\"height\":176,\"bordered\":true,\"rowKey\":\"id\",\"selectMode\":\"radio\",\"selectFormat\":\"{productSpecName} | {cpu} 核数 | {memory} GB\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"isLoadImmediate\":false,\"isBilling\":true,\"productIds\":[\"GAUSSDB-MYSQL\"],\"isPaging\":true,\"billParamsScript\":\"(data) => {\\n    return data && data.length > 0 ? data[0].name : null\\n}\"},\"columns\":[{\"title\":\"规格名称\",\"dataIndex\":\"productSpecName\"},{\"title\":\"vCpu（核数）\",\"dataIndex\":\"cpu\",\"filterAble\":true},{\"title\":\"内存（GB）\",\"dataIndex\":\"memory\",\"filterAble\":true}],\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/service/v1/product/spec_define/list\",\"params\":[{\"name\":\"productCode\",\"type\":\"fixed\",\"value\":\"GAUSSDB-MYSQL\"},{\"name\":\"status\",\"type\":\"fixed\",\"value\":\"enable\"},{\"name\":\"requiredAttr\",\"type\":\"fixed\",\"value\":\"true\"},{\"name\":\"attrCodes\",\"type\":\"form\",\"value\":\"\"},{\"name\":\"attrValues\",\"type\":\"form\",\"value\":\"radio_1715579939068\"},{\"name\":\"arch\",\"type\":\"form\",\"value\":\"radio_1710904921428\"},{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"zoneId\",\"type\":\"form\",\"value\":\"radio_1741141207934\"},{\"name\":\"instanceMode\",\"type\":\"form\",\"value\":\"radio_1730357903959\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    let tableData = data.map(item=>{\\n        item.name = item.productSpecName\\n        item.attrsData = JSON.parse(item.attrs)\\n         if(item.attrsData&&item.attrsData.length>0){\\n        item.id=item.attrsData[0].specTypeId\\n        }\\n        let cpuObj = item.attrsData.find(cpuItem=>\\n            cpuItem.attrCode === \\\"vcpus\\\"\\n        )\\n        item.cpu = cpuObj&&cpuObj.attrValue?cpuObj.attrValue:\\\"\\\"\\n         let memoryObj = item.attrsData.find(memoryItem=>\\n            memoryItem.attrCode === \\\"ram\\\"\\n        )\\n        item.memory = memoryObj&&memoryObj.attrValue?memoryObj.attrValue:\\\"\\\"\\n        return item\\n    })\\n    \\nfor(var i=0;i<tableData.length;i++){\\n\\tfor(var j=i+1;j<tableData.length;j++){\\n\\t\\t//如果第一个比第二个大，就交换他们两个位置\\n\\t\\tif(Number(tableData[i].cpu)>Number(tableData[j].cpu)||(Number(tableData[i].cpu)==Number(tableData[j].cpu)&&Number(tableData[i].memory)>Number(tableData[j].memory))){\\n\\t\\t\\tvar temp = tableData[i]\\n\\t\\t\\ttableData[i] = tableData[j]\\n\\t\\t\\ttableData[j] = temp\\n\\t\\t}\\n\\t}\\n}\\n    return tableData\\n}\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n    _this.$bus.$on(\\\"zoneId\\\",(value)=>{\\r\\n        const val=formData.radio_1730357838079\\r\\n        if(value&&val){\\r\\n    _this.reLoadGrid()\\r\\n        }\\r\\n     })\\r\\n      _this.$bus.$on(\\\"resourceType\\\",(value)=>{\\r\\n          const val=formData.radio_1681380624842\\r\\n          console.log(value,val)\\r\\n        if(value&&val){\\r\\n            _this.reLoadGrid()\\r\\n        }\\r\\n     })\\r\\n         _this.$bus.$on(\\\"instanceModeChange\\\",(value)=>{\\r\\n    _this.reLoadGrid()\\r\\n     })\\r\\n     \\r\\n}\"},{\"id\":\"radio_1721813061982\",\"type\":\"radio\",\"label\":\"存储类型\",\"model\":\"vdType\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"增强型高性能：最大吞吐量350MB/s\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"增强型高性能\",\"value\":\"SSD\"}],\"isRefresh\":false,\"default\":\"SSD\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"事务一致性不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  _this.$bus.$emit(\\\"zoneId-vmTypeId\\\",\'table_1681381200803\',curItem.id)\\r\\n  _this.$bus.$emit(\\\"engine-version-mode-storageType\\\")\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"number_1730790286324\",\"type\":\"number\",\"label\":\"存储空间\",\"model\":\"vdSize\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入存储空间\",\"labelExtra\":\"您申请的存储空间会有必要的文件系统开销，这些开销包括索引节点和保留块，以及数据库运行必需的空间。存储空间的数值必须为10的整数倍。\",\"extraHtmlFor\":\"\",\"min\":40,\"max\":72000,\"precision\":null,\"step\":10,\"disabled\":false,\"isBilling\":true,\"productIds\":[\"GaussDB-A\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null,\"default\":\"40\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n      let newVal = Math.floor(value/10)\\r\\n      if(value<40){\\r\\n          formData[curItem.id]=40\\r\\n      }else{\\r\\n         formData[curItem.id] = newVal*10 \\r\\n      }\\r\\n    _this.$bus.$emit(\\\"storageSizeBase-storageSize\\\",formData[curItem.id])\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"networkPicker_1681382980712\",\"type\":\"networkPicker\",\"label\":\"私有网络\",\"model\":\"network\",\"modelAlias\":\"\",\"defaultValue\":[],\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"parentPlaceholder\":\"请选择私有网络\",\"subPlaceholder\":\"请选择子网\",\"parentWidth\":\"50%\",\"subWidth\":\"50%\",\"spacer\":\"10px\",\"relationAttr\":\"children\",\"labelExtra\":\"虚拟私有云可以方便的管理、配置内部网络，进行安全、快捷的网络变更，不同虚拟私有云里面的弹性云服务器网络默认不通。\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[],\"isRefresh\":true,\"isFollowScroll\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/vpcs/subnets\",\"params\":[{\"name\":\"envId\",\"type\":\"preparams\",\"value\":\"envId\"},{\"name\":\"projectEnvType\",\"type\":\"preparams\",\"value\":\"projectEnvType\"},{\"name\":\"enableDhcp\",\"type\":\"fixed\",\"value\":\"true\"}],\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\r\\n    let parseData = []\\r\\n    data.forEach(item=>{\\r\\n        let obj = {\\r\\n            label: item.name,\\r\\n            value: item.id,\\r\\n            children: []\\r\\n        }\\r\\n        if(item.subnets && item.subnets.length > 0){\\r\\n            item.subnets.forEach(subItem=>{\\r\\n                let subObj = {\\r\\n                    label: subItem.name+\\\"(\\\"+subItem.cidr+\\\")\\\",\\r\\n                    value: subItem.id,\\r\\n                    cidr:subItem.cidr\\r\\n                }\\r\\n                obj.children.push(subObj)\\r\\n            })\\r\\n        }\\r\\n        parseData.push(obj)\\r\\n    })\\r\\n    return parseData\\r\\n}\\r\\n\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\"},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    setTimeout(function() {\\r\\n        let subnetItem =_this.subData.find(item=>item.value === _this.subValue)\\r\\n        // formData.input_1726049588959 = subnetItem?subnetItem.cidr:\\\"\\\"\\r\\n    }, 2000);\\r\\n    \\r\\n}\",\"blured\":\"(value,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n    _this.$bus.$off(\\\"projectUuid-network\\\")\\r\\n    _this.$bus.$on(\\\"projectUuid-network\\\",(id,value)=>{\\r\\n        if(curItem.id === id){\\r\\n            _this.loadData().then(() => {\\r\\n                // 查找当前表单值，已经私有网络数据\\r\\n                let value = formData.networkPicker_1681382980712.vpcId\\r\\n                let array = _this._data.controlData\\r\\n                let cuurentValue = null\\r\\n                if (array.length && value) {\\r\\n                    // 判断当前值是否存在于新数据源中\\r\\n                    array.forEach(item => {\\r\\n                        if (item.value === value) {\\r\\n                            cuurentValue = true\\r\\n                        }\\r\\n                    })\\r\\n                    // 不存在则设置默认值\\r\\n                    if (!cuurentValue) {\\r\\n                        _this.vpcValue = undefined\\r\\n                    _this.subValue = undefined\\r\\n                        formData.networkPicker_1681382980712.vpcId = undefined\\r\\n                        formData.networkPicker_1681382980712.subnetId = undefined\\r\\n                    }\\r\\n                } else {\\r\\n                    // 清空值时需将 vpcValue、subValue置空\\r\\n                    _this.vpcValue = undefined\\r\\n                    _this.subValue = undefined\\r\\n                    formData.networkPicker_1681382980712.vpcId = undefined\\r\\n                    formData.networkPicker_1681382980712.subnetId = undefined\\r\\n                }\\r\\n                _this.handleChange()\\r\\n            })\\r\\n        }\\r\\n    })\\r\\n}\",\"changed\":\"(_this, vpcValue, subValue, curItem, formData)=>{\\n    let subnetItem =_this.subData.find(item=>item.value === _this.subValue)\\n    // formData.input_1726049588959 = subnetItem?subnetItem.cidr:\\\"\\\"\\n}\"},{\"id\":\"number_1744353339366\",\"type\":\"number\",\"label\":\"数据库端口\",\"model\":\"port\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"默认端口3306\",\"labelExtra\":\"\",\"extraHtmlFor\":\" GaussDB(for MySQL)数据库端口设置范\\n围为1024～65535（其中12017、33062\\n和33071被系统占用不可设置）。\\n当不传该参数时，默认端口如下：\\nGaussDB(for MySQL)默认3306。\",\"min\":1024,\"max\":65535,\"precision\":0,\"step\":1,\"disabled\":false,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null,\"default\":\"3306\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteUrl\":\"\",\"remoteType\":\"get\",\"regexp\":null,\"value\":\"\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"select_1681384028767\",\"type\":\"select\",\"label\":\"安全组\",\"model\":\"securityId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择安全组\",\"labelExtra\":\"安全组用来实现安全组内和组间虚拟机的访问控制，加强虚拟机的安全保护。用户可以在安全组中定义各种访问规则，当虚拟机加入该安全组后，即受到这些访问规则的保护。\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[],\"isFollowScroll\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"get\",\"url\":\"/resource/v1/security_groups/list\",\"params\":[{\"name\":\"cloudEnvId\",\"type\":\"preparams\",\"value\":\"envId\"}],\"handleType\":\"mapping\",\"mapping\":[{\"field\":\"label\",\"mappingField\":\"name\"},{\"field\":\"value\",\"mappingField\":\"id\"}],\"customScript\":\"\",\"paramsType\":\"mapping\",\"paramsSchema\":\"\"},\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[{\"trigger\":\"change\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$off(\\\"projectUuid-sgId\\\")\\r\\n  _this.$bus.$on(\\\"projectUuid-sgId\\\",(id,value)=>{\\r\\n      if(value){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1681384028767\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1681384028767\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1681384028767\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"input_1722392048296\",\"type\":\"input\",\"label\":\"管理员账户名\",\"model\":\"userName\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入管理员账户名\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"maxLength\":64,\"allowClear\":false,\"readOnly\":false,\"disabled\":true,\"defaultPrefix\":false,\"prefixValue\":\"\",\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"none\",\"config\":null,\"default\":\"root\"},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"该项不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"password_1704352281902\",\"type\":\"password\",\"label\":\"管理员密码\",\"model\":\"userPassword\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入数据库密码\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"maxLength\":null,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"数据库密码不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"custom\",\"message\":\"密码应为8~32个字符需要包含大写字母、小写字母、数字或特殊字符(~ ! @ # % ^ * - _ = + ? ,)中的三种长度为8~32个字符\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":\"^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#%^*-_+=?,])|(?=.*[a-z])(?=.*\\\\d)(?=.*[~!@#%^*-_+=?,])|(?=.*[A-Z])(?=.*\\\\d)(?=.*[~!@#%^*-_+=?,]))[A-Za-z\\\\d~!@#%^*-_+=?,]{8,32}$\"}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"password_1721815069354\",\"type\":\"password\",\"label\":\"确认密码\",\"model\":\"comfirmPassword\",\"modelAlias\":\"\",\"defaultValue\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请输入确认密码\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"maxLength\":null,\"isBilling\":false,\"productIds\":[],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"rules\":[{\"trigger\":\"blur\",\"ruleType\":\"required\",\"message\":\"确认密码不能为空\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null},{\"trigger\":\"blur\",\"ruleType\":\"equal\",\"message\":\"两次输入的数据值不相同\",\"length\":1,\"rangeLength\":[1,10],\"controlId\":\"password_1704352281902\",\"remoteType\":\"get\",\"remoteUrl\":\"\",\"regexp\":null}],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"blured\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"},{\"id\":\"select_1725865969300\",\"type\":\"select\",\"label\":\"参数模板\",\"model\":\"configurationId\",\"modelAlias\":\"\",\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"width\":\"100%\",\"placeholder\":\"请选择\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"allowClear\":false,\"disabled\":false,\"showSearch\":true,\"isFollowScroll\":true,\"isLoadImmediate\":false,\"isBilling\":false,\"productIds\":[\"HRDS\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"api\",\"config\":{\"type\":\"post\",\"url\":\"/resource/v1/actions/GAUSSDB-MYSQL\",\"paramsType\":\"customSchema\",\"params\":[],\"paramsSchema\":\"{\\n    \\\"cloudEnvId\\\": \\\"preparams.envId\\\",\\n    \\\"resTypeCode\\\": \\\"fixed.GaussDB-A\\\",\\n    \\\"action\\\": \\\"fixed.configQuery\\\",\\n    \\\"data\\\": {\\n        \\\"projectUuid\\\":\\\"form.select_1730356755149\\\",\\n        \\\"dsVersion\\\":\\\"form.radio_1730357210958\\\",\\n        \\\"instanceMode\\\":\\\"form.radio_1730357903959\\\"\\n    }\\n}\",\"handleType\":\"customScript\",\"mapping\":[],\"customScript\":\"(data)=>{\\n    console.log(data,\'data\')\\n    let array = data.map(item=>{\\n        item.value = item.id\\n        item.label=item.name\\n        return item\\n    })\\n    return array\\n}\"},\"isRefresh\":false,\"default\":\"&index_0\",\"controlData\":[]},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n    // let value = formData.select_1704351506003\\r\\n    //   if(value === \'5.6\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-5.6\\\",\\r\\n    //         \\\"value\\\": \\\"07fc12a8e0e94df7a3fcf53d0b5e1605pr01\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }else if(value === \'5.7\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-5.7\\\",\\r\\n    //         \\\"value\\\": \\\"3bc1e9cc0d34404b9225ed7a58fb284epr01\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }else if(value === \'8.0\'){\\r\\n    //       _this.controlData = [\\r\\n    //       {\\r\\n    //         \\\"label\\\": \\\"Default-MySQL-8.0\\\",\\r\\n    //         \\\"value\\\": \\\"6a906cd03be84aff81cd41c4c61234e0pr02\\\"\\r\\n    //       }\\r\\n    //     ]\\r\\n    //   }\\r\\n    //   curItem.source.config = _this.controlData\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  _this.$bus.$on(\\\"projectUuidChange\\\",(value)=>{\\r\\n      const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n    _this.$bus.$on(\\\"dsVersionChange\\\",(id,value)=>{\\r\\n        const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n    _this.$bus.$on(\\\"instanceModeChange\\\",(id,value)=>{\\r\\n        const val1= formData.select_1730356755149\\r\\n       const val2= formData.radio_1730357210958\\r\\n       const val3=formData.radio_1730357903959\\r\\n      if(val1&&val2&&val3){\\r\\n     _this.loadData().then(() => {\\r\\n        // 获取当前值及数据源\\r\\n        let value = formData.select_1725865969300\\r\\n        let array = _this._data.controlData\\r\\n        let cuurentValue = null\\r\\n        // 判断当前选中在数据源中存储不存在\\r\\n        if (array.length && value) {\\r\\n            array.forEach(item => {\\r\\n                if (item.value === value) {\\r\\n                    cuurentValue = true\\r\\n                }\\r\\n            })\\r\\n            if (!cuurentValue) {\\r\\n                // 不存在则取初始默认值\\r\\n                _this.$set(formData, \'select_1725865969300\', null)\\r\\n                _this.initControlValue()\\r\\n            }\\r\\n        } else {\\r\\n            _this.$set(formData, \'select_1725865969300\', null)\\r\\n            _this.initControlValue()\\r\\n        }\\r\\n    })\\r\\n      }\\r\\n  })\\r\\n}\"},{\"id\":\"radio_1741250613965\",\"type\":\"radio\",\"label\":\"时长\",\"model\":\"period\",\"modelAlias\":\"\",\"defaultValue\":null,\"hidden\":false,\"group\":\"basic\",\"category\":\"\",\"options\":{\"mode\":\"btn\",\"btnStyle\":\"outline\",\"btnSize\":\"default\",\"labelExtra\":\"\",\"extraHtmlFor\":\"\",\"disabled\":false,\"isLoadImmediate\":true,\"isBilling\":true,\"productIds\":[\"GAUSSDB-NOSQL\"],\"billParamsScript\":\"\",\"applyVisible\":true,\"applyEdit\":true},\"source\":{\"type\":\"static\",\"config\":[{\"label\":\"1个月\",\"value\":1},{\"label\":\"2个月\",\"value\":2},{\"label\":\"3个月\",\"value\":3},{\"label\":\"4个月\",\"value\":4},{\"label\":\"5个月\",\"value\":5},{\"label\":\"6个月\",\"value\":6},{\"label\":\"9个月\",\"value\":9},{\"label\":\"1年\",\"value\":12},{\"label\":\"2年\",\"value\":24},{\"label\":\"3年\",\"value\":36}],\"isRefresh\":false,\"default\":1},\"rules\":[],\"mounted\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"changed\":\"(_this,value,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\",\"eventbus\":\"(_this,curItem,formData)=>{\\r\\n  // TODO\\r\\n}\"}],\"config\":{\"mode\":\"pc\",\"layout\":\"horizontal\",\"labelPosition\":\"left\",\"labelMode\":\"custom\",\"labelWidth\":150,\"labelCol\":{\"span\":4,\"offset\":0},\"wrapperCol\":{\"span\":20,\"offset\":0},\"hideRequiredMark\":false,\"colon\":false,\"customStyle\":{\"padding\":\"0 15px 0 0\",\"overflow-y\":\"auto\"},\"preParams\":[{\"filedKey\":\"envId\",\"filedValue\":\"envId\",\"default\":\"\"},{\"filedKey\":\"projectEnvType\",\"filedValue\":\"envCode\",\"default\":\"\"},{\"filedKey\":\"projectId\",\"filedValue\":\"projectId\",\"default\":\"\"},{\"filedKey\":\"envTypeId\",\"filedValue\":\"envTypeId\",\"default\":\"\"}],\"preParamsValue\":{\"envId\":\"\",\"projectEnvType\":\"\",\"projectId\":\"\",\"envTypeId\":\"\"},\"isAutoMatch\":true,\"isOpenInquiry\":false}}', `description` = 'GaussDB for MySQL创建', `created_by` = 'wangadmin', `created_dt` = '2025-04-18 14:28:06', `updated_by` = 'wangadmin', `updated_dt` = '2025-04-18 14:28:06', `version` = 1 WHERE `id` = 1428971716354048;
