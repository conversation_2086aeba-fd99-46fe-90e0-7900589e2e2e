/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.analysis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 月费用汇总服务
 * <AUTHOR>
 * @Date: 2021/6/2 9:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Document(collection = "biz_bill_usage_item_summary_month")
public class InstanceGaapCostSummaryMonth implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @MongoId
    private String id;

    private Long orgSid;

    private Long ownerId;

    /**
     * 账期YYYY－MM
     */
    private String billingCycle;

    /**
     * 原始金额
     */
    private BigDecimal pretaxGrossAmount;

    /**
     * 询价优惠
     */
    private BigDecimal pricingDiscount;

    /**
     * 优惠后金额
     */
    private BigDecimal pretaxAmount;

    /**
     * 抹零金额
     */
    private BigDecimal eraseZeroAmount;

    /**
     * 本月分摊优惠后金额
     */
    private BigDecimal monthGaapPretaxAmount;

    /**
     * 已分摊优惠后金额
     */
    private BigDecimal gaapPretaxAmount;

    private BigDecimal couponDiscount;

    private BigDecimal orgDiscount;
    /**
     * 官网价
     */
    private BigDecimal officialAmount;
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    /**
     * 现金支付金额
     */
    private BigDecimal cashAmount;
    /**
     * 信用额度支付金额
     */
    private BigDecimal creditAmount;
    /**
     * 代金券支付金额
     */
    private BigDecimal couponAmount;
    /**
     * 已开票金
     */
    private BigDecimal invoiceAmount;
    /**
     * 累计出账
     */
    private BigDecimal totalBillAmount;

    private Date createDt;

    private Date updateDt;

    /**
     * 抵扣信用余额
     */
    private BigDecimal rechargeCreditAmount;
    /**
     * 抵扣现金券支付金额
     */
    private BigDecimal deductCouponDiscount;

    /**
     * 运营实体id
     */
    private Long entityId;

    /**
     * 运营实体name
     */
    private String entityName;

}
