/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.snapshot;

import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.SnapshotVo;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "快照")
public class ResSnapshot extends BaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快照ID
     */
    @ApiModelProperty(notes = "快照ID")
    private Long id;

    /**
     * 快照名称
     */
    @ApiModelProperty(notes = "快照名称")
    private String name;

    /**
     * 资源ID
     */
    @ApiModelProperty(notes = "UUID")
    private String uuid;

    /**
     * 关联存储ID
     */
    @ApiModelProperty(notes = "关联存储ID")
    private String resVdId;

    /**
     * 关联云主机ID
     */
    @ApiModelProperty(notes = "关联云主机ID")
    private String resVmId;

    /**
     * 云环境ID
     */
    @ApiModelProperty(notes = "云环境ID")
    private Long cloudEnvId;

    /**
     * 快照大小
     */
    @ApiModelProperty(notes = "快照大小")
    private Double snapshotSize;

    /**
     * 快照属性；01：系统盘，02：数据盘
     */
    @ApiModelProperty(notes = "快照属性；01：系统盘，02：数据盘")
    private String snapshotType;

    /**
     * 状态；01：创建中；02：已完成；03：创建失败
     */
    @ApiModelProperty(notes = "状态；01：创建中；02：已完成；03：创建失败")
    private String status;

    @ApiModelProperty(notes = "快照创建时间")
    private Date createdDate;

    /**
     * 描述
     */
    @ApiModelProperty(notes = "描述")
    private String description;

    /**
     * 企业ID
     */
    @ApiModelProperty(notes = "企业ID")
    private Long orgSid;

    @ApiModelProperty(notes = "关联资源类型")
    private String resType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    @ApiModelProperty("")
    private String resVdName;

    @ApiModelProperty(notes = "主机名称")
    private String resVmName;

    @ApiModelProperty(notes = "磁盘类别ID")
    private Long resVolumeTypeId;

    @ApiModelProperty(notes = "所属组织")
    private String ownerOrgName;

    @ApiModelProperty(notes = "所有者ID")
    private String ownerId;
    private String ownerName;

    private String tenantId;
    private String cloudEnvName;
    private String cloudEnvType;

    /**快照类型
     * 普通快照：normal
     * 备份点快照：backup
     * CBT备份：CBTbackup
     * 默认为normal
     **/
    private String type;
    private Long serviceDeployInstId;

    private Date lastBillTime;
    private String chargeType;

    private Date startTime;
    private Date endTime;

    private Integer period;

    private String orgName;

    private Integer countdown;

    public ResSnapshot() {
    }

    public ResSnapshot(SnapshotVo snapshotVo) {
        this.uuid = snapshotVo.getUuid();
        this.name = snapshotVo.getName();
        this.snapshotSize = snapshotVo.getSnapshotSize();
        this.status = snapshotVo.getStatus();
        this.description = snapshotVo.getDescription();
        this.snapshotType = snapshotVo.getType();
        if (Objects.nonNull(snapshotVo.getCreatedDate())) {
            this.createdDate = snapshotVo.getCreatedDate();
        }
    }
}
