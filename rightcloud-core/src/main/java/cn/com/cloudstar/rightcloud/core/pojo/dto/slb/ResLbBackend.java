/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.slb;

import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.LoadBalanceBackendVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(description = "负载均衡后端服务器")
public class ResLbBackend implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "ID")
    private Long id;

    @ApiModelProperty(notes = "负载均衡ID")
    private Long resLbId;

    @ApiModelProperty(notes = "监听器ID")
    private Long resListenerId;

    @ApiModelProperty(notes = "规则ID")
    private Long extId;

    @ApiModelProperty(notes = "分组ID")
    private Long resLbBackendGroupId;

    @ApiModelProperty(notes = "主机ID")
    private String resVmId;

    @ApiModelProperty(notes = "端口")
    private String port;

    @ApiModelProperty("")
    private Integer weight;

    @ApiModelProperty(notes = "健康状态")
    private String hcStatus;

    @ApiModelProperty(notes = "服务器类型")
    private String serverType;

    @ApiModelProperty(notes = "创建时间")
    private Date createdDt;

    @ApiModelProperty(notes = "创建人")
    private String createdBy;

    @ApiModelProperty(notes = "修改时间")
    private Date updatedDt;

    @ApiModelProperty(notes = "修改人")
    private String updatedBy;

    @ApiModelProperty(notes = "版本")
    private Long version;

    @ApiModelProperty("BackendServers列表")
    private List<BackendServers> backendServersList;

    @ApiModelProperty(notes = "服务器ID")
    private String serverId;

    @ApiModelProperty(notes = "UUID")
    private String uuid;

    @ApiModelProperty(notes = "实例名称")
    private String instanceName;

    @ApiModelProperty("成员名称")
    private String memberName;

    @ApiModelProperty("实例ID")
    private String instanceId;

    @ApiModelProperty(notes = "公网IP")
    private String publicIp;

    @ApiModelProperty(notes = "内网IP")
    private String innerIp;

    @ApiModelProperty(notes = "实例网络类型")
    private String instanceNetworkType;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "状态名称")
    private String statusName;

    @ApiModelProperty(notes = "可用区")
    private String zone;

    @ApiModelProperty(notes = "可用区名称")
    private String zoneName;

    @ApiModelProperty(notes = "网络ID")
    private String networkId;

    @ApiModelProperty(notes = "云环境ID")
    private Long cloudEnvId;

    @ApiModelProperty(notes = "云环境类型")
    private String cloudEnvType;

    @ApiModelProperty(notes = "网络信息")
    private String networkInfo;

    public ResLbBackend() {
    }

    /**
     * Instantiates a new Res lb backend.
     *
     * @param loadBalanceBackendVO the load balance backend vo
     */
    public ResLbBackend(LoadBalanceBackendVO loadBalanceBackendVO) {
        this.weight = loadBalanceBackendVO.getWeight();
        this.serverType = loadBalanceBackendVO.getServerType();
        this.hcStatus = loadBalanceBackendVO.getHcStatus();
        this.status = loadBalanceBackendVO.getStatus();
        this.port = loadBalanceBackendVO.getPort();
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getPublicIp() {
        return publicIp;
    }

    public void setPublicIp(String publicIp) {
        this.publicIp = publicIp;
    }

    public String getInnerIp() {
        return innerIp;
    }

    public void setInnerIp(String innerIp) {
        this.innerIp = innerIp;
    }

    public String getInstanceNetworkType() {
        return instanceNetworkType;
    }

    public void setInstanceNetworkType(String instanceNetworkType) {
        this.instanceNetworkType = instanceNetworkType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCode() {
        return this.getServerId() + this.getPort();
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public List<BackendServers> getBackendServersList() {
        return backendServersList;
    }

    public void setBackendServersList(List<BackendServers> backendServersList) {
        this.backendServersList = backendServersList;
    }

    public String getUuidAli() {
        return this.getResLbBackendGroupId() + this.getResVmId() + this.getPort();
    }

    public Long getResLbId() {
        return resLbId;
    }

    public void setResLbId(Long resLbId) {
        this.resLbId = resLbId;
    }

    public Long getResLbBackendGroupId() {
        return resLbBackendGroupId;
    }

    public void setResLbBackendGroupId(Long resLbBackendGroupId) {
        this.resLbBackendGroupId = resLbBackendGroupId;
    }

    public String getResVmId() {
        return resVmId;
    }

    public void setResVmId(String resVmId) {
        this.resVmId = resVmId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getHcStatus() {
        return hcStatus;
    }

    public void setHcStatus(String hcStatus) {
        this.hcStatus = hcStatus;
    }

    public String getServerType() {
        return serverType;
    }

    public void setServerType(String serverType) {
        this.serverType = serverType;
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Long getResListenerId() {
        return resListenerId;
    }

    public void setResListenerId(Long resListenerId) {
        this.resListenerId = resListenerId;
    }

    public Long getExtId() {
        return extId;
    }

    public Long getCloudEnvId() {
        return cloudEnvId;
    }

    public void setCloudEnvId(Long cloudEnvId) {
        this.cloudEnvId = cloudEnvId;
    }

    public String getCloudEnvType() {
        return cloudEnvType;
    }

    public void setCloudEnvType(String cloudEnvType) {
        this.cloudEnvType = cloudEnvType;
    }

    public void setExtId(Long extId) {
        this.extId = extId;
    }

    public String getNetworkInfo() {
        return networkInfo;
    }

    public void setNetworkInfo(String networkInfo) {
        this.networkInfo = networkInfo;
    }
}
