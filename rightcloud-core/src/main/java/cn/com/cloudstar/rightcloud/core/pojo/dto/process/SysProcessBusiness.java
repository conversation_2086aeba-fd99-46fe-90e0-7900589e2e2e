package cn.com.cloudstar.rightcloud.core.pojo.dto.process;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 流程业务记录
 *
 * <AUTHOR>
 * @date 2023/10/24
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "流程业务记录实体类")
public class SysProcessBusiness {

    /**
     * id
     */
    private Long id;

    /**
     * 流程申请单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processId;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 业务代码
     */
    private String businessCode;

    /**
     * 业务参数
     */
    private String attrData;

    /**
     * 提交人id
     */
    private Long submitUserSid;

    /**
     * 目标1
     */
    private String targetSt;

    /**
     * 目标2
     */
    private String targetNd;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本
     */
    private Long version;

    /**
     * 申请人
     */
    private String owner;
}
