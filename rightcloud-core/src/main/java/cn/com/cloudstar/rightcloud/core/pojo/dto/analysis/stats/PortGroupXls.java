/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @date: 10:19 2018/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortGroupXls {
    @ExcelProperty(value = "名称")
    private String uuid;

    @ExcelProperty(value = "类型")
    private String resVsType;

    @ExcelProperty(value = "云环境名称")
    private String cloudEnvName;

    @ExcelProperty(value = "vlanId")
    private String vlanId;

    @ExcelProperty(value = "交换机")
    private String resVsName;
}
