/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date: 18:25 2019/10/29
 */
@Data
public class MarketServiceVO {
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 显示类型
     */
    private String showType;
    /**
     * 服务组件
     */
    private String serviceComponent;
    /**
     * 目录id
     */
    private String catalogIds;
    /**
     * 服务形式
     */
    private String serviceForm;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 产品路由
     */
    private String serviceUrl;

    /**
     * 是否研发中
     */
    private Boolean develop;

    /**
     * 目录id
     */
    private Long catalogId;
}
