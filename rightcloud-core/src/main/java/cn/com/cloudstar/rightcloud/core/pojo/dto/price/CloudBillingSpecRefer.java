/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.price;

import org.springframework.util.ClassUtils;

import java.util.Objects;
import lombok.Data;

/**
 * The type CloudBillingSpecRefer.
 * <p>
 *
 * <AUTHOR>
 * @date 2018/7/18
 */
@Data
public class CloudBillingSpecRefer {
    private String id;
    private String name;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || ClassUtils.getUserClass(this) != ClassUtils.getUserClass(o)) {
            return false;
        }
        CloudBillingSpecRefer that = (CloudBillingSpecRefer) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, name);
    }
}
