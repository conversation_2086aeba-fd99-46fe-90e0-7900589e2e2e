/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.models.price;

import lombok.Data;

@Data
public class InquiryProduct {

    private String id;
    /**
     * 云服务类型编码，例如ECS的云服务类型编码为“hws.service.type.ec2”
     **/
    private String cloudServiceType;
    /**
     * 资源类型编码，例如ECS的VM为“hws.resource.type.vm”
     **/
    private String resourceType;
    /**
     * 云服务产品的资源规格，例如VM的资源规格举例为“s2.small.1.linux”
     **/
    private String resourceSpecCode;
    /**
     * 资源容量大小，与“resouceSizeMeasureId”配合。
     *
     * 例如购买的卷大小或带宽大小。
     **/
    private Integer resourceSize;
    /**
     * 资源容量度量标识，枚举值如下： 15：Mbps（购买带宽时使用） 17：GB（购买云硬盘时使用）
     **/
    private Integer resourceSizeMeasureId;
    /**
     * 使用量因子。
     **/
    private String usageFactor;

    private Double usageValue;

    private Integer usageMeasureId;

    private String extendParams;
}
