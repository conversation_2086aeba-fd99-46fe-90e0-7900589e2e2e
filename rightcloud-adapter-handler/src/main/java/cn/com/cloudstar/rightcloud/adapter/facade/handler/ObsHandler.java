/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketRepectCheck;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ForObjUpload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectDownload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectRestore;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectStorageCopy;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectUpload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketPolicyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketRepeatCheckResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ForObjUploadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectDownloadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectRestoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectStorageCopyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectUploadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketNameScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketNameScanResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Bucket handler.
 */
@Service
public class ObsHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public BucketCreateResult createBucket(BucketCreate bucketCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketCreateResult) actionServiceFactory.getActionService(bucketCreate).invoke(bucketCreate);
    }

    public BucketRepeatCheckResult bucketRepectCheck(BucketRepectCheck repectCheck)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketRepeatCheckResult) actionServiceFactory.getActionService(repectCheck).invoke(repectCheck);
    }

    public BucketNameScanResult bucketNameCheck(BucketNameScan bucketNameScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketNameScanResult) actionServiceFactory.getActionService(bucketNameScan).invoke(bucketNameScan);
    }

    public BucketDeleteResult deleteBucket(BucketDelete bucketDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketDeleteResult) actionServiceFactory.getActionService(bucketDelete).invoke(bucketDelete);
    }

    public BucketUpdateResult updateBucket(BucketUpdate bucketUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketUpdateResult) actionServiceFactory.getActionService(bucketUpdate).invoke(bucketUpdate);
    }

    public BucketPolicyResult updateBucketPolicy(BucketPolicyUpdate bucketPolicyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketPolicyResult) actionServiceFactory.getActionService(bucketPolicyUpdate)
                                                        .invoke(bucketPolicyUpdate);
    }

    public ForObjUploadResult getForObjUpload(ForObjUpload forObjUpload)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ForObjUploadResult) actionServiceFactory.getActionService(forObjUpload).invoke(forObjUpload);
    }

    public ObjectDeleteResult deleteObject(ObjectDelete objectDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectDeleteResult) actionServiceFactory.getActionService(objectDelete).invoke(objectDelete);
    }

    public ObjectRestoreResult restoreObject(ObjectRestore objectRestore)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectRestoreResult) actionServiceFactory.getActionService(objectRestore).invoke(objectRestore);
    }

    public ObjectDownloadResult downloadObject(ObjectDownload objectDownload)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectDownloadResult) actionServiceFactory.getActionService(objectDownload).invoke(objectDownload);
    }

    public ObjectCreateResult createObject(ObjectCreate objectCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectCreateResult) actionServiceFactory.getActionService(objectCreate).invoke(objectCreate);
    }

    public ObjectStorageCopyResult objectCopy(ObjectStorageCopy objectStorageCopy)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectStorageCopyResult) actionServiceFactory.getActionService(objectStorageCopy).invoke(objectStorageCopy);
    }

    public ObjectUploadResult objectUpload(ObjectUpload objectUpload)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ObjectUploadResult) actionServiceFactory.getActionService(objectUpload).invoke(objectUpload);
    }

}
