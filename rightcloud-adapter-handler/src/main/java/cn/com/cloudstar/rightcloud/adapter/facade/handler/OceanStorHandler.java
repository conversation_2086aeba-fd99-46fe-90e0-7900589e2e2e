/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.*;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * The type Bucket handler.
 */
@Service
public class OceanStorHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public OceanStorAccountResult oceanStorAccount(OceanStorAccount param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorAccountResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorAccountCreateResult oceanStorAccountCreate(OceanStorAccountCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorAccountCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorAccountDelete(OceanStorAccountDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorConvergedQosAssociationCreate(OceanStorConvergedQosAssociationCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorConvergedQosAssociationDelete(OceanStorConvergedQosAssociationDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorConvergedQosPolicyCreateResult oceanStorConvergedQosPolicyCreate(OceanStorConvergedQosPolicyCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorConvergedQosPolicyCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorConvergedQosPolicyDelete(OceanStorConvergedQosPolicyDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorLoginResult oceanStorLogin(OceanStorLogin param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorLoginResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorNamespacesResult oceanStorNamespaces(OceanStorNamespaces param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorNamespacesResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorNamespacesCreateResult oceanStorNamespacesCreate(OceanStorNamespacesCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorNamespacesCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorNamespacesDelete(OceanStorNamespacesDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorNamespacesQuotaResult oceanStorNamespacesQuota(OceanStorNamespacesQuota param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorNamespacesQuotaResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorNamespacesQuotaCreateResult oceanStorNamespacesQuotaCreate(OceanStorNamespacesQuotaCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorNamespacesQuotaCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorNamespacesQuotaDelete(OceanStorNamespacesQuotaDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorNamespacesQuotaUpdate(OceanStorNamespacesQuotaUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorTierPoliciesCreateResult oceanStorTierPoliciesCreate(OceanStorTierPoliciesCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorTierPoliciesCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorTierPoliciesDeleteResult oceanStorTierPoliciesDelete(OceanStorTierPoliciesDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorTierPoliciesDeleteResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorUnixUserResult oceanStorUnixUser(OceanStorUnixUser param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorUnixUserResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorUnixUserDelete(OceanStorUnixUserDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public OceanStorUnixUserGroupResult oceanStorUnixUserGroup(OceanStorUnixUserGroup param) throws CommonAdapterException, AdapterUnavailableException {
        return (OceanStorUnixUserGroupResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult oceanStorUnixUserGroupDelete(OceanStorUnixUserGroupDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }
}
