/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.message;

import cn.com.cloudstar.rightcloud.adapter.facade.handler.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.vdc.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.vdc.result.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.facade.common.Constants;
import cn.com.cloudstar.rightcloud.adapter.facade.util.BaseUtil;
import cn.com.cloudstar.rightcloud.adapter.facade.util.BeanUtil;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockBackupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockBackupRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockClone;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockBackupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockBackupRecoveryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockCloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotRecovryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceFailover;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceFloatingIPAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstancePortUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceReconfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceFailoverResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceFloatingIPActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstancePortUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceReconfigResult;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupRestore;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskExpand;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupRestoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskExpandResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAdds;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAddsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.DescribeAvailableZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.DescribeProductSpec;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.KafkaTopicCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.KafkaTopicDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.DescribeAvailableZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.DescribeProductSpecResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.KafkaTopicCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.KafkaTopicDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.NetCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.NetDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortAssignPrivateIp;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortAssignPrivateIpResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortAttachInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortDetachInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortUnAssignPrivateIp;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortUnAssignPrivateIpResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterAddEntry;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServiceChainContextCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServiceChainContextDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServiceChainCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServiceChainDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VlanPoolCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VlanPoolDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallAntivirusPolicyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallAntivirusPolicyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallAntivirusPolicyRelevantInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallAntivirusPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallIPSPolicyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallIPSPolicyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallIPSPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallIPSSignature;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallRouterRelUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallRuleMove;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallStrategyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallStrategyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallStrategyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.NetCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.NetDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortAttachInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortDetachInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortGroupUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.PortUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouterAddEntryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServiceChainContextCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServiceChainContextDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServiceChainCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServiceChainDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VlanPoolCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VlanPoolDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallAntivirusPolicyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallIPSResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallRuleResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallSignatureResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallStrategyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringApply;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringRelease;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDescribe;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceEnlargeVolume;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceRelease;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceRestart;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDescribeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceEnlargeVolumeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceReleaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceRestartResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PortGroupVmsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.CloneVmAsTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmClone;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmImageCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmMigrate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNicAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNicDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRebuild;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmReconfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmResize;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotRevert;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmTypeChange;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmCloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmImageCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmMigrateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmNicAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmNicDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRebuildResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmReconfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRecoveryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmResizeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotRevertResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTypeChangeResult;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * heavy listener
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HeavyListener {

    /**
     * handle action about virtual machine
     */
    @Autowired
    private VmHandler vmHandler;
    /**
     * handle action about storage
     */
    @Autowired
    private StorageHandler storageHandler;
    /**
     * handle action about net
     */
    @Autowired
    private NetHandler netHandler;

    /**
     * handle action about rds
     */
    @Autowired
    private RdsHandler rdsHandler;

    @Autowired
    private DmsHandler dmsHandler;

    /**
     * 处理高速缓存操作
     **/
    @Autowired
    private DcsHandler dcsHandler;


    @Autowired
    private AdminHandler adminHandler;

    /**
     * Handle message base result.
     *
     * @param routerAddEntry the router add entry
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterAddEntry routerAddEntry) {
        log.info("receiving message for create routerEntry, virtual type : [{}]",
                         routerAddEntry.getVirtEnvType());

        log.info("msg id : [{}]", routerAddEntry.getMsgId());
        RouterAddEntryResult result = new RouterAddEntryResult();
        try {
            result = netHandler.addRouterEntry(routerAddEntry);

        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        BaseUtil.setResult(routerAddEntry, result);
        result.setMsgId(routerAddEntry.getMsgId());
        result.setDestinationCidrBlock(routerAddEntry.getDestinationCidrBlock());
        result.setNextHopId(routerAddEntry.getNextHopId());
        result.setOptions(routerAddEntry.getOptions());
        return result;
    }

    /**
     * receive and handle vm modify
     *
     * @param vmModify vm modify param
     *
     * @return vm modification result
     */
    public VmModifyResult handleMessage(VmModify vmModify) {

        log.info("receiving message for modifying vm, virtual type : [{}] vm name : [{}]" ,vmModify.getVirtEnvType(), vmModify.getVmName());

        log.info("msg id : [{}]", vmModify.getMsgId());

        VmModifyResult vmModifyResult = new VmModifyResult();

        try {
            vmModifyResult = vmHandler.modifyVm(vmModify);
        } catch (CommonAdapterException e) {
            vmModifyResult.setSuccess(false);
            vmModifyResult.setErrCode(e.getErrCode());
            vmModifyResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmModifyResult.setSuccess(false);
            vmModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmModifyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmModifyResult.setSuccess(false);
            vmModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmModifyResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmModify, vmModifyResult);
        vmModifyResult.setMsgId(vmModify.getMsgId());
        vmModifyResult.setVmName(vmModify.getVmName());
        vmModifyResult.setVmId(vmModify.getVmId());
        vmModifyResult.setCpu(vmModify.getCpu());
        vmModifyResult.setMemory(vmModify.getMemory());
        vmModifyResult.setNics(vmModify.getNics());
        vmModifyResult.setDisks(vmModify.getDisks());
        vmModifyResult.setOldCpu(vmModify.getOldCpu());
        vmModifyResult.setOldMemory(vmModify.getOldMemory());
        return vmModifyResult;
    }

    /**
     * Handle message vm create result.
     *
     * @param vmCreate the vm create
     *
     * @return the vm create result
     */
    public VmCreateResult handleMessage(VmCreate vmCreate) {

        log.info("receiving message for creating vm, virtual type : [{}] vm name : [{}]" ,vmCreate.getVirtEnvType(), vmCreate.getName());

        log.info("msg id : [{}]" ,vmCreate.getMsgId());

        VmCreateResult vmCreateResult = new VmCreateResult();
        try {
            vmCreateResult = vmHandler.createVm(vmCreate);

            log.info("adaptor vm :[{}] has been created successfully. {}",vmCreate.getName(),
                     vmCreateResult.isSuccess() ? "" : "But " + vmCreateResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            vmCreateResult.setSuccess(false);
            vmCreateResult.setErrCode(e.getErrCode());
            vmCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmCreateResult.setSuccess(false);
            vmCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            vmCreateResult.setSuccess(false);
            vmCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmCreate, vmCreateResult);
        vmCreateResult.setMsgId(vmCreate.getMsgId());
        vmCreateResult.setVmName(vmCreate.getName());
        vmCreateResult.setId(vmCreate.getId());
        vmCreateResult.setSoftware(vmCreate.getSoftware());
        vmCreateResult.setHostName(vmCreate.getHostName());
        vmCreateResult.setSystemDiskLocation(vmCreate.getSysDiskLocation());
        vmCreateResult.setOrignNics(vmCreate.getNics());
        vmCreateResult.setPhysicalHostPoolId(vmCreate.getPhysicalHostPoolId());

        return vmCreateResult;
    }

    /**
     * Handle message vm create result.
     *
     * @param vmTypeChange the vm create
     *
     * @return the vm create result
     */
    public VmTypeChangeResult handleMessage(VmTypeChange vmTypeChange) {

        log.info("receiving vmTypeChange for creating vm, virtual type : [{}] vm name : [{}]"
                ,vmTypeChange.getVirtEnvType(), vmTypeChange.getName());

        log.info("msg id : [{}]", vmTypeChange.getMsgId());

        VmTypeChangeResult vmTypeChangeResult = new VmTypeChangeResult();
        try {
            vmTypeChangeResult = vmHandler.vmTypeChange(vmTypeChange);

            log.info("adaptor vm :[{}] has been created successfully. {}",vmTypeChange.getName(),
                     vmTypeChangeResult.isSuccess() ? "" : "But " + vmTypeChangeResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            vmTypeChangeResult.setSuccess(false);
            vmTypeChangeResult.setErrCode(e.getErrCode());
            vmTypeChangeResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmTypeChangeResult.setSuccess(false);
            vmTypeChangeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmTypeChangeResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            vmTypeChangeResult.setSuccess(false);
            vmTypeChangeResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmTypeChange, vmTypeChangeResult);
        vmTypeChangeResult.setName(vmTypeChange.getName());
        vmTypeChangeResult.setMsgId(vmTypeChange.getMsgId());
        vmTypeChangeResult.setResVmId(vmTypeChange.getHostId());
        vmTypeChangeResult.setHostUUid(vmTypeChange.getHostUUid());
        vmTypeChangeResult.setVmTypeUuid(vmTypeChange.getVmTypeUuid());
        vmTypeChangeResult.setResChangeRecordId(vmTypeChange.getResChangeRecordId());

        return vmTypeChangeResult;
    }

    /**
     * Handle message vm create result.
     *
     * @param shareCreate the vm create
     *
     * @return the vm create result
     */
    public ShareCreateResult handleMessage(ShareCreate shareCreate) {

        log.info("receiving message for creating share, virtual type : [{}] share name : [{}]" ,shareCreate.getVirtEnvType(), shareCreate.getName());

        log.info("msg id : [{}]", shareCreate.getMsgId());

        ShareCreateResult shareCreateResult = new ShareCreateResult();
        try {
            shareCreateResult = vmHandler.createShare(shareCreate);

            log.info("[adaptor] share :[{}] has been created successfully. {}",shareCreate.getName(),
                     shareCreateResult.isSuccess() ? "" : "But " + shareCreateResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareCreateResult.setSuccess(false);
            shareCreateResult.setErrCode(e.getErrCode());
            shareCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareCreateResult.setSuccess(false);
            shareCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            shareCreateResult.setSuccess(false);
            shareCreateResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(shareCreate, shareCreateResult);
        shareCreateResult.setId(shareCreate.getId());
        shareCreateResult.setOrgSid(shareCreate.getOrgSid());
        shareCreateResult.setServiceOrderId(shareCreate.getServiceOrderId());
        return shareCreateResult;
    }

    /**
     * Handle message vm create result.
     *
     * @param shareDelete the vm create
     *
     * @return the vm create result
     */
    public ShareDeleteResult handleMessage(ShareDelete shareDelete) {

        log.info("receiving message for delete share, virtual type : [{}] share name : [{}]",shareDelete.getVirtEnvType(), shareDelete.getName());

        log.info("msg id : [{}]", shareDelete.getMsgId());

        ShareDeleteResult shareDeleteResult = new ShareDeleteResult();
        try {
            shareDeleteResult = vmHandler.deleteShare(shareDelete);

            log.info("adaptor share :[{}] has been created successfully. {}",shareDelete.getName(),
                     shareDeleteResult.isSuccess() ? "" : "But " + shareDeleteResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareDeleteResult.setSuccess(false);
            shareDeleteResult.setErrCode(e.getErrCode());
            shareDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareDeleteResult.setSuccess(false);
            shareDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            shareDeleteResult.setSuccess(false);
            shareDeleteResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(shareDelete, shareDeleteResult);
        shareDeleteResult.setId(shareDelete.getId());
        shareDeleteResult.setOrgSid(shareDelete.getOrgSid());
        shareDeleteResult.setName(shareDelete.getName());
        return shareDeleteResult;
    }

    /**
     * Handle message vm clone result.
     *
     * @param vmClone the vm clone
     *
     * @return the vm clone result
     */
    public VmCloneResult handleMessage(VmClone vmClone) {

        log.info("receiving message for clone vm-, virtual type : [{}] vm name : [{}]" ,vmClone.getVirtEnvType(), vmClone.getName());

        log.info("msg id : [{}]", vmClone.getMsgId());

        VmCloneResult vmCloneResult = new VmCloneResult();

        try {
            vmCloneResult = vmHandler.cloneVm(vmClone);

            log.info("adaptor vm :[{}] has been clone successfully. {}",vmClone.getName(),
                     vmCloneResult.isSuccess() ? "" : "But " + vmCloneResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            vmCloneResult.setSuccess(false);
            vmCloneResult.setErrCode(e.getErrCode());
            vmCloneResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmCloneResult.setSuccess(false);
            vmCloneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmCloneResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            vmCloneResult.setSuccess(false);
            vmCloneResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmClone, vmCloneResult);
        vmCloneResult.setMsgId(vmClone.getMsgId());
        vmCloneResult.setVmName(vmClone.getName());
        vmCloneResult.setId(vmClone.getId());
        vmCloneResult.setSoftware(vmClone.getSoftware());
        vmCloneResult.setHostName(vmClone.getHostName());
        vmCloneResult.setSystemDiskLocation(vmClone.getSysDiskLocation());
        vmCloneResult.setPowerStatus(vmClone.getPowerStatus());
        vmCloneResult.setNetworkChange(vmClone.isNetworkChange());
        vmCloneResult.setOrignNics(vmClone.getNics());

        return vmCloneResult;
    }

    /**
     * Handle message vm query result.
     *
     * @param vmQuery the vm query
     *
     * @return the vm query result
     */
    public VmQueryResult handleMessage(VmQuery vmQuery) {

        log.info("receiving message for querying vms, virtual type : [{}]", vmQuery.getVirtEnvType());

        log.info("msg id : [{}]", vmQuery.getMsgId());

        VmQueryResult vmQueryResult = new VmQueryResult();
        try {
            vmQueryResult = vmHandler.queryVms(vmQuery);
        } catch (CommonAdapterException e) {

            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(e.getErrCode());
            vmQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmQueryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmQuery, vmQueryResult);
        vmQueryResult.setMsgId(vmQuery.getMsgId());
        return vmQueryResult;

    }

    /**
     * Handle message disk create result.
     *
     * @param diskCreate the disk create
     *
     * @return the disk create result
     */
    public DiskCreateResult handleMessage(DiskCreate diskCreate) {

        log.info("receiving message for creating disk, virtual type : [{}] disk name:[{}]" ,diskCreate.getVirtEnvType(), diskCreate.getName());

        log.info("msg id : [{}]", diskCreate.getMsgId());

        DiskCreateResult diskCreateResult = new DiskCreateResult();

        try {
            diskCreateResult = storageHandler.createDisk(diskCreate);
            diskCreateResult.setUuid(diskCreateResult.getVolumeId());
        } catch (CommonAdapterException e) {
            diskCreateResult.setSuccess(false);
            diskCreateResult.setErrCode(e.getErrCode());
            diskCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskCreateResult.setSuccess(false);
            diskCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskCreateResult.setErrMsg(e.getMessage());
        }
        if (diskCreateResult.isSuccess()) {
            log.info("[adaptor] disk :[{}] has been created successfully",diskCreate.getName());
        } else {
            log.info("[adaptor] disk :[{}] has been created failure",diskCreate.getName());
        }
        BaseUtil.setResult(diskCreate, diskCreateResult);
        diskCreateResult.setSid(diskCreate.getSid());
        diskCreateResult.setMsgId(diskCreate.getMsgId());
        diskCreateResult.setDescription(diskCreate.getDescription());
        diskCreateResult.setLabel(diskCreate.getLabel());
        diskCreateResult.setDatastore(diskCreate.getLocation());
        diskCreateResult.setName(diskCreate.getName());
        diskCreateResult.setSize(diskCreate.getSize());
        diskCreateResult.setVmName(diskCreate.getVmName());
        diskCreateResult.setPeriod(diskCreate.getPeriod());
        return diskCreateResult;

    }

    /**
     * Handle message disk delete result.
     *
     * @param diskDelete the disk delete
     *
     * @return the disk delete result
     */
    public DiskDeleteResult handleMessage(DiskDelete diskDelete) {

        log.info("receiving message for deleting disk, virtual type : [{}]", diskDelete.getVirtEnvType());

        log.info("msg id : [{}]", diskDelete.getMsgId());

        DiskDeleteResult diskDeleteResult = new DiskDeleteResult();
        try {
            diskDeleteResult = storageHandler.deleteDisk(diskDelete);
        } catch (CommonAdapterException e) {
            diskDeleteResult.setSuccess(false);
            diskDeleteResult.setErrCode(e.getErrCode());
            diskDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskDeleteResult.setSuccess(false);
            diskDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskDeleteResult.setSuccess(false);
            diskDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskDelete, diskDeleteResult);
        diskDeleteResult.setMsgId(diskDelete.getMsgId());
        diskDeleteResult.setDataStore(diskDelete.getDataStore());
        diskDeleteResult.setId(diskDelete.getId());
        diskDeleteResult.setVmName(diskDelete.getVmName());
        diskDeleteResult.setDiskType(diskDelete.getDiskType());
        diskDeleteResult.setSnapshotIds(diskDelete.getSnapshotIds());
        return diskDeleteResult;
    }

    public DiskUpdateResult handleMessage(DiskUpdate diskUpdate) {
        log.info("receiving message for expanding disk, virtual type : [{}]", diskUpdate.getVirtEnvType());

        log.info("msg id : [{}]", diskUpdate.getMsgId());

        DiskUpdateResult diskUpdateResult = new DiskUpdateResult();
        try {
            diskUpdateResult = storageHandler.updateDisk(diskUpdate);
        } catch (CommonAdapterException e) {
            diskUpdateResult.setSuccess(false);
            diskUpdateResult.setErrCode(e.getErrCode());
            diskUpdateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskUpdateResult.setSuccess(false);
            diskUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskUpdateResult.setSuccess(false);
            diskUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskUpdate, diskUpdateResult);

        return diskUpdateResult;
    }

    /**
     * Handle message disk expand result.
     *
     * @param diskExpand the disk expand
     *
     * @return the disk expand result
     */
    public DiskExpandResult handleMessage(DiskExpand diskExpand) {

        log.info("receiving message for expanding disk, virtual type : [{}]", diskExpand.getVirtEnvType());

        log.info("msg id : [{}]", diskExpand.getMsgId());

        DiskExpandResult diskExpandResult = new DiskExpandResult();

        try {
            diskExpandResult = storageHandler.expandDisk(diskExpand);
        } catch (CommonAdapterException e) {
            diskExpandResult.setSuccess(false);
            diskExpandResult.setErrCode(e.getErrCode());
            diskExpandResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskExpandResult.setSuccess(false);
            diskExpandResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskExpandResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskExpandResult.setSuccess(false);
            diskExpandResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskExpandResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskExpand, diskExpandResult);

        diskExpandResult.setName(diskExpand.getName());
        diskExpandResult.setVmName(diskExpand.getVmName());
        diskExpandResult.setSize(diskExpand.getSize());
        diskExpandResult.setStorage(diskExpand.getStorage());

        diskExpandResult.setMsgId(diskExpand.getMsgId());
        return diskExpandResult;
    }

    /**
     * Handle message vm reconfig result.
     *
     * @param vmReconfig the vm reconfig
     *
     * @return the vm reconfig result
     */
    public VmReconfigResult handleMessage(VmReconfig vmReconfig) {

        log.info("receiving message for reconfiging vm, msg id : [{}]", vmReconfig.getMsgId());

        VmReconfigResult vmReconfigResult = new VmReconfigResult();

        try {

            vmReconfigResult = vmHandler.reconfigVm(vmReconfig);
        } catch (CommonAdapterException e) {
            vmReconfigResult.setSuccess(false);
            vmReconfigResult.setErrCode(e.getErrCode());
            vmReconfigResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmReconfigResult.setSuccess(false);
            vmReconfigResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmReconfigResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmReconfigResult.setSuccess(false);
            vmReconfigResult.setErrCode("500");
            vmReconfigResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmReconfig, vmReconfigResult);
        BeanUtil.transformBeanObj(vmReconfig, vmReconfigResult);
        vmReconfigResult.setStatus(vmReconfig.getOptions().get("vmStatus").toString());
        return vmReconfigResult;

    }

    /**
     * Handle message vm remove result.
     *
     * @param vmRemove the vm remove
     *
     * @return the vm remove result
     */
    public VmRemoveResult handleMessage(VmRemove vmRemove) {

        log.info("receiving message for removing vm, msg id : [{}]", vmRemove.getMsgId());

        VmRemoveResult vmRemoveResult = new VmRemoveResult();

        try {
            vmRemoveResult = vmHandler.removeVm(vmRemove);
            log.info("adaptor vm :[{}] has been removed successfully",vmRemove.getVmName());
        } catch (CommonAdapterException e) {

            vmRemoveResult.setSuccess(false);
            vmRemoveResult.setErrCode(e.getErrCode());
            vmRemoveResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmRemoveResult.setSuccess(false);
            vmRemoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRemoveResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmRemoveResult.setSuccess(false);
            vmRemoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRemoveResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmRemove, vmRemoveResult);
        vmRemoveResult.setSid(vmRemove.getSid());
        vmRemoveResult.setMsgId(vmRemove.getMsgId());
        vmRemoveResult.setVmName(vmRemove.getVmName());
        vmRemoveResult.setId(vmRemove.getId());
        vmRemoveResult.setDueToFailOfCreating(vmRemove.isDueToFailOfCreating());
        vmRemoveResult.setHostName(vmRemove.getHostName());
        vmRemoveResult.setDeleteFloatingIp(vmRemove.isDeleteFloatingIp());

        return vmRemoveResult;

    }

    /**
     * Handle message vm snapshot create result.
     *
     * @param vmSnapshotCreate the vm snapshot create
     *
     * @return the vm snapshot create result
     */
    public VmSnapshotCreateResult handleMessage(VmSnapshotCreate vmSnapshotCreate) {

        log.info("receiving message for creating vm snapshot, virtual type : [{}] vm name : [{}]",vmSnapshotCreate.getVirtEnvType(), vmSnapshotCreate.getVmName());

        log.info("msg id : [{}]", vmSnapshotCreate.getMsgId());

        VmSnapshotCreateResult vmSnapshotCreateResult = new VmSnapshotCreateResult();
        try {
            vmSnapshotCreateResult = vmHandler.snapshotVm(vmSnapshotCreate);

        } catch (CommonAdapterException e) {

            vmSnapshotCreateResult.setSuccess(false);
            vmSnapshotCreateResult.setErrCode(e.getErrCode());
            vmSnapshotCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmSnapshotCreateResult.setSuccess(false);
            vmSnapshotCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            log.error(e.getMessage());
            vmSnapshotCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(vmSnapshotCreate, vmSnapshotCreateResult);

        vmSnapshotCreateResult.setMsgId(vmSnapshotCreate.getMsgId());
        vmSnapshotCreateResult.setVmName(vmSnapshotCreate.getVmName());
        vmSnapshotCreateResult.setSnapshotName(vmSnapshotCreate.getSnapshotName());
        vmSnapshotCreateResult.setSnapshotDesp(vmSnapshotCreate.getSnapshotDesp());
        vmSnapshotCreateResult.setSnapshotId(vmSnapshotCreate.getSnapshotId());
        vmSnapshotCreateResult.setPeriod(vmSnapshotCreate.getPeriod());
        return vmSnapshotCreateResult;

    }

    /**
     * Handle message vm snapshot revert result.
     *
     * @param vmSnapshotRevert the vm snapshot revert
     *
     * @return the vm snapshot revert result
     */
    public VmSnapshotRevertResult handleMessage(VmSnapshotRevert vmSnapshotRevert) {

        log.info("receiving message for reverting vm, virtual type : [{}] vm name : [{}]",vmSnapshotRevert.getVirtEnvType(), vmSnapshotRevert.getVmName());

        log.info("msg id : [{}]", vmSnapshotRevert.getMsgId());

        VmSnapshotRevertResult vmSnapshotRevertResult = new VmSnapshotRevertResult();
        try {
            vmSnapshotRevertResult = vmHandler.revertVm(vmSnapshotRevert);
        } catch (CommonAdapterException e) {

            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(e.getErrCode());
            vmSnapshotRevertResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotRevertResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotRevertResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmSnapshotRevert, vmSnapshotRevertResult);
        vmSnapshotRevertResult.setMsgId(vmSnapshotRevert.getMsgId());
        vmSnapshotRevertResult.setVmName(vmSnapshotRevert.getVmName());
        vmSnapshotRevertResult.setSnapshotName(vmSnapshotRevert.getSnapshotName());
        vmSnapshotRevertResult.setHostName(vmSnapshotRevert.getHostName());
        vmSnapshotRevertResult.setSnapshotId(vmSnapshotRevert.getSnapshotId());
        return vmSnapshotRevertResult;

    }

    /**更新快照信息**/
    public VmSnapshotUpdateResult handleMessage(VmSnapshotUpdate snapshotUpdate) {

        log.info("receiving message for update snapshot, virtual type : [{}]   name : [{}]",
                 snapshotUpdate.getVirtEnvType(), snapshotUpdate.getName());

        log.info("msg id : [{}]", snapshotUpdate.getMsgId());

        VmSnapshotUpdateResult vmSnapshotRevertResult = new VmSnapshotUpdateResult();
        try {
            vmSnapshotRevertResult = vmHandler.updateSnapshot(snapshotUpdate);
        } catch (CommonAdapterException e) {

            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(e.getErrCode());
            vmSnapshotRevertResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotRevertResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmSnapshotRevertResult.setSuccess(false);
            vmSnapshotRevertResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotRevertResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(snapshotUpdate, vmSnapshotRevertResult);
        vmSnapshotRevertResult.setMsgId(snapshotUpdate.getMsgId());
        vmSnapshotRevertResult.setName(snapshotUpdate.getName());
        vmSnapshotRevertResult.setDescription(snapshotUpdate.getDescription());
        vmSnapshotRevertResult.setSnapshotId(snapshotUpdate.getSnapshotId());
        return vmSnapshotRevertResult;

    }

    /**
     * Handle message vm nic add result.
     *
     * @param vmNicAdd the vm nic add
     *
     * @return the vm nic add result
     */
    public VmNicAddResult handleMessage(VmNicAdd vmNicAdd) {

        log.info("receiving message for adding vm nic, virtual type : [{}] vm name : [{}]",vmNicAdd.getVirtEnvType() , vmNicAdd.getVmName());

        log.info("msg id : [{}]", vmNicAdd.getMsgId());

        VmNicAddResult vmNicAddResult = new VmNicAddResult();

        try {
            vmHandler.addNic(vmNicAdd);
            vmNicAddResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            vmNicAddResult.setSuccess(false);
            vmNicAddResult.setErrCode(e.getErrCode());
            vmNicAddResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmNicAddResult.setSuccess(false);
            vmNicAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmNicAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmNicAddResult.setSuccess(false);
            vmNicAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmNicAddResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmNicAdd, vmNicAddResult);
        vmNicAddResult.setMsgId(vmNicAdd.getMsgId());
        vmNicAddResult.setVmName(vmNicAdd.getVmName());
        vmNicAddResult.setNics(vmNicAdd.getNics());
        return vmNicAddResult;

    }

    /**
     * Handle message vm nic delete result.
     *
     * @param vmNicDelete the vm nic delete
     *
     * @return the vm nic delete result
     */
    public VmNicDeleteResult handleMessage(VmNicDelete vmNicDelete) {

        log.info(
                "receiving message for deletinng vm nic, virtual type : [{}] vm name : [{}]",vmNicDelete.getVirtEnvType(), vmNicDelete.getVmName());

        log.info("msg id : [{}]", vmNicDelete.getMsgId());

        VmNicDeleteResult vmNicDeleteResult = BaseUtil.setResult(vmNicDelete, VmNicDeleteResult.class);
        vmNicDeleteResult.setMsgId(vmNicDelete.getMsgId());
        vmNicDeleteResult.setVmName(vmNicDelete.getVmName());
        vmNicDeleteResult.setNics(vmNicDelete.getNics());

        try {
            vmHandler.deleteNic(vmNicDelete);
            vmNicDeleteResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            vmNicDeleteResult.setSuccess(false);
            vmNicDeleteResult.setErrCode(e.getErrCode());
            vmNicDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmNicDeleteResult.setSuccess(false);
            vmNicDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmNicDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmNicDeleteResult.setSuccess(false);
            vmNicDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmNicDeleteResult.setErrMsg(e.getMessage());
        }

        return vmNicDeleteResult;

    }

    /**
     * Handle message vm migrate result.
     *
     * @param vmMigrate the vm migrate
     *
     * @return the vm migrate result
     */
    public VmMigrateResult handleMessage(VmMigrate vmMigrate) {

        log.info("receiving message for migrating vm, virtual type : [{}] vm name : [{}]",vmMigrate.getVirtEnvType(), vmMigrate.getVmName());

        log.info("msg id : [{}]", vmMigrate.getMsgId());

        VmMigrateResult vmMigrateResult = new VmMigrateResult();
        try {
            vmMigrateResult = vmHandler.migrateVm(vmMigrate);
        } catch (CommonAdapterException e) {

            vmMigrateResult.setSuccess(false);
            vmMigrateResult.setErrCode(e.getErrCode());
            vmMigrateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmMigrateResult.setSuccess(false);
            vmMigrateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmMigrateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmMigrateResult.setSuccess(false);
            vmMigrateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmMigrateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmMigrate, vmMigrateResult);
        vmMigrateResult.setMsgId(vmMigrate.getMsgId());
        vmMigrateResult.setVmName(vmMigrate.getVmName());
        vmMigrateResult.setVmId(vmMigrate.getVmId());
        vmMigrateResult.setMigrateType(vmMigrate.getMigrateType());
        return vmMigrateResult;

    }

    /**
     * Handle message vm rebuild result.
     *
     * @param vmRebuild the vm rebuild
     *
     * @return the vm rebuild result
     */
    public VmRebuildResult handleMessage(VmRebuild vmRebuild) {
        log.info("receiving message for vmRebuilding vm, virtual type : [{}] Image name : [{}]",vmRebuild.getVirtEnvType(),vmRebuild.getImageId());

        log.info("msg id : [{}]", vmRebuild.getMsgId());

        VmRebuildResult vmRebuildResult = new VmRebuildResult();

        try {
            vmRebuildResult = vmHandler.rebuildVm(vmRebuild);
        } catch (CommonAdapterException e) {

            vmRebuildResult.setErrCode(e.getErrCode());
            vmRebuildResult.setErrMsg(e.getErrMsg());
            vmRebuildResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {

            vmRebuildResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRebuildResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            vmRebuildResult.setSuccess(false);

        } catch (Exception e) {

            log.error(e.getMessage());
            vmRebuildResult.setSuccess(false);
        }
        BaseUtil.setResult(vmRebuild, vmRebuildResult);
        vmRebuildResult.setMsgId(vmRebuild.getMsgId());
        vmRebuildResult.setId(vmRebuild.getId());
        return vmRebuildResult;
    }

    /**
     * Handle message vm resize result.
     *
     * @param vmResize the vm resize
     *
     * @return the vm resize result
     */
    public VmResizeResult handleMessage(VmResize vmResize) {
        log.info("receiving message for vmResizing vm, virtual type : [{}] vm Id : [{}]" ,vmResize.getVirtEnvType(), vmResize.getId());

        log.info("msg id : [{}]", vmResize.getMsgId());
        VmResizeResult vmResizeResult = BaseUtil.setResult(vmResize, VmResizeResult.class);
        vmResizeResult.setMsgId(vmResize.getMsgId());

        try {
            vmHandler.resizeVm(vmResize);
            vmResizeResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            vmResizeResult.setSuccess(false);
            vmResizeResult.setErrCode(e.getErrCode());
            vmResizeResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmResizeResult.setSuccess(false);
            vmResizeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmResizeResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmResizeResult.setSuccess(false);
            vmResizeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmResizeResult.setErrMsg(e.getMessage());
        }
        return vmResizeResult;
    }

    /**
     * Handle message vm recovery result.
     *
     * @param vmRecovery the vm recovery
     *
     * @return the vm recovery result
     */
    public VmRecoveryResult handleMessage(VmRecovery vmRecovery) {
        log.info("receiving message for vmRecoverying vm, virtual type : [{}] vm Id : [{}]" ,vmRecovery.getVirtEnvType(), vmRecovery.getId());

        log.info("msg id : [{}]", vmRecovery.getMsgId());

        VmRecoveryResult vmRecoveryResult = new VmRecoveryResult();

        try {
            vmRecoveryResult = vmHandler.recoveryVm(vmRecovery);
        } catch (CommonAdapterException e) {

            vmRecoveryResult.setErrCode(e.getErrCode());
            vmRecoveryResult.setErrMsg(e.getErrMsg());
            vmRecoveryResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {

            vmRecoveryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRecoveryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            vmRecoveryResult.setSuccess(false);

        } catch (Exception e) {
            vmRecoveryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRecoveryResult.setErrMsg(e.getMessage());
            vmRecoveryResult.setSuccess(false);

        }
        BaseUtil.setResult(vmRecovery, vmRecoveryResult);
        vmRecoveryResult.setMsgId(vmRecovery.getMsgId());
        return vmRecoveryResult;
    }

    /**
     * Handle message vm image create result.
     *
     * @param vmImageCreate the vm image create
     *
     * @return the vm image create result
     */
    public VmImageCreateResult handleMessage(VmImageCreate vmImageCreate) {

        log.info("receiving message for vmImageCreating vm, virtual type : [{}] image name: [{}]",vmImageCreate.getVirtEnvType(), vmImageCreate.getImageName());

        log.info("msg id : [{}]", vmImageCreate.getMsgId());

        VmImageCreateResult vmImageCreateResult = new VmImageCreateResult();

        try {
            vmImageCreateResult = vmHandler.imageVm(vmImageCreate);
            vmImageCreateResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            vmImageCreateResult.setSuccess(false);
            vmImageCreateResult.setErrCode(e.getErrCode());
            vmImageCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmImageCreateResult.setSuccess(false);
            vmImageCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmImageCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmImageCreateResult.setSuccess(false);
            vmImageCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmImageCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmImageCreate, vmImageCreateResult);
        vmImageCreateResult.setMsgId(vmImageCreate.getMsgId());
        vmImageCreateResult.setSnapshotUuid(vmImageCreate.getSnapshotUuid());
        vmImageCreateResult.setId(vmImageCreate.getId());
        return vmImageCreateResult;
    }

    /**
     * Handle message block backup create result.
     *
     * @param backupCreate the backup create
     *
     * @return the block backup create result
     */
    public BlockBackupCreateResult handleMessage(BlockBackupCreate backupCreate) {

        log.info("receiving message for block backup creating, virtual type : [{}] backup name: [{}]" ,backupCreate.getVirtEnvType(), backupCreate.getName());

        log.info("msg id : [{}]", backupCreate.getMsgId());

        BlockBackupCreateResult backupCreateResult = new BlockBackupCreateResult();

        try {
            backupCreateResult = storageHandler.createBlockBackup(backupCreate);
            backupCreateResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            backupCreateResult.setSuccess(false);
            backupCreateResult.setErrCode(e.getErrCode());
            backupCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            backupCreateResult.setSuccess(false);
            backupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            backupCreateResult.setSuccess(false);
            backupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backupCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(backupCreate, backupCreateResult);
        backupCreateResult.setVolumeId(backupCreate.getVolumeId());
        backupCreateResult.setMsgId(backupCreate.getMsgId());
        backupCreateResult.setVolumeId(backupCreate.getVolumeId());
        return backupCreateResult;
    }

    /**
     * Handle message block snapshot recovry result.
     *
     * @param blockSnapshotRecovery the block snapshot recovery
     *
     * @return the block snapshot recovry result
     */
    public BlockSnapshotRecovryResult handleMessage(BlockSnapshotRecovery blockSnapshotRecovery) {

        log.info("receiving message for block backup recovring, virtual type : [{}] snapshot id: [{}]"
                ,blockSnapshotRecovery.getVirtEnvType(), blockSnapshotRecovery.getSnapshotId());

        log.info("msg id : [{}]", blockSnapshotRecovery.getMsgId());

        BlockSnapshotRecovryResult blockSnapshotRecovryResult = new BlockSnapshotRecovryResult();
        try {
            blockSnapshotRecovryResult = storageHandler.recoveryBlockSnapshot(blockSnapshotRecovery);

            blockSnapshotRecovryResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            blockSnapshotRecovryResult.setSuccess(false);
            blockSnapshotRecovryResult.setErrCode(e.getErrCode());
            blockSnapshotRecovryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            blockSnapshotRecovryResult.setSuccess(false);
            blockSnapshotRecovryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotRecovryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockSnapshotRecovryResult.setSuccess(false);
            blockSnapshotRecovryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotRecovryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blockSnapshotRecovery, blockSnapshotRecovryResult);
        blockSnapshotRecovryResult.setVolumeId(blockSnapshotRecovery.getVolumeId());
        blockSnapshotRecovryResult.setSnapshotId(blockSnapshotRecovery.getSnapshotId());
        blockSnapshotRecovryResult.setMsgId(blockSnapshotRecovery.getMsgId());
        blockSnapshotRecovryResult.setId(blockSnapshotRecovery.getId());
        return blockSnapshotRecovryResult;
    }

    /**
     * Handle message block backup recovery result.
     *
     * @param blockBackupRecovery the block backup recovery
     *
     * @return the block backup recovery result
     */
    public BlockBackupRecoveryResult handleMessage(BlockBackupRecovery blockBackupRecovery) {

        log.info("receiving message for block backup recovring, virtual type : [{}]"
                         ,blockBackupRecovery.getVirtEnvType());

        log.info("msg id : [{}]", blockBackupRecovery.getMsgId());

        BlockBackupRecoveryResult backupRecoveryResult = new BlockBackupRecoveryResult();
        try {
            backupRecoveryResult = storageHandler.recoveryBlockBachup(blockBackupRecovery);
            backupRecoveryResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            backupRecoveryResult.setSuccess(false);
            backupRecoveryResult.setErrCode(e.getErrCode());
            backupRecoveryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            backupRecoveryResult.setSuccess(false);
            backupRecoveryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backupRecoveryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            backupRecoveryResult.setSuccess(false);
            backupRecoveryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backupRecoveryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blockBackupRecovery, backupRecoveryResult);
        backupRecoveryResult.setVolumeId(blockBackupRecovery.getVolumeId());
        backupRecoveryResult.setBackupId(blockBackupRecovery.getBackupId());
        backupRecoveryResult.setMsgId(blockBackupRecovery.getMsgId());
        return backupRecoveryResult;
    }

    /**
     * Handle message block clone result.
     *
     * @param blockClone the block clone
     *
     * @return the block clone result
     */
    public BlockCloneResult handleMessage(BlockClone blockClone) {

        log.info("receiving message for block cloning, virtual type : [{}]", blockClone.getVirtEnvType());

        log.info("msg id : [{}]", blockClone.getMsgId());

        BlockCloneResult blockCloneResult = BaseUtil.setResult(blockClone, BlockCloneResult.class);
        blockCloneResult.setMsgId(blockClone.getMsgId());

        try {
            blockCloneResult = storageHandler.cloneBlock(blockClone);
            blockCloneResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            blockCloneResult.setSuccess(false);
            blockCloneResult.setErrCode(e.getErrCode());
            blockCloneResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            blockCloneResult.setSuccess(false);
            blockCloneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockCloneResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockCloneResult.setSuccess(false);
            blockCloneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockCloneResult.setErrMsg(e.getMessage());

        }
        return blockCloneResult;
    }

    /**
     * Handle message net create result.
     *
     * @param netCreate the net create
     *
     * @return the net create result
     */
    public NetCreateResult handleMessage(NetCreate netCreate) {

        log.info("receiving message for creating network, virtual type : [{}] network name : [{}]",netCreate.getVirtEnvType(), netCreate.getName());

        log.info("msg id : [{}]", netCreate.getMsgId());
        NetCreateResult netCreateResult = new NetCreateResult();
        try {
            netCreateResult = netHandler.createNet(netCreate);

        } catch (CommonAdapterException e) {

            netCreateResult.setSuccess(false);
            netCreateResult.setErrCode(e.getErrCode());
            netCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            netCreateResult.setSuccess(false);
            netCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            netCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            netCreateResult.setSuccess(false);
            netCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            netCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(netCreate, netCreateResult);
        netCreateResult.setMsgId(netCreate.getMsgId());
        netCreateResult.setResId(netCreate.getResId());
        netCreateResult.setOptions(netCreate.getOptions());
        return netCreateResult;
    }

    /**
     * Handle message base result.
     *
     * @param routerCreate the router create
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterCreate routerCreate) {
        log.info("receiving message for create router, virtual type : [{}]", routerCreate.getVirtEnvType());

        log.info("msg id : [{}]", routerCreate.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.createRouter(routerCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerCreate, result);
        result.setMsgId(routerCreate.getMsgId());
        result.setOptions(routerCreate.getOptions());
        return result;
    }


    /**
     * Handle message net delete result.
     *
     * @param netDelete the net delete
     *
     * @return the net delete result
     */
    public NetDeleteResult handleMessage(NetDelete netDelete) {

        log.info("receiving message for deleting network, virtual type : [{}] network id : [{}]",netDelete.getVirtEnvType(), netDelete.getNetId());

        log.info("msg id : [{}]", netDelete.getMsgId());
        NetDeleteResult deleteResult = new NetDeleteResult();
        try {
            deleteResult = netHandler.deleteNet(netDelete);
        } catch (CommonAdapterException e) {

            deleteResult.setSuccess(false);
            deleteResult.setErrCode(e.getErrCode());
            deleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            deleteResult.setSuccess(false);
            deleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            deleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            deleteResult.setSuccess(false);
            deleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            deleteResult.setErrMsg(e.getMessage());

        }

        BaseUtil.setResult(netDelete, deleteResult);
        deleteResult.setMsgId(netDelete.getMsgId());
        deleteResult.setNetId(netDelete.getNetId());
        deleteResult.setOptions(netDelete.getOptions());
        return deleteResult;
    }

    /**
     * @param portDelete
     *
     * @description Handle message port delete result
     */
    public PortDeleteResult handleMessage(PortDelete portDelete) {
        log.info("receiving message for deleting port, virtual type : [{}] port id : [{}]",portDelete.getVirtEnvType(), portDelete.getPortId());

        log.info("msg id : [{}]", portDelete.getMsgId());
        PortDeleteResult deleteResult = new PortDeleteResult();
        try {
            deleteResult = netHandler.deletePort(portDelete);
        } catch (CommonAdapterException e) {

            deleteResult.setSuccess(false);
            deleteResult.setErrCode(e.getErrCode());
            deleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            deleteResult.setSuccess(false);
            deleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            deleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            deleteResult.setSuccess(false);
            deleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            deleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portDelete, deleteResult);
        deleteResult.setMsgId(portDelete.getMsgId());
        deleteResult.setVpcId(portDelete.getVpcId());
        deleteResult.setOptions(portDelete.getOptions());
        return deleteResult;
    }

    /**
     * 创建端口组
     * @param portGroupCreate
     * @return
     */
    public PortGroupCreateResult handleMessage(PortGroupCreate portGroupCreate) {
        log.info("receiving message for deleting port, virtual type : [{}]", portGroupCreate.getVirtEnvType());

        log.info("msg id : [{}]", portGroupCreate.getMsgId());
        PortGroupCreateResult portGroupCreateResult = new PortGroupCreateResult();
        try {
            portGroupCreateResult = netHandler.createPortGroup(portGroupCreate);
        } catch (CommonAdapterException e) {

            portGroupCreateResult.setSuccess(false);
            portGroupCreateResult.setErrCode(e.getErrCode());
            portGroupCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            portGroupCreateResult.setSuccess(false);
            portGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            portGroupCreateResult.setSuccess(false);
            portGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portGroupCreate, portGroupCreateResult);
        portGroupCreateResult.setMsgId(portGroupCreate.getMsgId());
        portGroupCreateResult.setOptions(portGroupCreate.getOptions());
        return portGroupCreateResult;
    }

    /**
     * 更新端口组
     * @param portGroupUpdate
     * @return
     */
    public PortGroupUpdateResult handleMessage(PortGroupUpdate portGroupUpdate) {
        log.info("receiving message for deleting port, virtual type : [{}]", portGroupUpdate.getVirtEnvType());

        log.info("msg id : [{}]", portGroupUpdate.getMsgId());
        PortGroupUpdateResult portGroupUpdateResult = new PortGroupUpdateResult();
        try {
            portGroupUpdateResult = netHandler.updatePortGroup(portGroupUpdate);
        } catch (CommonAdapterException e) {

            portGroupUpdateResult.setSuccess(false);
            portGroupUpdateResult.setErrCode(e.getErrCode());
            portGroupUpdateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            portGroupUpdateResult.setSuccess(false);
            portGroupUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            portGroupUpdateResult.setSuccess(false);
            portGroupUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portGroupUpdate, portGroupUpdateResult);
        portGroupUpdateResult.setMsgId(portGroupUpdate.getMsgId());
        portGroupUpdateResult.setOptions(portGroupUpdate.getOptions());
        return portGroupUpdateResult;
    }

    /**
     * 创建端口组
     * @param portGroupVmsByEnv
     * @return
     */
    public ScanVmsByEnvResult handleMessage(PortGroupVmsByEnv portGroupVmsByEnv) {
        log.info("receiving message for deleting port, virtual type : [{}]", portGroupVmsByEnv.getVirtEnvType());

        log.info("msg id : [{}]", portGroupVmsByEnv.getMsgId());
        ScanVmsByEnvResult scanVmsByEnvResult = new ScanVmsByEnvResult();
        try {
            scanVmsByEnvResult = netHandler.getPortGroupVms(portGroupVmsByEnv);
        } catch (CommonAdapterException e) {

            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(e.getErrCode());
            scanVmsByEnvResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanVmsByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanVmsByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portGroupVmsByEnv, scanVmsByEnvResult);
        scanVmsByEnvResult.setMsgId(portGroupVmsByEnv.getMsgId());
        scanVmsByEnvResult.setOptions(portGroupVmsByEnv.getOptions());
        return scanVmsByEnvResult;
    }

    /**
     * 删除端口组
     * @param portGroupDelete
     * @return
     */
    public PortGroupDeleteResult handleMessage(PortGroupDelete portGroupDelete) {
        log.info("receiving message for deleting port, virtual type : [{}]", portGroupDelete.getVirtEnvType());

        log.info("msg id : [{}]", portGroupDelete.getMsgId());
        PortGroupDeleteResult portGroupDeleteResult = new PortGroupDeleteResult();
        try {
            portGroupDeleteResult = netHandler.deletePortGroup(portGroupDelete);
        } catch (CommonAdapterException e) {

            portGroupDeleteResult.setSuccess(false);
            portGroupDeleteResult.setErrCode(e.getErrCode());
            portGroupDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            portGroupDeleteResult.setSuccess(false);
            portGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            portGroupDeleteResult.setSuccess(false);
            portGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            portGroupDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portGroupDelete, portGroupDeleteResult);
        portGroupDeleteResult.setMsgId(portGroupDelete.getMsgId());
        portGroupDeleteResult.setOptions(portGroupDelete.getOptions());
        return portGroupDeleteResult;
    }

    /**
     * 创建VLAN池
     * @param vlanPoolCreate
     * @return
     */
    public VlanPoolCreateResult handleMessage(VlanPoolCreate vlanPoolCreate) {
        log.info("receiving message for deleting port, virtual type : [{}]", vlanPoolCreate.getVirtEnvType());

        log.info("msg id : [{}]", vlanPoolCreate.getMsgId());
        VlanPoolCreateResult vlanPoolCreateResult = new VlanPoolCreateResult();
        try {
            vlanPoolCreateResult = netHandler.createVlanPool(vlanPoolCreate);
        } catch (CommonAdapterException e) {

            vlanPoolCreateResult.setSuccess(false);
            vlanPoolCreateResult.setErrCode(e.getErrCode());
            vlanPoolCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            vlanPoolCreateResult.setSuccess(false);
            vlanPoolCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vlanPoolCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            vlanPoolCreateResult.setSuccess(false);
            vlanPoolCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vlanPoolCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vlanPoolCreate, vlanPoolCreateResult);
        vlanPoolCreateResult.setMsgId(vlanPoolCreate.getMsgId());
        vlanPoolCreateResult.setOptions(vlanPoolCreate.getOptions());
        return vlanPoolCreateResult;
    }

    /**
     * 删除VLAN池
     * @param vlanPoolDelete
     * @return
     */
    public VlanPoolDeleteResult handleMessage(VlanPoolDelete vlanPoolDelete) {
        log.info("receiving message for deleting port, virtual type : [{}]", vlanPoolDelete.getVirtEnvType());

        log.info("msg id : [{}]", vlanPoolDelete.getMsgId());
        VlanPoolDeleteResult vlanPoolDeleteResult = new VlanPoolDeleteResult();
        try {
            vlanPoolDeleteResult = netHandler.deleteVlanPool(vlanPoolDelete);
        } catch (CommonAdapterException e) {

            vlanPoolDeleteResult.setSuccess(false);
            vlanPoolDeleteResult.setErrCode(e.getErrCode());
            vlanPoolDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            vlanPoolDeleteResult.setSuccess(false);
            vlanPoolDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vlanPoolDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            vlanPoolDeleteResult.setSuccess(false);
            vlanPoolDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vlanPoolDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vlanPoolDelete, vlanPoolDeleteResult);
        vlanPoolDeleteResult.setMsgId(vlanPoolDelete.getMsgId());
        vlanPoolDeleteResult.setOptions(vlanPoolDelete.getOptions());
        return vlanPoolDeleteResult;
    }

    /**
     * Handle message vm snapshot delete result.
     *
     * @param vmSnapshotDelete the vm snapshot delete
     *
     * @return the vm snapshot delete result
     */
    public VmSnapshotDeleteResult handleMessage(VmSnapshotDelete vmSnapshotDelete) {

        log.info("receiving message for deleting vmSnapshot, virtual type : [{}]"
                         ,vmSnapshotDelete.getVirtEnvType());

        log.info("msg id : [{}]", vmSnapshotDelete.getMsgId());
        VmSnapshotDeleteResult vmSnapshotDeleteResult = new VmSnapshotDeleteResult();
        try {
            vmSnapshotDeleteResult = vmHandler.deleteVmSnapshot(vmSnapshotDelete);
        } catch (CommonAdapterException e) {

            vmSnapshotDeleteResult.setSuccess(false);
            vmSnapshotDeleteResult.setErrCode(e.getErrCode());
            vmSnapshotDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmSnapshotDeleteResult.setSuccess(false);
            vmSnapshotDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmSnapshotDeleteResult.setSuccess(false);
            vmSnapshotDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmSnapshotDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmSnapshotDelete, vmSnapshotDeleteResult);
        vmSnapshotDeleteResult.setMsgId(vmSnapshotDelete.getMsgId());
        vmSnapshotDeleteResult.setServerId(vmSnapshotDelete.getServerId());
        vmSnapshotDeleteResult.setImageId(vmSnapshotDelete.getImageId());
        vmSnapshotDeleteResult.setResId(vmSnapshotDelete.getResId());
        vmSnapshotDeleteResult.setSnapshotId(vmSnapshotDelete.getSnapshotId());
        return vmSnapshotDeleteResult;
    }


    /**
     * Handle message data store create result.
     *
     * @param dataStoreCreate the data store create
     *
     * @return the data store create result
     */
    public DataStoreCreateResult handleMessage(DataStoreCreate dataStoreCreate) {
        log.info("receiving message for datastore creating, virtual type : [{}]"
                         ,dataStoreCreate.getVirtEnvType());

        log.info("msg id : [{}]", dataStoreCreate.getMsgId());

        DataStoreCreateResult result = new DataStoreCreateResult();

        try {
            result = storageHandler.createDataStore(dataStoreCreate);
            log.info("创建存储 [{}] 完成！",dataStoreCreate.getDatastoreName());
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dataStoreCreate, result);
        result.setDatastoreName(dataStoreCreate.getDatastoreName());
        result.setSid(dataStoreCreate.getSid());
        return result;
    }

    /**
     * Handle message data store delete result.
     *
     * @param delete the delete
     *
     * @return the data store delete result
     */
    public DataStoreDeleteResult handleMessage(DataStoreDelete delete) {
        log.info("receiving message for datastore creating, virtual type : [{}]", delete.getVirtEnvType());

        log.info("msg id : [{}]", delete.getMsgId());

        DataStoreDeleteResult result = new DataStoreDeleteResult();
        try {
            result = storageHandler.deleteDataStore(delete);
            log.info("删除存储 [{}] 完成！",delete.getDatastoreName());
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(delete, result);
        result.setDatastoreName(delete.getDatastoreName());
        result.setSid(delete.getSid());
        return result;
    }

    /**
     * Handle message data store extend result.
     *
     * @param extend the extend
     *
     * @return the data store extend result
     */
    public DataStoreExtendResult handleMessage(DataStoreExtend extend) {

        log.info("receiving message for datastore creating, virtual type : [{}]", extend.getVirtEnvType());

        log.info("msg id : [{}]", extend.getMsgId());

        DataStoreExtendResult result = new DataStoreExtendResult();

        try {
            result = storageHandler.extendDataStore(extend);
            log.info("扩容存储 [{}] 完成！",extend.getDatastoreName());
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(extend, result);
        result.setDatastoreName(extend.getDatastoreName());
        result.setNewSize(extend.getNewSize());
        result.setSid(extend.getSid());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param connectStringApply the connect string apply
     *
     * @return the base result
     */
    public BaseResult handleMessage(ConnectStringApply connectStringApply) {

        log.info("receiving message for apply connectString, virtual type : [{}]"
                , connectStringApply.getVirtEnvType());

        log.info("msg id : [{}]", connectStringApply.getMsgId());

        ConnectStringResult result = new ConnectStringResult();

        try {
            result = rdsHandler.applyConnectString(connectStringApply);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(connectStringApply, result);

        return result;
    }

    /**
     * release connectString
     **/
    public BaseResult handleMessage(ConnectStringRelease connectStringRelease) {

        log.info("receiving message for release connectString, virtual type : [{}]"
                , connectStringRelease.getVirtEnvType());

        log.info("msg id : [{}]", connectStringRelease.getMsgId());

        ConnectStringResult result = new ConnectStringResult();

        try {
            result = rdsHandler.releaseConnectString(connectStringRelease);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(connectStringRelease, result);

        return result;
    }

    /**
     * update connectString
     **/
    public BaseResult handleMessage(ConnectStringUpdate connectStringUpdate) {

        log.info("receiving message for update connectString, virtual type : [{}]"
                , connectStringUpdate.getVirtEnvType());

        log.info("msg id : [{}]", connectStringUpdate.getMsgId());

        ConnectStringUpdateResult result = new ConnectStringUpdateResult();

        try {
            result = rdsHandler.updateConnectString(connectStringUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(connectStringUpdate, result);
        result.setOldInsStatus(connectStringUpdate.getOldInsStatus());

        return result;
    }

    /**
     * Handle message base result.
     *
     * @param dbInstanceCreate the db instance create
     *
     * @return the base result
     */
    public BaseResult handleMessage(DBInstanceCreate dbInstanceCreate) {

        log.info("receiving message for create rdsDBInstance, virtual type : [{}]", dbInstanceCreate.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceCreate.getMsgId());

        DBInstanceCreateResult result = new DBInstanceCreateResult();
        try {
            result = rdsHandler.createRdsDBInstance(dbInstanceCreate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceCreate, result);
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param dbInstanceRestart the db instance restart
     *
     * @return the base result
     */
    public BaseResult handleMessage(DBInstanceRestart dbInstanceRestart) {

        log.info("receiving message for restart rdsDBInstance, virtual type : [{}]",
                         dbInstanceRestart.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceRestart.getMsgId());

        DBInstanceRestartResult result = BaseUtil.setResult(dbInstanceRestart, DBInstanceRestartResult.class);
        try {
            result = rdsHandler.restartRdsDBInstance(dbInstanceRestart);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceRestart, result);
        return result;
    }

    public BaseResult handleMessage(DBInstanceEnlargeVolume dbInstanceEnlargeVolume) {

        log.info("receiving message for enlargeVolume rdsDBInstance, virtual type : [{}]",
                         dbInstanceEnlargeVolume.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceEnlargeVolume.getMsgId());

        DBInstanceEnlargeVolumeResult result = BaseUtil.setResult(dbInstanceEnlargeVolume,
                                                                  DBInstanceEnlargeVolumeResult.class);
        try {
            result = rdsHandler.enlargeVolumeRdsDBInstance(dbInstanceEnlargeVolume);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceEnlargeVolume, result);
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param dbInstanceRelease the db instance release
     *
     * @return the base result
     */
    public BaseResult handleMessage(DBInstanceRelease dbInstanceRelease) {

        log.info("receiving message for release rdsDBInstance, virtual type : [{}]",
                         dbInstanceRelease.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceRelease.getMsgId());

        DBInstanceReleaseResult result = new DBInstanceReleaseResult();
        try {
            result = rdsHandler.releaseRdsDBInstance(dbInstanceRelease);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceRelease, result);
        return result;
    }

    public BaseResult handleMessage(DBInstanceFloatingIPAction ipAction) {

        log.info("receiving message for bind/unbind rdsDBInstance floatingip, virtual type : [{}]",
                 ipAction.getVirtEnvType());

        log.info("msg id : [{}]", ipAction.getMsgId());

        DBInstanceFloatingIPActionResult result = new DBInstanceFloatingIPActionResult();
        try {
            result = rdsHandler.updateRdsInstanceFloatingip(ipAction);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(ipAction, result);
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param dbInstanceDescribe the db instance describe
     *
     * @return the base result
     */
    public BaseResult handleMessage(DBInstanceDescribe dbInstanceDescribe) {

        log.info("receiving message for describe rdsDBInstance, virtual type : [{}]",
                 dbInstanceDescribe.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceDescribe.getMsgId());

        DBInstanceDescribeResult result = new DBInstanceDescribeResult();
        try {
            result = rdsHandler.describeRdsDBInstance(dbInstanceDescribe);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceDescribe, result);
        return result;
    }

    public BaseResult handleMessage(DBInstanceReconfig dbInstanceReconfig) {

        log.info("receiving message for reconfig rdsDBInstance, virtual type : [{}]",
                 dbInstanceReconfig.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceReconfig.getMsgId());

        DBInstanceReconfigResult result = new DBInstanceReconfigResult();
        try {
            result = rdsHandler.reconfigRdsInstance(dbInstanceReconfig);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceReconfig, result);
        return result;
    }

    public BaseResult handleMessage(DBInstancePortUpdate dbInstancePortUpdate) {

        log.info("receiving message for update rdsDBInstance port, virtual type : [{}]",
                 dbInstancePortUpdate.getVirtEnvType());

        log.info("msg id : [{}]", dbInstancePortUpdate.getMsgId());

        DBInstancePortUpdateResult result = new DBInstancePortUpdateResult();
        try {
            result = rdsHandler.updateRdsInstancePort(dbInstancePortUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstancePortUpdate, result);
        return result;
    }

    public BaseResult handleMessage(DBInstanceFailover dbInstanceFailover) {

        log.info("receiving message for failover rdsDBInstance, virtual type : [{}]",
                 dbInstanceFailover.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceFailover.getMsgId());

        DBInstanceFailoverResult result = new DBInstanceFailoverResult();
        try {
            result = rdsHandler.failoverRdsInstance(dbInstanceFailover);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);

        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(dbInstanceFailover, result);
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param cloneVmAsTemplate the db instance describe
     *
     * @return the base result
     */
    public VmTemplateResult handleMessage(CloneVmAsTemplate cloneVmAsTemplate) {

        log.info("receiving message for Clone Vm As Template, virtual type : [{}]",
                         cloneVmAsTemplate.getVirtEnvType());

        log.info("msg id : [{}]", cloneVmAsTemplate.getMsgId());

        VmTemplateResult vmTemplateResult = new VmTemplateResult();
        try {
            vmTemplateResult = vmHandler.cloneVmAsTemplate(cloneVmAsTemplate);
        } catch (CommonAdapterException e) {
            vmTemplateResult.setErrCode(e.getErrCode());
            vmTemplateResult.setErrMsg(e.getErrMsg());
            vmTemplateResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            vmTemplateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmTemplateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            vmTemplateResult.setSuccess(false);

        } catch (Exception e) {
            vmTemplateResult.setErrCode("500");
            vmTemplateResult.setErrMsg(e.getMessage());
            vmTemplateResult.setSuccess(false);
        }
        BaseUtil.setResult(cloneVmAsTemplate, vmTemplateResult);
        vmTemplateResult.setOptions(cloneVmAsTemplate.getOptions());
        return vmTemplateResult;
    }

    public BaseResult handleMessage(PortCreate portCreate) {
        log.info("receiving message for create port, virtual type : [{}]", portCreate.getVirtEnvType());
        log.info("msg id : [{}]", portCreate.getMsgId());

        PortCreateResult result = new PortCreateResult();
        try {
            result = netHandler.createPort(portCreate);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(portCreate, result);
        return result;
    }

    public BaseResult handleMessage(PortUpdate portUpdate) {
        log.info("receiving message for create port, virtual type : [{}]", portUpdate.getVirtEnvType());
        log.info("msg id : [{}]", portUpdate.getMsgId());

        PortUpdateResult result = new PortUpdateResult();
        try {
            result = netHandler.updatePort(portUpdate);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(portUpdate, result);
        return result;
    }

    public BaseResult handleMessage(PortAttachInstance portAttach) {
        log.info("receiving message for attach port to instance, virtual type : [{}]", portAttach.getVirtEnvType());
        log.info("msg id : [{}]", portAttach.getMsgId());

        PortAttachInstanceResult result = new PortAttachInstanceResult();
        try {
            result = netHandler.attachPortToInstance(portAttach);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(portAttach, result);
        result.setPortName(portAttach.getPortName());
        result.setPortId(portAttach.getPortId());
        result.setId(portAttach.getId());
        result.setMsgId(portAttach.getMsgId());
        result.setInstanceId(portAttach.getInstanceId());
        return result;
    }

    public BaseResult handleMessage(PortDetachInstance portDetach) {
        log.info("receiving message for detach port from instance, virtual type : [{}]", portDetach.getVirtEnvType());
        log.info("msg id : [{}]", portDetach.getMsgId());

        PortDetachInstanceResult result = new PortDetachInstanceResult();
        try {
            result = netHandler.detachPortToInstance(portDetach);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(portDetach, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallRuleCreate fireWallRuleCreate) {
        log.info("receiving message for create firewall rule from fireWallRuleCreate, virtual type : [{}]", fireWallRuleCreate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallRuleCreate.getMsgId());

        VpcFireWallRuleResult result = new VpcFireWallRuleResult();
        try {
            result = netHandler.createVpcFireWallRule(fireWallRuleCreate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallRuleCreate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallRuleUpdate fireWallRuleUpdate) {
        log.info("receiving message for update firewall rule from fireWallRuleUpdate ,virtual type : [{}]", fireWallRuleUpdate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallRuleUpdate.getMsgId());

        VpcFireWallRuleResult result = new VpcFireWallRuleResult();
        try {
            result = netHandler.updateVpcFireWallRule(fireWallRuleUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallRuleUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallRuleDelete fireWallRuleDelete) {
        log.info("receiving message for delete firewall rule from fireWallRuleDelete, virtual type : [{}]", fireWallRuleDelete.getVirtEnvType());
        log.info("msg id : [{}]", fireWallRuleDelete.getMsgId());

        VpcFireWallRuleResult result = new VpcFireWallRuleResult();
        try {
            result = netHandler.deleteVpcFireWallRule(fireWallRuleDelete);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallRuleDelete, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallRuleMove fireWallRuleMove) {
        log.info("receiving message for delete firewall rule from fireWallRuleMove, virtual type : [{}]", fireWallRuleMove.getVirtEnvType());
        log.info("msg id : [{}]", fireWallRuleMove.getMsgId());

        VpcFireWallRuleResult result = new VpcFireWallRuleResult();
        try {
            result = netHandler.moveVpcFireWallRule(fireWallRuleMove);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallRuleMove, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallStrategyCreate fireWallStrategyCreate) {
        log.info("receiving message for create firewall strategy from fireWallStrategyCreate, virtual type : [{}]", fireWallStrategyCreate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallStrategyCreate.getMsgId());

        VpcFireWallStrategyResult result = new VpcFireWallStrategyResult();
        try {
            result = netHandler.createVpcFireWallStrategy(fireWallStrategyCreate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallStrategyCreate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallStrategyUpdate fireWallStrategyUpdate) {
        log.info("receiving message for update firewall strategy from fireWallStrategyUpdate, virtual type : [{}]", fireWallStrategyUpdate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallStrategyUpdate.getMsgId());

        VpcFireWallStrategyResult result = new VpcFireWallStrategyResult();
        try {
            result = netHandler.updateVpcFireWallStrategy(fireWallStrategyUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallStrategyUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallStrategyDelete fireWallStrategyDelete) {
        log.info("receiving message for delete firewall strategy from fireWallStrategyDelete, virtual type : [{}]", fireWallStrategyDelete.getVirtEnvType());
        log.info("msg id : [{}]", fireWallStrategyDelete.getMsgId());

        VpcFireWallStrategyResult result = new VpcFireWallStrategyResult();
        try {
            result = netHandler.deleteVpcFireWallStrategy(fireWallStrategyDelete);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallStrategyDelete, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallCreate fireWallCreate) {
        log.info("receiving message for create firewall from fireWallCreate, virtual type : [{}]", fireWallCreate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallCreate.getMsgId());

        VpcFireWallResult result = new VpcFireWallResult();
        try {
            result = netHandler.createVpcFireWall(fireWallCreate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallCreate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallUpdate fireWallUpdate) {
        log.info("receiving message for update firewall from fireWallUpdate, virtual type : [{}]", fireWallUpdate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallUpdate.getMsgId());

        VpcFireWallResult result = new VpcFireWallResult();
        try {
            result = netHandler.updateVpcFireWall(fireWallUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallDelete fireWallDelete) {
        log.info("receiving message for delete firewall from fireWallDelete, virtual type : [{}]", fireWallDelete.getVirtEnvType());
        log.info("msg id : [{}]", fireWallDelete.getMsgId());

        VpcFireWallResult result = new VpcFireWallResult();
        try {
            result = netHandler.deleteVpcFireWall(fireWallDelete);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallDelete, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallRouterRelUpdate fireWallRouterRelUpdate) {
        log.info("receiving message for update firewall from fireWallRouterRelUpdate, virtual type : [{}]", fireWallRouterRelUpdate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallRouterRelUpdate.getMsgId());

        VpcFireWallResult result = new VpcFireWallResult();
        try {
            result = netHandler.updateFireWallRouterRel(fireWallRouterRelUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallRouterRelUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallAntivirusPolicyCreate fireWallAntivirusPolicyCreate) {
        log.info("receiving message for create firewall from fireWallAntivirusPolicyCreate, virtual type : [{}]", fireWallAntivirusPolicyCreate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallAntivirusPolicyCreate.getMsgId());

        VpcFireWallAntivirusPolicyResult result = new VpcFireWallAntivirusPolicyResult();
        try {
            result = netHandler.createVpcFireWallAntivirusPolicy(fireWallAntivirusPolicyCreate);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallAntivirusPolicyCreate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallAntivirusPolicyDelete fireWallAntivirusPolicyDelete) {
        log.info("receiving message for create firewall from fireWallAntivirusPolicyDelete, virtual type : [{}]", fireWallAntivirusPolicyDelete.getVirtEnvType());
        log.info("msg id : [{}]", fireWallAntivirusPolicyDelete.getMsgId());

        VpcFireWallAntivirusPolicyResult result = new VpcFireWallAntivirusPolicyResult();
        try {
            result = netHandler.deleteVpcFireWallAntivirusPolicy(fireWallAntivirusPolicyDelete);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallAntivirusPolicyDelete, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallAntivirusPolicyUpdate fireWallAntivirusPolicyUpdate) {
        log.info("receiving message for create firewall from fireWallAntivirusPolicyUpdate, virtual type : [{}]", fireWallAntivirusPolicyUpdate.getVirtEnvType());
        log.info("msg id : [{}]", fireWallAntivirusPolicyUpdate.getMsgId());

        VpcFireWallAntivirusPolicyResult result = new VpcFireWallAntivirusPolicyResult();
        try {
            result = netHandler.updateVpcFireWallAntivirusPolicy(fireWallAntivirusPolicyUpdate);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallAntivirusPolicyUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallAntivirusPolicyRelevantInfo fireWallAntivirusPolicyRelevantInfo) {
        log.info("receiving message for create firewall from fireWallAntivirusPolicyRelevantInfo, virtual type : [{}]", fireWallAntivirusPolicyRelevantInfo.getVirtEnvType());
        log.info("msg id : [{}]", fireWallAntivirusPolicyRelevantInfo.getMsgId());

        VpcFireWallAntivirusPolicyResult result = new VpcFireWallAntivirusPolicyResult();
        try {
            result = netHandler.relevantInfoVpcFireWallAntivirusPolicy(fireWallAntivirusPolicyRelevantInfo);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(fireWallAntivirusPolicyRelevantInfo, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallIPSPolicyCreate ipsPolicyCreate) {
        log.info("receiving message for create firewall IPS Policy from ipsPolicyCreate, virtual type : [{}]", ipsPolicyCreate.getVirtEnvType());
        log.info("msg id : [{}]", ipsPolicyCreate.getMsgId());

        VpcFireWallIPSResult result = new VpcFireWallIPSResult();
        try {
            result = netHandler.createVpcFireWallIpsPolicy(ipsPolicyCreate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(ipsPolicyCreate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallIPSPolicyUpdate ipsPolicyUpdate) {
        log.info("receiving message for update firewall IPS Policy from ipsPolicyCreate [{}], virtual type : [{}]", ipsPolicyUpdate.getVirtEnvType());
        log.info("msg id : [{}]", ipsPolicyUpdate.getMsgId());

        VpcFireWallIPSResult result = new VpcFireWallIPSResult();
        try {
            result = netHandler.updateVpcFireWallIpsPolicy(ipsPolicyUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(ipsPolicyUpdate, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallIPSPolicyDelete ipsPolicyDelete) {
        log.info("receiving message for update firewall IPS Policy from ipsPolicyCreate, virtual type : [{}]", ipsPolicyDelete.getVirtEnvType());
        log.info("msg id : [{}]", ipsPolicyDelete.getMsgId());

        VpcFireWallIPSResult result = new VpcFireWallIPSResult();
        try {
            result = netHandler.deleteVpcFireWallIpsPolicy(ipsPolicyDelete);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(ipsPolicyDelete, result);
        return result;
    }

    public BaseResult handleMessage(VpcFireWallIPSSignature ipsSignature) {
        log.info("receiving message for get firewall IPS signature from ipsSignature, virtual type : [{}]", ipsSignature.getVirtEnvType());
        log.info("msg id : [{}]", ipsSignature.getMsgId());

        VpcFireWallSignatureResult result = new VpcFireWallSignatureResult();
        try {
            result = netHandler.getVpcFireWallIpsSignature(ipsSignature);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(ipsSignature, result);
        return result;
    }

    /**
     * Handle message service chain create result.
     *
     * @param serviceChainCreate the service chain create
     *
     * @return the service chain create result
     */
    public ServiceChainCreateResult handleMessage(ServiceChainCreate serviceChainCreate) {

        log.info("receiving message for creating service chain, virtual type : [{}] service chain name : [{}]",serviceChainCreate.getVirtEnvType(), serviceChainCreate.getName());

        log.info("msg id : [{}]", serviceChainCreate.getMsgId());
        ServiceChainCreateResult serviceChainCreateResult = new ServiceChainCreateResult();
        try {
            serviceChainCreateResult = netHandler.createServiceChain(serviceChainCreate);

        } catch (CommonAdapterException e) {

            serviceChainCreateResult.setSuccess(false);
            serviceChainCreateResult.setErrCode(e.getErrCode());
            serviceChainCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            serviceChainCreateResult.setSuccess(false);
            serviceChainCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serviceChainCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            serviceChainCreateResult.setSuccess(false);
            serviceChainCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serviceChainCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serviceChainCreate, serviceChainCreateResult);
        serviceChainCreateResult.setMsgId(serviceChainCreate.getMsgId());
        serviceChainCreateResult.setOptions(serviceChainCreate.getOptions());
        return serviceChainCreateResult;
    }

    /**
     * Handle message service chain context create result.
     *
     * @param serviceChainContextCreate the service chain context create
     *
     * @return the service chain context create result
     */
    public ServiceChainContextCreateResult handleMessage(ServiceChainContextCreate serviceChainContextCreate) {

        log.info("receiving message for creating service chain context, virtual type : [{}] service chain context name : [{}]"
                ,serviceChainContextCreate.getVirtEnvType(), serviceChainContextCreate.getName());

        log.info("msg id : [{}]", serviceChainContextCreate.getMsgId());
        ServiceChainContextCreateResult result = new ServiceChainContextCreateResult();
        try {
            result = netHandler.createServiceChainContext(serviceChainContextCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serviceChainContextCreate, result);
        result.setMsgId(serviceChainContextCreate.getMsgId());
        result.setOptions(serviceChainContextCreate.getOptions());
        return result;
    }

    /**
     * Handle message service chain delete result.
     *
     * @param serviceChainDelete the service chain delete
     *
     * @return the service chain delete result
     */
    public ServiceChainDeleteResult handleMessage(ServiceChainDelete serviceChainDelete) {

        log.info(
                "receiving message for deleting service chain ----virtual type : [{}] service chain id : [{}]",serviceChainDelete.getVirtEnvType(), serviceChainDelete.getUuid());

        log.info("msg id : [{}]", serviceChainDelete.getMsgId());
        ServiceChainDeleteResult result = new ServiceChainDeleteResult();
        try {
            result = netHandler.deleteServiceChain(serviceChainDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serviceChainDelete, result);
        result.setMsgId(serviceChainDelete.getMsgId());
        result.setOptions(serviceChainDelete.getOptions());
        return result;
    }

    /**
     * Handle message service chain context delete result.
     *
     * @param serviceChainContextDelete the service chain context delete
     *
     * @return the service chain context delete result
     */
    public ServiceChainContextDeleteResult handleMessage(ServiceChainContextDelete serviceChainContextDelete) {

        log.info("receiving message for deleting service chain context, virtual type : [{}] service chain context id : [{}]" ,serviceChainContextDelete.getVirtEnvType(), serviceChainContextDelete.getUuid());

        log.info("msg id : [{}]", serviceChainContextDelete.getMsgId());
        ServiceChainContextDeleteResult result = new ServiceChainContextDeleteResult();
        try {
            result = netHandler.deleteServiceChainContext(serviceChainContextDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serviceChainContextDelete, result);
        result.setMsgId(serviceChainContextDelete.getMsgId());
        result.setOptions(serviceChainContextDelete.getOptions());
        return result;
    }

    /**
     * Handle message share group create result.
     *
     * @param shareGroupCreate the share group create
     *
     * @return the share group create result
     */
    public ShareGroupCreateResult handleMessage(ShareGroupCreate shareGroupCreate) {

        log.info("receiving message for creating share group, virtual type : [{}] share group name : [{}]" ,shareGroupCreate.getVirtEnvType(), shareGroupCreate.getName());

        log.info("msg id : [{}]", shareGroupCreate.getMsgId());

        ShareGroupCreateResult shareGroupCreateResult = new ShareGroupCreateResult();
        try {
            shareGroupCreateResult = vmHandler.createShareGroup(shareGroupCreate);

            log.info("adaptor share group :[{}] has been created successfully. {}",shareGroupCreate.getGroupName(),
                     shareGroupCreateResult.isSuccess() ? "" : "But " + shareGroupCreateResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareGroupCreateResult.setSuccess(false);
            shareGroupCreateResult.setErrCode(e.getErrCode());
            shareGroupCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareGroupCreateResult.setSuccess(false);
            shareGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareGroupCreateResult.setSuccess(false);
            shareGroupCreateResult.setErrMsg(e.getMessage());
        }
        log.info("返回平台创建share group完成json：[{}]", BaseUtil.toJson(shareGroupCreateResult));
        BaseUtil.setResult(shareGroupCreate, shareGroupCreateResult);
        shareGroupCreateResult.setId(shareGroupCreate.getId());
        shareGroupCreateResult.setOrgSid(shareGroupCreate.getOrgSid());
        return shareGroupCreateResult;
    }

    /**
     * Handle message share group update result.
     *
     * @param shareGroupUpdate the share group update
     *
     * @return the share group update result
     */
    public ShareGroupUpdateResult handleMessage(ShareGroupUpdate shareGroupUpdate) {

        log.info("receiving message for creating share group, virtual type : [{}] share group name : [{}]"
                ,shareGroupUpdate.getVirtEnvType(),shareGroupUpdate.getName());

        log.info("msg id : [{}]", shareGroupUpdate.getMsgId());

        ShareGroupUpdateResult shareGroupUpateResult = new ShareGroupUpdateResult();
        try {
            shareGroupUpateResult = vmHandler.updateShareGroup(shareGroupUpdate);

            log.info("adaptor share group :[{}] has been created successfully. {}",shareGroupUpdate.getGroupName(),
                     shareGroupUpateResult.isSuccess() ? "" : "But " + shareGroupUpateResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareGroupUpateResult.setSuccess(false);
            shareGroupUpateResult.setErrCode(e.getErrCode());
            shareGroupUpateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareGroupUpateResult.setSuccess(false);
            shareGroupUpateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareGroupUpateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareGroupUpateResult.setSuccess(false);
            shareGroupUpateResult.setErrMsg(e.getMessage());
        }
        log.info("返回平台创建share group完成json：[{}]", BaseUtil.toJson(shareGroupUpateResult));
        BaseUtil.setResult(shareGroupUpdate, shareGroupUpateResult);
        shareGroupUpateResult.setId(shareGroupUpdate.getId());
        shareGroupUpateResult.setOrgSid(shareGroupUpdate.getOrgSid());
        return shareGroupUpateResult;
    }

    /**
     * Handle message share mount target create result.
     *
     * @param shareMountTargetCreate the share mount target create
     *
     * @return the share mount target create result
     */
    public ShareMountTargetCreateResult handleMessage(ShareMountTargetCreate shareMountTargetCreate) {

        log.info("receiving message for creating share mount target, virtual type : [{}] share mount target name : [{}]" ,shareMountTargetCreate
                .getVirtEnvType(), shareMountTargetCreate.getName());

        log.info("msg id : [{}]", shareMountTargetCreate.getMsgId());

        ShareMountTargetCreateResult shareMountTargetCreateResult = new ShareMountTargetCreateResult();
        try {
            shareMountTargetCreateResult = vmHandler.createShareTarget(shareMountTargetCreate);

            log.info("adaptor share mount target :[{}] has been created successfully. [{}]",shareMountTargetCreate.getName(),
                     shareMountTargetCreateResult.isSuccess() ? "" : "But " + shareMountTargetCreateResult
                             .getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareMountTargetCreateResult.setSuccess(false);
            shareMountTargetCreateResult.setErrCode(e.getErrCode());
            shareMountTargetCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareMountTargetCreateResult.setSuccess(false);
            shareMountTargetCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareMountTargetCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareMountTargetCreateResult.setSuccess(false);
            shareMountTargetCreateResult.setErrMsg(e.getMessage());
        }
        log.info("返回平台创建share mount target完成json：[{}]", BaseUtil.toJson(shareMountTargetCreateResult));
        BaseUtil.setResult(shareMountTargetCreate, shareMountTargetCreateResult);
        shareMountTargetCreateResult.setId(shareMountTargetCreate.getId());
        shareMountTargetCreateResult.setOrgSid(shareMountTargetCreate.getOrgSid());
        return shareMountTargetCreateResult;
    }

    /**
     * Handle message share mount target update result.
     *
     * @param shareMountTargetUpdate the share mount target update
     *
     * @return the share mount target update result
     */
    public ShareMountTargetUpdateResult handleMessage(ShareMountTargetUpdate shareMountTargetUpdate) {

        log.info("receiving message for updating share mount target, virtual type : [{}] share mount target name : [{}]",shareMountTargetUpdate
                .getVirtEnvType(), shareMountTargetUpdate.getName());

        log.info("msg id : {}", shareMountTargetUpdate.getMsgId());

        ShareMountTargetUpdateResult shareMountTargetUpdateResult = new ShareMountTargetUpdateResult();
        try {
            shareMountTargetUpdateResult = vmHandler.updateShareTarget(shareMountTargetUpdate);

            log.info("adaptor share mount target :[{}] has been updated successfully. {}",shareMountTargetUpdate.getName(),
                     shareMountTargetUpdateResult.isSuccess() ? "" : "But " + shareMountTargetUpdateResult
                             .getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareMountTargetUpdateResult.setSuccess(false);
            shareMountTargetUpdateResult.setErrCode(e.getErrCode());
            shareMountTargetUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareMountTargetUpdateResult.setSuccess(false);
            shareMountTargetUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareMountTargetUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareMountTargetUpdateResult.setSuccess(false);
            shareMountTargetUpdateResult.setErrMsg(e.getMessage());
        }
        log.info("返回平台修改share mount target完成json：[{}]", BaseUtil.toJson(shareMountTargetUpdateResult));
        shareMountTargetUpdateResult.setOrgSid(shareMountTargetUpdate.getOrgSid());
        return shareMountTargetUpdateResult;
    }

    /**
     * Handle message share mount target delete result.
     *
     * @param shareMountTargetDelete the share mount target delete
     *
     * @return the share mount target delete result
     */
    public ShareMountTargetDeleteResult handleMessage(
            ShareMountTargetDelete shareMountTargetDelete) {

        log.info(
                "receiving message for deleting share group, virtual type : [{}] share group name : [{}]",shareMountTargetDelete
                        .getVirtEnvType(), shareMountTargetDelete.getName());

        log.info("msg id : [{}]", shareMountTargetDelete.getMsgId());

        ShareMountTargetDeleteResult shareMountTargetDeleteResult = new ShareMountTargetDeleteResult();
        try {
            shareMountTargetDeleteResult = vmHandler.deleteShareTarget(shareMountTargetDelete);

            log.info("[adaptor] share mount target:[{}] has been deleted successfully. [{}]",shareMountTargetDelete.getName(),
                     shareMountTargetDeleteResult.isSuccess() ? ""
                             : "But " + shareMountTargetDeleteResult.getErrMsg());

        } catch (CommonAdapterException e) {

            shareMountTargetDeleteResult.setSuccess(false);
            shareMountTargetDeleteResult.setErrCode(e.getErrCode());
            shareMountTargetDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareMountTargetDeleteResult.setSuccess(false);
            shareMountTargetDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareMountTargetDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareMountTargetDeleteResult.setSuccess(false);
            shareMountTargetDeleteResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(shareMountTargetDelete, shareMountTargetDeleteResult);
        shareMountTargetDeleteResult.setOrgSid(shareMountTargetDelete.getOrgSid());
        return shareMountTargetDeleteResult;
    }

    /**
     * Handle message share group delete result.
     *
     * @param shareGroupDelete the share group delete
     *
     * @return the share group delete result
     */
    public ShareGroupDeleteResult handleMessage(ShareGroupDelete shareGroupDelete) {

        log.info("receiving message for deleting share group, virtual type : [{}] share group name : [{}]",shareGroupDelete.getVirtEnvType(), shareGroupDelete.getName());

        log.info("msg id : [{}]", shareGroupDelete.getMsgId());

        ShareGroupDeleteResult shareGroupDeleteResult = new ShareGroupDeleteResult();
        try {
            shareGroupDeleteResult = vmHandler.deleteShareGroup(shareGroupDelete);

            log.info("[adaptor] share group:[{}] has been deleted successfully. [{}]",shareGroupDelete.getUuid(),
                     shareGroupDeleteResult.isSuccess() ? "" : "But " + shareGroupDeleteResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareGroupDeleteResult.setSuccess(false);
            shareGroupDeleteResult.setErrCode(e.getErrCode());
            shareGroupDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareGroupDeleteResult.setSuccess(false);
            shareGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareGroupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareGroupDeleteResult.setSuccess(false);
            shareGroupDeleteResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(shareGroupDelete, shareGroupDeleteResult);
        shareGroupDeleteResult.setId(shareGroupDelete.getId());
        shareGroupDeleteResult.setOrgSid(shareGroupDelete.getOrgSid());
        return shareGroupDeleteResult;
    }

    /**
     * Handle message share rule update result.
     *
     * @param shareRuleUpdate the share rule update
     *
     * @return the share rule update result
     */
    public ShareRuleUpdateResult handleMessage(ShareRuleUpdate shareRuleUpdate) {

        log.info("receiving message for update share rule, virtual type : [{}] share rule name : [{}]"
                ,shareRuleUpdate.getVirtEnvType(), shareRuleUpdate.getName());

        log.info("msg id : [{}]", shareRuleUpdate.getMsgId());

        ShareRuleUpdateResult shareRuleUpdateResult = new ShareRuleUpdateResult();
        try {
            shareRuleUpdateResult = vmHandler.updateShareRule(shareRuleUpdate);

            log.info("adaptor share rule :[{}] has been created successfully. [{}]",shareRuleUpdate.getName(),
                     shareRuleUpdateResult.isSuccess() ? "" : "But " + shareRuleUpdateResult.getErrMsg()
            );

        } catch (CommonAdapterException e) {

            shareRuleUpdateResult.setSuccess(false);
            shareRuleUpdateResult.setErrCode(e.getErrCode());
            shareRuleUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareRuleUpdateResult.setSuccess(false);
            shareRuleUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareRuleUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            shareRuleUpdateResult.setSuccess(false);
            shareRuleUpdateResult.setErrMsg(e.getMessage());
        }

        shareRuleUpdateResult.setOrgSid(shareRuleUpdate.getOrgSid());
        return shareRuleUpdateResult;
    }


    public BaseResult handleMessage(DescribeAvailableZone availableZone) {
        log.info("receiving message for querying dms available zone, virtual type : [{}]", availableZone.getVirtEnvType());
        log.info("msg id : [{}]", availableZone.getMsgId());

        DescribeAvailableZoneResult result = new DescribeAvailableZoneResult();
        try {
            result = dmsHandler.getAvailableZones(availableZone);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(availableZone, result);
        return result;
    }

    public BaseResult handleMessage(DescribeProductSpec product) {
        log.info("receiving message for querying dms product engine type : [{}]", product.getEngine());
        log.info("msg id : [{}]", product.getMsgId());

        DescribeProductSpecResult result = new DescribeProductSpecResult();
        try {
            result = dmsHandler.getProducts(product);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(product, result);
        return result;
    }

    public MqInstanceCreateResult handleMessage(MqInstanceCreate instanceCreate) {

        log.info("receiving message for creating mq instancce, virtual type : [{}] mq instancce name : [{}]",instanceCreate.getVirtEnvType(), instanceCreate.getName());

        log.info("msg id : [{}]", instanceCreate.getMsgId());
        MqInstanceCreateResult instanceCreateResult = new MqInstanceCreateResult();
        try {
            instanceCreateResult = dmsHandler.createMqInstance(instanceCreate);

        } catch (CommonAdapterException e) {

            instanceCreateResult.setSuccess(false);
            instanceCreateResult.setErrCode(e.getErrCode());
            instanceCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            instanceCreateResult.setSuccess(false);
            instanceCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            instanceCreateResult.setSuccess(false);
            instanceCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(instanceCreate, instanceCreateResult);
        instanceCreateResult.setMsgId(instanceCreate.getMsgId());
        instanceCreateResult.setId(instanceCreate.getId());
        instanceCreateResult.setOptions(instanceCreate.getOptions());
        return instanceCreateResult;
    }

    public MqInstanceUpdateResult handleMessage(MqInstanceUpdate instanceUpdate) {

        log.info("receiving message for update mq instancce, virtual type : [{}] mq instancce name : [{}]",instanceUpdate.getVirtEnvType(), instanceUpdate.getName());

        log.info("msg id : [{}]", instanceUpdate.getMsgId());
        MqInstanceUpdateResult instanceUpdateResult = new MqInstanceUpdateResult();
        try {
            instanceUpdateResult = dmsHandler.updateMqInstance(instanceUpdate);

        } catch (CommonAdapterException e) {

            instanceUpdateResult.setSuccess(false);
            instanceUpdateResult.setErrCode(e.getErrCode());
            instanceUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            instanceUpdateResult.setSuccess(false);
            instanceUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            instanceUpdateResult.setSuccess(false);
            instanceUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(instanceUpdate, instanceUpdateResult);
        instanceUpdateResult.setMsgId(instanceUpdate.getMsgId());
        instanceUpdateResult.setOptions(instanceUpdate.getOptions());
        return instanceUpdateResult;
    }

    public MqInstanceDeleteResult handleMessage(MqInstanceDelete instanceDelete) {

        log.info("receiving message for delete mq instancce, virtual type : [{}] mq instancce name : [{}]",instanceDelete.getVirtEnvType(), instanceDelete.getName());

        log.info("msg id [{}]: ", instanceDelete.getMsgId());
        MqInstanceDeleteResult instanceDeleteResult = new MqInstanceDeleteResult();
        try {
            instanceDeleteResult = dmsHandler.deleteMqInstance(instanceDelete);

        } catch (CommonAdapterException e) {

            instanceDeleteResult.setSuccess(false);
            instanceDeleteResult.setErrCode(e.getErrCode());
            instanceDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            instanceDeleteResult.setSuccess(false);
            instanceDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            instanceDeleteResult.setSuccess(false);
            instanceDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(instanceDelete, instanceDeleteResult);
        instanceDeleteResult.setMsgId(instanceDelete.getMsgId());
        instanceDeleteResult.setOptions(instanceDelete.getOptions());
        return instanceDeleteResult;
    }

    public MqInstanceActionResult handleMessage(MqInstanceAction mqInstanceAction) {

        log.info("receiving message for delete or restart mq instancce, virtual type : mq instancce name :",mqInstanceAction.getVirtEnvType(), mqInstanceAction.getName());

        log.info("msg id : [{}]", mqInstanceAction.getMsgId());
        MqInstanceActionResult actionResult = new MqInstanceActionResult();
        try {
            actionResult = dmsHandler.operateMqInstance(mqInstanceAction);

        } catch (CommonAdapterException e) {

            actionResult.setSuccess(false);
            actionResult.setErrCode(e.getErrCode());
            actionResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            actionResult.setSuccess(false);
            actionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            actionResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            actionResult.setSuccess(false);
            actionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            actionResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mqInstanceAction, actionResult);
        actionResult.setMsgId(mqInstanceAction.getMsgId());
        actionResult.setOptions(mqInstanceAction.getOptions());
        return actionResult;
    }

    public KafkaTopicCreateResult handleMessage(KafkaTopicCreate kafkaTopicCreate) {

        log.info("receiving message for create kafka topic, virtual type : [{}] topic name : [{}]",kafkaTopicCreate.getVirtEnvType(), kafkaTopicCreate.getName());

        log.info("msg id : [{}]", kafkaTopicCreate.getMsgId());
        KafkaTopicCreateResult topicCreateResult = new KafkaTopicCreateResult();
        try {
            topicCreateResult = dmsHandler.createKafkaTopic(kafkaTopicCreate);

        } catch (CommonAdapterException e) {

            topicCreateResult.setSuccess(false);
            topicCreateResult.setErrCode(e.getErrCode());
            topicCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            topicCreateResult.setSuccess(false);
            topicCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            topicCreateResult.setSuccess(false);
            topicCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(kafkaTopicCreate, topicCreateResult);
        topicCreateResult.setMsgId(kafkaTopicCreate.getMsgId());
        topicCreateResult.setOptions(kafkaTopicCreate.getOptions());
        return topicCreateResult;
    }

    public KafkaTopicDeleteResult handleMessage(KafkaTopicDelete kafkaTopicDelete) {

        log.info("receiving message for delete kafka topic, virtual type : [{}] topic name : [{}]",kafkaTopicDelete.getVirtEnvType(), kafkaTopicDelete.getName());

        log.info("msg id : [{}]", kafkaTopicDelete.getMsgId());
        KafkaTopicDeleteResult topicDeleteResult = new KafkaTopicDeleteResult();
        try {
            topicDeleteResult = dmsHandler.deleteKafkaTopic(kafkaTopicDelete);

        } catch (CommonAdapterException e) {

            topicDeleteResult.setSuccess(false);
            topicDeleteResult.setErrCode(e.getErrCode());
            topicDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            topicDeleteResult.setSuccess(false);
            topicDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            topicDeleteResult.setSuccess(false);
            topicDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(kafkaTopicDelete, topicDeleteResult);
        topicDeleteResult.setMsgId(kafkaTopicDelete.getMsgId());
        topicDeleteResult.setOptions(kafkaTopicDelete.getOptions());
        return topicDeleteResult;
    }

    /**
     * 高速缓存实例创建
     */
    public DcsCreateResult handleMessage(DcsCreate create) {
        log.info("高速缓存实例创建,msg id : [{}]", create.getMsgId());
        DcsCreateResult result = new DcsCreateResult();
        try {
            result = dcsHandler.createDcs(create);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(create, result);
        result.setId(create.getId());
        result.setMsgId(create.getMsgId());
        result.setOptions(create.getOptions());
        return result;
    }

    /**
     * 高速缓存实例拓展
     */
    public DcsExtendResult handleMessage(DcsExtend opt) {
        log.info("高速缓存实例拓展，msg id : [{}]", opt.getMsgId());
        DcsExtendResult result = new DcsExtendResult();
        try {
            result = dcsHandler.extendDcs(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * 高速缓存实例操作
     */
    public DcsActionResult handleMessage(DcsAction opt) {
        log.info("msg id : {}", opt.getMsgId());
        DcsActionResult result = new DcsActionResult();
        try {
            result = dcsHandler.actionDcs(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * Handle message diskbackup create result.
     *
     * @param diskBackupCreate the disk create
     *
     * @return the disk create result
     */
    public DiskBackupCreateResult handleMessage(DiskBackupCreate diskBackupCreate) {

        log.info("receiving message for creating diskBackup----virtual type : " + diskBackupCreate.getVirtEnvType() +
                         " diskBackup name:" + diskBackupCreate.getName());

        log.info("msg id : " + diskBackupCreate.getMsgId());

        DiskBackupCreateResult diskBackupCreateResult = new DiskBackupCreateResult();

        try {
            diskBackupCreateResult = storageHandler.createDiskBackup(diskBackupCreate);
            diskBackupCreateResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            diskBackupCreateResult.setSuccess(false);
            diskBackupCreateResult.setErrCode(e.getErrCode());
            diskBackupCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskBackupCreateResult.setSuccess(false);
            diskBackupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskBackupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskBackupCreateResult.setErrMsg(e.getMessage());
        }
        if (diskBackupCreateResult.isSuccess()) {
            log.info("[adaptor] diskbackup :" + diskBackupCreate.getName() + " has been created successfully");
        } else {
            log.info("[adaptor] diskbackup :" + diskBackupCreate.getName() + " has been created failure");
        }
        BaseUtil.setResult(diskBackupCreate, diskBackupCreateResult);
        diskBackupCreateResult.setMsgId(diskBackupCreate.getMsgId());
        diskBackupCreateResult.setId(diskBackupCreate.getId());
        diskBackupCreateResult.setResVdSid(diskBackupCreate.getResVdSid());
        return diskBackupCreateResult;

    }


    /**
     * Handle message diskbackup delete result.
     *
     * @param diskBackupDelete the diskbackup delete
     *
     * @return the diskbackup delete result
     */
    public DiskBackupDeleteResult handleMessage(DiskBackupDelete diskBackupDelete) {

        log.info("receiving message for deleting diskBackup----virtual type : " + diskBackupDelete.getVirtEnvType());

        log.info("msg id : " + diskBackupDelete.getMsgId());

        DiskBackupDeleteResult diskBackupDeleteResult = new DiskBackupDeleteResult();
        try {
            diskBackupDeleteResult = storageHandler.deleteDiskBackup(diskBackupDelete);
        } catch (CommonAdapterException e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(e.getErrCode());
            diskBackupDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskBackupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskBackupDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskBackupDelete, diskBackupDeleteResult);
        diskBackupDeleteResult.setMsgId(diskBackupDelete.getMsgId());
        diskBackupDeleteResult.setId(diskBackupDelete.getId());
        return diskBackupDeleteResult;
    }
    /**
     * Handle message diskbackup delete result.
     *
     * @param diskBackupRestore the diskbackup delete
     *
     * @return the diskbackup delete result
     */
    public DiskBackupRestoreResult handleMessage(DiskBackupRestore diskBackupRestore) {

        log.info("receiving message for resotre diskBackup----virtual type : " + diskBackupRestore.getVirtEnvType());

        log.info("msg id : " + diskBackupRestore.getMsgId());

        DiskBackupRestoreResult diskBackupDeleteResult = new DiskBackupRestoreResult();
        try {
            diskBackupDeleteResult = storageHandler.restoreDiskBackup(diskBackupRestore);
        } catch (CommonAdapterException e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(e.getErrCode());
            diskBackupDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskBackupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskBackupDeleteResult.setSuccess(false);
            diskBackupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskBackupDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskBackupRestore, diskBackupDeleteResult);
        diskBackupDeleteResult.setMsgId(diskBackupRestore.getMsgId());
        diskBackupDeleteResult.setId(diskBackupRestore.getId());
        diskBackupDeleteResult.setTargetResVdSid(diskBackupRestore.getTargetResVdSid());
        diskBackupDeleteResult.setTargetVdUuid(diskBackupRestore.getTargetVdUuid());
        return diskBackupDeleteResult;
    }


    /**
     * Handle message disk extend result.
     *
     * @param diskExtend the disk delete
     *
     * @return the disk extend result
     */
    public DiskExtendResult handleMessage(DiskExtend diskExtend) {

        log.info("receiving message for extending disk----virtual type : " + diskExtend.getVirtEnvType());

        log.info("msg id : " + diskExtend.getMsgId());

        DiskExtendResult diskExtendResult = new DiskExtendResult();
        try {
            diskExtendResult = storageHandler.extendDisk(diskExtend);
        } catch (CommonAdapterException e) {
            diskExtendResult.setSuccess(false);
            diskExtendResult.setErrCode(e.getErrCode());
            diskExtendResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskExtendResult.setSuccess(false);
            diskExtendResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskExtendResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            diskExtendResult.setSuccess(false);
            diskExtendResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskExtendResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskExtend, diskExtendResult);
        diskExtendResult.setMsgId(diskExtend.getMsgId());
        diskExtendResult.setResVdSid(diskExtend.getResVdSid());

        return diskExtendResult;
    }

    /**
     * Handle message v server group backend servers add result.
     *
     * @param vServerGroupBackendServersAdds the v server group backend servers add
     *
     * @return the v server group backend servers add result
     */
    public VServerGroupBackendServersAddsResult handleMessage(
            VServerGroupBackendServersAdds vServerGroupBackendServersAdds) {

        log.info("receiving message for set loadBalanceListener batch, virtual type : [{}]", vServerGroupBackendServersAdds.getVirtEnvType());

        VServerGroupBackendServersAddsResult vServerGroupBackendServersAddsResult = new VServerGroupBackendServersAddsResult();
        try {
            vServerGroupBackendServersAddsResult = netHandler
                    .addVServerGrouplbServers(vServerGroupBackendServersAdds);
        } catch (CommonAdapterException e) {
            vServerGroupBackendServersAddsResult.setSuccess(false);
            vServerGroupBackendServersAddsResult.setErrCode(e.getErrCode());
            vServerGroupBackendServersAddsResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupBackendServersAddsResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupBackendServersAddsResult.setSuccess(false);
            vServerGroupBackendServersAddsResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            vServerGroupBackendServersAddsResult.setErrMsg(e.getMessage());
            vServerGroupBackendServersAddsResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupBackendServersAdds, vServerGroupBackendServersAddsResult);
        return vServerGroupBackendServersAddsResult;
    }

    /**
     * @param portAssignPrivateIp
     *
     * @description Handle message port assign fixedIp result
     */
    public PortAssignPrivateIpResult handleMessage(PortAssignPrivateIp portAssignPrivateIp) {
        log.info("receiving message for deleting port, virtual type : [{}] port id : [{}]",portAssignPrivateIp.getVirtEnvType(), portAssignPrivateIp.getPortId());

        log.info("msg id : [{}]", portAssignPrivateIp.getMsgId());
        PortAssignPrivateIpResult result = new PortAssignPrivateIpResult();
        try {
            result = netHandler.assignPrivateIp(portAssignPrivateIp);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portAssignPrivateIp, result);
        result.setMsgId(portAssignPrivateIp.getMsgId());
        result.setOptions(portAssignPrivateIp.getOptions());
        return result;
    }

    /**
     * @param portUnAssignPrivateIp
     *
     * @description Handle message port assign fixedIp result
     */
    public PortUnAssignPrivateIpResult handleMessage(PortUnAssignPrivateIp portUnAssignPrivateIp) {
        log.info("receiving message for deleting port, virtual type : [{}] port id : [{}]",portUnAssignPrivateIp.getVirtEnvType(), portUnAssignPrivateIp.getPortId());

        log.info("msg id : [{}]", portUnAssignPrivateIp.getMsgId());
        PortUnAssignPrivateIpResult result = new PortUnAssignPrivateIpResult();
        try {
            result = netHandler.unassignPrivateIp(portUnAssignPrivateIp);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portUnAssignPrivateIp, result);
        result.setMsgId(portUnAssignPrivateIp.getMsgId());
        result.setOptions(portUnAssignPrivateIp.getOptions());
        return result;
    }

    public VdcCreateResult handleMessage(VdcCreate vdcCreate) {
        log.info("创建VDC，msg id : [{}]", vdcCreate.getMsgId());
        VdcCreateResult result = new VdcCreateResult();
        try {
            result = adminHandler.crateVdc(vdcCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcCreate, result);
        result.setMsgId(vdcCreate.getMsgId());
        result.setOptions(vdcCreate.getOptions());
        return result;
    }

    public VdcDeleteResult handleMessage(VdcDelete vdcDelete) {
        log.info("删除VDC，msg id : [{}]", vdcDelete.getMsgId());
        VdcDeleteResult result = new VdcDeleteResult();
        try {
            result = adminHandler.deleteVdc(vdcDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcDelete, result);
        result.setMsgId(vdcDelete.getMsgId());
        result.setOptions(vdcDelete.getOptions());
        return result;
    }

    public VdcDetailResult handleMessage(VdcDetail vdcDetail) {
        log.info("查询VDC详情，msg id : [{}]", vdcDetail.getMsgId());
        VdcDetailResult result = new VdcDetailResult();
        try {
            result = adminHandler.queryVdcDetail(vdcDetail);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcDetail, result);
        result.setMsgId(vdcDetail.getMsgId());
        result.setOptions(vdcDetail.getOptions());
        return result;
    }

    public VdcIdpUpdateResult handleMessage(VdcIdpUpdate vdcIdpUpdate) {
        VdcIdpUpdateResult result = new VdcIdpUpdateResult();
        try {
            result = adminHandler.vdcIdpUpdate(vdcIdpUpdate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcIdpUpdate, result);
        result.setMsgId(vdcIdpUpdate.getMsgId());
        result.setOptions(vdcIdpUpdate.getOptions());
        return result;
    }

    /**
     * 查询 VDC 网络资源列表
     *
     * @param networkResourceGet
     */
    public VdcNetworkGetResult handleMessage(VdcNetworkResourceGet networkResourceGet) {
        VdcNetworkGetResult result = new VdcNetworkGetResult();
        try {
            result = adminHandler.vdcNetworkGetResult(networkResourceGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(networkResourceGet, result);
        result.setMsgId(networkResourceGet.getMsgId());
        result.setOptions(networkResourceGet.getOptions());
        return result;
    }


    public VdcNetworkUpdateResult handleMessage(VdcNetworkResourceUpdate networkResourceUpdate) {
        VdcNetworkUpdateResult result = new VdcNetworkUpdateResult();
        try {
            result = adminHandler.vdcNetworkAuthUpdateResult(networkResourceUpdate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(networkResourceUpdate, result);
        result.setMsgId(networkResourceUpdate.getMsgId());
        result.setOptions(networkResourceUpdate.getOptions());
        return result;
    }


    public VdcRegionAZGetResult handleMessage(VdcRegionAZGet vdcRegionAZGet) {
        VdcRegionAZGetResult result = new VdcRegionAZGetResult();
        try {
            result = adminHandler.vdcRegionAZGetResult(vdcRegionAZGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcRegionAZGet, result);
        result.setMsgId(vdcRegionAZGet.getMsgId());
        result.setOptions(vdcRegionAZGet.getOptions());
        return result;
    }

    public VdcRoleCreateResult handleMessage(VdcRoleCreate vdcRoleCreate) {
        VdcRoleCreateResult result = new VdcRoleCreateResult();
        try {
            result = adminHandler.vdcRoleCreate(vdcRoleCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcRoleCreate, result);
        result.setMsgId(vdcRoleCreate.getMsgId());
        result.setOptions(vdcRoleCreate.getOptions());
        return result;
    }

    public VdcRoleDeleteResult handleMessage(VdcRoleDelete vdcRoleDelete) {
        VdcRoleDeleteResult result = new VdcRoleDeleteResult();
        try {
            result = adminHandler.vdcRoleDelete(vdcRoleDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcRoleDelete, result);
        result.setMsgId(vdcRoleDelete.getMsgId());
        result.setOptions(vdcRoleDelete.getOptions());
        return result;
    }

    public VdcRolesGetResult handleMessage(VdcRolesGet vdcRolesGet) {
        VdcRolesGetResult result = new VdcRolesGetResult();
        try {
            result = adminHandler.vdcRoleGet(vdcRolesGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcRolesGet, result);
        result.setMsgId(vdcRolesGet.getMsgId());
        result.setOptions(vdcRolesGet.getOptions());
        return result;
    }

    public VdcRoleUpdateResult handleMessage(VdcRoleUpdate vdcRoleUpdate) {
        VdcRoleUpdateResult result = new VdcRoleUpdateResult();
        try {
            result = adminHandler.vdcRoleUpdate(vdcRoleUpdate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcRoleUpdate, result);
        result.setMsgId(vdcRoleUpdate.getMsgId());
        result.setOptions(vdcRoleUpdate.getOptions());
        return result;
    }

    public VdcScopeUpdateResult handleMessage(VdcScopeUpdate vdcScopeUpdate) {
        VdcScopeUpdateResult result = new VdcScopeUpdateResult();
        try {
            result = adminHandler.updateVdcScope(vdcScopeUpdate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcScopeUpdate, result);
        result.setMsgId(vdcScopeUpdate.getMsgId());
        result.setOptions(vdcScopeUpdate.getOptions());
        return result;
    }

    public VdcUserCreateResult handleMessage(VdcUserCreate vdcUserCreate) {
        VdcUserCreateResult result = new VdcUserCreateResult();
        try {
            result = adminHandler.createVdcUser(vdcUserCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserCreate, result);
        result.setMsgId(vdcUserCreate.getMsgId());
        result.setOptions(vdcUserCreate.getOptions());
        return result;
    }

    public VdcUserDeleteResult handleMessage(VdcUserDelete vdcUserDelete) {
        log.info("删除VDC 用户，msg id : [{}],vdcUserId:{}", vdcUserDelete.getMsgId(),vdcUserDelete.getVdcUserId());
        VdcUserDeleteResult result = new VdcUserDeleteResult();
        try {
            result = adminHandler.delVdcUser(vdcUserDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserDelete, result);
        result.setMsgId(vdcUserDelete.getMsgId());
        result.setOptions(vdcUserDelete.getOptions());
        return result;
    }

    public VdcUserGetResult handleMessage(VdcUserGet vdcUserGet) {
        VdcUserGetResult result = new VdcUserGetResult();
        try {
            result = adminHandler.vdcUserGet(vdcUserGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGet, result);
        result.setMsgId(vdcUserGet.getMsgId());
        result.setOptions(vdcUserGet.getOptions());
        return result;
    }

    public VdcUserGroupCreateResult handleMessage(VdcUserGroupCreate vdcUserGroupCreate) {
        VdcUserGroupCreateResult result = new VdcUserGroupCreateResult();
        try {
            result = adminHandler.createVdcUserGroup(vdcUserGroupCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupCreate, result);
        result.setMsgId(vdcUserGroupCreate.getMsgId());
        result.setOptions(vdcUserGroupCreate.getOptions());
        return result;
    }

    public VdcUserGroupDeleteResult handleMessage(VdcUserGroupDelete vdcUserGroupDelete) {
        VdcUserGroupDeleteResult result = new VdcUserGroupDeleteResult();
        try {
            result = adminHandler.delVdcUserGroup(vdcUserGroupDelete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupDelete, result);
        result.setMsgId(vdcUserGroupDelete.getMsgId());
        result.setOptions(vdcUserGroupDelete.getOptions());
        return result;
    }

    public VdcUserGroupGetResult handleMessage(VdcUserGroupGet vdcUserGroupGet) {
        VdcUserGroupGetResult result = new VdcUserGroupGetResult();
        try {
            result = adminHandler.vdcRoleGet(vdcUserGroupGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupGet, result);
        result.setMsgId(vdcUserGroupGet.getMsgId());
        result.setOptions(vdcUserGroupGet.getOptions());
        return result;
    }

    public VdcUserGroupJoinUserResult handleMessage(VdcUserGroupJoinUser vdcUserGroupJoinUser) {
        VdcUserGroupJoinUserResult result = new VdcUserGroupJoinUserResult();
        try {
            result = adminHandler.vdcUserGroupJoinUser(vdcUserGroupJoinUser);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupJoinUser, result);
        result.setMsgId(vdcUserGroupJoinUser.getMsgId());
        result.setOptions(vdcUserGroupJoinUser.getOptions());
        return result;
    }

    public VdcUserGroupRoleGetResult handleMessage(VdcUserGroupRoleGet vdcUserGroupRoleGet) {
        VdcUserGroupRoleGetResult result = new VdcUserGroupRoleGetResult();
        try {
            result = adminHandler.vdcUserGroupRoleGetGet(vdcUserGroupRoleGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupRoleGet, result);
        result.setMsgId(vdcUserGroupRoleGet.getMsgId());
        result.setOptions(vdcUserGroupRoleGet.getOptions());
        return result;
    }

    public VdcUserGroupRoleUpdateResult handleMessage(VdcUserGroupRoleUpdate vdcUserGroupRoleUpdate) {
        VdcUserGroupRoleUpdateResult result = new VdcUserGroupRoleUpdateResult();
        try {
            result = adminHandler.updateUserGroupRole(vdcUserGroupRoleUpdate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupRoleUpdate, result);
        result.setMsgId(vdcUserGroupRoleUpdate.getMsgId());
        result.setOptions(vdcUserGroupRoleUpdate.getOptions());
        return result;
    }

    public VdcUserGroupUpdateResult handleMessage(VdcUserGroupUpdate vdcUserGroupCreate) {
        VdcUserGroupUpdateResult result = new VdcUserGroupUpdateResult();
        try {
            result = adminHandler.updateVdcUserGroup(vdcUserGroupCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserGroupCreate, result);
        result.setMsgId(vdcUserGroupCreate.getMsgId());
        result.setOptions(vdcUserGroupCreate.getOptions());
        return result;
    }

    public VdcUserJoinUserGroupResult handleMessage(VdcUserJoinUserGroup vdcUserJoinUserGroup) {
        VdcUserJoinUserGroupResult result = new VdcUserJoinUserGroupResult();
        try {
            result = adminHandler.updateUserJoinUserGroup(vdcUserJoinUserGroup);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vdcUserJoinUserGroup, result);
        result.setMsgId(vdcUserJoinUserGroup.getMsgId());
        result.setOptions(vdcUserJoinUserGroup.getOptions());
        return result;
    }
}
