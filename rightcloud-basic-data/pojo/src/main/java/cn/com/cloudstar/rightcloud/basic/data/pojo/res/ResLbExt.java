/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.basic.data.pojo.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(description = "负债均衡Ext")
public class ResLbExt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 是否健康检查
     */
    @ApiModelProperty(value = "是否健康检查")
    private Boolean healthCheck;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名")
    private String hcDomain;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String hcUri;

    /**
     * 门限
     */
    @ApiModelProperty(value = "门限")
    private Integer hcThreshold;

    /**
     * 不检查门限
     */
    @ApiModelProperty(value = "不检查门限")
    private Integer unhcThreshold;

    /**
     * 频率
     */
    @ApiModelProperty(value = "频率")
    private Integer hcInterval;

    /**
     * http代码
     */
    @ApiModelProperty(value = "http code")
    private String hcHttpCode;

    /**
     * 超时时间
     */
    @ApiModelProperty(value = "超时时间")
    private Integer hcTimeout;

    /**
     * hc端口
     */
    @ApiModelProperty(value = "端口")
    private Integer hcPort;

    /**
     * 会话保持
     */
    @ApiModelProperty(value = "启用会话保持")
    private Boolean stickySession;

    /**
     * 会话保持方式
     */
    @ApiModelProperty(value = "会话保持方式")
    private String stickySessionType;

    /**
     * cookie超时时间
     */
    @ApiModelProperty(value = "cookie超时时间")
    private Integer cookieTimeout;

    /**
     * cookie名称
     */
    @ApiModelProperty(value = "cookie名称")
    private String cookieName;

    /**
     * hc uuid
     */
    @ApiModelProperty(value = "uuid")
    private String hcUuid;

    /**
     * 会话保持时间
     */
    @ApiModelProperty(value = "会话保持时间")
    private Integer stickySessionTime;

    /**
     * Http请求方式
     */
    @ApiModelProperty(value = "Http请求方式")
    private String hcHttpMethod;

    /**
     * 规则的请求转发方式
     */
    @ApiModelProperty(value = "规则的请求转发方式")
    private String scheduler;

    /**
     * 转发域名
     */
    @ApiModelProperty(value = "转发域名")
    private String forwardDomain;

    /**
     * 转发URL
     */
    @ApiModelProperty(value = "转发URL")
    private String forwardUrl;

    /**
     * 监听器ID
     */
    @ApiModelProperty(value = "监听器ID")
    private Long resListenerId;

    /**
     * 负载均衡ID
     */
    @ApiModelProperty(value = "负载均衡ID")
    private Long resLoadBalanceId;

    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境Id")
    private Long cloudEnvId;

    /**
     * 规则uuid
     */
    @ApiModelProperty(value = "规则uuid")
    private String extUuid;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    private Date createdDt;

    /**
     * 创建人
     */
    @ApiModelProperty(notes = "创建人")
    private String createdBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(notes = "修改时间")
    private Date updatedDt;

    /**
     * 更新人
     */
    @ApiModelProperty(notes = "修改人")
    private String updatedBy;

    /**
     * 版本
     */
    @ApiModelProperty(notes = "版本")
    private Long version;

    /**
     * 是否开启Gzip
     */
    @ApiModelProperty(notes = "是否开启Gzip")
    private Integer httpGzip;

    /**
     * 健康检查类型
     */
    @ApiModelProperty(notes = "健康检查类型")
    private String hcType;

    public ResLbExt() {
    }

}
