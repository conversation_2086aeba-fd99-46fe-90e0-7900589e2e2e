{"deploy_type": {"name": "部署类型", "default": "layoutScript", "type": "string", "apply_visibility": false, "approve_visibility": false, "disabled": true, "constraints": [{"valid_values": ["layoutScript", "normalScript", "app", "stack", "host"]}], "description": "此服务应用部署所应用的具体实现脚本(script),应用(app),k8s编排(stack),云主机(host)"}, "deploy_mode": {"name": "部署模式", "type": "string", "default": "host", "value": "host", "apply_visibility": false, "approve_visibility": false, "description": "部署模式目标主机(host),集群(cluster)"}, "is_host_create": {"name": "是否是创建主机方式", "type": "boolean", "default": false, "apply_visibility": false, "approve_visibility": false, "description": "创建服务应用是自动创建主机方式，还是选择已存在主机并部署的方式"}, "env_types": {"name": "云环境", "type": "list", "apply_visibility": false, "approve_visibility": false, "component": "select", "constraints": [{"valid_values": ["OpenStack", "VMware", "Others"]}], "description": "支持的目标部署类型"}}