<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.msg.dao.SysMSmsTemplateMapper">

    <sql id="Base_Column_List">sid, msg_id, sms_name, sms_template_id, sms_content, sms_platform,
                sms_platform_code, created_by, created_dt, updated_by, updated_dt, version,sms_name_us,sms_content_us
    </sql>
    <sql id="Example_Where_Clause">
    <trim prefixOverrides="and|or">
        <if test="sid != null">
            and sid = #{sid}
        </if>
        <if test="smsNameLike != null">
            and sms_name like concat ('%', #{smsNameLike} ,'%')
        </if>
        <if test="msgId != null and msgId != ''">
            and msg_id = #{msgId}
        </if>
        <if test="smsName != null and smsName != ''">
            and sms_name = #{smsName}
        </if>
        <if test="smsTemplateId != null and smsTemplateId != ''">
            and sms_template_id = #{smsTemplateId}
        </if>
        <if test="smsContent != null and smsContent != ''">
            and sms_content = #{smsContent}
        </if>
        <if test="smsPlatform != null and smsPlatform != ''">
            and sms_platform = #{smsPlatform}
        </if>
        <if test="smsPlatformCode != null and smsPlatformCode != ''">
            and sms_platform_code = #{smsPlatformCode}
        </if>
        <if test="createdBy != null and createdBy != ''">
            and created_by = #{createdBy}
        </if>
        <if test="createdDt != null">
            and created_dt = #{createdDt}
        </if>
        <if test="updatedBy != null and updatedBy != ''">
            and updated_by = #{updatedBy}
        </if>
        <if test="updatedDt != null">
            and updated_dt = #{updatedDt}
        </if>
        <if test="version != null">
            and version = #{version}
        </if>
    </trim>
    </sql>


    <!--查询单个-->
    <select id="queryById" resultType="cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SysMSmsTemplate">
        select
        <include refid="Base_Column_List"/>
        from sys_m_sms_template
        where sid = #{sid}
    </select>

    <!--查询指定行数据-->
    <select id="queryAll" resultType="cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SysMSmsTemplate">
        select
        <include refid="Base_Column_List"/>
        from sys_m_sms_template
        <where>
            <include refid="Example_Where_Clause"/>
        </where>
    </select>
    <select id="queryByParam"
            resultType="cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SysMSmsTemplate">
        select
        <include refid="Base_Column_List"/>
        from sys_m_sms_template
        where <include refid="Example_Where_Clause"/>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="sid" useGeneratedKeys="true">
        insert into sys_m_sms_template(msg_id, sms_name, sms_template_id, sms_content, sms_platform, sms_platform_code, created_by, created_dt, updated_by, updated_dt, version)
        values (#{msgId}, #{smsName}, #{smsTemplateId}, #{smsContent}, #{smsPlatform}, #{smsPlatformCode}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{version})
    </insert>

    <insert id="insertBatch" keyProperty="sid" useGeneratedKeys="true">
        insert into sys_m_sms_template(msg_id, sms_name, sms_template_id, sms_content, sms_platform, sms_platform_code, created_by, created_dt, updated_by, updated_dt, version)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.msgId}, #{entity.smsName}, #{entity.smsTemplateId}, #{entity.smsContent}, #{entity.smsPlatform}, #{entity.smsPlatformCode}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt}, #{entity.version})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="sid" useGeneratedKeys="true">
        insert into sys_m_sms_template(msg_id, sms_name, sms_template_id, sms_content, sms_platform, sms_platform_code, created_by, created_dt, updated_by, updated_dt, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.msgId}, #{entity.smsName}, #{entity.smsTemplateId}, #{entity.smsContent}, #{entity.smsPlatform}, #{entity.smsPlatformCode}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt}, #{entity.version})
        </foreach>
        on duplicate key update
        msg_id = values(msg_id),
        sms_name = values(sms_name),
        sms_template_id = values(sms_template_id),
        sms_content = values(sms_content),
        sms_platform = values(sms_platform),
        sms_platform_code = values(sms_platform_code),
        created_by = values(created_by),
        created_dt = values(created_dt),
        updated_by = values(updated_by),
        updated_dt = values(updated_dt),
        version = values(version)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_m_sms_template
        <set>
            <if test="msgId != null and msgId != ''">
                msg_id = #{msgId},
            </if>
            <if test="smsName != null and smsName != ''">
                sms_name = #{smsName},
            </if>
            <if test="smsTemplateId != null and smsTemplateId != ''">
                sms_template_id = #{smsTemplateId},
            </if>
            <if test="smsContent != null and smsContent != ''">
                sms_content = #{smsContent},
            </if>
            <if test="smsPlatform != null and smsPlatform != ''">
                sms_platform = #{smsPlatform},
            </if>
            <if test="smsPlatformCode != null and smsPlatformCode != ''">
                sms_platform_code = #{smsPlatformCode},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where sid = #{sid}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_m_sms_template where sid = #{sid}
    </delete>

</mapper>

