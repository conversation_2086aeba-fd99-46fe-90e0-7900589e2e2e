/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "存储返回值")
public class DescribeStorageResponse {

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty("状体")
    private String status;

    /**
     * 存储属性
     */
    @ApiModelProperty("存储属性")
    private boolean isLocal;

    /**
     * 硬盘类型
     */
    @ApiModelProperty("硬盘类型")
    private String volumeTypeName;

    /**
     * 云环境名称
     */
    @ApiModelProperty("云环境名称")
    private String cloudEnvName;

    /**
     * 云环境类型
     */
    @ApiModelProperty("云环境类型")
    private String cloudEnvType;

    /**
     * 总量
     */
    @ApiModelProperty("总量")
    private double totalSize;

    /**
     * 已使用
     */
    @ApiModelProperty("已使用")
    private double usedSize;

    /**
     * 申请
     */
    @ApiModelProperty("申请")
    private double allocSize;

    /**
     * 预配大小
     */
    @ApiModelProperty("预配大小")
    private double provisionedSize;

}
