package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024-11-06 11:53
 * @Desc
 */
@Data
public class CostBillItem {

    /**
     *  名称
     */
    private String name;

    /**
     *  类别
     */
    private String category;

    /**
     *  金额
     */
    private BigDecimal totalAmount;

    /**
     *  优惠金额
     */
    private BigDecimal discountAmount;

    /**
     *  环比
     */
    private String qoq;

    /**
     *  排名
     */
    private int ranking;

    /**
     *  较上年/月排名
     */
    private String preRanking;

    private Boolean rise;

    public BigDecimal getTotalAmount() {
        if (totalAmount != null) {
            return totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return totalAmount;
    }

    public BigDecimal getDiscountAmount() {
        if (discountAmount != null) {
            return discountAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return discountAmount;
    }
}
