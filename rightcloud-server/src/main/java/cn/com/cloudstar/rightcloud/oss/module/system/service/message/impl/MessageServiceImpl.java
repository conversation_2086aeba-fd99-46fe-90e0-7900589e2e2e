/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.service.message.impl;


import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MqConnectUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Message;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.MessageHeadListDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.SystemMessage;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SystemConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.message.MessageMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.MessageService;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * MessageServiceImpl
 *
 * <AUTHOR>
 * @date 2018/1/25.
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    /**
     * 消息Mapper
     */
    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    /**
     * 根据条件查询记录总数
     *
     * @return 记录总数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int countByParams(Criteria example) {
        int count = this.messageMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }

    /**
     * 根据主键查询记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Message selectByPrimaryKey(Long msgSid) {
        return this.messageMapper.selectByPrimaryKey(msgSid);
    }

    /**
     * 根据条件查询记录集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Message> selectByParams(Criteria example) {
        List<Message> messages = this.messageMapper.selectByParams(example);
        messages.forEach(message -> {
            message.setMsgTitle(WebUtil.getHeaderAcceptLanguage() && StrUtil.isNotBlank(message.getMsgTitleUs()) ? message.getMsgTitleUs() : message.getMsgTitle());
            message.setMsgContent(WebUtil.getHeaderAcceptLanguage() && StrUtil.isNotBlank(message.getMsgContentUs()) ? message.getMsgContentUs() : message.getMsgContent());
        });
        return messages;
    }

    /**
     * 根据条件查询记录集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Message> selectByParamsApi(Criteria example) {
        List<Message> messages = this.messageMapper.selectByParamsApi(example);
        messages.forEach(message -> {
            message.setMsgTitle(WebUtil.getHeaderAcceptLanguage() && StrUtil.isNotBlank(message.getMsgTitleUs()) ? message.getMsgTitleUs() : message.getMsgTitle());
            message.setMsgContent(WebUtil.getHeaderAcceptLanguage() && StrUtil.isNotBlank(message.getMsgContentUs()) ? message.getMsgContentUs() : message.getMsgContent());
        });
        return messages;
    }
    /**
     * 根据条件查询记录集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> selectByParamsPage(Criteria example) {
        return this.messageMapper.selectByParamsPage(example);
    }

    @Override
    public List<Long> selectByParamsPageApi(Criteria example) {
        return this.messageMapper.selectByParamsPageApi(example);
    }


    /**
     * 根据条件查询记录数
     */
    @Override
    public long countByParamsPage(Criteria example) {
        return this.messageMapper.countByParamsPage(example);
    }

    @Override
    public long countByParamsPageApi(Criteria example) {
        return this.messageMapper.countByParamsPageApi(example);
    }

    /**
     * 根据主键删除记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long msgSid) {
        return this.messageMapper.deleteByPrimaryKey(msgSid);
    }

    /**
     * 根据主键更新属性不为空的记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(Message record) {
        // 根据用户Id推送状态，客户端重新拉取站内消息
        ServerMsgPublisher.sendMsg("/topic/msg/" + RequestContextUtil
                .getUserIdFromAccount(RequestContextUtil.getAuthUserInfo().getAccount()), "ReadMsg");
        return this.messageMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键更新记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKey(Message record) {
        return this.messageMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据条件删除记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByParams(Criteria example) {
        return this.messageMapper.deleteByParams(example);
    }

    /**
     * 根据条件更新属性不为空的记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParamsSelective(Message record, Criteria example) {
        return this.messageMapper.updateByParams(record, example.getCondition());
    }

    /**
     * 根据条件更新记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParams(Message record, Criteria example) {
        return this.messageMapper.updateByParams(record, example.getCondition());
    }

    /**
     * 保存记录,不管记录里面的属性是否为空
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(Message record) {
        return this.messageMapper.insert(record);
    }

    /**
     * 保存属性不为空的记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(Message record) {
        return this.messageMapper.insertSelective(record);
    }

    @Override
    public void checkAuth(List<String> ids) {
        // 去重
        ids = ids.stream().distinct().collect(Collectors.toList());
        int count = messageMapper.checkAuth(RequestContextUtil.getAuthUserInfo().getUserSid(), ids);
        if (count != ids.size()) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
    }

    @Override
    public boolean pushMsgTOThirdParty(Message message) {
        try{
            final Long companyId = userMapper.queryCompanyIdByUserSid(message.getToUserSid());
            //如果用户为空，或者用户为管理员，就不发送消息
            if(ObjectUtil.isEmpty(companyId)){
                log.error("管理员跳过消息发送:{}",message.getToUser());
                return true;
            }
            Criteria criteria = new Criteria();
            criteria.put("configType", "third_msg_mq_config");
            //获取默认配置
            List<SysConfig> sysConfigs = sysConfigService.selectByParams(criteria);
            if (cn.hutool.core.collection.CollectionUtil.isEmpty(sysConfigs)) {
                log.error("未查询到三方MQ配置");
                return false;
            }
            Map<String, String> mgtComfig = sysConfigs.stream().collect(Collectors.toMap(SysConfig::getConfigKey, SysConfig::getConfigValue));
            if(Objects.nonNull(mgtComfig.get("push.msg.flag")) && "1".equalsIgnoreCase(mgtComfig.get("push.msg.flag"))){
               //开启推送消息 才去推送消息到第三方mq
                message.setMsgContent(addDomainToLinks(message.getMsgContent()));
                HashMap<String, Object> map = new HashMap<>();
                map.put("requestId", UUID.randomUUID().toString().replaceAll("-", ""));
                map.put("msgSid", message.getMsgSid());
                map.put("msgTitle", message.getMsgTitle());
                map.put("msgContent", message.getMsgContent());
                map.put("sendDate", message.getSendDate());
                map.put("companyId",companyId);
                if(Objects.nonNull(mgtComfig.get("mq.port")) && !"".equalsIgnoreCase(mgtComfig.get("mq.port"))){
                    Connection connection = MqConnectUtil.getConnection(mgtComfig.get("mq.host"),
                                                                        mgtComfig.get("mq.port"),
                                                                        mgtComfig.get("mq.username"),
                                                                        CrytoUtilSimple.decrypt(mgtComfig.get("mq.password")));
                    Channel channel = connection.createChannel();
                    if (StringUtils.isNotBlank(mgtComfig.get("mq.routingKey"))) {
                        channel.basicPublish(mgtComfig.get("mq.exchange"), mgtComfig.get("mq.routingKey"), null, JSONUtil.toJsonStr(map).getBytes());
                    } else if (StringUtils.isNotBlank(mgtComfig.get("mq.queue"))) {
                        channel.basicPublish("", mgtComfig.get("mq.queue"), null, JSONUtil.toJsonStr(map).getBytes());
                    } else {
                        channel.basicPublish(mgtComfig.get("mq.exchange"), "", null, JSONUtil.toJsonStr(map).getBytes());
                    }
                    //关闭通道和连接
                    channel.close();
                    connection.close();
                    log.info("成功推送消息：{}", JSONUtil.toJsonStr(map));
                    return true;
                }else{
                    log.info("未开启mq配置,不推送消息到第三方平台");
                    return false;
                }
            }
        }catch (Exception e){
            log.error("推送消息到第三方平台失败:{}",e);
        }
        return false;
    }

    /**
     * 根据主键批量删除
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readAllMsgs(String account) {
        // 根据用户Id推送状态，客户端重新拉取站内消息
        ServerMsgPublisher.sendMsg("/topic/msg/" + RequestContextUtil
                .getUserIdFromAccount(RequestContextUtil.getAuthUserInfo().getAccount()), "ReadMsg");
        messageMapper.readAllMsgs(account);
    }

    /**
     * 根据主键批量删除
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBatchByPks(List<String> msgSids) {
        // 根据用户Id推送状态，客户端重新拉取站内消息
        ServerMsgPublisher.sendMsg("/topic/msg/" + RequestContextUtil
                .getUserIdFromAccount(RequestContextUtil.getAuthUserInfo().getAccount()), "ReadMsg");
        return this.messageMapper.deleteBatchByPks(msgSids);
    }

    /**
     * 根据主键批量更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBatchByPks(List<Message> messages) {
        // 根据用户Id推送状态，客户端重新拉取站内消息
        ServerMsgPublisher.sendMsg("/topic/msg/" + RequestContextUtil
                .getUserIdFromAccount(RequestContextUtil.getAuthUserInfo().getAccount()), "ReadMsg");
        return this.messageMapper.updateBatchByPks(messages);
    }

    /**
     * 批量插入消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBatch(List<Message> messages) {
        return this.messageMapper.insertBatch(messages);
    }

    /**
     * 插入系统消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSystemMessages(SystemMessage systemMessage) {
        return 0;
    }

    @Override
    public List<MessageHeadListDTO> displayUserMessageHeadList(Criteria criteria) {
        return this.messageMapper.displayUserMessageHeadList(criteria);
    }

    private String addDomainToLinks(String input) {
        try{
            final SysConfig config = systemConfigMapper.selectByKey("rightcloud.console.url");
            if(ObjectUtil.isNotEmpty(config)&&ObjectUtil.isNotEmpty(config.getConfigValue())){
                // 定义匹配 <a> 标签 href 属性的正则表达式
                String regex = "<a\\s+href\\s*=\\s*\"([^\"]+)\"";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(input);

                StringBuilder result = new StringBuilder();
                int lastIndex = 0;

                while (matcher.find()) {
                    // 将匹配到 <a> 标签之前的内容添加到结果中
                    result.append(input, lastIndex, matcher.start());
                    // 获取 href 属性的值
                    String href = matcher.group(1);
                    // 在链接前添加域名
                    String newHref = config.getConfigValue() + href;
                    // 替换原来的 href 属性值
                    result.append("<a href=\"").append(newHref).append("\"");
                    lastIndex = matcher.end();
                }
                // 添加剩余的内容
                result.append(input.substring(lastIndex));
                return result.toString();
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return input;
    }

}
