package cn.com.cloudstar.rightcloud.oss.module.collector.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "话单采集记录列表")
public class CollectorRecordResponse {

    @ApiModelProperty(value = "云环境id")
    private Long envId;

    @ApiModelProperty(value = "服务类型code")
    private Integer type;

    @ApiModelProperty(value = "服务类型")
    private String typeName;

    @ApiModelProperty(value = "服务类型中文名")
    private String typeMsg;

    @ApiModelProperty(value = "采集总条数")
    private Integer sdrTotalCount = 0;

    @ApiModelProperty(value = "出账总条数")
    private Integer sdrCount = 0;

    @ApiModelProperty(value = "桶名称")
    private String bucketName;

    @ApiModelProperty(value = "最后采集时间")
    private String lastTime;

    @ApiModelProperty(value = "最后出账时间")
    private String outAccountLastTime;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
}
