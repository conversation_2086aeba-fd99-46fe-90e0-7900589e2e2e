<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.productQuota.dao.BizAccountProductQuotaMapper">

  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.oss.module.productQuota.entity.BizAccountProductQuota">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_sid" jdbcType="BIGINT" property="orgSid" />
    <result column="user_sid" jdbcType="BIGINT" property="userSid" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="service_type" jdbcType="VARCHAR" property="serviceType" />
    <result column="min_amount" jdbcType="DECIMAL" property="minAmount" />
    <result column="min_frozen_amount" jdbcType="DECIMAL" property="minFrozenAmount" />
    <result column="frozen_amount_status" jdbcType="VARCHAR" property="frozenAmountStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_dt" jdbcType="VARCHAR" property="updatedDt" />
    <result column="cluster_id" jdbcType="VARCHAR" property="clusterId" />
    <result column="entity_id" jdbcType="BIGINT" property="entityId" />
  </resultMap>

  <sql id="Base_Column_List">
    id, org_sid, user_sid, service_id, service_type, min_amount, min_frozen_amount, frozen_amount_status, 
    `status`, version, created_by, created_dt, updated_by, updated_dt, cluster_id, entity_id
  </sql>

  <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_account_product_quota
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>

  <sql id="Example_Where_Clause" >
    <trim prefix="where" prefixOverrides="and|or" >
      <if test="condition.orgSid != null" >
        and org_sid = #{condition.orgSid}
      </if>
      <if test="condition.userSid != null" >
        and user_sid = #{condition.userSid}
      </if>
      <if test="condition.serviceId != null" >
        and service_id = #{condition.serviceId}
      </if>
      <if test="condition.serviceIds != null and condition.serviceIds.size > 0">
        and service_id in
        <foreach collection="condition.serviceIds"  open="(" separator="," close=")" item="serviceId">
          #{serviceId}
        </foreach>
      </if>
      <if test="condition.serviceType != null" >
        and service_type = #{condition.serviceType}
      </if>
      <if test="condition.minAmount != null" >
        and min_amount = #{condition.minAmount}
      </if>
      <if test="condition.minFrozenAmount != null" >
        and min_frozen_amount = #{condition.minFrozenAmount}
      </if>
      <if test="condition.frozenAmountStatus != null" >
        and frozen_amount_status = #{condition.frozenAmountStatus}
      </if>
      <if test="condition.status != null" >
        and status = #{condition.status}
      </if>
      <if test="condition.createdBy != null" >
        and created_by = #{condition.createdBy}
      </if>
      <if test="condition.createdDt != null" >
        and created_dt = #{condition.createdDt}
      </if>
      <if test="condition.updatedBy != null" >
        and updated_by = #{condition.updatedBy}
      </if>
      <if test="condition.updatedDt != null" >
        and updated_dt = #{condition.updatedDt}
      </if>
      <if test="condition.version != null" >
        and version = #{condition.version}
      </if>
      <if test="condition.clusterId != null" >
        and cluster_id = #{condition.clusterId}
      </if>
      <if test="condition.entityId != null" >
        and entity_id = #{condition.entityId}
      </if>
    </trim>
  </sql>

</mapper>