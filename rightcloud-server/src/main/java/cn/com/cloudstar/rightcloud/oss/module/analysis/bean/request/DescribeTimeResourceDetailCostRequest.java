/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(description = "资源详情成本明细查询")
public class DescribeTimeResourceDetailCostRequest {

    /**
     * 开始时间
     */
    @NotNull
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @NotNull
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 时间类型 daily,weekly,monthly
     */
    @NotNull
    @ApiModelProperty(value = "时间类型 daily,weekly,monthly")
    private String timeType;

    /**
     * 云环境类型
     */
    @NotNull
    @ApiModelProperty(value = "云环境类型")
    private String cloudEnvType;


    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境id")
    private String envId;

    /**
     * 资源组ID 0为未分配
     */
    @ApiModelProperty(value = "资源组ID 0为未分配")
    private String deploymentId;

    /**
     * 项目名称（模糊查询）
     */
    @ApiModelProperty(value = "项目名称（模糊查询）")
    private String projectNameLike;

    /**
     * 云环境名称（模糊查询）
     */
    @ApiModelProperty(value = "云环境名称（模糊查询）")
    private String envNameLike;

}
