package cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.BizBillingRegionResource;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingRegionCharge;

@Repository
public interface BizBillingRegionResourceMapper {

    /**
     * 通过产品类别查询计费配置
     *
     * @param resourceType 产品类别
     */
    List<BizBillingRegionResource> selectByResourceType(@Param("resourceType") String resourceType);

    /**
     * 批量修改计费配置
     */
    int updateByPrimaryKey(BizBillingRegionResource record);

    /**
     * 通过区域资源类型ID查询区域资源类型下的计费配置
     * biz_billing_region_charge
     * @param regionResourceId 区域资源类型ID
     */
    List<BizBillingRegionCharge> selectChargeByRegionResourceId(@Param("regionResourceId") Long regionResourceId);

    /**
     * 通过id查找
     * @param id
     * @return
     */
    String selectById(@Param("id") Long id);

}
