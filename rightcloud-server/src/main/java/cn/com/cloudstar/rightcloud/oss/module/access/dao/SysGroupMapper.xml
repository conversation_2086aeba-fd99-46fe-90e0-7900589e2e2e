<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.access.dao.SysGroupMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.oss.module.access.bean.SysGroup">
        <id column="group_sid" property="groupSid" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="tree_path" property="treePath" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="owner_id" property="ownerId" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="user_num" property="userNum" jdbcType="INTEGER"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="groupUsers" property="groupUsers" jdbcType="VARCHAR" />
        <result column="groupPolicys" property="groupPolicys" jdbcType="VARCHAR"/>
        <result column="sys_default" property="sysDefault" jdbcType="BIT"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.groupName != null">
                and group_name = #{condition.groupName}
            </if>
            <if test="condition.description != null">
                and description = #{condition.description}
            </if>
            <if test="condition.treePath != null">
                and tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and parent_id = #{condition.parentId}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.ownerId != null">
                and owner_id = #{condition.ownerId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.orgSids != null">
                and org_sid in
                <foreach collection="condition.orgSids" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.parentSidNotNull != null">
                and A.parent_sid is null
            </if>
        </trim>
    </sql>

    <sql id="Example_Where_Clause_1">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.groupName != null">
                and A.group_name = #{condition.groupName}
            </if>
            <if test="condition.description != null">
                and A.description = #{condition.description}
            </if>
            <if test="condition.treePath != null">
                and A.tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and A.parent_id = #{condition.parentId}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.ownerId != null">
                and A.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.parentSidNotNull == true">
                and A.parent_id is null
            </if>
            <if test="condition.sysDefault == true">
                or A.sys_default = 1
            </if>
            <if test="condition.groupNameLike != null">
                and A.group_name like concat ('%', #{condition.groupNameLike} ,'%')
            </if>
            <if test="condition.includeDefaultGroup == true">
                and (A.org_sid = #{condition.org} or A.sys_default = 1)
            </if>
        </trim>
    </sql>

    <sql id="Base_Column_List_1">
        A.group_sid, A.group_name, A.description, A.tree_path, A.parent_id, A.created_by, A.owner_id, A.org_sid,
        A.created_dt, A.updated_by, A.updated_dt, A.version, ifnull(A.sys_default, 0) sys_default,
        A.ccsp_mac,
        CASE
            WHEN A.group_sid != - 1 THEN
                ( SELECT GROUP_CONCAT( u.user_sid ORDER BY u.created_dt) FROM sys_m_user_group u WHERE u.group_sid = A.group_sid ) ELSE NULL
            END groupUsers,
        CASE
            WHEN A.group_sid != - 1 THEN
                ( SELECT GROUP_CONCAT( p.policy_sid ORDER BY p.created_dt) FROM sys_m_policy_group p WHERE p.group_sid = A.group_sid ) ELSE NULL
            END groupPolicys
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_1"/>
        , B.group_name as parent_name
        from sys_m_group A
        left join sys_m_group B on A.parent_id = B.group_sid
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_1"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.oss.module.access.bean.SysGroup">
        update sys_m_group
        set group_name = #{groupName},
            description = #{description},
            tree_path = #{treePath},
            parent_id = #{parentId},
            created_by = #{createdBy},
            owner_id = #{ownerId},
            org_sid = #{orgSid},
            created_dt = #{createdDt},
            updated_by = #{updatedBy},
            updated_dt = #{updatedDt},
            ccsp_mac = #{ccspMac},
            version = #{version}
        where group_sid = #{groupSid}
    </update>

    <delete id="deleteByParams">
        delete from sys_m_group
        <include refid="Example_Where_Clause"/>
    </delete>

    <select id="getByNames" resultType="java.lang.Long">
        SELECT group_sid
        FROM `sys_m_group`
        where 1 = 1
        <if test="groupNames != null and groupNames.size() != 0">
            and group_name in
            <foreach collection="groupNames" item="group" open="(" close=")" separator=",">
                #{group,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectByUserSid" resultType="cn.com.cloudstar.rightcloud.oss.module.access.bean.SysGroup">
        select
        <include refid="Base_Column_List_1"/>
        from sys_m_group A
        left join sys_m_user_group B on A.group_sid = B.group_sid
        where B.user_sid = #{userSid}
    </select>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.oss.module.access.bean.SysGroup"
        useGeneratedKeys="true" keyProperty="groupSid">
        insert into sys_m_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupSid != null">
                group_sid,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="treePath != null">
                tree_path,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="sysDefault != null">
                sys_default,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupSid != null">
                #{groupSid},
            </if>
            <if test="groupName != null">
                #{groupName},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="treePath != null">
                #{treePath},
            </if>
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="ownerId != null">
                #{ownerId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="sysDefault != null">
                #{sysDefault},
            </if>
        </trim>
    </insert>
</mapper>
