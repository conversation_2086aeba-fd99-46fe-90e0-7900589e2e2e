/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.impl;

import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.SysMPreOccRecord;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.SysMPreOccRecordExample;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.dao.SysMPreOccRecordMapper;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.ISysMPreOccRecordService;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/1/11 22:18
 */
@Service
@Slf4j
public class SysMPreOccRecordServiceImpl implements ISysMPreOccRecordService {


    private static String yyyyMMddHHmmss = "yyyyMMddHHmmss";

    @Autowired
    private SysMPreOccRecordMapper sysMPreOccRecordMapper;


    @Override
    public Map<String, String> getResUsageByMonths(List<String> months) {

        HashMap<String, String> resUsageMap = new HashMap<>();

        if(CollectionUtil.isNotEmpty(months)){
            for (String month : months) {
                Date startTime = null;
                try {
                    startTime = DateUtils.parseDate(month + "01000000", yyyyMMddHHmmss);
                    Calendar ca = Calendar.getInstance();
                    ca.setTime(startTime);
                    ca.add(Calendar.MONTH, 1);/*将本月+1，*/
                    ca.add(Calendar.DAY_OF_MONTH, -1);/*将本月天数-1*/
                    int dayofmonth = ca.get(Calendar.DAY_OF_MONTH);

                    Date endTime = DateUtils.addMonths(startTime,1);

                    List<SysMPreOccRecord> occRecordList = recordsByDate(startTime, endTime);
                    Long resUse = calcResUse(occRecordList,24*dayofmonth);

                    String resUsage = resUse.toString();
                    resUsageMap.put(month,resUsage);
                } catch (Exception e) {
                    log.error("SysMPreOccRecordServiceImpl.getResUsageByMonths Exeption:",e);
                }


            }
        }

        return resUsageMap;
    }

    @NotNull
    private Long calcResUse( List<SysMPreOccRecord> occRecordList,int elseHour) {
        String preoccupancyCueStr = PropertiesUtil.getProperty("screen.airesource.precue");
        BigDecimal preoccupancyCueNum = BigDecimal.ZERO;
        if(StringUtils.isNotEmpty(preoccupancyCueStr)){
            preoccupancyCueNum = new BigDecimal(preoccupancyCueStr);
        }
        BigDecimal resUse = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(occRecordList)){
            for (SysMPreOccRecord sysMPreOccRecord : occRecordList) {
                Long min = DateUtil.between(sysMPreOccRecord.getStartTime(),sysMPreOccRecord.getEndTime(), DateUnit.MINUTE);
                resUse = NumberUtil.add(resUse,NumberUtil.mul(new BigDecimal(sysMPreOccRecord.getCueValue()),NumberUtil.div(min,new BigDecimal(60))));
            }
        }else{
            return NumberUtil.mul(preoccupancyCueNum, new BigDecimal(elseHour)).longValue();
        }
        return resUse.longValue();
    }

    @Override
    public Map<String, String> getResUsageByDays(List<String> days) {
        HashMap<String, String> resUsageMap = new HashMap<>();

        if(CollectionUtil.isNotEmpty(days)){
            for (String day : days) {
                Date startTime = null;
                try {
                    startTime = DateUtils.parseDate(day + "000000", yyyyMMddHHmmss);
                    Date endTime = DateUtils.addDays(startTime,1);

                    List<SysMPreOccRecord> occRecordList = recordsByDate(startTime, endTime);
                    Long resUse = calcResUse(occRecordList,24);

                    String resUsage = resUse.toString();
                    resUsageMap.put(day,resUsage);
                } catch (Exception e) {
                    log.error("SysMPreOccRecordServiceImpl.getResUsageByDays Exeption:",e);
                }


            }
        }

        return resUsageMap;

    }

    @Override
    public Map<String, String> getResUsageByHours(List<String> hours) {

        HashMap<String, String> resUsageMap = new HashMap<>();

        if(CollectionUtil.isNotEmpty(hours)){
            for (String hour : hours) {
                Date startTime = null;
                try {
                    startTime = DateUtils.parseDate(hour + "0000", yyyyMMddHHmmss);
                    Date endTime = DateUtils.addHours(startTime,1);

                    List<SysMPreOccRecord> occRecordList = recordsByDate(startTime, endTime);
                    Long resUse = calcResUse(occRecordList,1);

                    String resUsage = resUse.toString();
                    resUsageMap.put(hour,resUsage);
                } catch (Exception e) {
                    log.error("SysMPreOccRecordServiceImpl.getResUsageByHours Exeption:",e);
                }


            }
        }

        return resUsageMap;
    }

    @Override
    public SysMPreOccRecord getPreOccRecord(Date current) {
        SysMPreOccRecordExample occRecordExample = new SysMPreOccRecordExample();
        occRecordExample.createCriteria().andStartTimeLessThanOrEqualTo(current).andEndTimeGreaterThan(current);
        occRecordExample.or().andStartTimeLessThanOrEqualTo(current).andEndTimeIsNull();
        List<SysMPreOccRecord> occRecords = sysMPreOccRecordMapper.selectByExample(occRecordExample);
        return CollectionUtil.getFirst(occRecords);
    }

    @Override
    public Map<String, SysMPreOccRecord> getRecordsMapByTimes(List<String> times,String pattern) {
        if (StringUtils.isEmpty(pattern)) {
            pattern=yyyyMMddHHmmss;
        }

        Map<String, SysMPreOccRecord> recordMap = new HashMap<>();
        for (String time : times) {
            SysMPreOccRecord preOccRecord = getPreOccRecord(DateUtil.parse(time, pattern));
            recordMap.put(time,preOccRecord);
        }
        return recordMap;

    }

    @Override
    public List<SysMPreOccRecord> getRecordsStartToEnd(Date start, Date end) {
        SysMPreOccRecordExample occRecordExample = new SysMPreOccRecordExample();
        occRecordExample.createCriteria().andStartTimeLessThan(end).andEndTimeGreaterThan(start);
        occRecordExample.or().andStartTimeLessThan(end).andEndTimeIsNull();
        return sysMPreOccRecordMapper.selectByExample(occRecordExample);
    }

    @Override
    public void savePreOccChange(String card,String cueValue) {

        if (StringUtils.isEmpty(card) || !NumberUtil.isNumber(card)) {
            card ="0";
        }
        if (StringUtils.isEmpty(cueValue) || !NumberUtil.isNumber(cueValue)) {
            cueValue ="0";
        }


        Date date = new Date();
        SysMPreOccRecordExample occRecordExample = new SysMPreOccRecordExample();
        occRecordExample.setOrderByClause(" id desc ");
        Optional<SysMPreOccRecord> first = sysMPreOccRecordMapper.selectByExample(occRecordExample).stream().findFirst();
        if(first.isPresent()){
            SysMPreOccRecord preOccRecord = first.get();
            preOccRecord.setEndTime(date);
            sysMPreOccRecordMapper.updateByPrimaryKey(preOccRecord);
        }
        SysMPreOccRecord sysMPreOccRecord = new SysMPreOccRecord();
        sysMPreOccRecord.setCard(Integer.valueOf(card));
        sysMPreOccRecord.setCueValue(cueValue);
        sysMPreOccRecord.setStartTime(date);
        sysMPreOccRecord.setCreatedDt(date);
        sysMPreOccRecordMapper.insert(sysMPreOccRecord);

    }

    private List<SysMPreOccRecord> recordsByDate(Date startTime, Date endTime) {
        List<SysMPreOccRecord> occRecords = getRecordsStartToEnd(startTime, endTime);
        for (SysMPreOccRecord occRecord : occRecords) {
            Date occRecordStartTime = occRecord.getStartTime();
            if(occRecordStartTime.before(startTime)){
                occRecord.setStartTime(startTime);
            }
            Date occRecordEndTime = occRecord.getEndTime();
            if(occRecordEndTime ==null || occRecordEndTime.after(endTime)){
                occRecord.setEndTime(endTime);
            }
        }
        return occRecords;
    }
}
