/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.bean.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * The type DescribeProjectResPoolResponse.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/17
 */
@Data
@ApiModel(description = "项目[已关联|未关联]的分区查询")
public class DescribeProjectResPoolResponse {
    /**
     * id
     */
    @ApiModelProperty(value = "id", name = "id", example = "123")
    private Long id;

    /**
     * 分区名称
     */
    @ApiModelProperty(value = "分区名称", name = "name", example = "分区123")
    private String name;
}
