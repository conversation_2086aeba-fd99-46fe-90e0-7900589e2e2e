/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.impl;

import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.oss.module.analysis.dao.CloudAnalysisResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.analysis.service.CloudAnalysisResourceService;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.BillingStrategyStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.CloudEnvInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.ClusterInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.OrgInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.OsStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.ResHostInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.ResPoolResHostIds;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.StatusStrUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.TagInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.VmTypeStaVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.ZoneInstanceStat;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRouter;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.QueryAllCloudEnvRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResFloatingIpByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResLoadBalanceByCriteriaRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResRouterRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVsPortGroupByCloudEnvIdRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResVpcByParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.lb.ResLoadBalanceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.NetworkRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResRouterRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResVpcRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResVsPortGroupRemoteService;

import cn.hutool.core.collection.CollectionUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date: 10:15 2018/12/11
 */
@Service
public class CloudAnalysisResourceServiceImpl implements CloudAnalysisResourceService {

    @Autowired
    private CloudAnalysisResourceMapper cloudAnalysisResourceMapper;

    @DubboReference
    private ResFloatingIpRemoteService resFloatingIpService;

    @DubboReference
    private ResLoadBalanceRemoteService resLoadBalanceService;

    @DubboReference
    private ResVpcRemoteService resVpcService;

    @DubboReference
    private NetworkRemoteService networkService;

    @DubboReference
    private ResRouterRemoteService resRouterService;

    @DubboReference
    private ResVsPortGroupRemoteService resVsPortGroupService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @Override
    public List<VmTypeStaVO> queryVmTypeStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryVmTypeStat(criteria);
    }

    @Override
    public List<BillingStrategyStat> queryBillingStrategyStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryBillingStrategyStat(criteria);
    }

    @Override
    public List<ResHostInstanceStat> queryResHostInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryResHostInstanceStat(criteria);
    }

    @Override
    public List<ClusterInstanceStat> queryClusterInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryClusterInstanceStat(criteria);
    }

    @Override
    public List<OsStat> queryOsStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryOsStat(criteria);
    }

    @Override
    public List<ResPoolResHostIds> resPoolResHostIds(Criteria criteria) {
        return cloudAnalysisResourceMapper.resPoolResHostIds(criteria);
    }

    @Override
    public List<ZoneInstanceStat> queryZoneInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryZoneInstanceStat(criteria);
    }

    @Override
    public List<OrgInstanceStat> queryOrgInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryOrgInstanceStat(criteria);
    }

    @Override
    public Long countInstance(Criteria criteria) {
        return cloudAnalysisResourceMapper.countInstance(criteria);
    }

    @Override
    public List<CloudEnvInstanceStat> queryCloudEnvInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryCloudEnvInstanceStat(criteria);
    }

    @Override
    public List<TagInstanceStat> queryTagInstanceStat(Criteria criteria) {
        return cloudAnalysisResourceMapper.queryTagInstanceStat(criteria);
    }

    @Override
    public List<ResFloatingIp> getResFloatingIps(Criteria criteria) {
        QueryResFloatingIpByParamsRequest queryResFloatingIpByParamsRequest = new QueryResFloatingIpByParamsRequest();

        if (criteria.get("status") == null) {
            queryResFloatingIpByParamsRequest.setStatusNotEquals( NetworkStatus.DELETED);
        }
        List<ResFloatingIp> resFloatingIps = resFloatingIpService.selectByParams(queryResFloatingIpByParamsRequest);
        for (ResFloatingIp floatingIp : resFloatingIps) {
            floatingIp.setInstanceType(StatusStrUtil.getFloatingIpBindResType(floatingIp.getInstanceType()));
            floatingIp.setInternetChargeType(StatusStrUtil.getFloatingIpChargeType(floatingIp.getInternetChargeType()));
        }
        return resFloatingIps;
    }

    @Override
    public List<ResLoadBalance> getResLoadBalances(Criteria criteria) {
        QueryResLoadBalanceByCriteriaRequest queryResLoadBalanceByCriteriaRequest = JsonUtil.fromJson(JsonUtil.toJson(criteria.getCondition()),
                                                                                         QueryResLoadBalanceByCriteriaRequest.class);
        return this.resLoadBalanceService.selectByCriteria(queryResLoadBalanceByCriteriaRequest);
    }

    @Override
    public List<ResVpc> getResVpcs(Criteria criteria) {
        ResVpcByParams resVpcByParams = JsonUtil.fromJson(JsonUtil.toJson(criteria.getCondition()),
                                                                            ResVpcByParams.class);
        List<ResVpc> resVpcs = this.resVpcService.selectByParams(resVpcByParams);
        for (ResVpc resVpc : resVpcs) {
            int subnetNum = this.networkService.countByExample( resVpc.getId());
            resVpc.setSubnetNum(subnetNum);
        }
        return resVpcs;
    }

    @Override
    public List<ResRouter> getResRouters(QueryResRouterRequest criteria) {
        return this.resRouterService.selectByParams(criteria);
    }

    @Override
    public List<ResVsPortGroup> getResVsPortGroup(Criteria criteria) {
        QueryAllCloudEnvRequest queryAllCloudEnvRequest = JsonUtil.fromJson(JsonUtil.toJson(criteria.getCondition()),
                                                                             QueryAllCloudEnvRequest.class);
        QueryResVsPortGroupByCloudEnvIdRequest queryResVsPortGroupByCloudEnvIdRequest = JsonUtil.fromJson(JsonUtil.toJson(criteria.getCondition()),
                                                                            QueryResVsPortGroupByCloudEnvIdRequest.class);
        if (criteria.get("cloudEnvId") != null) {
            queryResVsPortGroupByCloudEnvIdRequest.setParentTopologySid(criteria.get("cloudEnvId").toString());
        } else {
            List<CloudEnv> cloudEnvs = this.cloudEnvService.selectAllCloudEnvByCompanyId(queryAllCloudEnvRequest);
            List<Long> currentUserEnvIds = cloudEnvs.stream().map(CloudEnv::getId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(currentUserEnvIds)) {
                return new ArrayList<>();
            }
            queryResVsPortGroupByCloudEnvIdRequest.setCloudEnvIds(currentUserEnvIds);
        }
        return resVsPortGroupService.selectByCloudEnvId(queryResVsPortGroupByCloudEnvIdRequest);
    }
}
