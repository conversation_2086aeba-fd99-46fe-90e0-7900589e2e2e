/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.maintenance.bean.templates.request;

import cn.com.cloudstar.rightcloud.oss.common.pojo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "获取主机模版请求参数")
public class DescribeGetServerTemplateRequest extends BaseRequest implements Serializable {

    /**
     * 模板名称搜索
     **/
    @ApiModelProperty(value = "模板名称")
    private String templateNameLike;

    @ApiModelProperty(value = "模板类型", notes = "RES-VM:主机模板, RES-VD:硬盘模板, RES-FLOATING-IP:浮动IP")
    private String type;
}
