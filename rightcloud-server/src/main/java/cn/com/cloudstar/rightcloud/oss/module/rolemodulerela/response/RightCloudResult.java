package cn.com.cloudstar.rightcloud.oss.module.rolemodulerela.response;


import java.io.Serializable;
import java.util.Objects;

import lombok.Data;

import cn.com.cloudstar.rightcloud.common.common.ResultCodeEnum;


/**
 * 返回对象
 *
 * @author: chengpeng
 * @date: 2022/10/17 15:13
 */

@Data
public class RightCloudResult<T> implements Serializable {

    /**
     * 请求返回码
     */
    private int code;
    /**
     * 请求返回状态
     */
    private boolean status;
    /**
     * 请求返回内容
     */
    private String message;
    /**
     * 请求返回对象
     */
    private T data;

    public RightCloudResult() {
    }


    /**
     * 成功的返回结构
     *
     * @param data 类对象
     */
    public static <T> RightCloudResult<T> success(T data) {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(ResultCodeEnum.SUCCESS.getCode());
        result.setMessage("操作成功");
        result.setStatus(true);
        result.setData(data);
        return result;
    }

    /**
     * 成功的返回结构
     */
    public static <T> RightCloudResult<T> success() {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(ResultCodeEnum.SUCCESS.getCode());
        result.setMessage("操作成功");
        result.setStatus(true);
        return result;
    }

    /**
     * 成功的返回结构
     *
     * @param data 类对象
     */
    public static <T> RightCloudResult<T> success(String message, T data) {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(ResultCodeEnum.SUCCESS.code());
        result.setMessage("操作成功");
        result.setStatus(true);
        result.setData(data);
        return result;
    }

    /**
     * 失败的返回结果
     *
     * @param codeEnum 返回code码
     * @param message 返回消息内容
     * @param data 返回数据
     */
    public static <T> RightCloudResult<T> fail(ResultCodeEnum codeEnum, String message, T data) {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(Objects.nonNull(codeEnum) ? codeEnum.code() : ResultCodeEnum.BUSINESS_ERROR.code());
        result.setData(data);
        result.setMessage(message);
        result.setStatus(false);
        return result;
    }

    /**
     * 失败的返回结果
     *
     * @param codeEnum 返回code码
     * @param message 返回消息内容
     */
    public static <T> RightCloudResult<T> fail(ResultCodeEnum codeEnum, String message) {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(Objects.nonNull(codeEnum) ? codeEnum.code() : ResultCodeEnum.BUSINESS_ERROR.code());
        result.setMessage(message);
        result.setStatus(false);
        result.setData(null);
        return result;
    }

    /**
     * 失败的返回结果
     *
     * @param message 返回消息内容
     */
    public static <T> RightCloudResult<T> fail(String message) {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(ResultCodeEnum.BUSINESS_ERROR.code());
        result.setMessage(message);
        result.setStatus(false);
        result.setData(null);
        return result;
    }

    /**
     * 失败的返回结果
     */
    public static <T> RightCloudResult<T> fail() {
        RightCloudResult<T> result = new RightCloudResult<>();
        result.setCode(ResultCodeEnum.BUSINESS_ERROR.code());
        result.setMessage("操作失败");
        result.setStatus(false);
        result.setData(null);
        return result;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status;
    }
}
