
package cn.com.cloudstar.rightcloud.oss.listener;

import cn.com.cloudstar.rightcloud.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.common.util.RetryUtil;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.userlog.SysLogRequest;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.util.SysLogUtil;
import cn.hutool.core.util.BooleanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;


/**
 * syslog推送(消费者)
 *
 * <AUTHOR>
 */
@Component
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
@Slf4j
public class SysLogPushListener {
    public void handleMessage(String syslogMsg) throws Exception {
        String open = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_OPEN);
        if (BooleanUtil.toBoolean(open)) {
            String protocol = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_PROTOCOL);
            String host = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_HOST);
            Integer port = Integer.valueOf(PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_PORT));
            String tlsStorePassword = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_CLIENT_PRIVATE_PASSWORD);
            String clientPublic = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOGW_CLIENT_PUBLIC);
            String clientPassword = PropertiesUtil.getProperty(SysConfigConstants.SysLofConfig.SYSLOG_CLIENT_PRIVATE);
            SysLogRequest sysLogRequest = new SysLogRequest();
            sysLogRequest.setHost(host);
            sysLogRequest.setPort(port);
            sysLogRequest.setClientPublic(clientPublic);
            sysLogRequest.setClientPrivate(clientPassword);
            sysLogRequest.setProtocol(protocol);
            sysLogRequest.setClientPrivatePassword(tlsStorePassword);
            RetryUtil.retry(3, () -> {
                SysLogUtil.sendSyslog(sysLogRequest,syslogMsg);
            });
        }
    }
}
