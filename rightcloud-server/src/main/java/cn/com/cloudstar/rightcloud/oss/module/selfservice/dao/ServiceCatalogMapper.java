/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.selfservice.dao;

import cn.com.cloudstar.rightcloud.oss.common.mybatis.annotation.DataFilter;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.CatalogRelationOrg;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCatalogRelation;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategoryCatalog;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.oss.module.tag.bean.ServiceCatalogGroupList;

import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

/**
 * ServiceCatalogMapper
 *
 * <AUTHOR>
 * @date 2018/9/26
 */
@Repository
public interface ServiceCatalogMapper {
    List<ServiceCategoryCatalog> selectByParams(Criteria criteria);

    int countByParams(Criteria criteria);

    int deleteProjectCatalogRelation(Long id);

    int deleteServiceCatalogRelation(Criteria criteria);

    int deleteServiceCategoryCatalog(Long id);

    int createServiceCatalog(ServiceCategoryCatalog serviceCategoryCatalog);

    int insertServiceCatalogRelation(ServiceCatalogRelation serviceCatalogRelation);

    int insertProjectCatalogRelation(CatalogRelationOrg projectCatalogRelation);

    ServiceCategoryCatalog selectServiceCatalog(Long id);

    @DataFilter(tableAlias = "sc")
    List<ServiceCategory> selectServiceCategoryList(Long id);

    @DataFilter(tableAlias = "sg", orgId = "")
    List<Org> selectOrgLists(Long id);

    int updateServiceCatalog(ServiceCategoryCatalog serviceCategoryCatalog);

    int uniqueJudgement(Criteria criteria);

    @DataFilter(tableAlias = "B")
    List<Long> selectCatalogRelationService(Long catalogId);

    List<ServiceCategoryCatalog>  selectServiceCatalogRelation(Criteria criteria);

    int insertMulti(List<ServiceCategoryCatalog> serviceCategoryCatalogs);

    List<Map<String, String>> getCatalogWithCategory(Criteria criteria);

    List<ServiceCategoryCatalog> selectByServiceCategoryId(Long serviceCategoryId);

    List<Map<String, Long>> selectServerTemplateIds(Criteria criteria);

    List<ServiceCatalogGroupList> selectServiceCatalogList();
}
