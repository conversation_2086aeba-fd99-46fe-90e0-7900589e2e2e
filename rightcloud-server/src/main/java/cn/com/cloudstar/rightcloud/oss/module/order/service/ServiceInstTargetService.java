/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.order.service;

import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceInstTarget;
import java.util.List;

public interface ServiceInstTargetService {
    int insert(ServiceInstTarget serviceInstTarget);

    List<Long> getDeployInstByTargetId(String hostId, String targetType);

    List<String> getTargetInfoByDeployInstId(Criteria criteria);

    List<String> getTargetInfosByDeployInstId(Criteria criteria);

    int insert(Long sfInstId, String targetId, String targetType);

    void updateSelfInstByHostId(String hostId, String status);

    /**
     * 更新主机关联自服务实例状态
     *
     * @param hostId 主机id
     * @param status 状态值
     * @param selfInsTypes 更新的自服务实例类型,不在此类型中的自服务不会被更新状态
     */
    void updateSelfInstByHostId(String hostId, String status, List<String> selfInsTypes);

    /**
     * 更新自服务实例状态
     *
     * @param hostId resVmId
     * @param status 状态
     * @param upStatusWhenServiceInstIsSettingOrUnsubscribing 在自服务实例是变更中或退订时，是否更新状态 true：更新 false：不更新
     */
    void updateSelfInstByHostId(String hostId, String status, boolean upStatusWhenServiceInstIsSettingOrUnsubscribing);

    void updateSelfInstByClusterId(Long clusterId, String status);
}
