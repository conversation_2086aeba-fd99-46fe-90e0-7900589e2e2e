package cn.com.cloudstar.rightcloud.oss.ccsp.strategy;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * @description: AbstractCCSPStrategy
 * @author: ouyonghui
 * @date: 2023/3/31 16:00
 */
@Slf4j
public abstract class AbstractCCSPStrategy<T, D> {

    /**
     * 获取策略
     * @return String
     */
    public abstract String getStrategy();

    /**
     * 验证是否需要加解密
     * @param data data
     * @return Boolean
     */
    public abstract Boolean needEncryptDecrypt(T data);

    /**
     * 国密处理
     * @param data data
     * @return T
     */
    public abstract T handle(T data);

    /**
     * 国密处理
     * @param data
     * @return T
     */
    public <R> T handle(D d, T data, Function<T, R> function) {
        log.debug("executing AbstractCCSPStrategy handle ,class[{}],param[{}]", data.getClass(), d);
        primaryKeyCheck(d);
        R source = function.apply(null);
        if (source != null) {
            BeanUtil.copyProperties(source, data, CopyOptions.create().setOverride(false));
            return data;
        }
        log.error("executing AbstractCCSPStrategy handle error,class[{}],param[{}]", d, data.getClass());
        return data;
    }

    /**
     * 主键检查
     */
    public void primaryKeyCheck(D d) {
        if (d == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_633977107));
        }
        if (d instanceof String) {
            if (StringUtils.isBlank((String)d)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_633977107));
            }
        }
    }
}
