<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.account.dao.policy.PolicyMapper">

    <select id="selectUserIdsByParam" resultType="java.lang.Long">

        select user_sid
        from sys_m_user
        where parent_sid = #{userId}
--           and certification_status = "authSucceed"
          and status = 1 and  user_sid in(select sug.user_sid
                                          from sys_m_policy sp
                                                   inner join sys_m_policy_group spg on sp.policy_sid = spg.policy_sid
                                                   inner join sys_m_user_group sug on sug.group_sid=spg.group_sid
                                          where policy_name in ('HPCOperatorAccess','HPCAdminAccess')
                                          union
                                          select spu.user_sid
                                          from sys_m_policy sp
                                                   inner join sys_m_policy_user spu on sp.policy_sid=spu.policy_sid
                                          where policy_name in ('HPCOperatorAccess','HPCAdminAccess'));
    </select>
    <select id="getResourceTypesByPolicies" resultType="java.lang.String">
        select resource_types from sys_m_policy where policy_sid in
        <foreach collection="policies" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>


</mapper>
