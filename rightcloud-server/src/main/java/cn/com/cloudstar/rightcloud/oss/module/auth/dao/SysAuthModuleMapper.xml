<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.auth.dao.SysAuthModuleMapper">
    <resultMap type="cn.com.cloudstar.rightcloud.oss.module.auth.dto.SysAuthModule" id="SysAuthModuleMap">
        <result property="authKey" column="auth_key" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="moduleType" column="module_type" jdbcType="VARCHAR"/>
        <result property="requestType" column="request_type" jdbcType="VARCHAR"/>
        <result property="requestUrl" column="request_url" jdbcType="VARCHAR"/>
        <result property="moduleUrl" column="module_url" jdbcType="VARCHAR"/>
        <result property="displayType" column="display_type" jdbcType="VARCHAR"/>
        <result property="component" column="component" jdbcType="VARCHAR"/>
        <result property="dashboardId" column="dashboard_id" jdbcType="VARCHAR"/>
        <result property="parentAuthKey" column="parent_auth_key" jdbcType="VARCHAR"/>
        <result property="resourceColumnId" column="resource_column_id" jdbcType="BIGINT"/>
        <result property="sortRank" column="sort_rank" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="descriptionEn" column="description_en" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="shortcutName" column="shortcut_name" jdbcType="VARCHAR"/>
        <result property="collapseMenu" column="collapse_menu" jdbcType="VARCHAR"/>
        <result property="routerQuery" column="router_query" jdbcType="VARCHAR"/>
        <result property="displayStatus" column="display_status" jdbcType="VARCHAR"/>
        <result property="serviceDirectoryId" column="service_directory_id" jdbcType="VARCHAR"/>
        <result property="consoleLink" column="console_link" jdbcType="VARCHAR"/>
        <result property="moduleCategory" column="module_category" jdbcType="VARCHAR"/>
        <result property="roleSid" column="role_sid" jdbcType="BIGINT"/>
        <result property="paidModuleName" column="paid_module_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.moduleSids != null">
                AND auth_key IN
                <foreach item="item" index="index" collection="condition.moduleSids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.stand != null">
                AND auth_key NOT IN (select auth_key from sys_auth_module where paid_module_name is not null)
            </if>
        </trim>
    </sql>

    <sql id="Base_Column_List">
    auth_key,name,name_en,icon,module_type,request_type,request_url,module_url,display_type,component,dashboard_id,
    parent_auth_key,resource_column_id,sort_rank,status,description,description_en,created_by,created_dt,updated_by,
    updated_dt,version,shortcut_name,collapse_menu,router_query,display_status,service_directory_id,console_link,module_category,paid_module_name
    </sql>

    <select id="SysAuthModuleByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
        resultMap="SysAuthModuleMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_auth_module
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectByPrimaryKey" resultMap="SysAuthModuleMap">
        select
        <include refid="Base_Column_List"/>
        from sys_auth_module
        where auth_key = #{authKey}
    </select>

    <!-- 查询当前用户的可访问的菜单权限 -->
    <select id="selectUserMenus" resultMap="SysAuthModuleMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        distinct
        A.auth_key,
        A.name,
        A.name_en,
        A.parent_auth_key,
        A.icon,
        A.module_type,
        A.request_type,
        A.module_url,
        A.request_url,
        A.component,
        A.resource_column_id,
        A.sort_rank,
        A.status,
        A.display_status,
        A.router_query,
        A.display_location,
        A.description,
        A.module_category,
        A.collapse_menu,
        A.dashboard_id,
        A.description_en,
        A.display_type,
        A.service_directory_id,
        A.shortcut_name,
        A.ex_attribute,
        D.role_sid
        FROM sys_auth_module A
        LEFT JOIN sys_auth_module B ON B.auth_key = A.parent_auth_key
        JOIN sys_role_module_rela C ON C.auth_key = A.auth_key
        JOIN sys_m_role D ON D.role_sid = C.role_id
        JOIN sys_m_user_role E ON E.role_sid = D.role_sid
        WHERE A.module_type is not null and A.status = 'enable' and E.user_sid = #{condition.userSid}
        <if test="condition.orgSid != null">
            and E.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.orgType != null and condition.orgType == 'company'">
            and A.auth_key not like 'MP%'
        </if>
        <if test="condition.orgType != null and condition.orgType == 'project'">
            and A.auth_key not like 'MG%'
        </if>
        <if test="condition.orgType != null and condition.orgType == 'department'">
            and A.auth_key not like 'MG%' and A.auth_key not like 'MP%'
        </if>
        order by A.sort_rank, A.auth_key
    </select>

    <insert id="create" keyProperty="authKey" useGeneratedKeys="false">
        insert into sys_auth_module(auth_key,name, name_en, parent_auth_key, icon, module_type, request_type,request_url, module_url, display_type, component, dashboard_id, resource_column_id, sort_rank, status, description, description_en, created_by, created_dt, updated_by, updated_dt, version,display_location,shortcut_name,collapse_menu,router_query, display_status,service_directory_id,console_link,module_category)
        values (#{authKey},#{name}, #{nameEn}, #{parentAuthKey}, #{icon}, #{moduleType}, #{requestType}, #{requestUrl}, #{moduleUrl}, #{displayType}, #{component}, #{dashboardId}, #{resourceColumnId}, #{sortRank}, #{status}, #{description}, #{descriptionEn}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{version}, #{displayLocation}, #{shortcutName}, #{collapseMenu}, #{routerQuery}, #{displayStatus},#{serviceDirectoryId},#{consoleLink},#{moduleCategory})
    </insert>

    <update id="update">
        update sys_auth_module
        <set>
            <if test="authKey != null and authKey != ''">
                auth_key = #{authKey},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="parentAuthKey != null and parentAuthKey != ''">
                parent_auth_key = #{parentAuthKey},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="moduleType != null and moduleType != ''">
                module_type = #{moduleType},
            </if>
            <if test="requestType != null and requestType != ''">
                request_type = #{requestType},
            </if>
            <if test="requestUrl != null and requestUrl != ''">
                request_url = #{requestUrl},
            </if>
            <if test="moduleUrl != null and moduleUrl != ''">
                module_url = #{moduleUrl},
            </if>
            <if test="displayType != null and displayType != ''">
                display_type = #{displayType},
            </if>
            <if test="component != null and component != ''">
                component = #{component},
            </if>
            <if test="dashboardId != null and dashboardId != ''">
                dashboard_id = #{dashboardId},
            </if>
            <if test="resourceColumnId != null">
                resource_column_id = #{resourceColumnId},
            </if>
            <if test="sortRank != null">
                sort_rank = #{sortRank},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="descriptionEn != null and descriptionEn != ''">
                description_en = #{descriptionEn},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="displayLocation != null">
                display_location = #{displayLocation},
            </if>
            <if test="shortcutName != null">
                shortcut_name = #{shortcutName},
            </if>
            <if test="collapseMenu != null">
                collapse_menu = #{collapseMenu},
            </if>
            <if test="routerQuery != null">
                router_query = #{routerQuery},
            </if>
            <if test="serviceDirectoryId != null">
                service_directory_id = #{serviceDirectoryId},
            </if>
            <if test="consoleLink != null">
                console_link = #{consoleLink},
            </if>
            <if test="displayStatus != null">
                display_status = #{displayStatus},
            </if>
        </set>
        where auth_key = #{authKey}
    </update>

    <update id="updateStatusByAuthKey">
        update sys_auth_module
        set status = #{status},
            is_auth =
        <choose>
            <when test="status == 'enable'">
                1
            </when>
            <otherwise>
                0
            </otherwise>
        </choose>
        where auth_key in
        <foreach collection="authKeyList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectAuthKeyList" resultType="java.lang.String">
        select auth_key from sys_auth_module
    </select>

    <update id="updateById">
        update sys_auth_module
        set
            name = #{name},
            name_en = #{nameEn},
            icon = #{icon},
            module_type = #{moduleType},
            request_type = #{requestType},
            request_url = #{requestUrl},
            module_url = #{moduleUrl},
            display_type = #{displayType},
            component = #{component},
            dashboard_id = #{dashboardId},
            resource_column_id = #{resourceColumnId},
            sort_rank = #{sortRank},
            status = #{status},
            description = #{description},
            description_en = #{descriptionEn},
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            display_location = #{displayLocation},
            shortcut_name = #{shortcutName},
            collapse_menu = #{collapseMenu},
            router_query = #{routerQuery},
            service_directory_id = #{serviceDirectoryId},
            console_link = #{consoleLink},
            display_status = #{displayStatus}
        where auth_key = #{authKey}
    </update>

    <delete id="deleteById">
        delete from sys_auth_module where auth_key = #{authKey}
    </delete>

    <select id="selectByPaidModuleName" resultMap="SysAuthModuleMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_auth_module where paid_module_name is not null
    </select>

</mapper>
