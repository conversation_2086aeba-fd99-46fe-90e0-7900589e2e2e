/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.bean.request.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * The type DescribeEnvToProjectRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/17
 */
@Data
@ApiModel(description = "查询关联到项目上的云环境")
public class DescribeEnvToProjectRequest {
    /**
     * 是否按只读模式查询，当前模式下，只查询云环境关联到了哪些项目上，与分区无关
     */
    @ApiModelProperty("是否按只读模式查询，当前模式下，只查询云环境关联到了哪些项目上，与分区无关")
    private boolean readOnly = false;

    /**
     * 云环境ID
     */
    @NotNull
    @ApiModelProperty("云环境ID")
    private Long cloudEnvId;

    /**
     * 分区ID
     */
    @ApiModelProperty("分区ID")
    private Long resPoolId;
}
