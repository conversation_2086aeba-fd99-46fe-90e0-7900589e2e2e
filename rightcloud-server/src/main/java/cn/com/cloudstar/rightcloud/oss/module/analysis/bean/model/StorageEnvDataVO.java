/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@ApiModel(description = "云环境硬盘资源总分布")
public class StorageEnvDataVO {

    /**
     * 云环境名称
     */
    @ApiModelProperty("云环境名称")
    private String name;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Long value;

    /**
     * 各云环境的数据存储使用情况
     */
    @ApiModelProperty("各云环境的数据存储使用情况")
    private Map<String, Object> resourceStatStorage;

    /**
     * 存储统计数据
     */
    @ApiModelProperty("存储统计数据")
    private List<NameValueVO> storageStatusList;

    /**
     * 存储类型数据分布
     */
    @ApiModelProperty("存储类型数据分布")
    private List<NameValueVO> storageTypeList;

    /**
     * vmwarwe的存储量
     */
    @ApiModelProperty("vmwarwe的存储量")
    private List<Map<String, Object>> vmStorageList;
}
