/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.feign.bean.request;

import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;


/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/06/30 13:38
 */
@Data
@ApiModel("变更询价")
public class ModifyInquiryPriceRequest {

    @ApiModelProperty("资源Id")
    @NotBlank
    @Length(max = 128,message = "资源Id长度不能超过128")
    private String id;

    /**
     * 目标类型
     */
    @EnumValue(strValues = {"DRP", "ModelArts", "SFS2.0"}, message = "该产品不存在")
    @ApiModelProperty("目标类型")
    @NotBlank
    private String targetType;

    @ApiModelProperty("目标大小")
    @NotNull
    private Integer targetSize;

    @ApiModelProperty("有系统盘")
    private boolean hasSystemDisk;

    @ApiModelProperty("有数据磁盘")
    private boolean hasDataDisk;

    @ApiModelProperty("有eip")
    private boolean hasEip;


    /**
     * 代客下单管理员sid
     */
    @ApiModelProperty("代客下单管理员ID")
    @NotNull
    private Long behalfUserSid;

    /**
     * 用户sid
     */
    @ApiModelProperty("下单用户ID")
    @NotNull
    private Long userSid;

    /**
     * 用户组织sid
     */
    @ApiModelProperty("下单用户组织ID")
    @NotNull
    private Long userOrgSid;

    /**
     * 合同id
     */
    @ApiModelProperty("合同ID")
    private Long contractId;

    /**
     * 合同价格
     */
    @ApiModelProperty("合同价格")
    private BigDecimal contractPrice;

}
