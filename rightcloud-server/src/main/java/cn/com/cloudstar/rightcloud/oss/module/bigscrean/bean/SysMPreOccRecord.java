/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * sys_m_pre_occ_record
 * <AUTHOR>
@Data
public class SysMPreOccRecord implements Serializable {
    private Long id;

    /**
     * 有效算力
     */
    private String cueValue;

    /**
     * 卡数
     */
    private Integer card;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createdDt;

    private static final long serialVersionUID = 1L;
}