package cn.com.cloudstar.rightcloud.oss.module.schema.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

import cn.com.cloudstar.rightcloud.oss.module.schema.entity.SysMUserGroupOa;

/**
 * oa组织表(SysMUserGroupOa)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-21 12:19:05
 */
@Repository
public interface SysMUserGroupOaMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param orgId 主键
     * @return 实例对象
     */
    SysMUserGroupOa queryById(Long orgId);

    /**
     * 查询指定行数据
     *
     * @param sysMUserGroupOa 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<SysMUserGroupOa> queryAllByLimit(SysMUserGroupOa sysMUserGroupOa, @Param("pageable") Pageable pageable);

    /**
     * 查询指定行数据
     *
     * @param sysMUserGroupOa 查询条件
     * @return 对象列表
     */
    List<SysMUserGroupOa> queryAllByOa(SysMUserGroupOa sysMUserGroupOa);

    SysMUserGroupOa queryAllByaccount(String orgName);

    /**
     * 统计总行数
     *
     * @param sysMUserGroupOa 查询条件
     * @return 总行数
     */
    long count(SysMUserGroupOa sysMUserGroupOa);

    /**
     * 新增数据
     *
     * @param sysMUserGroupOa 实例对象
     * @return 影响行数
     */
    long insert(SysMUserGroupOa sysMUserGroupOa);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SysMUserGroupOa> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SysMUserGroupOa> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SysMUserGroupOa> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SysMUserGroupOa> entities);

    /**
     * 修改数据
     *
     * @param sysMUserGroupOa 实例对象
     * @return 影响行数
     */
    int update(SysMUserGroupOa sysMUserGroupOa);

    /**
     * 通过主键删除数据
     *
     * @param orgId 主键
     * @return 影响行数
     */
    int deleteById(Long orgId);

}

