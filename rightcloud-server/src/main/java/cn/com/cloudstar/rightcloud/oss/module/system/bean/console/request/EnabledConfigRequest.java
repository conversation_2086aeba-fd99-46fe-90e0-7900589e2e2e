/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.console.request;

import cn.com.cloudstar.rightcloud.oss.module.system.bean.console.valid.EnvConfigIdValidView;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.console.valid.ModuleIdValidView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 启用或禁用配置
 * <AUTHOR>
 */
@ApiModel(description = "启用或禁用配置")
@Setter
@Getter
public class EnabledConfigRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模块功能ID
     */
    @ApiModelProperty("模块功能ID")
    @NotBlank(groups = {ModuleIdValidView.class})
    private String sid;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    @NotBlank
    private String enabled;

    /**
     * 云环境类型
     */
    @ApiModelProperty("云环境类型")
    @NotNull(groups = {EnvConfigIdValidView.class})
    private String cloudEnvType;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
