package cn.com.cloudstar.rightcloud.oss.ccsp;

import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPMessageUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.OtherMsg.BSSMGT_DATA_TAMPERING_ERROR;
import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.OtherMsg.CCSP_UPGRADE_STATUS;
import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * @description: CCSPMessage
 * @author: ouyonghui
 * @date: 2023/5/5 11:47
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CCSPMessage {
    private final RabbitTemplate rabbitTemplate;
    private final UserMapper userMapper;

    /**
     * 发送升级消息
     *
     * @param ccspUpgradeStatus ccspUpgradeStatus
     * @return void
     */
    public void sendUpgradeMessage(String ccspUpgradeStatus) {
        Map<String, String> messageContent = new HashMap(8);
        messageContent.put("ccspUpgradeStatus", ccspUpgradeStatus);
        try {

            // 所有管理员
            List<Long> userSids = userMapper.selectUserSidByDataScope();
            if (userSids.isEmpty()) {
                return;
            }
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(CCSP_UPGRADE_STATUS);
            baseNotificationMqBean.getToUserIds().addAll(userSids);
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setSnapshotValue(-1L);
            baseNotificationMqBean.setEntityId(1L);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
        } catch (Exception e) {
            log.error("国密升级结果发送信息异常：{}", e.getMessage());
        }
    }

    /**
     * 发送CCSP Mac验证失败消息
     *
     * @param data data
     * @return void
     */
    public void sendCCSPMacVerifyFailedMessage(Object data) {
        boolean needSendMessage = CCSPMessageUtil.checkNeedSendMessage(data);
        if (!needSendMessage) {
            return;
        }

        Map<String, String> messageContent = new HashMap(8);
        messageContent.put("dataType", data.getClass().getSimpleName() + "(ID:" + CCSPMessageUtil.getPrimaryValue(data) + ")");
        try {
            // 所有管理员
            List<Long> userSids = userMapper.selectUserSidByDataScope();
            if (userSids.isEmpty()) {
                return;
            }
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(BSSMGT_DATA_TAMPERING_ERROR);
            baseNotificationMqBean.getToUserIds().addAll(userSids);
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setSnapshotValue(-1L);
            baseNotificationMqBean.setEntityId(1L);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.WARNING, baseNotificationMqBean);
        } catch (Exception e) {
            log.error("国密升级结果发送信息异常：{}", e.getMessage());
        }
    }
}
