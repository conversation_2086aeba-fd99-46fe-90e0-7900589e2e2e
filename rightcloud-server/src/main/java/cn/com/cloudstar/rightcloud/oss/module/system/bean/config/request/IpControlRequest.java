/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.config.request;

import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.model.IpAddressVO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <Description> IP访问控制信息 <br>
 *
 * <AUTHOR>
 * @createDate 2021/9/10
 */
@Data
public class IpControlRequest implements Serializable {
    /**
     * 控制平台类型（console/mgt）
     */
    @NotBlank
    private String controlType;
    /**
     * 启用IP访问控制(0:关闭 1：开启)
     */
    private Integer ipControlFlg;
    /**
     * 单一IP请求速率设置
     */
    private Integer ipRequestLimit;
    /**
     * 访问控制类型（ 0:允许访问的IP地址/网段 1:拒绝访问的IP地址/网段）
     */
    private Integer ipType;
    /**
     * IP控制名单
     */
    @Valid
    private IpAddressVO ipInfo;
}
