/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.core.out.share;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareSumInfo;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.oceanstor.ResOceanstorShare;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ModifyShareParam;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResShareParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.resource.service.share.ShareService;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/24
 */
@Component
@DubboService
@Slf4j
public class ShareRemoteServiceImpl implements ShareRemoteService {

    @Autowired
    private ShareService shareService;

    @Override
    public Boolean deleteShareById(Long id) {
        return shareService.deleteShareById(id);
    }

    @Override
    public ResShare selectByPrimaryKey(Long id) {
        ResShare resShare = shareService.selectByPrimaryKey(id);
        log.debug("resShare 查询:{}", JSONUtil.toJsonStr(resShare));
        return resShare;
    }

    @Override
    public RestResult createShare(ResShare resShare) {
        return shareService.createShare(resShare);
    }

    @Override
    public void checkShareCanDelete(Long id) {
        shareService.checkShareCanDelete(id);
    }

    @Override
    public int countByParams(ResShareParams criteria) {
        return shareService.countByParams(Criteria.prepareNewCriteria(criteria));
    }

    @Override
    public BigDecimal sumSizeByIds(List<Long> ids) {
        log.info("sum size");
        return shareService.sumSizeByIds(ids);
    }

    @Override
    public int updateByPrimaryKey(ResShare resShare) {
        return shareService.updateByPrimaryKey(resShare);
    }

    @Override
    public void createShareByOceanStor(ResOceanstorShare oceanstorShare) {
        shareService.createShareByOceanStor(oceanstorShare);
    }

    @Override
    public Boolean deleteShareByOceanStor(ResOceanstorShare oceanstorShare) {
        return shareService.deleteShareByOceanStor(oceanstorShare);
    }

    /**
     * 同步resShare数据
     *
     * @param id
     * @return
     */
    @Override
    public ResShare syncResShareByShareId(Long id) {
        return shareService.syncResShareByShareId(id);
    }

    @Override
    public List<ResShare> getShareList(ResShareParams criteria) {
        return shareService.getShareList(Criteria.prepareNewCriteria(criteria));
    }

    @Override
    public RestResult action(Long id, Integer size) {
        shareService.action(id, size);
        return new RestResult();
    }

    @Override
    public RestResult modify(ModifyShareParam modifyShareParam) {
        return new RestResult(shareService.modify(modifyShareParam));
    }

    @Override
    public int countShareByParams(ResShareParams resShare) {
        return shareService.countShareByParams(Criteria.prepareNewCriteria(resShare));
    }

    @Override
    public List<ResShare> selectByParams(Criteria criteria){
        return shareService.selectByParams(criteria);
    }

    @Override
    public RestResult deleteShareByIdAndOrderId(Long id, Long orderId) {
        return shareService.deleteShareByIdAndOrderId(id, orderId);
    }

    @Override
    public ResShareSumInfo sumSizeByIdGroupShareType(List<Long> allSFSIds, Integer hpcVersion) {
        return null;
    }

    @Override
    public List<Long> getShareIdsByClusterIds(List<Long> hpcClusterIds, String productType) {
        return null;
    }

    @Override
    public Long getByResourceIdAndClusterType(Long resourceId, String resourceType) {
        return null;
    }

    @Override
    public List<ResShare> getByIds(List<Long> shareIds) {
        return null;
    }
}
