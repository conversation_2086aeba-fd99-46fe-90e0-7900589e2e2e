/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.constants.type;

/**
 * DESC:部署任务类型
 *
 * <AUTHOR>
 * @date 2019 /07/12 15:24
 */
public interface DeployTaskType {

    /**
     * The constant 创建.
     */
    String CREATE_HOST = "createHost";
    /**
     * The constant 重启.
     */
    String RESTART_HOST = "restartHost";
    /**
     * The constant 删除.
     */
    String DELETE_HOST = "deleteHost";
    /**
     * The constant 更改配置.
     */
    String RE_CONFIG_HOST = "reConfigHost";
    /**
     * The constant 纳管.
     */
    String IMPORT_HOST = "importHost";
    /**
     * The constant 安装监控组件.
     */
    String INSTALL_MONITOR = "installMonitor";
    /**
     * The constant 安装容器组件.
     */
    String INSTALL_DOCKER = "installDocker";
    /**
     * The constant 卸载监控组件.
     */
    String UNINSTALL_AGENT = "unInstallAgent";
    /**
     * The constant 运行启动脚本.
     */
    String BOOT_SCRIPT = "bootScript";
    /**
     * The constant 运行结束脚本.
     */
    String DECOMMISSION_SCRIPT = "decommissionScript";

    /**
     * The constant 运行执行脚本.
     */
    String EXEC_SCRIPT = "execScript";

    /**
     * The constant 运维自动化.
     */
    String EXEC_JOB = "execJob";

    /**
     * The constant 部署应用.
     */
    String DEPOLY_APP = "depolyApp";

    /**
     * The constant 释放应用.
     */
    String RELEASE_APP = "releaseApp";
    /**
     * The constant 脱管主机.
     */
    String RELEASE_COMPONENT = "releaseComponent";
    /**
     * The constant 集群任务.
     */
    String CLUSTER_SETUP = "clusterSetup";
    /**
     * The constant 集群合并任务.
     */
    String CLUSTER_JOIN = "clusterJoin";
    /**
     * The constant 环境同步.
     */
    String SYNC_ENV = "syncEnv";
    /**
     * The constant 编排脚本合并任务.
     */
    String LAYOUT_SCRIPT_JOIN = "layoutScriptJoin";
    /**
     * 重置密码
     */
    String RESET_VM_CIPHER = "resetVmPassword";
    /**
     * 重置hostName
     */
    String RESET_VM_HOSTNAME = "resetVmHostName";

    /**
     * 克隆为模板
     */
    String CLONE_AS_TEMPLATE = "cloneAsTemplate";

    /**
     * The constant STOP_HOST.
     */
    String STOP_HOST = "stopHost";

    /**
     * 挂载弹性文件
     */
    String MOUNT_SFS = "mountSfs";

    /**
     * 卸载弹性文件
     */
    String UN_MOUNT_SFS = "unMountSfs";

    /**
     * 续费
     */
    String RENEW = "renew";

    /**
     * 成本明细同步
     */
    String SYNC_COST_DETAIL = "syncCostDetail";

    /**
     * 创建我的编排
     */
    String CREATE_STACK = "createStack";

    /**
     * The constant 启动.
     */
    String START_HOST = "startHost";

    /**
     * The constant 启动.
     */
    String INSTALL_NODE_EXPORTER = "installNodeExporter";

    /**
     * 重装系统
     */
    String REINSTALL_SYSTEM = "reinstallSystem";

    String INSTALL_SNMP = "install_snmp";
}
