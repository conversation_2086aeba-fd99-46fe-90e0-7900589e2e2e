/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.constants.res.type;

/**
 * DESC:路由器接口类型
 *
 * <AUTHOR>
 * @date 2019/07/12 15:03
 */
public interface ResRouterInterfaceType {

    /**
     * router_interface 内部接口
     */
    String ROUTERINTERFACE = "network:router_interface";

    /**
     * router_gateway 外部网关
     */
    String ROUTERGATEWAY = "network:router_gateway";

    /**
     * inner 内部接口
     */
    String INNER = "inner";

    /**
     * public 外部网关
     */
    String PUBLIC = "public";

    /**
     * failure 创建失败
     */
    String FAILURE = "failure";

    /**
     * pending 创建中
     */
    String PENDING = "pending";

    /**
     * Custom 用户自定义
     */
    String CUSTOM = "Custom";
}
