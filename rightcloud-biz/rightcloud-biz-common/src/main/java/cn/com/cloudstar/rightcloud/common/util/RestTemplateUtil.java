/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.util;


import cn.com.cloudstar.rightcloud.common.certificate.CustomizeTrustStrategy;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.lang.Nullable;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@Slf4j
public class RestTemplateUtil {


    private static String HEADER_USER_ID = "header_user_id";

    private static  String HEADER_ORG = "header_org_id";

    /**
     * 使用feign调用
     */
    private static String USE_FEIGN = "use_feign";

    /**
     * 使用feign调用
     */
    private static String REQUST_TYPE = "RequstType";

    private static final Integer RETRYCOUNT = 5;

    private static RestTemplateUtil restTemplateUtil = new RestTemplateUtil();

    private static RestTemplate restTemplate = new RestTemplateBuilder().build();

    static {

        try {
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, CustomizeTrustStrategy.INSTANCE).build();
            SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            HttpClientBuilder httpClientBuilder = HttpClients.custom();
            httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
            CloseableHttpClient httpClient = httpClientBuilder.build();
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);

            // 设置5s timeout
            requestFactory.setReadTimeout(60000);
            requestFactory.setConnectTimeout(15000);
            restTemplate.setRequestFactory(requestFactory);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private RestTemplateUtil() {

    }

    /**
     * 发送POST请求
     *
     * @param url  路径
     * @param maps 参数
     * @param body 参数(JSON字符串)
     * @return 结果
     */
    public static String sendHttpRequest(String url, Map<String, Object> maps, HttpMethod httpMethod, String body, Long userId, Long orgSid) {
        //rds的变配接口被扩容占用了 现在加了变更规格修改url
        if (StrUtil.endWith(url, "enlarge_volume")) {
            boolean rds = StrUtil.contains(body, "RDS");
            boolean specCode = StrUtil.contains(body, "specCode");
            boolean contains = StrUtil.contains(body, "rds.");
            if (rds && specCode && contains) {
                url = url.replace("enlarge_volume", "resize_flavor");
            }
        }

        log.info("RestTemplateUtil.sendHttpRequest url: [{}], userSid:[{}], orgSid:[{}]", url, userId, orgSid);
        // 3.发送
        // 3.2发起请求
        HttpEntity<String> httpEntity = getHttpEntity(body, userId, orgSid);
        ResponseEntity<String> response;
        if (HttpMethod.POST.matches(httpMethod.name()) || HttpMethod.PUT.matches(httpMethod.name())) {
            response = restTemplate.exchange(url, httpMethod, httpEntity, String.class);
        } else {
            response = restTemplate.exchange(url, httpMethod, httpEntity, String.class, maps);
        }
        // 若不成功
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            log.error("Response status received:{}, response body:{}", response.getStatusCode(), response.getBody());
            throw new BizException(response.getBody());
        }

        String responseBody = response.getBody();

        log.info("RestTemplateUtil.sendHttpRequest url: [{}], responseBody:[{}]", url, responseBody);

        return responseBody;
    }

    /**
     * 获取HttpEntity
     * @param param param
     * @return
     */
    public static HttpEntity<String> getHttpEntity(String param, Long userId, Long orgSid) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add(HEADER_USER_ID, String.valueOf(userId));
        headers.add(HEADER_ORG, String.valueOf(orgSid));
        headers.add(USE_FEIGN, "true");
        headers.add(REQUST_TYPE, "feign.route.tag");
        headers.add("AUTH_CLIENT", "inline");
        return new HttpEntity<>(param, headers);
    }

    public static RestTemplateUtil retriableInstance() {
        return restTemplateUtil;
    }

    public static RestTemplate instance() {
        return restTemplate;
    }

    public <T> T getForObject(URI url, Class<T> responseType) throws Exception {
        AtomicReference<T> t = new AtomicReference<>();
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            t.set(restTemplate.getForObject(url, responseType));
        });
        return t.get();
    }

    public <T> T getForObject(String url, Class<T> responseType) throws Exception {
        AtomicReference<T> t = new AtomicReference<>();
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            t.set(restTemplate.getForObject(url, responseType));
        });
        return t.get();
    }

    public <T> T postForObject(String url, @Nullable Object request, Class<T> responseType, Object... uriVariables)
            throws Exception {
        AtomicReference<T> t = new AtomicReference<>();
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            t.set(restTemplate.postForObject(url, request, responseType, uriVariables));
        });

        return t.get();
    }

    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, @Nullable HttpEntity<?> requestEntity,
                                          Class<T> responseType, Object... uriVariables) throws Exception {
        AtomicReference<ResponseEntity<T>> t = new AtomicReference<>();
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            t.set(restTemplate.exchange(url, method, requestEntity, responseType, uriVariables));
        });
        return t.get();
    }

    public <T> ResponseEntity<T> exchange(URI url, HttpMethod method, @Nullable HttpEntity<?> requestEntity,
                                          Class<T> responseType) throws Exception {
        AtomicReference<ResponseEntity<T>> t = new AtomicReference<>();
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            t.set(restTemplate.exchange(url, method, requestEntity, responseType));
        });
        return t.get();
    }

    public void delete(String url, Object... uriVariables) throws Exception {
        retry(RETRYCOUNT, 1, TimeUnit.SECONDS, () -> {
            restTemplate.delete(url, uriVariables);
        });

    }

    private void retry(int retryCount, long interval, TimeUnit timeUnit, ExecuteFunction function) throws Exception {
        if (function == null) {
            return;
        }

        for (int i = 0; i < retryCount; i++) {
            try {
                function.execute();
                break;
            } catch (Exception e) {
                if (shouldStopRetrying(retryCount, interval, timeUnit, i)) {
                    break;
                }
            }
        }
    }

    private static boolean shouldStopRetrying(int retryCount, long interval, TimeUnit timeUnit, int i) {
        if (i == retryCount - 1) {
            return true;
        } else if (timeUnit != null && interval > 0L) {
            try {
                timeUnit.sleep(interval);
            } catch (InterruptedException e1) {
                log.error(e1.getMessage(), e1);
                Thread.currentThread().interrupt();
            }
        }
        return false;
    }

    @FunctionalInterface
    interface ExecuteFunction {

        /**
         * Apply function
         *
         * @return result
         *
         * @throws Exception
         */
        void execute() throws Exception;
    }

}
