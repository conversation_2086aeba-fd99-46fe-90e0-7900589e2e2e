package com.cloudstar.rightcloud.resource.client.credential.param;

import lombok.Data;

import java.util.Map;

/**
 * 下发
 *
 * <AUTHOR>
 * @date 2023/9/18 17:57
 */
@Data
public class CredentialSendParam {

    /**
     * 变更记录 id
     */
    private Long id;
    /**
     * 资源id
     */
    private Long resourceId;
    /**
     * 凭证id
     */
    private Long credentialId;
    /**
     * 新凭证
     */
    private Long newCredentialId;
    /**
     * 修改类型: modifyCredential:修改凭证; modifyResRela: 修改资源关系
     */
    private String updateType;
    /**
     * 管理凭证时, 需要更新vm表和推cmdb的数据
     */
    private Map<String, Object> data;
    /**
     * 更新的cmp表
     */
    private String tableName;
    /**
     * 端口
     */
    private Integer sshPort;

}
