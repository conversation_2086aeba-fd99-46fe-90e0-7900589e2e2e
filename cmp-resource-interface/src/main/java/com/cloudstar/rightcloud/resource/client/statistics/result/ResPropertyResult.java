package com.cloudstar.rightcloud.resource.client.statistics.result;

import lombok.Data;

/**
 * 规格属性
 *
 * <AUTHOR>
 * @since 2023/10/25 14:49
 */
@Data
public class ResPropertyResult {

    /**
     * 在页面展示,Y/N
     */
    private String gridIncluded;

    /**
     * 支持导出,Y/N
     */
    private String exportIncluded;

    /**
     * 关键属性标识，Y/N，默认为N-非关键属性
     */
    private String keyFlag;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 是否来源于模板
     */
    private Boolean fromTemplate;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 属性id
     */
    private Long id;

    /**
     * 属性中文名
     */
    private String name;

    /**
     * 属性英文名
     */
    private String nameEn;

    /**
     * 预定义分类 Y/N
     */
    private String predefined;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 属性占位提示
     */
    private String placeHolder;

    /**
     * 数据类型:
     * string：字符串，需要最大长度、最小长度校验，以及格式校验
     * bool：布尔值
     * int：整型数值，需要最大值、最小值校验，需要单位
     * float：浮点型数值，需要小数位数，需要单位
     * date：日期时间，需要时间格式
     * datadict：字典代码，需要字典代码
     * category：配置分类
     * file：文档附件
     */
    private String dataType;

    /**
     * 表单类型：
     * text
     * password
     * textarea
     * number
     * radio
     * checkbox
     * select
     * date
     * file
     */
    private String inputType;

    /**
     * 单位，引用数据字典，DICT_KEY：PROP_UNIT
     */
    private String unit;

    /**
     * 存储单位
     */
    private String storeUnit;

    /**
     * 计量单位
     */
    private String meterageUnit;

    /**
     * 统计单位
     */
    private String statisticUnit;

    /**
     * 统计单位
     */
    private String statisticUnitEn;

    /**
     * 关系映射字段名称
     */
    private String relaMappingField;

    /**
     * 通用校验信息
     * {
     * "required":true,//是否必填
     * "minLen":1,//最小长度
     * "maxLen":50,//最大长度
     * "minVal":1,//最小值
     * "maxVal":100,//最大值
     * "dtFormat":"yyyy-MM-dd HH:mm:ss",//日期格式
     * "decimalNum":2,//小数位数
     * "regular":"正则表达式"//正则表达式
     * }
     */
    private String validExpr;

    /**
     * 字典代码KEY
     */
    private String dictKey;

    /**
     * 关联资产类别
     */
    private Long categoryId;

    /**
     * 启用禁用状态,Y启用，N未启用，null未设置
     */
    private String enabled;

    /**
     * 资产类别编码
     */
    private String categoryCode;

    /**
     * 所属模板ID，一个属性可以属于0个或1个模板
     */
    private Long templateId;

    /**
     * 属性定义CODE
     */
    private String code;

    /**
     * 值区间-
     */
    private String propValueRange;

    /**
     * 默认值-
     */
    private String propValueDefault;

    /**
     * 是否变更项 -
     */
    private Boolean changeItem;

    /**
     * 变更类型 -
     */
    private String changeType;

    /**
     * 变更排序 -
     */
    private Integer changeSortRank;

    /**
     * 属性英文 描述 -
     */
    private String descriptionEn;

    /**
     * 数据类型:
     * string：字符串，需要最大长度、最小长度校验，以及格式校验
     * bool：布尔值
     * int：整型数值，需要最大值、最小值校验，需要单位
     * float：浮点型数值，需要小数位数，需要单位
     * date：日期时间，需要时间格式
     * datadict：字典代码，需要字典代码
     * category：配置分类
     * file：文档附件
     */
    private String propDataType;

    /**
     * 属性类型 -
     */
    private String propType;

    /**
     * 排序号
     */
    private Integer sortRank;

    /**
     * 表单类型：
     * text
     * password
     * textarea
     * number
     * radio
     * checkbox
     * select
     * date
     * file
     */
    private String propInputType;

    /**
     * 关联资产类别名称
     */
    private String categoryName;

}
