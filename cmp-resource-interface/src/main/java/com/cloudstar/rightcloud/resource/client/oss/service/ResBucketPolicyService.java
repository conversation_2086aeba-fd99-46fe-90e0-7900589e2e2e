package com.cloudstar.rightcloud.resource.client.oss.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.resource.client.common.param.ActionParam;
import com.cloudstar.rightcloud.resource.client.oss.param.ResBucketPolicyPageParam;
import com.cloudstar.rightcloud.resource.client.oss.ressult.ResBucketPolicyPageResult;

/**
 * 对象存储策略Service接口
 *
 * @author: chengpeng
 * @date: 2023/6/8 13:32
 */
public interface ResBucketPolicyService {
    /**
     * 分页查询桶策略
     * @param param 查询条件
     * @return 桶策略
     */
    RightCloudResult<PageResult<ResBucketPolicyPageResult>> getBucketPolicyPage(ResBucketPolicyPageParam param);

    /**
     * 创建桶策略
     * @param param 创建参数
     * @return 桶策略ID
     */
    RightCloudResult<Long> createBucketPolicy(ActionParam param);

    /**
     * 删除桶策略
     * @param param 创建参数
     * @return 桶策略ID
     */
    RightCloudResult<Void> deleteBucketPolicy(ActionParam param);

    /**
     * 更新桶策略
     * @param param 创建参数
     * @return 桶策略ID
     */
    RightCloudResult<Void> updateBucketPolicy(ActionParam param);
}
