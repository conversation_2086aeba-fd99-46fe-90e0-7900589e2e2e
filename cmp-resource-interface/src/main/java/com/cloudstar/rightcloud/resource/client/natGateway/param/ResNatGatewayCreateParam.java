package com.cloudstar.rightcloud.resource.client.natGateway.param;

import com.cloudstar.rightcloud.sdk.resource.virtualnetwork.VirtualNetworkModel;

import java.io.Serializable;

import lombok.Data;

/**
 * <p>
 * NAT网关创建参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
public class ResNatGatewayCreateParam implements Serializable {

    private static final long serialVersionUID = 6943612006840671905L;

    private Long regionId;

    /**
     * NAT网关的名字。
     * NAT网关的名字仅支持数字、字母、_（下划线）、-（中划线）、中文。
     */
    private String name;

    /**
     * NAT网关的描述。
     */
    private String description;

    private Long natTypeId;


    /**
     * 网络信息
     */
    private VirtualNetworkModel port;

    private String chargeType;
}
