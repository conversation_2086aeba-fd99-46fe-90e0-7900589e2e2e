package com.cloudstar.rightcloud.resource.client.routerroute.param;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

/**
 * 修改路由
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/17 23:23
 */
@Setter
@Getter
public class ResRouterRouteUpdateParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 路由表id
     */
    @NotNull
    private Long routerId;

    /**
     * 修改路由
     */
    private List<RouterRouteParam> modRouterRoutes;
}
