package com.cloudstar.rightcloud.resource.client.change.param;

import com.cloudstar.rightcloud.resource.common.em.ResRelationModeEnum;
import lombok.Data;

import java.util.List;

/**
 * 变更历史调整接口入参
 *
 * <AUTHOR>
 * @since 2023/9/14 15:00
 */
@Data
public class ResChangeRecordRelationParam {

    /**
     * 需要调整的项目ID集合
     */
    private List<Long> preProjectIdIn;

    /**
     * 需要调整的组织ID集合
     */
    private List<Long> preOrgIdIn;

    /**
     * 调整后项目ID
     */
    private Long postProjectId;

    /**
     * 调整后组织ID
     */
    private Long postOrgId;

    /**
     * 归属人
     */
    private String ownerAccount;

    /**
     * 变更模式：
     * <li>1. new: 添加新的分配变更记录到所选择所属关系上</li>
     * <li>1. change: 调整资源所有变更历史所属关系到所选择所属关系</li>
     */
    private ResRelationModeEnum changeMode;

}
