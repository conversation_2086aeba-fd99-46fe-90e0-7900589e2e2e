package com.cloudstar.rightcloud.resource.client.oss.ressult;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 桶分页查询返回值
 * @author: chengpeng
 * @date: 2023/6/7 14:21
 */
@Data
public class ObsUserPolicyResult implements Serializable {
    private static final long serialVersionUID = -6799272934232280080L;

    /**
     * ID
     */
    private Long id;

    private Long iamUserId;

    //对象存储名称
    private String bucketName;

    //授权类型
    private String policyType;

    //授权方式
    private String policyMethod;

    private String osName;
}
