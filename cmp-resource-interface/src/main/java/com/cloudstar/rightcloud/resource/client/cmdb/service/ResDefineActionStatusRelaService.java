package com.cloudstar.rightcloud.resource.client.cmdb.service;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.resource.client.cmdb.param.ResDefineActionStatusQueryParam;
import com.cloudstar.rightcloud.resource.client.cmdb.param.ResDefineActionStatusRelaCreateParam;
import com.cloudstar.rightcloud.resource.client.cmdb.result.ResDefineActionStatusQueryResult;

import java.util.List;

/**
 * 资源状态
 *
 * @author: chengpeng
 * @date: 2023/8/14 15:08
 */
public interface ResDefineActionStatusRelaService {
    /**
     * 获取资源状态列表
     *
     * @param param 查询参数
     * @return 资源状态列表
     */
    RightCloudResult<List<ResDefineActionStatusQueryResult>> getResDefineActionStatusList(ResDefineActionStatusQueryParam param);

    /**
     * 添加资源状态
     * @param param 资源状态信息
     */
    RightCloudResult<Void> addResourceStatus(ResDefineActionStatusRelaCreateParam param);

    /**
     * 移除资源状态
     * @param actionStatusRelaIds 资源状态信息
     */
    RightCloudResult<Void> deleteResourceStatus(String actionStatusRelaIds);
}
