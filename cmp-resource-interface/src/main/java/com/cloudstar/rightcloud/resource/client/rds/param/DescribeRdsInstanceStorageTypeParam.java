package com.cloudstar.rightcloud.resource.client.rds.param;

import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询 RDS 存储类型入参
 */
@Data
public class DescribeRdsInstanceStorageTypeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 云环境ID
     */
    @NotNull
    private Long cloudEnvId;

    /**
     * 数据库引擎
     */
    @NotNull
    private String engine;

    /**
     * 数据库版本
     */
    @NotNull
    private String engineVersion;

    /**
     * 实例类型 主备实例:ha 只读实例:replica 单实例:single
     */
    //@EnumValue(strValues = {"ha", "replica", "single"})
    @NotNull
    private String category;

    /**
     * 可用区
     */
    @NotNull
    private String az;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
