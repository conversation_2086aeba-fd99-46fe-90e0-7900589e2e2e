package com.cloudstar.rightcloud.resource.client.subnet.result;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 子网表(原network)
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Getter
@Setter
public class ResSubnetResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 云环境id;
     */
    private Long cloudEnvId;

    /**
     * 私有网络id;
     */
    private Long vpcId;

    /**
     * 路由表id
     */
    private Long routerId;

    /**
     * 底层项目Id;
     */
    private String tenantId;

    /**
     * 所属网络的id;
     */
    private String networkId;

    /**
     * 名称;
     */
    private String name;

    /**
     * uuid;
     */
    private String uuid;

    /**
     * 子网类型
     */
    private String type;

    /**
     * CIDR
     */
    private String cidr;

    /**
     * 掩码
     */
    private String mask;

    /**
     * 网关
     */
    private String gateway;

    /**
     * dns1;
     */
    private String dns1;

    /**
     * dns2;
     */
    private String dns2;

    /**
     * vlan;
     */
    private String vlan;

    /**
     * 区域
     */
    private String region;

    /**
     * 可用区
     */
    private String zone;

    /**
     * normal:可用disabled:不可用
     */
    private String status;

    /**
     * 子网地址;
     */
    private String subnet;

    /**
     * 注释;
     */
    private String comments;

    /**
     * 是否启用dhcp;
     */
    private String enableDhcp;

    /**
     * 已使用ip;
     */
    private Long usedIp;

    /**
     * 总ip数;
     */
    private Long totalIp;

    /**
     * 子网内云主机个数
     */
    private Integer vpcDevicesNum;

    /**
     * ip段;
     */
    private String ipSegment;

    /**
     * urn
     */
    private String urn;

    /**
     * urn
     */
    private String uri;

    /**
     * ipv6_cidr;
     */
    private String ipv6Cidr;

    /**
     * ipv6网关;
     */
    private String ipv6Gateway;

    /**
     * 是否开启ipv6
     */
    private String enableIpv6;

    /**
     * 标签
     */
    private String tag;

    /**
     * 高级属性
     */
    private String extra;

    /**
     * 创建人;
     */
    private String createdBy;

    /**
     * 创建时间;
     */
    private Date createdDt;

    /**
     * 更新人;
     */
    private String updatedBy;

    /**
     * 更新时间;
     */
    private Date updatedDt;

    /**
     * 版本号;
     */
    private Long version;


}
