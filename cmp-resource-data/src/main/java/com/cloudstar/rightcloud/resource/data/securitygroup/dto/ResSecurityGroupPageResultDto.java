package com.cloudstar.rightcloud.resource.data.securitygroup.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 安全组分页结果DTO
 *
 * @author: fuxingwang
 * @date: 2023/06/07  17:07
 */
@Data
public class ResSecurityGroupPageResultDto implements Serializable {

    private Long id;
    /**
     * 云环境id
     */
    private Long cloudEnvId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 原始uuid
     */
    private String uuid;

    /**
     * 资源集ID
     */
    private String projectUuid;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 原始vpc id
     */
    private Long vpcId;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 所有者账号
     */
    private String ownerAccount;
    /**
     * 底层云环境项目id
     */
    private String tenantId;
    /**
     * 安全组名称
     */
    private String name;

    /**
     * 区域;
     */
    private String region;
    /**
     * 描述
     */
    private String description;
    /**
     * 网络类型
     */
    private String netType;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 版本号
     */
    private Long version;


}
