package com.cloudstar.rightcloud.resource.data.securitygroup.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全组规则 创建
 *
 * @author: fuxingwang
 * @date: 2023/05/04  14:01
 */
@Getter
@Setter
@ToString
public class ResSecurityGroupRuleCreateDto implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 安全组id
     */
    private Long securityGroupId;

    /**
     * 所属安全组的对端id
     */
    private Long remoteGroupId;

    /**
     * 底层项目Id
     */
    private String tenantId;

    /**
     * 规则id
     */
    private String uuid;

    /**
     * 规则方向
     */
    private String direction;

    /**
     * 对端ip网段
     */
    private String remoteIpPrefix;

    /**
     * 协议类型或直接指定IP协议号
     */
    private String protocol;

    /**
     * 最大端口，当协议类型为ICMP时，该值表示ICMP的code
     */
    private String portRangeMax;

    /**
     * 最小端口，当协议类型为ICMP时，该值表示ICMP的type。
     */
    private String portRangeMin;

    /**
     * 网络类型  支持IPv4，IPv6
     */
    private String etherType;

    /**
     * 安全组规则描述
     */
    private String description;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 授权策略
     */
    private String strategy;

    /**
     * 授权对象
     */
    private String authorizationStrategy;

    /**
     * for azure:default/other
     */
    private String type;

    /**
     * for cecstack
     */
    private String icmpType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

}
