package com.cloudstar.rightcloud.resource.data.rds.dto;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Getter;
import lombok.Setter;


/**
 * 查询RDS实例 数据库列表 入参
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/5/31 15:34
 */
@Setter
@Getter
public class ResRdsDatabasePageDto extends PageForm {

    private static final long serialVersionUID = 1L;

    /**
     * rds 实例id
     */
    private Long rdsId;

    /**
     * 数据库name like
     */
    private String databaseNameLike;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
