package com.cloudstar.rightcloud.resource.data.gaussdbMysql.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cloudstar.rightcloud.resource.data.gaussdbMysql.dto.ResGaussdbMysqlFlavorRela;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
* 资源-云数据库gaussdb nosql规格;(res_gaussdb_nosql_flavor_rela)表数据库访问层
* <AUTHOR> http://www.chiner.pro
* @date : 2024-1-19
*/
@Mapper
public interface ResGaussdbMysqlFlavorRelaMapper extends BaseMapper<ResGaussdbMysqlFlavorRela>{
}
