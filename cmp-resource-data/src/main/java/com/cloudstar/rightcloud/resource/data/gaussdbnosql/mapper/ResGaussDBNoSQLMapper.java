package com.cloudstar.rightcloud.resource.data.gaussdbnosql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.rightcloud.resource.data.gaussdbnosql.dto.ResGaussDBNoSQL;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * 资源-云数据库gaussdb nosql;(res_gaussdb_nosql)表数据库访问层
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-1-12
 */
@Mapper
public interface ResGaussDBNoSQLMapper extends BaseMapper<ResGaussDBNoSQL>{

    @Update(" update res_gaussdb_nosql set org_id=#{orgId} where\n"
            + "        id  IN\n"
            + "        <foreach collection=\"resId\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "            #{item}\n"
            + "        </foreach>")
    void updateOrgIdById(Long orgId, List<Long> resId);
}
