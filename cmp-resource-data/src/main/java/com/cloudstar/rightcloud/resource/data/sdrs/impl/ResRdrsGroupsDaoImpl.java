package com.cloudstar.rightcloud.resource.data.sdrs.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.data.datascope.annotation.DataFilter;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.resource.data.sdrs.dao.ResSdrsGroupsDao;
import com.cloudstar.rightcloud.resource.data.sdrs.dto.ResSdrsGroups;
import com.cloudstar.rightcloud.resource.data.sdrs.dto.ResSdrsGroupsListQueryDto;
import com.cloudstar.rightcloud.resource.data.sdrs.dto.ResSdrsGroupsPageQueryDto;
import com.cloudstar.rightcloud.resource.data.sdrs.mapper.ResSdrsGroupsMapper;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;

/**
 * 资源存储容灾服务保护组接口实现
 *
 * @author: zqy
 * @date: 2023/12/12
 */
@Repository
public class ResRdrsGroupsDaoImpl implements ResSdrsGroupsDao {

    @Resource
    private ResSdrsGroupsMapper resSdrsGroupsMapper;

    @Override
    public ResSdrsGroups selectById(Long keypairId) {
        return resSdrsGroupsMapper.selectById(keypairId);
    }

    @Override
    @DataFilter
    public List<ResSdrsGroups> selectList(ResSdrsGroupsListQueryDto dto) {
        return resSdrsGroupsMapper.selectList(Wrappers.lambdaQuery(ResSdrsGroups.class)
                                                      .eq(Objects.nonNull(dto.getCloudEnvId()),
                                                          ResSdrsGroups::getCloudEnvId, dto.getCloudEnvId()));
    }

    @Override
    @DataFilter
    public PageResult<ResSdrsGroups> page(ResSdrsGroupsPageQueryDto dto) {
        return PageHelperUtil.doSelectPageResult(dto, "cloud_env_id asc, created_dt desc ", ResSdrsGroups.class, () -> {
            resSdrsGroupsMapper.selectList(Wrappers.lambdaQuery(ResSdrsGroups.class)
                                                   .eq(Objects.nonNull(dto.getCloudEnvId()), ResSdrsGroups::getCloudEnvId, dto.getCloudEnvId())
                                                   .eq(Objects.nonNull(dto.getOwnerId()), ResSdrsGroups::getOwnerId, dto.getOwnerId())
                                                   .in(CollUtil.isNotEmpty(dto.getOrgIdIn()), ResSdrsGroups::getOrgSid, dto.getOrgIdIn())
                                                   .like(Objects.nonNull(dto.getName()), ResSdrsGroups::getName, dto.getName())
                                                   .eq(Objects.nonNull(dto.getOrgId()), ResSdrsGroups::getOrgSid, dto.getOrgId()));
        });
    }

    @Override
    public int insert(ResSdrsGroups dto) {
        return resSdrsGroupsMapper.insert(dto);
    }

    @Override
    public void batchInsert(List<ResSdrsGroups> dtos) {
        dtos.forEach(resSdrsGroupsMapper::insert);
    }

    @Override
    public int updateById(ResSdrsGroups dto) {
        return resSdrsGroupsMapper.updateById(dto);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return resSdrsGroupsMapper.deleteBatchIds(ids);
    }
}
