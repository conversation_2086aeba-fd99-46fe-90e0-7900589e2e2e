package com.cloudstar.rightcloud.resource.data.definegrid.dto;

import lombok.Data;

import java.util.Date;

/**
 * 云资源列表查询结果
 * <AUTHOR>
 * @Package com.cloudstar.rightcloud.resource.data.actionform.dto
 * @date 2023/4/17 11:44
 */
@Data
public class DefineGridDto {
    /**
     * ID
     */
    private Long id;
    /**
     * 云资源类型编码
     */

    private String resTypeCode;
    /**
     * 列表名称
     */

    private String name;
    /**
     * 列表名称英文
     */

    private String nameEn;
    /**
     * 云平台名称
     */

    private String envName;

    /**
     * 云平台CODE
     */

    private String envCode;

    /**
     * 云平台IDS
     */

    private String envIds;

    /**
     * code
     */
    private String code;
    /**
     * 描述
     */

    private String description;
    /**
     * 状态;(enable已发布、disable已禁用)
     */

    private String status;
    /**
     * 列表基础信息
     */

    private String baseInfo;
    /**
     * 数据源设置
     */

    private String datasourceInfo;
    /**
     * 列设置
     */
    private String columnInfo;
    /**
     * 查询项设置
     */

    private String searchInfo;
    /**
     * 列表操作配置
     */

    private String actionInfo;
    /**
     * 更改操作(enable\disable)
     */

    private String operable;
    /**
     * 创建人
     */

    private String createdBy;

    private Date createdDt;
    /**
     * 更新人
     */

    private String updatedBy;
    /**
     * 更新时间
     */

    private Date updatedDt;
    /**
     * 乐观锁
     */

    private Integer version;
}
