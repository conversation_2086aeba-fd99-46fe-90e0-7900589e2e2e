package com.cloudstar.rightcloud.resource.data.change.dto;

import lombok.Data;

import java.util.Date;

/**
 * 变更历史分页查询返回值
 *
 * <AUTHOR>
 * @since 2023/8/14 13:59
 */
@Data
public class ResChangeRecordPageResultDto {

    /**
     * id;
     */
    private Long id;
    /**
     * 云环境id;
     */
    private Long cloudEnvId;
    /**
     * 云环境类型
     */
    private String cloudEnvType;
    /**
     * 资源类型编码
     */
    private String resTypeCode;
    /**
     * 云管资源ID
     */
    private Long resId;
    /**
     * 资源名称
     */
    private String resName;
    /**
     * 底层资源ID
     */
    private String uuid;
    /**
     * 变更类型;新增(new)、续签(renew)、升配(increase)、降配(decrease)、扩容(expand)、缩容(shrink)、回收(recycle)、分配(allocate)
     */
    private String changeType;
    /**
     * 订单号ID
     */
    private String orderId;
    /**
     * 调整开始时间
     */
    private Date changeStartTime;
    /**
     * 调整结束时间
     */
    private Date changeEndTime;
    /**
     * 付费方式
     */
    private String chargeType;
    /**
     * 计费方式;Month:按月、Day:按天、Hour:按小时
     */
    private String priceUnit;
    /**
     * 结算时间
     */
    private Date lastChargeTime;
    /**
     * 组织ID
     */
    private Long orgId;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 所有者;
     */
    private String ownerAccount;
    /**
     * 创建时间;
     */
    private Date createdDt;
    /**
     * 创建人;
     */
    private String createdBy;
    /**
     * 更新时间;
     */
    private Date updatedDt;
    /**
     * 更新人;
     */
    private String updatedBy;
    /**
     * 版本;
     */
    private Long version;

}
