<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.resource.data.oss.mapper.ResBucketObjectMapper">

    <select id="list" resultType="com.cloudstar.rightcloud.resource.data.oss.dto.ResBucketObject">
        SELECT
        any_value(A.id) AS id,
        any_value(A.bucket_id) AS bucket_id,
        any_value(A.uuid) AS uuid,
        any_value(A.object_key) AS object_key,
        any_value(A.object_name) AS object_name,
        any_value(A.object_type) AS object_type,
        any_value(A.object_path) AS object_path,
        any_value(A.object_ext) AS object_ext,
        any_value(A.object_size) AS object_size,
        any_value(A.meta_info) AS meta_info,
        any_value(A.storage_class) AS storage_class,
        any_value(A.last_modified) AS last_modified,
        any_value(A.uri) AS uri,
        any_value(A.content_type) AS content_type,
        any_value(A.user_name) AS user_name,
        any_value(A.created_by) AS created_by,
        any_value(A.created_dt) AS created_dt,
        any_value(A.updated_by) AS updated_by,
        any_value(A.updated_dt) AS updated_dt,
        any_value(A.version) AS version
        FROM res_bucket_object A
        <trim prefix="where" prefixOverrides="and|or">
            <if test="dto.bucketId != null">
                and A.bucket_id = #{dto.bucketId}
            </if>
            <if test="dto.objectKey != null">
                and A.object_key = #{dto.objectKey}
            </if>
            <if test="dto.folderName != null">
                and A.object_path = #{dto.folderName}
            </if>
            <if test="dto.regexpFolder != null">
                and A.object_path regexp concat("^",#{dto.regexpFolder},"[^/]*")
            </if>
            <if test="dto.objectNameLike != null">
                and object_name like concat("%", #{dto.objectNameLike}, "%")
            </if>
        </trim>
        group by A.id
    </select>
</mapper>
