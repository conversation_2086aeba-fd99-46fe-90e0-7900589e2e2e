<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.resource.data.open.mapper.ResourceDataMapper">

    <select id="pageAllResource" resultType="java.util.HashMap">
        select *
        from ${tableName} d
    </select>

    <sql id="data_filter">
        <if test="resCreatedDateFrom != null">
            and d.`created_dt` &gt;= #{resCreatedDateFrom}
        </if>
        <if test="resCreatedDateTo != null">
            and d.`created_dt` &lt;= #{resCreatedDateTo}
        </if>
        <if test="syncDateFrom != null">
            and d.`updated_dt` &gt;= #{syncDateFrom}
        </if>
        <if test="syncDateTo != null">
            and d.`updated_dt` &lt;= #{syncDateTo}
        </if>
    </sql>
    <sql id="uuid_filter">
        <if test="uuid !=null and uuid != ''">
            and d.uuid = #{uuid}
        </if>
    </sql>
    <sql id="owner_account_filter">
        <if test="userAccount !=null and userAccount != ''">
            and d.owner_account = #{userAccount}
        </if>
    </sql>
    <sql id="owner_filter">
        <if test="userAccount !=null and userAccount != ''">
            and d.owner = #{userAccount}
        </if>
    </sql>
    <sql id="create_by_filter">
        <if test="userAccount !=null and userAccount != ''">
            and d.created_by = #{userAccount}
        </if>
    </sql>
    <sql id="org_id_filter">
        <if test="orgId !=null">
            and d.org_id = #{orgId}
        </if>
    </sql>
    <sql id="org_sid_filter">
        <if test="orgId !=null">
            and d.org_sid = #{orgId}
        </if>
    </sql>
    <sql id="env_id_filter">
        <if test="envIds != null and envIds.size() > 0">
            and d.cloud_env_id IN
            <foreach collection="envIds" item="envId" open="(" separator="," close=")">
                #{envId}
            </foreach>
        </if>
    </sql>
    <sql id="region_filter">
        <if test="region !=null and region != ''">
            and d.region = #{region}
        </if>
    </sql>
    <sql id="project_uuid_and_env_id_filter">
        <if test="(projectUuids != null and projectUuids.size() > 0) or (envIds != null and envIds.size() > 0)">
            AND (
            <trim prefixOverrides="OR">
                <if test="projectUuids != null and projectUuids.size() > 0">
                    d.project_uuid IN
                    <foreach collection="projectUuids" item="projectUuid" open="(" separator="," close=")">
                        #{projectUuid}
                    </foreach>
                </if>
                <if test="envIds != null and envIds.size() > 0">
                    OR d.cloud_env_id IN
                    <foreach collection="envIds" item="envId" open="(" separator="," close=")">
                        #{envId}
                    </foreach>
                </if>
            </trim>
            )
        </if>
    </sql>
    <sql id="ref_user_id_filter">
        <if test="refUserId !=null">
            and d.ref_user_id = #{refUserId}
        </if>
    </sql>
    <sql id="product_code_filter">
        <if test="productCode !=null and productCode != ''">
            and d.product_code = #{productCode}
        </if>
    </sql>
    <sql id="id_filter">
        <if test="uuid !=null and uuid != ''">
            and d.id = #{uuid}
        </if>
    </sql>
    <sql id="owner_sid_filter">
        <if test="userId !=null">
            and d.owner_sid = #{userId}
        </if>
    </sql>
    <sql id="owner_id_filter">
        <if test="userId !=null">
            and d.owner_id = #{userId}
        </if>
    </sql>
    <sql id="combined_uuid_org_date_filter">
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
    </sql>
    <sql id="combined_product_org_user_date_filter">
        <include refid="id_filter"/>
        <include refid="product_code_filter"/>
        <include refid="org_sid_filter"/>
        <include refid="owner_sid_filter"/>
        <include refid="data_filter"/>
    </sql>

    <select id="pageEcs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageBms" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageIms" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageVpc" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageElb" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageEvs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="data_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageObs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageSmn" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_product_org_user_date_filter"/>
    </select>

    <select id="pageHdr" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="uuid_filter"/>
        <include refid="org_sid_filter"/>
        <include refid="data_filter"/>
        <include refid="product_code_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageNat" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageDns" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_product_org_user_date_filter"/>
    </select>

    <select id="pageVpn" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageDc" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_product_org_user_date_filter"/>
    </select>

    <select id="pageSfs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="region_filter"/>
        <include refid="create_by_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageAs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageAsGroup" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageEip" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1=1
        and d.id not in (select id from ${tableName} where charge_type is null and status is null)
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>
    <select id="pageVpcep" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>


    <select id="pageCce" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_sid_filter"/>
        <include refid="owner_sid_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageDcs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>
    <select id="pageRoma" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="region_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageDew" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageWaf" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageCgs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageHss" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageCbh" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageSecMaster" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageCfw" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageDcaas" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="id_filter"/>
        <include refid="org_sid_filter"/>
        <include refid="owner_id_filter"/>
        <include refid="data_filter"/>
    </select>
    <select id="pageDbas" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="env_id_filter"/>
        <include refid="data_filter"/>
    </select>
    <select id="pageNdr" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>
    <select id="pageDsc" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>

    <select id="pageRds_MySQL" resultType="java.util.HashMap">
        select *,d.description as name
        from ${tableName} d
        where 1 = 1
        and d.engine like concat('%', 'MySQL', '%')
        and d.id not in (select id from ${tableName} where charge_type is null and status = 'deleted')
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="env_id_filter"/>
        <include refid="owner_filter"/>
    </select>

    <select id="pageGaussDB" resultType="java.util.HashMap">
        select *,d.instance_id as uuid
        from ${tableName} d
        where 1 = 1
        <if test="uuid !=null and uuid != ''">
            and d.instance_id = #{uuid}
        </if>
        <include refid="org_id_filter"/>
        <include refid="region_filter"/>
        <include refid="owner_id_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageGaussDBMysql" resultType="java.util.HashMap">
        select *,d.instance_id as uuid
        from ${tableName} d
        where 1 = 1
        <if test="uuid !=null and uuid != ''">
            and d.instance_id = #{uuid}
        </if>
        <include refid="org_id_filter"/>
        <include refid="region_filter"/>
        <include refid="owner_id_filter"/>
        <include refid="data_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageTaurusDB" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        and d.engine like concat('%', 'Taurus', '%')
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="env_id_filter"/>
        <include refid="owner_filter"/>
    </select>

    <select id="pageDds" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_sid_filter"/>
        <include refid="owner_id_filter"/>
        <include refid="data_filter"/>
        <include refid="region_filter"/>
        <include refid="env_id_filter"/>
    </select>
    <select id="pageMrs" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_product_org_user_date_filter"/>
    </select>

    <select id="pageElb_Listener" resultType="java.util.HashMap">
        select *
        from ${tableName} lb
        left join res_lb d on lb.lb_id = d.id
        where 1 = 1
        <if test="uuid !=null and uuid != ''">
            and lb.uuid = #{uuid}
        </if>
        <include refid="org_id_filter"/>
        <if test="userAccount !=null and userAccount != ''">
            and lb.created_by = #{userAccount}
        </if>
        <if test="resCreatedDateFrom != null">
            and lb.`created_dt` &gt;= #{resCreatedDateFrom}
        </if>
        <if test="resCreatedDateTo != null">
            and lb.`created_dt` &lt;= #{resCreatedDateTo}
        </if>
        <if test="syncDateFrom != null">
            and lb.`updated_dt` &gt;= #{syncDateFrom}
        </if>
        <if test="syncDateTo != null">
            and lb.`updated_dt` &lt;= #{syncDateTo}
        </if>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageElb_Group" resultType="java.util.HashMap">
        select *
        from ${tableName} lg
        left join res_lb d on lg.lb_id = d.id
        where 1 = 1
        <if test="uuid !=null and uuid != ''">
            and lg.uuid = #{uuid}
        </if>
        <include refid="org_id_filter"/>
        <if test="userAccount !=null and userAccount != ''">
            and lg.created_by = #{userAccount}
        </if>
        <if test="resCreatedDateFrom != null">
            and lg.`created_dt` &gt;= #{resCreatedDateFrom}
        </if>
        <if test="resCreatedDateTo != null">
            and lg.`created_dt` &lt;= #{resCreatedDateTo}
        </if>
        <if test="syncDateFrom != null">
            and lg.`updated_dt` &gt;= #{syncDateFrom}
        </if>
        <if test="syncDateTo != null">
            and lg.`updated_dt` &lt;= #{syncDateTo}
        </if>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageIpGroups" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="create_by_filter"/>
        <include refid="data_filter"/>
    </select>

    <select id="pageCertificates" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="create_by_filter"/>
        <include refid="data_filter"/>
    </select>

    <select id="pageSht" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        /*left join res_vd vd on d.vd_uuid = vd.uuid*/
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
        <include refid="create_by_filter"/>
        <!--<if test="userAccount !=null and userAccount != ''">
            and (vd.owner_account = #{userAccount} or vd.owner_account is null)
        </if>-->
    </select>

    <select id="pageSubnet" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageRr" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageVnic" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageSg" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="owner_account_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageAcl" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
        <include refid="project_uuid_and_env_id_filter"/>
    </select>

    <select id="pageKeypair" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageSwr" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="org_sid_filter"/>
        <include refid="data_filter"/>
        <if test="userId !=null">
            and d.user_sid = #{userId}
        </if>
    </select>

    <select id="pageEcs_Group" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="combined_uuid_org_date_filter"/>
        <include refid="create_by_filter"/>
    </select>

    <select id="pageShareBandwidth" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <include refid="org_id_filter"/>
        <include refid="data_filter"/>
        <include refid="owner_id_filter"/>
        <include refid="env_id_filter"/>
    </select>

    <select id="pageAll" resultType="java.util.HashMap">
        select *
        from ${tableName} d
        where 1 = 1
        <include refid="uuid_filter"/>
        <if test="userId !=null">
            and d.user_sid = #{userId}
        </if>
        <include refid="org_sid_filter"/>
        <include refid="data_filter"/>
        <include refid="region_filter"/>
        <if test="serviceTypeNotIn != null and serviceTypeNotIn.size() > 0">
            and d.service_type not in
            <foreach collection="serviceTypeNotIn" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusNotIn != null and statusNotIn.size() > 0">
            and d.status not in
            <foreach collection="statusNotIn" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyId !=null">
            and d.company_id = #{companyId}
        </if>
        <if test="refCompanyId !=null and refCompanyId != ''">
            and d.ref_company_id = #{refCompanyId}
        </if>
    </select>

    <select id="selectByUuidOrClusterId" resultType="java.util.HashMap">
        select so.type,
               so.order_extend,
               pr.start_time,
               pr.end_time,
               sod.charge_type,
               pr.cloud_env_id,
               sod.start_time sod_start_time
        from bss.sf_product_resource pr
                 inner join bss.service_order so on pr.service_order_id = so.id
                 inner join bss.service_order_detail sod
                            on pr.service_order_id = sod.order_id and pr.product_type = sod.service_type
        where pr.product_type = #{productCode}
          and (pr.instance_uuid = #{uuid} or pr.cluster_id = #{clusterId})
        order by pr.id desc
        limit 1
    </select>

    <select id="selectById" resultType="java.util.HashMap">
        select so.type,
               so.order_extend,
               pr.start_time,
               pr.end_time,
               sod.charge_type,
               pr.cloud_env_id,
               sod.start_time sod_start_time
        from bss.sf_product_resource pr
                 inner join bss.service_order so on pr.service_order_id = so.id
                 inner join bss.service_order_detail sod
                            on pr.service_order_id = sod.order_id and pr.product_type = sod.service_type
        where pr.product_type = #{productCode}
          and pr.id = #{id}
        order by pr.id desc
        limit 1
    </select>

</mapper>
