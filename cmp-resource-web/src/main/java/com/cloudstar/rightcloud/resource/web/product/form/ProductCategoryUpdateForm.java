package com.cloudstar.rightcloud.resource.web.product.form;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.resource.common.constant.msg.ServiceFieldKeyConstant;


import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 编辑云产品分类参数
 */
@Data
public class ProductCategoryUpdateForm implements Serializable {


    /**
     * 分类名称
     */
    @I18nProperty(propertyKey = ServiceFieldKeyConstant.PRODUCT_CATEGORY_NAME)
    @NotNull
    private String name;

    /**
     * 分类英文名称
     */
    private String nameEn;

    /**
     * 描述
     */
    private String description;

}
