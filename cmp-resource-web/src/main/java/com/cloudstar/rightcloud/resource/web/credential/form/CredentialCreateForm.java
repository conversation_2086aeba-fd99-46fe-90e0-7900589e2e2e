package com.cloudstar.rightcloud.resource.web.credential.form;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.resource.common.constant.msg.ResCredentialMsgConstant;
import com.cloudstar.rightcloud.resource.common.constant.msg.field.ResFieldKeyConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 创建凭证管理请求参数
 */
@Data
public class CredentialCreateForm {

    /**
     * 名称
     */
    @I18nProperty(propertyKey = ResFieldKeyConstant.CREDENTIAL_NAME)
    @NotBlank
    private String name;

    /**
     * 类型:密码password,密钥keypair
     */
    @I18nProperty(propertyKey = ResFieldKeyConstant.PASSWORD_TYPE)
    @NotBlank
    private String type;

    /**
     * 配置方式:手动设置manuallyOperation,自动生成automaticGeneration
     */
    @I18nProperty(propertyKey = ResFieldKeyConstant.CONFIG_TYPE)
    @NotBlank
    private String configType;

    /**
     * 是否开启自动更新
     */
    private Boolean autoPasswordUpdate;

    /**
     * 凭证分类:linux、windows
     */
    @I18nProperty(propertyKey = ResFieldKeyConstant.CATEGORY)
    @NotBlank
    private String category;

    /**
     * 用户名
     */
    @I18nProperty(propertyKey = ResFieldKeyConstant.USER_NAME)
    @NotBlank
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 密码是否有sudo权限
     */
    private Boolean hasSudoAuthority;

    /**
     * 密码长度
     */
    private Integer passwordLength;

    /**
     * 密码规则,多个用逗号,分隔;大写字母uppercase,小写字母lowercase,数字number,特殊字符special_character
     */
    private String passwordRule;

    /**
     * 密码更新周期;
     */
    private Integer passwordUpdateCycle;

    /**
     * 密钥类型:RSA_1024,RSA_2048,RSA_3072,RSA_4096
     */
    private String keypairType;

    /**
     * 公共key
     */
    private String publicKey;

    /**
     * 私有key
     */
    private String privateKey;

    /**
     * 描述
     */
    @Size(max = 256, message = ResCredentialMsgConstant.MAX_SIZE)
    private String description;
}
