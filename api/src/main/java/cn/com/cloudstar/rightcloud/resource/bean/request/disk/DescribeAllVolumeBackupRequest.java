/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.disk;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * DESC:查询块存储列表
 *
 * <AUTHOR>
 * @date 2019/09/27 09:48
 */
@Getter
@Setter
@ApiModel("查询块存储备份列表")
public class DescribeAllVolumeBackupRequest extends BaseRequest {

    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境ID")
    private Long cloudEnvId;

    /**
     * 名称模糊查询
     */
    @ApiModelProperty(value = "名称模糊查询")
    private String nameLike;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 块存储id
     */
    @ApiModelProperty(value = "块存储ID")
    private String resVdSid;

    /**
     * 块存储资源id
     */
    @ApiModelProperty(value = "块存储资源ID")
    private String volumeUuid;

    /**
     * 磁盘名称
     */
    @ApiModelProperty(value = "磁盘名称", hidden = true)
    private String vdNameLike;


}
