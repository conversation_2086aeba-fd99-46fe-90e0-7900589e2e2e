/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.resattr;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询资源属性")
public class DescribeResAttrRequest extends BaseRequest implements Serializable {

    @ApiModelProperty(value = "属性名称")
    private String attrName;

}
