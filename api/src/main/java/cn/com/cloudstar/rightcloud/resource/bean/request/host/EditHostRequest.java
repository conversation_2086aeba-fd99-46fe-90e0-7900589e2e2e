/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.host;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import cn.com.cloudstar.rightcloud.basic.data.pojo.vo.ResAttrVO;

/**
 * The type EditHostRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/1/16
 */
@Getter
@Setter
@ApiModel(description = "编辑计算主机")
public class EditHostRequest {

    /**
     * 计算主机名称
     */
    @ApiModelProperty("计算主机名称")
    private String hostName;

    /**
     * cpu类型
     */
    @ApiModelProperty("CPU类型")
    private String cpuType;

    /**
     * 计算主机ip
     */
    @ApiModelProperty("计算主机 IP")
    private String hostIp;

    /**
     * 主机操作系统
     */
    @ApiModelProperty("主机操作系统")
    private String hostOsType;

    /**
     * 制造商
     */
    @ApiModelProperty("制造商")
    private String vendor;

    /**
     * 主机型号
     */
    @ApiModelProperty("主机型号")
    private String model;

    /**
     * 序列号
     */
    @ApiModelProperty("序列号")
    private String serialNumber;

    /**
     * 虚拟化类型
     */
    @ApiModelProperty("虚拟化类型")
    private String hostType;

    /**
     * 扩展信息
     */
    @ApiModelProperty("扩展信息")
    private List<ResAttrVO> resAttrs;
}
