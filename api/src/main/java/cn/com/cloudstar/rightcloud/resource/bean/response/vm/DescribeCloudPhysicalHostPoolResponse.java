/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.vm;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date: 23:18 2020/04/08
 */
@Data
@ApiModel(description = "查询实例")
public class DescribeCloudPhysicalHostPoolResponse {

    private Long id;

    private Long cloudEnvId;

    private String cloudEnvType;

    private String instanceId;

    private String hostName;

    private String imageId;

    private String osName;

    private Integer cpu;

    private Integer memory;

    private String description;

    private String ownerId;

    private String innerIp;

    private String publicIp;

    private Integer sshPort;

    private String keypairName;

    private String networkName;

    private String networkId;

    private String subnetName;

    private String subnetId;

    private String floatingIpPoolName;

    private String floatingIpPoolId;

    private String zone;

    private String region;

    private String monitorStatus;

    private String manageStatus;

    private String monitorVersion;

    private String statusInfo;

    private String status;

    /**
     * 创建原始参数
     */
    private String originParam;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

    private Long serverTemplateId;

    private Long companyId;

    private Long projectId;

    /**
     * 是否分配
     */
    private Boolean allocFlag;

    private Date startTime;

    private Date endTime;

    private String osCategory;

    private String physicalUuid;

    private String macAddress;

    /**
     * 是否接入
     */
    private Boolean associateFlag;

    /**
     * 电源类型
     */
    private String powerType;

    /**
     * 电源类型参数
     */
    private String powerAttrData;

    /**
     * 硬盘
     */
    private String diskSet;

    /**
     * 启动盘
     */
    private String bootDisk;

    /**
     * 磁盘阵列数据
     */
    private String raids;

    /**
     * 磁盘大小
     */
    private Long storage;

    private String powerStatus;

    private String physicalStatus;

    private String managementAccount;

    private String managemenPassword;

    private String remoteLoginType;

    private String platform;


    //res_host
    private String resHostSid;
    private String serialNumber;
    private String equipType;
    private String brand;
    private String model;

    //other
    private String cloudEnvName;
    private String cloudImageName;
    private String serverTemplateName;
    private String cloudHostId;
    // 硬盘数量
    private Integer diskNum;
    // 实例id
    private String instanceSid;
    // 实例名称
    private String instanceName;
    private Boolean joinMaas;
    private String cpuModel;

    private String dataCenter;
    private BigDecimal costPrice;
    private String assetNumber;
    private String allocStatus;
    private String allocStatusName;
    private String vendor;

    private String gpuType;
    private Integer gpuNumber;
    private String costCycle;
    private String resourcePool;

}
