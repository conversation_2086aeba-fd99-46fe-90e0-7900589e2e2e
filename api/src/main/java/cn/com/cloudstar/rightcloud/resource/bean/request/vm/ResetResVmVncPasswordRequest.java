/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.vm;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * The type CloudHostResetPasswordDTO. <p>
 *
 * <AUTHOR>
 * @date 2019/3/11
 */
@Getter
@Setter
@ApiModel(description = "实例重置vnc密码")
public class ResetResVmVncPasswordRequest {

    /**
     * 实例ID
     */
    @NotBlank
    @ApiModelProperty(notes = "实例ID")
    private String resVmId;

    /**
     * 新密码
     */
    @NotBlank
    @ApiModelProperty(notes = "新密码")
    private String newPassword;

}
