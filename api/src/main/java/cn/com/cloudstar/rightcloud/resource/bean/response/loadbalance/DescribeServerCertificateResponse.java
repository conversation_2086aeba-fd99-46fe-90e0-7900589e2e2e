/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.loadbalance;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询服务证书返回结果出参
 * <AUTHOR>
 */
@ApiModel(description = "服务证书")
@Setter
@Getter
public class DescribeServerCertificateResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 证书ID
     */
    @ApiModelProperty("证书ID")
    private String id;

    /**
     * 证书名称
     */
    @ApiModelProperty("证书名称")
    private String name;

    /**
     * 证书类型
     */
    @ApiModelProperty("证书类型")
    private String cerType;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
