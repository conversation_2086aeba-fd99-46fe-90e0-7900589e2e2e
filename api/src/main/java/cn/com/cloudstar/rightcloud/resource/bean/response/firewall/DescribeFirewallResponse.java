/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.firewall;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.RouterVO;

/**
 * 获取防火墙列表查询返回结果
 * <AUTHOR>
 */
@ApiModel(description = "获取防火墙列表")
@Setter
@Getter
public class DescribeFirewallResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("cmp ID")
    private Long id;

    /**
     * 资源ID
     */
    @ApiModelProperty("资源ID")
    private String uuid;

    /**
     * 防火墙名称
     */
    @ApiModelProperty("防火墙名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 类型（VPC防火墙、服务链防火墙）
     */
    @ApiModelProperty("类型（VPC防火墙、服务链防火墙）")
    private String type;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 策略ID
     */
    @ApiModelProperty("策略ID")
    private Long strategyId;

    /**
     * 服务链ID
     */
    @ApiModelProperty("服务链ID")
    private Long serviceChainId;

    /**
     * 云环境ID
     */
    @ApiModelProperty("云环境ID")
    private Long cloudEnvId;

    /**
     * 服务链名称
     */
    @ApiModelProperty("服务链名称")
    private String serviceChainName;

    /**
     * 规则集
     */
    @ApiModelProperty("规则名称")
    private String strategyName;

    /**
     * 路由器名称
     */
    @ApiModelProperty("路由器名称")
    private String routerName;

    /**
     * 路由器id列表
     */
    @ApiModelProperty("路由ID列表")
    private List<Long> routerIdList;

    /**
     * 路由器列表
     */
    @ApiModelProperty("路由列表")
    private List<RouterVO> routerList;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名")
    private String tagNames;

    /**
     * 标签值
     */
    @ApiModelProperty("标签值")
    private String tagValues;

    /**
     * 标签编码
     */
    @ApiModelProperty("标签编码")
    private String rgbCodes;

    /**
     * 入策略ID
     */
    @ApiModelProperty("入策略ID")
    private String ingressFirewallPolicyId;

    /**
     * 出策略ID
     */
    @ApiModelProperty("出策略ID")
    private String egressFirewallPolicyId;

    /**
     * 是否开启
     */
    @ApiModelProperty("是否开启")
    private String adminStateUp;

    /**
     * 已关联的子网信息
     */
    @ApiModelProperty("已关联的子网信息")
    private String bindSubnetInfo;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
