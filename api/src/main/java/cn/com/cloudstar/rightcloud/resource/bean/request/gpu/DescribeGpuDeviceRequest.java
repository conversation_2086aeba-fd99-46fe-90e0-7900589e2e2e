/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.gpu;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

/**
 * 查询GPU设备列表
 *
 * <AUTHOR>
 */
@ApiModel(description = "查询GPU设备列表")
@Setter
@Getter
public class DescribeGpuDeviceRequest extends BaseRequest {

    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境ID", required = true)
    @NotNull
    private Long cloudEnvId;

    /**
     * 所在主机
     **/
    @ApiModelProperty(value = "所在主机", required = false)
    private String hostName;

    /**
     * 产品型号
     **/
    @ApiModelProperty(value = "产品型号", required = false)
    private String productNo;

    /**
     * 产品名称
     **/
    @ApiModelProperty(value = "产品名称", required = false)
    private String productName;

    /**
     * 厂商名称
     **/
    @ApiModelProperty(value = "厂商名称", required = false)
    private String vendorName;

    /**
     * 非关系
     */
    private Boolean unRelation;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
