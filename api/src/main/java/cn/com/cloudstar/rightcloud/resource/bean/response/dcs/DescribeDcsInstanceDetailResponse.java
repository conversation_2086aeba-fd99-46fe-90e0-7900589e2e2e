/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.dcs;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "高速缓存实例详情")
public class DescribeDcsInstanceDetailResponse implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例ID")
    private String instanceId;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 缓存引擎
     */
    @ApiModelProperty(value = "缓存引擎")
    private String engine;
    /**
     * 缓存引擎版本
     */
    @ApiModelProperty(value = "缓存引擎版本")
    private String engineVersion;
    /**
     * 缓存容量
     */
    @ApiModelProperty(value = "缓存容量")
    private Integer capacity;
    /**
     * 是否无密码访问
     */
    @ApiModelProperty(value = "是否无密码访问")
    private String noPasswordAccess;
    /**
     * 访问密码
     */
    @ApiModelProperty(value = "缓存实例的认证信息 访问密码")
    private String password;
    /**
     * 访问用户名
     */
    @ApiModelProperty(value = "访问用户名")
    private String accessUser;
    /**
     * 网络id
     */
    @ApiModelProperty(value = "网络id")
    private String vpcId;
    /**
     * 网络pk
     */
    @ApiModelProperty(value = "网络PK")
    private String vpcPid;
    /**
     * 网络名称
     */
    @ApiModelProperty(value = "网络名称")
    private String vpcName;
    /**
     * 安全组id
     */
    @ApiModelProperty(value = "安全组id")
    private String securityGroupId;
    /**
     * 安全组名称
     */
    @ApiModelProperty(value = "安全组名称")
    private String securityGroupName;
    /**
     * 子网id
     */
    @ApiModelProperty(value = "子网id")
    private String subnetId;
    /**
     * 子网pk
     */
    @ApiModelProperty(value = "子网PK")
    private String subnetPid;
    /**
     * 子网名称
     */
    @ApiModelProperty(value = "子网名称")
    private String subnetName;
    /**
     * 子网网段
     */
    @ApiModelProperty(value = "子网网段")
    private String subnetCidr;
    /**
     * 创建缓存节点到指定且有资源的可用区ID
     */
    @ApiModelProperty(value = "创建缓存节点到指定且有资源的可用区ID")
    private String availableZones;

    /**
     * 可用区名称
     */
    @ApiModelProperty(value = "可用区名称")
    private String availableZoneNames;
    /**
     * 产品的标识
     */
    @ApiModelProperty(value = "产品的标识")
    private String productId;
    /**
     * 维护时间窗开始时间
     */
    @ApiModelProperty(value = "维护时间窗开始时间")
    private String maintainBegin;
    /**
     * 维护时间窗结束时间
     */
    @ApiModelProperty(value = "维护时间窗结束时间")
    private String maintainEnd;
    /**
     * 私有ip
     */
    @ApiModelProperty(value = "私有IP")
    private String ip;
    /**
     * 实例域名
     */
    @ApiModelProperty(value = "实例域名")
    private String domainname;
    /**
     * 缓存的端口
     */
    @ApiModelProperty(value = "缓存的端口")
    private String port;
    /**
     * 实例的状态
     */
    @ApiModelProperty(value = "实例的状态")
    private String status;
    /**
     * 总内存
     */
    @ApiModelProperty(value = "总内存")
    private Integer maxMemory;
    /**
     * 已使用的内存
     */
    @ApiModelProperty(value = "已使用的内存")
    private Integer usedMemory;
    /**
     * 资源规格标识
     */
    @ApiModelProperty(value = "资源规格标识")
    private String resourceSpecCode;
    /**
     * DCS内部版本号
     */
    @ApiModelProperty(value = "DCS内部版本号")
    private String internalVersion;
    /**
     * 实例创建失败或状态异常时的错误码
     */
    @ApiModelProperty(value = "实例创建失败或状态异常时的错误码")
    private String errorCode;

    /**
     * 错误消息
     */
    @ApiModelProperty(value = "错误消息")
    private String errorMsg;

    /**
     * 集群实例的后端服务地址
     */
    @ApiModelProperty(value = "集群实例的后端服务地址")
    private String backendAddrs;

    /**
     * 计费模式
     */
    @ApiModelProperty(value = "计费模式")
    private Integer chargingMode;
    /**
     * 最小内存规格
     */
    @ApiModelProperty(value = "最小内存规格")
    private String capacityMinor;
    /**
     * 完成创建时间
     */
    @ApiModelProperty(value = "完成创建时间")
    private Date createdAt;
    /**
     * 启动时间
     */
    @ApiModelProperty(value = "启动时间")
    private Date launchedAt;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * cpu类型
     */
    @ApiModelProperty(value = "CPU类型")
    private String cpuType;
    /**
     * 存储类型
     */
    @ApiModelProperty(value = "存储类型")
    private String storageType;
    /**
     * 缓存类型
     */
    @ApiModelProperty(value = "缓存类型")
    private String cacheMode;

    /**
     * 资源服务代码
     */
    @ApiModelProperty(value = "资源服务代码")
    private String cloudServiceTypeCode;
    /**
     * 资源类型代码
     */
    @ApiModelProperty(value = "资源类型代码")
    private String cloudResourceTypeCode;

    /**
     * 用户组织sid
     */
    @ApiModelProperty(value = "用户组织id")
    private Long orgSid;
    /**
     * 云环境id
     */
    @ApiModelProperty(value = "云环境id")
    private Long cloudEnvId;
    /**
     * 云环境类型
     */
    @ApiModelProperty(value = "云环境类型")
    private String cloudEnvType;
    /**
     * 云环境名称
     */
    @ApiModelProperty(value = "云环境名称")
    private String cloudEnvName;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    /**
     * 产品类型名称
     */
    @ApiModelProperty(value = "产品类型名称")
    private String productTypeName;
    /**
     * 缓存类型名称
     */
    @ApiModelProperty(value = "缓存类型名称")
    private String cacheModeName;

    /**
     * 标签名
     */
    @ApiModelProperty(value = "标签名")
    private String tagNames;

    /**
     * 标签值
     */
    @ApiModelProperty(value = "标签值")
    private String tagValues;

    /**
     * 标签颜色
     */
    @ApiModelProperty(value = "标签颜色")
    private String rgbCodes;
}
