package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.enums.MarketSkuTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuRelevanceMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuRelevance;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuAddReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuNextReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuUpdReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketSkuPageReq;
import cn.com.cloudstar.rightcloud.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSku;
import cn.com.cloudstar.rightcloud.data.response.market.ShopSkuResp;
import cn.com.cloudstar.rightcloud.data.vo.market.StringVO;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【market_shop_sku(商品属性表)】的数据库操作Service实现
 * @createDate 2023-08-01 16:21:49
 */
@Service
public class MarketShopSkuServiceImpl extends ServiceImpl<MarketShopSkuMapper, MarketShopSku>
        implements MarketShopSkuService {

    private static final String ENUM_TYPE = "enum";

    @Autowired
    private MarketShopSkuMapper marketShopSkuMapper;

    @Autowired
    private MarketShopSkuRelevanceMapper shopSkuRelevanceMapper;

    @Override
    public void addSku(MarketShopSkuAddReq req) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        MarketShopSku marketShopSku = new MarketShopSku();
        marketShopSku.setUserSid(authUserInfo.getUserSid());
        marketShopSku.setUnit(req.getUnit());
        marketShopSku.setType(req.getType());
        marketShopSku.setAttrName(req.getAttrName());

        if (MarketSkuTypeEnum.QUANTITY.getType().equals(req.getType())) {
            if (StrUtil.isBlank(req.getUnit())) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        } else {
            if (CollectionUtil.isNotEmpty(req.getEnumValues())) {
                marketShopSku.setEnumValues(req.getEnumValues().stream().map(StringVO::getValue).collect(Collectors.joining(",")));
            }
        }
        marketShopSkuMapper.insert(marketShopSku);
    }

    @Override
    public RestResult delSku(Long skuId) {
        marketShopSkuMapper.selectById(skuId);

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        QueryWrapper<MarketShopSku> wrapper = new QueryWrapper<MarketShopSku>();
        wrapper.lambda()
                .eq(MarketShopSku::getId, skuId)
                .eq(MarketShopSku::getUserSid, authUserInfo.getUserSid());
        if (marketShopSkuMapper.delete(wrapper) > 0) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

    }

    @Override
    public Page<ShopSkuResp> selectPage(MarketSkuPageReq req) {
        User authUser = AuthUtil.getAuthUser();
        Page<MarketShopSku> page = PageUtil.preparePageParams(req);
        QueryWrapper<MarketShopSku> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(req.getName())) {
            queryWrapper.lambda().like(MarketShopSku::getAttrName, req.getName());
        }
        queryWrapper.lambda().eq(MarketShopSku::getUserSid, authUser.getUserSid());
        Page<MarketShopSku> pageRes = marketShopSkuMapper.selectPage(page, queryWrapper);
        List<MarketShopSku> records = pageRes.getRecords();
        List<ShopSkuResp> resp = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                ShopSkuResp shopSkuResp = new ShopSkuResp();
                shopSkuResp.setId(record.getId());
                shopSkuResp.setUnit(record.getUnit());
                shopSkuResp.setAttrName(record.getAttrName());
                shopSkuResp.setType(record.getType());
                shopSkuResp.setAvailableStatus(record.getAvailableStatus());

                if (MarketSkuTypeEnum.ENUM.getType().equals(record.getType())) {
                    if (StrUtil.isNotBlank(record.getEnumValues())) {
                        String[] split = record.getEnumValues().split(",");

                        List<StringVO> collect = Arrays.stream(split).map(s -> {
                            StringVO stringVO = new StringVO();
                            stringVO.setValue(s);
                            return stringVO;
                        }).collect(Collectors.toList());
                        shopSkuResp.setEnumValues(collect);
                    }
                }
                resp.add(shopSkuResp);
            });
        }
        Page<ShopSkuResp> newPageRes = new Page<>();
        newPageRes.setCurrent(pageRes.getCurrent());
        newPageRes.setPages(pageRes.getPages());
        newPageRes.setSize(pageRes.getSize());
        newPageRes.setTotal(pageRes.getTotal());
        newPageRes.setRecords(resp);
        return newPageRes;
    }

    @Override
    @Transactional
    public RestResult next(List<MarketShopSkuNextReq> req) {
        List<Long> skuIds = req.stream().map(MarketShopSkuNextReq::getId).collect(Collectors.toList());
        QueryWrapper<MarketShopSkuRelevance> skuRelevanceQueryWrapper = new QueryWrapper<>();
        skuRelevanceQueryWrapper.lambda().in(MarketShopSkuRelevance::getSkuId, skuIds);
        Integer integer = shopSkuRelevanceMapper.selectCount(skuRelevanceQueryWrapper);
        List<MarketShopSku> shopSkus = new ArrayList<>();
        if (integer > 0) {
            // 已关联商品，需判断枚举值有没有改变
            req.forEach(reqItem -> {
                List<StringVO> enumValues = reqItem.getEnumValues();
                String updSku = enumValues.stream().map(StringVO::getValue).collect(Collectors.joining(","));
                MarketShopSku dbSku = this.getById(reqItem.getId());
                if (!updSku.equals(dbSku.getEnumValues())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ASSOCIATED_WITH_SHOP_UNABLE_MODIFY));
                }
            });
        }

        req.forEach(reqItem -> {
            List<StringVO> enumValues = reqItem.getEnumValues();
            String updSku = enumValues.stream().map(StringVO::getValue).collect(Collectors.joining(","));
            MarketShopSku sku = new MarketShopSku();
            sku.setId(reqItem.getId());
            sku.setEnumValues(updSku);
            shopSkus.add(sku);
        });

        if (this.updateBatchById(shopSkus)) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
    }

    @Override
    public void upd(MarketShopSkuUpdReq req) {
        MarketShopSku shopSku = this.getById(req.getId());
        if (Objects.isNull(shopSku)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        if (ENUM_TYPE.equals(req.getType())){
            shopSku.setEnumValues(null);
        }
        shopSku.setUnit(req.getUnit());
        shopSku.setType(req.getType());
        shopSku.setAttrName(req.getAttrName());

        this.updateById(shopSku);
    }
}
