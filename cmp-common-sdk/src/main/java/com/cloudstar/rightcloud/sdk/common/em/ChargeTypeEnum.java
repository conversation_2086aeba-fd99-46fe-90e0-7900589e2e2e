package com.cloudstar.rightcloud.sdk.common.em;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 计费模式
 * 计费模式(CHARGE_TYPE)：包年包月prePaid，按需postPaid，按次oncePaid，不付费noPaid
 *
 * <AUTHOR>
 * @since 2023/10/8 11:25
 */
@Getter
public enum ChargeTypeEnum {
    PRE_PAID("prePaid", "包年包月"),
    POST_PAID("postPaid", "按量付费"),
    ONCE_PAID("oncePaid", "按次付费"),
    NO_PAID("noPaid", "不计费");

    private final String code;
    private final String desc;

    ChargeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean equals(String code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 忽略大小写比较
     *
     * @param code String
     * @return boolean
     */
    public boolean equalsIgnoreCase(String code) {
        return this.code.equalsIgnoreCase(code);
    }

    /**
     * 根据code 获取 枚举
     *
     * @param code 枚举code
     */
    public static ChargeTypeEnum get(String code) {
        return Arrays.stream(values())
                .filter(v -> v.equalsIgnoreCase(code))
                .findFirst().orElse(null);
    }
}
