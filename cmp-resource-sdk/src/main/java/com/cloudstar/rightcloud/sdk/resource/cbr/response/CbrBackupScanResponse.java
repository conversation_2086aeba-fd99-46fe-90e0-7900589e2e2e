package com.cloudstar.rightcloud.sdk.resource.cbr.response;

import com.cloudstar.rightcloud.sdk.resource.common.model.BaseModel;

import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/1/8 18:26
 * @Version 1.0
 */
@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CbrBackupScanResponse extends BaseModel implements Serializable {

    /** 资源ID */
    private String instanceId;

    /** 底层资源ID */
    private String uuid;

    /** 备份名称 */
    private String name;

    /** 备份自定义描述信息 */
    private String description;

    /**
     * 备份类型
     */
    private String type;

    /** 备份状态 */
    private String status;

    /** 服务器名称 */
    private String resourceName;

    /** 服务器类型 */
    private String resourceType;

    /** 资源大小 */
    private Long resourceSize;

    /** 存储库id */
    private String vaultId;

    /** 备份创建时间 */
    private Date backupCreateDt;

    /** 备份过期时间 */
    private Date backupExpiredDt;

    /** 计费类型;计费类型，PrePaid:包年包月, PostPaid:按量计费，None不计费 */
    private String chargeType;

    /** 云环境ID */
    private Long cloudEnvId;

    /** 堆栈所属项目ID */
    private String projectId;

    /** 堆栈所属租户 */
    private String domainId;

    /** 开始时间 */
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 所有者ID */
    private String ownerId;

    /** 组织ID */
    private Long orgSid;

    /** 创建者组织ID */
    private Long createdOrgSid;

    /** 版本号 */
    private Long version;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    private Date updatedDt;
}
