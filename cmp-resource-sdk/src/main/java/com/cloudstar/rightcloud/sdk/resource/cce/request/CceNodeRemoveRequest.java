package com.cloudstar.rightcloud.sdk.resource.cce.request;

import com.cloudstar.rightcloud.sdk.resource.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-10-11 15:06
 * @Desc
 *该API用于在指定集群下移除节点。
 * PUT /api/v3/projects/{project_id}/clusters/{cluster_id}/nodes/operation/remove
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CceNodeRemoveRequest extends BaseModel implements Serializable {


    private static final long serialVersionUID = -6296958366576277030L;

    /**
     *  集群ID
     */
    private String clusterUuid;

    /**
     *  必填
     *  API类型，固定值“Node”，该值不可修改。
     */
    private final String kind = "RemoveNodesTask";

    /**
     *  必填
     *  API版本，固定值“v3”，该值不可修改。
     */
    private final String apiVersion = "v3";

    /**
     *  必填
     *  spec是集合类的元素类型，您对需要管理的集群对象进行详细描述的主体部分都在spec中给出。CCE通过spec的描述来创建或更新对象。
     */
    private RemoveNodesSpec spec;

    @Data
    public static class RemoveNodesSpec {

        /**
         *  必填
         *  节点登录方式
         */
        private CceNodeRequest.Login login;

        /**
         *  必填
         *  待操作节点列表
         */
        private List<NodeItem> nodes;
    }

    @Data
    public static class NodeItem {

        /**
         * 必填
         * 节点ID
         */
        private String uid;
    }

}
