package com.cloudstar.rightcloud.sdk.resource.ecs.response;

import com.cloudstar.rightcloud.sdk.resource.common.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

/**
 * 云主机恢复响应
 *
 * @author: chengpeng
 * @date: 2023/5/8 13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EcsResumeResponse extends BaseModel implements Serializable {

    private static final long serialVersionUID = 4489120504713722918L;
    /**
     * 实例ID
     */
    private String uuid;
    /**
     * 高级属性
     */
    private Map<String, String> extra;
}
