package com.cloudstar.rightcloud.sdk.resource.natGateway.request;

import com.cloudstar.rightcloud.sdk.resource.common.model.BaseModel;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 查询网关服务SNAT规则详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SnatRuleDetailRequest extends BaseModel implements Serializable {

    private Long id;

    private Long natId;

    private String uuid;
}
