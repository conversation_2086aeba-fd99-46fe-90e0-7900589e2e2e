package com.cloudstar.rightcloud.sdk.resource.lb.response;

import com.cloudstar.rightcloud.sdk.resource.common.model.BaseModel;
import com.cloudstar.rightcloud.sdk.resource.lb.IpGroupModel;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IP地址组同步响应
 *
 * @author: zqy
 * @date: 2024/5/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IpGroupScanResponse extends BaseModel implements Serializable {

    private static final long serialVersionUID = -4375125229535719590L;
    /**
     * IP地址组
     */
    private List<IpGroupModel> ipGroupModel;

    public void setCertificate(List<IpGroupModel> ipGroupModel) {
        this.ipGroupModel = ipGroupModel;
        setUuids(ipGroupModel.stream().map(IpGroupModel::getUuid).collect(Collectors.toSet()));
    }
}
