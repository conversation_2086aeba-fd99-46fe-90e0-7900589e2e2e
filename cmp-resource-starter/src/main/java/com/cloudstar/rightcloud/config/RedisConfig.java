package com.cloudstar.rightcloud.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import com.cloudstar.rightcloud.redis.utils.JedisUtil;

import cn.com.cloudstar.rightcloud.common.redis.SslUtil;


import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.net.ssl.SSLSocketFactory;

import cn.hutool.core.util.StrUtil;
import redis.clients.jedis.JedisPoolConfig;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    private static final String CLOUDSTAR_REDIS_IS_SENTINEL = "cloudstar.redis.is_sentinel";

    private static final String CLOUDSTAR_REDIS_MASTER = "cloudstar.redis.master";

    private static final String CLOUDSTAR_REDIS_SENTINELS = "cloudstar.redis.sentinels";

    private static final String CLOUDSTAR_REDIS_SENTINEL_CIPHER = "redis.sentinel.password";

    private static final String CLOUDSTAR_REDIS_CIPHER = "redis.password";

    private static final String CLOUDSTART_REDIS_SSL = "cloudstar.redis.ssl";

    private static final String CLOUDSTART_REDIS_TRUSTKEYSTORE = "cloudstar.redis.trustKeyStore";

    private static final String CLOUDSTART_REDIS_TRUSTPW = "redis.trust.store.password";

    private static final String CLOUDSTART_REDIS_IS_CLUSTER = "cloudstar.redis.is_cluster";

    // 自定义前缀
    private final String prefix = "RC_EN(";

    // 自定义后缀
    private final String suffix = ")";

    @Autowired
    private Environment env;

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Bean(name = "redisTemplate-res")
    @Primary
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用 GenericFastJsonRedisSerializer 替换默认序列化
        GenericFastJsonRedisSerializer genericFastJsonRedisSerializer = new GenericFastJsonRedisSerializer();
        // 设置value的序列化规则
        template.setValueSerializer(genericFastJsonRedisSerializer);
        // 使用stringSerializer来序列化和反序列化redis的key值
        RedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.afterPropertiesSet();
        JedisUtil.instance().init(template);
        cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil.INSTANCE.init(template);

        return template;
    }

    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {

        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate(connectionFactory);
        JedisUtil.instance().initString(stringRedisTemplate);
        cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil.INSTANCE.initString(stringRedisTemplate);

        return stringRedisTemplate;
    }

    @Bean
    @ConditionalOnProperty(name = CLOUDSTAR_REDIS_IS_SENTINEL, havingValue = "false", matchIfMissing = false)
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
        configuration.setHostName(host);
        configuration.setPort(Integer.parseInt(port));
        if (!StrUtil.isEmpty(password)) {
            if (isEncrypted(password)) {
                password = decrypt(password);
            }
        }
        configuration.setPassword(password);

        JedisClientConfiguration jedisClientConfig = null;
        if (Boolean.parseBoolean(env.getProperty(CLOUDSTART_REDIS_SSL))) {
            String trustPw = env.getProperty(CLOUDSTART_REDIS_TRUSTPW);
            if (!StrUtil.isEmpty(trustPw)) {
                if (isEncrypted(trustPw)) {
                    trustPw = decrypt(trustPw);
                }
            }
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig =
                    JedisClientConfiguration.builder().useSsl()
                                            .hostnameVerifier(NoopHostnameVerifier.INSTANCE)
                                            .sslSocketFactory(socketFactory)
                                            .build();
        } else {
            jedisClientConfig = JedisClientConfiguration.builder().usePooling().poolConfig(new JedisPoolConfig()).build();
        }

        return new JedisConnectionFactory(configuration, jedisClientConfig);
    }

    @Bean
    @ConditionalOnProperty(name = CLOUDSTAR_REDIS_IS_SENTINEL, havingValue = "true", matchIfMissing = false)
    public RedisConnectionFactory redisSentinelConnectionFactory() throws Exception {
        System.out.println("哨兵模式证书连接");
        String master = env.getProperty(CLOUDSTAR_REDIS_MASTER);
        String sentinels = env.getProperty(CLOUDSTAR_REDIS_SENTINELS);
        RedisSentinelConfiguration rsc = new RedisSentinelConfiguration();
        rsc.setMaster(master);
        List<String> redisNodes = Arrays.asList(StrUtil.splitToArray(sentinels, StrUtil.COMMA));
        List<RedisNode> nodes = redisNodes.stream().map(str -> {
            String[] hostPorts = str.split(StrUtil.COLON);
            return new RedisNode(hostPorts[0], Integer.parseInt(hostPorts[1]));
        }).collect(Collectors.toList());
        rsc.setSentinels(nodes);
        String property = env.getProperty(CLOUDSTAR_REDIS_CIPHER);

        if (!StrUtil.isEmpty(property)) {
            if (isEncrypted(property)) {
                property = decrypt(property);
            }
        }

        rsc.setPassword(property);
        String sentinelPassword = StrUtil.emptyToNull(env.getProperty(CLOUDSTAR_REDIS_SENTINEL_CIPHER));

        if (!StrUtil.isEmpty(sentinelPassword)) {
            if (isEncrypted(sentinelPassword)) {
                sentinelPassword = decrypt(sentinelPassword);
            }
        }

        rsc.setSentinelPassword(sentinelPassword);
        JedisClientConfiguration jedisClientConfig;
        System.out.println("证书连接判断");
        if (Boolean.parseBoolean(env.getProperty(CLOUDSTART_REDIS_SSL))) {
            String trustPw = env.getProperty(CLOUDSTART_REDIS_TRUSTPW);

            if (!StrUtil.isEmpty(trustPw)) {
                if (isEncrypted(trustPw)) {
                    trustPw = decrypt(trustPw);
                }
            }
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig = JedisClientConfiguration.builder().useSsl().sslSocketFactory(socketFactory).build();
        } else {
            jedisClientConfig = JedisClientConfiguration.builder().build();
        }
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(rsc, jedisClientConfig);
        System.out.println("连接完成");
        return jedisConnectionFactory;
    }

    @Bean
    @ConditionalOnProperty(name = CLOUDSTART_REDIS_IS_CLUSTER, havingValue = "true", matchIfMissing = false)
    public RedisConnectionFactory redisClusterConnectionFactory() {
        System.out.println("redisClusterConnectionFactory-RedisClusterConfiguration-start");
        List<String> redisNodes;
        if (host.contains(StrUtil.COMMA)) {
            redisNodes = Arrays.asList(StrUtil.splitToArray(host, StrUtil.COMMA));
        }else {
            redisNodes = Collections.singletonList(host + StrUtil.COLON + port);
        }
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(redisNodes);
        clusterConfig.setPassword(env.getProperty(CLOUDSTAR_REDIS_CIPHER));
        clusterConfig.setMaxRedirects(10);

        JedisClientConfiguration jedisClientConfig;
        if (Boolean.parseBoolean(env.getProperty(CLOUDSTART_REDIS_SSL))) {
            String trustPw = env.getProperty(CLOUDSTART_REDIS_TRUSTPW);
            if (!StrUtil.isEmpty(trustPw)) {
                if (isEncrypted(trustPw)) {
                    trustPw = decrypt(trustPw);
                }
            }
            System.out.println("redisClusterConnectionFactory-getSSLSocketFactory before ====================================");
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig = JedisClientConfiguration.builder().useSsl().sslSocketFactory(socketFactory).build();
            System.out.println("redisClusterConnectionFactory-getSSLSocketFactory after ====================================");
        } else {
            jedisClientConfig = JedisClientConfiguration.builder().build();
        }
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(clusterConfig, jedisClientConfig);
        System.out.println("redisClusterConnectionFactory-RedisClusterConfiguration-end");
        return jedisConnectionFactory;
    }

    public boolean isEncrypted(String message) {
        if (StrUtil.isEmpty(message)) {
            return false;
        } else {
            String trimmedValue = message.trim();
            return trimmedValue.startsWith(this.prefix) && trimmedValue.endsWith(this.suffix);
        }
    }

    public String decrypt(String decryptMessage) {

        int prefixIndex = decryptMessage.indexOf(prefix);
        int suffixIndex = decryptMessage.indexOf(suffix);
        // 还原密文
        decryptMessage = decryptMessage.substring(prefixIndex + prefix.length(), suffixIndex);
        // 还原密码。注意如果需要密钥的这里添加
        return CrytoUtilSimple.decrypt(decryptMessage).trim();
    }
}
